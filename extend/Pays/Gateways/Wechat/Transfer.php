<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Wechat;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Wechat;

/**
 * 微信企业打款网关
 * Class TransferGateway
 * @link https://pay.weixin.qq.com/doc/v2/merchant/**********
 * @package Pay\Gateways\Wechat
 */
class Transfer extends Wechat
{

    /**
     * 应用并返回数据
     * @param array $options
     * @return array
     * @throws GatewayException
     */
    public function apply(array $options = [])
    {
        $options['mchid']     = $this->config['mch_id'];
        $options['mch_appid'] = $options['mch_appid'] ?? $this->userConfig->get('app_id');
        unset($this->config['mch_id']);
        unset($this->config['sign_type']);
        $this->config = array_merge($this->config, $options);
        $this->unsetTradeTypeAndNotifyUrl();
        $this->unsetAppid();
        $this->unsetWeappId();
        $this->unsetGuid();
        $this->config['sign'] = $this->getSign($this->config);
        $data                 = $this->fromXml($this->post(
            $this->gateway_transfer, $this->toXml($this->config),
            [
                'ssl_cer' => $this->userConfig->get('ssl_cer', ''),
                'ssl_key' => $this->userConfig->get('ssl_key', ''),
            ]
        ));
        if (!isset($data['return_code']) || $data['return_code'] !== 'SUCCESS' || $data['result_code'] !== 'SUCCESS') {
            $error = 'GetResultError:' . $data['return_msg'];
            if (isset($data['err_code_des']) && $data['err_code_des'] != $data['return_msg']) {
                $error .= $data['err_code_des'];
            }
            $error .= '(' . $data['err_code'] . ')';
        }
        if (isset($error)) {
            throw new GatewayException($error, 20001, $data);
        }
        return $data;
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return '';
    }
}
