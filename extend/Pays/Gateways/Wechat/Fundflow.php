<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Wechat;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Wechat;

/**
 * 下载微信资金账单
 * Class FundflowGateway
 * @package Pay\Gateways\Wechat
 */
class Fundflow extends Wechat
{
    /**
     * @var string
     */
    protected $encrypt_method = 'HMAC-SHA256';

    /**
     * 应用并返回参数
     * @param array $options
     * @return bool|array|string
     * @throws GatewayException
     */
    public function apply(array $options)
    {
        $this->config                 = array_merge($this->config, $options);
        $this->config['sign_type']    = $this->encrypt_method;
        $this->config['account_type'] = !empty($options['account_type']) ? $options['account_type'] : 'Basic';
        $this->config['sign']         = $this->getSign($this->config);
        $this->unsetTradeTypeAndNotifyUrl();
        $data = $this->fromXml($this->post($this->gateway_fundflow, $this->toXml($this->config), [
            'ssl_cer' => $this->userConfig->get('ssl_cer', ''),
            'ssl_key' => $this->userConfig->get('ssl_key', ''),
        ]));
        if (is_array($data) && (!isset($data['return_code']) || $data['return_code'] !== 'SUCCESS' || $data['result_code'] !== 'SUCCESS')) {
            $error = 'GetResultError:' . $data['return_msg'];
            $error .= isset($data['err_code_des']) ? ' - ' . $data['err_code_des'] : '';
        }
        if (isset($error)) {
            throw new GatewayException($error, 20001, $data);
        }
        return $this->parserDownloadData($data);
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return '';
    }
}