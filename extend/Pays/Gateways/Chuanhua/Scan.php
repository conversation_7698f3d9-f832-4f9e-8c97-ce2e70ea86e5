<?php


namespace Pays\Gateways\Chuanhua;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Chuanhua;

/**
 * 微信扫码支付网关
 * Class ScanGateway
 * @package Pay\Gateways\Wechat
 */
class Scan extends Chuanhua
{

    /**
     * 应用并返回参数
     * @param array $options
     * @return mixed
     * @throws GatewayException
     */
    public function apply(array $options = [])
    {
        return $this->preOrder($options)['code_url'];
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'NATIVE';
    }
}
