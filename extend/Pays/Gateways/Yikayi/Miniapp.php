<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Yikayi;

use Exception;
use Pays\Exceptions\GatewayException;
use Pays\Gateways\Yikayi;
use xieyongfa\yky\Yky;

/**
 * 微信小程序支付网关
 * Class MiniappGateway
 * @package Pay\Gateways\Yikayi
 */
class Miniapp extends Yikayi
{

    /**
     * 应用并返回参数
     * @link http://openapi.1card1.cn/VipCloudDoc/CreateOrder_Mini
     * @param array $options
     * @return array
     * @throws GatewayException|Exception
     */
    public function apply(array $options = [])
    {
        $yky_pay = Yky::Pay($this->userConfig->get());
        $request = request();
        if (!empty($request->__get('_bid'))) {
            $bid = $request->__get('_bid');
        } else {
            throw new Exception('请求中不包含_bid标识');
        }
        $config = get_config_by_bid($bid);
        if (!($appid = $config['weappid'])) {
            throw new Exception('该商家未授权小程序');
        }
        $data       = [
            'uniqueCode'  => $options['out_trade_no'],
            'appId'       => $appid,
            'openId'      => $options['openid'],
            'notifyUrl'   => $options['notify_url'],
            'userAccount' => $this->userConfig->get('user_account'),
            'paidMoney'   => tools()::nc_price_fen2yuan($options['total_fee'])
        ];
        $pay_result = $yky_pay->CreateOrder_Mini($data);
        if ($pay_result === false) {
            throw new Exception($yky_pay->message, $yky_pay->status);
        }
        return [
            'timeStamp' => $pay_result['timeStamp'],
            'nonceStr'  => $pay_result['nonceStr'],
            'package'   => $pay_result['package'],
            'signType'  => $pay_result['signType'],
            'paySign'   => $pay_result['paySign'],
        ];
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'JSAPI';
    }
}
