<?php

namespace Pays\Gateways\Yikayi;

use Exception;
use Pays\Exceptions\GatewayException;
use Pays\Gateways\Yikayi;
use xieyongfa\yky\Yky;

/**
 * POS刷卡支付网关
 * Class PosGateway
 * @package Pay\Gateways\Yikayi
 */
class Pos extends Yikayi
{
    /**
     * 应用并返回参数
     * @param array $options
     * @return array
     * @throws GatewayException|Exception
     */
    public function apply(array $options = [])
    {
        $auth_code = $options['auth_code'];
        if (!preg_match("/^(10|11|12|13|14|15)\d{16}$/", $auth_code) && !preg_match("/^28\d{15,16}$/", $auth_code)) {
            throw new Exception('付款码格式有误,请核实是否微信或支付宝付款码!');
        }
        $yky_pay     = Yky::Pay($this->userConfig->get());
        $total_money = tools()::nc_price_fen2yuan($options['total_fee']);
        $data        = [
            'userAccount' => $this->userConfig->get('user_account'),
            'authCode'    => $auth_code,
            'uniqueCode'  => $options['out_trade_no'],
            'totalFee'    => $total_money
        ];
        $pay_result  = $yky_pay->OperatorScan($data);
        if ($pay_result === false) {
            throw new Exception($yky_pay->message, $yky_pay->status);
        }
        if ($this->isSuccess($pay_result) && $pay_result['status'] == 0) {
            $trade_state = 'FAIL';
            switch ($pay_result['status']) {
                case 0:
                    $trade_state = 'SUCCESS';
                    break;
                case 1:
                    $trade_state = 'USERPAYING';
                    break;
                case -1:
                    $trade_state = 'PAYERROR';
                    break;
                default:
            }
            return [
                'return_code'    => 'SUCCESS', //通信结果
                'return_msg'     => 'SUCCESS',
                'result_code'    => $data['status'] == 0 ? 'SUCCESS' : 'FAIL',
                'trade_state'    => $trade_state,
                'appid'          => '',
                'mch_id'         => '',
                'device_info'    => '',
                'nonce_str'      => '',
                'sign'           => '',
                'openid'         => '',
                'is_subscribe'   => '',
                'trade_type'     => 'trade_type',
                'bank_type'      => '',
                'total_fee'      => $total_money,  //元
                'transaction_id' => $data['orderNo'],
                'out_trade_no'   => $options['out_trade_no'],
                'attach'         => '',
                //'time_end'       => tools()::format_time($data['payTime']),
                'time_end'       => $data['payTime'] ?? format_timestamp(), //一卡易通道暂时不支持返回支付时间,只能取当前时间
                'raw_data'       => $pay_result
            ];
        } else {
            throw new Exception('支付失败');
        }
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'MICROPAY';
    }
}
