<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Yikayi;

use Exception;
use Pays\Exceptions\GatewayException;
use Pays\Gateways\Yikayi;
use xieyongfa\yky\Yky;

/**
 * 公众号支付网关
 * Class MpGateway
 * @package Pay\Gateways\Yikayi
 */
class Mp extends Yikayi
{

    /**
     * 设置并返回参数
     * @link  http://openapi.1card1.cn/VipCloudDoc/UPayOrder
     * @param array $options
     * @return array
     * @throws GatewayException|Exception
     */
    public function apply(array $options = [])
    {
        $yky_pay = Yky::Pay($this->userConfig->get());
        $request = request();
        if (!empty($request->__get('_bid'))) {
            $bid = $request->__get('_bid');
        } else {
            throw new Exception('请求中不包含_bid标识');
        }
        $data       = [
            'billNumber'   => $options['out_trade_no'],
            'totalFee'     => $options['total_fee'],
            'notifyUrl'    => $options['notify_url'],
            "redirectUrl"  => urlencode((string)url('member/pay/pay_success', ['bid' => $bid, 'bill_no' => $options['out_trade_no']], false, true)),// 回调地址
            'uPayDiscount' => $options['total_fee']
        ];
        $pay_result = $yky_pay->UPayOrder($data);
        if ($pay_result === false) {
            throw new Exception($yky_pay->message, $yky_pay->status);
        }
        return [
            'pay_url' => $pay_result['upayUrl'],
        ];
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'JSAPI';
    }

    /**
     * 查询订单状态
     * @link http://openapi.1card1.cn/VipCloudDoc/QueryOrder
     * @param string $out_trade_no
     * @return array
     * @throws Exception
     */
    public function find($out_trade_no = '')
    {
        $yky_pay    = Yky::Pay($this->userConfig->get());
        $pay_result = $yky_pay->QueryOrder(['billNumber' => $out_trade_no]);
        return $this->_parseOrderResult($pay_result, $out_trade_no);
    }
}
