<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways;

use Exception;
use Pays\Contracts\Config;
use Pays\Contracts\GatewayInterface;
use Pays\Exceptions\GatewayException;
use Pays\Exceptions\InvalidArgumentException;
use xieyongfa\yky\Yky;

/**
 * 一卡易支付基础类
 * Class Yikayi
 * @package Pays\Gateways\Yikayi
 */
abstract class Yikayi extends GatewayInterface
{

    /**
     * @var array
     */
    protected $config;

    /**
     * @var Config
     */
    protected $userConfig;

    /**
     * Wechat constructor.
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->debug      = !empty($config['debug']);
        $this->userConfig = new Config($config);
        if (is_null($this->userConfig->get('openid'))) {
            throw new InvalidArgumentException('Missing Config -- [openid]');
        }
        if (is_null($this->userConfig->get('secret'))) {
            throw new InvalidArgumentException('Missing Config -- [secret]');
        }

        if (is_null($this->userConfig->get('user_account'))) {
            throw new InvalidArgumentException('Missing Config -- [user_account]');
        }

        $this->config = [
            'openid'           => $this->userConfig->get('openid', ''),
            'secret'           => $this->userConfig->get('secret', ''),
            'user_account'     => $this->userConfig->get('user_account', ''),
            'spbill_create_ip' => tools()::get_client_ip()
        ];
    }


    /**
     * @return mixed
     */
    abstract protected function getTradeType();

    /**
     * 订单退款操作
     * @link http://openapi.1card1.cn/OpenApiDoc/ReturnGoods
     * @param array $options
     * @return array
     * @throws GatewayException|Exception
     */
    public function refund($options = [])
    {
        $yky_pay = Yky::Consume($this->userConfig->get());
        $data    = [
            'userAccount' => $this->userConfig->get('user_account'),
            'billNumber'  => $options['bill_number'] ?? $options['out_trade_no'],
            'returnMoney' => tools()::nc_price_fen2yuan($options['refund_fee']),
            'meno'        => $options['refund_desc'] ?? 'API退款'
        ];
        //        billNumber
        //        orderNo
        //        thirdTradeNo
        $pay_result = $yky_pay->ReturnGoods($data);
        if ($pay_result === false) {
            throw new Exception($yky_pay->message, $yky_pay->status);
        }
        return [
            'return_code'    => 'SUCCESS', //通信结果
            'return_msg'     => 'SUCCESS',
            'result_code'    => (isset($pay_result['refundStatus']) && $pay_result['refundStatus'] == 1) ? 'SUCCESS' : 'FAIL',
            'appid'          => '',
            'mch_id'         => '',
            'nonce_str'      => '',
            'sign'           => '',
            'out_refund_no'  => $pay_result['refundOrderNo'] ?? 0,
            'out_trade_no'   => $options['out_trade_no'],
            'refund_id'      => '',
            'transaction_id' => '',
            'refund_fee'     => tools()::nc_price_yuan2fen($options['refund_fee']),  //元转分
            'raw_data'       => $data
        ];
    }

    /**
     * 查询退款订单状态
     * @link http://openapi.1card1.cn/OpenApiDoc/GetRefundOrderStatus
     * @param string $out_trade_no
     * @return array
     * @throws GatewayException|Exception
     */
    public function refund_find($out_trade_no = '')
    {
        $yky_pay    = Yky::Pay($this->userConfig->get());
        $data       = ['refundOrderNo' => $out_trade_no];
        $pay_result = $yky_pay->GetRefundOrderStatus($data);
        if ($pay_result === false) {
            return [
                'return_code' => 'SUCCESS',
                'return_msg'  => 'FAIL',
                'trade_state' => 'FAIL',
            ];
        }
        return [
            'return_code'    => 'SUCCESS', //通信结果
            'return_msg'     => 'SUCCESS',
            'result_code'    => (isset($pay_result['refundStatus']) && $pay_result['refundStatus'] == 1) ? 'SUCCESS' : 'FAIL',
            'appid'          => '',
            'mch_id'         => '',
            'nonce_str'      => '',
            'sign'           => '',
            'out_trade_no'   => $out_trade_no,
            'refund_id'      => '',
            'transaction_id' => '',
            //'refund_fee'     => '',  //元转分
            'raw_data'       => $data
        ];
    }

    /**
     * 关闭正在进行的订单
     * @param string $out_trade_no
     * @return array
     * @throws GatewayException|Exception
     */
    public function close($out_trade_no = '')
    {
        throw new Exception('该通道暂不支持关闭订单');
    }

    /**
     * 查询订单状态
     * @link http://openapi.1card1.cn/OpenApiDoc/CheckOperatorScan
     * @param string $out_trade_no
     * @return array
     * @throws GatewayException|Exception
     */
    public function find($out_trade_no = '')
    {
        $yky_pay    = Yky::Pay($this->userConfig->get());
        $pay_result = $yky_pay->CheckOperatorScan($out_trade_no);
        return $this->_parseOrderResult($pay_result, $out_trade_no);
    }

    protected function _parseOrderResult($pay_result, $out_trade_no)
    {
        if ($pay_result === false || (isset($pay_result['code']) && !in_array($pay_result['code'], [1, 2]))) {
            return [
                'return_code' => 'SUCCESS',
                'return_msg'  => 'FAIL',
                'trade_state' => 'FAIL',
            ];
        }
        return [
            'return_code'    => 'SUCCESS', //通信结果
            'return_msg'     => $pay_result['message'],
            'result_code'    => $pay_result['status'] == 0 ? 'SUCCESS' : 'FAIL',
            'appid'          => '',
            'mch_id'         => '',
            'device_info'    => '',
            'nonce_str'      => '',
            'sign'           => '',
            'openid'         => '',
            'is_subscribe'   => '',
            'trade_type'     => 'trade_type',
            'bank_type'      => '',
            'total_fee'      => tools()::nc_price_yuan2fen($pay_result['totalMoney']),  //分
            'transaction_id' => $pay_result['billNumber'] ?? '',
            'out_trade_no'   => $out_trade_no,
            'attach'         => '',
            //'time_end'       => tools()::format_time($data['payTime']),
            'time_end'       => $data['payTime'] ?? format_timestamp(), //一卡易通道暂时不支持返回支付时间,只能取当前时间
            'trade_state'    => ($pay_result['status'] === 0) ? 'SUCCESS' : 'FAIL',
            'raw_data'       => $pay_result
        ];
    }

    /**
     * 内容验证
     * @param string $data
     * @param null $sign
     * @param bool $sync
     * @return array|bool
     */
    public function verify($data, $sign = null, $sync = false)
    {
        //暂时只用于支付通知回调
        $yky_pay = Yky::Pay($this->userConfig->get());
        return $yky_pay->verify();
    }

    /**
     * 获取微信支付通知
     * @return array
     * @throws InvalidArgumentException|Exception
     */
    public function getNotify()
    {
        $yky_pay = Yky::Pay($this->userConfig->get());
        $data    = $yky_pay->verify();
        $type    = $data['type'] ?? null;
        if ($type == 'Refund') {
            //退款回调
            //   {
            //     "type": "Refund",
            //    "businessAccount": "***********",
            //    "refundStatus": 1,
            //    "billNumber": "****************",
            //    "message": "退款成功",
            //    "uniqueCode": "20220725084437366153",
            //    "orderNo": "****************",
            //    "refundOrderNo": "****************"
            //}
            return [
                'return_code' => 'SUCCESS', //通信结果
                'return_msg'  => 'SUCCESS',
                'result_code' => 'SUCCESS',
                'trade_state' => 'REFUND',
                'raw_data'    => $data
            ];
        }
        if ($data['status'] === 0) {
            // 小程序
            //     {
            //    "type": "Pay",
            //    "businessAccount": "***********",
            //    "billNumber": "****************",
            //    "totalMoney": 0.0600,
            //    "attach": "",
            //    "uniqueCode": "20220725084437366153",
            //    "orderNo": "****************",
            //    "paidTime": "2022-07-25 08:45:53",
            //    "status": 0
            //}

            $paid_time = $data['paidTime'] ?? format_timestamp();
            if (strpos($paid_time, 'Date') !== false) {
                if (preg_match('/\d+/', $paid_time, $arr)) {
                    $paid_time = format_timestamp($arr[0] / 1000);
                }
            }
            return [
                'return_code'    => 'SUCCESS', //通信结果
                'return_msg'     => $data['message'] ?? '',
                'result_code'    => 'SUCCESS',
                'appid'          => '',
                'mch_id'         => '',
                'device_info'    => '',
                'nonce_str'      => '',
                'sign'           => '',
                'openid'         => '',
                'is_subscribe'   => '',
                'trade_type'     => 'trade_type',
                'bank_type'      => '',
                'total_fee'      => tools()::nc_price_yuan2fen($data['totalMoney']),  //分
                'transaction_id' => $data['consumeBillNumber'] ?? ($data['billNumber'] ?? ''),
                'out_trade_no'   => $data['billNumber'] ?? '',
                'attach'         => '',
                'time_end'       => $paid_time,
                'trade_state'    => 'SUCCESS',
                'raw_data'       => $data
            ];
        } else {
            throw new Exception('支付回调不是成功状态');
        }
    }

    /**
     * 获取支付通知回复内容
     * @return string
     */
    public function getNotifySuccessReply()
    {
        return json_encode(['status' => 0, 'message' => '处理成功'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 返回失败通知
     * @param string $return_msg 错误信息
     * @return string
     */
    public function getNotifyFailedReply($return_msg = '')
    {
        return json_encode(['status' => -1, 'message' => 'FAIL:' . $return_msg], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 判断结果是否成功
     * @param $result
     * @return bool
     */
    protected function isSuccess($result)
    {
        if (!is_array($result)) {
            return false;
        }
        return isset($result['status']) && ($result['status'] === 1);
    }
}
