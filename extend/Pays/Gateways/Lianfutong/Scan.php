<?php

namespace Pays\Gateways\Lianfutong;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Lianfutong;

/**
 * 微信扫码支付网关
 * Class ScanGateway
 * @package Pay\Gateways\Wechat
 */
class Scan extends Lianfutong
{

    /**
     * 应用并返回参数
     * @param array $options
     * @return mixed
     * @throws GatewayException
     */
    public function apply(array $options = [])
    {
        return $this->preOrder($options)['code_url'];
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'NATIVE';
    }
}
