<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Suixingfu;

use Exception;
use Pays\Exceptions\GatewayException;
use Pays\Gateways\Suixingfu;

/**
 * 微信小程序支付网关
 * Class MiniappGateway
 * @package Pay\Gateways\Wechat
 */
class Miniapp extends Suixingfu
{

    /**
     * 应用并返回参数
     * @param array $options
     * @return array
     * @throws GatewayException
     */
    public function apply(array $options = [])
    {
        $this->service = "/qr/jsapiScan";
        $this->setReqData([
            'ordNo'      => $options['out_trade_no'] ?? tools()::get_bill_number(),
            'amt'        => tools()::nc_price_fen2yuan($options['total_fee']),
            'payType'    => 'LETPAY',
            'timeExpire' => 3,
            'subject'    => '微信小程序买单',
            'subOpenid'  => $options['openid'],
            'subAppid'   => $options['appid'], //wxfb6c194c44d7cc1f
            'notifyUrl'  => str_replace('https', 'http', $options['notify_url']),
            //'subMchId'     => $this->userConfig->get('sub_mch_id'),//必传
            //'attach'       => '{}',
        ]);
        $result = $this->getResult();
        if ($this->isSuccess($result)) {
            $pay_request = [
                'appId'     => $result['payAppId'],
                'timeStamp' => $result['payTimeStamp'],
                'nonceStr'  => $result['paynonceStr'],
                'signType'  => $result['paySignType'],
                'package'   => $result['payPackage'],
                'paySign'   => $result['paySign'],
            ];
            return $pay_request;
        } else {
            //特定失败自动尝试配置APPID
            if (isset($result['bizMsg']) && $result['bizMsg'] == '交易失败，请联系客服' && isset($result['bizCode']) && $result['bizCode'] == '2010') {
                try {
                    $this->service           = "/weChat/bindconfig";
                    $this->config['reqData'] = [];
                    $this->setReqData([
                        'mno'      => $this->userConfig->get('mno', ''),
                        'subMchId' => $this->userConfig->get('sub_mch_id'),//必传
                        'subAppid' => $options['appid'],//必传
                    ]);
                    $this->getResult();
                } catch (Exception $e) {
                    wr_log(__FUNCTION__ . ' Error: ' . $e->getMessage());
                }
            }
        }
        return $result;
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'MINIAPP';
    }
}