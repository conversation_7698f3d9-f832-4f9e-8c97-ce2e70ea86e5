<?php


namespace Pays\Gateways\Suixingfu;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Suixingfu;

/**
 * 微信公众号支付网关
 * Class MpGateway
 * @package Pay\Gateways\Wechat
 */
class Mp extends Suixingfu
{
    /**
     * 设置并返回参数
     * @param array $options
     * @return array
     * @throws GatewayException
     */
    public function apply(array $options = [])
    {
        $this->service = "/precreate";
        $data          = [
            'channel'     => 'WXPAY',
            'tradeType'   => $this->getTradeType(),
            'subject'     => '微信买单',
            'outTradeNo'  => $options['out_trade_no'],
            'totalAmount' => tools()::nc_price_fen2yuan($options['total_fee']),
            'openId'      => $options['openid'],
            'notifyUrl'   => str_replace('https', 'http', $options['notify_url'])
        ];
        $this->config  = array_merge($this->config, $data);
        $result        = $this->getResult();
        $payRequest    = [
            'appId'     => $result['appId'],
            'timeStamp' => $result['timeStamp'],
            'nonceStr'  => $result['nonceStr'],
            'signType'  => $result['signType'],
            'package'   => $result['payPackage'],
            'paySign'   => $result['paySign'],
        ];
        return $payRequest;
    }

    /**
     * 当前操作类型
     * @return string
     */
    protected function getTradeType()
    {
        return 'JSAPI';
    }
}
