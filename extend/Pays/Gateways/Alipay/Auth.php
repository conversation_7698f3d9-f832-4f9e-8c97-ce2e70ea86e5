<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Alipay;

use Pays\Gateways\Alipay;

/**
 * 支付宝授权网关
 * Class AppGateway
 * @package Pay\Gateways\Alipay
 */
class Auth extends Alipay
{

    /**
     * 应用并返回参数
     * @param array $options
     * @return string
     */
    public function apply(array $options = [])
    {
        return $this->getResult($options, $this->getMethod());
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getMethod()
    {
        return 'alipay.open.auth.token.app';
    }

    /**
     * 应用并返回参数
     * @param array $options
     * @return string
     */
    public function query(array $options = [])
    {
        return $this->getResult($options, $this->getQueryMethod());
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getQueryMethod()
    {
        return 'alipay.open.auth.token.app.query';
    }

    /**
     * 当前接口产品码
     * @return string
     */
    protected function getProductCode()
    {
        return '';
    }
}
