<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Alipay;

use Exception;
use Pays\Gateways\Alipay;

/**
 * 支付宝授权网关
 * Class AppGateway
 * @package Pay\Gateways\Alipay
 */
class Oauth extends Alipay
{

    /**
     * 应用并返回参数
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function apply(array $options = [])
    {
        $this->config = array_merge($this->config, $options);
        $result       = $this->getResult($options, $this->getMethod());
        if (empty($result['user_id'])) {
            throw new Exception('支付宝授权失败:' . ($result['sub_msg'] ?? (json_encode($result, JSON_UNESCAPED_UNICODE))));
        }
        $result['openid'] = $result['user_id'];
        return $result;
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getMethod()
    {
        return 'alipay.system.oauth.token';
    }

    /**
     * 应用并返回参数
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function query(array $options = [])
    {
        return $this->getResult($options, $this->getQueryMethod());
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getQueryMethod()
    {
        return 'alipay.open.auth.token.app.query';
    }

    /**
     * 应用并返回参数
     * @param array $options
     * @return string
     */
    public function decode(array $options = [])
    {
        return $this->getResult($options, 'alipay.marketing.facetoface.decode.use');
    }

    /**
     * 当前接口产品码
     * @return string
     */
    protected function getProductCode()
    {
        return '';
    }
}
