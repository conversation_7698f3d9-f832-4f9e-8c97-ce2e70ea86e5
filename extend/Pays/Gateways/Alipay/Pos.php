<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Alipay;

use Pays\Exceptions\GatewayException;
use Pays\Gateways\Alipay;

/**
 * 支付宝刷卡支付
 * Class PosGateway
 * @package Pay\Gateways\Alipay
 */
class Pos extends Alipay
{

    /**
     * 应用并返回参数
     * @param array $options
     * @param string $scene
     * @return array|bool
     * @throws GatewayException
     */
    public function apply(array $options = [], $scene = 'bar_code')
    {
        $options['scene']        = $scene;
        $options['subject']      = $options['body'];
        $total_fee               = $options['total_fee'];
        $options['total_amount'] = tools()::nc_price_fen2yuan($total_fee);
        unset($options['total_fee']);
        $result                   = $this->getResult($options, $this->getMethod());
        $result['time_end']       = date('YmdHis', strtotime($result['gmt_payment']));
        $result['transaction_id'] = $result['trade_no'];
        $result['openid']         = $result['buyer_user_id'];
        $result['total_fee']      = $total_fee;
        return $result;
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getMethod()
    {
        return 'alipay.trade.pay';
    }

    /**
     * 当前接口产品码
     * @return string
     */
    protected function getProductCode()
    {
        return 'FACE_TO_FACE_PAYMENT';
    }
}
