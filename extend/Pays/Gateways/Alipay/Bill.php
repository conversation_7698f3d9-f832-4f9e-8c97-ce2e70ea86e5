<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Pays\Gateways\Alipay;

use Exception;
use Pays\Exceptions\GatewayException;
use Pays\Gateways\Alipay;

/**
 * 支付宝电子面单下载
 * Class BillGateway
 * @package Pay\Gateways\Alipay
 */
class Bill extends Alipay
{
    protected $field_map = [
        'transaction_id'       => '支付宝交易号',
        'out_trade_no'         => '商户订单号',
        'trade_type'           => '业务类型',
        'goods_name'           => '商品名称',
        'trade_create_time'    => '交易创建时间',
        'trade_finish_time'    => '交易完成时间',
        'store_number'         => '门店编号',
        'store_name'           => '门店名称',
        'operator'             => '操作员',
        'terminal_number'      => '终端号',
        'account'              => '对方账户',
        'order_money'          => '订单金额（元）',
        'receiv_money'         => '商家实收（元）',
        'red_packets'          => '支付宝红包（元）',
        'set_points_treasure'  => '集分宝（元）',
        'discount'             => '支付宝优惠（元）',
        'business_discount'    => '商家优惠（元）',
        'coupon_money'         => '券核销金额（元）',
        'coupon_name'          => '券名称',
        'business_red_packets' => '商家红包消费金额（元）',
        'card_monty'           => '卡消费金额（元）',
        'refund_no'            => '退款批次号/请求号',
        'fee'                  => '服务费（元）',
        'profit_money'         => '分润（元）',
        'memo'                 => '备注',
        'pid'                  => '支付宝PID',
    ];

    /**
     * 应用并返回参数
     * @param array $options
     * @return array|bool
     * @throws GatewayException|Exception
     */
    public function apply(array $options = [])
    {
        $options['bill_date'] = isset($options['bill_date']) ? $options['bill_date'] : date('Y-m-d', strtotime('-1 day'));
        $options['bill_type'] = isset($options['bill_type']) ? $options['bill_type'] : 'trade';
        $this->config         = array_merge($this->config, $options);
        $result               = $this->getResult($options, $this->getMethod());
        $bill_download_url    = $result['bill_download_url'];
        $unzip_path           = app()->getRuntimePath() . '_bill_alipay';
        if (!is_dir($unzip_path)) {
            mkdir($unzip_path, 0755, true); // 若目录不存在则创建之
        }
        $path      = 'file' . DIRECTORY_SEPARATOR . 'downloads' . DIRECTORY_SEPARATOR . md5($bill_download_url) . '.zip';
        $local     = tools()::get_absolute_path($path);
        $result    = curl()->download_file($bill_download_url);
        $replace   = ['业务明细' => 'day', '汇总' => 'all'];
        $result    = tools()::extract_zip_to_file($result, $unzip_path, $replace);
        $bill_data = [];
        foreach ($result as $file) {
            if (strpos($file, 'all') === false) {
                $result = tools()::read_csv($file);
                $pid    = $this->userConfig->get('pid');
                foreach (array_slice($result, 5) as $data) {
                    if (count($data) < 20) {
                        continue;
                    } elseif (count($data) > 25) {
                        throw new Exception('支付宝账单字段可能有变化,下载失败,请核实');
                    } else {
                        $data[] = $pid;
                        foreach ($data as $k => $v) {
                            $data[$k] = str_replace(array("/r", "/n", "/r/n"), '', $v); //去掉换行符
                            $data[$k] = preg_replace("/\xA1/", '', $data[$k]); //去掉制表符
                            $data[$k] = trim($data[$k]);
                        }
                        $bill_data[] = array_combine(array_keys($this->field_map), $data);//处理数组 key值
                    }
                }
                break;
            }
        }
        return $bill_data;
    }

    /**
     * 当前接口方法
     * @return string
     */
    protected function getMethod()
    {
        return 'alipay.data.dataservice.bill.downloadurl.query';
    }

    /**
     * 应用并返回参数
     * @return array|bool
     */
    protected function getProductCode()
    {
        return '';
    }
}