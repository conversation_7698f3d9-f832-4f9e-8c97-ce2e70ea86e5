<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Storage;

use Storage\Contracts\Config;
use Storage\Contracts\Exception;
use Storage\Contracts\StorageInterface;
use Storage\Driver\Local;
use Storage\Driver\Qiniu;
use Storage\Driver\Tencent;
use Storage\Driver\Aliyun;
use Storage\Driver\Upaiyun;
use Storage\Driver\Wechat;
use Storage\Driver\Yikayi;
use think\facade\Config as Configs;

/**
 * Class Storage
 * @method Local local($options = []) static 本地存储引擎
 * @method Qiniu qiniu($options = []) static 七牛云存储引擎
 * @method Aliyun aliyun($options = []) static 阿里云OSS存储引擎
 * @method Upaiyun upaiyun($options = []) static 又拍云存储引擎
 * @method Tencent tencent($options = []) static 腾讯云存储引擎
 * @method Wechat wechat($options = []) static 微信图片服务器
 * @method Yikayi Yikayi($options = []) static 一卡易图片服务器
 *
 * @package Storage
 */
class Storage
{

    /**
     * @var Config
     */
    private static $config;

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return StorageInterface
     * @throws \Exception
     */
    public static function __callStatic($name, $arguments)
    {
        return self::driver($name);
    }

    /**
     * 指定驱动器
     * @param string $driver 默认本地
     * @return StorageInterface
     * @throws Exception
     */
    public static function driver($driver = 'local')
    {
        self::$config = new Config(Configs::get('storage'));
        if (is_null(self::$config->get($driver))) {
            throw new Exception("Driver [$driver]'s Config is not defined.");
        }
        if (!file_exists(__DIR__ . '/Driver/' . ucfirst($driver) . '.php')) {
            throw new Exception("Driver [$driver] is not supported.");
        }
        $gateway = __NAMESPACE__ . '\\Driver\\' . ucfirst($driver);
        return new $gateway(self::$config->get($driver));
    }
}
