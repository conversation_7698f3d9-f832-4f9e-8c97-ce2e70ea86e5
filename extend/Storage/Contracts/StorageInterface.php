<?php

namespace Storage\Contracts;

use app\common\exceptions\NotNotifyException;
use app\model\FileUploadNote;
use think\File;
use Throwable;

/**
 * 存储接口
 * Interface GatewayInterface
 * @package Storage\Contracts
 */
abstract class StorageInterface
{
    protected $code;
    protected $msg;
    protected $allow_ext = 'xls,xlsx,pem,jpg,jpeg,png,gif,mp4,zip,mov';

    public static function getFileMineByUrl($url)
    {
        if (empty($url)) {
            return false;
        }
        $headers = get_headers($url, 1);
        $type    = $headers['Content-Type'];
        return self::getExtByMines($type);
    }

    /**
     * 通过文件扩展的mine获取文件后缀
     * @param string $mine 文件后缀
     * @return mixed
     */
    public static function getExtByMines($mine)
    {
        $mines = self::getMines();
        foreach ($mines as $key => $val) {
            if (strtolower($mine) == strtolower($val)) {
                return strtolower($key);
            }
        }
        return false;
    }

    /**
     * 获取所有文件扩展的mine
     * @return mixed
     */
    public static function getMines()
    {
        $cache_key = 'all_ext_mine';
        $mines     = cache($cache_key);
        if (empty($mines)) {
            $content = file_get_contents('http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types');
            preg_match_all('#^([^\s]{2,}?)\s+(.+?)$#ism', $content, $matches, PREG_SET_ORDER);
            foreach ($matches as $match) {
                foreach (explode(" ", $match[2]) as $ext) {
                    $mines[$ext] = $match[1];
                }
            }
            cache($cache_key, $mines, 3600 * 24 * 31);
        }
        return $mines;
    }

    public function getSid()
    {
        return (int)request()->__get('_sid');
    }

    /**
     * 根据文件后缀获取文件MINE
     * @param array $ext 文件后缀
     * @param array $mine 文件后缀MINE信息
     * @return string
     */
    public static function getFileMine($ext, $mine = [])
    {
        $mines = self::getMines();
        foreach (is_string($ext) ? explode(',', $ext) : $ext as $e) {
            $mine[] = isset($mines[strtolower($e)]) ? $mines[strtolower($e)] : 'application/octet-stream';
        }
        return join(',', array_unique($mine));
    }

    /**
     * 通过相对路径获得绝对路径
     * @param string $path 相对路径
     * @param boolean $is_file 是否文件,默认为目录
     * @return string
     */
    public static function buildFullPath($path, $is_file = false)
    {
        $path = tools()::get_absolute_path($path);
        $path = rtrim(str_replace('\\', '/', $path), '/');
        return ($is_file !== false) ? $path : $path . '/';
    }

    /**
     * 上传文件
     * @param string $upload_path
     * @return mixed
     */
    abstract public function upload($upload_path = '');

    public function getFiles()
    {
        $request  = request();
        $files    = $request->file();
        $max_size = 1024 * 1024 * 20;
        //如果参数中含有base64 则直接取base64
        if ($request->__get('_base64')) {
            return $this->getFilesFromBase64($request->__get('_base64'));
        }
        if ($request->__get('_stream')) {
            return $this->getFilesFromStream($request->__get('_stream'));
        }

        if (empty($files)) {
            throw new NotNotifyException('请上传文件或者选择小于10M的文件', -1);
        }
        $files_array = [];
        if (!validate()->fileSize($files, $max_size)) {
            throw new NotNotifyException('请上传小于10M的文件', -1);
        }
        if (!validate()->fileExt($files, $this->allow_ext)) {
            throw new NotNotifyException('不支持的文件类型', -1);
        }
        foreach ($files as $key => $file) {
            $upload_path = $this->get_upload_path();
            /* @var $file File */
            $ext       = $file->extension();//文件扩展名
            $real_path = $file->getRealPath();
            $file_md5  = $file->hash('MD5');
            $guid      = tools()::md5_guid($file_md5);
            if (strtolower($ext) == 'pem') {
                //微信证书强制上传到固定目录,NGINX设置不允许访问
                $upload_path = '/file/wechat/';
            }
            $save_path     = tools()::get_absolute_path($upload_path);
            $save_name     = $guid . '.' . $ext;
            $files_array[] =
                [
                    'ext'         => $ext,
                    'key'         => ltrim($upload_path . $save_name, '/'),
                    'hash'        => $file_md5,
                    'real_path'   => $real_path,
                    'save_name'   => $save_name,
                    'save_path'   => $save_path,
                    'upload_path' => $upload_path,
                    'guid'        => $guid,
                    'obj'         => $file
                ];
        }
        return $files_array;
    }

    public function getFilesFromStream($stream)
    {
        //匹配出图片的格式
        $files_array = [];
        $upload_path = $this->get_upload_path();
        $save_path   = tools()::get_absolute_path($upload_path);
        if ($stream) {
            $ext = 'png';
            $this->check_ext($ext);
            $md5            = md5($stream);
            $guid           = tools()::md5_guid($md5);
            $save_name      = $guid . '.' . $ext;
            $full_save_name = $save_path . $save_name;
            if (tools()::download($save_path, $save_name, $stream)) {
                $key           = ltrim($upload_path . $save_name, '/');
                $files_array[] =
                    [
                        'ext'       => $ext,
                        'key'       => $key,
                        'hash'      => $md5,
                        'real_path' => $full_save_name,
                        'save_name' => $save_name,
                        'save_path' => $save_path,
                        'obj'       => null,
                        'url'       => request()->domain() . '/' . $key
                    ];
            }
        } else {
            throw new Exception('stream数据校验失败');
        }
        return $files_array;
    }

    public function getFilesFromBase64($base64_image_content)
    {
        $base64_image_content = tools()::search_str('src="', '"', $base64_image_content) ?: $base64_image_content;
        //匹配出图片的格式
        $files_array = [];
        $upload_path = $this->get_upload_path();
        $save_path   = tools()::get_absolute_path($upload_path);
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_image_content, $result)) {
            $ext = strtolower($result[2]);
            $this->check_ext($ext);
            $md5            = md5($base64_image_content);
            $guid           = tools()::md5_guid($md5);
            $save_name      = $guid . '.' . $ext;
            $full_save_name = $save_path . $save_name;
            if (file_put_contents($full_save_name, base64_decode(str_replace($result[1], '', $base64_image_content)))) {
                $key           = ltrim($upload_path . $save_name, '/');
                $files_array[] =
                    [
                        'ext'       => $ext,
                        'key'       => $key,
                        'hash'      => $md5,
                        'real_path' => $full_save_name,
                        'save_name' => $save_name,
                        'save_path' => $save_path,
                        'obj'       => null,
                        'url'       => request()->domain() . '/' . $key
                    ];
            }
        } else {
            throw new Exception('base64数据校验失败');
        }
        return $files_array;
    }

    public function get_upload_path()
    {
        $upload_path = $this->get_uploads_path();
        $dir         = tools()::get_absolute_path($upload_path);
        try {
            !is_dir($dir) && !mkdir($dir, 0755, true);
        } catch (Throwable $e) {
            wr_log('目录创建失败:原因:' . $e->getMessage(), 1);
            if (strpos($e->getMessage(), 'File exists') === false) {
                throw new Exception('目录创建失败:' . $e->getMessage());
            }
        }
        return $upload_path;
    }

    protected function get_uploads_path()
    {
        return '/file/uploads/' . $this->getSid() . '/' . date('Ym') . '/' . date('d') . '/';
    }

    /**
     * @throws Exception
     */
    protected function check_ext($ext)
    {
        $allow_ext = explode(',', $this->allow_ext);
        if (!in_array(strtolower($ext), $allow_ext)) {
            throw new NotNotifyException('不支持的文件格式:' . strtolower($ext));
        }
    }

    public function getCode()
    {
        return $this->code;
    }

    public function getMsg()
    {
        return $this->msg;
    }

    protected function check_config_key($key_array, $config)
    {
        foreach ($key_array as $key) {
            if (!isset($config[$key])) {
                throw new Exception('Missing Config Of Key:-- [' . $key . ']');
            }
        }
    }

    protected function save_upload_note($result, $storage_type)
    {
        $db        = new FileUploadNote();
        $request   = request();
        $bid       = $request->__get('_bid') ?? config('app.super_admin_business_guid');
        $save_data = [];
        foreach ($result as $key => $val) {
            $val['bid']          = $bid;
            $val['user_guid']    = $request->__get('_user_guid');
            $val['storage_type'] = parse_name(trim(strrchr($storage_type, '\\'), '\\'));
            $val                 = tools()::replace_readonly_to_www($val);
            $save_data[]         = $val;
        }
        $db->saveAll($save_data, false);
    }
}
