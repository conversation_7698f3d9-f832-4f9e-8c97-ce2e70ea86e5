<?php

namespace Storage\Driver;

use Exception;
use Storage\Contracts\Config;
use Storage\Contracts\StorageInterface;
use think\facade\Filesystem;

/**
 * 腾讯云
 * Class Tencent
 * @package Storage\Driver\Tencent
 */
class Wechat extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Alipay constructor.
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        // $this->check_config_key(['appid'], $config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean|array
     * @throws Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $result = [];
        foreach ($files as $key => $file) {
            $request = request();
            $bid     = $request->__get('_bid');
            if (empty($bid)) {
                error('bid参数不能为空');
            }
            $config = get_config_by_bid($bid);
            $appid  = $config['appid'];
            if (empty($appid)) {
                error('商家未配置公众号');
            }
            if (!empty($file['obj'])) {
                $upload_path = $upload_path ? self::buildFullPath($upload_path) : $file['save_path'];
                $upload      = Filesystem::putFile('/file/uploads', $file['obj'], function () use ($file) {
                    return date('Ym') . '/' . date('d') . '/' . tools()::md5_guid($file['obj']->md5());
                });
            }
            $upload   = weixin($appid)::WeChatMedia()->uploadImg($file['key']);
            $result[] = [
                'hash' => $file['hash'],
                'key'  => $file['key'],
                'url'  => $upload['url'],
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }
}
