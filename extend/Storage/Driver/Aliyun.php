<?php

namespace Storage\Driver;

use OSS\OssClient;
use Storage\Contracts\Config;
use Storage\Contracts\Exception;
use Storage\Contracts\StorageInterface;

/**
 * 阿里云
 * Class Aliyun
 * @package Storage\Driver\Aliyun
 */
class <PERSON>yun extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * <PERSON>yun constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        $this->check_config_key(['access_key_id', 'access_key_secret'], $config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean|array
     * @throws \Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $result = [];
        foreach ($files as $key => $file) {
            $domain    = $this->config->get('domain');
            $bucket    = $this->config->get('bucket');
            $endpoint  = is_host() ? $this->config->get('endpoint') : $this->config->get('internal_endpoint');
            $endpoint  = $bucket . '.' . $endpoint;
            $ossClient = new OssClient($this->config->get('access_key_id'), $this->config->get('access_key_secret'), $endpoint, true);
            $upload    = $ossClient->putObject($bucket, $file['key'], file_get_contents($file['real_path']));
            $result[]  = [
                'hash' => $upload['content-md5'],
                'key'  => $file['key'],
                'url'  => $domain . '/' . $file['key']
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }
}
