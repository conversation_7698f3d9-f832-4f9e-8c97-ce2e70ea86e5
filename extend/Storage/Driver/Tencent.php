<?php

namespace Storage\Driver;

use Qcloud\Cos\Client;
use Storage\Contracts\Config;
use Storage\Contracts\Exception;
use Storage\Contracts\StorageInterface;

/**
 * 腾讯云
 * Class Tencent
 * @package Storage\Driver\Tencent
 */
class Tencent extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Tencent constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        $this->check_config_key(['appid', 'secret_id', 'secret_key', 'region'], $config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean|array
     * @throws \Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $result = [];
        foreach ($files as $key => $file) {
            $domain     = $this->config->get('domain');
            $region     = $this->config->get('region');
            $appid      = $this->config->get('appid');
            $bucket     = $this->config->get('bucket');
            $secret_id  = $this->config->get('secret_id');
            $secret_key = $this->config->get('secret_key');
            $config     = [
                'region'      => $region,
                'credentials' => [
                    'appId'     => $appid,
                    'secretId'  => $secret_id,
                    'secretKey' => $secret_key]
            ];
            $cosClient  = new Client($config);
            $bucket     = $bucket . '-' . $appid;
            $upload     = $cosClient->putObject(['Bucket' => $bucket, 'Key' => $file['key'], 'Body' => file_get_contents($file['real_path'])]);
            $result[]   = [
                'hash' => $file['hash'],
                'key'  => $file['key'],
                'url'  => $domain . '/' . $file['key'],
                //'url'  => $upload['ObjectURL']
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }
}
