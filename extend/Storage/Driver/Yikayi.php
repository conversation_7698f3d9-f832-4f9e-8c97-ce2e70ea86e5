<?php


namespace Storage\Driver;

use Exception;
use Storage\Contracts\Config;
use Storage\Contracts\StorageInterface;
use think\facade\Filesystem;
use xieyongfa\yky\Yky;

/**
 * 一卡易文件上传类
 * Class Yikayi
 * @package Storage\Driver\Yikayi
 */
class Yikayi extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean
     * @throws Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $result = [];
        foreach ($files as $key => $file) {
            $url = isset($file['url']) ? $file['url'] : request()->domain() . '/' . $file['key'];
            if (!empty($file['obj'])) {
                $upload_path      = $upload_path ? self::buildFullPath($upload_path) : $file['save_path'];
                $upload_file_path = '/file/uploads';
                $upload           = Filesystem::putFile($upload_file_path, $file['obj'], function () use ($file) {
                    return date('Ym') . '/' . date('d') . '/' . tools()::md5_guid($file['obj']->md5());
                });
                // 上传到一卡易
                $yky_upload    = Yky::Upload($this->config->get());
                $upload_result = $yky_upload->UploadImage($url);
                if ($upload_result === false) {
                    throw new Exception('图片上传失败:' . $yky_upload->message);
                }
                $web_url  = $upload_result['imageFullPath'];
                $result[] = [
                    'hash' => $file['hash'],
                    'key'  => $file['key'],
                    'url'  => $web_url,
                ];
            }

        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }
}
