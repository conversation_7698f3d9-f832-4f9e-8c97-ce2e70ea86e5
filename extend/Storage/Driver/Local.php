<?php


namespace Storage\Driver;

use Exception;
use SplFileObject;
use Storage\Contracts\Config;
use Storage\Contracts\StorageInterface;
use think\facade\Filesystem;

/**
 * 本地文件上传类
 * Class Local
 * @package Storage\Driver\Local
 */
class Local extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean
     * @throws Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $result = [];
        foreach ($files as $key => $file) {
            if (!empty($file['obj'])) {
                $upload_path = $upload_path ? self::buildFullPath($upload_path) : $file['save_path'];
                //                if ($file['ext'] == 'pem') {
                //                    //微信证书强制上传到固定目录
                //                    $upload_file_path = '/file/wechat';
                //                } else {
                //                    $upload_file_path = '/file/uploads';
                //                }
                $upload = Filesystem::putFile($file['upload_path'], $file['obj'], function () use ($file) {
                    return tools()::md5_guid($file['obj']->md5());
                    //                    if ($file['ext'] == 'pem') {
                    //                        //微信证书强制上传到固定目录
                    //                        return tools()::md5_guid($file['obj']->md5());
                    //                    } else {
                    //                        return tools()::md5_guid($file['obj']->md5());
                    //                    }
                });
            }
            $result[] = [
                'hash' => $file['hash'],
                'key'  => $file['key'],
                'url'  => $file['url'] ?? request()->domain() . '/' . $file['key'],
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }

    public function delete($file)
    {
        $file = self::buildFullPath($file, true);
        if (file_exists($file) && is_file($file)) {
            return unlink($file);
        } else {
            $this->msg = '文件不存在';
            return false;
        }
    }

    function down_load($url, $file_name = "")
    {
        ignore_user_abort(); // run script in background
        set_time_limit(0); // run script forever
        $dir       = $this->get_upload_path();
        $data      = curl()->get($url)->get_body();
        $file_name = $file_name ?: tools()::md5_guid(md5($data));
        $ext       = self::getFileMineByUrl($url);
        $file_name = $file_name . '.' . $ext;
        $save_path = self::buildFullPath($dir . $file_name, true);
        $file      = fopen($save_path, "w");
        fputs($file, $data);//写入文件
        fclose($file);
        //检测文件是否大小等于0,是则说明失败,删除掉,并返回false
        $file_obj  = new SplFileObject($save_path);
        $file_size = $file_obj->getSize();
        if ($file_size == 0) {
            unlink($save_path);
            return false;
        }
        return request()->domain() . rtrim(str_replace('\\', '/', $dir . $file_name), '/');
    }

    /**
     * PHP 非递归实现查询该目录下所有文件
     * @param string $dir
     * @return array
     */
    function get_file_list($dir = 'file/uploads')
    {
        $dir_prefix = env('ROOT_PATH') . 'public/';
        $dir        = self::buildFullPath($dir);
        if (!is_dir($dir))
            return [];
        // 栈，默认值为传入的目录
        $dirs = [$dir];
        // 放置所有文件的容器
        $file_list = [];
        do {
            // 弹栈
            $dir = array_pop($dirs);
            // 扫描该目录
            $tmp = scandir($dir);
            foreach ($tmp as $f) {
                // 过滤. ..
                if ($f == '.' || $f == '..')
                    continue;
                // 组合当前绝对路径
                $path = $dir . $f;
                // 如果是目录，压栈。
                if (is_dir($path)) {
                    array_push($dirs, $path . '/');
                } else if (is_file($path)) { // 如果是文件，放入容器中
                    $file_list [] = tools()::array_iconv(str_replace($dir_prefix, '', $path));
                }
            }
        } while ($dirs); // 直到栈中没有目录
        return $file_list;
    }
}
