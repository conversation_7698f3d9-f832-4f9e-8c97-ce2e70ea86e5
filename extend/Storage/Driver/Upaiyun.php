<?php

namespace Storage\Driver;

use Storage\Contracts\Config;
use Storage\Contracts\Exception;
use Storage\Contracts\StorageInterface;
use Upyun\Config as UpaiyunConfig;
use Upyun\Upyun;

/**
 * 又拍云
 * Class Upaiyun
 * @package Storage\Driver\Upaiyun
 */
class Upaiyun extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Upaiyun constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        $this->check_config_key(['service_name', 'username', 'password'], $config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean|array
     * @throws \Exception
     */
    public function upload($upload_path = '')
    {
        $files  = $this->getFiles();
        $domain = $this->config->get('domain');
        $result = [];
        foreach ($files as $key => $file) {
            $bucketConfig = new UpaiyunConfig($this->config->get('service_name'), $this->config->get('username'), $this->config->get('password'));
            $upload       = new Upyun($bucketConfig);
            $upload->write($file['key'], file_get_contents($file['real_path']));
            $result[] = [
                'hash' => $file['hash'],
                'key'  => $file['key'],
                'url'  => $domain . '/' . $file['key']
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }
}
