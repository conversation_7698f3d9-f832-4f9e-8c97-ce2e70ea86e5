<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Storage\Driver;

use Qiniu\Auth;
use Qiniu\Storage\BucketManager;
use Qiniu\Storage\UploadManager;
use Storage\Contracts\Config;
use Storage\Contracts\Exception;
use Storage\Contracts\StorageInterface;

/**
 * 七牛云
 * Class Qiniu
 * @package Storage\Driver\Qiniu
 */
class Qiniu extends StorageInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Qiniu constructor.
     * @param array $config
     * @throws \Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        $this->check_config_key(['access_key', 'secret_key'], $config);
    }

    /**
     * 应用参数
     * @param string $upload_path
     * @return mixed|boolean|array
     * @throws Exception
     */
    public function upload($upload_path = '')
    {
        wr_log('图片上传功能维护中,请联系管理员', 1);
        $this->msg = '请上传文件哦';
        return false;
        $files = $this->getFiles();
        if (empty($files)) {
            $this->msg = '请上传文件哦';
            return false;
        }
        $result        = [];
        $uploadManager = new UploadManager();
        // 生成上传Token
        $token = $this->get_upload_token();
        foreach ($files as $key => $file) {
            $upload   = $uploadManager->putFile($token, $file['key'], $file['real_path']);
            $upload   = $this->_parseResult($upload);
            $result[] = [
                'hash' => $file['hash'],
                'key'  => $upload['key'],
                'url'  => $this->config->get('domain') . '/' . $upload['key'],
            ];
        }
        $this->save_upload_note($result, __CLASS__);
        return (count($result) == 1) ? reset($result) : $result;
    }

    /**
     * @param $bucket_name
     * @return mixed|string
     * 只有设置到配置的bucket才会使用缓存功能
     */
    private function get_upload_token($bucket_name = '')
    {
        $cache_key = 'qiniu_upload_token';
        if (empty($bucket_name)) {
            $bucket_name = $this->get_bucket();
        }
        $token = cache($cache_key);
        if (empty($token)) {
            $auth  = $this->get_auth();
            $token = $auth->uploadToken($bucket_name);
            cache($cache_key, $token, 3600);
        }
        return $token;
    }

    /**
     * @return string
     * 获取bucket
     */
    private function get_bucket()
    {
        return $this->config->get('bucket');
    }

    protected function get_auth()
    {
        return new Auth($this->config->get('access_key'), $this->config->get('secret_key'));
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     * @throws Exception
     */
    protected function _parseResult($result)
    {
        $result = tools()::object2array($result);
        if (!is_array($result)) {
            $this->msg = '结果解析失败';
            throw new Exception($this->msg, -2);
        }
        list($result, $error) = $result;
        if ($error !== null) {
            $error = end($error);
            wr_log($error);
            wr_log($result);
            $this->msg  = $error['error'] ?? '未知错误';
            $this->code = $error['statusCode'] ?? -1;
            throw new Exception($this->msg, $this->code);
        }
        return $result;
    }

    /**
     * 通过URL下载文件
     * @param string $url
     * @return string
     * @throws Exception
     */
    public function down_load($url)
    {
        $auth          = $this->get_auth();
        $bucketManager = new BucketManager($auth);
        $key           = ltrim($this->get_uploads_path(), '/');
        $ext           = self::getFileMineByUrl($url);
        $result        = $bucketManager->fetch($url, $this->get_bucket(), $key . tools()::md5_guid(md5($url)) . '.' . $ext);
        $result        = $this->_parseResult($result);
        return $this->config->get('domain') . '/' . $result['key'];
    }

    public function delete($file)
    {
        $auth          = $this->get_auth();
        $bucketManager = new BucketManager($auth);
        $result        = $bucketManager->delete($this->get_bucket(), $file);
        if ($result === null) {
            return true;
        }
        return $this->_parseError($result);
    }

    protected function _parseError($error)
    {
        $error = tools()::object2array($error);
        //todo 待完善
    }

    /**
     * 通过前缀查询文件列表,必须只能该目录下没有文件夹
     * @param string $prefix
     * @return array|boolean
     * @throws Exception
     */
    public function get_file_list($prefix = '/file/uploads')
    {
        $auth          = $this->get_auth();
        $bucketManager = new BucketManager($auth);
        // 上次列举返回的位置标记，作为本次列举的起点信息。
        $marker = '';
        // 本次列举的条目数
        $limit     = 100;
        $delimiter = '/';
        // 列举文件
        $result = $bucketManager->listFiles($this->get_bucket(), $prefix, $marker, $limit, $delimiter);
        $result = $this->_parseResult($result);
        return $result['items'];
    }
}
