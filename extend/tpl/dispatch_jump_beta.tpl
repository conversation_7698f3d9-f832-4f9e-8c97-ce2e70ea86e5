{__NOLAYOUT__}<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>跳转提示</title>
    <style type="text/css">
        *{ padding: 0; margin: 0; text-align:center;}
         @font-face {font-family: Open Sans;src: url('/static/css/fonts/OpenSans-light.woff');font-weight: normal;font-style: normal}
        body{ background: #fff; font-family: "Open Sans","PingFangSC-Light",Arial,"Hiragino Sans GB","Microsoft YaHei","微软雅黑","STHeiti","WenQuanYi Micro Hei",Sim<PERSON>un,sans-serif; color: #333; font-size: 16px; }
        .system-message{ padding: 24px 48px; }
        .system-message h1{ font-size: 100px; font-weight: normal; line-height: 120px; margin-bottom: 12px; }
        .system-message .jump{ padding-top: 10px; }
        .system-message .jump a{ color: #333; }
        .system-message .success,.system-message .error{ line-height: 1.8em; font-size: 36px; }
        .system-message .detail{ font-size: 12px; line-height: 20px; margin-top: 12px; display: none; }
    </style>
</head>
<body>
    <div class="system-message">
        <?php switch ($code) {?>
            <?php case 0:?>
            <h1>:)</h1>
            <p class="success"><?php echo(strip_tags($msg));?></p>
            <?php break;?>
            <?php default:?>
            <h1>:(</h1>
            <p class="error"><?php echo(strip_tags($msg));?></p>
            <?php break;?>
        <?php } ?>
        <p class="detail"></p>
        <?php if ($wait) {?>
        <p class="jump">
             <b id="wait"><?php echo($wait);?></b>秒后页面自动
        <?php if ($url) {?>
        <a id="href" href="<?php echo($url);?>">跳转</a>
        <?php } ?>
        <?php if (!$url) {?>
        关闭
        <?php } ?>
        </p>
        <?php } ?>
    </div>
    <script type="text/javascript">
        (function(){
            var wait = document.getElementById('wait'),
                href = document.getElementById('href');
            if(wait){
                var interval = setInterval(function(){
                    var time = --wait.innerHTML;
                    if(time <= 0) {
                        clearInterval(interval);
                        if(href){
                            location.href = href.href;
                        }else{
                            closeWindow();
                        }
                    };
                }, 1000);
            }
        })();
        function closeWindow() {
            try {
                WeixinJSBridge.call('closeWindow')
            } catch (e) {
                window.open("about:blank","_self")
                window.close();
                console.log(e);
            }
        }
    </script>
</body>
</html>
