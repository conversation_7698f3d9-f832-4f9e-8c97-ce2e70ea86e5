<?php

// +----------------------------------------------------------------------
// | pay-php-sdk
// +----------------------------------------------------------------------
// | 版权所有 2014~2017 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/pay-php-sdk
// +----------------------------------------------------------------------
// | 项目设计及部分源码参考于 yansongda/pay，在此特别感谢！
// +----------------------------------------------------------------------

namespace Reward;

use Reward\Contracts\Config;
use Reward\Contracts\Exception;
use Reward\Contracts\RewardInterface;
use Reward\Driver\Baiwang;
use Reward\Driver\Lianbao;
use Reward\Driver\Yunlianhui;
use think\facade\Config as Configs;

/**
 * Class Reward
 * @method Yunlianhui yunlianhui($options = []) static 云联惠
 * @method Lianbao lianbao($options = []) static 联宝
 * @method Baiwang baiwang($options = []) static 百望
 *
 * @package Storage
 */
class Reward
{

    /**
     * @var Config
     */
    private static $config = [];

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws \Exception
     */
    public static function __callStatic($name, $arguments)
    {
        return self::driver($name);
    }

    /**
     * 指定驱动器
     * @param string $driver 默认本地
     * @return RewardInterface
     * @throws Exception
     */
    public static function driver($driver)
    {
        self::$config = new Config(Configs::get('reward'));

        if (empty($driver)) {
            throw new Exception("Driver can not be empty.");
        }
        if (is_null(self::$config->get($driver))) {
            throw new Exception("Driver [$driver]'s Config is not defined.");
        }
        if (!file_exists(__DIR__ . '/Driver/' . ucfirst($driver) . '.php')) {
            throw new Exception("Driver [$driver] is not supported.");
        }
        $gateway = __NAMESPACE__ . '\\Driver\\' . ucfirst($driver);
        return new $gateway(self::$config->get($driver));
    }
}
