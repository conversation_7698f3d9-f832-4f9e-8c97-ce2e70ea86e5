<?php

namespace Reward\Contracts;


/**
 * 存储接口
 * Interface GatewayInterface
 * @package Storage\Contracts
 */
abstract class RewardInterface
{
    protected $code = -999;
    protected $msg = '';

    /**
     * 奖励
     * @param array $data
     * @return mixed
     */
    abstract public function reward($data);

    /**
     * 注册
     * @param string $buyer_mobile
     * @param string $third_account
     * @param string $bid
     * @return mixed
     */
    abstract public function register($buyer_mobile, $third_account, $bid);

    /**
     * 查单
     * @param array $data
     * @return mixed
     */
    abstract public function query($data);

    /**
     * 撤销订单
     * @param array $data
     * @return mixed
     */
    abstract public function revoke($data);

    public function getCode()
    {
        return $this->code;
    }

    public function getMsg()
    {
        return $this->msg;
    }

    protected function check_config_key($key_array, $config)
    {
        foreach ($key_array as $key) {
            if (!isset($config[$key])) {
                throw new Exception('Missing Config Of Key:-- [' . $key . ']');
            }
        }
    }

}
