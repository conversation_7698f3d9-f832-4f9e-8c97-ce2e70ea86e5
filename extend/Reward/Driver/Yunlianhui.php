<?php

namespace Reward\Driver;

use Reward\Contracts\Config;
use Reward\Contracts\Exception;
use Reward\Contracts\RewardInterface;

/**
 * 云联惠
 * Class Yunlianhui
 * @package Reward\Driver\Yunlianhui
 */
class Yunlianhui extends RewardInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Yun<PERSON>hui constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $this->config = new Config($config);
        $this->check_config_key([], $config);
    }

    /**
     * 奖励
     * @param array $data
     * @return mixed|boolean|array
     */
    public function reward($data)
    {

    }

    /**
     * 注册会员
     * @param string $buyer_mobile
     * @param string $third_account
     * @param string $bid
     * @return mixed|boolean|array
     */
    public function register($buyer_mobile, $third_account, $bid)
    {

    }

    /**
     * 查询订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function query($data)
    {

    }

    /**
     * 撤销订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function revoke($data)
    {

    }
}