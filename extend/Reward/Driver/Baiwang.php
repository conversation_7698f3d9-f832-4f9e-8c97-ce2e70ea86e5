<?php

namespace Reward\Driver;

use Reward\Contracts\Config;
use Reward\Contracts\Exception;
use Reward\Contracts\RewardInterface;

/**
 * 百望
 * Class Baiwang
 * @package Reward\Driver\Baiwang
 */
class Baiwang extends RewardInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Baiwang constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $this->config = new Config($config);
        $this->check_config_key([], $config);
    }

    /**
     * 奖励
     * @param array $data
     * @return mixed|boolean|array
     */
    public function reward($data)
    {
        $bw     = \OpenApi\BaiWang::getInstance();
        $result = $bw->createOrder($data);
        if ($result === false) {
            $this->code = $bw->errcode;
            $this->msg  = $bw->errmsg;
        } else {
            $result['order_id'] = $result['order_no'];
        }
        return $result;
    }

    /**
     * 注册会员
     * @param string $buyer_mobile
     * @param string $third_account
     * @param string $bid
     * @return mixed|boolean|array
     */
    public function register($buyer_mobile, $third_account, $bid)
    {
        $bw     = \OpenApi\BaiWang::getInstance();
        $result = $bw->register($buyer_mobile, $third_account, $bid);
        if ($result === false) {
            $this->code = $bw->errcode;
            $this->msg  = $bw->errmsg;
        }
        return $result;
    }

    /**
     * 查询订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function query($data)
    {

    }

    /**
     * 撤销订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function revoke($data)
    {
        $bw     = \OpenApi\BaiWang::getInstance();
        $result = $bw->refundOrder($data['order_id'], $data['yky_bill_number'] . '退单扣除');
        if ($result === false) {
            $this->code = $bw->errcode;
            $this->msg  = $bw->errmsg;
        }
        return $result;
    }
}