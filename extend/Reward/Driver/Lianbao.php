<?php

namespace Reward\Driver;

use Reward\Contracts\Config;
use Reward\Contracts\Exception;
use Reward\Contracts\RewardInterface;

/**
 * 联宝
 * Class Lianbao
 * @package Reward\Driver\Lianbao
 */
class Lianbao extends RewardInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * Lianbao constructor.
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $this->config = new Config($config);
        $this->check_config_key([], $config);
    }

    /**
     * 奖励
     * @param array $data
     * @return mixed|boolean|array
     */
    public function reward($data)
    {
        $lianbao                  = new \OpenApi\LianBao();
        $lianbao->recommend_phone = $data['seller_account'];
        $result                   = $lianbao->score_order($data['buyer_account'], $data['order_no'], tools()::nc_price_calculate($data['pay_cash'], '/', 100), $data['memo']);
        if ($result === false) {
            $this->code = $lianbao->errcode;
            $this->msg  = $lianbao->errmsg;
        } else {
            $result['order_id'] = $result['order_number'];
        }
        return $result;
    }

    /**
     * 注册会员
     * @param string $buyer_mobile
     * @param string $third_account
     * @param string $bid
     * @return mixed|boolean|array
     */
    public function register($buyer_mobile, $third_account, $bid)
    {
        $lianbao                  = new \OpenApi\LianBao();
        $lianbao->recommend_phone = $third_account;
        $result                   = $lianbao->add_client($buyer_mobile, $buyer_mobile);
        if (isset($result['password'])) {
            send_sms('恭喜您注册成功,您的登录密码是:' . $result['password'], $buyer_mobile, $bid);
        }
        return $result;
    }

    /**
     * 查询订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function query($data)
    {

    }

    /**
     * 撤销订单
     * @param array $data
     * @return mixed|boolean|array
     */
    public function revoke($data)
    {
    }
}