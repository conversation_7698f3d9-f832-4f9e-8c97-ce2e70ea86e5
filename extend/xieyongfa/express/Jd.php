<?php

namespace xieyongfa\express;

use app\model\ExpressAuthJd;
use Lop\LopOpensdkPhp\Filters\ErrorResponseFilter;
use Lop\LopOpensdkPhp\Filters\IsvFilter;
use Lop\LopOpensdkPhp\Options;
use Lop\LopOpensdkPhp\Support\DefaultClient;
use Lop\LopOpensdkPhp\Support\GenericRequest;
use xieyongfa\express\Contracts\ExpressInterface;

/**
 * Kuai di niao
 * <AUTHOR>
 *
 */
class Jd extends Express implements ExpressInterface
{
    protected $url = 'https://api.jdl.com';
    protected $debug_url = 'https://uat-api.jdl.com';
    protected $service_code = '';
    protected $path = '';
    protected $domain = '';
    protected $msg = 'success';
    protected $auth_url = 'https://oauth.jdl.com'; //生产环境域名
    protected $debug_auth_url = 'https://uat-oauth.jdl.com'; //预发环境域名


    protected $print_api_url = 'https://www.kdniao.com/External/PrintOrder.aspx'; // 批量打印接口地址
    protected $ip_service_url = 'http://www.kdniao.com/External/GetIp.aspx'; //IP服务地址
    protected $qrery_url = 'https://poll.kuaidi100.com/poll/query.do'; // 查询物流信息地址

    public function getError()
    {
        return $this->msg;
    }

    //获取UUID
    public function create_uuid()
    {
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
            . substr($chars, 8, 4) . '-'
            . substr($chars, 12, 4) . '-'
            . substr($chars, 16, 4) . '-'
            . substr($chars, 20, 12);
        return $uuid;
    }

    public function getAccessToken()
    {
        return $this->getConfigByKey('access_token');
    }

    protected function request($data)
    {
        $baseUri     = $this->get_url();
        $appKey      = $this->getAppKey();
        $appSecret   = $this->getAppSecret();
        $accessToken = $this->getAccessToken();
        $domain      = $this->domain;
        $path        = $this->path;
        $algorithm   = "md5-salt";
        $body        = json_encode([$data], JSON_UNESCAPED_UNICODE);
        $timestamp   = date("Y-m-d H:i:s");
        $content     = implode("", array(
            $appSecret,
            "access_token", $accessToken,
            "app_key", $appKey,
            "method", $path,
            "param_json", $body,
            "timestamp", $timestamp,
            "v", "2.0",
            $appSecret
        ));
        $sign        = $this->sign($algorithm, $content, $appSecret);
        $query       = http_build_query(array(
            "LOP-DN"       => $domain,
            "app_key"      => $appKey,
            "access_token" => $accessToken,
            "timestamp"    => $timestamp,
            "v"            => "2.0",
            "sign"         => $sign,
            "algorithm"    => $algorithm
        ));
        $uri         = $baseUri . $path . "?" . $query;
        $url         = $this->get_url() . $this->path . "?" . $query;
        $result      = curl()->set_http_errors(false)->form_params()->json()->post($url, [$data])->get_body();
        return $this->_parseResult($result);
    }

    protected function _parseResult($result)
    {
        if (isset($result['code']) && $result['code'] != 0) {
            $msg       = $result['msg'] ?? '未知错误';
            $sub_msg   = $result['subMsg'] ?? '';
            $this->msg = $msg . $sub_msg;
            return false;
            throw new \Exception($this->msg);
        }
        if (isset($result['error_response'])) {
            $msg       = $result['error_response']['zh_desc'] ?? '未知错误';
            $this->msg = $msg;
            return false;
            throw new \Exception($this->msg);
        }


        return $result['data'] ?? $result;
    }

    /**
     * 打印订单
     * @param array|string $expressNo 单号
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        $express_no_array = is_array($expressNo) ? $expressNo : [$expressNo];
        $documents        = [];
        foreach ($express_no_array as $express_no) {
            $documents[] = [
                'masterWaybillNo' => $express_no
            ];
        }
        //                requestID: "xxx",
        //                accessToken: "xxx",
        //                templateCode: "xxx",
        //                templateVersion: "",
        //                documents: [
        //                    {
        //                        masterWaybillNo: "xxx"
        //                    }
        //                ],
        //                extJson: {},
        //                customTemplateCode: "xxx"
        $print_data  = [
            'requestID'    => $this->create_uuid(),
            'accessToken'  => $this->getAccessToken(),
            'templateCode' => 'fm_150_standard_' . $this->getPartnerrId(),
            'documents'    => $documents,
        ];
        $config_data = [
            'env'       => $this->is_debug() ? 'sbox' : 'pro',
            //            'lodopFn'   => $this->is_debug() ? 'PREVIEW' : 'PRINT',
            'lodopFn'   => 'PREVIEW',
            'partnerID' => $this->getPartnerrId(),
        ];
        return [
            'channel_name' => $this->getChannelName(),
            'type'         => 'js',
            'config_data'  => $config_data,
            'print_data'   => $print_data
        ];
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function getPrintFile($orderId, $expressNo, $portName)
    {
        $order_id_array     = is_array($orderId) ? $orderId : [$orderId];
        $express_no_array   = is_array($expressNo) ? $expressNo : [$expressNo];
        $this->service_code = 'COM_RECE_CLOUD_PRINT_WAYBILLS';
        $documents          = [];
        foreach ($express_no_array as $express_no) {
            $documents[] = [
                'masterWaybillNo' => $express_no
            ];
        }
        $requestData = [
            'version'      => '2.0',
            'fileType'     => 'pdf',
            'sync'         => true,
            'templateCode' => 'fm_76130_standard_' . $this->getPartnerrId(),
            //            'customTemplateCode'=>'',
            'documents'    => $documents
        ];
        $result      = $this->request($requestData);
        if ($result === false) {
            throw new \Exception($this->msg);
        }
        $files         = $result['obj']['files'];
        $token         = $files[0]['token'];
        $url           = $files[0]['url'];
        $file_path     = '/temp/pdf/';
        $file_name     = $express_no_array[0] . '.pdf';
        $absolute_path = tools()::get_absolute_path($file_path);
        $download      = curl()->set_header(['X-Auth-token' => $token])->get($url)->download($absolute_path, $file_name);
        return ['channel_name' => $this->getChannelName(), 'type' => 'pdf', 'pdf_url' => tools()::path_to_web($file_path . $file_name)];
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        $this->service_code = 'EXP_RECE_CREATE_ORDER';
        //快递公司编码为空
        if (empty($expressCode)) {
            return ['status' => '0', 'message' => '快递公司编码为空'];
        }

        //订单验证
        if (empty($orderId)) {
            return ['status' => '0', 'message' => '订单编号不能为空'];
        }

        //收发件人验证
        $ret = $this->checkSenderOrReceiver($sender, 1);
        if ($ret['status'] == '0') {
            return $ret;
        }

        $ret = $this->checkSenderOrReceiver($receiver, 2);
        if ($ret['status'] == '0') {
            return $ret;
        }

        //$sender => $from
        $from = [
            'country'     => 'CN',
            'contactType' => 1, //地址类型： 1，寄件方信息 2，到件方信息
            'company'     => $sender['company'] ?? '',
            'contact'     => $sender['name'],
            'mobile'      => $sender['mobile'] ?? '',
            'tel'         => $sender['tel'] ?? '',
            'province'    => rtrim($sender['province'], '市'),
            'city'        => $sender['city'],
            'county'      => $sender['district'],
            'address'     => $sender['address'],
            'postCode'    => $sender['post_code'] ?? '',
        ];

        //receiver => $to
        $to = [
            'country'     => 'CN',
            'contactType' => 2, //地址类型： 1，寄件方信息 2，到件方信息
            'company'     => $sender['company'] ?? '',
            'contact'     => $receiver['name'],
            'mobile'      => $receiver['mobile'] ?? '',
            'tel'         => $sender['tel'] ?? '',
            'province'    => rtrim($receiver['province'], '市'),
            'city'        => $receiver['city'],
            'county'      => $receiver['district'],
            'address'     => $receiver['address'],
            'postCode'    => $receiver['post_code'] ?? '',
        ];

        //commodity
        $commodity = [];
        if (!empty($options['commodity'])) {
            if (isset($options['commodity']['name'])) {
                $commodity[] = ['name' => $options['commodity']['name']];
            } else {
                foreach ($options['commodity'] as $val) {
                    if (is_array($val) && !empty($val['name'])) {
                        $commodity[] = ['name' => $val['name']];
                    }
                }
            }
        }
        if (empty($commodity)) {
            $commodity[] = ['name' => '商品'];
        }

        //AddServices

        $orderData = [
            'language'           => 'zh_CN',
            'orderId'            => $orderId,
            //            'codingMapping'    => '',
            //            'codingMappingOut' => '',
            'cargoDetails'       => $commodity, //托寄物信息
            'contactInfoList'    => [
                $from, $to
            ],
            'monthlyCard'        => $options['month_code'] ?? '',
            'expressTypeId'      => 1, //快件产品类别， 支持附录 《快件产品类别表》 的产品编码值，仅可使用与顺丰销售约定的快件产品
            'isReturnRoutelabel' => 1, //是否返回路由标签： 默认1， 1：返回路由标签， 0：不返回；除部分特殊用户外，其余用户都默认返回
            //            'cargoDesc'          => '货物'

            //            'PayType'               => $options['pay_type'] ?? 1, //邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付
            //            'ExpType'               => $options['exp_type'] ?? 1, //快递类型：1-标准快件，其它量看文档
            //            'TransType'             => $options['trans_type'] ?? 1, //运输方式 1-陆运  2-空运 不填默认为1
            //            'IsNotice'              => $options['is_notice'] ?? 1, //是否通知快递员上门揽件 0-通知, 1-不通知 不填则默认为1
            //            'StartDate'             => $options['start_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            //            'EndDate'               => $options['end_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            //            'CustomerName'          => $options['customer_name'] ?? '',
            //            'CustomerPwd'           => $options['customer_pwd'] ?? '',
            //            'SendSite'              => $options['send_site'] ?? '',
            //            'SendStaff'             => $options['send_staff'] ?? '',
            //            'IsReturnPrintTemplate' => $options['need_print_tpl'] ?? 0,
            //            'Remark'                => $options['remark'] ?? '', //备注
            //            'Sender'                => $from,
            //            'Receiver'              => $to,
        ];
        if (!empty($options['template_size'])) {
            $requestData['TemplateSize'] = $options['template_size'];
        }
        $result = $this->request($orderData);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->msg];
        }

        //结果order
        $resultOrder = [
            'order_id'     => $orderId, //订单号
            'response'     => $result,
            'express_code' => $expressCode, //快递公司编号
            'express_no'   => $result['waybillNoInfoList'][0]['waybillNo'] ?? '', //快递单号
            'dest_code'    => $result['originCode'] ?? '', //目的地区域编码
            'print_tpl'    => '', //打印模板html内容
        ];

        return ['status' => '1', 'message' => 'success', 'order' => $resultOrder];
    }

    protected function get_url()
    {
        return $this->is_debug() ? $this->debug_url : $this->url;
    }

    protected function get_auth_url()
    {
        return $this->is_debug() ? $this->debug_auth_url : $this->auth_url;
    }

    protected function is_debug()
    {
        return (bool)$this->getConfigByKey('debug');
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @link https://open.sf-express.com/Api/ApiDetails?level3=252&category=1&apiClassify=1
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        //        if (empty($expressCode) || empty($expressNo) || empty($orderId)) {
        //            return ['status' => '0', 'message' => '快递编码、运单号、订单编号不能为空'];
        //        }
        $this->service_code = 'EXP_RECE_UPDATE_ORDER';

        $requestData = [
            'orderId'  => $orderId,
            'dealType' => 2, //客户订单操作标识: 1:确认 (丰桥下订单接口默认自动确认，不需客户重复确认，该操作用在其它非自动确认的场景) 2:取消
        ];


        $result = $this->request($requestData);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->msg];
        }
        //        if (isset($result['status']) && $result['status'] == '0') {
        //            return $result;
        //        }

        //        if (empty($result) || !isset($result['Success'])) {
        //            return ['status' => '0', 'message' => '接口返回数据有误'];
        //        }
        //        if (!$result['Success']) {
        //            return ['status' => '0', 'message' => $result['Reason'] ?? '接口返回结果失败'];
        //        }
        return ['status' => '1', 'message' => '取消成功'];
    }

    protected function sign($algorithm, $data, $secret)
    {
        if ($algorithm == "md5-salt") {
            return md5($data);
        } else if ($algorithm == "HMacMD5") {
            return base64_encode(hash_hmac("md5", $data, $secret, true));
        } else if ($algorithm == "HMacSHA1") {
            return base64_encode(hash_hmac("sha1", $data, $secret, true));
        } else if ($algorithm == "HMacSHA256") {
            return base64_encode(hash_hmac("sha256", $data, $secret, true));
        } else if ($algorithm == "HMacSHA512") {
            return base64_encode(hash_hmac("sha512", $data, $secret, true));
        }
        throw new Exception("Algorithm " . $algorithm . " not supported yet");
    }

    public function updateAuthNote($bid, $guid, $params)
    {
        $express_channel_id = $params['express_channel_id'];
        $db                 = new ExpressAuthJd();
        $map                = [
            ['bid', '=', $bid],
            ['guid', '=', $guid]
        ];
        $update_data        = [];
        if (isset($params['customer_code'])) {
            $update_data['customer_code'] = $params['customer_code'];
        }
        if (!empty($update_data)) {
            $result = $db::update($update_data, $map);
        }
        return true;
    }

    public function getAuthNote($bid, $channel_id)
    {
        $db  = new ExpressAuthJd();
        $map = [
            ['bid', '=', $bid],
            ['express_channel_id', '=', $channel_id]
        ];
        return $db->where($map)->order(['access_expire' => 'DESC'])->findOrEmpty();
    }

    protected function setCustomerCodeAndAccessToken($query_data)
    {
        $order_guid = $query_data['order_guid'] ?? '';
        $bid        = $query_data['bid'] ?? '';
        if (empty($bid)) {
            $this->msg = '该订单对应商家未京东授权,无法查询';
            return false;
        }
        $channel_id = $this->getChannelId();
        if (empty($channel_id)) {
            $this->msg = 'channel_id 为空';
            return false;
        }
        $last_auth_note = $this->getAuthNote($bid, $channel_id);
        if ($last_auth_note->isEmpty()) {
            $this->msg = '商家没有京东授权,无法查询物流信息';
            return false;
        }
        //后续判断授权是否过期 通过 access_expire 字段
        if (tools()::is_expired($last_auth_note['access_expire'])) {
            $this->msg = '商家京东授权已过期,请重新授权';
            return false;
        }
        if (empty($last_auth_note['customer_code'])) {
            $this->msg = '商家没有填写客户编码,无法查询物流信息';
            return false;
        }
        $this->setConfigByKey('access_token', $last_auth_note['access_token']);
        $this->setConfigByKey('customer_code', $last_auth_note['customer_code']);
        return true;

    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';

        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }


        $result = $this->setCustomerCodeAndAccessToken($query_data);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->msg];
        }
        $this->path   = '/ecap/v1/orders/trace/query';
        $this->domain = 'ECAP'; //对接方案的编码，应用订购对接方案后可在订阅记录查看
        $data         = [
            'orderOrigin'  => 1,
            'waybillCode'  => $expressNo,
            'customerCode' => $this->getCustomerCode()
        ];
        $result       = $this->request($data);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->msg];
        }
        $traces                = $this->_parseTrace($result);
        $express_status        = $this->_parseExpressStatus($result); // 物流状态
        $express_name          = '京东快递';//快递公司名称
        $express_logo          = 'https://b.bdstatic.com/searchbox/icms/searchbox/img/jd-newlogo.jpeg'; //快递公司LOGO
        $express_service_phone = '950616';//快递公司客服电话
        $message               = $this->msg ?? 'success'; //提示文字
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }


    /**
     * Get customer_code
     */
    protected function getCustomerCode()
    {
        return $this->getConfigByKey('customer_code');
    }

    protected function _parseTrace($result)
    {
        $trace = [];
        if (!empty($result['traceDetails'])) {
            foreach ($result['traceDetails'] as $val) {
                $remark  = $val['operationRemark'] ?? '';
                $remark  = tools()::remove_empty_string($remark);
                $trace[] = [
                    'remark'           => $remark,
                    'time'             => $val['operationTime'] ?? '',
                    'date_hour_minute' => date('n月d日 H:i', strtotime($val['operationTime'])),
                    //                    'accept_address' => $val['acceptAddress'] ?? '',
                    //'status_code'      => $val['state'] ?? '',
                ];
            }
        }
        return $this->sort_trace($trace);
    }

    /**
     * 批量查询路由 可以queryRoute传入多个单号 手机号用逗号隔开
     * @param array $dataMap
     */
    public function queryRoutes($dataMap)
    {

    }

    public function auth($params, $redirect_uri)
    {
        $auth_url           = $this->get_auth_url();
        $app_key            = $this->getAppKey();
        $app_secret         = $this->getAppSecret();
        $code               = $params['code'] ?? null;
        $bid                = $params['bid'] ?? null;
        $express_channel_id = $params['express_channel_id'] ?? null;
        if (empty($bid)) {
            throw new \Exception('缺少BID参数');
        }
        if (empty($express_channel_id)) {
            throw new \Exception('缺少express_channel_id参数');
        }
        if (empty($code)) {
            $url = $auth_url . '/oauth/authorize?client_id=' . $app_key . '&redirect_uri=' . urlencode(urlencode($redirect_uri)) . '&response_type=code';
            redirect($url);
        }
        $url    = $auth_url . '/oauth/token';
        $data   = [
            'code'          => $code,
            'client_id'     => $app_key,
            'client_secret' => $app_secret
        ];
        $result = curl()->get($url, $data)->get_body();
        $result = json_decode(json_decode($result, true), true);

        //    ["accessExpire"] => string(19) "2024-01-19 14:50:48"
        //    ["accessToken"] => string(32) "8d5c6931129742d9843a1f32f11e46ae"
        //    ["clientId"] => string(32) "d5390884058946e885ae55d6737188d8"
        //    ["code"] => string(32) "354f4e082c9e44a6bfa0589bf8bf3dc7"
        //    ["refreshExpire"] => string(19) "2024-01-19 14:50:48"
        //    ["refreshToken"] => string(32) "b68ce7c4d5c647339f3c849104e1ec66"
        //    ["sellerId"] => string(12) "xieyongfa123"
        $code               = $result['code'];
        $seller_id          = $result['sellerId'];
        $client_id          = $result['clientId'];
        $refresh_token      = $result['refreshToken'];
        $refresh_expire     = $result['refreshExpire'];
        $access_token       = $result['accessToken'];
        $access_expire      = $result['accessExpire'];
        $db_express_auth_jd = new ExpressAuthJd();
        $map                = [
            ['seller_id', '=', $seller_id],
            ['client_id', '=', $client_id],
            ['bid', '=', $bid],
            ['is_debug', '=', (int)$this->is_debug()]
        ];
        $guid               = $db_express_auth_jd->where($map)->value('guid');
        if ($guid) {
            //存在则更新
            $update_data = [
                'code'           => $code,
                'access_token'   => $access_token,
                'access_expire'  => $access_expire,
                'refresh_token'  => $refresh_token,
                'refresh_expire' => $refresh_expire,
            ];
            $map[]       = ['guid', '=', $guid];
            $update      = $db_express_auth_jd::update($update_data, $map);
            wr_log('京东账号:' . $seller_id . ' 物流【授权更新】成功', 1);
        } else {
            $insert_data = [
                'guid'               => create_guid(),
                'bid'                => $bid,
                'express_channel_id' => $express_channel_id,
                'access_token'       => $access_token,
                'code'               => $code,
                'client_id'          => $client_id,
                'seller_id'          => $seller_id,
                'access_expire'      => $access_expire,
                'refresh_token'      => $refresh_token,
                'is_debug'           => (int)$this->is_debug(),
                'refresh_expire'     => $refresh_expire,
            ];
            $db_express_auth_jd->save($insert_data);
            wr_log('京东账号:' . $seller_id . ' 物流【首次授权】成功', 1);
        }
        return true;
    }
    //快递状态
    //0 已揽件 (不会返回这个状态)
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * Get express status
     * @link https://cloud.jdl.com/#/open-business-document/access-guide/267/54128
     * @param int $state
     */
    protected function _parseExpressStatus($result)
    {
        if (empty($result['traceDetails'])) {
            return 1; //暂无记录
        }
        $last_route        = reset($result['traceDetails']);
        $last_route_status = $last_route['state'] ?? '';
        if (empty($last_route_status)) {
            return 1; //暂无记录
        }
        $status_map = [

            '200034' => 0,
            '200001' => 0,

            '10033'  => 2, // 站点验货 &第三方运单回传
            '200075' => 2, // 站点收货

            '11000'  => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '200003' => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '200023' => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '200031' => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)

            '10042' => 4, // 部分妥投
            '10034' => 4, // 妥投
        ];
        return $status_map[$last_route_status] ?? 2; //其余状态返回 运输中
    }
}