<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON> di niao
 * <AUTHOR>
 *
 */
class KuaiDiYiBai extends Express implements ExpressInterface
{
    protected $create_url = 'https://api.kdniao.com/api/EOrderService';
    protected $debug_create_url = 'http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json';

    protected $print_api_url = 'https://www.kdniao.com/External/PrintOrder.aspx'; // 批量打印接口地址
    protected $ip_service_url = 'http://www.kdniao.com/External/GetIp.aspx'; //IP服务地址
    protected $qrery_url = 'https://poll.kuaidi100.com/poll/query.do'; // 查询物流信息地址

    /**
     * 电商Sign签名生成
     * @param data 内容
     * @param appkey Appkey
     * @return DataSign签名
     */
    function encrypt($data, $appkey)
    {
        return urlencode(base64_encode(md5($data . $appkey)));
    }

    /**
     * Get secret
     */
    public function getUserid()
    {
        return $this->getConfigByKey('userid');
    }

    /**
     * Get secret
     */
    public function getSecret()
    {
        return $this->getConfigByKey('secret');
    }

    /**
     * Get customer
     */
    public function getCustomer()
    {
        return $this->getConfigByKey('customer');
    }

    /**
     * Get key
     */
    public function getKey()
    {
        return $this->getConfigByKey('key');
    }

    public function request($url, $param)
    {
        //参数设置
        $key      = $this->getKey();                        // 客户授权key
        $customer = $this->getCustomer();                   // 查询公司编号

        //请求参数
        $post_data             = array();
        $post_data['customer'] = $customer;
        $post_data['param']    = json_encode($param, JSON_UNESCAPED_UNICODE);
        $sign                  = md5($post_data['param'] . $key . $customer);
        $post_data['sign']     = strtoupper($sign);
        $result                = curl()->form_params()->post($url, $post_data)->get_body();
        return $this->getResult($result);
    }

    protected function getResult($result)
    {
        if (isset($result['result']) && $result['result'] == false) {
            throw new \Exception($result['msg'] ?? '快递接口请求错误,状态码:' . $result['returnCode'] ?? -1);
        }
        return $result;
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        //OrderCode:需要打印的订单号，和调用快递鸟电子面单的订单号一致，
        //PortName：本地打印机名称，请参考使用手册设置打印机名称。支持多打印机同时打印。
        $order_id_array   = is_array($orderId) ? $orderId : [$orderId];
        $express_no_array = is_array($expressNo) ? $expressNo : [$expressNo];
        $data             = [];
        $order_id_string  = '';
        $i                = 0;
        foreach ($order_id_array as $order_id) {
            $i++;
            $data[]          = ['OrderCode' => $order_id, 'PortName' => $portName];
            $order_id_string .= '<div class="layui-form-item">
                   <label class="layui-form-label">单号:' . $i . '</label>'
                . '<div class="layui-form-mid layui-word-aux">' . $order_id . '</div>' .
                '</div>';
        }
        $request_data = json_encode($data);
        //'[{"OrderCode":"A1113","PortName":""}]';
        $request_data_encode = urlencode($request_data);
        $data_sign           = $this->encrypt(tools()::get_client_ip() . $request_data, $this->getConfigByKey('app_key'));
        //是否预览，0-不预览 1-预览
        $is_preview = '1';

        //组装表单
        $form = '<form id="form1" target="_blank" method="POST" action="' . $this->print_api_url . '">
        <input type="hidden" name="RequestData" value="' . $request_data_encode . '"/>
        <input type="hidden" name="EBusinessID" value="' . $this->getAppId() . '"/>
        <input type="hidden" name="DataSign" value="' . $data_sign . '"/>
        <input type="hidden" name="IsPreview" value="' . $is_preview . '"/>
        ' . $order_id_string . '
        </form>
        <script>//form1.submit();</script>';
        return ['type' => 'html', 'html' => $form, 'channel_name' => $this->getChannelName()];
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        //快递公司编码为空
        if (empty($expressCode)) {
            return ['status' => '0', 'message' => '快递公司编码为空'];
        }

        //订单验证
        if (empty($orderId)) {
            return ['status' => '0', 'message' => '订单编号不能为空'];
        }

        //收发件人验证
        $ret = $this->checkSenderOrReceiver($sender, 1);
        if ($ret['status'] == '0') {
            return $ret;
        }

        $ret = $this->checkSenderOrReceiver($receiver, 2);
        if ($ret['status'] == '0') {
            return $ret;
        }

        //$sender => $from
        $from = [
            'Company'      => $sender['company'] ?? '',
            'Name'         => $sender['name'],
            'Mobile'       => $sender['mobile'] ?? '',
            'Tel'          => $sender['tel'] ?? '',
            'ProvinceName' => rtrim($sender['province'], '市'),
            'CityName'     => $sender['city'],
            'ExpAreaName'  => $sender['district'],
            'Address'      => $sender['address'],
            'PostCode'     => $sender['post_code'] ?? '',
        ];

        //receiver => $to
        $to = [
            'Company'      => $receiver['company'] ?? '',
            'Name'         => $receiver['name'],
            'Mobile'       => $receiver['mobile'] ?? '',
            'Tel'          => $receiver['tel'] ?? '',
            'ProvinceName' => rtrim($receiver['province'], '市'),
            'CityName'     => $receiver['city'],
            'ExpAreaName'  => $receiver['district'],
            'Address'      => $receiver['address'],
            'PostCode'     => $receiver['post_code'] ?? '',
        ];

        //commodity
        $commodity = [];
        if (!empty($options['commodity'])) {
            if (isset($options['commodity']['name'])) {
                $commodity[] = ['GoodsName' => $options['commodity']['name']];
            } else {
                foreach ($options['commodity'] as $val) {
                    if (is_array($val) && !empty($val['name'])) {
                        $commodity[] = ['GoodsName' => $val['name']];
                    }
                }
            }
        }
        if (empty($commodity)) {
            $commodity[] = ['GoodsName' => '商品'];
        }

        //AddServices

        $requestData = [
            'ShipperCode'           => $expressCode,
            'OrderCode'             => $orderId,
            'PayType'               => $options['pay_type'] ?? 1, //邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付
            'ExpType'               => $options['exp_type'] ?? 1, //快递类型：1-标准快件，其它量看文档
            'TransType'             => $options['trans_type'] ?? 1, //运输方式 1-陆运  2-空运 不填默认为1
            'IsNotice'              => $options['is_notice'] ?? 1, //是否通知快递员上门揽件 0-通知, 1-不通知 不填则默认为1
            'StartDate'             => $options['start_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            'EndDate'               => $options['end_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            'CustomerName'          => $options['customer_name'] ?? '',
            'CustomerPwd'           => $options['customer_pwd'] ?? '',
            'MonthCode'             => $options['month_code'] ?? '',
            'SendSite'              => $options['send_site'] ?? '',
            'SendStaff'             => $options['send_staff'] ?? '',
            'IsReturnPrintTemplate' => $options['need_print_tpl'] ?? 0,
            'Remark'                => $options['remark'] ?? '', //备注
            'Sender'                => $from,
            'Receiver'              => $to,
            'Commodity'             => $commodity,
        ];
        if (!empty($options['template_size'])) {
            $requestData['TemplateSize'] = $options['template_size'];
        }
        $jsonReqData = json_encode($requestData, JSON_UNESCAPED_UNICODE);

        $orderData = [
            'EBusinessID' => $this->getAppId(),
            'RequestType' => '1007',
            'RequestData' => urlencode($jsonReqData),
            'DataType'    => '2',
            'DataSign'    => $this->makeSign($jsonReqData),
        ];

        $result = curl()->post($this->get_create_url(), $orderData)->get_body();

        if (isset($result['status']) && $result['status'] == '0') {
            return $result;
        }

        if (empty($result) || !isset($result['Success'])) {
            return ['status' => '0', 'message' => '接口返回数据有误'];
        }
        if (!$result['Success'] || !isset($result['Order'])) {
            return ['status' => '0', 'message' => $result['Reason'] ?? '接口返回结果为失败'];
        }

        //结果order
        $resultOrder = [
            'order_id'     => $orderId, //订单号
            'response'     => $result,
            'express_code' => $expressCode, //快递公司编号
            'express_no'   => $result['Order']['LogisticCode'] ?? '', //快递单号
            'dest_code'    => $result['Order']['DestinatioCode'] ?? '', //目的地区域编码
            'print_tpl'    => $result['PrintTemplate'] ?? '', //打印模板html内容
        ];

        return ['status' => '1', 'message' => 'success', 'order' => $resultOrder];
    }

    protected function get_create_url()
    {
        return $this->is_debug() ? $this->debug_create_url : $this->create_url;
    }

    protected function is_debug()
    {
        return (bool)$this->getConfigByKey('debug');
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        if (empty($expressCode) || empty($expressNo) || empty($orderId)) {
            return ['status' => '0', 'message' => '快递编码、运单号、订单编号不能为空'];
        }

        $requestData = [
            'ShipperCode'  => $expressCode,
            'OrderCode'    => $orderId,
            'ExpNo'        => $expressNo,
            'CustomerName' => $options['customer_name'] ?? '',
            'CustomerPwd'  => $options['customer_pwd'] ?? '',
        ];

        $jsonReqData = json_encode($requestData, JSON_UNESCAPED_UNICODE);

        $orderData = [
            'EBusinessID' => $this->getAppId(),
            'RequestType' => '1147',
            'RequestData' => urlencode($jsonReqData),
            'DataType'    => '2',
            'DataSign'    => $this->makeSign($jsonReqData),
        ];

        $result = curl()->post($this->get_create_url(), $orderData)->get_body();

        if (isset($result['status']) && $result['status'] == '0') {
            return $result;
        }

        if (empty($result) || !isset($result['Success'])) {
            return ['status' => '0', 'message' => '接口返回数据有误'];
        }
        if (!$result['Success']) {
            return ['status' => '0', 'message' => $result['Reason'] ?? '接口返回结果失败'];
        }

        return ['status' => '1', 'message' => '取消成功'];
    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;
        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        $param  = [
            'com'      => $expressNo,             // 快递公司编码
            'num'      => $expressCode,     // 快递单号
            'phone'    => '',                // 手机号
            'from'     => '',                 // 出发地城市
            'to'       => '',                   // 目的地城市
            'resultv2' => '1',            // 开启行政区域解析
            'show'     => '0',                // 返回格式：0：json格式（默认），1：xml，2：html，3：text
            'order'    => 'desc'             // 返回结果排序:desc降序（默认）,asc 升序
        ];
        $result = $this->request($this->qrery_url, $param);

        if (isset($result['status']) && $result['status'] == '0') {
            return $result;
        }

        if (empty($result) || !isset($result['data'])) {
            return ['status' => '0', 'message' => '接口返回数据有误'];
        }
        if (!$result['status']) {
            return ['status' => '0', 'message' => $result['message'] ?? '接口返回结果失败'];
        }

        $traces = [];
        if (!empty($result['data'])) {
            foreach ($result['data'] as $trace) {
                $traces[] = [
                    'remark'         => $trace['context'] ?? '',
                    'accept_time'    => $trace['time'] ?? '',
                    'accept_address' => $trace['areaName'] ?? '',
                ];
            }
        }
        $trace = $this->sort_trace($trace);

        $expressStatus = isset($result['state']) ? $this->getExpressStatus($result['state']) : 1; //0-无轨迹，1-揽件，2-在途，3-签收，4-问题件

        return ['status' => '1', 'message' => 'success', 'traces' => $traces, 'express_status' => $expressStatus, 'result' => $result];
    }

    public function queryRoutes($dataMap)
    {

    }

    /**
     * Make sign
     * @param string $requestData
     */
    protected function makeSign($requestData)
    {
        return urlencode(base64_encode(md5($requestData . $this->getAppKey())));
    }

    /**
     * Get express status
     * 归为：1-无轨迹，2-揽件，3-在途，4-签收，5-问题件
     * @param int $state
     */
    protected function getExpressStatus($state)
    {
        $state = intval($state);
        return $state + 1;
    }
}