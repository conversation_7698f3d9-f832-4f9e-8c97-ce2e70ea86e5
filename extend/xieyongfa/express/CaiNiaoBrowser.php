<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON>
 * <AUTHOR>
 *
 */
class CaiNiaoBrowser extends Express implements ExpressInterface
{

    //    protected $qrery_url = 'https://acs.m.taobao.com/h5/mtop.taobao.logisticstracedetailservice.queryalltrace/1.0/'; // 查询物流信息地址
    protected $qrery_url = 'https://acs.m.taobao.com/h5/mtop.cainiao.ld.detail.h5.mailno.rescode.get/1.0/';
    protected $key = '12574478';
    protected $secret = '1865499600';
    protected $jsv = '2.6.1';
    protected $api_name = 'mtop.cainiao.ld.detail.h5.mailNo.resCode.get';
    protected $m_h5_tk = '';
    protected $m_h5_tk_enc = '';
    protected $time = '';


    protected function get_cookie()
    {
        if (empty($this->m_h5_tk) || empty($this->key) || empty($this->m_h5_tk_enc)) {
            // 请求设置cookie
            $url    = 'https://acs.m.taobao.com/h5/mtop.cainiao.guoguo.nbnetflow.ads.show/1.1/';
            $data   = [
                'jsv'                        => $this->jsv,
                'appKey'                     => $this->key,
                //                't'                          => '1673839077239',
                //                'sign'                       => 'fc62ed795c33bc1b34c189ff10ff6ec3',
                'v'                          => '1.1',
                'dataType'                   => 'json',
                'AntiCreep'                  => 'true',
                'dangerouslySetAlipayParams' => '[object Object]',
                'api'                        => 'mtop.cainiao.guoguo.nbnetflow.ads.show',
                'type'                       => 'originaljson',
                'data'                       => '{"appName":"APP_MY_EXPRESS","pit":1178}',

            ];
            $cookie = curl()->get($url, $data)->get_cookies();
            $cookie = tools()::cookies_to_array($cookie);
            if (empty($cookie)) {
                throw new \Exception("cookie获取失败");
            }
            // ["cookie2"] => string(32) "10e458225eee18d99284171453a9c60a"
            // ["_m_h5_tk"] => string(46) "71b7d765f109ea48cf460a4713f78c04_1673766366684"
            // ["_m_h5_tk_enc"] => string(32) "3b40c9477ffb74f53663c010f2e48dc1"
            $_m_h5_tk_array    = explode('_', $cookie['_m_h5_tk']);
            $this->m_h5_tk     = $_m_h5_tk_array[0];
            $this->time        = $_m_h5_tk_array[1];
            $this->m_h5_tk_enc = $cookie['_m_h5_tk_enc'];
        }
        return '_m_h5_tk=' . $this->m_h5_tk . '_' . $this->time . '; _m_h5_tk_enc=' . $this->m_h5_tk_enc;
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->status  = 0;
            $this->message = '解析返回结果失败';
            return false;
        }
        if (isset($result['ret'])) {
            $this->message = join(',', $result['ret'] ?? ['错误原因未找到']);
            $this->message = str_replace('SUCCESS::', '', $this->message);
        }
        if (empty($result['data'])) {
            $this->status = 0;
            return false;
        }
        return !empty($result['data']['result']) ? reset($result['data']['result']) : $result;
    }
    //快递状态
    //0 已揽件
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * 解析返回的结果
     * @param string $result
     * @return int
     */
    protected function _parseExpressStatus($status)
    {
        if (empty($status)) {
            return 1; //暂无记录
        }
        switch ($status) {
            case 'ACCEPT': //已揽件
                $status = 0;
                break;
            case 'TRANSPORT': //运输中
                $status = 2;
                break;
            case 'AGENT_SIGN': //待签收
                $status = 3;
                break;
            case 'DELIVERING': //派送中
                $status = 3;
                break;
            case 'SIGN': //已签收
                $status = 4;
                break;
            case 'FAILED': //异常
                $status = 6;
                break;
            default:
                $status = 2;
        }
        return $status;
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return int
     */
    protected function _parseTrace($array)
    {
        $trace = [];
        foreach ($array as $key => $val) {
            $trace[] = [
                'remark'           => $val['standerdDesc'],
                'time'             => $val['time'],
                'date_hour_minute' => date('n月d日 H:i', strtotime($val['time'])),
            ];
        }
        return $this->sort_trace($trace);
    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;

        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }

        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }
        $cpCode       = $this->getExpressCodeByCode($expressCode, 'cainiao_code', '');
        $body         = [
            "mailNo"    => $expressNo,
            'secretKey' => $this->secret,
            "appName"   => "KUAKE",
            "brandCode" => $cpCode,
            "extParams" => json_encode(['querySourceId' => '68719484959'])
            //            "mobileNumberSuffix"       => $mobile,
            //            "actor"                    => "RECEIVER",
            //            "isAccoutOut"              => true,
            //            "isShowConsignDetail"      => true,
            //            "ignoreInvalidNode"        => true,
            //            "isUnique"                 => true,
            //            "isStandard"               => true,
            //            "isShowItem"               => true,
            //            "isShowTemporalityService" => true,
            //            "isShowCommonService"      => true,
            //            "isStandardActionCode"     => true,
            //            "isOrderByAction"          => true,
            //            "isShowExpressMan"         => true,
            //            "isShowProgressbar"        => true,
            //            "isShowLastOneService"     => true,
            //            "isShowServiceProvider"    => true,
            //            "isShowDeliveryProgress"   => true
        ];
        $json         = json_encode($body, JSON_UNESCAPED_UNICODE);
        $cookie       = $this->get_cookie();
        $sign         = $this->makeSign($json);
        $data         = [
            'jsv'                        => $this->jsv,
            'appKey'                     => $this->key,
            't'                          => $this->time,
            'sign'                       => $sign,
            'v'                          => '1.0',
            'dataType'                   => 'json',
            'AntiCreep'                  => true,
            'dangerouslySetAlipayParams' => '[object Object]',
            'api'                        => $this->api_name,
            'encode'                     => '1',
            'type'                       => 'originaljson',
            'c'                          => $this->m_h5_tk . '_' . $this->time . ';' . $this->m_h5_tk_enc,
        ];
        $data['data'] = json_encode($body, JSON_UNESCAPED_UNICODE);

        $user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1';
        $header     = [
            'user-agent' => $user_agent
        ];
        $result     = curl()->set_cookies($cookie)->set_header($header)->get($this->qrery_url, $data)->get_body();
        $result     = $this->_parseResult($result);
        if ($result === false) {
            return ['status' => $this->status, 'message' => $this->message];
        }
        $traces                = $this->_parseTrace($result['fullTraceDetail'] ?? []);
        $express_status        = $this->_parseExpressStatus($result['packageStatus']['newStatusCode'] ?? ''); // 物流状态
        $express_name          = $result['cp']['tpName'] ?? '未知快递';//快递公司名称
        $express_logo          = $result['cp']['cpLogUrl'] ?? ''; //快递公司LOGO
        $express_service_phone = $result['cp']['tpContact'] ?? '';//快递公司客服电话
        $message               = $this->message;
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }


    /**
     * Cancel order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    public function queryRoutes($dataMap)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Make sign
     * @param string $requestData
     */
    protected function makeSign($requestData)
    {
        $string = $this->m_h5_tk . '&' . $this->time . '&' . $this->key . '&' . $requestData;
        return strtolower(md5($string));
    }

}