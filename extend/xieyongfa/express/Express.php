<?php

namespace xieyongfa\express;

use app\model\ExpressConfig;

/**
 * Express
 * <AUTHOR>
 *
 */
class Express
{
    /**
     * driver name
     * @var string
     */
    protected $driverName;


    /**
     * config
     * @var array
     */
    protected $config;

    /**
     * status
     * @var integer
     */
    protected $status = 1;  //0失败 1成功

    /**
     * message
     * @var string
     */
    protected $message = 'success';


    /**
     * Construct
     * @param array $config
     */
    public function __construct($config = [])
    {
        $this->setConfig($config);
    }

    public function __call($method, $args)
    {
        throw new \Exception('不存在' . $method . '方法');
    }

    /**
     * Get config
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Set config
     * @param array $config
     */
    public function setConfig($config)
    {
        $this->config = $config;

        return $this;
    }

    /**
     * Get config by key
     * @param string $key
     */
    public function getConfigByKey($key)
    {
        return $this->config[$key] ?? null;
    }

    public function getExpressConfig()
    {
        $config_guid = $this->getConfigGuid();
        if (empty($config_guid)) {
            return [];
        }
        $db_express_config  = new ExpressConfig();
        $map_express_config = [
            ['guid', '=', $config_guid],
        ];
        return $db_express_config->where($map_express_config)->order(['create_time' => 'ASC'])->findOrFail();
    }

    /**
     * Set config by key
     * @param string $key
     * @param string $val
     */
    public function setConfigByKey($key, $val)
    {
        if (isset($this->config[$key])) {
            $this->config[$key] = $val;
        }
    }

    /**
     * Get channel_name
     */
    public function getChannelName()
    {
        return $this->getConfigByKey('channel_name');
    }

    /**
     * Get channel_name
     */
    public function getChannelId()
    {
        return $this->getConfigByKey('channel_id');
    }

    public function getConfigGuid()
    {
        return $this->getConfigByKey('config_guid');
    }

    /**
     * Get app id
     */
    public function getAppId()
    {
        return $this->getConfigByKey('app_id');
    }

    /**
     * Get app id
     */
    public function getAppSecret()
    {
        return $this->getConfigByKey('app_secret');
    }

    /**
     * Get secret
     */
    public function getSecret()
    {
        return $this->getConfigByKey('secret');
    }

    /**
     * Get app key or secrect
     */
    public function getAppKey()
    {
        return $this->getConfigByKey('app_key');
    }


    protected function sort_trace($trace)
    {
        if (!empty($trace)) {
            $trace = tools()::multi_array_sort($trace, 'time', SORT_DESC); //最近更新的最前面
        }
        return $trace;
    }

    public function getExpressStatusNameByStatus($status)
    {
        $status = intval($status);
        $list   = [
            0  => '已揽件',
            1  => '暂无记录',
            2  => '在途中',
            3  => '派送中',
            4  => '已签收',
            5  => '用户拒签',
            6  => '疑难件',
            7  => '无效单',
            8  => '超时单',
            9  => '签收失败',
            10 => '退回',
        ];
        return $list[$status] ?? '状态未知';
    }

    public function getExpressCodeByCode($code, $field_name, $default_code)
    {
        if (empty($code)) {
            return $default_code;
        }
        $db_express   = new \app\model\Express();
        $express_code = $db_express->where([['code', '=', $code]])->value($field_name);
        return $express_code ?: $default_code;
    }

    public function preCheckExpressNo($express_no)
    {
        if (mb_strlen($express_no) < 10) {
            $this->message = '单号不能小于10个字符';
            $this->status  = 0;
            return false;
        }
        if (preg_match("/[\x80-\xff]/", $express_no)) {
            $this->message = '单号包含中文';
            $this->status  = 0;
            return false;
        }
        if (strpos($express_no, '/') !== false) {
            $this->message = '单号中不能包含/字符';
            $this->status  = 0;
            return false;
        }
        if (!preg_match("/^[a-zA-Z\d]+$/", $express_no)) {
            $this->message = '单号只能是字母+数字组成';
            $this->status  = 0;
            return false;
        }
        return true;
    }

    /**
     * Check sender or receiver
     * @param array $data
     * @param int $type 1-sender, 2-receiver
     * @return array
     */
    public function checkSenderOrReceiver($data, $type)
    {
        $columns = [
            'name'     => '姓名不能为空',
            'province' => '省份不能为空',
            'city'     => '城市不能为空',
            'district' => '区县不能为空',
            'address'  => '详细地址不能为空',
        ];

        $status = '1';
        $msg    = 'ok';
        foreach ($columns as $key => $value) {
            if (empty($data[$key])) {
                $status = '0';
                $msg    = $value;
                break;
            }
        }

        if ($status == '1') {
            if (empty($data['mobile']) && empty($data['tel'])) {
                $status = '0';
                $msg    = '手机号或电话不能都为空';
            }
        }

        $msg = ($type == 1 ? '发件方' : '收件方') . $msg;

        return ['status' => $status, 'message' => $msg];
    }
}