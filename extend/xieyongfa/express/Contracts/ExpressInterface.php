<?php

namespace xieyongfa\express\Contracts;

/**
 * Express interface
 * <AUTHOR>
 *
 */
interface ExpressInterface
{

    //快递状态
    //0 已揽件 (部分快递公司才会返回)
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * Query Route
     * @param array $query_data 查询数据
     * @return array $result
     */
    public function queryRoute(array $query_data);

    /**
     * Query Route
     * @param array $query_data 查询数据
     * @return array $result
     */
    public function queryFee(array $query_data);

    /**
     * Query Routes
     * @param array $dataMap = ['order_id' => $expressNo]
     * @return array $result
     */
    public function queryRoutes($dataMap);

    /**
     * Create order
     * @param string $expressCode
     * @param string $orderId
     * @param array $sender
     * @param array $receiver
     * @param array $options
     * @return array $result
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = []);

    /**
     * Cancel order
     * @param string $expressCode
     * @param string $expressNo
     * @param string $orderId
     * @return array $result
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = []);

    /**
     * printOrder order
     * @param string|array $orderId
     * @param string|array $expressNo
     * @param string $portName
     * @return string|array $result
     */
    public function printOrder($orderId, $expressNo, $portName);


}