<?php

namespace xieyongfa\express;


/**
 * Express manager
 * <AUTHOR>
 *
 */
class ExpressManager
{
    protected $config;
    protected $config_guid;
    protected $channel_id;
    protected $drivers;

    /**
     * construct
     * @param array $config
     */
    public function __construct($config = [], $channel_id = 0)
    {
        $this->config     = $config;
        $this->channel_id = $channel_id;
    }

    /**
     * Set config
     * @param array $config
     */
    public function setConfig($name = 'kdniao', $config = [])
    {
        if (!empty($name)) {
            $this->config[$name] = $config;
        } else {
            $this->config = $config;
        }

        return $this;
    }

    /**
     * get express service by name
     * @param string $name
     * @param string $config_guid
     * @return \xieyongfa\express\Contracts\ExpressInterface
     */
    public function express($name = 'kdniao', $config_guid = null)
    {
        $name              = $name ?: $this->getDefaultDriver();
        $this->config_guid = $config_guid;
        return $this->drivers[$name] ?? $this->drivers[$name] = $this->resolve($name);
    }

    /**
     * Get default driver name
     * @return string
     */
    public function getDefaultDriver()
    {
        $name = $this->getConfigByKey('default');
        return !empty($name) ? $name : 'kdniao';
    }

    /**
     * Get config by key
     * @param string $key
     */
    public function getConfigByKey($key)
    {
        return $this->config[$key] ?? '';
    }

    /**
     * Resolve express service
     * @param string $name
     * @return \bailuWorker\Services\Express\Contracts\ExpressInterface;
     */
    protected function resolve($name)
    {
        $config                 = $this->config[$name] ?? $this->config;
        $config['channel_name'] = $name; // 自动传入channel_name 参数 便于内部获取
        $config['channel_id']   = $this->channel_id; // 自动传入channel_id 参数 便于内部获取
        $config['config_guid']  = $this->config_guid; // 自动传入 config_guid 参数 便于内部获取
        $class                  = __NAMESPACE__ . "\\" . tools()::parse_name($name, 1);
        switch ($name) {
            case 'kdniao':
                return new KuaiDiNiao($config);
            case 'kdwang':
                return new KuaiDiWang($config);
            case 'kdyibai':
                return new KuaiDiYiBai($config);
            case 'shunfeng':
                return new ShunFeng($config);
            case 'cainiao':
                return new CaiNiao($config);
            case 'cainiaobrowser':
                return new CaiNiaoBrowser($config);
            case 'wangwei':
                return new WangWei($config);
            case 'jd':
                return new Jd($config);
            case 'sto':
                return new Sto($config);
            default :
                return new $class($config);
        }
    }
}