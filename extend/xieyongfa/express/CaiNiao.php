<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON> di <PERSON>
 * <AUTHOR>
 *
 */
class CaiNiao extends Express implements ExpressInterface
{

    protected $qrery_url = 'https://acs.m.taobao.com/h5/mtop.taobao.logisticstracedetailservice.queryalltrace/1.0/'; // 查询物流信息地址

    protected $key = '12574478';
    protected $cache_key_prefix = 'cainiao_express_query_cookies:';

    protected $jsv = '2.6.1';
    protected $m_h5_tk = '';
    protected $m_h5_tk_enc = '';
    protected $time = '';

    protected $currentMethod = [];
    protected $isTry = false;
    protected $needRetry = false;

    protected function clear_cookie()
    {
        $cache_key = $this->cache_key_prefix . $this->key;
        return cache($cache_key, null);
    }

    protected function get_cookie()
    {
        $cache_key = $this->cache_key_prefix . $this->key;
        $cache     = cache($cache_key);
        if (empty($cache)) {
            // 请求设置cookie
            $data   = [
                'jsv'                        => $this->jsv,
                'appKey'                     => $this->key,
                //                't'                          => $this->time,
                //                'sign'                       => $sign,
                'v'                          => '1.0',
                'dataType'                   => 'json',
                'AntiCreep'                  => true,
                'dangerouslySetAlipayParams' => '[object Object]',
                'api'                        => 'mtop.taobao.logisticstracedetailservice.queryalltrace',
                'encode'                     => '1',
                'type'                       => 'originaljson',
                //                'c'                          => $this->m_h5_tk . '_' . $this->time . ';' . $this->m_h5_tk_enc,
            ];
            $cookie = curl()->get($this->qrery_url, $data)->get_cookies();
            $cookie = tools()::cookies_to_array($cookie);
            if (empty($cookie) || empty($cookie['_m_h5_tk']) || empty($cookie['_m_h5_tk_enc'])) {
                $this->message = '物流查询服务维护中,您可复制单号至其他平台查询或者稍后再试!';
                return false;
            }
            $_m_h5_tk_array = explode('_', $cookie['_m_h5_tk']);
            $cache          = [
                'time'         => $_m_h5_tk_array[1],
                '_m_h5_tk'     => $_m_h5_tk_array[0],
                '_m_h5_tk_enc' => $cookie['_m_h5_tk_enc'],
            ];
            cache($cache_key, $cache, 3600);
        }
        $this->time        = $cache['time'];
        $this->m_h5_tk     = $cache['_m_h5_tk'];
        $this->m_h5_tk_enc = $cache['_m_h5_tk_enc'];
        return '_m_h5_tk=' . $this->m_h5_tk . '_' . $this->time . '; _m_h5_tk_enc=' . $this->m_h5_tk_enc;
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->status  = 0;
            $this->message = '解析返回结果失败';
            return false;
        }
        if (isset($result['ret'])) {
            $this->message = join(',', $result['ret'] ?? ['错误原因未找到']);
            $this->message = str_replace('SUCCESS::', '', $this->message);
            if (strpos($this->message, 'FAIL_SYS') !== false && !$this->isTry) {
                //FAIL_SYS_TOKEN_ILLEGAL FAIL_SYS_TOKEN_EMPTY FAIL_SYS_USER_VALIDATE
                //如果返回令牌为空说明这个token失效了,需要清空cookie后重新发起请求
                $this->needRetry = true;
            }
        }
        if (empty($result['data'])) {
            $this->status = 0;
            return false;
        }
        return !empty($result['data']['result']) ? reset($result['data']['result']) : $result;
    }
    //快递状态
    //0 已揽件
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * 解析返回的结果
     * @param string $result
     * @return int
     */
    protected function _parseExpressStatus($status)
    {
        if (empty($status)) {
            return 1; //暂无记录
        }
        switch ($status) {
            case 'ACCEPT': //已揽件
                $status = 0;
                break;
            case 'TRANSPORT': //运输中
                $status = 2;
                break;
            case 'AGENT_SIGN': //待签收
                $status = 3;
                break;
            case 'DELIVERING': //派送中
                $status = 3;
                break;
            case 'SIGN': //已签收
                $status = 4;
                break;
            case 'FAILED': //异常
                $status = 6;
                break;
            default:
                $status = 2;
        }
        return $status;
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return int
     */
    protected function _parseTrace($array)
    {
        $trace = [];
        foreach ($array as $key => $val) {
            $trace[] = [
                'remark'           => $val['standerdDesc'],
                'time'             => $val['time'],
                'date_hour_minute' => date('n月d日 H:i', strtotime($val['time'])),
            ];
        }
        return $this->sort_trace($trace);
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi($method, $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    public function retry()
    {
        $this->clear_cookie();
        $this->isTry     = true;  //已经重试过
        $this->needRetry = false; //默认不需要再重试
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;

        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }

        $this->registerApi(__FUNCTION__, func_get_args());
        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $cpCode = $this->getExpressCodeByCode($expressCode, 'cainiao_code', '');
        $body   = [
            "mailNo"                   => $expressNo,
            "appName"                  => "GUOGUO",
            "cpCode"                   => $cpCode,
            "mobileNumberSuffix"       => $mobile,
            "actor"                    => "RECEIVER",
            "isAccoutOut"              => true,
            "isShowConsignDetail"      => true,
            "ignoreInvalidNode"        => true,
            "isUnique"                 => true,
            "isStandard"               => true,
            "isShowItem"               => true,
            "isShowTemporalityService" => true,
            "isShowCommonService"      => true,
            "isStandardActionCode"     => true,
            "isOrderByAction"          => true,
            "isShowExpressMan"         => true,
            "isShowProgressbar"        => true,
            "isShowLastOneService"     => true,
            "isShowServiceProvider"    => true,
            "isShowDeliveryProgress"   => true
        ];
        $json   = json_encode($body, JSON_UNESCAPED_UNICODE);
        $cookie = $this->get_cookie();
        if ($cookie === false) {
            return ['status' => -1, 'message' => $this->message];
        }
        $sign         = $this->makeSign($json);
        $data         = [
            'jsv'                        => $this->jsv,
            'appKey'                     => $this->key,
            't'                          => $this->time,
            'sign'                       => $sign,
            'v'                          => '1.0',
            'dataType'                   => 'json',
            'AntiCreep'                  => true,
            'dangerouslySetAlipayParams' => '[object Object]',
            'api'                        => 'mtop.taobao.logisticstracedetailservice.queryalltrace',
            'encode'                     => '1',
            'type'                       => 'originaljson',
            'c'                          => $this->m_h5_tk . '_' . $this->time . ';' . $this->m_h5_tk_enc,
        ];
        $data['data'] = json_encode($body, JSON_UNESCAPED_UNICODE);
        $result       = curl()->set_cookies($cookie)->get($this->qrery_url, $data)->get_body();
        $result       = $this->_parseResult($result);
        if ($this->needRetry) {
            return $this->retry();
        }
        if ($result === false) {
            return ['status' => $this->status, 'message' => $this->message];
        }
        $traces                = $this->_parseTrace($result['fullTraceDetail'] ?? []);
        $express_status        = $this->_parseExpressStatus($result['packageStatus']['newStatusCode'] ?? ''); // 物流状态
        $express_name          = $result['cp']['tpName'] ?? '未知快递';//快递公司名称
        $express_logo          = $result['cp']['cpLogUrl'] ?? ''; //快递公司LOGO
        $express_service_phone = $result['cp']['tpContact'] ?? '';//快递公司客服电话
        $message               = $this->message;
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }


    /**
     * Cancel order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    public function queryRoutes($dataMap)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Make sign
     * @param string $requestData
     */
    protected function makeSign($requestData)
    {
        $string = $this->m_h5_tk . '&' . $this->time . '&' . $this->key . '&' . $requestData;
        return strtolower(md5($string));
    }

}