<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON>
 * <AUTHOR>
 *
 */
class ShunF<PERSON> extends Express implements ExpressInterface
{
    protected $url = 'https://sfapi.sf-express.com/std/service';
    protected $debug_url = 'http://sfapi-sbox.sf-express.com/std/service';
    protected $service_code = '';
    protected $auth_url = 'https://sfapi.sf-express.com/oauth2/accessToken';
    protected $debug_auth_url = 'https://sfapi-sbox.sf-express.com/oauth2/accessToken';

    protected $print_api_url = 'https://www.kdniao.com/External/PrintOrder.aspx'; // 批量打印接口地址
    protected $ip_service_url = 'http://www.kdniao.com/External/GetIp.aspx'; //IP服务地址
    protected $qrery_url = 'https://poll.kuaidi100.com/poll/query.do'; // 查询物流信息地址

    //获取UUID
    public function create_uuid()
    {
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
            . substr($chars, 8, 4) . '-'
            . substr($chars, 12, 4) . '-'
            . substr($chars, 16, 4) . '-'
            . substr($chars, 20, 12);
        return $uuid;
    }


    /**
     * Get secret
     */
    public function getPartnerrId()
    {
        return $this->getConfigByKey('partner_id');
    }

    /**
     * Get secret
     */
    public function getCheckWord()
    {
        return $this->getConfigByKey('check_word');
    }

    public function getAccessToken()
    {
        $partner_id = $this->getPartnerrId();
        $env        = $this->is_debug() ? 'sbox' : 'pro';
        $cache_key  = 'shunfeng_access_token:' . $partner_id . ':' . $env;
        if ($cache = cache($cache_key)) {
            return $cache;
        }
        $check_word             = $this->getCheckWord();
        $post_data              = array();
        $post_data['partnerID'] = $partner_id;
        $post_data['secret']    = $check_word;
        $post_data['grantType'] = 'password';
        $url                    = $this->get_auth_url();
        $result                 = curl()->form_params()->post($url, $post_data)->get_body();
        $result                 = $this->getResult($result);
        if ($result === false) {
            throw new \Exception($this->message);
        }
        $access_token = $result['accessToken'];
        $expires_in   = $result['expiresIn'];
        cache($cache_key, $access_token, $expires_in);
        return $access_token;
    }

    public function request($param)
    {
        //参数设置
        $partner_id = $this->getPartnerrId();
        //        $check_word   = $this->getCheckWord();
        $request_guid = $this->create_uuid();
        $timestamp    = time();
        $json_param   = json_encode($param, JSON_UNESCAPED_UNICODE);
        $msg_digest   = $this->makeSign($json_param, $timestamp);
        //请求参数
        $post_data                = array();
        $post_data['partnerID']   = $partner_id;
        $post_data['requestID']   = $request_guid;
        $post_data['serviceCode'] = $this->service_code;
        $post_data['timestamp']   = $timestamp;
        $post_data['msgDigest']   = $msg_digest;
        $post_data['msgData']     = $json_param;
        $url                      = $this->get_url();
        $result                   = curl()->form_params()->post($url, $post_data)->get_body();
        return $this->getResult($result);
    }

    protected function getResult($result)
    {
        if (isset($result['apiResultCode']) && $result['apiResultCode'] != 'A1000') {
            $apiErrorMsg   = $result['apiErrorMsg'] ?? '未知错误';
            $this->message = ($result['apiResultCode'] ?? -1) . $apiErrorMsg;
            return false;
            throw new \Exception($this->message);
        }

        $result_data = isset($result['apiResultData']) ? json_decode($result['apiResultData'], true) : $result;
        if ((isset($result_data['success']) && $result_data['success'] !== true) || (isset($result_data['errorCode']) && $result_data['errorCode'] !== 'S0000')) {
            $result_msg    = $result_data['errorMsg'] ?? '未知业务错误';
            $this->message = ($result_data['errorCode'] ?? -1) . $result_msg;
            return false;
            throw new \Exception($this->message);
        }
        return $result_data['msgData'] ?? $result_data;
    }

    /**
     * 打印订单
     * @param array|string $expressNo 单号
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        $express_no_array = is_array($expressNo) ? $expressNo : [$expressNo];
        $documents        = [];
        foreach ($express_no_array as $express_no) {
            $documents[] = [
                'masterWaybillNo' => $express_no
            ];
        }
        //                requestID: "xxx",
        //                accessToken: "xxx",
        //                templateCode: "xxx",
        //                templateVersion: "",
        //                documents: [
        //                    {
        //                        masterWaybillNo: "xxx"
        //                    }
        //                ],
        //                extJson: {},
        //                customTemplateCode: "xxx"


        $express_config = $this->getExpressConfig();
        $template_size  = !empty($express_config['template_size']) ? $express_config['template_size'] : '150';
        $print_type     = !empty($express_config['print_type']) ? $express_config['print_type'] : 'PREVIEW';  //PRINT
        $print_data     = [
            'requestID'    => $this->create_uuid(),
            'accessToken'  => $this->getAccessToken(),
            'templateCode' => 'fm_' . $template_size . '_standard_' . $this->getPartnerrId(),
            'documents'    => $documents,
        ];
        $config_data    = [
            'env'       => $this->is_debug() ? 'sbox' : 'pro',
            //            'lodopFn'   => $this->is_debug() ? 'PREVIEW' : 'PRINT',
            'lodopFn'   => $print_type,
            'partnerID' => $this->getPartnerrId(),
        ];
        return [
            'channel_name' => $this->getChannelName(),
            'type'         => 'js',
            'config_data'  => $config_data,
            'print_data'   => $print_data
        ];
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function getPrintFile($orderId, $expressNo, $portName)
    {
        $order_id_array     = is_array($orderId) ? $orderId : [$orderId];
        $express_no_array   = is_array($expressNo) ? $expressNo : [$expressNo];
        $this->service_code = 'COM_RECE_CLOUD_PRINT_WAYBILLS';
        $documents          = [];
        foreach ($express_no_array as $express_no) {
            $documents[] = [
                'masterWaybillNo' => $express_no
            ];
        }
        $requestData = [
            'version'      => '2.0',
            'fileType'     => 'pdf',
            'sync'         => true,
            'templateCode' => 'fm_76130_standard_' . $this->getPartnerrId(),
            //            'customTemplateCode'=>'',
            'documents'    => $documents
        ];
        $result      = $this->request($requestData);
        if ($result === false) {
            throw new \Exception($this->message);
        }
        $files         = $result['obj']['files'];
        $token         = $files[0]['token'];
        $url           = $files[0]['url'];
        $file_path     = '/temp/pdf/';
        $file_name     = $express_no_array[0] . '.pdf';
        $absolute_path = tools()::get_absolute_path($file_path);
        $download      = curl()->set_header(['X-Auth-token' => $token])->get($url)->download($absolute_path, $file_name);
        return ['channel_name' => $this->getChannelName(), 'type' => 'pdf', 'pdf_url' => tools()::path_to_web($file_path . $file_name)];
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        $this->service_code = 'EXP_RECE_CREATE_ORDER';
        //快递公司编码为空
        if (empty($expressCode)) {
            return ['status' => '0', 'message' => '快递公司编码为空'];
        }

        //订单验证
        if (empty($orderId)) {
            return ['status' => '0', 'message' => '订单编号不能为空'];
        }

        //收发件人验证
        $ret = $this->checkSenderOrReceiver($sender, 1);
        if ($ret['status'] == '0') {
            return $ret;
        }

        $ret = $this->checkSenderOrReceiver($receiver, 2);
        if ($ret['status'] == '0') {
            return $ret;
        }

        //$sender => $from
        $from = [
            'country'     => 'CN',
            'contactType' => 1, //地址类型： 1，寄件方信息 2，到件方信息
            'company'     => $sender['company'] ?? '',
            'contact'     => $sender['name'],
            'mobile'      => $sender['mobile'] ?? '',
            'tel'         => $sender['tel'] ?? '',
            'province'    => rtrim($sender['province'], '市'),
            'city'        => $sender['city'],
            'county'      => $sender['district'],
            'address'     => $sender['address'],
            'postCode'    => $sender['post_code'] ?? '',
        ];

        //receiver => $to
        $to = [
            'country'     => 'CN',
            'contactType' => 2, //地址类型： 1，寄件方信息 2，到件方信息
            'company'     => $sender['company'] ?? '',
            'contact'     => $receiver['name'],
            'mobile'      => $receiver['mobile'] ?? '',
            'tel'         => $sender['tel'] ?? '',
            'province'    => rtrim($receiver['province'], '市'),
            'city'        => $receiver['city'],
            'county'      => $receiver['district'],
            'address'     => $receiver['address'],
            'postCode'    => $receiver['post_code'] ?? '',
        ];

        //commodity
        $commodity = [];
        if (!empty($options['commodity'])) {
            if (isset($options['commodity']['name'])) {
                $commodity[] = ['name' => $options['commodity']['name']];
            } else {
                foreach ($options['commodity'] as $val) {
                    if (is_array($val) && !empty($val['name'])) {
                        $commodity[] = ['name' => $val['name']];
                    }
                }
            }
        }
        if (empty($commodity)) {
            $commodity[] = ['name' => '商品'];
        }

        //AddServices

        $orderData = [
            'language'           => 'zh_CN',
            'orderId'            => $orderId,
            //            'codingMapping'    => '',
            //            'codingMappingOut' => '',
            'cargoDetails'       => $commodity, //托寄物信息
            'contactInfoList'    => [
                $from, $to
            ],
            'monthlyCard'        => $options['month_code'] ?? '',
            'expressTypeId'      => $options['exp_type'] ?? 1, //快件产品类别， 支持附录 《快件产品类别表》 的产品编码值，仅可使用与顺丰销售约定的快件产品
            'isReturnRoutelabel' => 1, //是否返回路由标签： 默认1， 1：返回路由标签， 0：不返回；除部分特殊用户外，其余用户都默认返回
            //            'cargoDesc'          => '货物'

            //            'PayType'               => $options['pay_type'] ?? 1, //邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付
            //            'ExpType'               => $options['exp_type'] ?? 1, //快递类型：1-标准快件，其它量看文档
            //            'TransType'             => $options['trans_type'] ?? 1, //运输方式 1-陆运  2-空运 不填默认为1
            //            'IsNotice'              => $options['is_notice'] ?? 1, //是否通知快递员上门揽件 0-通知, 1-不通知 不填则默认为1
            //            'StartDate'             => $options['start_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            //            'EndDate'               => $options['end_date'] ?? '', //上门取件时间点, "yyyy-MM-dd HH:mm:ss"
            //            'CustomerName'          => $options['customer_name'] ?? '',
            //            'CustomerPwd'           => $options['customer_pwd'] ?? '',
            //            'SendSite'              => $options['send_site'] ?? '',
            //            'SendStaff'             => $options['send_staff'] ?? '',
            //            'IsReturnPrintTemplate' => $options['need_print_tpl'] ?? 0,
            //            'Remark'                => $options['remark'] ?? '', //备注
            //            'Sender'                => $from,
            //            'Receiver'              => $to,
        ];
//        if (!empty($options['template_size'])) {
//            $requestData['TemplateSize'] = $options['template_size'];
//        }
        $result = $this->request($orderData);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }

        //结果order
        $resultOrder = [
            'order_id'     => $orderId, //订单号
            'response'     => $result,
            'express_code' => $expressCode, //快递公司编号
            'express_no'   => $result['waybillNoInfoList'][0]['waybillNo'] ?? '', //快递单号
            'dest_code'    => $result['originCode'] ?? '', //目的地区域编码
            'print_tpl'    => '', //打印模板html内容
        ];

        return ['status' => '1', 'message' => '查询成功', 'order' => $resultOrder];
    }

    protected function get_url()
    {
        return $this->is_debug() ? $this->debug_url : $this->url;
    }

    protected function get_auth_url()
    {
        return $this->is_debug() ? $this->debug_auth_url : $this->auth_url;
    }

    protected function is_debug()
    {
        return (bool)$this->getConfigByKey('debug');
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @link https://open.sf-express.com/Api/ApiDetails?level3=252&category=1&apiClassify=1
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        //        if (empty($expressCode) || empty($expressNo) || empty($orderId)) {
        //            return ['status' => '0', 'message' => '快递编码、运单号、订单编号不能为空'];
        //        }
        $this->service_code = 'EXP_RECE_UPDATE_ORDER';

        $requestData = [
            'orderId'  => $orderId,
            'dealType' => 2, //客户订单操作标识: 1:确认 (丰桥下订单接口默认自动确认，不需客户重复确认，该操作用在其它非自动确认的场景) 2:取消
        ];


        $result = $this->request($requestData);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        //        if (isset($result['status']) && $result['status'] == '0') {
        //            return $result;
        //        }

        //        if (empty($result) || !isset($result['Success'])) {
        //            return ['status' => '0', 'message' => '接口返回数据有误'];
        //        }
        //        if (!$result['Success']) {
        //            return ['status' => '0', 'message' => $result['Reason'] ?? '接口返回结果失败'];
        //        }

        return ['status' => '1', 'message' => '取消成功'];
    }

    public function preCheckExpressNo($express_no)
    {
//        if (!tools()::start_with($express_no, 'SF')) {
//            $this->message = '顺丰单号必须以SF开头';
//            $this->status  = 0;
//            return false;
//        }
        $express_no = strtoupper($express_no);
        if (!preg_match("/(^SF\d{13}$)|(^\d{12}$)/", $express_no)) {
            $this->message = '顺丰单号必须以【SF开头+13位数字】或【12位数字】';
            $this->status  = 0;
            return false;
        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        $this->service_code = 'EXP_RECE_QUERY_SFWAYBILL';
        $this->url          = 'https://bspgw.sf-express.com/std/service';
        $this->debug_url    = 'https://sfapi-sbox.sf-express.com/std/service';
        $param              = [
            'trackingType'    => 2,         //1:表示按订单查询 2:表示按运单查询
            'trackingNum'     => $query_data['express_no'],       // 订单号或运单号
            'phone'           => '',  // 配置校验手机号时必传，支持最高6个收寄方电话后4位下发， 每个电话号码使用英文逗号分隔
            'bizTemplateCode' => '',      // 业务配置代码，针对客户业务需求配置的一套接口处理逻辑，一个接入编码可对应多个业务配置代码
        ];
        $result             = $this->request($param);
        if ($result === false) {
            return ['status' => -1, 'message' => $this->message];
        }
        return ['status' => 0, 'message' => '查询成功', 'data' => $result];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo          = $query_data['express_no'] ?? '';
        $expressCode        = $query_data['express_code'] ?? '';
        $mobile             = $query_data['mobile'] ?? '';
        $mobile             = substr($mobile, -4);// 只需要后四位;
        $orderId            = $query_data['order_guid'] ?? '';
        $bid                = $query_data['bid'] ?? '';
        $this->service_code = 'EXP_RECE_SEARCH_ROUTES';
        $this->url          = 'https://bspgw.sf-express.com/std/service';
        $this->debug_url    = 'https://sfapi-sbox.sf-express.com/std/service';
        $expressCode        = 'SF'; //顺丰通道固定使用SF
        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $expressNo = strtoupper($expressNo);
        $result    = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }
        $param  = [
            'checkPhoneNo'   => $mobile,         // 电话号码验证
            'methodType'     => '1',       // 路由查询类别:   1:标准路由查询    2:定制路由查询
            'trackingNumber' => $expressNo,  // 查询号:  trackingType=1,则此值为顺丰运单号            如果trackingType = 2, 则此值为客户订单号
            'trackingType'   => '1',      // 查询号类别:    1:根据顺丰运单号查询,trackingNumber将被当作顺丰运单号处理            2:根据客户订单号查询,trackingNumber将被当作客户订单号处理
            'language'       => '0'         // //返回描述语语言 0：中文 1：英文 2：繁体
        ];
        $result = $this->request($param);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        $traces                = $this->_parseTrace($result);
        $express_status        = $this->_parseExpressStatus($result); // 物流状态
        $express_name          = '顺丰速运';//快递公司名称
        $express_logo          = 'https://img.alicdn.com/imgextra/i4/6000000002393/O1CN011TY2QUOdnU7UPBc_!!6000000002393-2-urc.png'; //快递公司LOGO
        $express_service_phone = '95338';//快递公司客服电话
        $message               = $this->message ?? 'success'; //提示文字
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    protected function _parseTrace($result)
    {
        $trace = [];
        if (!empty($result['routeResps'][0]['routes'])) {
            foreach (array_reverse($result['routeResps'][0]['routes']) as $val) {
                $trace[] = [
                    'remark'           => $val['remark'] ?? '',
                    'time'             => $val['acceptTime'] ?? '',
                    'date_hour_minute' => date('n月d日 H:i', strtotime($val['acceptTime'])),
                    //                    'accept_address' => $val['acceptAddress'] ?? '',
                    //'status_code'      => $val['opCode'] ?? '',
                ];
            }
        }
        return $this->sort_trace($trace);
    }

    /**
     * 批量查询路由 可以queryRoute传入多个单号 手机号用逗号隔开
     * @param array $dataMap
     */
    public function queryRoutes($dataMap)
    {

    }

    /**
     * Make sign
     * @param string $json_param
     * @param int $timestamp
     */
    protected function makeSign($json_param, $timestamp)
    {
        //通过MD5和BASE64生成数字签名
        $check_word    = $this->getCheckWord();
        $urlencode_str = urlencode($json_param . $timestamp . $check_word);
        $msg_digest    = base64_encode(md5(($urlencode_str), true));
        return str_replace('+', '', $msg_digest); //不处理则间歇性签名不通过
    }
    //快递状态
    //0 已揽件 (不会返回这个状态)
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * Get express status
     *
     * @link http://open.sf-express.com/developSupport/734349?activeIndex=589678
     * 归为：1-无轨迹，2-揽件，3-在途，4-签收，5-问题件
     * @param int $state
     */
    protected function _parseExpressStatus($result)
    {
        if (empty($result['routeResps'][0]['routes'])) {
            return 1;
        }
        $last_route        = end($result['routeResps'][0]['routes']);
        $last_route_status = $last_route['opCode'] ?? '';
        if (empty($last_route_status)) {
            return 1; //暂无记录
        }
        $status_map = [
            '50'   => 0, //顺丰已收件
            '51'   => 0, //顺丰已收件
            '54'   => 0, //顺丰已收件
            '204'  => 3, //派件责任交接
            '44'   => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '631'  => 10, //新单退回
            '648'  => 10, //快件已退回/转寄,新单号为: XXX
            '80'   => 4, //已签收,感谢使用顺丰,期待再次为您服务
            '8000' => 4, //在官网"运单资料&签收图",可查看签收人信
        ];
        return $status_map[$last_route_status] ?? 2; //其余状态返回 运输中
    }
    //操作码(opCode)	操作名称(opName)
    //50	顺丰已收件
    //51	一票多件的子件
    //54	上门收件
    //30	快件在【XXX营业点】已装车,准备发往 【XXX集散中心】
    //31	快件到达 【XXX集散中心】
    //302	车辆发车
    //304	离开经停点
    //3036	快件在XXX ,准备送往下一站
    //33	派件异常原因
    //34	滞留件出仓
    //36	封车操作
    //10	办事处发车/中转发车/海关发车/机场发货
    //105	航空起飞
    //106	航空落地
    //11	办事处到车/中转到车/海关到车/机场提货
    //122	加时区域派件出仓
    //123	快件正送往顺丰店/站
    //125	快递员派件至丰巢
    //126	快递员取消派件将快件取出丰巢
    //127	寄件客户将快件放至丰巢
    //128	客户从丰巢取件成功
    //129	快递员从丰巢收件成功
    //130	快件到达顺丰店/站
    //131	快递员从丰巢收件失败
    //135	信息记录
    //136	落地配装车
    //137	落地配卸车
    //138	用户自助寄件
    //14	货件已放行
    //140	国际件特殊通知
    //141	预售件准备发运
    //147	整车在途
    //15	海关查验
    //151	分配师傅
    //152	师傅预约
    //153	师傅提货
    //154	师傅上门
    //16	正式报关待申报
    //17	海关待查
    //18	海关扣件
    //186	KC装车
    //187	KC卸车
    //188	KC揽收
    //189	KC快件交接
    //190	无人车发车
    //201	准备拣货
    //202	出库
    //205	仓库内操作-订单审核
    //206	仓库内操作-拣货
    //208	代理交接
    //211	星管家派件交接
    //212	星管家派送
    //214	星管家收件
    //215	星管家退件给客户
    //405	船舶离港
    //406	船舶到港
    //407	接驳点收件出仓
    //41	交收件联(二程接驳收件)
    //43	收件入仓
    //46	二程接驳收件
    //47	二程接驳派件
    //570	铁路发车
    //571	铁路到车
    //604	CFS清关
    //605	运力抵达口岸
    //606	清关完成
    //607	代理收件
    //611	理货异常
    //612	暂存口岸待申报
    //613	海关放行待补税
    //614	清关时效延长
    //619	检疫查验
    //620	检疫待查
    //621	检疫扣件
    //623	海关数据放行
    //626	到转第三方快递
    //627	寄转第三方快递
    //630	落地配派件出仓
    //631	新单退回
    //632	储物柜交接
    //633	港澳台二程接驳收件
    //634	港澳台二程接驳派件
    //64	晨配转出
    //642	门市/顺丰站快件上架
    //643	门市/顺丰站快件转移
    //646	包装完成
    //647	寄方准备快件中
    //648	快件已退回/转寄,新单号为: XXX
    //649	代理转运
    //65	晨配转入
    //651	SLC已揽件
    //655	合作点收件
    //656	合作点交接给顺丰
    //657	合作点从顺丰交接
    //658	合作点已派件
    //66	中转批量滞留
    //660	合作点退件给客户
    //664	客户接触点交接
    //676	顺PHONE车
    //677	顺手派
    //678	快件到达驿站
    //679	驿站完成派件
    //70	由于XXX原因 派件不成功
    //700	拍照收件
    //701	一票多件拍照收件
    //72	标记异常
    //75	混建包复核
    //77	中转滞留
    //830	出库
    //831	入库
    //833	滞留件入仓
    //84	重量复核
    //843	集收入库
    //844	配送出库
    //847	二程接驳
    //850	集收
    //851	集收
    //86	晨配装车
    //87	晨配卸车
    //870	非正常派件
    //88	外资航空起飞
    //880	上门派件
    //89	外资航空落地
    //900	订舱路由
    //921	晨配在途
    //930	外配装车
    //931	外配卸车
    //932	外配交接
    //933	外配出仓
    //934	外配异常
    //935	外配签收
    //950	快速收件
    //96	整车发货
    //97	整车签收
    //98	代理路由信息
    //99	应客户要求,快件正在转寄中
    //44	正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
    //204	派件责任交接
    //80	已签收,感谢使用顺丰,期待再次为您服务
    //8000	在官网"运单资料&签收图",可查看签收人信
}