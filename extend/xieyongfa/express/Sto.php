<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON> di <PERSON>
 * <AUTHOR>
 *
 */
class Sto extends Express implements ExpressInterface
{
    protected $url = 'http://cloudinter-linkgateway.sto.cn/gateway/link.do';
    protected $debug_url = 'http://cloudinter-linkgatewaytest.sto.cn/gateway/link.do';

    protected $api_name = '';


    public function request($param)
    {
        //参数设置
        $secret     = $this->getSecret();
        $json_param = json_encode($param, JSON_UNESCAPED_UNICODE);
        //请求参数
        $post_data = [
            'content'     => $json_param,
            'data_digest' => base64_encode(md5($json_param . $secret, true)),
            'api_name'    => $this->api_name,
            'from_appkey' => $this->getAppKey(),
            'from_code'   => $this->getAppKey(),
            'to_appkey'   => 'sto_trace_query',
            'to_code'     => 'sto_trace_query',
        ];
        $url       = $this->get_url();
        $result    = curl()->form_params()->post($url, $post_data)->get_body();
        return $this->getResult($result);
    }

    protected function getResult($result)
    {
        if (isset($result['success']) && $result['success'] !== 'true') {
            $apiErrorMsg   = $result['errorMsg'] ?? '未知错误';
            $this->message = ($result['errorCode'] ?? -1) . $apiErrorMsg;
            return false;
            throw new \Exception($this->message);
        }
        return $result['data'] ?? $result;
    }

    /**
     * 打印订单
     * @param array|string $expressNo 单号
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function getPrintFile($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    protected function get_url()
    {
        return $this->is_debug() ? $this->debug_url : $this->url;
    }

    protected function get_auth_url()
    {
        return $this->is_debug() ? $this->debug_auth_url : $this->auth_url;
    }

    protected function is_debug()
    {
        return (bool)$this->getConfigByKey('debug');
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @link https://open.sf-express.com/Api/ApiDetails?level3=252&category=1&apiClassify=1
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    public function preCheckExpressNo($express_no)
    {
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';

        $this->api_name = 'STO_TRACE_QUERY_COMMON';
        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $expressNo = strtoupper($expressNo);
        $result    = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }
        $param  = [
            'order'         => 'asc',     // 电话号码验证
            'waybillNoList' => [$expressNo],  // 查询号:  trackingType=1,则此值为顺丰运单号            如果trackingType = 2, 则此值为客户订单号
        ];
        $result = $this->request($param);

        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        $traces                = $this->_parseTrace($result);
        $express_status        = $this->_parseExpressStatus($result); // 物流状态
        $express_name          = '申通快递';//快递公司名称
        $express_logo          = 'https://img.alicdn.com/imgextra/i4/6000000005759/O1CN011sPfaFSwwp3GZX2_!!6000000005759-2-urc.png'; //快递公司LOGO
        $express_service_phone = '95543';//快递公司客服电话
        $message               = $this->message ?? '查询成功'; //提示文字
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    protected function _parseTrace($result)
    {
        $trace = [];
        if (!empty($result)) {
            foreach (current($result) as $val) {
                $trace[] = [
                    'remark'           => $val['memo'] ?? '',
                    'time'             => $val['opTime'] ?? '',
                    'date_hour_minute' => date('n月d日 H:i', strtotime($val['opTime'])),
                    //                    'accept_address' => $val['acceptAddress'] ?? '',
                    //'status_code'      => $val['opCode'] ?? '',
                ];
            }
        }
        return $this->sort_trace($trace);
    }

    /**
     * 批量查询路由 可以queryRoute传入多个单号 手机号用逗号隔开
     * @param array $dataMap
     */
    public function queryRoutes($dataMap)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    //快递状态
    //0 已揽件 (不会返回这个状态)
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * Get express status
     *
     * @link http://open.sf-express.com/developSupport/734349?activeIndex=589678
     * 归为：1-无轨迹，2-揽件，3-在途，4-签收，5-问题件
     * @param int $state
     */
    protected function _parseExpressStatus($result)
    {
        if (empty($result)) {
            return 1; // 无记录
        }

        $last_route = current($result);
        $last_route = end($last_route);

        $last_route_status = $last_route['scanType'] ?? '';
        if (empty($last_route_status)) {
            return 1; //暂无记录
        }
        $status_map = [
            '收件'     => 0, //顺丰已收件
            '到件'     => 2, //运输中
            '发件'     => 2, //运输中
            '派件'     => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '签收'     => 4, //已签收
            '客户签收' => 4, //已签收
        ];
        return $status_map[$last_route_status] ?? 2; //其余状态返回 运输中
    }
}