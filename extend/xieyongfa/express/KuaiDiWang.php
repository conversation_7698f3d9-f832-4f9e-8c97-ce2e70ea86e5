<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON> di wang
 * <AUTHOR>
 *
 */
class <PERSON>aiDiWang extends Express implements ExpressInterface
{
    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        return ['status' => '0', 'message' => '暂无开通', 'order' => null];
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        return ['status' => '0', 'message' => '暂无开通'];
    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;

        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        $requestData = [
            'id'    => $this->getAppId(),
            'com'   => $expressCode,
            'nu'    => $expressNo,
            'show'  => 0,
            'muti'  => 0,
            'order' => 'desc',
        ];

        $result = curl()->get($this->getConfigByKey('query_url'), $requestData)->get_body();

        if (empty($result) || !isset($result['success'])) {
            return ['status' => '0', 'message' => '接口返回数据有误，' . $result['message'] ?? ''];
        }
        if (!$result['success']) {
            return ['status' => '0', 'message' => '查询失败，' . $result['reason'] ?? ''];
        }

        $traces = [];
        if (!empty($result['data'])) {
            foreach ($result['data'] as $trace) {
                $traces[] = [
                    'remark'         => $trace['Remark'] ?? '',
                    'accept_time'    => $trace['time'] ?? '',
                    'accept_address' => $trace['context'] ?? '',
                ];
            }
        }
        $trace = $this->sort_trace($trace);

        $expressStatus = isset($result['status']) ? $this->getExpressStatus($result['status']) : 1; //0-无轨迹，1-揽件，2-在途，3-签收，4-问题件

        return ['status' => '1', 'message' => 'success', 'traces' => $traces, 'express_status' => $expressStatus, 'result' => $result];
    }

    public function queryRoutes($dataMap)
    {

    }

    /**
     * Make sign
     * @param string $requestData
     */
    protected function makeSign($requestData)
    {
        return urlencode(base64_encode(md5($requestData . $this->getAppKey())));
    }

    /**
     * Get express status
     * 归为：1-无轨迹，2-揽件，3-在途，4-签收，5-问题件
     * @param int $state
     */
    protected function getExpressStatus($state)
    {
        $status = 0;
        switch ($state) {
            case 0:
                $status = 0;
                break;
            case 3: //在途
            case 8: //派送
                $status = 2;
                break;
            case 4:
                $status = 1;
                break;
            case 6:
                $status = 3;
                break;
            case 5:
            case 7:
            case 9:
                $status = 4;
                break;
        }

        return $status;
    }
}