<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * <PERSON><PERSON> di <PERSON>
 * <AUTHOR>
 *
 */
class Jt extends Express implements ExpressInterface
{
    protected $url = 'https://openapi.jtexpress.com.cn/webopenplatformapi/api/{PATH}';
    protected $debug_url = 'https://uat-openapi.jtexpress.com.cn/webopenplatformapi/api/{PATH}?uuid=ca846caa35c944899191bc61cb2549fc';

    protected $api_name = '';

    protected function getContentDigest()
    {
        $str = strtoupper($this->customerCode . md5($this->pwd . 'jadada236t2')) . $this->key;
        return base64_encode(pack('H*', strtoupper(md5($str))));
    }

    /**
     * 头部请求部分加密
     * param array $post
     * @return string
     */
    protected function getHeaderDigest($post)
    {
        return base64_encode(pack('H*', strtoupper(md5(json_encode($post) . $this->getAppKey()))));
    }

    public function request($param)
    {
        $json_param = json_encode($param, JSON_UNESCAPED_UNICODE);
        $header     = [
            'apiAccount' => $this->getAppId(),
            'digest'     => $this->getHeaderDigest($param),
            'timestamp'  => time(),
        ];
        $url        = str_replace('{PATH}', $this->api_name, $this->get_url());
        $result     = curl()->set_header($header)->form_params()->post($url, ['bizContent' => $json_param])->get_body();
        return $this->getResult($result);
    }

    protected function getResult($result)
    {
        if (isset($result['code']) && $result['code'] !== '1') {
            $this->message = $result['msg'] ?? '未知错误';
            return false;
            throw new \Exception($this->message);
        }
        return $result['data'] ?? $result;
    }

    protected function get_url()
    {
        return $this->is_debug() ? $this->debug_url : $this->url;
    }

    protected function is_debug()
    {
        return (bool)$this->getConfigByKey('debug');
    }

    public function preCheckExpressNo($express_no)
    {
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';

        $this->api_name = 'logistics/trace';
        if (empty($expressCode) || empty($expressNo)) {
            return ['status' => '0', 'message' => '快递编码、运单号不能为空'];
        }
        $expressNo = strtoupper($expressNo);
        $result    = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }
        $param = [
            'billCodes' => $expressNo,  // 查询号:  trackingType=1,则此值为顺丰运单号            如果trackingType = 2, 则此值为客户订单号
        ];

        $result = $this->request($param);

        if ($result === false) {
            return ['status' => '0', 'message' => $this->message];
        }
        $traces                = $this->_parseTrace($result);
        $express_status        = $this->_parseExpressStatus($result); // 物流状态
        $express_name          = '极兔速递';//快递公司名称
        $express_logo          = 'https://img.alicdn.com/imgextra/i3/6000000007799/O1CN01vz5s7P27TzeDcwJtK_!!6000000007799-0-urc.jpg'; //快递公司LOGO
        $express_service_phone = '956025';//快递公司客服电话
        $message               = $this->message ?? '查询成功'; //提示文字
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    protected function _parseTrace($result)
    {
        $trace = [];
        if (!empty($result)) {
            $first_trace = current($result);
            foreach ($first_trace['details'] as $val) {
                $trace[] = [
                    'remark'           => $val['desc'] ?? '',
                    'time'             => $val['scanTime'] ?? '',
                    'date_hour_minute' => date('n月d日 H:i', strtotime($val['scanTime'])),
                    //                    'accept_address' => $val['acceptAddress'] ?? '',
                    //'status_code'      => $val['opCode'] ?? '',
                ];
            }
        }
        return $this->sort_trace($trace);
    }

    /**
     * 批量查询路由 可以queryRoute传入多个单号 手机号用逗号隔开
     * @param array $dataMap
     */
    public function queryRoutes($dataMap)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    //快递状态
    //1、快件揽收
    //2、入仓扫描（停用）
    //3、发件扫描
    //4、到件扫描
    //5、出仓扫描
    //6、入库扫描
    //7、代理点收入扫描
    //8、快件取出扫描
    //9、出库扫描
    //10、快件签收
    //11、问题件扫描
    /**
     * Get express status
     *
     * @param array $result
     */
    protected function _parseExpressStatus($result)
    {
        if (empty($result)) {
            return 1; // 无记录
        }
        $last_route = current($result);
        if (empty($last_route['details'])) {
            return 1; // 无记录
        }
        $last_route = current($last_route['details']);

        $last_route_status = $last_route['scanType'] ?? '';

        if (empty($last_route_status)) {
            return 1; //暂无记录
        }
        //1、快件揽收
        //2、入仓扫描（停用）
        //3、发件扫描
        //4、到件扫描
        //5、出仓扫描
        //6、入库扫描
        //7、代理点收入扫描
        //8、快件取出扫描
        //9、出库扫描
        //10、快件签收
        //11、问题件扫描
        $status_map = [
            '快件揽收'     => 0, //顺丰已收件
            //            '发件'         => 2, //运输中
            '出仓扫描'     => 3, //正在派送途中,请您准备签收(派件人:XXX,电话:XXX)
            '快件签收'     => 4, //已签收
            '出库扫描'     => 4, //已签收
            '快件取出扫描' => 4, //已签收
        ];
        return $status_map[$last_route_status] ?? 2; //其余状态返回 运输中
    }

    /**
     * 打印订单
     * @param array|string $expressNo 单号
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function getPrintFile($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Cancel order
     * {@inheritDoc}
     * @link https://open.sf-express.com/Api/ApiDetails?level3=252&category=1&apiClassify=1
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }
}