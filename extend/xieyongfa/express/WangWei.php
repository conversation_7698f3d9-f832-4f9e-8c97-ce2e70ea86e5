<?php

namespace xieyongfa\express;

use xieyongfa\express\Contracts\ExpressInterface;

/**
 * WangWei
 * <AUTHOR>
 *
 */
class WangWei extends Express implements ExpressInterface
{
    protected $qrery_url = 'https://ali-deliver.showapi.com/showapi_expInfo'; // 查询物流信息地址
    protected $app_code = '1c23d44c233640a591f4a0adbd8262c0';
    protected $status = 1;
    protected $message = '';


    /**
     * 打印订单
     * @param array|string $orderId 单号
     * @param array|string $expressNo 单号
     * @param string $portName 打印机名称
     * @return string|array $html
     */
    public function printOrder($orderId, $expressNo, $portName)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }

    /**
     * Create order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::createOrder()
     */
    public function createOrder($expressCode, $orderId, $sender, $receiver, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }


    /**
     * Cancel order
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::cancelOrder()
     */
    public function cancelOrder($expressCode, $expressNo, $orderId, $options = [])
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }
    //快递状态
    //0 已揽件 (不会返回这个状态)
    //1 暂无记录
    //2 在途中
    //3 派送中
    //4 已签收 (完结状态)
    //5 用户拒签
    //6 疑难件
    //7 无效单 (完结状态)
    //8 超时单
    //9 签收失败
    //10 退回
    /**
     * 解析快递状态
     * @param string $result
     * @return int
     */
    protected function _parseExpressStatus($status)
    {
        return $status;
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return int
     */
    protected function _parseTrace($array)
    {
        $trace = [];
        foreach ($array as $key => $val) {
            $trace[] = [
                'remark'           => $val['context'],
                'time'             => $val['time'],
                'date_hour_minute' => date('n月d日 H:i', strtotime($val['time'])),
            ];
        }
        return $this->sort_trace($trace);
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->status  = 0;
            $this->message = '解析返回结果失败';
            return false;
        }
        //        if (isset($result['showapi_res_body']) && isset($result['showapi_res_body']['flag']) && $result['showapi_res_body']['flag'] == false) {
        //            $this->status  = 0;
        //            $this->message = $result['showapi_res_body']['msg'];
        //            return false;
        //        }
        if (isset($result['showapi_res_body']['msg'])) {
            $this->message = $result['showapi_res_body']['msg'];
        }

        if (isset($result['showapi_res_code']) && $result['showapi_res_code'] == -1) {
            $msg = '物流信息查询失败:' . $result['showapi_res_error'] ?? '系统维护中!';
            wr_log($msg, 1);
            $this->status  = 0;
            $this->message = $msg;
            return false;
        }

        if (isset($result['showapi_res_body']) && isset($result['showapi_res_body']['ret_code']) && $result['showapi_res_body']['ret_code'] == -1) {
            $msg = '物流信息查询失败:' . $result['showapi_res_body']['remark'] ?? '系统维护中';
            wr_log($msg, 1);
            $this->status  = 0;
            $this->message = $msg;
            return false;
        }
        return !empty($result['showapi_res_body']) ? $result['showapi_res_body'] : $result;
    }

    public function preCheckExpressNo($express_no)
    {
        //        if (!tools()::start_with($express_no, 'SF')) {
        //            $this->message = '顺丰单号必须以SF开头';
        //            $this->status  = 0;
        //            return false;
        //        }
        return parent::preCheckExpressNo($express_no);
    }

    public function queryFee(array $query_data)
    {
        return ['status' => -1, 'message' => '暂不支持查询运费'];
    }

    /**
     * @param string $query_data 查询数据
     * {@inheritDoc}
     * @see \xieyongfa\express\Contracts\ExpressInterface::queryRoute()
     */
    public function queryRoute(array $query_data)
    {
        $expressNo   = $query_data['express_no'] ?? '';
        $expressCode = $query_data['express_code'] ?? '';
        $mobile      = $query_data['mobile'] ?? '';
        $mobile      = substr($mobile, -4);// 只需要后四位;
        $orderId     = $query_data['order_guid'] ?? '';
        $bid         = $query_data['bid'] ?? '';
        $appcode     = "APPCODE " . $this->app_code;

        $result = $this->preCheckExpressNo($expressNo);
        if ($result === false) {
            $express_status = 7;// 无效单
            return [
                'status'              => '0',
                'express_status'      => $express_status,
                'express_status_name' => $this->getExpressStatusNameByStatus($express_status),
                'message'             => $this->message
            ];
        }


        $expressCode = $this->getExpressCodeByCode($expressCode, 'wanwei_code', 'auto');
        $data        = [
            'com'           => $expressCode,
            'nu'            => $expressNo,
            'phone'         => $mobile,
            'receiverPhone' => 'receiverPhone',
            'senderPhone'   => 'senderPhone',
        ];
        $result      = curl()->set_http_errors(false)->set_header(['Authorization' => $appcode])->get($this->qrery_url, $data)->get_body();
        $result      = $this->_parseResult($result);
        if ($result === false) {
            return ['status' => $this->status, 'message' => $this->message];
        }
        if (!isset($result['data']) || !isset($result['status'])) {
            throw new \Exception('物流系统维护中,请复制单号到其他APP查询物流信息');
        }
        $traces                = $this->_parseTrace($result['data']);
        $express_status        = $this->_parseExpressStatus($result['status']); // 物流状态
        $express_name          = $result['expTextName'] ?? '未知快递';//快递公司名称
        $express_logo          = $result['logo'] ?? ''; //快递公司LOGO
        $express_service_phone = $result['tel'] ?? '';//快递公司客服电话
        $message               = $result['msg'] ?? '查询成功'; //提示文字
        return [
            'status'                => 1,
            'message'               => $message,
            'traces'                => $traces,
            'express_status'        => $express_status,
            'express_status_name'   => $this->getExpressStatusNameByStatus($express_status),
            'express_no'            => $expressNo,
            'express_name'          => $express_name,
            'express_logo'          => $express_logo,
            'express_service_phone' => $express_service_phone,
            //            'result'                => $result
        ];
    }

    public function queryRoutes($dataMap)
    {
        throw new \Exception('当前通道暂时不支持' . __FUNCTION__ . '方法');
    }


    /**
     * Get express status
     * 归为：1-无轨迹，2-揽件，3-在途，4-签收，5-问题件
     * @param int $state
     */
    protected function getExpressStatus($state)
    {
        $state = intval($state);
        return $state + 1;
    }
}