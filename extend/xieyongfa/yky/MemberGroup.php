<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 会员级别
 * @library MemberGroup
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Get_MemberGroupPagedV2_ALL() 所有级别列表 // http://openapi.1card1.cn/OpenApiDoc/Get_MemberGroupPagedV2
 **/
class MemberGroup extends BasicYky
{
    /**
     * 获取级别列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_MemberGroupPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_MemberGroupPagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'GroupName ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}