<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 推送
 * @library Push
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Push extends BasicYky
{

    /**
     * 消息推送
     * @link http://openapi.1card1.cn/OpenApiDoc/PushMessage
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function PushMessage($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'title'       => '', // 是 消息标题
            'click'       => '', // 是 点击消息打开的链接
            'broadcast'   => '', // 否 语音播报内容（此项为空时，播报title）
            'printUrl'    => '', // 否 打印url
            'action'      => '', // 否 事件处理(如qd11云打印：Printer,区分大小写)
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}