<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 会员
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Member extends BasicYky
{
    /**
     * 获取会员列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_MembersPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_MembersPagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'CardId ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__, true);
    }

    /**
     * 获取会员列表
     * @link http://openapi.1card1.cn/OpenApiDoc/MemberLogin
     * @param string $card_id 卡号
     * @param string $password 密码
     * @throws Exception
     * @return bool|array
     */
    public function MemberLogin($card_id, $password)
    {
        $post_data = [
            'cardId'   => $card_id,
            'password' => $password,
        ];
        return $this->getArrayResult($post_data, __FUNCTION__);
    }

    /**
     * 查询会员信息
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_MemberInfo
     * @param string $card_id 卡号，或者手机号，或者会员唯一标识Guid
     * @param string $password 密码
     * @param boolean $is_get_ext_value 是否获取扩展字段
     * @throws Exception
     * @return bool|array
     */
    public function Get_MemberInfo($card_id, $password = '', $is_get_ext_value = false)
    {
        $post_data = [
            'cardId'        => $card_id,
            'password'      => $password,
            'isGetExtValue' => $is_get_ext_value,
        ];
//        {
//    "status": 0,
//  "data": [
//    {
//        "CardId": "1",
//      "Sex": 0,
//      "TrueName": "谢永发",
//      "Mobile": "",
//      "Tel": "",
//      "Email": "",
//      "UserAccount": "2",
//      "StoreName": "支付下沉门店",
//      "MemberGuid": "2da99ecb-4f7c-e511-ab53-001018640e2a",
//      "ChainStoreGuid": "2f65a638-9972-11ea-8c97-20040fed9860",
//      "IdCard": "",
//      "MemberGroupGuid": "6dae807e-6f24-e611-94a9-0010186c9142",
//      "BirthTime": null,
//      "Birthday": null,
//      "IsLunar": false,
//      "ImagePath": "https://files.1card1.cn//Platform/159168/********/d10098c193e74691ab078f751a1f47b7.jpg",
//      "MemberGroupName": "白金VIP卡",
//      "RegisterTime": "2024-03-29",
//      "DurationTime": "永久有效",
//      "Meno": null,
//      "RecommendMemberCardId": "2",
//      "RecommendMemberName": "饭饭",
//      "ProvinceId": 19,
//      "ProvinceName": "广东",
//      "CityId": 199,
//      "CityName": "深圳",
//      "CountyId": 3255,
//      "CountyName": "龙华区",
//      "Address": null,
//      "PostCode": "",
//      "UsedPoint": "***********.32",
//      "UsedValue": "453540.06",
//      "EnablePoint": "2.20",
//      "EnableValue": "2698.00",
//      "TotalPoint": "10001391808.52",
//      "TotalValue": "456238.06",
//      "TotalMoney": "806724.8645",
//      "FreezedValue": "0.00",
//      "ThirdOpenId": "oZa5mw57eITM54_RTwAWzSinJizs",
//      "CarId": "DQ777,冀C3B23,粤S6GK4,粤S6GK46,冀C3B23F"
//    }
//  ]
//}
        return $this->getArrayResult($post_data, __FUNCTION__, true);
    }

    /**
     * 注册会员
     * @link http://openapi.1card1.cn/OpenApiDoc/Add_Member
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Add_Member(array $post_data)
    {
        $default_data = [
            'userAccount'     => $this->default_user_account, // 否 工号（会员级别为空时，取云会员中设置的店面下属的工号）
            'cardId'          => '', // 是 卡号
            'password'        => '', // 否 密码，为空时根据“密码模式”生成密码
            'memberGroupName' => '', // 否 会员级别 （通过“获取会员级别列表”获得）, 为空时，取云会员设置的会员级别、登记店面和工号'
            'trueName'        => '', // 否 姓名
            'sex'             => '', // 否 性别(1,先生;2,女士;)
            'birth'           => '', // 否 生日（如“2000-01-01”，暂不支持农历）
            'mobile'          => '', // 否 手机号码
            'idCard'          => '', // 否 身份证
            'imagePath'       => '', // 否 图片地址（通过“上传图片”获得）imagePath 字段
            'email'           => '', // 否 邮箱
            'provinceId'      => '', // 否 省份Id（通过“获取省份列表”获得）
            'cityId'          => '', // 否 城市Id（通过“获取城市列表”获得）
            'countyId'        => '', // 否 县区Id（通过“获取县区列表”获得）
            'address'         => '', // 否 地址
            'recommendCardId' => '', // 否 推荐人卡号
            'meno'            => '', // 否 备注
            'openId'          => '', // 否 第三方平台openid
            'deviceType'      => '', // 否 设备登录类型：1,表示微信登录;2表示支付宝（第三方平台openid不为空时生效，为空时表示微信）
            'extValue'        => '', // 否 扩展字段(Json类型字符串数组)
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 编辑会员
     * @link http://openapi.1card1.cn/OpenApiDoc/Update_Member
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Update_Member($post_data = [])
    {
        $default_data = [
            'cardId' => '', // 是 卡号
            //            'password'              => '', // 否 密码
            //            'memberGroupName'       => '', // 否 会员级别名称（通过会员级别列表”获得“GroupName”字段）
            //            'chainStoreName'        => '', // 否 店面名称（通过获取店面列表”获得“StoreName”字段）
            //            'durationTime'          => '', // 否 会员到期时间（字符串类型。1、null为不传参数；2、"2019-09-04"为会员2019-09-04到期；3、"永久有效"为会员永久有效）
            //            'imagePath'             => '', // 否 图片地址（通过“上传图片”获得）imagePath 字段
            //            'sex'                   => '', // 否 性别（1、男；2、女）
            //            'trueName'              => '', // 否 姓名
            //            'mobile'                => '', // 否 手机号码
            //            'idCard'                => '', // 否 身份证号码
            //            'birthday'              => '', // 否 生日（DateTime类型）
            //            'postCode'              => '', // 否 邮编
            //            'email'                 => '', // 否 电子邮箱
            //            'provinceId'            => '', // 否 省份Id（通过“获取省份列表”获得）
            //            'cityId'                => '', // 否 城市Id（通过“获取城市列表”获得）
            //            'countyId'              => '', // 否 县区Id（通过“获取县区列表”获得）
            //            'address'               => '', // 否 详细地址
            //            'meno'                  => '', // 否 备注
            //            'recommendMemberCardId' => '', // 否 推荐人卡号
            //            'extValue'              => '', // 否 扩展字段(Json类型字符串数组)
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 修改会员密码
     * @link http://openapi.1card1.cn/OpenApiDoc/Update_MemberPassword
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Update_MemberPassword($post_data = [])
    {
        $default_data = [
            'cardId'      => '',
            'password'    => '',
            'newPassword' => '',
            'userAccount' => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 重置会员密码
     * @link http://openapi.1card1.cn/OpenApiDoc/ReSetMemberPassword
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function ReSetMemberPassword($post_data = [])
    {
        $default_data = [
            'cardId'      => '',
            'newPassword' => '',
            'mobile'      => '',
            'userAccount' => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 删除会员
     * @link http://openapi.1card1.cn/OpenApiDoc/Delete_Member
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Delete_Member($post_data = [])
    {
        $default_data = [
            'userAccount'  => $this->default_user_account,
            'cardId'       => '',
            'isRealDelete' => false //	是否彻底删除（默认彻底删除，为false时删除到回收站）
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 锁定/解锁会员
     * @link http://openapi.1card1.cn/OpenApiDoc/Lock_Member
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Lock_Member($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => '',
            'type'        => 1, // 操作类型，1：锁定，2：解锁
            'reason'      => '' //锁定原因
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 根据第三方平台标识获取会员唯一标识
     * @link http://openapi.1card1.cn/VipCloudDoc/GetMemberGuidByOpenId
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function GetMemberGuidByOpenId($post_data = [])
    {
        $this->controller = 'VipCloud';
        $default_data     = [
            'deviceType'  => 1, // 用户类型(1,微信;2,支付宝;4,百度直达号)
            'thirdOpenId' => '' // 绑定类型Id
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 绑定会员
     * @link http://openapi.1card1.cn/VipCloudDoc/BindMember
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function BindMember($post_data = [])
    {
        $this->controller = 'VipCloud';
        $default_data     = [
            'deviceType'  => 1, // 用户类型(1,微信;2,支付宝;4,百度直达号)
            'thirdOpenId' => '', // 绑定类型Id
            'cardId'      => '' //会员卡号
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}