<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 门店
 * @library Store
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * // * @method Update_ChainStoreV2($post_data) 更新门店 // http://openapi.1card1.cn/BusinessDoc/Update_ChainStoreV2
 **/
class Gift extends BasicYky
{

    /**
     * 获取礼品列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_GiftsPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_GiftsPagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'Name ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取礼品类别列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_GiftTypePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_GiftTypePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'Name ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 新建礼品
     * @link http://openapi.1card1.cn/BusinessDoc/Add_Gift
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Add_Gift($post_data = [])
    {
        $this->controller = 'Special';
        $default_data     = [
            'Account'          => $this->default_user_account,
            'Barcode'          => '', // 是 礼品编号
            'Name'             => '', // 是 礼品名称
            'GiftType'         => '', // 是 所属类别（通过“获取礼品类别列表”获得Name字段）
            'Point'            => '', // 是 所需积分
            'Number'           => '', // 是 数量
            'EndTime'          => '', // 是 有效期
            'Meno'             => '', // 否 备注
            'MemberGroupGuids' => '', // 否 限定会员级别
            'GoodsItemGuid'    => '', // 是 项目Guid
            'Count'            => '', // 是 计次数量
            'Value'            => '', // 否 储值
            'Description'      => '', // 是 礼品描述
            'IsHide'           => '', // 是 是否在微信端隐藏
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 更新礼品
     * @link http://openapi.1card1.cn/BusinessDoc/Update_Gift
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Update_Gift($post_data = [])
    {
        $this->controller = 'Special';
        $default_data     = [
            //            'Guid'             => '', // 是 礼品Guid
            //            'Barcode'          => '', // 是 礼品编号
            //            'Name'             => '', // 是 礼品名称
            //            'GiftType'         => '', // 是 所属类别（通过“获取礼品类别列表”获得Name字段）
            //            'Point'            => '', // 是 所需积分
            //            'Number'           => '', // 是 数量
            //            'EndTime'          => '', // 是 有效期
            //            'Meno'             => '', // 否 备注
            //            'MemberGroupGuids' => '', // 否 限定会员级别
            //            'GoodsItemGuid'    => '', // 否 项目Guid
            //            'Count'            => '', // 是 计次数量
            //            'Value'            => '', // 否 储值
            //            'Description'      => '', // 是 礼品描述
            //            'IsHide'           => '', // 是 是否在微信端隐藏
            'Account' => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 删除礼品
     * @link http://openapi.1card1.cn/BusinessDoc/Delete_Gift
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Delete_Gift($post_data = [])
    {
        $this->controller = 'Special';
        $default_data     = [
            'Guid'    => '', // 是 礼品Guid
            'Account' => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取积分兑换记录
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_GiftExchangeNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_GiftExchangeNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount'    => $this->default_user_account,
            'cardId'         => '',
            'memberPassword' => '',//如果密码不为空，则验证密码
            'where'          => '',
            'pageIndex'      => 0,
            'pageSize'       => 200,
            'orderBy'        => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 积分兑换礼品
     * @link http://openapi.1card1.cn/OpenApiDoc/GiftExChange
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function GiftExChange($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => '', // 是 卡号
            'password'    => '', // 否 会员密码（会员系统中设置储值消费或者积分消费需要密码时，此项必须）
            'giftList'    => '', // 是 Json数据，礼品列表（barcode：礼品编码（通过“获取礼品列表”获得“Barcode”字段）；number：数量；meno：备注）
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}