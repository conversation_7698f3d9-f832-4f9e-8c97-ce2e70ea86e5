<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 券
 * @library Coupon
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Add_ChainStoreV2($post_data) 新建门店
 * @method Update_ChainStoreV2($post_data) 更新门店
 **/
class Coupon extends BasicYky
{

    /**
     * 获取优惠券列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_CouponPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_CouponPagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'CreateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取优惠券发送列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_CoupnSendPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_CoupnSendPagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取优惠券使用记录列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_CoupnUsedPaged
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_CoupnUsedPaged($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取已发送团购券
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_GroupBuyTicketPaged
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_GroupBuyTicketPaged($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'Guid DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 核销优惠券或者SN码
     * @link http://openapi.1card1.cn/OpenApiDoc/SubCoupon
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function SubCoupon($post_data = [])
    {
        $default_data = [
            'userAccount'    => $this->default_user_account,
            'couponSendGuid' => '', //	优惠券发送记录唯一标识（通过“获取已发送优惠券”获得“Guid”字段,（与subCount共同生效））
            'subCount'       => 0, //优惠券核销数量（与couponSendGuid共同生效）
            'snCodes'        => '', //团购券SN码（多个SN码用英文逗号隔开，couponSendGuid与subCount都为空时生效，即不与优惠券同时核销）
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 发送优惠券
     * @link http://openapi.1card1.cn/OpenApiDoc/SendCoupon
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function SendCoupon($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'mobiles'     => '', //	手机号码(多个用“,”隔开,mobiles与cardIds 不能同时为空)
            'cardIds'     => '', //	会员卡号(多个用“,”隔开,mobiles与cardIds 不能同时为空)
            'memberGuids' => '',//会员GUID
            'couponGuid'  => '', // 优惠券唯一标识（通过“获取优惠券列表”获得“Guid”字段）
            'sendCount'   => 0, // 发送数量
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

}