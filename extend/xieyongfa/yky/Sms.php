<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 短信
 * @library Sms
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Sms extends BasicYky
{

    /**
     * 发送短信（单发）
     * @link http://openapi.1card1.cn/OpenApiDoc/SendSms
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function SendSms($post_data = [])
    {
        $this->controller = 'OpenApi';
        $default_data     = [
            'userAccount' => $this->default_user_account,
            'mobile'      => '',
            'content'     => 0,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}