<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 消费
 * @library Consume
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Consume($post_data) 消费 // http://openapi.1card1.cn/OpenApiDoc/Consume
 * @method SaveCountAddConsume($post_data) 计次消费 // http://openapi.1card1.cn/OpenApiDoc/SaveCountAddConsume
 * @method ReturnGoods($post_data) 单据撤销 // http://openapi.1card1.cn/OpenApiDoc/ReturnGoods
 **/
class Consume extends BasicYky
{

    /**
     * 获取消费列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_ConsumeNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_ConsumeNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__, true);
    }

    /**
     * 根据消费单据唯一标识，或者单据号获取消费明细
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_ConsumeNoteItemPagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_ConsumeNoteItemPagedV2($post_data = [])
    {
        $default_data = [
            'guid'       => '',
            'billNumber' => '',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__, true);
    }
}