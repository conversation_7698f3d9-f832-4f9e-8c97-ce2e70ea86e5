<?php
declare (strict_types=1);


namespace xieyongfa\yky;

use xieyongfa\yky\Contracts\DataArray;
use xieyongfa\yky\Exceptions\InvalidInstanceException;

/**
 * 加载缓存器
 * @library Yky
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Member Member($options) static 会员
 * @method MemberGroup MemberGroup($options) static 会员级别
 * @method User User($options) static 用户
 * @method Store Store($options) static 门店
 * @method Business Business($options) static 商家
 * @method Point Point($options) static 积分
 * @method Value Value($options) static 储值
 * @method Goods Goods($options) static 商品
 * @method Gift Gift($options) static 礼品
 * @method Pay Pay($options) static 支付
 * @method Oauth Oauth($options) static 授权
 * @method Coupon Coupon($options) static 优惠券
 * @method Sms Sms($options) static 短信
 * @method Staff Staff($options) static 员工
 * @method Consume Consume($options) static 消费
 * @method Extensions Extensions($options) static 扩展字段
 * @method Push Push($options) static 推送
 * @method Upload Upload($options) static 上传
 *
 **/
class Yky
{

    /**
     * 定义当前版本
     * @var string
     */
    const VERSION = '1.0.1';

    /**
     * 静态配置
     * @var DataArray
     */
    private static DataArray $config;

    /**
     * 设置及获取参数
     * @param array $option
     * @return array
     */
    public static function config($option = null): array
    {
        $sn = get_instance_sn($option);
        if (is_array($option)) {
            self::$config[$sn] = new DataArray($option);
        }
        if (isset(self::$config[$sn]) && self::$config[$sn] instanceof DataArray) {
            return self::$config[$sn]->get();
        }
        return [];
    }

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function __callStatic(string $name, array $arguments)
    {
        $class = __NAMESPACE__ . '\\' . $name;
        if (!class_exists($class)) {
            throw new InvalidInstanceException('Class {$class} not found');
        }
        $option = array_shift($arguments);
        $config = is_array($option) ? $option : self::$config->get();
        return new $class($config);
    }

    /**
     * 静态魔术加载方法
     * @param array $config 配置
     * @param string $class 类名
     * @param string $action 方法名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function call(array $config, string $class, string $action, array $arguments)
    {
        $obj = self::__callStatic($class, [$config]);
        return call_user_func_array([$obj, $action], $arguments);
    }
}