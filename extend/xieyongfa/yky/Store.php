<?php
declare (strict_types=1);


namespace xieyongfa\yky;

use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 门店
 * @library Store
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Add_ChainStoreV2($post_data) 新建门店 // http://openapi.1card1.cn/BusinessDoc/Add_ChainStoreV2
 * @method Get_ChainStorePagedV2_ALL() 所有门店 // http://openapi.1card1.cn/OpenApiDoc/Get_ChainStorePagedV2
 * @method Update_ChainStoreV2($post_data) 更新门店 // http://openapi.1card1.cn/BusinessDoc/Update_ChainStoreV2
 **/
class Store extends BasicYky
{
    public string $controller = 'Special';

    /**
     * 获取门店列表
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_ChainStorePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_ChainStorePagedV2($post_data = [])
    {
        $this->controller = 'OpenApi';
        $default_data     = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'StoreName ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 删除门店
     * @link http://openapi.1card1.cn/BusinessDoc/Delete_ChainStore
     * @param array $post_data 数据包
     * @return bool|array
     */
    public function Delete_ChainStore($post_data = [])
    {
        $default_data = [
            'account'        => $this->default_user_account,
            'chainStoreGuid' => '',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}