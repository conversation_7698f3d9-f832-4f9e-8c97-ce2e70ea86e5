<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 用户
 * @library User
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Add_User($post_data) 新建用户 // http://openapi.1card1.cn/BusinessDoc/Add_User
 * @method Update_User($post_data) 更新用户 // http://openapi.1card1.cn/BusinessDoc/Update_User
 * @method Get_UserAccountPaged_ALL() 所有用户 // http://openapi.1card1.cn/BusinessDoc/Get_UserAccountPaged
 **/
class User extends BasicYky
{
    public string $controller = 'BusinessApi';

    /**
     * 获取工号列表
     * @link http://openapi.1card1.cn/BusinessDoc/Get_UserAccountPaged
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_UserAccountPaged($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'UserAccount ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     *
     * 删除工号
     * @link http://openapi.1card1.cn/BusinessDoc/Delete_User
     * @param array $post_data 数据包
     * @return bool|array
     */
    public function Delete_User($post_data = [])
    {
        $default_data = [
            'userAccount' => '',
            'account'     => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}