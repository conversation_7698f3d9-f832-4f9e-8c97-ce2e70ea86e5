<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 授权
 * @library Oauth
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Oauth extends BasicYky
{
    public string $controller = 'VipCloud';


    /**
     * 网页授权接口
     * @link http://openapi.1card1.cn/VipCloudDoc/GetOAuthUrl
     * @param string $url 回调地址
     * @throws Exception
     * @return bool|array
     */
    public function GetOAuthUrl($url)
    {
        $post_data = ['redirectUrl' => $url];
        return $this->getArrayResult($post_data, __FUNCTION__);
    }

    /**
     * 验证签名
     * 用于验证请求的合法性
     * @param string $timestamp 时间戳
     * @param string $signature 待验证的签名
     * @throw Exception
     * @return void 返回验证结果(true-验证通过,false-验证失败)
     */
    public function checkSignature($timestamp, $signature)
    {
        if (strtoupper(md5($this->openid . $this->secret . $timestamp)) !== $signature) {
            throw  new Exception('系统繁忙,请稍候再试');
        }
    }
}