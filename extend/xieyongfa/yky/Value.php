<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 储值
 * @library Value
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Add_Value($post_data) 会员充值 // http://openapi.1card1.cn/OpenApiDoc/Add_Value
 * @method Add_MemberRuleValue($post_data) 规则充值 // http://openapi.1card1.cn/OpenApiDoc/Add_MemberRuleValue
 * @method ValueConsume($post_data) 储值扣费 // http://openapi.1card1.cn/OpenApiDoc/ValueConsume
 * @method Get_MemberGroupValueRulesV2($post_data) 获取充值规则 // http://openapi.1card1.cn/OpenApiDoc/Get_MemberGroupValueRulesV2
 * @method Update_MemberValue($post_data) 增加/扣除储值. // http://openapi.1card1.cn/OpenApiDoc/Update_MemberValue
 **/
class Value extends BasicYky
{

    /**
     * 根据会员卡号，获取工号查看店面范围内的储值记录
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_ValueNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_ValueNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount'    => $this->default_user_account,
            'cardId'         => '',
            'memberPassword' => '',//如果密码不为空，则验证密码
            'where'          => '',
            'pageIndex'      => 0,
            'pageSize'       => 200,
            'orderBy'        => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取所有会员在工号查看店面范围内的储值记录，也可根据筛选条件获取部分储值记录
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_AllValueNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_AllValueNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 冻结/解冻储值
     * @link http://openapi.1card1.cn/OpenApiDoc/FrozenValue
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function FrozenValue($post_data = [])
    {
        $default_data = [
            'userAccount'    => $this->default_user_account,
            'cardId'         => '',
            'freezedOperate' => 1, //操作类型（"1":表示冻结，"2"：表示解冻）
            'frozenValue'    => 0, //冻结/解冻储值
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}