<?php
declare (strict_types=1);

namespace xieyongfa\yky\Contracts;

use app\model\Business;
use app\model\Parameter;
use Exception;

class BasicYky
{
    /**
     * 接口基础地址
     */
    const BASE_URL = 'https://openapi.1card1.cn';
    /**
     * 商户OPENID
     */
    public string $openid;

    /**
     * 商户SECRET
     */
    public string $secret;

    /**
     * status
     */
    public int $status = -1;

    /**
     * message
     */
    public string $message = '';

    /**
     * data
     */
    public array $response_data = [];
    /**
     * action
     */
    public string $action = '';

    /**
     * controller
     */
    public string $controller = 'OpenApi';

    /**
     * 默认操作工号,默认10000工号
     */
    protected string $default_user_account;

    /**
     * @var DataArray
     */
    public DataArray $config;


    /**
     * 当前请求方法参数
     * @var array
     */
    protected array $currentMethod = [];

    /**
     * 当前是否已经重试过
     * @var bool
     */
    protected bool $isTry = false;

    /**
     * @param array $options
     * @throws Exception
     */
    public function __construct(array $options)
    {
        $this->config = new DataArray($options);
        if (!$this->config->offsetExists('openid') || !$this->config->offsetExists('secret')) {
            throw new Exception('openid或者secret未设置');
        }
        $this->openid = $options['openid'];
        $this->secret = $options['secret'];
        if (empty($this->openid) || empty($this->secret)) {
            throw new Exception('开放平台API密钥未设置!');
        }
        $this->default_user_account = (!empty($options['yky_default_user_account'])) ? $options['yky_default_user_account'] : '10000';
    }

    /**
     * 获取签名
     * @param string $data 签名json数据
     * @param int $timestamp 时间戳
     * @return bool|string 签名值
     */
    protected function getSignature(string $data, int $timestamp)
    {
        return strtoupper(md5($this->openid . $this->secret . $timestamp . $data));
    }

    /**
     * 动态调用
     * @param string $method
     * @param array $arguments
     * @return mixed
     * @throws Exception
     */
    public function __call(string $method, array $arguments = [])
    {
        $this->action = $method;
        $needle       = '_ALL';
        $length       = strlen($needle);
        if (substr($method, -$length) === $needle) {
            //以_ALL结尾代表获取所有数据
            $method = str_replace('_ALL', '', $method);
            $result = $this->$method();
            if ($result === false) {
                throw new Exception($this->message);
            }
            $total_page = ceil($result['total'] / count($result['data']));
            if ($total_page > 1) {
                for ($page_index = 1; $page_index <= $total_page; $page_index++) {
                    $data           = ['pageIndex' => $page_index];
                    $store          = $this->$method($data);
                    $result['data'] = array_merge($result['data'], $store['data']);
                }
            }
            return $result;
        }
        return $this->getArrayResult(array_shift($arguments), $method);
    }

    /**
     * 获取原始结果
     * @return array
     */
    public function getResponseData(): array
    {
        return $this->response_data;
    }

    /**
     * 获取结果
     * @param array $post_data 数据包
     * @param string $action 方法名
     * @param bool $ignore_log 不记录日志
     * @return bool|array
     * @throws Exception
     */
    protected function getArrayResult(array $post_data = [], string $action = '', $ignore_log = false)
    {
        $backtrace = debug_backtrace();
        $action    = $action ?: ($backtrace[1]['function']);
        $this->registerApi(__FUNCTION__, func_get_args());
        $data      = json_encode($post_data, JSON_UNESCAPED_UNICODE);
        $timestamp = time();
        $signature = $this->getSignature($data, $timestamp);
        $type      = $action == 'Add_Business' ? 'agentAccount' : 'openId';
        $url       = self::BASE_URL . '/' . $this->controller . '/' . $action . '?' . $type . '=' . $this->openid . '&signature=' . $signature . '&timestamp=' . $timestamp;
        $result    = curl()->set_timeout(15)->setMaxRetries(3)->ignore_log($ignore_log)->form_params()->post($url, ['data' => $data])->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @link http://openapi.1card1.cn/
     * @return bool|array
     */
    protected function retry()
    {
        $this->isTry = true;
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @link http://openapi.1card1.cn/
     * @param mixed $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            if (isset($this->currentMethod['method']) && empty($this->isTry)) {
                return $this->retry();
            }
            $this->status  = -1;
            $this->message = '解析返回结果失败';
            return false;
        }
        $this->response_data = $result; //赋值 $data 便于 status 不等于0时候还需要使用返回的数据
        if ((isset($result['status']) && $result['status'] !== 0) || (isset($result['success']) && $result['success'] !== true)) {
            $this->status  = $result['status'] ?? -1;
            $this->message = $result['message'] ?? '未知错误';
            $this->message .= $result['failMessages'] ?? '';
            $list          = '已经存在|卡号不存在|会员不存在|密码错误|密码有误|锁定|卡号已被删除|未匹配到订单号|订单还未付款|过期|该笔消费使会员升级';
            if (!preg_match("/$list/", $this->message)) {
                $db_parameter = new Parameter();
                $bid          = $db_parameter->get_bid_by_key_name_and_value('openid', $this->openid);
                if ($bid) {
                    //有可能只是走的一卡易支付通道,就不会有bid信息
                    $db_business   = new Business();
                    $business_info = $db_business->get_business_info_by_account_or_guid($bid);
                    $this->message .= '(账号:' . $business_info['account'] . '-' . $business_info['business_name'] . ')';
                }
                wr_log($this->message, 1);
            }
            return false;
        }
        return $result;
    }

    /**
     * 支付通知接口
     * @link http://openapi.1card1.cn/VipCloudDoc/NotifyUrl
     * @return array 数据包
     * @throws Exception
     */
    public function verify(): array
    {

        //        {
        //     "type": "Pay",
        //    "businessAccount": "***********",
        //    "billNumber": "****************",
        //    "totalMoney": 0.0100,
        //    "attach": "",
        //    "uniqueCode": "20220606120641427980",
        //    "orderNo": "****************"
        //}
        $_GET             = array_change_key_case($_GET); // key 全部转小写,便于兼容
        $remote_signature = $_GET['signature']; //Signature
        $data             = $_POST['data'];
        $local_signature  = $this->getSignature($data, (int)($_GET['timestamp'])); //TimeStamp
        if ($remote_signature !== $local_signature) {
            $msg = '签名校验不通过:remote_signature:' . $remote_signature . ' local_signature:' . $local_signature;
            throw new Exception($msg);
        }
        return json_decode($data, true);
    }
}