<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 商家
 * @library Business
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Business extends BasicYky
{
    public string $controller = 'BusinessApi';

    /**
     * 新建商家
     * @link http://openapi.1card1.cn/BusinessDoc/Add_Business
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Add_Business($post_data = [])
    {
        $default_data = [
            'businessAccount' => '', // 是 商家账号
            'password'        => '', // 是 密码
            'businessName'    => '', // 是 商家名称
            'edition'         => '', // 是 注册版本（通过“获取版本列表”获得“EditionKeyName”字段） http://openapi.1card1.cn/BusinessDoc/GetEditionListV2
            'contact'         => '', // 否 联系人
            'sex'             => '', // 否 性别(1,先生;2,女士;)
            'qq'              => '', // 否 QQ
            'tel'             => '', // 否 联系电话
            'email'           => '', // 否 邮箱
            'provinceId'      => '', // 否 省份Id（通过“获取省份列表”获得）
            'cityId'          => '', // 否 城市Id（通过“获取城市列表”获得）
            'countyId'        => '', // 否 县区Id（通过“获取县区列表”获得）
            'address'         => '', // 否 详细地址
            'postCode'        => '', // 否 邮编
            'webAddress'      => '', // 否 网址
            'introduction'    => '', // 否 企业介绍
            'meno'            => '', // 否 备注
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 受限充值
     * @link http://openapi.1card1.cn/BusinessDoc/Update_ChainStoreLimit
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Update_ChainStoreLimit($post_data = [])
    {
        $default_data = [
            'userAccount'     => $this->default_user_account,
            'chainStoreGuid'  => '',
            'limitType'       => 1, // 充值类型(1,积分受限;2,储值受限;3,短信受限;4,计次受限)
            'totalLimitValue' => 0, // 充值额度
            'meno'            => '', // 	备注
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取账号信息
     * @link http://openapi.1card1.cn/BusinessDoc/Get_BusinessInfo
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_BusinessInfo($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}