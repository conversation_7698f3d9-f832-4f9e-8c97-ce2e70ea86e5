<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 积分
 * @library Point
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Get_GiftExchangeNotePagedV2($post_data)  获取积分兑换记录 // http://openapi.1card1.cn/OpenApiDoc/Get_GiftExchangeNotePagedV2
 * @method Update_MemberPoint($post_data) 增加/扣除积分. // http://openapi.1card1.cn/OpenApiDoc/Update_MemberPoint
 **/
class Point extends BasicYky
{

    /**
     * 根据会员卡号，获取工号查看店面范围内的积分记录
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_PointNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_PointNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount'    => $this->default_user_account,
            'cardId'         => '',
            'memberPassword' => '',//如果密码不为空，则验证密码
            'where'          => '',
            'pageIndex'      => 0,
            'pageSize'       => 200,
            'orderBy'        => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取所有会员在工号查看店面范围内的积分记录，也可根据筛选条件获取部分积分记录
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_AllPointNotePagedV2
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_AllPointNotePagedV2($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'OperateTime DESC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__, true);
    }
}