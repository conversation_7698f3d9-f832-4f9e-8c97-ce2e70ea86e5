<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 扩展字段
 * @library Extensions
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Extensions extends BasicYky
{

    /**
     * 获取扩展字段名称、描述
     * @link http://openapi.1card1.cn/OpenApiDoc/Get_Extensions
     * @param int $type 数据包 （1、会员，2、商品，3、礼品，4、订单）
     * @throws Exception
     * @return bool|array
     */
    public function Get_Extensions($type = 1)
    {
        $post_data = [
            'userAccount' => $this->default_user_account,
            'parentType'  => $type, //属性归属的对象（1、会员，2、商品，3、礼品，4、订单）
        ];
        return $this->getArrayResult($post_data, __FUNCTION__);
    }
}