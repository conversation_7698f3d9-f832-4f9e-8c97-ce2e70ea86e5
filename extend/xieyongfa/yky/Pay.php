<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 支付
 * @library Pay
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method UPayOrder($post_data) 自助买单下单接口  // http://openapi.1card1.cn/VipCloudDoc/UPayOrder
 * @method QueryOrder($post_data) 查询订单接口 // http://openapi.1card1.cn/VipCloudDoc/QueryOrder
 * @method CreateOrder_Mini($post_data) 小程序下单接口 // http://openapi.1card1.cn/VipCloudDoc/CreateOrder_Mini
 **/
class Pay extends BasicYky
{
    public string $controller = 'VipCloud';

    /**
     * 查单接口
     * @link http://openapi.1card1.cn/OpenApiDoc/CheckOperatorScan
     * @param string $order_no 订单号
     * @throws Exception
     * @return bool|array
     */
    public function CheckOperatorScan(string $order_no)
    {
        $this->controller = 'OpenApi';
        $post_data        = ['orderNo' => $order_no];
        return $this->getArrayResult($post_data, __FUNCTION__);
    }

    /**
     * 退款查单接口
     * @link http://openapi.1card1.cn/OpenApiDoc/GetRefundOrderStatus
     * @param string $order_no 订单号
     * @throws Exception
     * @return bool|array
     */
    public function GetRefundOrderStatus(string $order_no)
    {
        $this->controller = 'OpenApi';
        $post_data        = ['refundOrderNo' => $order_no];
        return $this->getArrayResult($post_data, __FUNCTION__);
    }

    /**
     * B扫C
     * @link http://openapi.1card1.cn/OpenApiDoc/OperatorScan
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function OperatorScan($post_data = [])
    {
        $this->controller = 'OpenApi';
        $default_data     = [
            'userAccount' => $this->default_user_account,
            'totalFee'    => 0, // 支付金额(元)
            'meno'        => '', // 单据备注
            'authCode'    => '', // 顾客付款码
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}