# 一卡易(YKY)扩展包使用说明

## 简介

本扩展包提供了与一卡易系统进行交互的完整解决方案，包含会员管理、积分操作、储值管理、优惠券、商品等多个功能模块。

## 主要功能模块

### 基础配置

- `Yky.php` - 一卡易基础配置和初始化
- `Oauth.php` - OAuth2.0 授权认证
- `Business.php` - 商户相关操作

### 会员相关

- `Member.php` - 会员信息管理
- `MemberGroup.php` - 会员分组管理
- `User.php` - 用户信息操作

### 营销工具

- `Coupon.php` - 优惠券管理
- `Point.php` - 积分操作
- `Value.php` - 储值管理
- `Gift.php` - 礼品/赠品管理

### 商品与交易

- `Goods.php` - 商品管理
- `Pay.php` - 支付相关
- `Consume.php` - 消费记录

### 门店管理

- `Store.php` - 门店信息管理
- `Staff.php` - 员工管理

### 系统功能

- `Push.php` - 消息推送
- `Sms.php` - 短信服务
- `Upload.php` - 文件上传
- `Extensions.php` - 扩展功能

## 使用示例

### 1. 配置说明

```php
$config = [
    'appid' => 'your_appid',        // 一卡易应用ID
    'secret' => 'your_secret',      // 一卡易密钥
    'store_id' => 'your_store_id'   // 店铺ID
];
```

### 2. 会员操作

```php
use xieyongfa\yky\Yky;

// 获取会员信息
$member = Yky::Member($config);
$memberInfo = $member->Get_MemberInfo('会员卡号');

// 注册会员
$data = [
    'cardId' => '会员卡号',
    'mobile' => '13800138000',
    'trueName' => '张三'
];
$result = $member->Add_Member($data);

// 修改会员信息
$updateData = [
    'cardId' => '会员卡号',
    'mobile' => '13800138001'
];
$result = $member->Update_Member($updateData);
```

### 3. 积分操作

```php
use xieyongfa\yky\Yky;

$point = Yky::Point($config);

// 查询会员积分记录
$pointData = [
    'cardId' => '会员卡号',
    'pageSize' => 10,
    'pageIndex' => 0
];
$result = $point->Get_PointNotePagedV2($pointData);

// 增加/扣除积分
$pointUpdateData = [
    'cardId' => '会员卡号',
    'point' => 100,               // 正数为增加，负数为扣除
    'memo' => '活动奖励'          // 备注说明
];
$result = $point->Update_MemberPoint($pointUpdateData);
```

### 4. 优惠券操作

```php
use xieyongfa\yky\Yky;

$coupon = Yky::Coupon($config);
// 发放优惠券
$result = $coupon->grant([
    'cardId' => '会员卡号',
    'couponId' => '优惠券ID'
]);
```

## 注意事项

1. 使用前请确保已正确配置一卡易的相关参数
2. 所有金额相关的数值单位均为"分"
3. 接口调用前建议先做异常处理
4. 所有方法都支持通过静态方式调用：`Yky::ModuleName($config)`

## 异常处理

本扩展包使用统一的异常处理机制：

```php
use xieyongfa\yky\Exceptions\YkyException;

try {
    $member = Yky::Member($config);
    $result = $member->Get_MemberInfo('会员卡号');
} catch (YkyException $e) {
    // 处理异常
    echo $e->getMessage();
}
```

## 其他说明

- 本扩展包需要 PHP 7.1+
- 依赖 curl 扩展
- 建议配置日志记录所有 API 调用
- 正式环境中请确保 HTTPS 传输
