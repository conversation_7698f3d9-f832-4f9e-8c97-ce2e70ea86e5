<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 员工
 * @library Staff
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Add_Staff($post_data) 新建员工 // http://openapi.1card1.cn/BusinessDoc/Add_Staff
 * @method Update_Staff($post_data) 更新员工 // http://openapi.1card1.cn/BusinessDoc/Update_Staff
 * @method Delete_Staff($post_data) 删除员工 // http://openapi.1card1.cn/BusinessDoc/Delete_Staff
 **/
class Staff extends BasicYky
{
    public string $controller = 'BusinessApi';

    /**
     * 获取员工列表
     * @link http://openapi.1card1.cn/BusinessDoc/Get_StaffPaged
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_StaffPaged($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'CreatedTime ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取员工-职位列表
     * @link http://openapi.1card1.cn/BusinessDoc/Get_PositionPaged
     * @param array $post_data 数据包
     * @throws Exception
     * @return bool|array
     */
    public function Get_PositionPaged($post_data = [])
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => 'Name ASC',
        ];
        return $this->getArrayResult(array_merge($default_data, $post_data), __FUNCTION__);
    }
}