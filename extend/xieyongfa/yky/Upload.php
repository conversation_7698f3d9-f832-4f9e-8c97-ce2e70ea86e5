<?php
declare (strict_types=1);


namespace xieyongfa\yky;


use Exception;
use xieyongfa\yky\Contracts\BasicYky;

/**
 * 上传
 * @library Push
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Upload extends BasicYky
{

    /**
     * 上传图片或视频
     * @link http://openapi.1card1.cn/OpenApiDoc/UploadImage
     * @param string $url 数据包
     * @throws Exception
     * @return bool|array
     */
    public function UploadImage($url)
    {
        $default_data = [
            'userAccount' => $this->default_user_account,
            'imageUrl'    => $url, // 文件地址 web外网可访问的URL
            'imgType'     => 1, // 上传类型(空或者1为图片上传,2为视频上传)
        ];
        return $this->getArrayResult(array_merge($default_data, []), __FUNCTION__);
    }
}