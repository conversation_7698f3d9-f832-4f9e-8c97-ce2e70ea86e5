<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\translate\driver;


use xieyongfa\translate\Driver;
use xieyongfa\translate\traits\Tools;

class Baidu extends Driver
{
    use Tools;

    public function __construct($config)
    {

    }

    public function get_config($data)
    {
        return [
            'channel' => 'baidu', //根据此字段来决定如何处理  translate_url 结果 便于后续扩展其他通道
        ];
    }

    public function translate($data)
    {

    }


}
