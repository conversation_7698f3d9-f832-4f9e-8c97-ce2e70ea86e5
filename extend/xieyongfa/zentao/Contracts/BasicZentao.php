<?php
declare (strict_types=1);

namespace xieyongfa\zentao\Contracts;

use Exception;

class BasicZentao
{
    /**
     * 接口基础地址
     */
    const BASE_URL = 'https://sz1card1.5upm.com';
    /**
     * 账号
     */
    public string $user_name;

    /**
     * 密码
     */
    public string $password;
    /**
     * 登录标识
     */
    public array $session;

    /**
     * status
     */
    public int $status = -1;

    /**
     * message
     */
    public string $message;


    /**
     * @var DataArray
     */
    public DataArray $config;


    /**
     * 当前请求方法参数
     * @var array
     */
    protected array $currentMethod = [];

    /**
     * 当前模式
     * @var bool
     */
    protected bool $isTry = false;

    /**
     * @param array $options
     * @throws Exception
     */
    public function __construct(array $options = [])
    {
        $options      = ['user_name' => 'xyf', 'password' => 'xyf1155'];
        $this->config = new DataArray($options);
        if (!$this->config->offsetExists('user_name') || !$this->config->offsetExists('password')) {
            throw new Exception('user_name或者password不能为空');
        }
        $this->user_name = $options['user_name'];
        $this->password  = $options['password'];
        $this->session   = [];
    }

    public function get_session()
    {
        if (!empty($this->session) && is_array($this->session)) {
            return $this->session;
        }
        $username = $this->user_name;
        $key      = 'zentao:login_session:' . $username;
        if ($session = cache($key)) {
            $this->session = $session;
        } else {
            $path          = '/api-getSessionID.json';
            $this->session = $this->get($path, [], false);
            $login_result  = $this->login();
            cache($key, $this->session, 600);
        }
        return $this->session;
    }

    public function login()
    {
        $path = '/user-login.json';
        $data = ['account' => $this->user_name, 'password' => $this->password];
        return $this->post($path, $data);
    }

    /**
     * 获取结果
     * @param string $path 路径
     * @param array $data 数据包
     * @param bool $require_login 是否需要登录
     * @return bool|array
     */
    protected function get(string $path, array $data = [], $require_login = true)
    {
        //        $backtrace = debug_backtrace(); //$backtrace[1]
        $this->registerApi(__FUNCTION__, func_get_args());
        return $this->request($path, 'GET', $data, $require_login);
    }

    /**
     * 获取结果
     * @param string $path 路径
     * @param array $data 数据包
     * @param bool $require_login 是否需要登录
     * @return bool|array
     */
    protected function post(string $path, array $data = [], $require_login = true)
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        return $this->request($path, 'POST', $data, $require_login);
    }

    /**
     * 获取结果
     * @param string $path 路径
     * @param string $method 请求方法 GET|POST
     * @param array $data 数据包
     * @param bool $require_login 是否需要登录
     * @return bool|array
     */
    protected function request(string $path, string $method, array $data = [], $require_login = true)
    {
        $url = self::BASE_URL . $path;
        if ($require_login) {
            $parse_url = parse_url($path);
            $link_str  = empty($parse_url['query']) ? '?' : '&';
            $session   = $this->get_session();
            $url       .= $link_str . $session['sessionName'] . '=' . $session['sessionID'];
        }
        $method = strtolower($method);
        $result = curl()->form_params()->$method($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @link http://openapi.1card1.cn/
     * @return bool|array
     */
    protected function retry()
    {
        $this->isTry = true;
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @link http://openapi.1card1.cn/
     * @param mixed $result
     * @return bool|array
     * @throws Exception
     */
    protected function _parseResult($result)
    {
        if (is_array($result)) {
            if (isset($result['status']) && $result['status'] == 'success' && isset($result['data'])) {
                return json_decode($result['data'], true);
            }
            if (isset($result['status']) && $result['status'] !== 'success') {
                $this->status  = -1;
                $this->message = $result['reason'] ?? '未知错误';
                if (isset($this->currentMethod['method']) && empty($this->isTry)) {
                    return $this->retry();
                }
                throw new Exception($this->message);
            }
        }
        return $result;
    }
}