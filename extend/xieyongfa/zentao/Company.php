<?php
declare (strict_types=1);


namespace xieyongfa\zentao;

use xieyongfa\zentao\Contracts\BasicZentao;

/**
 * 组织
 * @library Company
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Company extends BasicZentao
{

    public $user_array = [
        'wuling'   => '吴玲',
        'dengling' => '邓玲',
        'zchen'    => '张晨',
        'xty'      => '谢腾远',
    ];

    /**
     * 获取组织列表
     * @param array $post_data 数据包
     * @return bool|array
     */
    public function browse($post_data = [])
    {
        $path = '/company-browse.json';
        return $this->get($path);
    }

    /**
     * 获取日志列表
     * @param string $date 日期
     * @param bool $json 是否返回json
     * @return bool|array
     */
    public function calendar(string $date = '', $json = true)
    {
        // $path = '/company-calendar.json';
        $date   = $date ?: date('Ymd', strtotime('-1 day'));
        $date   = strtotime($date);
        $ext    = $json ? 'json' : 'html';
        $path   = '/company-calendar-27-' . $date . '-' . $date . '-0-0--1-yes.' . $ext;
        $result = $this->get($path);
        return $result['datas']['current'];
    }

    /**
     * 获取昨天产品部日志列表
     * @param bool $json 是否返回json
     * @return bool|array
     */
    public function yesterday_work_record($json = true)
    {
        $date        = date('Ymd', strtotime('- 1day'));
        $work_record = $this->calendar($date, $json);
        if ($json) {
            foreach ($work_record as $key => $val) {
                if (!empty($val)) {
                    $current_val = current($val);
                    foreach ($current_val as $k => $v) {
                        //                        unset($current_val[$k]['end']);
                        //                        unset($current_val[$k]['begin']);
                        //$current_val[$k]['consumed'] = (int)$current_val[$k]['consumed'];
                        $current_val[$k] = $current_val[$k]['work'] . '(' . $current_val[$k]['consumed'] . 'h)';
                    }
                    $work_record[$key] = $current_val;
                }
                if (isset($this->user_array[$key])) {
                    $work_record[$this->user_array[$key]] = $work_record[$key];
                }
                unset($work_record[$key]);
            }
        }
        return $work_record;
    }

    /**
     * 获取昨日日志html
     * @return bool|array
     */
    public function yesterday_work_record_html()
    {
        $result       = $this->yesterday_work_record();
        $date         = date('Y-m-d', strtotime('-1 day')) . ' 产品部日志';
        $html         = "<table cellspacing='0' style='width:100%;border-collapse: collapse;text-align: center;border:none;font-size:11pt'>";
        $html         .= "<tr><td colspan='10' style='text-align:center;font-size:24px;font-weight:200'>" . $date . "</td></tr>";
        $html         .= "<tr style='background-color:#00B050;color:#FFF;height:40px;'>";
        $header_array = ['姓名', '日志'];
        foreach ($header_array as $header) {
            $html .= '<th>' . $header . '</th>';
        }
        $html     .= "</tr>";
        $td_style = 'border: solid #000 1px;';
        foreach ($result as $key => $val) {
            //$background_color = ($key % 2 == 0) ? '#FFFFFF' : '#dbf4e6';
            $background_color = '#FFFFFF';
            //$current_key      = ++$key;
            $html .= "<tr style='background-color:$background_color'>";
            $html .= " <td  style='$td_style'>" . $key . "</td>";
            $str  = '';
            foreach ($val as $k => $v) {
                $str .= $v . "<br/>";
            }
            $html .= "<td style='$td_style'>" . $str . "</td>";
            $html .= "</tr>";
        }
        $html .= "</table >";
        return $html;
    }

    /**
     * 获取今日产品部日志列表
     * @param bool $json 是否返回json
     * @return bool|array
     */
    public function today_work_record($json = true)
    {
        $date        = date('Ymd');
        $work_record = $this->calendar($date, $json);
        foreach ($work_record as $key => $val) {
            if (!empty($val)) {
                $work_record[$key] = current($val);
            }
        }
        return $work_record;
    }
}