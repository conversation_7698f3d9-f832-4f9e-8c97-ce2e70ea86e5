<?php
declare (strict_types=1);


namespace xieyongfa\douyin;


use Exception;
use xieyongfa\douyin\Contracts\BasicDouyin;

/**
 * 门店
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Shop extends BasicDouyin
{
    /**
     * 门店信息查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.billing/ledger.query-record-by-cert
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Query(array $data): bool|array
    {
        $url          = '/goodlife/v1/shop/poi/query/';
        $default_data = [
            'page' => 1, //	 页码 （从1开始）
            'size' => 10, // 页大小
        ];
        return $this->getArrayResult($url, array_merge($default_data, $data));
    }
}