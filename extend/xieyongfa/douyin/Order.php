<?php
declare (strict_types=1);


namespace xieyongfa\douyin;


use Exception;
use xieyongfa\douyin\Contracts\BasicDouyin;

/**
 * 券
 * @library Coupon
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Order extends BasicDouyin
{


    /**
     * 验券历史查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.billing/certificate.verifyrecord.query
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Query(array $data): bool|array
    {
        $url          = '/goodlife/v1/trade/order/query/';
        $default_data = [
            'page_size'         => 10, //页大小，取值范围1～100
            'page_num'          => 1, //游标，传前一页最后一条记录的游标（首页传1）
            'account_id'        => $this->account_id, //企业号商家总店id
            'order_id'          => '1012865978624989791',
            //'poi_ids'    => [], //门店id列表，不传默认返回商家所有门店核销记录，多个值使用,拼接
            //'start_time' => '', //起始时间戳，单位毫秒，不传表示今天
            //'end_time'   => time().'000', //截止时间戳，单位毫秒
            'get_secret_number' => true,
        ];
        return $this->getResult($url, array_merge($default_data, $data));
    }
}