<?php
declare (strict_types=1);


namespace xieyongfa\douyin;


use Exception;
use xieyongfa\douyin\Contracts\BasicDouyin;

/**
 * 授权
 * @library Oauth
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Oauth extends BasicDouyin
{
    public bool $require_access_token = false;

    /**
     * 抖音获取授权码
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/account-permission/douyin-get-permission-code
     * @param string $redirect_uri 回调地址
     * @throws Exception
     * @return string
     */
    public function GetOAuthUrl($redirect_uri)
    {
        $url           = '/platform/oauth/connect/';
        $response_type = 'code';// string | 设置为'code'这个字符串即可
        $scope         = 'message'; // string | 应用授权作用域,多个授权作用域以英文逗号（,）分隔
        //        $redirect_uri   = "redirect_uri_example"; // string | 授权成功后的回调地址，必须以http/https开头。域名必须对应申请应用时填写的域名，如不清楚请联系应用申请人。
        $state = "state_example"; // string | 用于保持请求和回调的状态

        $optional_scope    = 'user_info,1,data.external.poi,1';//应用授权可选作用域,多个授权作用域以英文逗号（,）分隔，每一个授权作用域后需要加上一个是否默认勾选的参数，1为默认勾选，0为默认不勾选
        $scope_scope_array = [
            'user_info',
            //      'trial.whitelist',
            //            'life.capacity.shop',
            //            'data.external.poi',
            //            'micapp.is_legal',
            //            'message.once.send',
            //            'poi.cps.common',
            //            'incremental_authorization',
            //            'js.ticket',
            //            'jsb.open.auth',
            //            'jsb.open.showAuth',
            //            'life.capacity.fulfilment'


            //            'friend_relation',
            //            'message',
        ];
        $optional_scope    = '';
        foreach ($scope_scope_array as $scope_scope) {
            $optional_scope .= $scope_scope . ',1,';
        }
        $optional_scope = rtrim($optional_scope, ',');
        $scope_list     = join(',', $scope_scope_array);
        $data           = [
            'client_key'    => $this->client_key,
            'response_type' => $response_type,
            'scope'         => $scope_list,
            //'optionalScope' => $optional_scope,
            'redirect_uri'  => tools()::replace_readonly_to_www($redirect_uri),
            'state'         => $state,
        ];
        return self::BASE_URL . $url . '?' . http_build_query($data);
    }

    /**
     * 获取 access_token
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/account-permission/get-access-token
     * @param string $code 授权码
     * @throws Exception
     * @return bool|array
     */
    public function GetAccessToken(string $code): bool|array
    {
        //     "access_token": "access_token",
        //    "description": "",
        //    "error_code": "0",
        //    "expires_in": "86400",
        //    "open_id": "aaa-bbb-ccc",
        //    "refresh_expires_in": "86400",
        //    "refresh_token": "refresh_token",
        //    "scope": "user_info"
        $url       = '/oauth/access_token/';
        $post_data = [
            'client_key'    => $this->client_key,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'authorization_code',
            'code'          => $code,
        ];
        return $this->getArrayResult($url, $post_data);
    }

    /**
     * 刷新 refresh_token 该接口用于刷新refresh_token的有效期；该接口适用于抖音授权。
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/account-permission/refresh_token
     * @param string $refresh_token 填写通过access_token获取到的refresh_token参数
     * @throws Exception
     * @return bool|array
     */
    public function RefreshToken(string $refresh_token): bool|array
    {

        //        "description": "",
        //        "error_code": "0",
        //        "expires_in": "86400",
        //        "refresh_token": "refresh_token"

        $url       = '/oauth/renew_refresh_token/';
        $post_data = [
            'client_key'    => $this->client_key,
            'refresh_token' => $refresh_token,
        ];
        return $this->getArrayResult($url, $post_data);
    }

    /**
     * 生成 client_token 该接口用于获取接口调用的凭证client_access_token，主要用于调用不需要用户授权就可以调用的接口；该接口适用于抖音/头条授权。
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/account-permission/client-token
     * @throws Exception
     * @return bool|array
     */
    public function GetClientToken(): bool|array
    {
        //    "access_token": "access_token",
        //    "description": "",
        //    "error_code": "0",
        //    "expires_in": "7200"
        $url       = '/oauth/client_token/';
        $post_data = [
            'client_key'    => $this->client_key,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'client_credential',
        ];
        return $this->getArrayResult($url, $post_data);
    }

    /**
     * 刷新access_token 该接口用于刷新access_token的有效期；该接口适用于抖音/头条授权。
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/account-permission/refresh-access-token
     * @param string $refresh_token 填写通过access_token获取到的refresh_token参数
     * @throws Exception
     * @return bool|array
     */
    public function RefreshAccessToken(string $refresh_token): bool|array
    {
        //    "access_token": "access_token",
        //    "description": "",
        //    "error_code": "0",
        //    "expires_in": "86400",
        //    "open_id": "aaaa-bbbb-cccc-dddd",
        //    "refresh_expires_in": "86400",
        //    "refresh_token": "refresh_token",
        //    "scope": "user_info"
        $url       = '/oauth/refresh_token/';
        $post_data = [
            'client_key'    => $this->client_key,
            'client_secret' => $this->client_secret,
            'refresh_token' => $refresh_token,
        ];
        return $this->getArrayResult($url, $post_data);
    }

}