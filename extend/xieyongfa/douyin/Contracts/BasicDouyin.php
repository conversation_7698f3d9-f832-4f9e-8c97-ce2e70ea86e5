<?php
declare (strict_types=1);

namespace xieyongfa\douyin\Contracts;

use app\model\DouyinConfig;
use Exception;

class BasicDouyin
{
    /**
     * 接口基础地址
     */
    const BASE_URL = 'https://open.douyin.com';
    /**
     * 商户id
     */
    public string $open_id;
    /**
     * 商户id
     */
    public string $account_id;
    /**
     * 应用key
     */
    public string $client_key;

    /**
     * 应用密钥
     */
    public string $client_secret;

    /**
     * 是否需要token
     */
    public bool $require_access_token = true;
    /**
     * status
     */
    public int $status = -1;

    /**
     * message
     */
    public string $message;

    /**
     * data
     */
    public array $response_data = [];


    /**
     * @var DataArray
     */
    public DataArray $config;


    /**
     * 当前请求方法参数
     * @var array
     */
    protected array $currentMethod = [];

    /**
     * 当前是否已经重试过
     * @var bool
     */
    protected bool $isTry = false;

    /**
     * @param array $options
     * @throws Exception
     */
    public function __construct(array $options)
    {
        $this->config = new DataArray($options);
        if (!$this->config->offsetExists('client_key') || !$this->config->offsetExists('client_secret')) {
            throw new Exception('client_key 或者 client_secret');
        }
        $this->client_key    = $options['client_key'];
        $this->client_secret = $options['client_secret'];
        if (!empty($options['open_id'])) {
            $this->open_id = $options['open_id'];
        }
        if (!empty($options['account_id'])) {
            $this->account_id = $options['account_id'];
        }
    }

    /**
     * 获取签名
     * @param string $data 签名json数据
     * @param int $timestamp 时间戳
     * @return bool|string 签名值
     */
    protected function getSignature(string $data, int $timestamp)
    {
        return strtoupper(md5($this->client_key . $this->client_secret . $timestamp . $data));
    }

    //    /**
    //     * 动态调用
    //     * @param string $method
    //     * @param array $arguments
    //     * @return mixed
    //     * @throws Exception
    //     */
    //    public function __call(string $method, array $arguments = [])
    //    {
    //        return $this->getArrayResult(array_shift($arguments), $method);
    //    }

    /**
     * 获取原始结果
     * @return array
     */
    public function getResponseData(): array
    {
        return $this->response_data;
    }


    /**
     * 获取结果
     * @param string $url url
     * @param array $post_data 数据包
     * @param bool $ignore_log 不记录日志
     * @return bool|array
     * @throws Exception
     */
    protected function getResult(string $url, array $post_data = [], bool $ignore_log = false)
    {
        $url = $url . '?' . http_build_query($post_data);
        return $this->getArrayResult($url, [], $ignore_log, 'get');
    }

    /**
     * 获取结果
     * @param string $url url
     * @param array $post_data 数据包
     * @param bool $ignore_log 不记录日志
     * @return bool|array
     * @throws Exception
     */
    protected function getArrayResult(string $url, array $post_data = [], bool $ignore_log = false, $method = 'post')
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $url    = self::BASE_URL . $url;
        $header = [
            'Content-Type' => 'application/json'
        ];
        if ($this->require_access_token) {
            //获取access_token
            $access_token           = isset($this->open_id) ? $this->getOpenIdAccessToken() : $this->getClientAccessToken();
            $header['access-token'] = $access_token;
        }
        $result = curl()->set_header($header)->$method($url, $post_data)->get_body();
        return $this->_parseResult($result);
    }

    public function getClientAccessToken()
    {
        $url       = self::BASE_URL . '/oauth/client_token/';
        $header    = [
            'Content-Type' => 'application/json'
        ];
        $cache_key = 'douyin_access_token:' . $this->client_key;
        if ($access_token = cache($cache_key)) {
            return $access_token;
        }
        $data         = [
            'grant_type'    => 'client_credential',
            'client_key'    => $this->client_key,
            'client_secret' => $this->client_secret,
        ];
        $result       = curl()->set_header($header)->set_header()->post($url, $data)->get_body();
        $access_token = $result['data']['access_token'];
        cache($cache_key, $access_token, $result['data']['expires_in']);
        return $access_token;
    }

    /**
     * @return mixed
     */
    public function getOpenIdAccessToken()
    {
        $open_id          = $this->open_id;
        $client_key       = $this->client_key;
        $db_douyin_config = new DouyinConfig();
        $map              = ['client_key' => $client_key, 'open_id' => $open_id];
        return $db_douyin_config->where($map)->value('access_token');
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @link http://openapi.1card1.cn/
     * @return bool|array
     */
    protected function retry()
    {
        $this->isTry = true;
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @link http://openapi.1card1.cn/
     * @param mixed $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            if (isset($this->currentMethod['method']) && empty($this->isTry)) {
                return $this->retry();
            }
            $this->status  = -1;
            $this->message = '解析返回结果失败';
            return false;
        }
        $this->response_data = $result; //赋值 $data 便于 status 不等于0时候还需要使用返回的数据
        $data                = $result['data'];
        if ((isset($data['error_code']) && $data['error_code'] !== 0)) {
            $this->status  = $data['error_code'];
            $this->message = $data['description'] ?? ($result['message'] ?? '未知错误');
            return false;
        }
        return $data;
    }

    //    /**
    //     * 支付通知接口
    //     * @link http://openapi.1card1.cn/VipCloudDoc/NotifyUrl
    //     * @return array 数据包
    //     * @throws Exception
    //     */
    //    public function verify(): array
    //    {
    //        $remote_signature = $_GET['Signature'];
    //        $local_signature  = $this->getSignature($_POST['data'], (int)$_GET['TimeStamp']);
    //        if ($remote_signature !== $local_signature) {
    //            throw new Exception('签名校验不通过');
    //        }
    //        return json_decode($_POST['data'], true);
    //    }
}