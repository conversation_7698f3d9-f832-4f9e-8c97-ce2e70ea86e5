<?php
declare (strict_types=1);


namespace xieyongfa\douyin;


use Exception;
use xieyongfa\douyin\Contracts\BasicDouyin;

/**
 * 门店
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class User extends BasicDouyin
{
    /**
     * 用户信息查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/account-management/get-account-open-info
     * @throws Exception
     * @return bool|array
     */
    public function get_user_info(): bool|array
    {
        $url          = '/oauth/userinfo/';
        $default_data = [
            'open_id'      => $this->open_id,
            'access_token' => parent::getOpenIdAccessToken(),
        ];
        return $this->getResult($url, $default_data);
    }
}