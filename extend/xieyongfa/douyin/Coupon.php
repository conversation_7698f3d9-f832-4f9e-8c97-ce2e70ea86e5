<?php
declare (strict_types=1);


namespace xieyongfa\douyin;


use Exception;
use xieyongfa\douyin\Contracts\BasicDouyin;

/**
 * 券
 * @library Coupon
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Coupon extends BasicDouyin
{

    /**
     * 验券准备
     * 刷新access_token 该接口用于刷新access_token的有效期；该接口适用于抖音/头条授权。
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.fulfilment/certificate.prepare
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Prepare(array $data): bool|array
    {
        $url          = '/goodlife/v1/fulfilment/certificate/prepare/';
        $default_data = [
//            'encrypted_data' => $data['encrypted_data'] ?? '', //从二维码解析出来的标识（传参前需要先进行URL编码） (encrypted_data/code必须二选一)
            'code'           => $data['code'] ?? '', //原始的抖音团购券码 (encrypted_data/code必须二选一)
        ];
        return $this->getResult($url, array_merge($default_data, $data));
    }

    /**
     * 验券准备
     * 刷新access_token 该接口用于刷新access_token的有效期；该接口适用于抖音/头条授权。
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.fulfilment/certificate.prepare
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Verify(array $data): bool|array
    {
        $url          = '/namek/fulfilment/prepare/';
        $default_data = [
            'verify_token'        => '', // 一次验券的标识 (用于短时间内的幂等)
            'poi_id'              => '', //核销的抖音门店id (若需要分店分账, 此参数必传)
            'encrypted_codes'     => [], //验券准备接口返回的加密抖音券码
            'codes'               => [], //三方原始券码值列表 (encrypted_codes/codes必须二选一)
            'order_id'            => '', //抖音侧的订单号 (非预导码模式的三方券码必需)
            'code_with_time_list' => [], //带有核销时间的三方码列表 （如果code_with_time_list 和 codes 同时传， 本字段优先级更高）
        ];
        return $this->getArrayResult($url, array_merge($default_data, $data));
    }

    /**
     * 验券准备
     * @link https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.fulfilment/certificate.prepare
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Cancel(array $data): bool|array
    {
        $url          = '/goodlife/v1/fulfilment/certificate/cancel/';
        $default_data = [
            'verify_id'      => '', // 代表券码一次核销的唯一标识（验券时返回）
            'certificate_id' => '', // 代表一张券码的标识（验券时返回）
        ];
        return $this->getArrayResult($url, array_merge($default_data, $data));
    }

    /**
     * 券状态查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.fulfilment/certificate.get
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Get(array $data): bool|array
    {
        $url          = '/goodlife/v1/fulfilment/certificate/get/';
        $default_data = [
            'encrypted_codes' => '', //验券准备接口返回的加密券码 （传参前需要先进行URL编码）
            'codes'           => [], //券码列表(明码, 可以传抖音码或三方码)
            'order_id'        => '', //抖音订单号 使用codes查询时必传
        ];
        return $this->getArrayResult($url, array_merge($default_data, $data));
    }

    /**
     * 验券历史查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.billing/certificate.verifyrecord.query
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function Query(array $data): bool|array
    {
        $url          = '/goodlife/v1/fulfilment/certificate/verify_record/query/';
        $default_data = [
            'size'       => 10, //页大小，取值范围1～500
            'cursor'     => 0, //游标，传前一页最后一条记录的游标（首页传0）
            'account_id' => '6829865644180228109', //企业号商家总店id（验券时返回）
            //'poi_ids'    => [], //门店id列表，不传默认返回商家所有门店核销记录，多个值使用,拼接
            //'start_time' => '', //起始时间戳，单位毫秒，不传表示今天
            //'end_time'   => time().'000', //截止时间戳，单位毫秒

        ];
        return $this->getResult($url, array_merge($default_data, $data));
    }

    /**
     * 分账明细查询
     * @link  https://open.douyin.com/platform/doc?doc=docs/openapi/life-service-open-ability/life.capacity/life.capacity.billing/ledger.query-record-by-cert
     * @param array $data 数据
     * @throws Exception
     * @return bool|array
     */
    public function QueryRecordByCert(array $data): bool|array
    {
        $url          = '/goodlife/v1/settle/ledger/query_record_by_cert/';
        $default_data = [
            'certificate_ids' => [], //券码的标识（验券时返回）列表，列表长度范围1～50，多个值使用,拼接
        ];
        return $this->getArrayResult($url, array_merge($default_data, $data));
    }
}