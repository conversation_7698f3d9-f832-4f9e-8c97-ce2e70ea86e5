<?php
declare (strict_types=1);

namespace xieyongfa\facade;

use think\Facade;
use xieyongfa\notify\Driver;

/**
 * @see \xieyongfa\notify\Notify
 * @package xieyongfa\facade
 * @mixin \xieyongfa\notify\Notify
 * @method static Driver channel(string $name = null) 创建网关
 * @method static Driver create_gateway($key_name, $driver, $provider) 创建网关
 *
 */
class Notify extends Facade
{
    /**
     * 获取当前Facade对应类名（或者已经绑定的容器对象标识）
     * @access protected
     * @return string
     */
    protected static function getFacadeClass(): string
    {
        return 'notify';
    }
}
