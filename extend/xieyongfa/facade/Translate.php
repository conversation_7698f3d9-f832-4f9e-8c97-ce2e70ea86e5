<?php
declare (strict_types=1);

namespace xieyongfa\facade;

use think\Facade;
use xieyongfa\translate\Driver;

/**
 * @see \xieyongfa\translate\Translate
 * @package xieyongfa\facade
 * @mixin \xieyongfa\translate\Translate
 * @method static Driver channel(string $name = null) 创建网关
 *
 */
class Translate extends Facade
{
    /**
     * 获取当前Facade对应类名（或者已经绑定的容器对象标识）
     * @access protected
     * @return string
     */
    protected static function getFacadeClass(): string
    {
        return 'translate';
    }
}
