<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 单据管理
 * @library Bill
 * <AUTHOR>
 * @date 2022/01/16 20:24
 **/
class Bill extends BasicYikayi
{
    public string $module = 'BillManagement';
    public string $controller = 'BillManagementHandler';


    public function getMemberDelayBill(array $post_data = [])
    {
        $default_data = [
            'start'      => 0,
            'limit'      => 100,
            'sort'       => 'OperateTime',
            'dir'        => 'asc',
            //            'StartTime'  => '2022-07-01',
            'billTypeId' => 23,
        ];
        //        MemberGuid:
        //        BillNumber:
        //        UserAccount:
        //        StartTime:
        //        EndTime:
        //        Meno:
        return $this->GetBillManagementList(array_merge($default_data, $post_data));
    }

    public function getMemberUpgradeReturnBill(array $post_data = [])
    {
        $default_data = [
            'start'      => 0,
            'limit'      => 100,
            'sort'       => 'OperateTime',
            'dir'        => 'asc',
            //            'StartTime'  => '2022-07-01',
            'billTypeId' => 21,
        ];
        //        MemberGuid:
        //        BillNumber:
        //        UserAccount:
        //        StartTime:
        //        EndTime:
        //        Meno:
        return $this->GetReturnBillList(array_merge($default_data, $post_data));
    }

    public function getMemberUpgradeBill(array $post_data = [])
    {
        $default_data = [
            'start'      => 0,
            'limit'      => 100,
            'sort'       => 'OperateTime',
            'dir'        => 'asc',
            //            'StartTime'  => '2022-07-01',
            'billTypeId' => 21,
        ];
        //        MemberGuid:
        //        BillNumber:
        //        UserAccount:
        //        StartTime:
        //        EndTime:
        //        Meno:
        return $this->GetBillManagementList(array_merge($default_data, $post_data));
    }

    /**
     * 获取退单单据
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetReturnBillList(array $post_data = [])
    {
        $default_data = [
            'start'      => 0,
            'limit'      => 20,
            'sort'       => 'Id',
            'dir'        => 'desc',
            'billTypeId' => '', //不能为空
        ];
        //        MemberGuid:
        //        BillNumber:
        //        UserAccount:
        //        StartTime:
        //        EndTime:
        //        Meno:
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetBillManagementList(array $post_data = [])
    {
        $default_data = [
            'start'      => 0,
            'limit'      => 20,
            'sort'       => 'Id',
            'dir'        => 'desc',
            //            'BillNumber' => '****************',
            'billTypeId' => '', //不能为空
        ];
        //        MemberGuid:
        //        BillNumber:
        //        UserAccount:
        //        StartTime:
        //        EndTime:
        //        Meno:
        $all_data = array_merge($default_data, $post_data);
        if (!empty($all_data['BillNumber'])) {
            unset($all_data['StartTime']);
        }
        return $this->post($all_data, __FUNCTION__);
    }
}