<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 回收站
 * @library Recycle
 * <AUTHOR>
 * @date 2022/01/16 20:24
 **/
class Recycle extends BasicYikayi
{
    public string $module = 'BillManagement';
    public string $controller = 'BillManagementHandler';

    /**
     * 获取
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetRecyclingList(array $post_data = [])
    {
        $default_data = [
            //            'start'      => 0,
            'start'      => random_int(12, 222) * 20,
            //            'limit'      => 20,
            //            'dir'        => 'asc',
            'limit'      => 20,
            'sort'       => 'Id',
            'dir'        => 'desc',
            'dataTypeId' => 2, // 1 会员 2 商品
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__, true);
    }

    /**
     * 还原数据
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function RcData(array $post_data = [])
    {
        $default_data     = [
            'Ids'        => '', //6e635dfb-d7d6-49c1-8456-dbfa4dc80e17,63227c3e-90a8-4fad-a4d7-cdbb87d23f12
            'dataTypeId' => 2, // 1 会员 2 商品
            //'Names' => '13919001439',
            //'Names' => '13873333033',
        ];
        $post_data        = array_merge($default_data, $post_data);
        $post_data['Ids'] = $post_data['Ids'] ?: $this->getIds($post_data['dataTypeId']);
        return $this->post($post_data, __FUNCTION__);
    }

    /**
     * 清空数据
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function DelData(array $post_data = [])
    {
        $default_data     = [
            'Ids'        => '', //6e635dfb-d7d6-49c1-8456-dbfa4dc80e17,63227c3e-90a8-4fad-a4d7-cdbb87d23f12
            'dataTypeId' => 1, // 1 会员 2 商品
            //'Names' => '13919001439',
            //'Names' => '13873333033',
        ];
        $post_data        = array_merge($default_data, $post_data);
        $post_data['Ids'] = $post_data['Ids'] ?: $this->getIds($post_data['dataTypeId']);
        return $this->post($post_data, __FUNCTION__, true);
    }

    /**
     * @param int $dataTypeId 类型
     * @return string
     * @throws Exception
     */
    protected function getIds(int $dataTypeId = 1)
    {
        $result = $this->GetRecyclingList(['dataTypeId' => $dataTypeId]);
        if ($result['data']['length'] == 0) {
            throw new Exception('没有要处理的数据');
        }
        return join(',', array_column($result['data'], 'Id'));
    }
}