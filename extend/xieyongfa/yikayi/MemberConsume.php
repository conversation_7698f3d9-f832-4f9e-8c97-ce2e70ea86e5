<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 会员
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class MemberConsume extends BasicYikayi
{
    public string $module = 'MemberConsume';
    public string $controller = 'MemberConsumeHandler';

    /**
     * 获取会员列表
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetMemberInfo(array $post_data = [])
    {
        //        {
        //            "success": true,
        //    "data": {
        //         "MemberGuid": "3cbda424-f4c0-e511-9cd1-0010186c5bc6",
        //        "CardId": "2988151622927083",
        //        "MotherCardId": "",
        //        "TrueName": "谢永发",
        //        "Mobile": "18603047034",
        //        "AllowModify": "false",
        //        "MemberGroupGuid": "2fd2baf8-75bb-e511-afca-0010186c5bc6",
        //        "MemberGroupId": "2fd2baf8-75bb-e511-afca-0010186c5bc6",
        //        "MemberGroupName": "默认级别",
        //        "BirthTime": "1991-08-23:0",
        //        "IsBirthday": false,
        //        "Birthday": "1991-08-23",
        //        "IsLunar": false,
        //        "EnablePoint": 12824.4,
        //        "EnableValue": 12.72,
        //        "UsedPoint": 187.51,
        //        "UsedValue": 12900.71,
        //        "DurationTime": "2099-12-31",
        //        "MaxValueForNoSecret": 999999,
        //        "MultiChildCardValue": 0
        //    },
        //    "cardid": "2988151622927083"
        //}

        $default_data = [
            'CardId'   => '',
            'FromType' => 'AddValue',
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }
}