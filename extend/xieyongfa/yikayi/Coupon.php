<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 优惠券
 * @library Coupon
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Coupon extends BasicYikayi
{
    public string $module = 'E-Coupon';
    public string $controller = 'ECouponHandler';

    /**
     * 获取会员列表
     * @param string $coupon_send_note_guid 优惠券发送记录GUID
     * @return bool|array
     * @throws Exception
     */
    public function CancelSendCoupon(string $coupon_send_note_guid)
    {
        $post_data = [
            'couponId' => $coupon_send_note_guid,
        ];
        return $this->post($post_data, __FUNCTION__);
    }
}