<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 会员
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Member extends BasicYikayi
{
    public string $module = 'MemberManage';
    public string $controller = 'MemberManageHandler';

    /**
     * 获取会员列表
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetMemberInfoListPaged(array $post_data = [])
    {
        $default_data = [
            'start'  => 0,
            'limit'  => 20,
            'sort'   => 'RegisterTime',
            'dir'    => 'desc',
            'filter' => ''
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }
}