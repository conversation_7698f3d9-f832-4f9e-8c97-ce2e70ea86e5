<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;

use xieyongfa\yikayi\Contracts\DataArray;
use xieyongfa\yikayi\Exceptions\InvalidInstanceException;

/**
 * 加载缓存器
 * @library Yky
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Member Member($options) static 会员
 * @method Coupon Coupon($options) static 优惠券
 * @method MemberConsume MemberConsume($options) static 会员消费
 * @method MemberValue MemberValue($options) static 会员充值
 * @method User User($options) static 用户
 * @method Bill Bill($options) static 单据
 * @method Store Store($options) static 门店
 * @method Recycle Recycle($options) static 回收站
 **/
class Yikayi
{

    /**
     * 定义当前版本
     * @var string
     */
    const VERSION = '1.0.1';

    /**
     * 静态配置
     * @var DataArray
     */
    private static DataArray $config;

    /**
     * 设置及获取参数
     * @param array $option
     * @return array
     */
    public static function config($option = null): array
    {
        if (is_array($option)) {
            self::$config = new DataArray($option);
        }
        if (self::$config instanceof DataArray) {
            return self::$config->get();
        }
        return [];
    }

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function __callStatic(string $name, array $arguments)
    {
        $class = __NAMESPACE__ . '\\' . $name;
        if (!class_exists($class)) {
            throw new InvalidInstanceException('Class {$class} not found');
        }
        $option = array_shift($arguments);
        $config = is_array($option) ? $option : self::$config->get();
        return new $class($config);
    }

    /**
     * 静态魔术加载方法
     * @param array $config 配置
     * @param string $class 类名
     * @param string $action 方法名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function call(array $config, string $class, string $action, array $arguments)
    {
        $obj = self::__callStatic($class, [$config]);
        return call_user_func_array([$obj, $action], $arguments);
    }
}