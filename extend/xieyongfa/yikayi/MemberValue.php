<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 会员
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class MemberValue extends BasicYikayi
{
    public string $module = 'MemberValue';
    public string $controller = 'MemberValueHandler';

    /**
     * 获取
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetRenderAddValuePage(array $post_data = [])
    {
        // {
        // "success": true,
        //    "valueAddMode": 1,
        //    "memberInfo": {
        //        "CardId": "2988151622927083",
        //        "TrueName": "谢永发",
        //        "Mobile": "18603047034",
        //        "MemberGuid": "3cbda424-f4c0-e511-9cd1-0010186c5bc6",
        //        "MemberGroupGuid": "2fd2baf8-75bb-e511-afca-0010186c5bc6",
        //        "BirthTime": "1991-08-23:0",
        //        "MemberGroupName": "默认级别",
        //        "DurationTime": "永久有效",
        //        "AvailablePoint": 12824.4,
        //        "AvailableValue": 12.72,
        //        "ImagePath": "https://files.1card1.cnhttp://wx.qlogo.cn/mmopen/uHwLXtyH4IXxAoviaru8ESQ2LIyJWFiaA2E8RXeQqMsXFQTwdNagymn7icbhYffpH03fm00yMacMGQMicARumE0xJg/0",
        //        "OliTypeName": ""
        //    },
        //    "rules": [
        //        {
        //            "ActivityGuid": "3c1af643-61f9-11e9-995b-20040fec85e4",
        //            "RuleGuid": "e35149ba-8994-11e8-9331-0010184dbd22",
        //            "ValueMin": 1,
        //            "ValueMax": 1,
        //            "IsRule": 1,
        //            "IsOld": true,
        //            "ReceiveModel": 0,
        //            "GiftValue": 100,
        //            "GiftPoint": 0,
        //            "GiftModel": 1,
        //            "Description": "{\"GiftValueDesc\":\"赠送100元;\",\"GiftPointDesc\":\"\",\"GiftCouponDesc\":null,\"GiftCoupons\":null,\"GiftLuckyDesc\":\"\",\"GiftCount\":null,\"GiftCountDesc\":\"\"}",
        //            "PriorityLevel": 0,
        //            "TotalMemberLimit": 2147483647,
        //            "OneMemberLimit": 2147483647,
        //            "isOpenTimeLimit": 0,
        //            "GiftType": 0,
        //            "IsForSelete": true,
        //            "IsReceived": false,
        //            "IsForMatch": false
        //        },
        //        {
        //            "ActivityGuid": "3c1af643-61f9-11e9-995b-20040fec85e4",
        //            "RuleGuid": "c33e2509-8aef-11e8-9331-0010184dbd22",
        //            "ValueMin": 2000,
        //            "ValueMax": 2000,
        //            "IsRule": 1,
        //            "IsOld": true,
        //            "ReceiveModel": 0,
        //            "GiftValue": 1,
        //            "GiftPoint": 0,
        //            "GiftModel": 1,
        //            "Description": "{\"GiftValueDesc\":\"赠送1元;\",\"GiftPointDesc\":\"\",\"GiftCouponDesc\":null,\"GiftCoupons\":null,\"GiftLuckyDesc\":\"\",\"GiftCount\":null,\"GiftCountDesc\":\"\"}",
        //            "PriorityLevel": 0,
        //            "TotalMemberLimit": 2147483647,
        //            "OneMemberLimit": 2147483647,
        //            "isOpenTimeLimit": 0,
        //            "GiftType": 0,
        //            "IsForSelete": true,
        //            "IsReceived": false,
        //            "IsForMatch": false
        //        }
        //    ],
        //下方字段可能是null 说明读卡后不会弹出来
        //    "oilCardTypeList": [
        //        {
        //            "Guid": "3c0fd9fc-a66e-11e8-9331-0010184dbd22",
        //            "GsName": "汽油"
        //        }
        //    ]
        //}
        $default_data = [
            'MemberGuid'    => '',
            'goodsItemGuid' => '',
            //'goodsItemGuid' => '3c2b33e8-a66e-11e8-9331-0010184dbd22',
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }

    /**
     * 获取
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetOilCardInfo(array $post_data = [])
    {

        // {
        //    "success": true,
        //    "valueAddMode": 1,
        //    "memberInfo": {
        //        "CardId": "2988151622927083_1",
        //        "TrueName": "谢永发",
        //        "Mobile": "",
        //        "MemberGuid": "95c1e4b8-ab3e-11eb-b9b6-20040fec85e4",
        //        "MemberGroupGuid": "99ed1c02-1489-11e9-917f-0010184dbd22",
        //        "BirthTime": null,
        //        "MemberGroupName": "中石化",
        //        "DurationTime": "永久有效",
        //        "AvailablePoint": 0,
        //        "AvailableValue": 0,
        //        "ImagePath": "https://files.1card1.cnhttp://wx.qlogo.cn/mmopen/uHwLXtyH4IXxAoviaru8ESQ2LIyJWFiaA2E8RXeQqMsXFQTwdNagymn7icbhYffpH03fm00yMacMGQMicARumE0xJg/0",
        //        "OliTypeName": "汽油"
        //    },
        //    "rules": []
        //}
        $default_data = [
            'guid'       => '', //油卡guid
            'memberGuid' => '',
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }

}