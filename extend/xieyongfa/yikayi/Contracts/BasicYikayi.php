<?php
declare (strict_types=1);

namespace xieyongfa\yikayi\Contracts;

use Exception;

class BasicYikayi
{
    /**
     * 接口基础地址
     */
    const BASE_URL = 'https://server.1card1.cn';
    const LOGIN_URL = 'https://www.1card1.cn/Login.aspx?IsCheckVersion=False';

    /**
     * 账号
     */
    public string $account;

    /**
     * 工号
     */
    public string $user_account;

    /**
     * 密码
     */
    public string $password;

    /**
     * module
     */
    public string $module;
    /**
     * controller
     */
    public string $controller;

    /**
     * status
     */
    public int $status = -1;

    /**
     * message
     */
    public string $message;

    /**
     * @var DataArray
     */
    public DataArray $config;

    /**
     * 当前请求方法参数
     * @var array
     */
    protected array $currentMethod = [];

    /**
     * 当前是否已经重试过
     * @var bool
     */
    protected bool $isTry = false;

    /**
     * @param array $options
     * @throws Exception
     */
    public function __construct(array $options)
    {
        $this->config = new DataArray($options);
        if (!$this->config->offsetExists('account') || !$this->config->offsetExists('user_account') || !$this->config->offsetExists('password')) {
            throw new Exception('商家账号工号密码不能为空!');
        }
        $this->account      = $options['account'];
        $this->user_account = $options['user_account'] ?: '10000';
        $this->password     = $options['password'];
    }

    /**
     * 动态调用
     * @param string $method
     * @param array $arguments
     * @return mixed
     * @throws Exception
     */
    public function __call(string $method, array $arguments = [])
    {
        return $this->post(array_shift($arguments), $method);
    }

    /**
     * 获取post结果
     * @param array $post_data 数据包
     * @param string $action 方法名
     * @param bool $ignore_log 不记录日志
     * @return bool|array
     * @throws Exception
     */
    protected function post(array $post_data, string $action, $ignore_log = false)
    {
        return $this->request($post_data, $action, $ignore_log, 'POST');
    }

    /**
     * 获取get结果
     * @param array $post_data 数据包
     * @param string $action 方法名
     * @param bool $ignore_log 不记录日志
     * @return bool|array
     * @throws Exception
     */
    protected function get(array $post_data, string $action, $ignore_log = false)
    {
        return $this->request($post_data, $action, $ignore_log, 'GET');
    }

    /**
     * 获取post结果
     * @param array $post_data 数据包
     * @param string $action 方法名
     * @param bool $ignore_log 不记录日志
     * @param string $method 请求类型 GET或者POST
     * @return bool|array
     * @throws Exception
     */
    protected function request(array $post_data, string $action, $ignore_log = false, $method = 'POST')
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $method = strtolower($method);
        $url    = self::BASE_URL . '/Module/' . $this->module . '/' . $this->controller . '.ashx?action=' . $action;
        $cookie = $this->get_login_cookie();
        $result = curl()->ignore_log($ignore_log)->set_header(['Cookie' => $cookie])->form_params()->$method($url, $post_data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 获取登录cookies缓存key
     * @return bool|array
     * @throws Exception
     */
    protected function get_login_cookie_key()
    {
        return 'yikayi_login_cookie:' . $this->account . ':' . $this->user_account . ':' . md5($this->password);
    }

    /**
     * 清空登录缓存cookies
     * @return bool|array
     * @throws Exception
     */
    protected function clear_login_cookie()
    {
        cache($this->get_login_cookie_key(), null);
        return true;
    }

    /**
     * 获取登录cookies
     * @return bool|array
     * @throws Exception
     */
    protected function get_login_cookie()
    {
        $cache_key     = $this->get_login_cookie_key();
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($cache_key . __FUNCTION__);
        if ($cache = cache($cache_key)) {
            $lock_instance->unlock($lock);
            return $cache;
        }
        $result                    = curl()->get(self::LOGIN_URL);
        $body                      = $result->get_body();
        $params                    = tools()::get_names_and_values($body);
        $separator                 = '%1D';
        $params['__CALLBACKPARAM'] = 'LOGIN' . $separator . '0' . $separator . $this->account . $separator . $this->user_account . $separator . md5($this->password) . $separator . 'false' . $separator;
        $params['__CALLBACKPARAM'] = urldecode($params['__CALLBACKPARAM']);
        $params['__CALLBACKID']    = '__page';
        unset($params['hidServerTime']);
        unset($params['hidUpdateText']);
        unset($params['GetCheckCode']);
        unset($params['txtPhone']);
        $result       = curl()->set_header()->form_params()->set_cookies($result->get_cookies())->post(self::LOGIN_URL, $params);
        $cookie       = $result->get_cookies();
        $body         = $result->get_body();
        $array        = explode('|', $body);
        $array        = explode(urldecode($separator), $array[1]);
        $login_result = $array[0];
        if ($login_result == 'false') {
            $lock_instance->unlock($lock);
            throw new Exception($array[1] ?? '登录失败');
        } elseif ($login_result == 'redirect') {
            $result = curl()->set_cookies($cookie)->set_allow_redirects(false)->get($array[1]);
            $body   = $result->get_body();
            if (strpos($body, 'application.html') === false) {
                $lock_instance->unlock($lock);
                throw new Exception('模拟登录失败,结果中未找到application.html!');
            }
            $login_success_cookie = $result->get_cookies();
            cache($cache_key, $login_success_cookie, 3600);
            $lock_instance->unlock($lock);
            return $login_success_cookie;
            //$url = self::BASE_URL . '/application.html?random=' . tools()::get_bill_number();
            //curl()->set_cookies($login_success_cookie)->get($url);
        } else {
            $lock_instance->unlock($lock);
            throw new Exception('未知登录类型:' . $login_result);
        }
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @return bool|array
     * @throws Exception
     */
    protected function retry()
    {
        $this->isTry = true;
        $this->clear_login_cookie();
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @param mixed $result
     * @return bool|array
     * @throws Exception
     */
    protected function _parseResult($result)
    {
        $login_fail_list = ['强制下线', '重新登录', '会话超时'];
        if (is_array($result) && isset($result['success']) && $result['success'] == false && isset($result['message']) && tools()::match_keyword_in_array($result['message'], $login_fail_list)) {
            if (isset($this->currentMethod['method']) && empty($this->isTry)) {
                return $this->retry();
            }
            $this->status  = -1;
            $this->message = '解析返回结果失败';
            return false;
        }
        return $result;
    }
}