<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 用户
 * @library 用户
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class User extends BasicYikayi
{
    public string $module = 'User';
    public string $controller = 'UserHandler';

    /**
     * 获取用户信息JS
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetUserInfoJS(array $post_data = [])
    {
        return $this->get([], __FUNCTION__);
    }
}