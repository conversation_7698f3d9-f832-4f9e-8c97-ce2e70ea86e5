<?php
declare (strict_types=1);


namespace xieyongfa\yikayi;


use Exception;
use xieyongfa\yikayi\Contracts\BasicYikayi;

/**
 * 门店
 * @library Store
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method GetChainStoreComboPaged($post_data) 搜索门店
 **/
class Store extends BasicYikayi
{
    public string $module = 'ChainStore';
    public string $controller = 'ChainStoreHandler';

    /**
     * 获取门店列表
     * @param array $post_data 数据包
     * @return bool|array
     * @throws Exception
     */
    public function GetChainStorePaged(array $post_data = [])
    {
        $default_data = [
            'start'  => 0,
            'limit'  => 25,
            'filter' => ''
        ];
        return $this->post(array_merge($default_data, $post_data), __FUNCTION__);
    }
}