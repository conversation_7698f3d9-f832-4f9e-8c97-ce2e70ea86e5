<?php

declare (strict_types=1);

namespace xieyongfa\log;

use app\model\ApiLog;
use Exception;
use think\App;
use think\contract\LogHandlerInterface;
use think\event\LogRecord;

class Db implements LogHandlerInterface
{

    /**
     * 配置参数
     * @var array
     */
    protected array $config = [
        'time_format' => 'c',
        'json'        => true,
        'debug'       => false,
    ];

    /**
     * Sls constructor.
     *
     * @param App $app
     * @param array $config
     */
    public function __construct(App $app, array $config = [])
    {
        if (is_array($config)) {
            $this->config = array_merge($this->config, $config);
        }
        if (!isset($config['debug'])) {
            $this->config['debug'] = $app->isDebug();
        }
    }

    /**
     * @inheritDoc
     * @param array<LogRecord> $log
     * @throws Exception
     */
    public function save(array $log): bool
    {
        if (empty($log)) {
            return false;
        }
        return $this->write($log);
    }


    /**
     * 日志写入
     * @access protected
     * @param array<LogRecord> $message 日志信息
     * @return bool
     */
    protected function write(array $message): bool
    {
        try {
            foreach ($message as $record) {
                $type = $record->type;
                $msg  = $record->message;
                switch ($type) {
                    case 'api_log':
                        $db = new ApiLog();
                        $db->save($msg);
                        break;
                    default:
                }
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}