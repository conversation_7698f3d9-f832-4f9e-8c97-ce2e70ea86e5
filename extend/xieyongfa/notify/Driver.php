<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace xieyongfa\notify;

use app\model\NotifyRecord;
use think\App;


abstract class Driver
{
    /** @var App */
    protected App $app;

    /**
     * 驱动句柄
     * @var object
     */
    protected $handler = null;
    /**
     * The connector name for the notify.
     *
     * @var string
     */
    protected string $connection;
    /**
     * The connector config for the connector.
     *
     * @var array
     */
    protected array $config = [];

    /**
     * The param
     *
     * @var array
     */
    public array $param = [];

    /**
     * 模板
     * @var string|array
     */
    public string|array $template;
    public int $template_id;
    public string $provider;
    public string $key_name;
    public array $data = [];
    public array $map = [];
    public string|null $bid = null;
    public array|string|null $user_guid = null;
    public string|null $member_guid = null;
    public string|null $member_mobile = null;

    abstract public function send($message, $to_user = '');

    abstract public function getSendData(): array;

    abstract public function getSendHandler();

    abstract public function getReceiver(): array;

    abstract public function notify();

    /**
     * 从模板中替换变量
     * @return string
     */
    public function replace_variable_from_template(): string
    {
        $template = $this->template;
        $data     = $this->getSendData();
        foreach ($data as $key => $val) {
            if (is_array($val)) {
                //数组则不替换
                continue;
            }
            $val = tools()::remove_empty_string($val, false);
            //双引号替换成单引号,否则微信模板消息json不合法
            $val      = str_replace('"', "'", $val);
            $template = str_replace('{$' . $key . '}', $val, $template);
        }
        return $template;
    }

    /**
     * 设置通知数据
     * @param array $data 发送的数据
     * @return Driver
     */
    public function setData(array $data = []): Driver
    {
        if (!empty($data)) {
            if (!empty($data['bid']) && empty($this->bid)) {
                $this->bid = $data['bid'];
            }
            if (!empty($data['user_guid']) && empty($this->user_guid)) {
                $this->user_guid = $data['user_guid'];
            }
            if (!empty($data['member_guid']) && empty($this->member_guid)) {
                $this->member_guid = $data['member_guid'];
            }
            $this->data = $data;
        }
        return $this;
    }

    /**
     * 设置通知类型
     * @param string $provider 通知类型 system 系统平台通知 business 商户通知 member 会员通知
     * @return Driver
     */
    public function setProvider(string $provider): Driver
    {
        $this->provider = $provider;
        return $this;
    }

    /**
     * 设置模板键值
     * @param string $key_name 模板键值
     * @return Driver
     */
    public function setKeyName(string $key_name): Driver
    {
        $this->key_name = $key_name;
        return $this;
    }

    /**
     * 设置查询条件,一般用于通过某些表的主键来构造发送内容
     * @param array $map 查询条件
     * @return Driver
     */
    public function setMap(array $map = []): Driver
    {
        if (!empty($map)) {
            $this->map = $map;
        }
        return $this;
    }

    /**
     * 扩展参数
     * @param array $param 扩展参数
     * @return Driver
     */
    public function setParam(array $param = []): Driver
    {
        $this->param = $param;
        return $this;
    }

    /**
     * 设置模板内容
     * @param int $template_id 模板ID
     * @return Driver
     */
    public function setTemplateId(int $template_id): Driver
    {
        $this->template_id = $template_id;
        return $this;
    }

    /**
     * 设置模板内容
     * @param array|string $template 模板
     * @return Driver
     */
    public function setTemplate(array|string $template): Driver
    {
        $this->template = $template;
        return $this;
    }


    /**
     * 设置商家标识
     * @param string|null $bid 商家标识
     * @return Driver
     */
    public function setBid(string|null $bid = null): Driver
    {
        $this->bid = $bid;
        return $this;
    }

    public function check_to_user_list_num($to_user_list)
    {
        if (count($to_user_list) > 100) {
            throw new \Exception('发送对象数量太多');
        }
    }

    public function record($data): bool
    {
        $status           = $data['status'] ?? 1;
        $result           = $data['result'] ?? '';
        $to_user          = $data['to_user'] ?? '';
        $content          = $data['content'] ?? '';
        $db_notify_record = new NotifyRecord();
        $insert_data      = [
            'notify_template_id' => $this->template_id,
            'bid'                => $this->bid,
            'member_guid'        => $this->member_guid,
            'user_guid'          => is_string($this->user_guid) ? $this->user_guid : '',
            'content'            => $content,
            'to_user'            => $to_user,
            'status'             => $status,
            'result'             => $result,
        ];
        $db_notify_record->save($insert_data);
        return true;
    }

    /**
     * 设置工号标识
     * @param array|string|null $user_guid 工号标识
     * @return Driver
     */
    public function setUserGuid(array|string|null $user_guid = null): Driver
    {
        $this->user_guid = $user_guid;
        return $this;
    }

    /**
     * 设置会员标识
     * @param string|null $member_guid 工号标识
     * @return Driver
     */
    public function setMemberGuid(string|null $member_guid = null): Driver
    {
        $this->member_guid = $member_guid;
        return $this;
    }

    /**
     * 设置指定手机号
     * @param string|null $member_mobile 指定手机号
     * @return Driver
     */
    public function setMemberMobile(string|null $member_mobile = null): Driver
    {
        $this->member_mobile = $member_mobile;
        return $this;
    }

    public function setApp(App $app): Driver
    {
        $this->app = $app;
        return $this;
    }

    /**
     * Get the connector name for the queue.
     *
     * @return string
     */
    public function getConnection(): string
    {
        return $this->connection;
    }

    /**
     * Set the connector name for the queue.
     *
     * @param string $name
     * @return $this
     */
    public function setConnection(string $name): Driver
    {
        $this->connection = $name;

        return $this;
    }

    /**
     * Get the connector config
     * @param string|null $key
     * @param string|null $default
     * @return array|string|null
     */
    public function getConfig(string $key = null, ?string $default = ''): array|string|null
    {
        if (is_null($key)) {
            return $this->config;
        }

        return $this->config[$key] ?? $default;
    }

    /**
     * Set the connector config
     *
     * @param array $config
     * @return $this
     */
    public function setConfig(array $config): Driver
    {
        $this->config = $config;

        return $this;
    }
}
