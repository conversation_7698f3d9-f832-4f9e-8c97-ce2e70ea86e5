<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\common\exceptions\NotNotifyException;
use app\model\AppidOpenidMapping;
use app\model\Business;
use app\model\Member;
use app\model\UserBindNote;
use app\model\WechatSubscribeTemplateList;
use app\model\WechatConfig;
use Exception;
use WeChat\Newtmpl;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;
use xieyongfa\yky\Yky;

class WechatSubscribe extends Driver
{
    use Tools;

    public function __construct($config)
    {
        $this->handler = weixin($config['appid'])::WeChatNewtmpl();
    }

    protected $wechat_appid = '';
    protected $openid_appid_list = [];

    public function getSendData(): array
    {
        return $this->data;
    }

    public function getSendHandlerSystem(): Newtmpl|bool
    {
        $this->wechat_appid = get_system_config('platform_wechat_appid');
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatNewtmpl();
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): Newtmpl|bool
    {
        $bid              = $bid ?: $this->bid;
        $db_business      = new Business();
        $map              = [
            ['guid', '=', $bid]
        ];
        $business_info    = $db_business->where($map)->findOrFail();
        $parent_guid      = $business_info['parent_guid'];
        $parent_user_guid = $business_info['parent_user_guid'];
        if (empty($parent_guid) || empty($parent_user_guid)) {
            return false;
        }
        return $this->getSendHandlerBusiness($parent_guid, $parent_user_guid);
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): Newtmpl|bool
    {
        $bid               = $bid ?: $this->bid;
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['way', '=', 1], // 公众号关联的才能发消息  2 是小程序
        ];
        if (!empty($user_guid)) {
            $map[] = ['user_guid', '=', $user_guid];
        }
        $openid_appid_list = $db_user_bind_note->where($map)->order(['create_time' => 'DESC'])->field(['appid', 'openid'])->select();
        if ($openid_appid_list->isEmpty()) {
            return false;
        }
        foreach ($openid_appid_list as $key => $val) {
            if (!$this->wechat_appid) {
                $this->wechat_appid = $val['appid'];
            }
            $this->openid_appid_list[$val['openid']] = $val['appid'];
        }
        return weixin($this->wechat_appid)::WeChatNewtmpl();
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): Newtmpl|bool
    {
        $bid                = $bid ?: $this->bid;
        $config             = get_config_by_bid($bid);
        $this->wechat_appid = $config['appid'];
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatNewtmpl();
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): Newtmpl|bool
    {
        $bid                = $bid ?: $this->bid;
        $config             = get_config_by_bid($bid);
        $this->wechat_appid = $config['appid'];
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatNewtmpl();
    }


    public function getSendHandler(): Newtmpl|bool
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        return [get_system_config('platform_wechat_openid')];
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid              = $bid ?: $this->bid;
        $db_business      = new Business();
        $map              = [
            ['guid', '=', $bid]
        ];
        $business_info    = $db_business->where($map)->findOrFail();
        $parent_guid      = $business_info['parent_guid'];
        $parent_user_guid = $business_info['parent_user_guid'];
        if (empty($parent_guid) || empty($parent_user_guid)) {
            return [];
        }
        return $this->getReceiverBusiness($parent_guid, $parent_user_guid);
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid       = $bid ?: $this->bid;
        $user_guid = $user_guid ?: $this->user_guid;

        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForWechat未指定商家标识');
        }
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['way', '=', 1],
        ];
        if (!empty($user_guid)) {
            if (is_string($user_guid)) {
                $map[] = ['user_guid', '=', $user_guid];
            } elseif (is_array($user_guid)) {
                $map[] = ['user_guid', 'IN', $user_guid];
            }
        }

        return $db_user_bind_note->where($map)->column('openid');
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        $config = get_config_by_bid($bid);

        $yky_member  = Yky::Member($config);
        $member_info = $yky_member->Get_MemberInfo($member_guid);
        if (empty($member_info['data'])) {
            return [];
        }
        return [$member_info['data'][0]['ThirdOpenId']];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        $db_member = new Member();
        $map       = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
            ['from', 'NOT IN', [6, 8]],  //排除掉钉钉和企业微信
        ];

        $member_info = $db_member->where($map)->field(['appid', 'openid'])->find();
        if (empty($member_info['openid'])) {
            return [];
        }
        $appid            = $member_info['appid'];
        $openid           = $member_info['openid'];
        $db_wechat_config = new WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['type']);
        if ($info['type'] == 1) {
            return [$openid];
        }
        // 不是公众号尝试找映射关系
        $db_appid_openid_mapping = new AppidOpenidMapping();
        $map                     = [
            ['appid', '=', $appid],
            ['openid', '=', $openid],
            //            ['appid_type', '=', 2], //小程序
            ['relation_appid_type', '=', 1], //关联类型公众号
        ];
        $relation_openid         = $db_appid_openid_mapping->where($map)->value('relation_openid');
        return $relation_openid ? [$relation_openid] : [];
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    protected function get_admin_weapp($appid)
    {
        //小程序appid => 公众号 appid 数组
        $list = [
            'wxae45934daf093dbf' => ['wx07ce368b922c9841']
        ];
        foreach ($list as $key => $val) {
            if (in_array($appid, $val)) {
                return $key;
            }
        }
        return '';
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        if (empty($send_handler)) {
            return true;
        }
        $to_user_list = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        $param                   = $this->param;
        $tid                     = $param['tid']; //模板编号 4354543 这样
        $kid                     = $param['kid']; //关键词列表  1,2,3
        $after_replace_send_data = str_replace("\\r\\n", '#', $after_replace_send_data); //微信 2023年5月起不支持换行符了 替换成#
        $after_replace_send_data = str_replace("\\", '|', $after_replace_send_data); // \ 反斜杠会导致json无法转数组
        $send_data_array         = json_decode($after_replace_send_data, true);
        if (!tools()::is_json($after_replace_send_data)) {
            throw new Exception('可能模板内容不是合法的json:' . $after_replace_send_data);
        }
        foreach ($to_user_list as $to_user) {
            try {
                $appid = $this->wechat_appid;
                if (!empty($this->openid_appid_list) && !empty($this->openid_appid_list[$to_user])) {
                    //为了兼容一个商户同时用多个公众号绑定的时候能准确的使用公众号发送
                    $appid        = $this->openid_appid_list[$to_user];
                    $send_handler = weixin($appid)::WeChatNewtmpl();
                }
                //替换小程序appid
                if (strpos($after_replace_send_data, '{$ADMIN_WEAPP}') !== false) {
                    //存在小程序APPID 则走替换流程
                    $admin_weapp                         = $this->get_admin_weapp($appid);
                    $after_replace_admin_weapp_send_data = str_replace('{$ADMIN_WEAPP}', $admin_weapp, $after_replace_send_data);
                    $send_data_array                     = json_decode($after_replace_admin_weapp_send_data, true);
                }
                if (empty($send_data_array['miniprogram']['appid'])) {
                    $send_data_array['miniprogram']['pagepath'] = '';
                    unset($send_data_array['miniprogram']);
                }
                $send_data_array                   = $this->filter($send_data_array);
                $db_wechat_subscribe_template_list = new WechatSubscribeTemplateList();
                $send_data_array['touser']         = $to_user;
                $record_data                       = [
                    'to_user' => $to_user,
                    'content' => json_encode($send_data_array, JSON_UNESCAPED_UNICODE),
                ];
                $template_id                       = $db_wechat_subscribe_template_list->get_template_id_by_tid_and_kid($appid, $tid, $kid); //获取模板id
                if (empty($template_id)) {
                    throw new NotNotifyException('appid:' . $appid . '订阅消息模板id' . $tid . '获取失败');
                }
                $send_data_array['template_id'] = $template_id;
                $result                         = $send_handler->send($send_data_array);
            } catch (Exception $e) {
                $msg = '模板消息发送失败,错误码:' . $e->getCode() . '原因:' . $e->getMessage();
                if (!in_array($e->getCode(), [43004])) {
                    //模板消息发送失败,错误码:43004原因:require subscribe rid: 671c5348-6cf94bbb-50aafe87
                    wr_log($msg);
                }
                $record_data['status'] = -1;
                $record_data['result'] = $msg;
            }
            $this->record($record_data);
        }
        return true;
    }

    protected function filter(array $send_data_array)
    {
        if (empty($send_data_array['data'])) {
            throw new \Exception('发送数据中不包含data字段');
        }
        foreach ($send_data_array['data'] as $key => $val) {
            $field_type = preg_replace('/\d+/', '', $key); // character_string10 处理成 character_string
            $value      = $val['value'];
            switch ($field_type) {
                //
                //参数类别	参数说明	参数值限制	说明
                //thing.DATA	事物	20个以内字符	可汉字、数字、字母或符号组合
                //number.DATA	数字	32位以内数字	只能数字，可带小数
                //letter.DATA	字母	32位以内字母	只能字母
                //symbol.DATA	符号	5位以内符号	只能符号
                //character_string.DATA	字符串	32位以内数字、字母或符号	可数字、字母或符号组合
                //time.DATA	时间	24小时制时间格式（支持+年月日），支持填时间段，两个时间点之间用“~”符号连接	例如：15:01，或：2019年10月1日 15:01
                //date.DATA	日期	年月日格式（支持+24小时制时间），支持填时间段，两个时间点之间用“~”符号连接	例如：2019年10月1日，或：2019年10月1日 15:01
                //amount.DATA	金额	1个币种符号+10位以内纯数字，可带小数，结尾可带“元”	可带小数
                //phone_number.DATA	电话	17位以内，数字、符号	电话号码，例：+86-0766-66888866
                //car_number.DATA	车牌	8位以内，第一位与最后一位可为汉字，其余为字母或数字	车牌号码：粤A8Z888挂
                //name.DATA	姓名	10个以内纯汉字或20个以内纯字母或符号	中文名10个汉字内；纯英文名20个字母内；中文和字母混合按中文名算，10个字内
                //phrase.DATA	汉字	5个以内汉字	5个以内纯汉字，例如：配送中
                //符号表示除中文、英文、数字外的常见符号，不能带有换行等控制字符。 时间格式支持HH:MM:SS或者HH:MM。 日期包含年月日，为y年m月d日，y年m月、m月d日格式，或者用‘-’、‘/’、‘.’符号连接，如2018-01-01，2018/01/01，2018.01.01，2018-01，01-01。 每个模板参数都会以类型为前缀，例如第一个数字模板参数为number01.DATA，第二个为number02.DATA
                case 'thing': //事物	20个以内字符	可汉字、数字、字母或符号组合
                    if (empty($value)) {
                        $value = '.';
                    }
                    $value = mb_substr($value, 0, 20);
                    break;
                case 'character_string': //字符串	32位以内数字、字母或符号	可数字、字母或符号组合
                    if (empty($value)) {
                        $value = '.';
                    }
                    $value = mb_substr($value, 0, 32);
                    break;
                case 'phrase':  //phrase.DATA	汉字	5个以内汉字	5个以内纯汉字，例如：配送中
                    $value = mb_substr($value, 0, 5);
                    break;
                default:
                    break;
            }
            $send_data_array['data'][$key]['value'] = $value;
        }
        return $send_data_array;
    }

    public function send($message, $to_user = '')
    {
        return true;
    }
}
