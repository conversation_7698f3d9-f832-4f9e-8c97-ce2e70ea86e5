<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\common\service\QyWeixinMessageService;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class WorkWeixin extends Driver
{
    use Tools;

    public function __construct($config)
    {
        $this->handler = QyWeixinMessageService::get_instance();
    }

    public function getSendData(): array
    {
        return $this->data;
    }


    public function getSendHandlerSystem(): QyWeixinMessageService
    {
        return QyWeixinMessageService::get_instance();
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): QyWeixinMessageService
    {
        $bid = $bid ?: $this->bid;
        return QyWeixinMessageService::get_instance();
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): QyWeixinMessageService
    {
        $bid = $bid ?: $this->bid;
        return QyWeixinMessageService::get_instance();
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): QyWeixinMessageService
    {
        $bid = $bid ?: $this->bid;
        return QyWeixinMessageService::get_instance();
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): QyWeixinMessageService|bool
    {
        return false;
    }

    public function getSendHandler(): QyWeixinMessageService
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        //系统通知接收者暂时写死
        return [get_system_config('default_touser')];
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }


    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            try {
                $content_arr = tools()::mb_str_split($after_replace_send_data, 2000);
                foreach ($content_arr as $key => $val) {
                    if (!$val) {
                        continue;
                    }
                    $send_handler->touser($to_user)->text($val)->send();
                }
                return true;
            } catch (Exception $e) {
                return false;
            }
        }
    }

    public function send($message, $to_user = '')
    {
        if (empty($message)) {
            return false;
        }
        try {
            $content_arr = tools()::mb_str_split($message, 2000);
            foreach ($content_arr as $key => $val) {
                if (!$val) {
                    continue;
                }
                $this->handler->touser($to_user)->text($val)->send();
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
