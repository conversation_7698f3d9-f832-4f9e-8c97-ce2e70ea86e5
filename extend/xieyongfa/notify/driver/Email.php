<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use Exception;
use mailer\facade\Mailer;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Email extends Driver
{
    use Tools;

    public function __construct($config)
    {
        $this->handler = Mailer::getFacadeClass();
    }

    public static function __make($config)
    {
        return new self($config);
    }

    public function get_send_handler()
    {
        return $this->handler;
    }

    public function get_to_user_list(): array
    {
        return ['<EMAIL>'];
    }


    public function getSendData(): array
    {
        return $this->data;
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): Mailer|bool
    {
        return false;
    }

    public function getSendHandlerSystem(): Mailer
    {
        return $this->handler;
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): Mailer
    {
        $bid = $bid ?: $this->bid;
        return $this->handler;
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): Mailer
    {
        $bid = $bid ?: $this->bid;
        return $this->handler;
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): Mailer
    {
        $bid = $bid ?: $this->bid;
        return $this->handler;
    }


    public function getSendHandler(): Mailer
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        //系统通知接收者暂时写死
        return ['<EMAIL>'];
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            $result = $send_handler->to($to_user)->subject('【新的消息】')->text($after_replace_send_data)->send();
        }
    }

    public function send($message, $to_user = '')
    {
        try {
            $mailer = $this->handler;
            $result = $mailer->to($to_user ?: $this->getConfig('to'))->subject('【新的消息】')->text($message)->send();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
