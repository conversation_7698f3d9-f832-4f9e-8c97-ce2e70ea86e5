<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\model\WebsocketUser;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Websocket extends Driver
{
    use Tools;

    public string $channel = 'websocket';

    public function __construct($config)
    {
        $this->handler = tools();
    }

    public function getSendData(): array
    {
        return $this->data;
    }

    public function getSendHandlerSystem(): WebsocketUser
    {
        return new WebsocketUser();
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): WebsocketUser
    {
        $bid = $bid ?: $this->bid;
        return new WebsocketUser();
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): WebsocketUser
    {
        $bid = $bid ?: $this->bid;
        return new WebsocketUser();
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): WebsocketUser
    {
        $bid = $bid ?: $this->bid;
        return new WebsocketUser();
    }


    public function getSendHandler(): WebsocketUser
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        return [];
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): WebsocketUser|bool
    {
        return false;
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid       = $bid ?: $this->bid;
        $user_guid = $user_guid ?: $this->user_guid;
        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForWebsocket 未指定商家标识');
        }
        $db_websocket_user = new WebsocketUser();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['channel', '=', $this->channel],
        ];
        if ($user_guid) {
            if (is_string($user_guid)) {
                $map[] = ['user_guid', '=', $user_guid];
            } elseif (is_array($user_guid)) {
                $map[] = ['user_guid', 'IN', $user_guid];
            }
        }
        return $db_websocket_user->where($map)->column('uid');
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForWebsocket 未指定会员标识');
        }
        $db_websocket_user = new WebsocketUser();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['channel', '=', $this->channel],
        ];
        if (!empty($member_guid)) {
            return [];
        }
        $map[] = ['member_guid', '=', $member_guid];
        return $db_websocket_user->where($map)->column('uid');
    }

    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            try {
                //后续要异步化
                $data = json_decode($after_replace_send_data, true);
                if (!is_array($data)) {
                    throw new Exception('websoket 模板可能不是合法json');
                }
                $data['action'] = $data['action'] ?? 'broadcast';
                $result         = $send_handler->push($data, $to_user, $this->channel);
                $record_data    = [
                    'to_user' => $to_user,
                    'content' => $after_replace_send_data,
                ];
                if ($result !== true) {
                    $msg                   = is_string($result) ? $result : '推送失败!';
                    $record_data['status'] = -1;
                    $record_data['result'] = $msg;
                }
                $this->record($record_data);
            } catch (Exception $e) {
                wr_log('websoket 消息推送失败,错误码:' . $e->getCode() . '原因:' . $e->getMessage());
            }
        }
        return true;
    }

    public function send($message, $to_user = '')
    {
        return true;
    }
}
