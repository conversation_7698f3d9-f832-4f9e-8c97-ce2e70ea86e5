<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\model\Business;
use app\model\Member;
use app\model\User;
use app\common\service\SmsService;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Sms extends Driver
{
    use Tools;

    public function __construct($config)
    {

    }

    public function getSendData(): array
    {
        return $this->data;
    }


    public function getSendHandlerSystem(): SmsService
    {
        return SmsService::get_instance();
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): SmsService|bool
    {
        return false;
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): SmsService
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForSms未指定商家标识');
        }
        return SmsService::get_instance($bid);
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): SmsService
    {
        $bid = $bid ?: $this->bid;
        return SmsService::get_instance($bid);
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): SmsService
    {
        $bid = $bid ?: $this->bid;
        return SmsService::get_instance($bid);
    }


    public function getSendHandler(): SmsService
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        //系统通知接收者暂时写死
        return ['***********'];
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid       = $bid ?: $this->bid;
        $user_guid = $user_guid ?: $this->user_guid;
        if (empty($bid)) {
            return [];
        }
        if (empty($user_guid)) {
            $db_business = new Business();
            $map         = [['guid', '=', $bid]];
            $mobile      = $db_business->where($map)->value('mobile');
            return [$mobile];
        } else {
            $db_user = new User();
            $map     = [
                ['bid', '=', $bid],
            ];
            if (is_string($user_guid)) {
                $map[] = ['guid', '=', $user_guid];
            } elseif (is_array($user_guid)) {
                $map[] = ['guid', 'IN', $user_guid];
            }
            return $db_user->where($map)->column('tel');
        }
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        if (!empty($this->member_mobile)) {
            return [$this->member_mobile];
        }
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        $db_member = new Member();
        $map       = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
        ];
        $mobile    = $db_member->where($map)->value('mobile');
        return [$mobile];
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            $record_data = [
                'to_user' => $to_user,
                'content' => $after_replace_send_data,
            ];
            $result      = $send_handler->send_sms(['content' => $after_replace_send_data, 'mobile' => $to_user]);
            if ($result === false) {
                $record_data['status'] = -1;
                $record_data['result'] = $send_handler->message;
            }
            $this->record($record_data);
        }
        return true;
    }

    public function send($message, $to_user = '')
    {
        try {
            send_sms($message, $to_user ?: $this->getConfig('to'));
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
