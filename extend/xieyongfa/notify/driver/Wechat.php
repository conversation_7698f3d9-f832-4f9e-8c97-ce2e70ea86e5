<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\model\AppidOpenidMapping;
use app\model\Business;
use app\model\Member;
use app\model\UserBindNote;
use app\model\WechatConfig;
use app\model\WechatTemplateList;
use Exception;
use WeChat\Template;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Wechat extends Driver
{
    use Tools;

    public function __construct($config)
    {
        $this->handler = weixin($config['appid'])::WeChatTemplate();
    }

    protected $wechat_appid = '';
    protected $openid_appid_list = [];

    public function getSendData(): array
    {
        return $this->data;
    }

    public function getSendHandlerSystem(): Template|bool
    {
        $this->wechat_appid = get_system_config('platform_wechat_appid');
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatTemplate();
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): Template|bool
    {
        $bid              = $bid ?: $this->bid;
        $db_business      = new Business();
        $map              = [
            ['guid', '=', $bid]
        ];
        $business_info    = $db_business->where($map)->findOrFail();
        $parent_guid      = $business_info['parent_guid'];
        $parent_user_guid = $business_info['parent_user_guid'];
        if (empty($parent_guid) || empty($parent_user_guid)) {
            return false;
        }
        return $this->getSendHandlerBusiness($parent_guid, $parent_user_guid);
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): Template|bool
    {
        $bid               = $bid ?: $this->bid;
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['way', '=', 1], // 公众号关联的才能发消息  2 是小程序
        ];
        if (!empty($user_guid)) {
            $map[] = ['user_guid', '=', $user_guid];
        }
        $openid_appid_list = $db_user_bind_note->where($map)->order(['create_time' => 'DESC'])->field(['appid', 'openid'])->select();
        if ($openid_appid_list->isEmpty()) {
            return false;
        }
        foreach ($openid_appid_list as $key => $val) {
            if (!$this->wechat_appid) {
                $this->wechat_appid = $val['appid'];
            }
            $this->openid_appid_list[$val['openid']] = $val['appid'];
        }
        return weixin($this->wechat_appid)::WeChatTemplate();
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): Template|bool
    {
        $bid                = $bid ?: $this->bid;
        $config             = get_config_by_bid($bid);
        $this->wechat_appid = $config['appid'];
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatTemplate();
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): Template|bool
    {
        $bid                = $bid ?: $this->bid;
        $config             = get_config_by_bid($bid);
        $this->wechat_appid = $config['appid'];
        if (!$this->wechat_appid) {
            return false;
        }
        return weixin($this->wechat_appid)::WeChatTemplate();
    }


    public function getSendHandler(): Template|bool
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        return [get_system_config('platform_wechat_openid')];
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid              = $bid ?: $this->bid;
        $db_business      = new Business();
        $map              = [
            ['guid', '=', $bid]
        ];
        $business_info    = $db_business->where($map)->findOrFail();
        $parent_guid      = $business_info['parent_guid'];
        $parent_user_guid = $business_info['parent_user_guid'];
        if (empty($parent_guid) || empty($parent_user_guid)) {
            return [];
        }
        return $this->getReceiverBusiness($parent_guid, $parent_user_guid);
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid       = $bid ?: $this->bid;
        $user_guid = $user_guid ?: $this->user_guid;

        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForWechat未指定商家标识');
        }
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['way', '=', 1],
        ];
        if (!empty($user_guid)) {
            if (is_string($user_guid)) {
                $map[] = ['user_guid', '=', $user_guid];
            } elseif (is_array($user_guid)) {
                $map[] = ['user_guid', 'IN', $user_guid];
            }
        }

        return $db_user_bind_note->where($map)->column('openid');
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        $db_member = new Member();
        $map       = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
            ['from', 'NOT IN', [6, 8]],  //排除掉钉钉和企业微信
        ];

        $member_info = $db_member->where($map)->field(['appid', 'openid'])->find();
        if (empty($member_info['openid'])) {
            return [];
        }
        $appid            = $member_info['appid'];
        $openid           = $member_info['openid'];
        $db_wechat_config = new WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['type']);
        if ($info['type'] == 1) {
            return [$openid];
        }
        // 不是公众号尝试找映射关系
        $db_appid_openid_mapping = new AppidOpenidMapping();
        $map                     = [
            ['appid', '=', $appid],
            ['openid', '=', $openid],
            //            ['appid_type', '=', 2], //小程序
            ['relation_appid_type', '=', 1], //关联类型公众号
        ];
        $relation_openid         = $db_appid_openid_mapping->where($map)->value('relation_openid');
        return $relation_openid ? [$relation_openid] : [];
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    protected function get_admin_weapp($appid)
    {
        //小程序appid => 公众号 appid 数组
        $list = [
            'wxae45934daf093dbf' => ['wx07ce368b922c9841']
        ];
        foreach ($list as $key => $val) {
            if (in_array($appid, $val)) {
                return $key;
            }
        }
        return '';
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        if (empty($send_handler)) {
            return true;
        }
        $to_user_list = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        $param                   = $this->param;
        $template_no             = $param['template_no']; //模板编号
        $after_replace_send_data = str_replace("\\r\\n", '#', $after_replace_send_data); //微信 2023年5月起不支持换行符了 替换成#
        $after_replace_send_data = str_replace("\\", '|', $after_replace_send_data); // \ 反斜杠会导致json无法转数组
        $send_data_array         = json_decode($after_replace_send_data, true);
        if (!tools()::is_json($after_replace_send_data)) {
            throw new Exception('可能模板内容不是合法的json:' . $after_replace_send_data);
        }
        foreach ($to_user_list as $to_user) {
            try {
                $appid = $this->wechat_appid;
                if (!empty($this->openid_appid_list) && !empty($this->openid_appid_list[$to_user])) {
                    //为了兼容一个商户同时用多个公众号绑定的时候能准确的使用公众号发送
                    $appid        = $this->openid_appid_list[$to_user];
                    $send_handler = weixin($appid)::WeChatTemplate();
                }
                //替换小程序appid
                if (strpos($after_replace_send_data, '{$ADMIN_WEAPP}') !== false) {
                    //存在小程序APPID 则走替换流程
                    $admin_weapp                         = $this->get_admin_weapp($appid);
                    $after_replace_admin_weapp_send_data = str_replace('{$ADMIN_WEAPP}', $admin_weapp, $after_replace_send_data);
                    $send_data_array                     = json_decode($after_replace_admin_weapp_send_data, true);
                    if (empty($send_data_array['miniprogram']['appid'])) {
                        $send_data_array['miniprogram']['pagepath'] = '';
                    }
                }
                $db_wechat_template_list   = new WechatTemplateList();
                $send_data_array['touser'] = $to_user;
                $record_data               = [
                    'to_user' => $to_user,
                    'content' => json_encode($send_data_array, JSON_UNESCAPED_UNICODE),
                ];
                $template_id               = $db_wechat_template_list->get_template_id_by_template_no($appid, $template_no); //获取模板id
                if (empty($template_id)) {
                    throw new Exception('appid:' . $appid . '模板id' . $template_no . '获取失败', -1);
                }
                $send_data_array['template_id'] = $template_id;
                $result                         = $send_handler->send($send_data_array);
            } catch (Exception $e) {
                $msg = '模板消息发送失败,错误码:' . $e->getCode() . '原因:' . $e->getMessage();
                if (!in_array($e->getCode(), [43004, 40247, -1])) {
                    //模板消息发送失败,错误码:40247原因:历史模板库已升级为类目模板。历史模板库内模板不再支持添加。 如有相关消息需求，可使用类目模板库ID添加对应模板 rid: 671e238f-17e07495-1cb14dfc
                    //模板消息发送失败,错误码:43004原因:require subscribe rid: 671c5348-6cf94bbb-50aafe87
                    wr_log($msg);
                }
                $record_data['status'] = -1;
                $record_data['result'] = $msg;
            }
            $this->record($record_data);
        }
        return true;
    }

    public function send($message, $to_user = '')
    {
        $wechat   = $this->handler;
        $template = [
            'touser'      => $to_user ?: $this->getConfig('openid'),
            'template_id' => $this->getConfig('template_id'),
            'data'        => [
                'first'    => [
                    'value' => '您有新的消息，请及时处理。',
                    'color' => '#666666',
                ],
                'keyword1' => [
                    'value' => '系统通知',
                    'color' => '#000000',
                ],
                'keyword2' => [
                    'value' => $message,
                    'color' => '#FF7F00',
                ],
                'remark'   => [
                    'value' => "接收时间:" . format_timestamp() . "\r\n\r\n点击查看详情",
                    'color' => '#000000',
                ],
            ],
        ];
        try {
            $result = $wechat->send($template);
        } catch (Exception $e) {
            return false;
        }
        return true;
    }
}
