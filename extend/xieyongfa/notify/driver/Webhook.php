<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use app\model\Business;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Webhook extends Driver
{
    use Tools;

    public string $channel = 'webhook';

    public function __construct($config)
    {
        $this->handler = tools();
    }

    public function getSendData(): array
    {
        return $this->data;
    }

    public function getSendHandlerSystem()
    {
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null)
    {
        $bid = $bid ?: $this->bid;
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null)
    {
        $bid = $bid ?: $this->bid;
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null)
    {
        $bid = $bid ?: $this->bid;
    }


    public function getSendHandler()
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        return [];
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): bool
    {
        return false;
    }

    /**
     * @throws Exception
     */
    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid       = $bid ?: $this->bid;
        $user_guid = $user_guid ?: $this->user_guid;
        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForWebhook 未指定商家标识');
        }
        $config = get_config_by_bid($bid);
        if (!empty($config['notify_url'])) {
            //推送第三方,后续支持多个URL推送
            return [$config['notify_url']];
        }
        return [];
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        throw new Exception('getSendHandlerBusinessForWebhook 暂时不支持通知会员');
    }

    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    protected function buildSignature($data, $appid, $secret, $timestamp)
    {
        ksort($data);
        $sign_content = tools()::get_sign_content($data);
        return strtoupper(md5($sign_content . '&appid=' . $appid . '&secret=' . $secret . '&timestamp=' . $timestamp));
    }

    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_url_list             = $this->getReceiver();
        if (empty($to_url_list)) {
            return true;
        }
        foreach ($to_url_list as $to_url) {
            try {
                $data = json_decode($after_replace_send_data, true);
                if (!is_array($data)) {
                    throw new Exception('webhook 模板可能不是合法json');
                }
                $bid             = $this->bid;
                $db              = new Business();
                $business_info   = $db->get_business_info_by_account_or_guid($bid);
                $appid           = $business_info['appid'];
                $secret          = $business_info['secret'];
                $timestamp       = time();
                $signature       = $this->buildSignature($data, $appid, $secret, $timestamp);
                $url_params_data = [
                    'appid'     => $appid,
                    'timestamp' => $timestamp,
                    'signature' => $signature,
                ];
                $url_params      = http_build_query($url_params_data);
                $to_url          .= '?' . $url_params;
                $notify_data     = [
                    'bid'  => $bid,
                    'data' => $data,
                    'type' => $this->key_name
                ];
                $result          = curl()->set_timeout(5)->post($to_url, $notify_data)->get_body();
                $record_data     = [
                    'to_user' => $to_url,
                    'content' => $after_replace_send_data,
                ];
                if (is_array($result) && isset($result['code']) && $result['code'] == 0) {
                    wr_log($to_url . 'webhook消息推送成功');
                } else {
                    $msg                   = is_string($result) ? $result : '推送失败!';
                    $record_data['status'] = -1;
                    $record_data['result'] = $msg;
                }
                $this->record($record_data);
            } catch (Exception $e) {
                wr_log('webhook 消息推送失败,错误码:' . $e->getCode() . '原因:' . $e->getMessage());
            }
        }
        return true;
    }

    public function send($message, $to_user = '')
    {
        return true;
    }
}
