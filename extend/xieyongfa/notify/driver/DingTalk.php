<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use bingher\ding\DingBot;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class DingTalk extends Driver
{
    use Tools;

    public function __construct($config)
    {
        try {
            $this->handler = new DingBot($config);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    public function getSendData(): array
    {
        return $this->data;
    }


    public function getSendHandlerSystem(): DingBot
    {
        return new DingBot($this->getConfig());
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null): DingBot|bool
    {
        return false;
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null): DingBot
    {
        $bid = $bid ?: $this->bid;
        return $this->getSendHandlerSystem();
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null): DingBot
    {
        $bid = $bid ?: $this->bid;
        return $this->getSendHandlerSystem();
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null): DingBot
    {
        $bid = $bid ?: $this->bid;
        return $this->getSendHandlerSystem();
    }


    public function getSendHandler(): DingBot
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        //系统通知接收者暂时写死
        return ['***********'];
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        return $this->getReceiverSystem();
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            $result = $send_handler->atAll()->text($this->build($after_replace_send_data));
        }
    }

    public function send($message, $to_user = '')
    {
        try {
            $ding_talk = $this->handler;
            $result    = $ding_talk->atAll()->text($this->build($message));
            return $this->_parseResult($result);
        } catch (Exception $e) {
            return false;
        }
    }

    protected function build($message): string
    {
        return '【监控报警】' . $message;
    }

    /**
     * 解析返回的结果
     * @param mixed $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if ($result === false) {
            wr_log($this->handler->getError(), 1);
            return false;
        }
        return true;
    }
}
