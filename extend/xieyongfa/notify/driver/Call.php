<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: xieyongfa123 <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace xieyongfa\notify\driver;

use AlibabaCloud\SDK\Dyvmsapi\*********\Dyvmsapi;
use AlibabaCloud\SDK\Dyvmsapi\*********\Models\SingleCallByTtsRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use app\model\Business;
use app\model\Member;
use app\model\User;
use Darabonba\OpenApi\Models\Config;
use Exception;
use xieyongfa\notify\Driver;
use xieyongfa\notify\traits\Tools;

class Call extends Driver
{
    use Tools;

    public function __construct($config)
    {

    }

    public function getSendData(): array
    {
        return $this->data;
    }


    public function getSendHandlerSystem()
    {
        return null;
    }

    public function getReceiverAgent(string|null $bid = null, array|string|null $user_guid = null): array
    {
        return [];
    }

    public function getSendHandlerAgent(string|null $bid = null, string|null $user_guid = null)
    {
        return false;
    }

    public function getSendHandlerBusiness(string|null $bid = null, string|null $user_guid = null)
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            throw new Exception('getSendHandlerBusinessForSms未指定商家标识');
        }
        return null;
    }

    public function getSendHandlerMember(string|null $bid = null, string|null $member_guid = null)
    {
        $bid = $bid ?: $this->bid;
        return null;
    }

    public function getSendHandlerYkyMember(string|null $bid = null, string|null $member_guid = null)
    {
        $bid = $bid ?: $this->bid;
        return null;
    }


    public function getSendHandler()
    {
        return call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], []);
    }

    public function getReceiverSystem(): array
    {
        //系统通知接收者暂时写死
        return ['***********'];
    }

    public function getReceiverBusiness(string|null $bid = null, array|string|null $user_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        if (empty($user_guid)) {
            $db_business = new Business();
            $map         = [['guid', '=', $bid]];
            $mobile      = $db_business->where($map)->value('mobile');
        } else {
            $db_user = new User();
            $map     = [
                ['bid', '=', $bid],
                ['guid', '=', $user_guid],
            ];
            $mobile  = $db_user->where($map)->value('mobile');
        }
        return [$mobile];
    }

    public function getReceiverYkyMember(string|null $bid = null, string|null $member_guid = null): array
    {
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        return [];
    }

    public function getReceiverMember(string|null $bid = null, string|null $member_guid = null): array
    {
        if (!empty($this->member_mobile)) {
            return [$this->member_mobile];
        }
        $bid = $bid ?: $this->bid;
        if (empty($bid)) {
            return [];
        }
        $member_guid = $member_guid ?: $this->member_guid;
        if (empty($member_guid)) {
            return [];
        }
        $db_member = new Member();
        $map       = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
        ];
        $mobile    = $db_member->where($map)->value('mobile');
        return [$mobile];
    }


    public function getReceiver(): array
    {
        return array_filter(array_unique(call_user_func_array([$this, __FUNCTION__ . parse_name($this->provider, 1)], [])));
    }

    /**
     * @throws Exception
     */
    public function notify()
    {
        $after_replace_send_data = $this->replace_variable_from_template();
        $send_handler            = $this->getSendHandler();
        $to_user_list            = $this->getReceiver();
        if (empty($to_user_list)) {
            return true;
        }
        $this->check_to_user_list_num($to_user_list);
        foreach ($to_user_list as $to_user) {
            $record_data = [
                'to_user' => $to_user,
                'content' => $after_replace_send_data,
            ];
            //            $result      = $send_handler->send_sms(['content' => $after_replace_send_data, 'mobile' => $to_user]);
            $result = false;//todo 待完成
            if ($result === false) {
                $record_data['status'] = -1;
                $record_data['result'] = $send_handler->message;
            }
            $this->record($record_data);
        }
        return true;
    }

    public static function createClient($accessKeyId, $accessKeySecret)
    {
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId"     => $accessKeyId,
            // 必填，您的 AccessKey Secret
            "accessKeySecret" => $accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "dyvmsapi.aliyuncs.com";
        return new Dyvmsapi($config);
    }

    public function send($message, $to_user = '')
    {
        $accessKeyId     = 'LTAI5tQn2xTQ3ez1jr6amDrb'; // 一卡易_谢永发 key
        $accessKeySecret = '******************************';

        $client                 = self::createClient($accessKeyId, $accessKeySecret);
        $tts_param              = [
            'body' => $message,
        ];
        $singleCallByTtsRequest = new SingleCallByTtsRequest([
            "ttsParam"     => json_encode($tts_param, JSON_UNESCAPED_UNICODE),
            "calledNumber" => $to_user ?: $this->getConfig('to'),
            "ttsCode"      => "TTS_198926842"
        ]);
        $runtime                = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $result = $client->singleCallByTtsWithOptions($singleCallByTtsRequest, $runtime);
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            Utils::assertAsString($error->message);
        }
        return true;
    }
}
