<?php
declare(strict_types=1);

namespace xieyongfa\notify;

use Exception;
use think\helper\Str;
use think\Manager;

class Notify extends Manager
{

    /**
     * @param string|null $name
     * @return Driver
     */
    public function channel(string $name = null): Driver
    {
        $channel   = Str::studly($name);
        $file_name = __DIR__ . '/driver/' . $channel . '.php';
        if (!file_exists($file_name)) {
            throw new Exception("File " . $file_name . " is not exist.");
        }
        $this->namespace = "\\" . __NAMESPACE__ . '\\driver\\';
        return $this->driver($channel);
    }

    /**
     * @param string $key_name
     * @param string $channel
     * @param string $type
     * @return Driver
     * @throws Exception
     */
    public function create_gateway(string $key_name, string $channel, string $type): Driver
    {
        $channel         = Str::studly($channel);
        $file_name       = __DIR__ . '/driver/Gateways/' . ucfirst($key_name) . '/' . ucfirst($type) . '/' . $channel . '.php';
        $this->namespace = "\\" . __NAMESPACE__ . '\\driver\Gateways\\' . ucfirst($key_name) . '\\' . ucfirst($type) . '\\';
        if (!file_exists($file_name)) {
            $this->namespace = "\\" . __NAMESPACE__ . '\\driver\\';
            //throw new \Exception("File " . $file_name . " is not exist.");
        }
        return $this->driver($channel);
    }

    /**
     * 默认驱动
     * @return string
     */
    public function getDefaultDriver(): string
    {
        return $this->app->config->get('notify.default');
    }

    protected function resolveType(string $name)
    {
        return $this->app->config->get("notify.driver.{$name}.type", 'email');
    }

    protected function createDriver(string $name): Driver
    {
        /** @var Driver $driver */
        $driver = parent::createDriver($name);
        return $driver->setApp($this->app)->setConnection($name)->setConfig((array)$this->resolveConfig($name));
    }

    protected function resolveConfig(string $name)
    {
        return $this->app->config->get("notify.driver.{$name}");
    }
}