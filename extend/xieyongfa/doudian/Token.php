<?php
declare (strict_types=1);


namespace xieyong<PERSON>\doudian;


use Exception;
use xieyong<PERSON>\doudian\Contracts\BasicDoudian;

/**
 * 门店
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Token extends BasicDoudian
{
    /**
     * 生成token API
     * @link  https://op.jinritemai.com/docs/api-docs/162/1600
     * @param array $data 请求数据
     * @throws Exception
     * @return bool|array
     */
    public function create($data = []): bool|array
    {
        $default_data = [
            'code'       => '',
            'grant_type' => 'authorization_self',
            'shop_id'    => $this->shop_id,
        ];
        $method       = $this->get_method(__FILE__, __FUNCTION__);
        return $this->request($method, $default_data, false);
    }

    /**
     * 生成token API
     * @link  https://op.jinritemai.com/docs/api-docs/162/1600
     * @param array $data 请求数据
     * @throws Exception
     * @return bool|array
     */
    public function refresh($data = []): bool|array
    {
        $default_data = [
            'refresh_token' => $this->refreshToken, //注意：传真实的refreshToken值，而不是传REFRESH_TOKEN
            'grant_type'    => 'refresh_token',
        ];
        $method       = $this->get_method(__FILE__, __FUNCTION__);
        return $this->request($method, $default_data, false);
    }
}