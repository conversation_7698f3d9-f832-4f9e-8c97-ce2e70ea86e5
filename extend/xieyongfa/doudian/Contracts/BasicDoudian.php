<?php
declare (strict_types=1);

namespace xieyong<PERSON>\doudian\Contracts;

use xieyong<PERSON>\doudian\Token;
use Exception;

class BasicDoudian
{
    /**
     * 接口基础地址
     */
    const BASE_URL = 'https://openapi-fxg.jinritemai.com';
    private $host; //抖音接口API，API调用指南：https://op.jinritemai.com/docs/guide-docs/148/814
    private $appKey; //appKey
    private $appSecret; //appSecret
    public $shop_id;
    public $accessToken; //访问令牌
    public $refreshToken; //刷新令牌
    private $versionNumber; //API协议版本，当前版本为 2
    private $versionNumberStr; //API协议版本，当前版本为 v2
    /**
     * status
     */
    public int $status = -1;

    /**
     * message
     */
    public string $message = '';

    /**
     * data
     */
    public array $response_data = [];


    /**
     * @var DataArray
     */
    public DataArray $config;


    /**
     * 当前请求方法参数
     * @var array
     */
    protected array $currentMethod = [];

    /**
     * 当前是否已经重试过
     * @var bool
     */
    protected bool $isTry = false;

    /**
     * @param array $options
     * @throws Exception
     */
    public function __construct(array $options = [])
    {
        $this->config = new DataArray($options);
//        if (!$this->config->offsetExists('client_key') || !$this->config->offsetExists('client_secret')) {
//            throw new Exception('client_key 或者 client_secret');
//        }
        $this->host             = 'https://openapi-fxg.jinritemai.com'; //接口访问地址
        $this->appKey           = '7410007734822667813';
        $this->appSecret        = '736570e1-dcfa-41f3-a4e1-e4b8e6170697';
        $this->versionNumber    = '2';
        $this->shop_id          = '19465181';
        $this->versionNumberStr = 'v' . $this->versionNumber;
    }


    //    /**
    //     * 动态调用
    //     * @param string $method
    //     * @param array $arguments
    //     * @return mixed
    //     * @throws Exception
    //     */
    //    public function __call(string $method, array $arguments = [])
    //    {
    //        return $this->getArrayResult(array_shift($arguments), $method);
    //    }

    /**
     * 获取原始结果
     * @return array
     */
    public function getResponseData(): array
    {
        return $this->response_data;
    }


    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return $this
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @link http://openapi.1card1.cn/
     * @return bool|array
     */
    protected function retry()
    {
        $this->isTry = true;
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @link http://openapi.1card1.cn/
     * @param mixed $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        dump($result);
        if (empty($result) || !is_array($result)) {
            if (isset($this->currentMethod['method']) && empty($this->isTry)) {
                return $this->retry();
            }
            $this->status  = -1;
            $this->message = '解析返回结果失败';
            return false;
        }
        $this->response_data = $result; //赋值 $data 便于 status 不等于0时候还需要使用返回的数据
        if ((isset($result['code']) && $result['code'] !== 10000)) {
            $this->status  = $result['code'];
            $this->message = $result['msg'] ?? '未知错误';
            isset($result['sub_msg']) && $this->message .= '(子错误消息:' . $result['sub_msg'] . ')';
            isset($result['sub_code']) && $this->message .= '(子错误码:' . $result['sub_code'] . ')';
            return false;
        }
        $data = $result['data'];
        return is_null($data) ? [] : $data;
    }


    /**
     * 封装抖音接口公共方法
     * PHP调用说明：https://op.jinritemai.com/docs/guide-docs/151/811
     * @param string $method 方法名：格式 token.create 方法中转为 token/create
     * @param array $param 请求接口需要的参数名
     * @param bool $accessToken url中是否要加上 access_token，默认否。
     *              为什么不直接传accessToken的值：在本类中，可以获取到accessToken的值，直接传，但是如果在其他的地方调用就获取不到access_token的值，需要传true/false标识在本类中获取。
     * @param bool $paramJsonAddToUrl 是否把paramJson放到 url 中，根据实际情况
     *          例：实际过程中【订单批量解密接口】不需要放到url中（猜测是这个接口paramJson内容太多，会超出GET的最大内容）
     *              订单批量解密接口：https://op.jinritemai.com/docs/api-docs/15/982
     * @return array|bool
     */
    public function request(string $method, array $param, bool $accessToken = true, bool $paramJsonAddToUrl = true)
    {
        //当前时间戳
        $timestamp = time();
        //PHP中：如果数组为空转为json之后是[]。但接口可能是强类型语言编写的，需要传{}。所以$param为空时，需要把$paramJson设置为{}
        $paramJson = $param ? self::marshal($param) : '{}';
        //获取签名
        $sign = self::sign($method, $timestamp, $paramJson);
        //调用的方法.替换为/
        $methodPath = str_replace('.', '/', $method);
        //拼接url路径
        $url = $this->host . '/' . $methodPath . '?method=' . urlencode($method) . '&app_key=' . urlencode($this->appKey);
        if ($accessToken) {
            $url .= '&access_token=' . urlencode($this->get_access_token());
        }
        $url .= '&timestamp=' . urlencode(strval($timestamp)) . '&v=' . urlencode($this->versionNumber) . '&sign=' . $sign;
        if ($paramJsonAddToUrl) {
            $url .= '&param_json=' . $paramJson;
        }
        $url    .= '&sign_method=' . urlencode('hmac-sha256'); //官方接口为非必填，但是不加签名会验证失败
        $result = curl()->json()->post($url, $paramJson)->get_body();
        return $this->_parseResult($result);
    }

    public function get_access_token()
    {
        $cache_key    = 'dou_dian_access_token:' . $this->appKey . ':' . $this->shop_id;
        $access_token = cache($cache_key);
        if (empty($access_token)) {
            $token = new Token($this->config->get());
            $data  = $token->create();
            if ($data === false) {
                throw new Exception('获取access_token失败:' . $token->message);
            }
            $access_token  = $data['access_token']; //accessToken
            $refresh_token = $data['refresh_token']; //refreshToken //todo 存储到数据库
            $expires_in    = $data['expires_in']; //Token过期时间 = 当前时间 + 有效时间（秒s）
            cache($cache_key, $access_token, $expires_in);
        }
        return $access_token;
    }

    //计算签名
    public function sign($method, $timestamp, $paramJson)
    {
        $paramPattern = 'app_key' . $this->appKey . 'method' . $method . 'param_json' . $paramJson . 'timestamp' . $timestamp . $this->versionNumberStr;
        $signPattern  = $this->appSecret . $paramPattern . $this->appSecret;
        return hash_hmac("sha256", $signPattern, $this->appSecret);
    }

    //序列化参数，入参必须为关联数组（键值对数组）
    public function marshal(array $param)
    {
        $this->rec_ksort($param); // 对关联数组中的kv，执行排序，需要递归
        // 重新序列化，确保所有key按字典序排序
        // 加入flag，确保斜杠不被escape，汉字不被escape
        return json_encode($param, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    //关联数组排序，递归
    public function rec_ksort(array &$arr)
    {
        $kstring = true;
        foreach ($arr as $k => &$v) {
            if (!is_string($k)) {
                $kstring = false;
            }
            if (is_array($v)) {
                $this->rec_ksort($v); //这里的调用方式要和marshal中调用方式一致
            }
        }
        if ($kstring) {
            ksort($arr);
        }
    }

    public function get_method($file, $function)
    {
        // 使用 basename() 获取文件名（包含扩展名）
        $filenameWithExtension = basename($file);
        // 使用 explode() 分割文件名和扩展名，并获取文件名
        $filenameParts = explode('.', $filenameWithExtension);
        $filename      = $filenameParts[0]; // 不包含扩展名的文件名
        return lcfirst($filename) . '.' . $function;
    }
}