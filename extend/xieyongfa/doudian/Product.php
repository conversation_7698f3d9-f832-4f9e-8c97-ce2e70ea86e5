<?php
declare (strict_types=1);


namespace xieyong<PERSON>\doudian;


use Exception;
use xieyongfa\doudian\Contracts\BasicDoudian;

/**
 * 门店
 * @library Member
 * <AUTHOR>
 * @date 2020/01/03 20:24
 **/
class Product extends BasicDoudian
{
    /**
     * 批量查询商品列表
     * @link https://op.jinritemai.com/docs/api-docs/14/633
     * @param array $data 请求数据
     * @throws Exception
     * @return bool|array
     */
    public function listV2($data = []): bool|array
    {
        $default_data = [
            'page' => 1,
            'size' => 10,
        ];
        $method       = $this->get_method(__FILE__, __FUNCTION__);
        return $this->request($method, $default_data);
    }
}