<?php
declare (strict_types=1);


namespace xieyongfa\doudian;

use xieyongfa\doudian\Contracts\DataArray;
use xieyongfa\doudian\Exceptions\InvalidInstanceException;

/**
 * 加载缓存器
 * @library Yky
 * <AUTHOR>
 * @date 2020/01/03 20:24
 * @method Token Token($options = []) static Token
 * @method Product Product($options = []) static Product
 **/
class Doudian
{

    /**
     * 定义当前版本
     * @var string
     */
    const VERSION = '1.0.1';

    /**
     * 静态配置
     * @var DataArray
     */
    private static DataArray $config;

    /**
     * 设置及获取参数
     * @param array $option
     * @return array
     */
    public static function config(array $option = []): array
    {
        $sn = get_instance_sn($option);
        if (is_array($option)) {
            self::$config[$sn] = new DataArray($option);
        }
        if (isset(self::$config[$sn]) && self::$config[$sn] instanceof DataArray) {
            return self::$config[$sn]->get();
        }
        return [];
    }

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function __callStatic(string $name, array $arguments)
    {
        $class = __NAMESPACE__ . '\\' . $name;
        if (!class_exists($class)) {
            throw new InvalidInstanceException('Class {$class} not found');
        }
        $option = array_shift($arguments);
        $option = is_null($option) ? [] : $option;
        $config = is_array($option) ? $option : self::$config->get();
        return new $class($config);
    }

    /**
     * 静态魔术加载方法
     * @param array $config 配置
     * @param string $class 类名
     * @param string $action 方法名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws InvalidInstanceException
     */
    public static function call(array $config, string $class, string $action, array $arguments)
    {
        $obj = self::__callStatic($class, [$config]);
        return call_user_func_array([$obj, $action], $arguments);
    }
}