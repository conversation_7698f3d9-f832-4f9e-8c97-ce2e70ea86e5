<?php

namespace OpenApi;

use app\model\YlhAccessToken;

class Yunlianhuiv2
{
    /** 接口基础地址 */
    public $client_id;
    public $client_secret;
    public $user_id;
    public $access_token;
    public $refresh_token;
    /** 错误码 */
    public $errcode;
    /** 错误描述 */
    public $errmsg;
    public $redirect_uri;
    public $return_url;
    public $notify_url;
    public $gateway_pay_url;
    public $gateway_url;
    public $client_private_key;
    public $alipay_public_key;
    public $is_debug = false; //是否调试模式
    public $_retry = false; //是否已经重试

    /**
     *   constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $develop_config           = [
            //商家ID
            'user_id'            => '',
            //应用ID
            'client_id'          => '219aac5bf6887198',
            //应用密钥
            'client_secret'      => '4bcf65b0219aac5bf68871988d959d46',
            //应用回调地址
            'redirect_uri'       => 'http://www.yikayi.net/gateway/yunlianhui',
            //应用私钥，您的原始格式RSA私钥
            'client_private_key' => 'MIICXAIBAAKBgQDBDPOWlhA/cL22lWx3IlE9vBvBjDewYwOgZ/82WI468B3CXhWTEWSJ2fyqJusWrVX4wJVReKmyD6Ka4981Q2nDPAYQhJzEfRG8SP0mwHHDgILgPQYKoi1c2S44bssJwqzFisBBPJXGhhO5g8GrlkDXWKMJvN0nz+Pku8DtSq4YMwIDAQABAoGAIoLyOQVyuiSpha6IyXN1Gpg6TguHGR1aVJ7C3TBPnPG4IDs5mCUP2CUEKlbyyAcMy3+CMiJkE9T/uLjr+lWm3lSsbRkuOjUgcsb4sFuwEzTiqz+CmYpEQB/GGKlVLjBZvx/KFVLk7cydqGxJ8vdzghnjW+J6HiZSw8qlHR8NCeECQQDfDcsfwxYPK7khDm2fPgn2tt18GoAofb3UKgCTyLLIN0/ytXFt6HHwqcEjGgA1N/ozd4Dh8iTs4G6hR5xgqQ2VAkEA3ZCpQldg6v/5B7FhuDkUlwqACkwY1YGYdsPhg9RKGMnmqIjohUw2OLFEdkdeb1B/2175vPuam1LzaCeDV2ZMpwJAP8qbGBdcHLSdepW8xjkg8l00FMUjd/EWNDJa/ej9MIOZKsm3if5bb7+sy9Qo0Q+UT9nSk0wEQXaiTR/E2mQezQJBANDqDV7biywqrkLds7mEQPDmWrLa6/OtnfgmXObz7eYPwikZ5r2BjgLIyzgHtXiaW+SaWjsitrZMcey8jTCE6jMCQGkAzyDWSwGI+z8bS+R+SO43nDNNDksTOKlsLdjvuEOP5RiBnpJYx6gXuaQTaxO19xadWDBlI3BawcZXhKxkBj8=',
            //同步跳转
            'return_url'         => 'https://example.com/example/return_url.php',
            //异步通知地址
            'notify_url'         => 'https://example.com/example/notify_url.php',
            //云联惠API网关
            'gatewayUrl'         => "https://openapidev.yunlianhui.com",
            //云联惠开放平台收银台
            'gatewayPay'         => "https://openapidev.yunlianhui.com",
            //云联惠公钥,查看地址：https://docs.yunlianhui.com/keyManage
            'alipay_public_key'  => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBDPOWlhA/cL22lWx3IlE9vBvBjDewYwOgZ/82WI468B3CXhWTEWSJ2fyqJusWrVX4wJVReKmyD6Ka4981Q2nDPAYQhJzEfRG8SP0mwHHDgILgPQYKoi1c2S44bssJwqzFisBBPJXGhhO5g8GrlkDXWKMJvN0nz+Pku8DtSq4YMwIDAQAB'
        ];
        $master_config            = [
            //商家ID
            'user_id'            => '',
            //应用ID
            'client_id'          => 'a1ca9ae8',
            //应用密钥
            'client_secret'      => '1ad7c171db2d8fc0fcbbaea8e9f812c45a16f7de8c968309d71061463c334dec',
            //应用回调地址
            'redirect_uri'       => 'http://www.yikayi.net/gateway/yunlianhui',
            //应用私钥，您的原始格式RSA私钥
            'client_private_key' => 'MIICXAIBAAKBgQDl1AJp/YdiyLOSGWERqD1KMx69bBB9AsM110+KLGcaRohdJXvVJmSskONI+Uk+c3vHnK2kXyEIkPE3n44+EpVTq2NRBsb3SbyZsfvFHbU3rVVJuHp41nrezk8ZkxSP9i3vTeRjDBkxcU2EjecV+BIb8zf1n/rGGzH5ASNnrSOCqQIDAQABAoGAFiG3ZcuDSRD4sd39yfyAgm+vyx/n6pHkiH/GahDikNYv9YBRj3IWCcQeDv3qfrgSOGOvGQAMJ04F3Ixh3zpc5LYB8+JCr0OxKXL4pQXPz9ZOU1+Ik5z72tkFN9+nUi5F77IsiEWxPvaDO6fvTAoUgLmA7yaWpD1yqQuBsDRR2p0CQQD7nrB5g0p8zCyo6+LM7lvON49w/CHfWXX8Al8koLYrRh3l9ovFEPtS5kTC7Cq0v0MIPY0t5QLttz0q3bBdJkN7AkEA6dQ1Sqz1RfcSnNVPSvE41vTFW+Y36T0UvLeJrbmBiqWzJAP4h+niKS93ewe9AS6hq1jQjW8CqCY3/zZan4h3KwJBANY6d9odNDAozeVMYJ9GWZerqk7x/AzjaBaWFldjFXamjhIa5Ul1eeVM9EJZPMsDBJlhfnASyXmHR2bVtLC6QT0CQCkhct5X3i0YcOW1f+Um2WC2XaD0thEI9YTcpBMy9l51FzXWnPkLYwTrnJC/o7d5hGankMfQJx982YzC+t3lG1ECQDSbg7jRbo0oo5q5dId4GDDxRBSz33n46S7Wypea5nod2jJjwaeyf0lFqQbKuk5UBCO1SNCHUM9YLR44WT/JORA=',
            //同步跳转
            'return_url'         => 'https://example.com/example/return_url.php',
            //异步通知地址
            'notify_url'         => 'https://example.com/example/notify_url.php',
            //云联惠API网关
            'gatewayUrl'         => "https://openapi.yunlianhui.com",
            //云联惠开放平台收银台
            'gatewayPay'         => "https://openapi.yunlianhui.com",
            //云联惠公钥,查看地址：https://docs.yunlianhui.com/keyManage
            'alipay_public_key'  => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDl1AJp/YdiyLOSGWERqD1KMx69bBB9AsM110+KLGcaRohdJXvVJmSskONI+Uk+c3vHnK2kXyEIkPE3n44+EpVTq2NRBsb3SbyZsfvFHbU3rVVJuHp41nrezk8ZkxSP9i3vTeRjDBkxcU2EjecV+BIb8zf1n/rGGzH5ASNnrSOCqQIDAQAB'
        ];
        $config                   = array_merge($master_config, $config);
        $this->user_id            = $config['user_id'];
        $this->client_id          = $config['client_id'];
        $this->client_secret      = $config['client_secret'];
        $this->redirect_uri       = $config['redirect_uri'];
        $this->return_url         = $config['return_url'];
        $this->notify_url         = $config['notify_url'];
        $this->gateway_pay_url    = $config['gatewayPay'];
        $this->gateway_url        = $config['gatewayUrl'];
        $this->alipay_public_key  = $config['alipay_public_key'];
        $this->client_private_key = $config['client_private_key'];
        $this->access_token       = '';//先初始化为空,每次从cache中取
    }

    public function get_user_id($access_token)
    {
        $url    = 'api/v2/getUserId';
        $data   = [
            'client_id'    => $this->client_id,
            'access_token' => $access_token,
        ];
        $result = $this->getArrayResult($url, $data);
        if ($result !== false) {
            return $this->user_id = $result['user_id'];
        }
        return false;
    }

    protected function getArrayResult($url, $data = [])
    {
        $header            = [
            'Content-Type'    => 'application/x-www-form-urlencoded',
            'User-Agent'      => 'ylh-php-sdk',
            'Accept-Encoding' => '*',
        ];
        $data['timestamp'] = (string)time();
        $data['sign']      = $this->generateSign($data, $this->client_private_key);
        $url               = $this->gateway_url . '/' . $url;
        $data              = http_build_query($data, null, '&'); //拼装参数
        $result            = curl()->set_header($header)->post($url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 签名
     * @param $params
     * @param $rsaPrivateKey
     * @return string
     */
    protected function generateSign($params, $rsaPrivateKey)
    {
        $sign = new Sign($rsaPrivateKey);
        return $sign->generateSign($params);
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['error_code']) && $result['error_code'] != '0') {
            $this->errcode = $result['error_code'];
            $this->errmsg  = '未知错误';
            if (isset($result['error_message'])) {
                if (is_array($result['error_message'])) {
                    $this->errmsg = json_encode($result['error_message'], JSON_UNESCAPED_UNICODE);
                }
                if (is_string($result['error_message'])) {
                    $this->errmsg = $result['error_message'];
                }
            }
            return false;
        }
        if (isset($result['error']) && $result['error'] != '') {
            $this->errcode = $result['error'];
            $this->errmsg  = isset($result['error_description']) ? $result['error_description'] : '';
            return false;
        }
        return $result;
    }

    public function get_user_info()
    {
        $url  = 'api/v2/getUserInfo';
        $data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_access_token(),
        ];
        return $this->getArrayResult($url, $data);
    }

    public function get_access_token($force = false)
    {
        //TODO 获取access_token逻辑待完善
        if (!$this->user_id) {
            $this->errmsg = 'user_id 不能为空';
            return false;
        }
        $key           = 'ylh:access_token:' . $this->user_id;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        //获得分布式锁之后开始获取access_token,先判断是否存在缓存
        if ($force === false) {
            //如果不是强制获取 则优先从缓存获取
            $cache = cache($key);
            if ($cache) {
                $lock_instance->unlock();
                return $this->access_token = $cache;
            }
        }
        //TODO 后续尽量不要在SDK中连接数据库
        if (!$this->refresh_token && !$this->get_refresh_token()) {
            return false;
        }
        $data   = [
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'refresh_token',
            'refresh_token' => $this->refresh_token,
        ];
        $url    = 'api/v2/oauth/token';
        $result = $this->getArrayResult($url, $data);
        if ($result !== false) {
            cache($key, $result['access_token'], intval($result['expires_in']));
            $update_date = [
                'access_token'  => $result['access_token'],
                'refresh_token' => $result['refresh_token'],
                'expired_time'  => date("Y-m-d H:i:s", intval(time() + intval($result['expires_in']))),
            ];
            $db          = new YlhAccessToken();
            $map         = [['ylh_user_id', '=', $this->user_id]];
            $db::update($update_date, $map);
            $lock_instance->unlock();
            return $this->access_token = $result['access_token'];
        } else {
            $lock_instance->unlock();
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
    }

    protected function get_refresh_token($user_id = '')
    {
        if (!$user_id && !$this->user_id) {
            return false;
        }
        $user_id = $user_id ?: $this->user_id;
        $key     = 'ylh:refresh_token' . $user_id;
        $cache   = cache($key);
        if ($cache) {
            return $this->refresh_token = $cache;
        }
        //缓存不存在则从数据库中取
        $db            = new YlhAccessToken();
        $map           = [['ylh_user_id', '=', $user_id]];
        $refresh_token = $db->where($map)->value('refresh_token');
        if ($refresh_token) {
            cache($key, $refresh_token, 3600 * 24);
            return $this->refresh_token = $refresh_token;
        }
        $this->errmsg = 'get_refresh_token failed';
        return false;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    //快捷注册会员

    public function register_fast($mobile, $rcm_id = '')
    {
        $url  = 'api/v2/registerTempMember';
        $data = [
            'client_id' => $this->client_id,
            'mobile'    => $mobile,
            'rcm_id'    => $rcm_id,
        ];
        return $this->getArrayResult($url, $data);
    }

    public function get_token_by_code($code)
    {
        $data   = [
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'authorization_code',
            'code'          => $code,
            'redirect_uri'  => $this->redirect_uri
        ];
        $url    = 'api/v2/oauth/token';
        $result = $this->getArrayResult($url, $data);
        if ($result) {
            $this->refresh_token = $result['refresh_token'];
            $this->access_token  = $result['access_token'];
            cache('ylh:access_token:' . $result['refresh_token'], $this->access_token, intval($result['expires_in']));
        }
        return $result;
    }

    public function redirect_authorize_url($token)
    {
        ob_end_clean();
        $location_url = $this->gateway_url . '/oauth/authorize?client_id=' . $this->client_id . '&redirect_uri=' . urlencode($this->redirect_uri) . '&state=' . $token . '&response_type=code&scope=points+basic_info';
        header("Location:" . $location_url);
        exit;
    }

    public function refund_points($data)
    {
        $this->errcode = -999;
        $this->errmsg  = '系统维护中';
        return false;
        if ($this->is_debug) {
            return true;
        }
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url          = 'api/v2/refund';
        $default_data = [
            'client_id'     => $this->client_id,
            'access_token'  => $this->access_token,
            'order_id'      => '',//	订单号
            'refund_amount' => '',//金额（单位：分）
            'refund_reason' => '退单同步撤销积分',//退款原因
        ];
        $data         = array_merge($default_data, $data);
        return $this->getArrayResult($url, $data);
    }

    public function query_order($data)
    {
        $url          = 'api/v2/oderQuery';
        $default_data = [
            'client_id'    => $this->client_id,
            'order_id'     => '',//	订单号
            'out_trade_no' => '',//外商户号（单位：分）
        ];
        $data         = array_merge($default_data, $data);
        return $this->getArrayResult($url, $data);

    }

    public function return_points_by_platform($data)
    {
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url          = 'api/v2/returnPoints';
        $default_data = [
            'client_id'      => $this->client_id,
            'access_token'   => $this->access_token,
            'seller_user_id' => '',//平台指定商家user_id
            'buyer_mobile'   => '',//买家buyer_mobile
            'body'           => '',
            'subject'        => '',
            'out_trade_no'   => '',
            'total_amount'   => '',//订单总金额（单位：分）
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->getArrayResult($url, $data);
        if ($result === false) {
            return false;
        }
        //recharge_order_id	string	充值订单号
        //recharge_order_status	string	充值订单状态
        //order_id	string	订单号
        //first_time	bool	true为第一次
        //order_status	string	订单状态 T0为成功，其他失败
        //error_code	string	错误代码
        return $result;
    }

    public function return_points($data)
    {
        $this->errcode = -999;
        $this->errmsg  = '系统维护中';
        return false;
        if ($this->is_debug) {
            return [
                'order_id'     => format_timestamp(),
                'first_time'   => true,
                'order_status' => 'OK',
            ];
        }
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url          = 'api/v2/returnPoints';
        $default_data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->access_token,
            'buyer_mobile' => '',//买家手机号
            'total_amount' => '',//金额（单位：分）
            'body'         => '',
            'subject'      => '',
            'out_trade_no' => '',
            'order_msg'    => 'order_msg',//订单信息
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->getArrayResult($url, $data);
        if ($result === false) {
            return false;
        }
        switch ($result['order_status']) {
            case 'T0':
                return $result;
            case 'P990408':
                wr_log($data['out_trade_no'] . '积分返回失败:库存积分账户余额不足');
                $this->errcode = $result['order_status'];
                $this->errmsg  = '库存积分账户余额不足';
                return false;
            default:
                //wr_log($data['out_trade_no'] . '积分返回可能失败,状态码:' . $result['order_status'], 1);
                return $result;
        }
    }

    public function pay_init()
    {
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url    = 'api/v2/payInit';
        $data   = [
            'client_id'    => $this->client_id,
            'access_token' => $this->access_token,
            'out_trade_no' => '',//
            'subject'      => '',//
            'body'         => '',
            'return_url'   => $this->return_url,
            'notify_url'   => $this->notify_url,
            'total_amount' => '',//订单总金额（单位）
        ];
        $result = $this->getArrayResult($url, $data);
        if ($result !== false) {
            ob_clean();
            header('Location: ' . $this->gateway_pay_url . '/?order_token=' . $result['data']['order_token']);
            exit();
        }
    }

    public function transfer($data)
    {
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url          = 'api/v2/transfer';
        $default_data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->access_token,
            'to_user_id'   => '',//转入账户会员user_id
            'amount'       => '',//string	订单总金额（单位：分）
            'out_trade_no' => '',//外部订单号
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->getArrayResult($url, $data);
        if ($result === false) {
            return false;
        }
        switch ($result['order_status']) {
            case 'OPEN-TRANSFER-SUCCESS':
                return $result;
            case 'OPEN-TRANSFER-FAIL-FROM-':
                $this->errcode = $result['order_status'];
                $this->errmsg  = '代发账户扣除失败';
                return false;
            case 'OPEN-TRANSFER-FAIL-TO-':
                $this->errcode = $result['order_status'];
                $this->errmsg  = '转入账户增加失败';
                return false;
            default:
                return $result;
        }
    }

    public function sys_last_day_sta()
    {
        if (!$this->access_token && !$this->get_access_token()) {
            return false;
        }
        $url    = 'api/v2/SysLastDaySta';
        $result = $this->getArrayResult($url, []);
        if ($result === false) {
            return false;
        }
        return $result;
    }

    /**
     * 获取签名
     * @param array $data 签名数组
     * @return bool|string 签名值
     */
    protected function getSignature($data)
    {
        ksort($data);
        $params = '';
        foreach ($data as $key => $value) {
            if (!$this->checkEmpty($value)) {
                $params .= $key . $value;
            }
        }
        return strtoupper(MD5($this->client_secret . $params . $this->client_secret));
    }

    /**
     * 校验$value是否非空
     *
     * @param  $value
     * @return boolean
     *  if not set ,return true;
     *    if is null , return true;
     **/
    protected function checkEmpty($value)
    {
        if (!isset($value))
            return true;
        if ($value === null)
            return true;
        if (trim($value) === "")
            return true;
        return false;
    }

}