<?php

namespace OpenApi;

use app\common\exceptions\NotNotifyException;
use app\model\XiaoQuHouse;
use app\model\XiaoQuBindInfo;
use app\model\XiaoQuBuilding;

class ZhuJianJu
{
    const  API_URL = "https://zjj.sz.gov.cn";
    /** status */
    public $errcode;
    /** message */
    public $errmsg;
    public $openid = 'oLtYzuO3pf061iTnf1Z8nxRHXdxY';
    public $ea_area_code = '1044030901001225';
    public $vote_id = '3d3ee10c-621d-11f0-95a6-61c5b31e69c7';
    public $village_id = 76642395;

    public $_retry = false;//接口地址
    public $ignore_log = false;//是否不记录log

    /**
     * ZhuTongSms constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
    }

    //获取默认从众规则
    public function get_default_conformity($open_id)
    {
        $url    = '/wxjwx/wx/weiXinCongZhongService/getDefaultConformity.json';
        $data   = ['open_id' => $open_id];
        $result = $this->request($url, $data, false);
        return (bool)$result['is_conformity'];
    }

    public function clear()
    {
        $this->ignore_log = false;
    }

    public function ignore_log()
    {
        $this->ignore_log = true;
        return $this;
    }

    public function get_list_ea_area_building_no()
    {
        $data = [
            'openId'     => $this->openid,
            'eaAreaCode' => $this->ea_area_code,
        ];
        $url  = '/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingNO';
        return $this->request($url, $data);
//        array(15) {
//        [0] => array(12) {
//            ["EaAreaHouseTotal"] => int(150)
//            ["BindEaAreaHouseSuccessTotal"] => int(95)
//            ["BindEaAreaOwnerSuccessTotal"] => NULL
//            ["EaAreaHouseTotalRate"] => NULL
//            ["BuildingNo"] => string(8) "77142566"
//            ["BuildingName"] => string(28) "锦绣御园（一期）1栋"
//            ["HouseNo"] => NULL
//            ["BindOfOwnerName"] => NULL
//            ["CertificateCode"] => NULL
//            ["BuildOrder"] => string(1) "1"
//            ["FloorNum"] => NULL
//            ["Sort"] => NULL
//  }
//  [1] => array(12) {
//            ["EaAreaHouseTotal"] => int(54)
//            ["BindEaAreaHouseSuccessTotal"] => int(39)
//            ["BindEaAreaOwnerSuccessTotal"] => NULL
//            ["EaAreaHouseTotalRate"] => NULL
//            ["BuildingNo"] => string(8) "77142567"
//            ["BuildingName"] => string(28) "锦绣御园（一期）2栋"
//            ["HouseNo"] => NULL
//            ["BindOfOwnerName"] => NULL
//            ["CertificateCode"] => NULL
//            ["BuildOrder"] => string(1) "2"
//            ["FloorNum"] => NULL
//            ["Sort"] => NULL
//  }
    }


    //获取绑定详情
    public function get_list_ea_area_bind($ea_area_code = '')
    {
        $data = [
            'openId'     => '',
            //            'openId'     => $this->openid,
            'eaAreaCode' => $ea_area_code ?: $this->ea_area_code,
        ];
        $url  = '/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBind';
        return $this->request($url, $data, false);
        // ["EaAreaHouseTotal"] => int(2317) //总户数
        // ["BindEaAreaHouseSuccessTotal"] => int(1666) //已绑定户数
        // ["BindEaAreaOwnerSuccessTotal"] => int(1507) //已绑定人数
        // ["EaAreaHouseTotalRate"] => string(5) "71.90" //绑定率
        // ["BuildingNo"] => NULL
        // ["BuildingName"] => NULL
        // ["HouseNo"] => NULL
        // ["BindOfOwnerName"] => NULL
        // ["CertificateCode"] => NULL
        // ["BuildOrder"] => NULL
        // ["FloorNum"] => NULL
        // ["Sort"] => NULL
    }

    public function update_bind_list()
    {
        $db_xiao_qu_building = new XiaoQuBuilding();
        $building_list       = $db_xiao_qu_building->get_building_list();
        $cookies             = get_system_config('zzj_cookie');
        $url                 = 'https://zjj.sz.gov.cn/wxjwx/wx/weixinNewService/getHouseListByBuildingId.json?date=' . time();
        $url_data_array      = [];
        foreach ($building_list as $building_id => $building_name) {
            $url_data_array[] = [
                'url'  => $url,
                'data' => [
                    'building_id'   => $building_id,
                    'building_name' => $building_name,
                    'village_id'    => $this->village_id,
                    'vote_id'       => $this->vote_id,
                ],
            ];
        }
        $result_array         = curl()->ignore_log()->set_cookies($cookies)->form_params()->multi_post($url_data_array);
        $db_xiao_qu_bind_info = new XiaoQuBindInfo();
        $exists_bind_id_array = $db_xiao_qu_bind_info->column('bind_id'); //已经存在的绑定id
        $i                    = 0;
        foreach ($result_array as $result) {
            if ($result['success'] !== true) {
                $msg = '请求异常' . $result['response'];
                wr_log(__LINE__ . '行数,' . $msg, 1);
                //只处理部分数据
                continue;
//                throw new \Exception('请求异常' . $result['response']);
            }
            $response      = $result['response'];
            $request_data  = $result['data'];
            $building_name = $request_data['building_name'];
            $building_id   = $request_data['building_id'];
            foreach ($response['dateList'] as $key => $val) {
//                ["house_no"] => string(3) "10A"
//                ["bind_id"] => string(36) "66e4e68a-07e5-4c73-972f-d8f2c404349f"
//                ["house_id"] => string(9) "173141989"
//                ["person_name"] => string(7) "庄*明"
                $house_no    = $val['house_no']; //房号 10D
                $house_id    = $val['house_id']; //房  173142357
                $bind_id     = $val['bind_id']; //绑定唯一id  "66e4e68a-07e5-4c73-972f-d8f2c404349f"
                $person_name = $val['person_name']; //投票日期 2024-08-15 没有则为NULL
                if (in_array($bind_id, $exists_bind_id_array) || $this->is_shang_pu_house_no($house_no)) {
                    //已经存在的绑定记录 不再更新
                    continue;
                }
                $insert_data          = [
                    'bind_id'     => $bind_id,
                    'house_id'    => $house_id,
                    'person_name' => $person_name,
                ];
                $db_xiao_qu_bind_info = new XiaoQuBindInfo();
                $db_xiao_qu_bind_info->save($insert_data);
                $i++;
            }
        }
        $i && wr_log('新增' . $i . '户绑定记录');
        return $i;
    }

    public function update_new_vote_house_list()
    {
        $db_xiao_qu                     = new XiaoQuHouse();
        $map_xiao_qu                    = [['vote_source', '<>', '']];
        $has_vote_source_house_id_array = $db_xiao_qu->where($map_xiao_qu)->column('house_id');

        $result                                    = $this->get_official_voted_info(); //获取正式票人数 户数
        $electronic_official_voting_of_house_total = $result['official_voted_house_num'];

        if (count($has_vote_source_house_id_array) >= $electronic_official_voting_of_house_total) {
            return 0;
        }
        $db_xiao_qu_building = new XiaoQuBuilding();
        $building_list       = $db_xiao_qu_building->get_building_list();
        $url                 = 'https://zjj.sz.gov.cn/wxjwx/wx/weixinNewService/getVoteHouseList.json?date=' . time();
        $url_data_array      = [];
        foreach ($building_list as $building_id => $building_name) {
            $url_data_array[] = [
                'url'  => $url,
                'data' => [
                    'building_id'   => $building_id,
                    'searchContent' => '',
                    'village_id'    => $this->village_id,
                    'vote_id'       => $this->vote_id,
                ],
            ];
        }
        $result_array = curl()->ignore_log()->form_params()->multi_post($url_data_array);
        $i            = 0;
        foreach ($result_array as $result) {
            if ($result['success'] !== true) {
                $msg = '请求异常' . $result['response'];
                wr_log(__LINE__ . '行数,' . $msg, 1);
                //只处理部分数据
                continue;
//                throw new \Exception('请求异常' . $result['response']);
            }
            $response     = $result['response'];
            $request_data = $result['data'];
            foreach ($response['dateList'] as $key => $val) {
                $house_no    = $val['house_no']; //房号 10D
                $house_id    = $val['house_id']; //房  173142357
                $vote_date   = $val['vote_date']; //投票日期 2024-08-15 没有则为NULL
                $vote_time   = $val['vote_time']; //投票时间 09:59:08 没有则为NULL
                $vote_source = $val['vote_source']; //'06006001'  没有则为NULL
                if (in_array($house_id, $has_vote_source_house_id_array) || $this->is_shang_pu_house_no($this->is_shang_pu_house_no($house_no))) {
                    //已经有投票数据 不再更新
                    continue;
                }
                $db_xiao_qu  = new XiaoQuHouse();
                $map_xiao_qu = [
                    ['house_id', '=', $house_id],
                    ['vote_source', '=', ''],
                ];
                $update_data = [];
                if ($vote_source) {
                    $update_data['vote_source']    = $vote_source;
                    $update_data['vote_date_time'] = $vote_date . ' ' . $vote_time;
                }
                if ($update_data) {
                    $i++;
                    $db_xiao_qu::update($update_data, $map_xiao_qu);
                }
            }

        }
        $i && wr_log('新增' . $i . '户投票');
        return $i;
    }

    public function update_default_conformity()
    {
        $db   = new XiaoQuHouse();
        $map  = [
            ['default_conformity', '=', -1],
            ['openid', '<>', ''],
        ];
        $list = $db->where($map)->limit(2)->field(['openid', 'id'])->order(['update_time' => 'DESC'])->select();
        $url  = 'https://zjj.sz.gov.cn/wxjwx/wx/weixinBasService/getOwnerInfo.json';
        foreach ($list as $val) {
            $default_conformity = (int)$this->get_default_conformity($val['openid']);
            $db                 = new XiaoQuHouse();
            $map                = [
                ['id', '=', $val['id']]
            ];
            $update_data        = ['default_conformity' => $default_conformity];
            $result             = $db::update($update_data, $map);
            usleep(100000); // 0.1秒等于100,000微秒
        }
    }

    public function update_user_info()
    {
        $db   = new XiaoQuHouse();
        $map  = [
            ['certificate_code_full', 'NULL', NULL],
            ['openid', '<>', ''],
        ];
        $i    = 0;
        $list = $db->where($map)->limit(20)->field(['openid', 'id'])->order(['update_time' => 'DESC'])->select();
        $url  = 'https://zjj.sz.gov.cn/wxjwx/wx/weixinBasService/getOwnerInfo.json';
        foreach ($list as $val) {
            $data   = [
                'openid'        => $val['openid'],
                'owner_face_rs' => ''
                //            ["offline"] => string(1) "0"
                //    ["certificate_code"] => string(18) "440306198510233115"
                //    ["cert_no"] => NULL
                //    ["bindtime"] => string(19) "2024-08-18 10:44:00"
                //    ["openid"] => string(28) "oLtYzuJUc6iNUF-k6WNHW42r3824"
                //    ["person_name"] => string(9) "黄宇生"
                //    ["phone_num"] => string(11) "13823333017"
                //    ["certificate_type"] => string(8) "02015001"
                //    ["qualifi"] => string(1) "1"
            ];
            $result = curl()->form_params()->post($url, $data)->get_body();
            $db     = new XiaoQuHouse();
            $map    = [
                ['id', '=', $val['id']]
            ];
            if (is_array($result)) {
                $update_data = [
                    'user_info'             => $result,
                    'phone_num_full'        => $result['phone_num'],
                    'person_name_full'      => $result['person_name'],
                    'certificate_code_full' => $result['certificate_code'],
                ];
            } else {
                $update_data = ['certificate_code_full' => '0'];
            }
            $result = $db::update($update_data, $map);
            $i++;
        }
        return $i;
    }

    public function update_bind_data_and_update_openid()
    {
        $url                 = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingHouseNO';
        $db_xiao_qu_building = new XiaoQuBuilding();
        $building_list       = $db_xiao_qu_building->get_building_list();
        $url_data_array      = [];
        foreach ($building_list as $building_id => $building_name) {
            $url_data_array[] = [
                'url'  => $url,
                'data' => [
                    'openId'       => $this->openid,
                    'eaAreaCode'   => $this->ea_area_code,
                    'buildingName' => $building_name,
                ],
            ];
        }
        $cookies        = get_system_config('zzj_cookie');
        $result_array   = curl()->ignore_log()->set_cookies($cookies)->form_params()->multi_post($url_data_array);
        $db_xiao_qu     = new XiaoQuHouse();
        $map            = [
            ['openid', '<>', ''],
        ];
        $list           = $db_xiao_qu->field(['building', 'room_number'])->where($map)->select();
        $building_array = [];
        foreach ($list as $val) {
            $building_array[] = $val['building'] . $val['room_number'];
        }
        $i = 0;
        foreach ($result_array as $result) {
            if ($result['success'] !== true) {
                $msg = '请求异常' . $result['response'];
                wr_log(__LINE__ . '行数,' . $msg, 1);
                //只处理部分数据
                continue;
//                throw new \Exception('请求异常' . $result['response']);
            }

            $response     = $result['response'];
            $request_data = $result['data'];

            foreach ($response['List'] as $key => $val) {
                $house_no      = $val['HouseNo']; //房号
                $building_name = $val['BuildingName']; //房号
                $house_info    = $this->analyze_house_info($building_name, $house_no);
                $building      = $house_info['building_name']; //处理后
                $room_number   = $house_info['room_number']; //处理后
                if (in_array($building . $room_number, $building_array) || $this->is_shang_pu_house_no($house_no)) {
                    //纯数字或者字母开头但是不包含中文 是商铺
                    continue;
                }
                $openid = $val['CertificateCode'] ?: '';
                if ($openid) {
                    $update_data = ['openid' => $openid];
                    $db_xiao_qu  = new XiaoQuHouse();
                    $map         = [
                        ['room_number', '=', $room_number],
                        ['building', '=', $building],
                        ['openid', '=', ''],
                    ];
                    $db_xiao_qu::update($update_data, $map);
                    $i++;
                }
            }
        }
        $i && wr_log('新增' . $i . '户openid更新', 1);
        return $i;
    }

    //获取正式票情况
    public function get_official_voted_house_num()
    {
        $data   = [
            'openId'     => $this->openid,
            'eaAreaCode' => $this->ea_area_code,
        ];
        $url    = '/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListElectronicVoting';
        $result = $this->request($url, $data);
        return $result[0]['ElectronicVotingOfHouseTotal'] ?? false;
        //    ["ElectronicVotingOfOwnerTotal"] => int(984)  //正式票人数
        //    ["ElectronicVotingOfHouseTotal"] => int(1094)  //正式票户数
        //    ["ElectronicVotingTotal"] => int(1)
        //    ["VoteTaskTotal"] => int(4)
    }

    public function get_census_info()
    {
        $url    = '/szwywx/MenuControllerSvr.assx/GetCensusInfo';
        $data   = [
            'openId'    => $this->openid,
            'villageId' => $this->village_id,
        ];
        $result = $this->request($url, $data);
        return [
            'total_voted_house_num'  => $result['voted_house_num'], //总投票户数
            'total_voted_house_area' => $result['voted_house_area'] //总面积
        ];
//        {
//   "Name": "小区",
//	"VoteUse": 1459, //累计投票户数
//	"HouseBind": 1713,//累计绑定
//	"EvaCount": 413,//评价数
//	"NumCount": 2317,//总计户数
//	"Type": 2,
//	"DeptName": "业主"
//}
    }

    public function get_total_voted_info()
    {
        $url    = '/wxjwx/wx/weixinNewService/getVote.json';
        $data   = [
            'openid'     => $this->openid,
            'vote_id'    => $this->vote_id,
            'village_id' => $this->village_id,
            'pageno'     => 1,
            'pagesize'   => 1
        ];
        $result = $this->request($url, $data, false);
        return [
            'total_voted_house_num'  => $result['voted_house_num'], //总投票户数
            'total_voted_house_area' => $result['voted_house_area'] //总面积
        ];
    }

    public function get_total_voted_info_v2()
    {
        //临时写死
//        return [
//            'total_voted_house_num'  => 1657, //投票户数
//            'total_voted_house_area' => 158009, //投票面积
//        ];
        $url    = '/wxjwx/wx/OwnerVoteController.assx/getVoteListWaitVoteDetails.do';
        $data   = [
            'openId'      => $this->openid,
            'voteId'      => $this->vote_id,
            'delegater'   => 0,
            'delegaterId' => null
        ];
        $result = $this->request($url, $data);
        return [
            'total_voted_house_num'  => (int)$result['data']['voteHouseCount'], //投票户数
            'total_voted_house_area' => (int)$result['data']['voteHouseAreaCount'], //投票面积
        ];
    }

    //获取投票详情 有每个选项的票数 面积 等
    public function get_official_voted_info()
    {
        $url                      = '/wxjwx/wx/weixinNewService/getVoteTask.json';
        $data                     = [
            'openid'     => $this->openid,
            'vote_id'    => $this->vote_id,
            'village_id' => $this->village_id,
            'pageno'     => 1,
            'pagesize'   => 1
        ];
        $result                   = $this->request($url, $data);
        $official_voted_house_num = $this->get_official_voted_house_num();
        if ($official_voted_house_num === false) {
            throw new \Exception('获取正式投票户数失败');
        }
        return [
            'official_voted_house_num'  => $official_voted_house_num, //正式投票户数
            'official_voted_person_num' => (int)$result[0]['join_unit'], //正式投票人数
            'official_voted_house_area' => (int)$result[0]['join_area'], //正式投票面积
        ];
    }

    public function get_bind_info($house_id, $bind_id)
    {
        $url     = '/wxjwx/wx/weixinNewService/getBindInfo.json';
        $vote_id = $this->vote_id;
        $data    = [
            'vote_id'  => $vote_id,
            'house_id' => $house_id,
            'bind_id'  => $bind_id,
        ];
        return $this->request($url, $data);
        //        "certificate_code": "4414**********2750",
//		"show_content": "多名业主推选出的投票人 ",
//		"create_time": "2021-03-29 09:42:56",
//		"is_election": "1",
//		"house_ticket_weight": "155",
//		"house_area": "155.47",
//		"mobile_phone": "139****8075",
//		"is_show": true
    }

    protected function request($url, array $data = [], $use_cookie = true)
    {
        $url     = self::API_URL . $url . '?date=' . time();
        $cookies = $use_cookie ? get_system_config('zzj_cookie') : '';
        $result  = curl()->set_timeout(5)->set_cookies($cookies)->ignore_log($this->ignore_log)->form_params()->post($url, $data)->get_body();
        $this->clear();
        return $this->_parseResult($result);
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (is_string($result)) {
            wr_log('住建局模拟登录异常,请及时更新cookie', 1);
            throw new NotNotifyException('系统繁忙,请稍候再试~');
            $this->errcode = -1;
            $this->errmsg  = '结果解析失败';
            return false;
        }
        return $result;
    }

    protected function is_shang_pu_house_no($house_no)
    {
        return (preg_match('/^\d+$/', $house_no) || preg_match('/^[a-zA-Z](?:[^\\x{4e00}-\\x{9fa5}])*$/u', $house_no));
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    public function analyze_house_info($building_name, $room_number)
    {
        $building_name = $this->str_replace_building($building_name);
        $list          = ['A座', 'B座', '1单元', '2单元'];
        foreach ($list as $v) {
            if (strpos($room_number, $v) !== false) {
                //房号包含单元, 则把单元增加到栋 房号删除单元
                $building_name .= $v;
                $room_number   = str_replace($v, '', $room_number);
                break;
            }
        }
        $floor          = $this->extractLeadingNumbers($room_number);
        $building_index = $this->extractLeadingNumbers($building_name);
        return [
            'building_name'  => $building_name,
            'room_number'    => $room_number,
            'floor'          => $floor,
            'building_index' => $building_index,
        ];
    }

    /**
     * @param $building
     * @return array|string|string[]
     */
    protected function str_replace_building($building)
    {
        $replace = ["锦绣御园(一期)", "锦绣御园(二期)", "锦绣御园(三期)"];
        return str_replace($replace, '', tools()::sbc2_dbc($building));
    }

    /**
     * 7栋A座 转换成 7
     * @param $str
     * @return string|null
     */
    protected function extractLeadingNumbers($str)
    {
        // 正则表达式解释：
        // ^\d+ 表示匹配字符串开头的一个或多个数字
        if (preg_match('/^\d+/', $str, $matches)) {
            return $matches[0]; // 返回匹配到的数字
        }
        return null; // 如果没有找到数字，则返回null
    }

}