<?php

namespace OpenApi;

use Exception;

class TencentDoc
{
    const API_URL = 'https://docs.qq.com';
    public $id;
    public $url;
    public $code;
    public $cookie;

    /**
     *   constructor.
     * @param $cookie
     */
    public function __construct($cookie = '')
    {
        $this->cookie = $cookie ?: get_system_config('tencent_doc_cookie');
    }

    /**
     * 模拟提交刷脸活动报名xyf
     * @return bool|array
     * @throws Exception
     */
    public function submit_test()
    {
        $url    = 'https://docs.qq.com/form/page/DZnd4dkR2bnVUTHlP';
        $answer = [
            format_timestamp(), //服务商名称（如为渠道商则填渠道商名）*
            '229806337', //服务商商户号（如为渠道商则填渠道号）*
        ];
        return $this->url($url)->collect_answer($answer);
    }

    /**
     * 模拟提交刷脸活动报名
     * @param array|string $merchant_id 商户号或商户号数组
     * @param array|string $merchant_name 商户名称或商户名称数组
     * @return bool|array
     * @throws Exception
     */
    public function submit_face_pay_active($merchant_id, $merchant_name)
    {
        //$url    = 'https://docs.qq.com/form/page/DR3RUVGtUZldXSUhl'; //3月
        //$url    = 'https://docs.qq.com/form/page/DREhjcldPdFVtQ3hH'; //4月
        $url    = 'https://docs.qq.com/form/page/DRERJbE5MS29MSW5J'; //5月
        $answer = [
            '深圳一卡易科技股份有限公司', //服务商名称（如为渠道商则填渠道商名）*
            '229806337', //服务商商户号（如为渠道商则填渠道号）*
            is_array($merchant_name) ? join(';', $merchant_name) : $merchant_name, //商户名称*  （如有多个商户名称，请用分号[；] 隔开；例如：1234534321；1237281923；1289301298）
            is_array($merchant_id) ? join(';', $merchant_id) : $merchant_id, //子商户号*  （如有多个商户号，请用分号[；] 隔开；例如：1234534321；1237281923；1289301298）
            '小屏人脸设备（单屏、双屏）', //设备类型（如：青蛙PRO/青蛙/桌面单屏刷脸设备/桌面双屏POS设备/自助大屏）*
            //           'SDK', //当前设备版本版本（APP/SDK）*
        ];
        return $this->url($url)->collect_answer($answer);
    }

    /**
     * 模拟提交问卷
     * @param array $answer 答案数据包
     * @return bool|array
     */
    public function collect_answer(array $answer)
    {
        $url          = '/form/collect/submit';
        $survey_pages = $this->get_survey_pages();
        foreach ($survey_pages['data'] as $key => $val) {
            $survey_pages['data'][$key]['content'] = $answer[$key] ?? '';
        }
        $survey_pages['data'] = json_encode($survey_pages['data'], JSON_UNESCAPED_UNICODE);
        return $this->post($url, ['data' => $survey_pages], 'multipart');
    }

    /**
     * 获取默认数据后组装问卷答案
     * @return bool|array
     */
    public function get_survey_pages()
    {
        $cache_key = 'survey_pages:' . $this->id;
        if ($survey_pages = cache($cache_key)) {
            return $survey_pages;
        }
        $result = $this->get_survey();
        if ($result === false) {
            return false;
        }
        $initial_attributed_text = $result['initialAttributedText']['text'];
        $questions               = $initial_attributed_text['questions'];
        $sections                = $initial_attributed_text['sections'];
        $qlist                   = $sections[0]['qlist'];
        $global_padid            = $result['globalPadId'];
        $survey_pages            = [];
        //       foreach ($questions as $question) {
        //           if (isset($question['status']) && $question['status'] == 0) {
        //               continue;
        //           }
        //           $survey_pages[] = [
        //               'id'       => $question['id'],
        //               'type'     => $question['type'],
        //               'content'  => '',
        //               'dataType' => $question['dataType'] ?? 1,
        //           ];
        //       }
        foreach ($qlist as $val) {
            $survey_pages[] = [
                'id'       => $val,
                'type'     => 'SIMPLE',
                'content'  => '',
                'dataType' => 1,
            ];
        }
        $result = [
            'global_padid' => $global_padid,
            'data'         => $survey_pages
        ];
        cache($cache_key, $result, 3600);
        return $result;
    }

    /**
     * 获取默认数据
     * @return bool|array
     */
    public function get_survey()
    {
        $url = '/form/data/get?id=' . $this->id . '&normal=1';
        return $this->post($url);
    }

    /**
     * 获取用户信息
     * @return bool|array
     */
    public function get_user_info()
    {
        $url = '/cgi-bin/online_docs/user_info';
        return $this->post($url);
    }


    /**
     * HTTP GET
     * @param string $cookie cookies
     * @return self
     * @throws Exception
     *
     */
    public function set_cookie(string $cookie)
    {
        $this->cookie = $cookie;
        $user_info    = $this->get_user_info();
        if ($user_info === false) {
            throw new Exception('cookies已失效');
        }
        return $this;
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!is_array($result)) {
            $this->code = 'result error';
            return false;
        }
        $result = tools()::json_string_to_array($result);
        if ((isset($result['retcode']) && $result['retcode'] !== 0) || (isset($result['code']) && $result['code'] !== 0)) {
            $this->code = $result['retcode'] ?? $result['code'] ?? -1;
            return false;
        }
        return $result['data'] ?? $result;
    }

    /**
     * HTTP GET
     * @param string $url 请求的url
     * @return bool|array
     */
    public function get(string $url)
    {
        $result = curl()->set_cookies($this->cookie)->set_header(['Referer' => self::API_URL])->get(self::API_URL . $url)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * HTTP POST请求
     * @param string $url 请求的url
     * @param array $data 请求的数据
     * @param string $data_type 数据类型
     * @return bool|array
     * @throws Exception
     */
    public function post(string $url, array $data = [], $data_type = 'json')
    {
        $result = curl()->setDataType($data_type)->set_cookies($this->cookie)->set_header(['Referer' => self::API_URL])->post(self::API_URL . $url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 设置url
     * @param string $url
     * @return $this
     * @throws Exception
     */
    public function url(string $url)
    {
        $array = explode('page/', $url);
        if (empty($array[1])) {
            throw new Exception('URL链接不是腾讯文档链接'); // https://docs.qq.com/form/page/DR3RUVGtUZldXSUhl
        }
        $this->id  = $array[1];
        $this->url = $url;
        return $this;
    }
}