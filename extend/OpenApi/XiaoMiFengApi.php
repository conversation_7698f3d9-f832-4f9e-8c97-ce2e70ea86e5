<?php

namespace OpenApi;

class XiaoMiFengApi
{
    const  API_URL = 'https://openapi.jcthy.com';

    public $errcode;
    public $errmsg;
    public $_retry = false;//接口地址
    private $appid = '12fa85c810ab3cb492ac5540992a9dc0';
    private $secret = '7d6a5ea684aa452da7773e48cb058b81';

    /**
     *  constructor.
     * @param array $config
     */
    public function __construct($config = [])
    {

    }

    /**
     * @param array $data
     * @return string
     */
    protected function get_sign(array $data)
    {
        ksort($data);
        $sign_str = '';
        foreach ($data as $key => $val) {
            if ($val !== '' && !is_null($val)) {
                $sign_str .= $key . $val;
            }
        }
        $sign_str .= $this->secret;
        $sign     = md5($sign_str);
        return strtolower($sign);
    }

    protected function get(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function post(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function request(string $url, array $data = [], $method = 'get')
    {
        $url             = self::API_URL . $url;
        $data['api_key'] = $this->appid;
        $data['time']    = time();
        $data['sign']    = $this->get_sign($data);
        $result          = curl()->$method($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    //资产余额
    public function user_account()
    {
        $url = '/open/api/user/account';
        return $this->get($url);
    }

    public function depth($symbol, $limit = 100)
    {
        $url  = '/sapi/v1/depth';
        $data = [
            'symbol' => $symbol . 'usdt',
            'limit'  => $limit,
        ];
        return $this->get($url, $data);
    }

    public function ticker($symbol)
    {
        $url  = '/sapi/v1/ticker';
        $data = [
            'symbol' => $symbol . 'usdt',
        ];
        return $this->get($url, $data);
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] !== '0') {
            $this->errcode = $result['code'];
            $this->errmsg  = $result['msg'];
            return false;
        }
        return $result['data'] ?? $result;
    }


    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }
}