<?php

namespace OpenApi;

/**
 *
 * @package     Application
 * <AUTHOR>
 * @version     Email.php  2017-07-16 09:52:16Z
 */
class ReceiveEmail
{

    public $errcode;
    public $errmsg;
    /**
     * @var resource $_connect
     */
    private $_connect;
    /**
     * @var object $_mailInfo
     */
    private $_mailInfo;
    private $_contentType;

    /**
     * __construct of the class
     */
    public function __construct()
    {
        $this->_contentType = array(
            'ez'      => 'application/andrew-inset', 'hqx' => 'application/mac-binhex40',
            'cpt'     => 'application/mac-compactpro', 'doc' => 'application/msword',
            'bin'     => 'application/octet-stream', 'dms' => 'application/octet-stream',
            'lha'     => 'application/octet-stream', 'lzh' => 'application/octet-stream',
            'exe'     => 'application/octet-stream', 'class' => 'application/octet-stream',
            'so'      => 'application/octet-stream', 'dll' => 'application/octet-stream',
            'oda'     => 'application/oda', 'pdf' => 'application/pdf',
            'ai'      => 'application/postscript', 'eps' => 'application/postscript',
            'ps'      => 'application/postscript', 'smi' => 'application/smil',
            'smil'    => 'application/smil', 'mif' => 'application/vnd.mif',
            'xls'     => 'application/vnd.ms-excel', 'xlsx' => 'application/vnd.ms-excel',
            'ppt'     => 'application/vnd.ms-powerpoint', 'pptx' => 'application/vnd.ms-powerpoint',
            'wbxml'   => 'application/vnd.wap.wbxml', 'wmlc' => 'application/vnd.wap.wmlc',
            'wmlsc'   => 'application/vnd.wap.wmlscriptc', 'bcpio' => 'application/x-bcpio',
            'vcd'     => 'application/x-cdlink', 'pgn' => 'application/x-chess-pgn',
            'cpio'    => 'application/x-cpio', 'csh' => 'application/x-csh',
            'dcr'     => 'application/x-director', 'dir' => 'application/x-director',
            'dxr'     => 'application/x-director', 'dvi' => 'application/x-dvi',
            'spl'     => 'application/x-futuresplash', 'gtar' => 'application/x-gtar',
            'hdf'     => 'application/x-hdf', 'js' => 'application/x-javascript',
            'skp'     => 'application/x-koan', 'skd' => 'application/x-koan',
            'skt'     => 'application/x-koan', 'skm' => 'application/x-koan',
            'latex'   => 'application/x-latex', 'nc' => 'application/x-netcdf',
            'cdf'     => 'application/x-netcdf', 'sh' => 'application/x-sh',
            'shar'    => 'application/x-shar', 'swf' => 'application/x-shockwave-flash',
            'sit'     => 'application/x-stuffit', 'sv4cpio' => 'application/x-sv4cpio',
            'sv4crc'  => 'application/x-sv4crc', 'tar' => 'application/x-tar',
            'tcl'     => 'application/x-tcl', 'tex' => 'application/x-tex',
            'texinfo' => 'application/x-texinfo', 'texi' => 'application/x-texinfo',
            't'       => 'application/x-troff', 'tr' => 'application/x-troff',
            'roff'    => 'application/x-troff', 'man' => 'application/x-troff-man',
            'me'      => 'application/x-troff-me', 'ms' => 'application/x-troff-ms',
            'ustar'   => 'application/x-ustar', 'src' => 'application/x-wais-source',
            'xhtml'   => 'application/xhtml+xml', 'xht' => 'application/xhtml+xml',
            'zip'     => 'application/zip', 'au' => 'audio/basic', 'snd' => 'audio/basic',
            'mid'     => 'audio/midi', 'midi' => 'audio/midi', 'kar' => 'audio/midi',
            'mpga'    => 'audio/mpeg', 'mp2' => 'audio/mpeg', 'mp3' => 'audio/mpeg',
            'aif'     => 'audio/x-aiff', 'aiff' => 'audio/x-aiff', 'aifc' => 'audio/x-aiff',
            'm3u'     => 'audio/x-mpegurl', 'ram' => 'audio/x-pn-realaudio', 'rm' => 'audio/x-pn-realaudio',
            'rpm'     => 'audio/x-pn-realaudio-plugin', 'ra' => 'audio/x-realaudio',
            'wav'     => 'audio/x-wav', 'pdb' => 'chemical/x-pdb', 'xyz' => 'chemical/x-xyz',
            'bmp'     => 'image/bmp', 'gif' => 'image/gif', 'ief' => 'image/ief',
            'jpeg'    => 'image/jpeg', 'jpg' => 'image/jpeg', 'jpe' => 'image/jpeg',
            'png'     => 'image/png', 'tiff' => 'image/tiff', 'tif' => 'image/tiff',
            'djvu'    => 'image/vnd.djvu', 'djv' => 'image/vnd.djvu', 'wbmp' => 'image/vnd.wap.wbmp',
            'ras'     => 'image/x-cmu-raster', 'pnm' => 'image/x-portable-anymap',
            'pbm'     => 'image/x-portable-bitmap', 'pgm' => 'image/x-portable-graymap',
            'ppm'     => 'image/x-portable-pixmap', 'rgb' => 'image/x-rgb', 'xbm' => 'image/x-xbitmap',
            'xpm'     => 'image/x-xpixmap', 'xwd' => 'image/x-xwindowdump', 'igs' => 'model/iges',
            'iges'    => 'model/iges', 'msh' => 'model/mesh', 'mesh' => 'model/mesh',
            'silo'    => 'model/mesh', 'wrl' => 'model/vrml', 'vrml' => 'model/vrml',
            'css'     => 'text/css', 'html' => 'text/html', 'htm' => 'text/html',
            'asc'     => 'text/plain', 'txt' => 'text/plain', 'rtx' => 'text/richtext',
            'rtf'     => 'text/rtf', 'sgml' => 'text/sgml', 'sgm' => 'text/sgml',
            'tsv'     => 'text/tab-separated-values', 'wml' => 'text/vnd.wap.wml',
            'wmls'    => 'text/vnd.wap.wmlscript', 'etx' => 'text/x-setext',
            'xsl'     => 'text/xml', 'xml' => 'text/xml', 'mpeg' => 'video/mpeg',
            'mpg'     => 'video/mpeg', 'mpe' => 'video/mpeg', 'qt' => 'video/quicktime',
            'mov'     => 'video/quicktime', 'mxu' => 'video/vnd.mpegurl', 'avi' => 'video/x-msvideo',
            'movie'   => 'video/x-sgi-movie', 'ice' => 'x-conference/x-cooltalk',
            'rar'     => 'application/x-rar-compressed', 'zip' => 'application/x-zip-compressed',
            '*'       => 'application/octet-stream', 'docx' => 'application/msword',
        );
        $this->errcode      = 0;
    }

    /**
     * Open an IMAP stream to a mailbox
     *
     * @param string $host
     * @param string $port
     * @param string $user
     * @param string $pass
     * @return resource|bool
     */
    public function mailConnect($host, $port, $user, $pass, $ssl = false)
    {
        $this->_connect = imap_open("{" . "$host:$port" . ($ssl ? "/ssl" : "") . "}INBOX", $user, $pass);
        if (!$this->_connect) {
            $this->errmsg = 'cannot connect: ' . imap_last_error();
            return false;
        }
        return $this->_connect;
    }

    /**
     * Get information about the current mailbox
     *
     * @return object|bool
     */
    public function mailInfo()
    {
        $this->_mailInfo = imap_mailboxmsginfo($this->_connect);
        if (!$this->_mailInfo) {
            $this->errmsg = "get mailInfo failed: " . imap_last_error();
            return false;
        }
        return $this->_mailInfo;
    }

    /**
     * Read an overview of the information in the headers of the given message
     *
     * @param string $msgRange
     * @return array
     */
    public function mailList($msgRange = '')
    {
        if ($msgRange) {
            $range = $msgRange;
        } else {
            $range = "1:" . $this->mailTotalCount();
        }
        $overview = imap_fetch_overview($this->_connect, $range);
        foreach ($overview as $val) {
            $mailList[$val->msgno] = (array)$val;
        }
        return $this->decode($mailList);
    }

    /**
     * get the total count of the current mailbox
     *
     * @return int
     */
    public function mailTotalCount()
    {
        return imap_num_msg($this->_connect);
    }

    public function decode($arr)
    {
        foreach ($arr as $key => $val) {
            if (is_array($val)) {
                $arr[$key] = $this->decode($val);
            } else {
                $val       = imap_mime_header_decode($val)[0]->text;
                $arr[$key] = trim(tools()::array_iconv($val));
            }
        }
        return $arr;
    }

    /**
     * Read the header of the message
     *
     * @param string $msgCount
     * @return array
     */
    public function mailHeader($msgCount)
    {
        $header  = imap_header($this->_connect, $msgCount);
        $header  = tools()::object2array($header);
        $header  = $this->decode($header);
        $mailbox = strtolower($header['from'][0]['mailbox']);
        if ($mailbox != 'mailer-daemon' && $mailbox != 'postmaster') {
            $header['MailDate'] = date('Y-m-d H:i:s', strtotime($header['MailDate']));
            $header['date']     = date('Y-m-d H:i:s', strtotime($header['date']));
            $header['Date']     = date('Y-m-d H:i:s', strtotime($header['Date']));
        }
        return $header;
    }

    /**
     * Mark a message for deletion from current mailbox
     *
     * @param string $msgCount
     */
    public function mailDelete($msgCount)
    {
        return imap_delete($this->_connect, $msgCount);
    }

    function getAttach($msgCount, $path = '')
    {
        $struckture = imap_fetchstructure($this->_connect, $msgCount);
        $attach     = [];
        if ($struckture->parts) {
            foreach ($struckture->parts as $key => $value) {
                if ($struckture->parts[$key]->subtype == 'OCTET-STREAM') {
                    $name    = imap_mime_header_decode($struckture->parts[$key]->parameters[1]->value)[0]->text;
                    $message = imap_fetchbody($this->_connect, $msgCount, $key + 1);
                    $enc     = $struckture->parts[$key]->encoding;
                    switch ($enc) {
                        case 0:
                            $message = imap_8bit($message);
                            break;
                        case 1:
                            $message = imap_8bit($message);
                            break;
                        case 2:
                            $message = imap_binary($message);
                            break;
                        case 3:
                            $message = imap_base64($message);
                            break;
                        case 4:
                            $message = quoted_printable_decode($message);
                            break;
                        case 5:
                            $message = $message;
                            break;
                    }
                    // 文件名转换,注意有可能文件名不带.,判断下防止程序异常
                    $name = tools()::array_iconv($name);
                    $name = explode('.', $name);
                    if (!empty($name[0]) && !empty($name[1])) {
                        //$firs_name = urlencode($name[0]);
                        $filename = $name[0] . '.' . $name[1];
                        $this->downAttach($path, $filename, $message);
                        $attach[] = $filename;
                    }
                }
            }
        }
        return $attach;
    }

    /**
     * download the attach of the mail to localhost
     *
     * @param string $path
     * @param string $message
     * @param string $name
     */
    public function downAttach($path, $name, $message)
    {
        if (!is_dir($path) && $path) {
            mkdir($path, 0755, true);
        }
        $fp = fopen($path . $name, "w");
        fwrite($fp, $message);
        return fclose($fp);
    }

    /**
     * click the attach link to download the attach
     *
     * @param string $id
     */
    public function getAttachData($id, $filePath, $fileName)
    {
        $nameArr = explode('.', $fileName);
        $length  = count($nameArr);
        if (empty($this->_contentType[$nameArr[$length - 1]])) {
            return false;
        }
        $contentType = $this->_contentType[$nameArr[$length - 1]];
        if (!$contentType) {
            $contentType = $this->_contentType['*'];
        }
        $filePath = chop($filePath);
        if ($filePath != '') {
            if (substr($filePath, strlen($filePath) - 1, strlen($filePath)) != '/') {
                $filePath .= '/';
            }
            $filePath .= $fileName;
        } else {
            $filePath = $fileName;
        }
        if (!file_exists($filePath)) {
            echo 'the file is not exsit';
            return false;
        }
        $fileSize = filesize($filePath);
        header("Content-type: " . $contentType);
        header("Accept-Range : byte ");
        header("Accept-Length: $fileSize");
        header("Content-Disposition: attachment; filename=" . $fileName);
        $fileOpen   = fopen($filePath, "r");
        $bufferSize = 1024;
        $curPos     = 0;
        while (!feof($fileOpen) && $fileSize - $curPos > $bufferSize) {
            $buffer = fread($fileOpen, $bufferSize);
            echo $buffer;
            $curPos += $bufferSize;
        }
        $buffer = fread($fileOpen, $fileSize - $curPos);
        echo $buffer;
        fclose($fileOpen);
        return true;
    }

    /**
     * get the body of the message
     *
     * @param string $msgCount
     * @return string
     */
    public function getBody($msgCount)
    {
        $body = $this->getPart($msgCount, "TEXT/HTML");
        if ($body == "") {
            $body = $this->getPart($msgCount, "TEXT/PLAIN");
        }
        if ($body == "") {
            return "";
        }
        return tools()::array_iconv($body);
    }

    /**
     * Read the structure of a particular message and fetch a particular
     * section of the body of the message
     *
     * @param string $msgCount
     * @param string $mimeType
     * @param object $structure
     * @param string $partNumber
     * @return string|bool
     */
    private function getPart($msgCount, $mimeType, $structure = false, $partNumber = false)
    {

        if (!$structure) {
            $structure = imap_fetchstructure($this->_connect, $msgCount);
        }
        if ($structure) {
            if ($mimeType == $this->getMimeType($structure)) {
                if (!$partNumber) {
                    $partNumber = "1";
                }
                $fromEncoding = $structure->parameters[0]->value;
                $text         = imap_fetchbody($this->_connect, $msgCount, $partNumber);
                if ($structure->encoding == 3) {
                    $text = imap_base64($text);
                } else if ($structure->encoding == 4) {
                    $text = imap_qprint($text);
                }
                $text = mb_convert_encoding($text, 'utf-8', $fromEncoding);
                return $text;
            }
            if ($structure->type == 1) {
                while (list($index, $subStructure) = $this->fun_adm_each($structure->parts)) {
                    $prefix = '';
                    if ($partNumber) {
                        $prefix = $partNumber . '.';
                    }
                    $data = $this->getPart($msgCount, $mimeType, $subStructure, $prefix . ($index + 1));
                    if ($data) {
                        return $data;
                    }
                }
            }
        }
        return false;
    }

    //php7.2下面替代each函数的方法

    /**
     * get the subtype and type of the message structure
     *
     * @param object $structure
     */
    private function getMimeType($structure)
    {
        $mimeType = array("TEXT", "MULTIPART", "MESSAGE", "APPLICATION", "AUDIO", "IMAGE", "VIDEO", "OTHER");
        if ($structure->subtype) {
            return $mimeType[(int)$structure->type] . '/' . $structure->subtype;
        }
        return "TEXT/PLAIN";
    }

    private function fun_adm_each(&$array)
    {
        $res = array();
        $key = key($array);
        if ($key !== null) {
            next($array);
            $res[1] = $res['value'] = $array[$key];
            $res[0] = $res['key'] = $key;
        } else {
            $res = false;
        }
        return $res;
    }

    /**
     * put the message from unread to read
     *
     * @param string $msgCount
     * @return bool
     */
    public function mailRead($msgCount)
    {
        $status = imap_setflag_full($this->_connect, $msgCount, "\\Seen");
        return $status;
    }

    /**
     * Close an IMAP stream
     */
    public function closeMail()
    {
        return imap_close($this->_connect, CL_EXPUNGE);
    }

}