<?php

namespace OpenApi;

use app\model\ImgMap;
use SplFileObject;
use think\Exception;
use think\Image;

class Swiftpass
{
    /** 接口基础地址 */
    const BASE_URL = 'https://ceb.swiftpass.cn/';
    /** 登录账号 */
    public $username;
    /** 登录密码 */
    public $password;
    /** status */
    public $status;
    /** message */
    public $errmsg;
    public $_retry = false;
    protected $cookie;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $default_config = [
            'username' => 'chaxun',
            'password' => '111aaa...',
        ];
        $config         = array_merge($default_config, $config);
        $this->username = $config['username'];
        $this->password = $config['password'];
        $this->_retry   = false;
    }

    public function examine_activate($merchant_id)
    {
        $url    = self::BASE_URL . 'cms/base/merchant/examineActivate';
        $data   = [
            'merchantId'                         => $merchant_id,
            'merchantDetailDto.merchantDetailId' => $merchant_id,
            'examineStatus'                      => 1, //审核通过
            'activateStatus'                     => 1, //激活支付方式
            //           'channelId_tmp'                          => '105520000010',
            //           'merchantDetailDto.merchantShortName'    => '',
            //           'merchantType'                           => '',
            //           'salesmanId_tmp'                         => '',
            //           'salesmanId'                             => '',
            //           'feeType'                                => '',
            //           'merchantDetailDto.industrId_tmp'        => '',
            //           'merchantDetailDto.industrId'            => '',
            //           'merchantDetailDto.province'             => '',
            //           'merchantDetailDto.city'                 => '',
            //           'merchantDetailDto.county'               => '',
            //           'merchantDetailDto.address'              => '',
            //           'merchantDetailDto.tel'                  => '',
            //           'merchantDetailDto.email'                => '',
            //           'merchantDetailDto.webSite'              => '',
            //           'merchantDetailDto.principal'            => '',
            //           'merchantDetailDto.idCode'               => '',
            //           'merchantDetailDto.principalMobile'      => '',
            //           'merchantDetailDto.customerPhone'        => '',
            //           'merchantDetailDto.fax'                  => '',
            //           'mchDealType'                            => '',
            //           'merchantDetailDto.interfaceRefundAudit' => '',
            //           'merchantDetailDto.businessLicense'      => '',
            //           'mchDailyLimitBd'                        => '',
            //           'merchantDetailDto.licensePhoto'         => '',
            //           'merchantDetailDto.indentityPhoto'       => '',
            //           'merchantDetailDto.orgPhoto'             => '',
            //           'merchantDetailDto.protocolPhoto'        => '',
            //           'merchantDetailDto.mainPhoto'            => '',
            //           'merchantDetailDto.businessPlace'        => '',
            //           'merchantDetailDto.otherDoc'             => '',
            //           'merchantDetailDto.activateStatusRemark' => '',
        ];
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    protected function http_request($url, $data, $method = 'POST')
    {
        if (!$this->cookie && !$this->get_login_cookie()) {
            return false;
        }
        $result = curl()->set_cookies($this->cookie)->$method($url, $data)->get_body();
        if ($result === false) {
            $this->errmsg = __CLASS__ . __FUNCTION__ . '请求发生错误';
            return false;
        }
        return $this->_parseResult($result);
    }

    public function get_login_cookie($force = false)
    {
        $username     = $this->username;
        $password     = $this->password;
        $key          = 'swiftpass:login_cookie:' . $username;
        $cache_cookie = cache($key);
        if ($force === false && $cache_cookie) {
            return $this->cookie = $cache_cookie;
        }
        $url            = self::BASE_URL . 'login2';
        $data_arr       = [
            "userName" => $username,
            "password" => $password
        ];
        $request_result = curl()->post($url, $data_arr);
        $body           = $request_result->get_body();
        if ($this->_parseResult($body) !== false) {
            $this->cookie = $request_result->get_cookies();
            cache($key, $this->cookie, 600);
            return $this->cookie;
        }
        return false;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            return $result;
        }
        $result = json_decode($result, true);
        if (empty($result) || !is_array($result)) {
            $this->status = 'result error';
            $this->errmsg = '解析返回结果失败';
            return false;
        }
        if (isset($result['success']) && $result['success'] === false) {
            $this->status = isset($result['errorCode']) ? $result['errorCode'] : -1;
            $this->errmsg = $result['msg'];
            return false;
        }
        return $result;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            ($this->_retry = true) && $this->clear_cookies();
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    protected function clear_cookies($username = '')
    {
        if (!$username) {
            $username = $this->username;
        }
        return cache('swiftpass:login_cookie:' . $username, NULL);
    }

    //获得威富通后台登陆cookies

    public function batch_examine_activate($merchant_id_arr)
    {
        $url  = self::BASE_URL . 'cms/base/merchant/batchExamineActivate';
        $data = [];
        foreach ($merchant_id_arr as $merchant_id) {
            $data[] = [
                'merchantId'     => $merchant_id,
                'examineStatus'  => 1,
                'activateStatus' => 1,
            ];
        }
        $json                      = json_encode($data);
        $post_data['merchantDtos'] = $json;
        $result                    = $this->http_request($url, $post_data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function edit_mchPayConf($data)
    {
        $mchPayConf_info = $this->get_mchPayConf_info($data['merchantId']);
        if ($mchPayConf_info['partner'] == $data['partner']) {
            $this->errmsg = '修改失败,当前交易识别码已经是:' . $data['partner'];
            return false;
        }
        $msg = '商户号:' . $mchPayConf_info['merchantId'] . '的交易识别码正在尝试由' . $mchPayConf_info['partner'] . '修改为:' . $data['partner'];
        wr_log($msg, 1);
        $url         = self::BASE_URL . 'tra/base/mchPayConf/edit';
        $accountInfo = $this->get_bank_account($data['merchantId']);
        $data        = [
            "mchPayConfId"  => $mchPayConf_info['mchPayConfId'],
            "merchantId"    => $mchPayConf_info['merchantId'],
            //"merchantName"  => "一卡易谢永发",
            // "isSync"                       => "2",
            "oldAccountId"  => $accountInfo['accountId'],
            "accountId"     => $accountInfo['accountId'],
            // "payCenterName"                => "光大深圳-微信2通道",
            // "payCenterId"                  => "61",
            "payTypeId"     => "********", //代表 pay.weixin.jspay
            "billRateFt"    => $mchPayConf_info['billRateFt'],
            "preMinLimitFt" => "0.0",
            "preMaxLimitFt" => "0.0",
            "partnerOld"    => $data['partner'],//交易识别码
            "partner"       => $data['partner'], //交易识别码
            //"tradeChanMchid"   => "",
            //"limitCreditPay"   => "",
            //"rawState"         => "1",
            //"accountIdCode"    => "1",
            //"bankAccountDto.includeBankId" => "13",
            //"bankAccountDto.bankId"        => "13",
            //"bankAccountDto.accountType"   => "2",
            //"bankAccountDto.province"      => "190000",
            //"bankAccountDto.idCardType"    => "1",
            //"parentJsonArry"   => "",
            //"payTypeJsonArray" => "",
        ];
        $result      = $this->http_request($url, $data);
        return $result;
    }

    //编辑微信支付类型

    public function get_mchPayConf_info($merchant_id, $apiCode = 'pay.weixin.jspay')
    {
        $result = $this->get_mch_pay_conf_info($merchant_id);
        if ($result === false) {
            return false;
        }
        foreach ($result['rows'] as $key => $val) {
            if ($val['apiCode'] == $apiCode) {
                return $val;
            }
        }
        return false;
    }

    //自动添加支付通道

    public function get_mch_pay_conf_info($merchant_id)
    {
        $url    = self::BASE_URL . 'tra/base/mchPayConf/datagrid.json';
        $data   = [
            'merchantId' => $merchant_id,
            'page'       => 1,
            'rows'       => 1,
            'pageNumber' => 1,
            'pageSize'   => 50
        ];
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function get_bank_account($merchant_id)
    {
        //先通过商户号获得银行账户信息
        $url    = self::BASE_URL . 'tra/base/bankAccount/getMerchantUsingLatestBankAccount.json';
        $data   = ['orgId' => $merchant_id];
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function add_mchPayConf($merchant_id)
    {
        $bankAccount = $this->get_bank_account($merchant_id);
        $url         = self::BASE_URL . 'tra/base/mchPayConf/add';
        $pays        = [
            '98' => ['********', '********', '********'],
            '61' => ['********', '********', '********'],
        ];
        foreach ($pays as $key => $val) {
            $data = [
                'mchPayConfId'                 => '',
                'merchantId'                   => $merchant_id,
                'merchantName'                 => $bankAccount['orgId'],
                'isSync'                       => '1',
                'oldAccountId'                 => '',
                'accountId'                    => $bankAccount['accountId'],
                'payCenterId'                  => $key,//光大银行-支付宝2通道
                'payCenterId_tmp'              => $key,//光大银行-支付宝2通道
                'tradeChanMchid'               => '',
                'accountIdCode'                => '1',//关联已有卡
                'bankAccountDto.bankId'        => $bankAccount['bankId'],
                'bankAccountDto.includeBankId' => $bankAccount['bankId'],
                'bankAccountDto.accountType'   => $bankAccount['accountType'],
                'bankAccountDto.province'      => $bankAccount['province'],
                'bankAccountDto.idCardType'    => $bankAccount['idCardType'],
                'parentJsonArry'               => '',
            ];
            if ($key == 98) {
                $data['tradeChanMchid'] = '****************';//支付宝渠道号
            }
            foreach ($val as $k => $v) {
                $data['payTypeJsonArray'][] = [
                    'payTypeId'       => $v,//支付宝-js支付(光大银行)
                    "billRateBd"      => "6",
                    "preMinLimitFt"   => 0,
                    "preMaxLimitText" => 0,
                    "partner"         => "",
                    "limitCreditPay"  => "0"
                ];
            }
            $data['payTypeJsonArray'] = json_encode($data['payTypeJsonArray']);
            $result                   = $this->http_request($url, $data);
            if ($result === false) {
                wr_log('大商户号' . $merchant_id . '添加支付通道失败,返回信息:' . $this->errmsg, 1);
                return $this->checkRetry(__FUNCTION__, func_get_args());
            }
        }
        return true;
    }

    public function get_partnerid_from_swiftpass($merchant_id)
    {
        //下方是通过模拟登录的方法查找partnerid
        $partnerid = false;
        $url       = self::BASE_URL . 'cms/base/store/datagrid.json';
        $data      = [
            'parentMerchant_tmp' => $merchant_id,
            'parentMerchant'     => $merchant_id,
            'pageSize'           => "100",
            'page'               => "1",
            'pageNumber'         => "1",
            'rows'               => "100"
        ];
        $result    = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        $merchant_id_arr[] = $merchant_id;
        if (isset($result['total']) && $result['total'] > 0) {
            foreach ($result['rows'] as $key => $val) {
                $merchant_id_arr[] = $val['merchantId'];
            }
        }
        foreach ($merchant_id_arr as $val) {
            if (($mchPayConf_info = $this->get_mchPayConf_info($val)) !== false) {
                break;
            }
        }
        return $mchPayConf_info['partner'];
    }

    public function get_partnerid($merchant_id)
    {
        $api       = new Swiftpassapi();
        $partnerid = $api->mch_partner_search($merchant_id);
        if ($partnerid === false) {
            return $this->get_partnerid_from_conf_info($merchant_id);
        }
        return $partnerid;
    }

    public function get_partnerid_from_conf_info($merchant_id)
    {
        $url       = self::BASE_URL . 'cms/base/weixinDevConf/loadWeixinBindResult';
        $data      = [
            'partner'    => '',
            'merchantId' => $merchant_id
        ];
        $result    = $this->http_request($url, $data, 'GET');
        $partnerid = tools()::search_str("config_weixin_sub('", "'", $result);
        return is_numeric($partnerid) ? $partnerid : false;
    }

    public function get_payment_channel_activate_status($merchant_id)
    {
        $result = $this->get_mch_pay_conf_info($merchant_id);
        if ($result === false) {
            return false;
        }
        $this->errmsg = '';
        if ($result['total'] < 6) {
            $this->errmsg .= '支付类型缺失,仅有' . $result['total'] . "个\n";
            return -1;
        }
        foreach ($result['rows'] as $key => $val) {
            if ($val['activateStatus'] == '未激活') {
                $this->errmsg .= $val['apiCode'] . '-' . $val['payTypeId'] . "\n";
            }
        }
        return ($this->errmsg == '') ? 1 : 0;
    }

    public function mch_pay_conf_batch_activate($merchant_id)
    {
        $result = $this->get_mch_pay_conf_info($merchant_id);
        if ($result === false) {
            return false;
        }
        $array = [];
        if ($result['total'] == 0) {
            return false;
        }
        foreach ($result['rows'] as $key => $val) {
            if ($val['activateStatus'] == '未激活') {
                $array[] = [
                    'mchPayConfId'   => $val['mchPayConfId'],
                    'activateStatus' => 1,
                ];
            }
        }
        if (empty($array)) {
            $this->errmsg = '没有待激活的支付方式';
            return true;
        }
        $json                   = json_encode($array);
        $data['mchPayConfDtos'] = $json;
        $url                    = self::BASE_URL . 'tra/base/mchPayConf/batchActivate';
        $result                 = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //将图片下载到本地并且压缩并且上传到光大

    public function get_swiftpass_imgurl($imgurl)
    {
        $key = 'swiftpass:imgurl:' . $imgurl;
        if (cache($key)) {
            return cache($key);
        }
        //todo 下载到本地
        $local_file_name = curl()->set_sink('')->get($imgurl);
        if ($local_file_name === false) {
            $this->errmsg = '文件下载失败,请联系谢永发!';
            return false;
        }
        $file_obj  = new SplFileObject($local_file_name);
        $file_size = $file_obj->getSize();
        $image     = Image::open($local_file_name);
        // 返回图片的类型
        $type = $image->type();
        // 返回图片的mime类型
        $mime = $image->mime();
        for ($i = 0; $i <= 7; $i++) {
            if (($file_size < 1024 * 200)) {
                break;
            }
            $image->save($local_file_name, null, 80 - $i * 10);
            $file_obj  = new SplFileObject($local_file_name);
            $file_size = $file_obj->getSize();
        }
        if ($file_size > 1024 * 200) {
            $this->errmsg = '文件无法压缩到200kb以内!';
            return false;
        }
        $url    = self::BASE_URL . 'cms/base/merchant/uploadImg';
        $file   = curl_file_create($local_file_name, $mime, $type); // try adding
        $result = $this->http_request($url, ['accessory' => $file]);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        if (empty($result['obj'])) {
            $this->errmsg = '图片上传失败:' . $result['msg'] . '请联系谢永发';
            return false;
        }
        wr_log(format_timestamp() . '上传成功:' . $local_file_name);
        $this->cache_swiftpass_imgurl($imgurl, $result['obj']);
        return $result['obj'];
    }

    protected function cache_swiftpass_imgurl($local_img, $remote_img)
    {
        try {
            $db     = new ImgMap();
            $data   = [
                'guid'       => create_guid(),
                'local_img'  => $local_img,
                'remote_img' => $remote_img,
            ];
            $insert = $db->save($data);
            $key    = 'swiftpass:imgurl:' . $local_img;
            return cache($key, $remote_img, 3600 * 24);
        } catch (Exception $e) {

        }
    }

    public function get_detail($merchantId)
    {

        $url = self::BASE_URL . 'cms/base/merchant/detailUI?merchantId=' . $merchantId;
        return $this->http_request($url, [], 'GET');
    }

    public function get_merchantIds($data, $all = false)
    {
        $url          = self::BASE_URL . 'cms/base/merchant/datagrid.json';
        $default_data = [
            //           'channelId_tmp' => "105520000010",//不传代表查看权限内所有渠道
            //           'channelId'     => "105520000010",//不传代表查看权限内所有渠道
            'pageSize'   => "100",
            'page'       => "1",
            'pageNumber' => "1",
            'rows'       => "100"
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        $pages = $result['pages'];
        //如果有多页则循环获取
        if ($pages > 1 && $data['pageNumber'] == 1 && $all) {
            for ($i = 2; $i <= $pages; $i++) {
                $data['pageNumber'] = $i;
                $new_result         = $this->get_merchantIds($data);
                if ($new_result === false) {
                    break;
                }
                //循环将新的结果push到第一次结果中
                foreach ($new_result['rows'] as $key => $val) {
                    $result['rows'][] = $val;
                }
            }
        }
        return $result;
    }

    public function add_merchant_api($data)
    {
        $url    = self::BASE_URL . 'cms/base/merchant/add';
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //获取微信配置信息方法
    public function search_subdev_config($merchant_id = '', $partner = '')
    {
        $data   = [
            'partner'    => $partner,
            'merchantId' => $merchant_id,
        ];
        $url    = self::BASE_URL . 'cms/base/weixinDevConf/loadWeixinBindResult';
        $result = $this->http_request($url, $data, 'GET');
        $config = tools()::search_str('config_weixin_sub(', ')', $result);
        if ($config === false) {
            $this->errmsg = '该商户可能未同步,请稍后再试';
            return false;
        }
        $config = str_replace("'", '', $config);
        $config = explode(',', $config);
        if (empty($config[0]) || empty($config[1])) {
            $this->errmsg = '数据解析失败';
            return false;
        }
        $partner     = trim($config[0]);
        $payCenterId = trim($config[1]);
        $data        = [
            'partner'     => $partner,
            'payCenterId' => $payCenterId,
            'menuFlag'    => 'true',
            '_'           => time(),
        ];
        $url         = self::BASE_URL . 'cms/base/weixinDevConf/subConfUI';
        $result      = $this->http_request($url, $data, 'GET');
        if ($result === false) {
            $this->errmsg = 'weixinDevConf/subConfUI请求失败';
            return false;
        }
        $data = [
            'path'           => $result['weixinConf']['pathList'],
            'appid'          => $result['weixinConf']['appids'],
            'subscribeAppid' => $result['weixinConf']['subscribeAppid']
        ];
        return $data;
    }

    //添加微信支付目录方法
    public function add_wechat_pay_path($data)
    {
        //       $data  = [
        //           'partner'    => $config['partner'],
        //           'payCenterId' => $config['payCenterId'],
        //           'path'        => $pay_path,
        //       ];
        $url    = self::BASE_URL . 'cms/base/weixinDevConf/reqBindPath';
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //添加关注APPID方法
    public function add_subscribe_appid($data)
    {
        //       $data  = [
        //           'partner'        => $config['partner'],
        //           'payCenterId'    => $config['payCenterId'],
        //           'subscribeAppid' => $info['wxappid'],
        //       ];
        $url    = self::BASE_URL . 'cms/base/weixinDevConf/reqBindSubscribeAppid';
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //添加支付子商户APPID方法
    public function add_sub_appids($data)
    {
        //       $data  = [
        //           'partner'    => $config['partner'],
        //           'payCenterId' => $config['payCenterId'],
        //           'appid'      => $sub_appid,
        //       ];
        $url    = self::BASE_URL . 'cms/base/weixinDevConf/reqBindAppid';
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function get_cleaning_result($data = array())
    {
        $url          = self::BASE_URL . 'cle/cleaningResultQuery/resultClean';
        $default_data = [
            "cleanBeginDate" => date("Y-m-d", strtotime("-1 day")),
            "cleanEndDate"   => date("Y-m-d", strtotime("-1 day")),
            "cleaningStatus" => -1, //-1全部,3成功,4失败
            //"merchantId_tmp" => "", //商户号
            //"merchantId"     => "", //商户号
            //"payeeAccountCode"     => "", //收款账户
            //"serialNumber"     => "", //流水号
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result['obj'];
    }
}