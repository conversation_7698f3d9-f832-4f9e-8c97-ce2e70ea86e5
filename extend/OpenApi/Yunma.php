<?php

namespace OpenApi;

class Yunma
{
    /** 接口基础地址 */
    const BASE_URL = 'http://api.jfbym.com/api/YmServer/customApi';
    /** $token */
    public $token;
    public $type;
    /** errcode */
    public $errcode;
    /** errmsg */
    public $errmsg;
    public $result;
    public $text = null;


    /**
     * Yundama constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $default_options = [
            'token' => '2fv2c2HmMmC8BTdebWSdCnfTTwcUZJ5hex5WVYmIqJg',
        ];
        $options         = array_merge($default_options, $options);
        $this->token     = $options['token'];
    }

    public function image_to_base64($filename)
    {
        $imageDetails = getimagesize($filename);
        if ($fp = fopen($filename, "rb", 0)) {
            $picture = fread($fp, filesize($filename));
            fclose($fp);
            $base64 = chunk_split(base64_encode($picture));
            return $base64;
            return 'data:' . $imageDetails['mime'] . ';base64,' . $base64;
            //输出base64图片代码...
        }
    }

    function CJY_Post_base64($base64_str)
    {
        $base64_str = $this->image_to_base64($base64_str);
        $url        = 'http://upload.chaojiying.net/Upload/Processing.php';
        $fields     = array(
            'user'        => 'xieyongfa123',
            'pass'        => '111aaa...',
            'softid'      => '945944',
            'codetype'    => 1006,
            'file_base64' => $base64_str
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, count($fields));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 获取数据返回
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true); // 在启用 CURLOPT_RETURNTRANSFER 时候将获取数据返回
        curl_setopt($ch, CURLOPT_REFERER, '');
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.2.3) Gecko/20100401 Firefox/3.6.3');
        $result = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($result, true);
        dump($result);
        return $result['pic_str'] ?? '';
    }

    public function get_code($image)
    {
        $image = $this->image_to_base64($image);

        //        /**
        //         * 获取图片base64数据
        //         * @param ImageFile String 图片路径
        //         * @return base64数据
        //         */
        //        function Base64EncodeImage($ImageFile)
        //        {
        //            if (file_exists($ImageFile) || is_file($ImageFile)) {
        //                $base64_image = '';
        //                $image_data   = fread(fopen($ImageFile, 'r'), filesize($ImageFile));
        //                $base64_image = base64_encode($image_data);
        //                return $base64_image;
        //            } else {
        //                return '';
        //            }
        //        }
        //
        //        /**
        //         * 发送post请求
        //         * @param string $url 请求地址
        //         * @param array $post_data post键值对数据
        //         * @return string
        //         */
        //        function send_post($url, $post_data)
        //        {
        //            $postdata = http_build_query($post_data);
        //            $options  = array(
        //                'http' => array(
        //                    'method'  => 'POST',
        //                    'header'  => 'Content-type: application/x-www-form-urlencoded',
        //                    'content' => $postdata,
        //                    'timeout' => 30
        //                )
        //            );
        //            $context  = stream_context_create($options);
        //            $result   = file_get_contents($url, false, $context);
        //            return $result;
        //        }
        //
        //
        //        // 获取图片base64位数据
        //        $post_data = array(
        //            'username'    => 'xieyongfa123',
        //            'password'    => '111aaa...',
        //            'captchaType' => 1008,
        //            'captchaData' => $image
        //        );
        //        $result    = send_post('http://www.bingtop.com/ocr/upload/', $post_data);
        //        // 返回值示例{"code":0, "message":"", "data":{"captchaId":"1001-158201918112812","recognition":"RESULT"}}
        //        $result = json_decode($result, true);
        //        dump($result);
        //        return $result['data']['recognition'];


        $data   = [
            'image' => $image,
            'token' => $this->token,
            'type'  => '10111',
        ];
        $result = curl()->post(SELF::BASE_URL, $data)->get_body();
        return $this->_parseResult($result);
    }


    public function upload($file_path, $codetype = 1004)
    {
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'codetype' => $codetype,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'timeout'  => 60,
            'file'     => '@' . $file_path,
            'method'   => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        if ($this->result && !empty($this->result['text'])) {
            $this->text = $this->result['text'];
        }
        return $this;
    }

    protected function getArrayResult($url, $data = [], $method = 'POST')
    {
        $method = strtolower($method);
        $result = curl()->$method($url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        //        "result": "{\"msg\":\"识别成功\",\"code\":10000,\"data\":{\"code\":0,\"data\":\"vvf43\",\"time\":0.015088558197021484,\"unique_code\":\"a608ebf5b48429bad240627b6b426324\"}}",
        if (empty($result)) {
            $this->errcode = -1;
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if ($result['code'] !== 10000) {
            $this->errcode = $result['msg'];
            return false;
        }
        return $result['data']['data'] ?? $result;
    }

    public function balance()
    {
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'method'   => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        return $this->result ? $this->result['balance'] : false;
    }

    public function report($flag = false, $cid = '')
    {
        $cid          = $cid ?: $this->cid;
        $flag         = ($flag === false) ? 0 : 1;
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'cid'      => $cid,
            'flag'     => $flag,
            'method'   => 'report',
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        return $this->result;
    }

    public function get_text()
    {
        if (!is_array($this->result)) {
            return false;
        }
        if ($this->text) {
            return $this->text;
        }
        $i = 0;
        while (true) {
            $this->text = $this->result();
            if ($this->text || $i > 5) {
                break;
            }
            sleep(3);
            $i++;
        }
        return $this->text;
    }

    public function result($cid = '')
    {
        $cid          = $cid ?: $this->cid;
        $data         = [
            'cid'    => $cid,
            'method' => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data, 'GET');
        return !empty($this->result['text']) ? $this->result['text'] : $this->result;
    }
}