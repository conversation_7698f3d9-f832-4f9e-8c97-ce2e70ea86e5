<?php

namespace OpenApi;

use think\Exception;

class LianBao
{
    /** 接口基础地址 */
    // const API_URL = 'http://dev-dawei.ffun360.com/api';
    const API_URL = 'http://vstore.lianyunbaoidl.com/api';


    public $service;
    public $key;
    public $errcode;
    public $errmsg;
    public $recommend_phone;
    public $_retry = false;
    public $country_code = '+86';
    protected $appid;
    protected $secret;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        //$this->appid           = 'wx_kasd123';
        $this->appid = 'dw_80fe1afc7a778755993f77a34b7d451e';
        //$this->secret          = 'wwkksseeaa';
        $this->secret = 'f95538a85badfeb47a3abab452f049a3';
        //$this->recommend_phone = '18407718888';
        $this->_retry = false;
        $this->key    = '';
    }

    /**
     * 生成签名
     * $paras 请求参数字符串
     * $key 密钥
     * return 生成的签名
     */

    public function query_score_order($out_trade_no)
    {
        $this->service        = '/query_score_order';
        $data['out_trade_no'] = $out_trade_no;//商户订单号
        return $this->request($data); //发送请求
    }

    /**
     * 请求
     * @param array $data
     * @return bool|array
     * @throws \Exception
     */
    protected function request($data = [])
    {
        if (empty($this->service)) {
            throw new \Exception('api 地址不能为空');
        }
        if (empty($data['appid'])) {
            $data['appid'] = $this->appid;
        }
        if (empty($data['recommend_phone'])) {
            $data['recommend_phone'] = $this->country_code . $this->recommend_phone;
        }
        $data['sign'] = $this->createSign($data, $this->getKey());
        $result       = curl()->post(self::API_URL . $this->service, $data)->get_body();
        return $this->_parseResult($result);
    }

    //------------------------------------------------------------
    // 订单查询API
    //------------------------------------------------------------

    /**
     * 创建签名
     * @param array $data
     * @param string $key
     * @return bool|array
     * @throws \Exception
     */

    protected function createSign($data, $key)
    {
        ksort($data);
        $string = md5($this->getSignContent($data) . "&key=" . $key);
        return strtoupper($string);
    }


    //------------------------------------------------------------
    // 会员注册
    //------------------------------------------------------------

    /**
     * 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @param array $data 需要拼接的数组
     * @return string 拼接完成以后的字符串
     */
    protected function getSignContent($data)
    {
        $str = "";
        foreach ($data as $key => $val) {
            if ($val != null && $val != "" && $key != "sign") {
                $str .= $key . "=" . $val . "&";
            }
        }
        return rtrim($str, '&');
    }

    //------------------------------------------------------------
    // 积分返回接口
    //------------------------------------------------------------

    /**
     * 获取密钥
     * @return bool|array
     * @throws \Exception
     */
    public function getKey()
    {
        if ($this->key) {
            return $this->key;
        }
        //$cache_key     = 'token:' . md5($this->appid . $this->secret);
        $cache_key     = 'token:lianbao:' . $this->appid;
        $lock_instance = get_distributed_instance();
        $lock_instance->get_lock($cache_key);
        if ($this->key = cache($cache_key)) {
            $lock_instance->unlock();
            return $this->key;
        }
        $data   = [
            'appid'  => $this->appid,
            'secret' => $this->secret
        ];
        $result = curl()->post(self::API_URL . "/get_key", $data)->get_body();
        $result = $this->_parseResult($result);
        if ($result !== false) {
            $this->key = $result['key'];
            cache($cache_key, $this->key, $result['expires_in'] - 200);
            $lock_instance->unlock();
            return $this->key;
        }
        $lock_instance->unlock();
        throw new Exception('获取key失败:' . $this->errmsg);
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            $this->errcode = 'result is not json';
            $this->errmsg  = '解析返回结果失败!';
            return false;
        }
        $result = json_decode($result, true);
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['errorCode']) && $result['errorCode'] === 0 && isset($result['data'])) {
            return $result['data'];
        }
        if (isset($result['errorCode']) && isset($result['errorMessage'])) {
            $this->errcode = $result['errorCode'];
            $this->errmsg  = $result['errorMessage'];
            wr_log($this->errmsg, 1);
        } else {
            $this->errcode = -1;
            $this->errmsg  = '未知错误';
        }
        return false;
    }

    public function add_client($phone, $name)
    {
        $this->service = '/add_client';
        $data          = [
            'phone' => $this->country_code . $phone,
            'name'  => $name,
        ];
        return $this->request($data); //发送请求
    }

    public function score_order($phone, $out_trade_no, $money, $remark = '')
    {
        $this->service = '/score_order';
        $data          = [
            'phone'        => $this->country_code . $phone,
            'out_trade_no' => $out_trade_no,
            'money'        => $money,
            'remark'       => $remark,
        ];
        return $this->request($data); //发送请求
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}