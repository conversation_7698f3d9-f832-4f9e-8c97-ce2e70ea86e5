<?php

namespace OpenApi;

class ZhuTongSms
{
    /** 登录账号 */
    /** 登录密码 */
    const  API_URL = "http://hy.mix2.zthysms.com";
    /** status */
    public $errcode;
    /** message */
    public $errmsg;
    public $_retry = false;//接口地址
    private $username = 'yrykhy'; //账号：yrykhy 密码：8UzfG4LPzabgge2 //账号：yrykyx 密码：Wpulae
    private $password = '8UzfG4LPzabgge2';
    private $sign_name = '一卡';

    /**
     * ZhuTongSms constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        if (!empty($config['signature'])) {
            $this->setSignName($config['signature']);
        }
    }

    public function setSignName($sign_name)
    {
        if ($sign_name) {
            $this->sign_name = $sign_name;
        }
    }

    public function sendSms($content, $mobile)
    {
        $url  = (strstr($mobile, ',')) ? '/sendSmsBatch.do' : '/sendSms.do'; //手机号包含逗号则走群发
        $data = [
            'content' => $content . '【' . $this->sign_name . '】',
            'mobile'  => $mobile,
        ];
        return $this->request($url, $data);
    }

    protected function request($url, array $data = [])
    {
        $url              = self::API_URL . $url;
        $data['username'] = $this->username;
        $time_key         = $this->getTime();
        $data['password'] = md5(md5($this->password) . $time_key);
        $data['tkey']     = $time_key;
        $result           = curl()->post($url, http_build_query($data))->get_body();
        return $this->_parseResult($result);
    }

    protected function getTime()
    {
        return date('YmdHis');
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (strstr($result, ',')) {
            $arr    = explode(',', $result);
            $result = [
                'status'  => intval($arr[0]),
                'message' => $arr[1],
            ];
        } elseif (is_string($result)) {
            //余额查询接口
            $result = [
                'status'  => 1,
                'message' => intval($result),
            ];
        } else {
            $this->errcode = -1;
            $this->errmsg  = '结果解析失败';
            return false;
        }
        if ($result['status'] != 1) {
            $this->errcode = $result['status'];
            $this->errmsg  = $result['message'];
            return false;
        }
        return $result;
    }

    public function getBalance()
    {
        $url = '/balance.do';
        return $this->request($url);
    }

    public function batchReport()
    {
        $url = '/batchreport.do';
        return $this->request($url);
    }

    public function batchReply()
    {
        $url = '/batchreply.do';
        return $this->request($url);
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}