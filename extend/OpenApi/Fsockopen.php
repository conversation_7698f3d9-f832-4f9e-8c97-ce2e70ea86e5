<?php

namespace OpenApi;

/** HttpRequest class, HTTP请求类，支持GET,POST,Multipart/form-data
 *  Date:  2017-08-08
 *  Author: fdipzone
 *  Ver:  1.0
 *
 *  Func:
 *  public setConfig   设置连接参数
 *  public setFormdata  设置表单数据
 *  public setFiledata  设置文件数据
 *  public send     发送数据
 *  private connect    创建连接
 *  private disconnect  断开连接
 *  private sendGet    get 方式,处理发送的数据,不会处理文件数据
 *  private sendPost   post 方式,处理发送的数据
 *  private sendMultipart multipart 方式,处理发送的数据,发送文件推荐使用此方式
 */
class Fsockopen
{
    public $_errno = '';
    public $_errstr = '';
    private $_host = '';
    private $_scheme = '';
    private $_url = '';
    private $_port = '';
    private $_timeout = 30;

    private $_fp = null;
    private $_crlf = '';
    private $_asyn = false;

    private $_formdata = array();
    private $_filedata = array();


    // 设置连接参数
    public function setConfig($config)
    {
        $this->_crlf = "\r\n";
        $url_pieces  = parse_url($config['url']);
        $url_scheme  = (isset($url_pieces['scheme'])) ? $url_pieces['scheme'] : '';
        switch (strtolower($url_scheme)) {
            case 'https':
                $this->_scheme = "ssl://";
                $port          = 443;
                break;
            case 'http':
                $port = 80;
                break;
            default:
                return false;
        }
        $this->_host = $url_pieces['host'];
        $this->_asyn = (isset($config['asyn'])) ? $config['asyn'] : false;
        //设置正确的路径和端口号
        $this->_url  = (isset($url_pieces['path'])) ? $url_pieces['path'] : '/';
        $this->_port = (isset($url_pieces['port'])) ? $url_pieces['port'] : $port;
    }

    public function setPostData($data = array())
    {
        $file_data = [];
        foreach ($data as $key => $val) {
            if (is_string($val) && stripos($val, '@') === 0) {
                $path = ltrim($val, '@');
                if (file_exists($path) && is_file($path)) {
                    $file_data[$key] = $path;
                    unset($data[$key]);
                }
            }
        }
        if (!empty($data)) {
            $this->setFormData($data);
        }
        if (!empty($file_data)) {
            $this->setFileData($file_data);
        }
        return $this;
    }


    // 设置表单数据
    public function setFormData($formdata = array())
    {
        $this->_formdata = $formdata;
    }

    // 设置文件数据
    public function setFileData($filedata = array())
    {
        $this->_filedata = $filedata;
    }

    // 发送数据
    public function get()
    {
        if ($this->connect()) {
            $out = $this->sendGet();
            return $this->send($out);
        } else {
            return false;
        }
    }

    // 发送数据

    private function connect()
    {
        $this->_fp = fsockopen($this->_scheme . $this->_host, $this->_port, $this->_errno, $this->_errstr, $this->_timeout);
        if (!$this->_fp) {
            return false;
        }
        return true;
    }

    private function sendGet()
    {

        // 检查是否空数据
        if (!$this->_formdata) {
            return false;
        }

        // 处理url
        $url = $this->_url . '?' . http_build_query($this->_formdata);

        $out = "GET " . $url . " HTTP/1.1" . $this->_crlf;
        $out .= "HOST: " . $this->_host . $this->_crlf;
        $out .= "Connection: close" . $this->_crlf . $this->_crlf;

        return $out;
    }

    // 发送数据

    public function send($out)
    {
        // 空数据
        if (!$out) {
            return false;
        }

        // 发送数据
        fputs($this->_fp, $out);

        //非异步则获取返回内容
        if (!$this->_asyn) {
            // 读取返回数据
            $response = '';
            while ($row = fread($this->_fp, 4096)) {
                $response .= $row;
            }
        }

        // 断开连接
        $this->disconnect();
        //非异步则获取返回内容
        if (!$this->_asyn) {
            // 读取返回数据
            $pos      = strpos($response, $this->_crlf . $this->_crlf);
            $response = substr($response, $pos + 4);
            return $response;
        }
        return true;
    }

    // 创建连接

    private function disconnect()
    {
        if ($this->_fp != null) {
            fclose($this->_fp);
            $this->_fp = null;
        }
    }

    // 断开连接

    public function post()
    {
        if ($this->connect()) {
            $out = $this->sendPost();
            return $this->send($out);
        } else {
            return false;
        }
    }

    // get 方式,处理发送的数据,不会处理文件数据

    private function sendPost()
    {

        // 检查是否空数据
        if (!$this->_formdata && !$this->_filedata) {
            return false;
        }

        // form data
        $data = $this->_formdata ? $this->_formdata : array();

        // file data
        if ($this->_filedata) {
            foreach ($this->_filedata as $filedata) {
                if (file_exists($filedata['filepath'])) {
                    $data[$filedata['name']] = file_get_contents($filedata['filepath']);
                }
            }
        }

        if (!$data) {
            return false;
        }

        $data = http_build_query($data);
        $out  = "POST " . $this->_url . " HTTP/1.1" . $this->_crlf;
        $out  .= "HOST: " . $this->_host . $this->_crlf;
        $out  .= "content-type: application/x-www-form-urlencoded" . $this->_crlf;
        $out  .= "content-length: " . strlen($data) . $this->_crlf;
        $out  .= "Connection: close" . $this->_crlf . $this->_crlf;
        $out  .= $data;

        return $out;
    }

    // post 方式,处理发送的数据

    public function multipart()
    {
        if ($this->connect()) {
            $out = $this->sendMultipart();
            return $this->send($out);
        } else {
            return false;
        }
    }

    // multipart 方式,处理发送的数据,发送文件推荐使用此方式

    private function sendMultipart()
    {
        // 检查是否空数据
        if (!$this->_formdata && !$this->_filedata) {
            return false;
        }
        // 设置分割标识
        srand((double)microtime() * 1000000);
        $boundary = '----' . substr(md5(rand(0, 32000)), 0, 10);
        $data     = '';
        // 处理表单数据上传
        $formdata = '';
        foreach ($this->_formdata as $key => $val) {
            $formdata .= '--' . $boundary . $this->_crlf;
            $formdata .= 'Content-Disposition: form-data; name="' . $key . '"' . $this->_crlf . $this->_crlf;
            $formdata .= $val . $this->_crlf;
        }
        // 处理文件上传
        $filedata = '';
        foreach ($this->_filedata as $key => $val) {
            if (file_exists($val)) {
                $filedata .= '--' . $boundary . $this->_crlf;
                $filedata .= 'Content-Disposition: form-data; name="' . $key . '"; filename="' . $boundary . '.jpg"' . $this->_crlf;
                $filedata .= "Content-Type: application/octet-stream" . $this->_crlf . $this->_crlf;
                $file     = fopen($val, 'r');
                $size     = filesize($val);
                $fileData = fread($file, $size);
                $filedata .= $fileData;
                $filedata .= '--' . $boundary . $this->_crlf;
            }
        }

        if (!$formdata && !$filedata) {
            return false;
        }
        $data .= $formdata . $filedata . "--" . $boundary . "--" . $this->_crlf . $this->_crlf;
        $out  = "POST " . $this->_url . " HTTP/1.1" . $this->_crlf;
        $out  .= "HOST: " . $this->_host . $this->_crlf;
        $out  .= "Content-Type: multipart/form-data; boundary=" . $boundary . $this->_crlf;
        $out  .= "Content-Length: " . strlen($data) . $this->_crlf;
        $out  .= "Connection: close" . $this->_crlf . $this->_crlf;
        $out  .= $data;

        return $out;
    }
}
