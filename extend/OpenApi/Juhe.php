<?php

namespace OpenApi;

class Juhe
{
    /** 接口基础地址 */
    const BASE_URL = 'http://apis.juhe.cn/';
    /** appkey */
    public $appkey;

    /** juheOpenId */
    public $juheOpenId;

    /** error_code */
    public $error_code;

    /** reason */
    public $reason;

    /** action */
    public $action;
    public $_retry = false;

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $this->appkey     = $options['appkey'];
        $this->juheOpenId = $options['juheOpenId'];
    }

    public function getMobileArea($data)
    {
        $url = 'http://apis.juhe.cn/mobile/get';
        return $this->getArrayResult($url, $data, 'GET');
    }

    protected function getArrayResult($url, $data = [], $method = 'GET')
    {
        $data['key'] = $this->appkey;
        $result      = curl()->$method($url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->error_code = 'result error';
            $this->reason     = '解析返回结果失败';
            return false;
        }
        if ($result['error_code'] !== 0) {
            $this->error_code = $result['error_code'];
            $this->reason     = $result['reason'];
            return false;
        }
        return $result;
    }

    public function getWeixinQuery($data)
    {
        $default_data = [
            "cardId"        => '',
            "password"      => '',
            "isGetExtValue" => false,
        ];
        $url          = 'http://v.juhe.cn/weixin/query';
        return $this->getArrayResult($url, $data, 'GET');
    }

    //获取手机号归属地,请求示例：http://apis.juhe.cn/mobile/get?phone=13429667914&key=您申请的KEY

    public function ofpaySinopecOnlineorder($data)
    {
        $url          = 'http://op.juhe.cn/ofpay/sinopec/onlineorder';
        $default_data = [
            "sign" => strtolower(MD5($this->juheOpenId . $this->appkey . $data['proid'] . $data['cardnum'] . $data['game_userid'] . $data['orderid']))
        ];
        $data         = array_merge($default_data, $data);
        return $this->getArrayResult($url, $data, 'POST');
    }

    //获取会员信息

    public function ofpaySinopecOrdersta($data)
    {
        $url    = 'http://op.juhe.cn/ofpay/sinopec/ordersta';
        $result = $this->getArrayResult($url, $data, 'POST');
        return $result ?: $this->checkRetry(__FUNCTION__, func_get_args());
    }
    //ofpay/sinopec/onlineorder
    //提交加油卡充值

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    //加油卡充值状态查询

    public function getToh($data)
    {
        $url          = 'http://api.juheapi.com/japi/toh';
        $default_data = [
            "v" => '1.0',
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->getArrayResult($url, $data, 'POST');
        return $result ?: $this->checkRetry(__FUNCTION__, func_get_args());
    }

    //获取手机号归属地

    public function getBalance()
    {
        $url    = "http://op.juhe.cn/ofpay/sinopec/yue";
        $data   = [
            "timestamp" => time(),
            "sign"      => strtolower(MD5($this->juheOpenId . $this->appkey . time()))
        ];
        $result = $this->getArrayResult($url, $data, 'POST');
        //如果结果为false则重试
        return $result ?: $this->checkRetry(__FUNCTION__, func_get_args());
    }

    //************2.账户余额查询************
    //array(3) {
    //["reason"] => string(12) "查询成功"
    //["result"] => array(2) {
    //["uid"] => string(12) "xieyongfa123"
    //["money"] => string(5) "1.000"
    //}
    //["error_code"] => int(0)
    //}

    public function telCheck($data)
    {
        $url          = 'http://op.juhe.cn/ofpay/mobile/telcheck';
        $default_data = [
            "phoneno" => '',
            'cardnum' => ''
        ];
        $data         = array_merge($default_data, $data);
        $result       = $this->getArrayResult($url, $data, 'POST');
        return $this->getArrayResult($url, $data, 'POST');
    }

    //检测手机号码及金额是否能充值

    public function rechargeMobile($phone_no, $card_num, $order_id)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/onlineorder';
        $data = [
            "phoneno" => $phone_no,
            'cardnum' => $card_num,
            'orderid' => $order_id
        ];
        return $this->getArrayResult($url, $data, 'POST');
    }

    //充值接口

    public function queryRechargeMobile($order_id)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/ordersta';
        $data = [
            'orderid' => $order_id
        ];
        return $this->getArrayResult($url, $data, 'POST');
    }

    //查询订单接口

    public function telQuery($phone_no, $card_num)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/telquery';
        $data = [
            "phoneno" => $phone_no,
            'cardnum' => $card_num,
        ];
        return $this->getArrayResult($url, $data, 'POST');
    }

    //根据手机号和面值查询商品信息

    /**
     * 获取签名
     * @param array $data 签名数组
     * @return bool|string 签名值
     */
    protected function getSignature($data)
    {
        return strtoupper(MD5($this->openid . $this->secret . time() . $data));
    }
}