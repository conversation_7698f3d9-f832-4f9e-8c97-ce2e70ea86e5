<?php
/**
 * 博联科技云平台接口SDK
 */

namespace OpenApi;

use Exception;

class Bolink
{
    /** 错误码 */
    public $errcode;
    /** 错误信息 */
    public $errmsg;
    public $sign;
    /** 配置参数 */
    protected $config = [
        'comid'    => '254454',                    // 车场编号
        'union_id' => '200068',                    // 厂商编号
        'ckey'     => '77YJ53SIZZGC13B9',         // 车场密钥
        'test_url' => 'http://test.bolink.club',   // 测试环境
        'prod_url' => 'http://yun.bolink.club',    // 生产环境
        'is_test'  => false,                        // 是否测试环境
    ];

    /**
     * 构造函数
     * @param array $config
     */
    public function __construct($config = [])
    {
        $this->config = array_merge($this->config, $config);
    }

    /**
     * 数据签名
     * @param array $data
     * @return string
     */
    protected function signData(array $data)
    {
        // 将数组转为JSON字符串
        $dataString = json_encode($data, JSON_UNESCAPED_UNICODE);
        // 组装签名串：data内容 + key=车场密钥
        $signString = $dataString . 'key=' . $this->config['ckey'];

        // MD5加密并转大写
        return strtoupper(md5($signString));
    }

    /**
     * 通用API请求方法
     * @param string $api_url
     * @param array $data
     * @return bool|array
     */
    public function api($api_url, $data)
    {
        try {
            // 生成签名
            $this->sign = $this->signData($data);

            // 组装完整请求数据
            $requestData = [
                'data' => $data,
                'sign' => $this->sign
            ];
            // Base64编码
            $encodedData = base64_encode(json_encode($requestData, JSON_UNESCAPED_UNICODE));

            // 构造完整URL
            $url = $this->getBaseUrl() . '/' . $api_url;

            // 发送HTTP请求
            $result = curl()->post($url, $encodedData)->get_body();

            return $this->_parseResult($result);

        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    /**
     * 解析返回结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->errcode = 'empty_result';
            $this->errmsg  = '返回结果为空';
            return false;
        }

        try {
            // Base64解码
            $decodedResult = base64_decode($result);
            $resultArray   = json_decode($decodedResult, true);
            if (!$resultArray) {
                $this->errcode = -3;
                $this->errmsg  = '解码失败';
                return false;
            }
            // 验证返回结果签名
            if (isset($resultArray['sign']) && isset($resultArray['data'])) {
                if ($this->sign !== $resultArray['sign']) {
                    $this->errcode = 'sign_error';
                    $this->errmsg  = '返回结果签名验证失败';
                    return false;
                }
            }

            return $resultArray;

        } catch (Exception $e) {
            $this->errcode = -4;
            $this->errmsg  = '解析异常：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取基础URL
     * @return string
     */
    protected function getBaseUrl()
    {
        return $this->config['is_test'] ? $this->config['test_url'] : $this->config['prod_url'];
    }

    /**
     * 远程开关闸接口
     * @param string $channel_id 通道编号
     * @param string $liftrod_id 操作编号
     * @param int $operate 道闸命令 0-抬杆 1-落杆
     * @return bool|array
     */
    public function operateLiftrod($channel_id, $liftrod_id, $operate = 1)
    {
        // 组装请求数据
        $data = [
            'comid'      => $this->config['comid'],
            'union_id'   => $this->config['union_id'],
            'channel_id' => $channel_id,
            'operate'    => $operate,
            'liftrod_id' => $liftrod_id
        ];

        // 调用API
        $result = $this->api('zld/openservice/operateliftrod', $data);

        if ($result === false) {
            return false;
        }
        // 检查返回状态
        if (isset($result['data']['state']) && $result['data']['state'] == 1) {
            return $result['data'];
        } else {
            $this->errcode = -1;
            $this->errmsg  = $result['data']['message'] ?? '操作失败';
            return false;
        }
    }
}