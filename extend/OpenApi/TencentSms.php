<?php

namespace OpenApi;

use Exception;
use Qcloud\Sms\SmsMobileStatusPuller;
use Qcloud\Sms\SmsMultiSender;
use Qcloud\Sms\SmsSingleSender;
use Qcloud\Sms\SmsStatusPuller;
use Qcloud\Sms\SmsVoicePromptSender;
use Qcloud\Sms\SmsVoiceVerifyCodeSender;


class TencentSms
{

    public $errcode;
    public $errmsg;
    protected $appid;
    protected $appkey;
    protected $sign;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $this->sign   = '一卡';
        $this->appid  = 1400066507;
        $this->appkey = 'd67e36a970141c0dedbb6619eb6caa79';
        //$templateId = 82047;  // NOTE: 这里的模板ID`7839`只是一个示例，真实的模板ID需要在短信控制台中申请
    }

    public function smsSingleSender($mobile, $msg)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->send(0, "86", $mobile, $msg, "", "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    //单发短信

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        $result = json_decode($result, true);
        if (isset($result['result']) && $result['result'] !== 0) {
            $this->errcode = $result['result'];
            $this->errmsg  = $result['errmsg'];
            return false;
        }
        return $result;
    }

    //群发短信

    public function smsMultiSender($mobile_arr, $msg)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->send(0, "86", $mobile_arr, $msg, "", "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    //指定模板ID群发短信
    public function smsSingleSenderByTemplateId($mobile_arr, $params, $template_id)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->sendWithParam("86", $mobile_arr, $template_id, $params, $this->sign, "", "");  // 签名参数未提供或者为空时，会使用默认签名发送短信
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    //指定模板ID群发短信
    public function smsMultiSenderByTemplateId($mobile_arr, $params, $template_id)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsMultiSender($this->appid, $this->appkey);
            $result = $sender->sendWithParam("86", $mobile_arr, $template_id, $params, $this->sign, "", "");  // 签名参数未提供或者为空时，会使用默认签名发送短信
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    // 发送语音通知
    public function smsVoicePromptSender($mobile, $msg)
    {
        try {
            // "尊敬的谢永发您好！您收到一封来自12306的邮件,请及时处理！"
            $sender = new SmsVoicePromptSender($this->appid, $this->appkey);
            $result = $sender->send("86", $mobile, 2, $msg, 1, "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    // 发送语音验证码
    public function smsVoiceVerifyCodeSender($mobile, $code)
    {
        try {
            // "尊敬的谢永发您好！您收到一封来自12306的邮件,请及时处理！"
            $sender = new SmsVoiceVerifyCodeSender($this->appid, $this->appkey);
            $result = $sender->send("86", $mobile, $code, 2, "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }


    // 拉取短信发送报告
    public function pullCallback($max = 10)
    {
        try {
            $puller = new SmsStatusPuller($this->appid, $this->appkey);
            $result = $puller->pullCallback($max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    // 拉取短信回执
    public function pullReply($max = 10)
    {
        try {
            $puller = new SmsStatusPuller($this->appid, $this->appkey);
            $result = $puller->pullReply($max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    // 拉取单个手机短信回执
    public function pullCallbackSingleMobile($mobile, $begin_time = null, $end_time = null, $max = 10)
    {
        try {
            $puller     = new SmsMobileStatusPuller($this->appid, $this->appkey);
            $begin_time = $begin_time ?: strtotime(date("Y-m-d"), time());
            $end_time   = $end_time ?: time();
            $result     = $puller->pullCallback("86", $mobile, $begin_time, $end_time, $max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }

    // 拉取单个手机回复状态
    public function pullReplySingleMobile($mobile, $begin_time = null, $end_time = null, $max = 10)
    {
        try {
            $puller     = new SmsMobileStatusPuller($this->appid, $this->appkey);
            $begin_time = $begin_time ?: strtotime(date("Y-m-d"), time());
            $end_time   = $end_time ?: time();
            $result     = $puller->pullReply("86", $mobile, $begin_time, $end_time, $max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->errcode = -2;
            $this->errmsg  = $e->getMessage();
            return false;
        }
    }
}