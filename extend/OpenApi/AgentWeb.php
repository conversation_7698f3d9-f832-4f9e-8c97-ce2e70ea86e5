<?php

namespace OpenApi;

class AgentWeb
{
    /** 接口基础地址 */
    const BASE_URL = 'https://admin.cmiyun.com/api/av1/ad';
    /** 登录账号 */
    public $username;
    /** 登录密码 */
    public $password;
    /** errcode */
    public $errcode;
    /** message */
    public $errmsg;
    public $_retry = false;

    protected $token;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $default_config = [
            'username' => '15012884946',
            'password' => 'wx6161',
        ];
        $config         = array_merge($default_config, $config);
        $this->username = $config['username'];
        $this->password = $config['password'];
        $this->_retry   = false;
    }

    public function get_welcome_info()
    {
        $url    = self::BASE_URL . '/agents/welcome_info';
        $result = $this->get($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    protected function get($url)
    {
        return $this->http_request($url, [], 'GET');
    }

    protected function http_request($url, $data, $method = 'POST')
    {
        if (!$this->token && !$this->get_login_token()) {
            return false;
        }
        $result = curl()->set_header(['authorization' => 'Bearer ' . $this->token])->$method($url, $data)->get_body();
        if ($result === false) {
            $this->errmsg = __CLASS__ . __FUNCTION__ . '请求发生错误';
            return false;
        }
        return $this->_parseResult($result);
    }

    public function get_login_token($force = false)
    {
        $username     = $this->username;
        $password     = $this->password;
        $key          = $this->get_cache_key();
        $cache_cookie = cache($key);
        if ($force === false && $cache_cookie) {
            return $this->token = $cache_cookie;
        }
        //访问登录页面获取token
        $url       = 'https://admin.cmiyun.com/account/login';
        $result    = curl()->get($url);
        $html      = $result->get_body();
        $cookies   = $result->get_cookies();
        $token     = tools()::search_str("value=\"", "\">", $html);
        $post_data = [
            'mobile'   => $username,
            '_token'   => $token,
            'password' => base64_encode($password),
        ];
        $login_url = 'https://admin.cmiyun.com/form_v1/account/login';
        $result    = curl()->set_cookies($cookies)->post($login_url, $post_data)->get_body();
        if (($result = $this->_parseResult($result)) === false) {
            return false;
        }
        //进行登录操作
        $auth_url  = 'https://admin.cmiyun.com/authenticate/login';
        $post_data = [
            'loginToken' => $result['token'],
            'loginType'  => 'password',
        ];
        $result    = curl()->set_cookies($cookies)->post($auth_url, $post_data)->get_body();
        if (($result = $this->_parseResult($result)) === false) {
            return false;
        }
        $jwt_token    = $result['token_data']['token'];
        $expires_unix = $result['token_data']['expires_unix'];
        $this->token  = $jwt_token;
        cache($key, $this->token, $expires_unix - time() - 60);
        return $this->token;
    }

    protected function get_cache_key($username = '')
    {
        $username = $username ?: $this->username;
        return 'agent:login_token:' . $username;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['status']) && $result['status'] !== 1) {
            $this->errcode = isset($result['errcode']) ? $result['errcode'] : -1;
            $this->errmsg  = $result['info'];
            return false;
        }
        return isset($result['result']) ? $result['result'] : $result;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            ($this->_retry = true) && $this->clear_token();
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    //获得后台登陆token

    protected function clear_token($username = '')
    {
        $username = $username ?: $this->username;
        return cache($this->get_cache_key($username), NULL);
    }

    //获取首页信息

    public function get_agent_list()
    {
        $url    = self::BASE_URL . '/agents/partner_lists?user_type=3';
        $result = $this->get($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //获取代理商列表

    public function get_partner_list()
    {
        $url    = self::BASE_URL . '/agents/partner_lists?user_type=2';
        $result = $this->get($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //获取商家列表

    protected function post($url, $data)
    {
        return $this->http_request($url, $data);
    }
}