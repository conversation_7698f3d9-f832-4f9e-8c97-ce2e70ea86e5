<?php

namespace OpenApi;

class Leshua
{
    /** 接口基础地址 */
    //  const BASE_URL = 'http://t-saas-combine.lepass.cn/saas-open-api';  //debug
    const BASE_URL = 'https://saas-combine.leshuazf.com/open-api';
    /** agent_key */
    public $agent_key;

    /** agent_id */
    public $agent_id;

    /** error_code */
    public $error_code;

    /** reason */
    public $reason;

    /** action */
    public $action;
    public $_retry = false;

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $this->agent_id  = '8960539';
        $this->agent_key = '2023B7C753764A467E70C83D99657FEF';
        // {"key": "D4AB3FD5BFA45BBDCAA0B0C648D04B8B", "agentId": "1157213", "reportConfigId": "103"}
        $this->agent_id  = '1157213';
        $this->agent_key = 'D4AB3FD5BFA45BBDCAA0B0C648D04B8B';

    }



    //$leshua = new Leshua();
    //$mch_id = '9951014782';
    ////       $bill_id = '20220503995101478201'; //未使用
    //$bill_id = '20220503001027246382';
    //$result  = $leshua->balance_query($mch_id);
    //dump($result);
    ////       $data   = [
    ////           'applyAmount' => 10793194,
    ////           'merchantId'  => $mch_id,
    ////           'reqId'       => $bill_id,
    ////           'type'        => 1,
    ////       ];
    //   // "reqId" => "8960539_20220503995101478201"
    //   //   "merchantId" => "9951014782"
    //   //   "billId" => "20220503001027246382"
    ////       $result = $leshua->apply($data);
    ////       halt($result);
    //$result = $leshua->query($bill_id);
    //halt($result);

    public function query($billId)
    {
        $path            = '/agent/settlement-order';
        $type            = 't1';
        $data            = ['billId' => $billId, 'type' => $type];
        $data['sign']    = md5('agentId=' . $this->agent_id . '&billId=' . $billId . '&type=' . $type . '&key=' . $this->agent_key);
        $data['agentId'] = $this->agent_id;
        return curl()->form_params()->post(self::BASE_URL . $path, $data)->get_body();
    }

    public function apply($data)
    {
        $path = '/merchant-withdraw/apply';
        return $this->getArrayResult($path, $data);
    }

    public function balance_query($merchantId)
    {
        $path = '/merchant-withdraw/balance-query';
        return $this->getArrayResult($path, ['merchantId' => $merchantId]);
    }

    protected function getArrayResult($url, $post_data = [])
    {
        $data['agentId']     = $this->agent_id;
        $data['version']     = '2.0';
        $data['reqSerialNo'] = create_guid();
        $data['data']        = $post_data;
        if (empty($data['sign'])) {
            $data['sign'] = $this->getSignature($data);
        }
        $result = curl()->json()->post(self::BASE_URL . $url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }


    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->error_code = 'result error';
            $this->reason     = '解析返回结果失败';
            return false;
        }
        if ($result['code'] !== 0) {
            $this->error_code = $result['code'];
            $this->reason     = $result['msg'];
            return false;
        }
        return $result['data'] ?? $result;
    }


    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    //根据手机号和面值查询商品信息

    /**
     * 获取签名
     * @param array $data 签名数组
     * @return bool|string 签名值
     */
    protected function getSignature($data)
    {
        if (is_array($data)) {
            return md5($this->agent_key . $data['reqSerialNo'] . $data['version'] . json_encode($data['data'], JSON_UNESCAPED_UNICODE));

        }
        //加密规则： md5hex(代理商密钥+reqSerialNo+version+ data的 json 串)
        return md5($this->agent_key . $data['reqSerialNo'] . $data['version'] . $data['data']);
    }


    /**
     * 16进制转string拼接
     * @param array $bytes [description]
     * @return [type] [description]
     * <AUTHOR>
     * @dateTime 2018-01-25T10:18:31+0800
     */
    function encodeHexString(array $bytes)
    {
        $LOWER   = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c'];
        $length  = count($bytes);
        $charArr = [];
        foreach ($bytes as $value) {
            $value     = intval($value);
            $charArr[] = $LOWER[$this->uright(0xF0 & $value, 4)];
            $charArr[] = $LOWER[0x0F & $value];
        }
        return implode("", $charArr);
    }

    /** php 无符号右移 */
    function uright($a, $n)
    {
        $c = 2147483647 >> ($n - 1);
        return $c & ($a >> $n);
    }

    /**
     * 模拟DigestUtils.md5
     * @param    [string]                   $string 加密字符
     * @return   [array]                           加密之后的byte数组
     * <AUTHOR>
     * @dateTime 2018-01-25T09:28:33+0800
     */
    function md5Hex($string)
    {
        return unpack("c*", md5($string, true));
    }
}