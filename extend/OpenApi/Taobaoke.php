<?php

namespace OpenApi;

use ETaobao\Factory;
use Exception;

/**
 * Taobaoke
 * Class Taobaoke
 * @package Taobaoke https://www.yuque.com/kouss/taoke/ngffqg
 * <AUTHOR> <<EMAIL>>
 * @date 2019/09/04 22:00
 *
 * @method Taobaoke privilegeGet(array $options) 单品券高效转链
 * @method Taobaoke tpwdCreate(array $options)
 * @method Taobaoke spreadGet(array $options) 淘宝长链转短链接
 * @method Taobaoke activitylink(array $options) 官方活动推广链接生成
 * @method Taobaoke tljCreateSc(array $options) 淘礼金创建
 * @method Taobaoke shopConvert(array $options) 淘宝客店铺链接转换
 * @method Taobaoke itemCouponGet(array $options) 单品加券检索（联盟公告说即将下线，用scMaterialOptional代替）
 * @method Taobaoke scMaterialOptional(array $options) 物料搜索
 * @method Taobaoke optimusMaterial(array $options) 物料精选（原擎天柱通用物料）
 * @method Taobaoke tpwdConvert(array $options) 淘宝客口令转商品ID，没文档，入参password_content:淘口令文案
 * @method Taobaoke tpwdConvertSc(array $options) 淘口令解析&转链
 * @method Taobaoke itemClickExtract(array $options) 单品推广链接解析
 * @method Taobaoke orderGet(array $options) 淘宝客订单查询（联盟公告2019年9月20日下线）
 * @method Taobaoke orderDetailGet(array $options) 淘宝客订单查询-新
 * @method Taobaoke punishOrderGet(array $options) 淘宝客处罚订单查询
 * @method Taobaoke invitecodeGet(array $options) 淘宝客邀请码生成
 * @method Taobaoke publisherInfoSave(array $options) 淘宝客渠道信息备案
 * @method Taobaoke publisherInfoGet(array $options) 淘宝客渠道信息查询
 * @method Taobaoke groupchatCreate(array $options) 手淘群创建
 * @method Taobaoke groupchatGet(array $options) 手淘群查询
 * @method Taobaoke groupchatMessageSend(array $options) 手淘群发单
 */
class Taobaoke
{

    /** 接口基础地址 */
    const API_URL = 'http://gateway.kouss.com/tbpub/';

    public $errcode;
    public $errmsg;
    public $_retry = false;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {

    }

    /**
     * @param string $action 调用方法名
     * @param array|string $arguments 调用参数
     * @return array|string|bool
     */
    public function __call($action, $arguments)
    {
        $arguments[] = $action;//$url参数直接转
        //return $this->request($arguments, $name);
        return call_user_func_array([$this, 'request'], $arguments);
    }

    public function get_url_by_password_content($password_content)
    {
        $data   = ['password_content' => $password_content];
        $result = $this->tpwdConvert($data);
        if ($result === false) {
            return false;
        }
        $item_id = $result['num_iid'];
        $data    = [
            'adzone_id' => '101690436',
            'site_id'   => '26210326',
            'session'   => get_system_config('taobao_session'),
            'item_id'   => $item_id,
        ];
        $result  = $this->privilegeGet($data);
        if ($result === false) {
            return false;
        }
        $return              = '';
        $coupon_value        = 0;
        $max_commission_rate = 0;
        foreach ($result as $key => $val) {
            if ($key == 'coupon_info') {
                $return       .= '【优惠券】:' . $val . "\r\n";
                $coupon_value = tools()::search_str('减', '元', $val);
            }
            if ($key == 'max_commission_rate') {
                $max_commission_rate = $val;
            }
            // $return .= $key . ':' . $val . "\r\n";
        }
        //$return = json_encode($result, JSON_UNESCAPED_UNICODE);
        $url    = isset($result['coupon_click_url']) ? $result['coupon_click_url'] : $result['item_url'];
        $result = $this->get_item_info($item_id);
        if ($result === false) {
            return false;
        }
        $item_info = $result['n_tbk_item'][0];
        $return    .= '【商品】:' . $item_info['title'] . "\r\n";
        $return    .= '【在售价】:' . number_format($item_info['zk_final_price'], 2, '.', '') . "元\r\n";
        if ($coupon_value > 0) {
            $after_coupon_value = tools()::nc_price_calculate($item_info['zk_final_price'], '-', $coupon_value);
            $return             .= '【券后价】:' . number_format($after_coupon_value, 2, '.', '') . "元\r\n";
            if ($max_commission_rate > 0) {
                $commission = tools()::nc_price_calculate($max_commission_rate, '*', $after_coupon_value);
                $return     .= '【佣金】:' . tools()::nc_price_fen2yuan($commission) . "元\r\n";
            }
        }
        $data   = [
            'logo' => $item_info['pict_url'],
            'text' => $item_info['title'],
            'url'  => $url
        ];
        $result = $this->create_password_content($data);
        if ($result !== false) {
            $return .= '复制到TaoBao打开:' . $result['model'];
            return $return;
        }
        //return json_encode($result, JSON_UNESCAPED_UNICODE);
        return $result;
    }

    public function get_item_info($item_id)
    {
        $app    = self::get_tbk_class();
        $data   = ['num_iids' => $item_id];
        $result = $app->item->getInfo($data);
        return $this->_parseResult($result);
    }

    public static function get_tbk_class()
    {
        $config = [
            'appkey'    => '27870663',
            'secretKey' => '3ecbfe171f0b63efff5ba5d4f819f212',
            'format'    => 'json',
            'session'   => '',//授权接口（sc类的接口）需要带上
            'sandbox'   => false,
        ];
        $app    = Factory::Tbk($config);
        return $app;
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result) && !is_array($result) && !is_object($result)) {
            $this->errcode = 'result is not json or array';
            $this->errmsg  = '解析返回结果失败!';
            return false;
        }
        if (is_object($result)) {
            $result = tools()::object2array($result);
        } elseif (tools()::is_json($result)) {
            $result = json_decode($result, true);
        }
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败'; //错误时有code msg sub_msg(可选)
            return false;
        }
        if (isset($result['code'])) {
            $this->errcode = $result['code'];
            $this->errmsg  = $result['msg'];
            if (isset($result['sub_msg'])) {
                $this->errmsg .= $result['sub_msg'];
            }
            return false;
        }
        if (isset($result['result']) && isset($result['result']['data'])) {
            return $result['result']['data'];
        } elseif (isset($result['data'])) {
            return $result['data'];
        } elseif (isset($result['results'])) {
            return $result['results'];
        }
        return $result;
    }

    public function create_password_content($data)
    {
        $app    = self::get_tbk_class();
        $result = $app->tpwd->create($data);
        return $this->_parseResult($result);
    }

    /**
     * 请求
     * @param array $data
     * @param string $url
     * @return bool|array
     * @throws Exception
     */
    protected function request($data = [], $action)
    {
        $result = curl()->post(self::API_URL . $action, json_encode($data, JSON_UNESCAPED_UNICODE))->get_body();
        return $this->_parseResult($result);
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}