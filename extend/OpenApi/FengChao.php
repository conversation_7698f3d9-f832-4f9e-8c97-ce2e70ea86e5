<?php

namespace OpenApi;

class Feng<PERSON>hao
{
    /** 接口基础地址 */
    const BASE_URL = 'https://webchatapp.fcbox.com';


    /** reason */
    public $code = 0;

    /** action */
    public $msg = '';

    public $_retry = false;

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {

    }

    public function get_express_list($all = false)
    {
        $url    = '/post/express/pageQuery4Mini';
        $data   = [
            'pageNo'        => 1,
            'pageSize'      => 10,
            'expressId'     => '',
            //            'expressStatus' => 1 //1 待取件
            'expressStatus' => $all ? 0 : 1 //全部取件
        ];
        $result = $this->getArrayResult($url, $data);
        if ($result === false) {
            return false;
        }
        $array = [];
        if ($result['data']['total'] > 0) {
            foreach ($result['data']['data'] as $list) {
                $company_name        = $list['companyName']; //快递公司
                $status              = $list['boxStatus'];
                $send_tm             = $list['sendTm']; //入柜时间
                $exp_time            = date('Y-m-d H:i:s', strtotime($send_tm . ' +18 hours'));
                $pick_tm             = $list['pickTm']; //取件时间
                $pick_up_code        = $list['code']; //取件码
                $post_id             = $list['postId']; //加密单号
                $client_mobile       = $list['clientMobile']; //手机号
                $express_id          = $list['expressId']; //快递单号
                $company_name_simple = str_replace(['快递', '速递', '中国'], '', $company_name);
                $express_id_suffix   = substr($express_id, -4);
                $address             = $list['address'];
                $address             = str_replace(['锦绣御园'], '', $address);
                $address             = str_replace(['架空层'], '', $address);
                $address             = str_replace(['丰巢柜'], '', $address);
                $url                 = 'https://external.fcbox.com/staticResource/wechat/program/wechat_pick/pick-card.html#/pickupPromotion?bizType=0&mobile=' . $client_mobile . '&postId=' . $post_id;
                $array[]             = [
                    'url'                 => $url,
                    'status'              => $status,
                    'post_id'             => $post_id,
                    'pick_up_code'        => $pick_up_code,
                    'pick_up_address'     => $address,
                    'express_name'        => $company_name,
                    'express_no'          => $express_id,
                    'company_name_simple' => $company_name_simple,
                    'client_mobile'       => $client_mobile,
                    'express_id_suffix'   => $express_id_suffix,
                    'send_time'           => $send_tm,
                    'exp_time'            => $exp_time,
                    'pick_time'           => $pick_tm,
                    'num'                 => is_null($list['children']) ? 1 : count($list['children'])
                ];
            }
        }
        $result['list'] = $array;
        return $result;
    }


    public function getMsg()
    {
        return $this->msg;
    }

    public function getCode()
    {
        return $this->code;
    }

    protected function getArrayResult($url, $data = [])
    {
        $url    = self::BASE_URL . $url;
        $header = [
            'Authorization' => get_system_config('feng_chao_token'),
            //            "Host"          => "webchatapp.fcbox.com",
            //            "Fc_version_no" => "907901",
            //            "content-type"  => "application/x-www-form-urlencoded",
            //            "Cookie"        => "507644725977968640",
            //            "User-Agent"    => "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312b) NetType/WIFI Language/zh_CN",
            //            "Referer"       => " https://servicewechat.com/wxf953eb11611bb594/403/page-frame.html"
        ];
        $result = curl()->form_params()->set_header($header)->set_http_errors(false)->post($url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->code = -1;
            $this->msg  = '解析返回结果失败';
            return false;
        }
        if ($result['success'] !== true) {
            $this->code = $result['code'];
            $this->msg  = $result['msg'];
            return false;
        }
        return $result;
    }
}