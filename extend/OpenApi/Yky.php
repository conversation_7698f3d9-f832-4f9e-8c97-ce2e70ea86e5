<?php

namespace OpenApi;

use Exception;

class Yky
{
    /** 接口基础地址 */
    const BASE_URL = 'https://openapi.1card1.cn/';
    /** 商户OPENID */
    public $openid;

    /** 商户SECRET */
    public $secret;
    /** status */
    public $status;
    /** message */
    public $message;
    /** action */
    public $action;
    /** 默认操作工号,默认10000工号 */
    protected $default_user_account;
    /** 重复提交间隔时间 */
    protected $resubmit_expire = 3;

    /**
     *  constructor.
     * @param array $options
     * @throws Exception
     */
    public function __construct($options = array())
    {
        if (empty($options['openid']) || empty($options['secret'])) {
            throw new Exception('openid或者secret不能为空');
        }
        $this->openid               = $options['openid'];
        $this->secret               = $options['secret'];
        $this->default_user_account = (!empty($options['yky_default_user_account'])) ? $options['yky_default_user_account'] : '10000';
    }

    /**
     * 验证签名
     * @return bool|string 签名值
     */
    public function verify_signature()
    {
        try {
            //  openId = [OpenId] & signature = [Signature] & timestamp = [TimeStamp]
            $remote_signature = $_GET['Signature'];
            $local_signature  = $this->getSignature($_POST['data'], $_GET['TimeStamp']);
            return $remote_signature === $local_signature;
        } catch (Exception $e) {
            $this->message = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取签名
     * @param string $data 签名json数据
     * @param int $timestamp 时间戳
     * @return bool|string 签名值
     */
    protected function getSignature($data, $timestamp)
    {
        return strtoupper(MD5($this->openid . $this->secret . $timestamp . $data));
    }

    public function get_recommend_member_card_id_or_mobile($card_mobile_guid)
    {
        $result = $this->get_recommend_member_card_id($card_mobile_guid);
        if ($result === false) {
            return false;
        }
        if (!tools()::is_mobile($result) && !empty($result)) {
            //获取到的卡号不为空且不是手机号 则继续查找推荐人信息
            $member = $this->get_member_info($result);
            $result = isset($member['Mobile']) ? $member['Mobile'] : false;
        }
        return tools()::is_mobile($result) ? $result : '';
    }

    public function get_recommend_member_card_id($card_mobile_guid)
    {
        $result = $this->get_member_info($card_mobile_guid);
        if ($result === false) {
            return false;
        }
        return $result['RecommendMemberCardId'] ?: null;
    }

    public function get_member_info($card_mobile_guid, $is_get_ext_value = false)
    {
        if ($card_mobile_guid === '' || is_null($card_mobile_guid)) {
            return false;
        }
        $result = $this->Get_MemberInfo(['cardId' => $card_mobile_guid, 'isGetExtValue' => $is_get_ext_value]);
        if ($result === false || empty($result['data'][0])) {
            return false;
        }
        return $result['data'][0];
    }

    public function Get_BusinessInfo()
    {
        $data = ["userAccount" => $this->default_user_account];
        return $this->getArrayResult(__FUNCTION__, $data, 'BusinessApi');
    }

    public function Get_MemberInfo($post_data)
    {
        $default_data = [
            "cardId"        => '',
            "password"      => '',
            "isGetExtValue" => false,
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    protected function getArrayResult($action, $post_data = [], $controller = 'OpenApi')
    {
        $this->action = $action;
        $data         = json_encode($post_data, JSON_UNESCAPED_UNICODE);
        $api_url      = self::BASE_URL . $controller . '/';
        $timestamp    = time();
        $signature    = $this->getSignature($data, $timestamp);
        $url          = $api_url . $action . '?openId=' . $this->openid . '&signature=' . $signature . "&timestamp=" . $timestamp;
        $result       = curl()->form_params()->post($url, ['data' => $data])->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->status  = 'result error';
            $this->message = '解析返回结果失败';
            return false;
        }
        if (isset($result['status']) && $result['status'] !== 0) {
            $this->status  = $result['status'];
            $this->message = $result['message'];
            $list          = "卡号不存在|会员不存在|密码错误|密码有误|锁定|卡号已被删除";
            if (!preg_match("/$list/", $result['message'])) {
                send_qy_wechat($result['message']);
            }
            return false;
        }
        if (isset($result['success']) && $result['success'] !== true) {
            return false;
        }
        return $result;
    }

    public function get_member_card_id_or_mobile($card_mobile_guid)
    {
        if (tools()::is_mobile($card_mobile_guid)) {
            return $card_mobile_guid;
        }
        if (empty($card_mobile_guid)) {
            return false;
        }
        $result = $this->get_member_info($card_mobile_guid);
        if ($result === false) {
            return false;
        }
        if (tools()::is_mobile($result['CardId'])) {
            return $result['CardId'];
        } elseif (tools()::is_mobile($result['Mobile'])) {
            return $result['Mobile'];
        }
        $this->message = '会员标识:' . $card_mobile_guid . ',卡号:' . $result['CardId'] . ',手机号:' . $result['Mobile'] . '都不是手机号格式!';
        return false;
    }


    //获取会员信息

    public function get_recommend_member_card_id_by_openid($openid, $deviceType = 1)
    {
        if (empty($openid)) {
            return false;
        }
        $result = $this->get_member_info_by_openid($openid, $deviceType);
        if ($result === false) {
            return false;
        }
        return $result['RecommendMemberCardId'];
    }

    //获取会员级别列表

    public function get_member_info_by_openid($openid, $deviceType = 1)
    {
        if (empty($openid)) {
            return false;
        }
        $data   = [
            "deviceType"  => $deviceType,//用户类型(1,微信;2,支付宝;4,百度直达号)
            'thirdOpenId' => $openid
        ];
        $result = $this->GetMemberGuidByOpenId($data);
        if ($result === false) {
            return false;
        }
        return !empty($result['data'][0]) ? $result['data'][0] : false;
    }

    // 编辑会员信息

    public function GetMemberGuidByOpenId($post_data)
    {
        $default_data = [
            "deviceType"  => 1,//用户类型(1,微信;2,支付宝;4,百度直达号)
            'thirdOpenId' => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data), 'VipCloud');
    }


    //会员注册

    public function Get_MemberGroupPagedV2($post_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "pageIndex"   => 0,
            "pageSize"    => 200,
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }


    //会员登录

    public function Update_Member($post_data = [])
    {
        $default_data = [
            "cardId" => '',
            //"extValue" => ''扩展字段(Json类型字符串数组)
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //获取积分折扣系数

    public function Add_Member($post_data)
    {
        $default_data = [
            "cardId"          => '',
            "password"        => '',
            "memberGroupName" => '',
            "trueName"        => '',
            "userAccount"     => $this->default_user_account,
            "sex"             => '',
            "birth"           => '',
            "mobile"          => '',
            "idCard"          => '',
            "email"           => '',
            "provinceId"      => '',
            "cityId"          => '',
            "countyId"        => '',
            "address"         => '',
            "recommendCardId" => '',
            "meno"            => '',
            "openId"          => '',
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //获取商品列表

    public function MemberLogin($post_data)
    {
        $default_data = [
            "cardId"   => '',
            "password" => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //获取商品列表

    public function Get_PointAndDiscount($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "cardId"      => '',
            "barcode"     => '',
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //获取会员列表

    public function get_goods_info_by_barcode($barcode)
    {
        if (empty($barcode)) {
            return false;
        }
        $where     = " Barcode='" . $barcode . "'";
        $post_data = [
            "where" => $where,
        ];
        $result    = $this->Get_GoodsItemsPagedV2($post_data);
        if ($result === false) {
            return false;
        }
        if ($result['total'] == 0) {
            $this->message = '没有符合条件的商品';
            return false;
        }
        if ($result['total'] > 1) {
            $this->message = '查找到多个符合条件商品';
            return false;
        }
        return $result['data'][0];
    }


    //发送优惠券
    //http://openapi.1card1.cn/OpenApiDoc/SendCoupon

    public function Get_GoodsItemsPagedV2($post_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "where"       => "",
            "pageIndex"   => 0,
            "pageSize"    => 200,
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //https://openapi.1card1.cn/BusinessApi/Get_UserAccountPaged?openId=[OpenId]&signature=[Signature]&timestamp=[TimeStamp]
    //获取工号

    public function Get_MembersPagedV2($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "pageIndex"   => 0,
            "pageSize"    => 200,
            "orderBy"     => " RegisterTime desc "
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //获取扩展字段

    public function SendCoupon($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'mobiles'     => '',
            'cardIds'     => '',
            'couponGuid'  => '',
            'sendCount'   => 1
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    public function Get_Extensions($type = 1)
    {
        $data = [
            "userAccount" => $this->default_user_account,
            "parentType"  => $type,
        ];
        return $this->getArrayResult(__FUNCTION__, $data);
    }

    //获取优惠券列表

    public function get_chain_store_guid_by_user_account($user_account)
    {
        $data['where'] = " UserAccount='" . $user_account . "'";
        $result        = $this->Get_UserAccountPaged($data);
        if ($result === false) {
            return false;
        }
        if (empty($result['totalCount'])) {
            $this->message = '没有符合条件的工号';
            return false;
        }
        return $result['data'][0]['ChainStoreGuid'];
    }

    public function Get_UserAccountPaged($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "where"       => " 1=1 ",
            "pageIndex"   => 0,
            "pageSize"    => 200,
            "orderBy"     => " Guid desc "
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data), 'BusinessApi');
    }

    //获取已发送优惠券

    public function Get_CouponPagedV2($post_data)
    {

        $default_data = [
            "userAccount" => $this->default_user_account,
            "where"       => " 1=1 ",
            "pageIndex"   => 0,
            "pageSize"    => 200,
            "orderBy"     => " CreateTime desc "
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //使用优惠券
    //http://openapi.1card1.cn/OpenApiDoc/SubCoupon

    public function get_coupon_send_note_by_cardid($card_id, $coupon_arr)
    {
        if (empty($coupon_arr)) {
            return false;
        }
        $coupon_guid        = join("','", $coupon_arr);
        $post_data['where'] = " CouponGuid IN ('" . $coupon_guid . "') AND CardId='" . $card_id . "' AND Flag=1 AND EnableCount>0";
        $result             = $this->Get_CoupnSendPagedV2($post_data);
        return !empty($result['total']) ? $result['data'] : false;
    }

    public function Get_CoupnSendPagedV2($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "pageIndex"   => 0,
            "pageSize"    => 200,
            //"orderBy" => " cardId desc "
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //增加/减少储值,不参与店间结算
    //http://openapi.1card1.cn/OpenApiDoc/Update_MemberValue

    public function SubCoupon($post_data)
    {
        $default_data = [
            "userAccount"    => $this->default_user_account,
            'couponSendGuid' => '',
            'subCount'       => 1
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //增加/减少积分,不参与店间结算
    //http://openapi.1card1.cn/OpenApiDoc/Update_MemberPoint
    public function point_transfer($data)
    {
        $transfer_point = abs($data['point']);
        //先扣除
        $post_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => $data['from_card_id'],
            'point'       => -$transfer_point,
            'meno'        => '自助转账,转给卡号:' . $data['to_card_id'] . '|备注:' . $data['memo']
        ];
        $result    = $this->Update_MemberPoint($post_data);
        if ($result === false) {
            return false;
        }
        //增加,先判断是否有手续费
        if (!empty($data['fee']) && $data['fee'] > 0) {
            //计算手续费,精确到两位小数
            $fee = round($transfer_point * $data['fee'], 2);
            //看手续费是否达到封顶
            $fee = ($fee > $data['fee_limit']) ? $data['fee_limit'] : $fee;
            //入账金额要减去手续费
            $transfer_point = $transfer_point - $fee;
        }
        $post_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => $data['to_card_id'],
            'point'       => $transfer_point,
            'meno'        => '自助转账,来自卡号:' . $data['from_card_id'] . ' | 备注:' . $data['memo']
        ];
        $result    = $this->Update_MemberPoint($post_data);
        if ($result === false) {
            return false;
        }
        return true;
    }

    public function value_transfer($data)
    {
        $transfer_value = abs($data['value']);
        //先扣除
        $post_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => $data['from_card_id'],
            'value'       => -$transfer_value,
            'meno'        => '自助转账,转给卡号:' . $data['to_card_id'] . '|备注:' . $data['memo']
        ];
        $result    = $this->Update_MemberValue($post_data);
        if ($result === false) {
            return false;
        }
        //增加,先判断是否有手续费
        if (!empty($data['fee']) && $data['fee'] > 0) {
            //计算手续费,精确到两位小数
            $fee = round($transfer_value * $data['fee'], 2);
            //看手续费是否达到封顶
            $fee = ($fee > $data['fee_limit']) ? $data['fee_limit'] : $fee;
            //入账金额要减去手续费
            $transfer_value = $transfer_value - $fee;
        }
        $post_data = [
            'userAccount' => $this->default_user_account,
            'cardId'      => $data['to_card_id'],
            'value'       => $transfer_value,
            'meno'        => '自助转账,来自卡号:' . $data['from_card_id'] . ' | 备注:' . $data['memo']
        ];
        $result    = $this->Update_MemberValue($post_data);
        if ($result === false) {
            return false;
        }
        return true;
    }

    public function Update_MemberValue($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            "cardId"      => '',
            "value"       => '',
            'meno'        => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //增加/减少积分,不参与店间结算
    //https://openapi.1card1.cn/OpenApiDoc/Update_MemberPoint

    public function Update_MemberPoint($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'meno'        => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    public function get_goods_guid_from_consume_note($guid)
    {
        if (empty($guid)) {
            return false;
        }
        $result = $this->Get_ConsumeNoteItemPagedV2($guid);
        if ($result === false) {
            return false;
        }
        if ($result['total'] == 0) {
            $this->message = '消费项目明细为空';
            return false;
        }
        $consume_data   = $result['data'];
        $goods_guid_arr = [];
        foreach ($consume_data as $key => $val) {
            $goods_guid_arr[] = $val['GoodsItemGuid'];
        }
        return $goods_guid_arr;
    }

    //http://openapi.1card1.cn/OpenApiDoc/Get_ConsumeNoteItemPagedV2
    public function Get_ConsumeNoteItemPagedV2($guid)
    {
        $data = [
            "guid" => $guid,
        ];
        return $this->getArrayResult(__FUNCTION__, $data);
    }

    //获取消费列表
    //http://openapi.1card1.cn/OpenApiDoc/Get_ConsumeNotePagedV2
    public function Get_ConsumeNotePagedV2($post_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => " OperateTime Asc",
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //获取储值记录
    //http://openapi.1card1.cn/OpenApiDoc/Get_AllValueNotePagedV2
    public function Get_AllValueNotePagedV2($post_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => " OperateTime Asc",
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //获取积分记录
    //http://openapi.1card1.cn/OpenApiDoc/Get_AllPointNotePagedV2
    public function Get_AllPointNotePagedV2($post_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => " OperateTime Asc",
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //获取门店列表
    //http://openapi.1card1.cn/OpenApiDoc/Get_ChainStorePagedV2
    public function Get_ChainStorePagedV2($post_data, $return_data = [])
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 200,
            'orderBy'     => "",
        ];
        $data         = array_merge($default_data, $post_data);
        $result       = $this->getArrayResult(__FUNCTION__, $data);
        if ($result) {
            //把返回的数据 追加到$return_data中
            foreach ($result['data'] as $key => $val) {
                $return_data[] = $val;
            }
            $count = count($result['data']); //数量
            if ($count == $data['pageSize']) {
                $data['pageIndex'] = $data['pageIndex'] + 1;
                return $this->Get_ChainStorePagedV2($data, $return_data);
            }
            $result['data']  = $return_data;
            $result['total'] = count($return_data);
        }
        return $result;
    }


    //http://openapi.1card1.cn/VipCloudDoc/BindMember
    public function BindMember($post_data)
    {
        $default_data = [
            "deviceType"  => 1,
            'thirdOpenId' => '',
            'cardId'      => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data), 'VipCloud');
    }

    public function UPayOrder($post_data)
    {
        $default_data = [
            "billNumber"     => '',//订单号(同一个商家下唯一)
            'totalFee'       => 0,//总金额(以分为单位，1元= 100 分)
            'chainstoreGuid' => '',
            //'openId'         => '',
            //'memberGuid'     => '',
            'notifyUrl'      => '',
            //'attach'         => '',
            //'redirectUrl'    => '',
            //'isPushMessage'  => '',
            'uPayDiscount'   => 0,
            //'consumeNoteItems' => '',
            'meno'           => '',
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data), 'VipCloud');
    }

    public function QueryOrder($post_data)
    {
        $default_data = [
            "billNumber" => '',//订单号(同一个商家下唯一)
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data), 'VipCloud');
    }

    //https://openapi.1card1.cn/OpenApi/SendSms?openId=[OpenId]&signature=[Signature]&timestamp=[TimeStamp]
    public function SendSms($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'mobile'      => '',
            'content'     => ''
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }
    //当消费明细为空时，等同于快速消费
    //特别注意：移动支付仅供记账，并没有实际调用微信支付接口,当且仅当thirdPayType与thirdPayValue都不为空时有效
    //https://openapi.1card1.cn/OpenApi/Consume?openId=[OpenId]&signature=[Signature]&timestamp=[TimeStamp]
    public function Consume($post_data)
    {
        $default_data = [
            "cardId"          => '',//否 卡号（为空时表示散客，此时paidValue、paidPoint不生效）
            "password"        => '',//否	会员密码（会员系统中设置储值消费或者积分消费需要密码时，此项必须）
            "userAccount"     => $this->default_user_account,//是	工号
            "operateTime"     => '',//否	提交时间（补录单据时使用）
            "totalMoney"      => 0,//是	应付金额
            "totalPaid"       => 0,//是	实付金额
            "paidMoney"       => 0,//是	现金支付
            "paidPoint"       => 0,//是	积分抵现（金额,cardId为空时不生效）
            "paidValue"       => 0,//是	储值支付（cardId为空时不生效）
            "paidCard"        => 0,//是	银联支付
            "paidCoupon"      => '',//否	优惠券支付(新增参数。此参数为0或者为空时，couponList参数无效)
            "thirdPayType"    => 0,//否	移动支付类型，1：微信支付、2：支付宝支付（thirdPayType与thirdPayValue同时不为空时有效）
            "thirdPayValue"   => 0,//否	移动支付金额（thirdPayType与thirdPayValue同时不为空时有效）
            "thirdPayOrderNo" => '',//否	 移动支付订单号(向移动支付接口发起请求时的订单号)
            "thirdpayTradeNo" => '',//否	 移动支付交易号（移动支付接口返回的交易单号）
            "paidOther"       => 0,//是	其他支付(金额)
            "otherPayType"    => '',//否	其他支付类型（其他支付的别名，在“系统设置-参数设置-其他参数-支付方式名称”中设置）
            "meno"            => '',//否	单据备注
            //"consumeList"     => [],//Json字符串（barcode：商品编码 （通过接口“获取商品列表”获得“Barcode”字段） ；name：商品名称；disCount：商品折扣；number:商品数量； paidMoney：该明细总应付金额；meno：此明细的备注）"consumeList":[] 或者不传consumeList,等同于快速消费
            //"couponList"      => '',//否	Json字符串（sendNoteGuid：优惠券发送记录唯一标识 （通过接口“获取已发送优惠券”获得“Guid”字段） ；count：核销数量）此参数为空时，paidCoupon无效
            //"thirdOpenId"     => '',//微信openid 或支付宝的user_id（供支付即会员使用，与deviceType共同生效）
            //"deviceType"      => '',//设备登录类型：1,表示微信登录;2表示支付宝（供支付即会员使用，与thirdOpenId共同生效）
            //"uniqueCode"      => '',//唯一码（实现幂等，如果该唯一码已经执行成功，则不会再执行，并返回相关联的单据号）
        ];
        $post_data    = array_merge($default_data, $post_data);
        $cache_key    = 'yky:' . __FUNCTION__ . ':' . $post_data['cardId'];
        if (cache($cache_key)) {
            sleep($this->resubmit_expire);
        }
        $result = $this->getArrayResult(__FUNCTION__, $post_data);
        cache($cache_key, time(), $this->resubmit_expire);
        return $result;

    }

    //撤单接口
    public function ReturnGoods($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'billNumber'  => '',
            'meno'        => '',
            'returnItems' => '',//退货明细（仅对消费收银单据有效。Json格式数组，ReturnNumber：此次退货数量;GoodsItemGuid：退货商品唯一标识，通过“获取消费明细”获得GoodsItemGuid字段;Guid：单据明细唯一标识,通过“获取消费明细”获得Guid字段）
        ];
        return $this->getArrayResult(__FUNCTION__, array_merge($default_data, $post_data));
    }

    //会员充值
    public function Add_Value($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'cardId'      => '',
            'value'       => '',
            'valuePlus'   => '',
            'paidMoney'   => 0,
            'meno'        => '',
            "uniqueCode"  => '',//唯一码（实现幂等，如果该唯一码已经执行成功，则不会再执行，并返回相关联的单据号）
        ];
        $post_data    = array_merge($default_data, $post_data);
        if ($post_data['value'] <= 0) {
            $this->message = '会员充值金额不能小于0!';
            return false;
        }
        if ($post_data['paidMoney'] == 0) {
            unset($post_data['paidMoney']);
        }
        $cache_key = 'yky:' . __FUNCTION__ . ':' . $post_data['cardId'];
        if (cache($cache_key)) {
            sleep($this->resubmit_expire);
        }
        $result = $this->getArrayResult(__FUNCTION__, $post_data);
        cache($cache_key, $post_data['cardId'], $this->resubmit_expire);
        return $result;
    }

    //储值扣费
    public function ValueConsume($post_data)
    {
        $default_data = [
            "userAccount" => $this->default_user_account,
            'cardId'      => '',
            'password'    => '',
            'value'       => '',
            'meno'        => '',
            "uniqueCode"  => '',//唯一码（实现幂等，如果该唯一码已经执行成功，则不会再执行，并返回相关联的单据号）
        ];
        $post_data    = array_merge($default_data, $post_data);
        if ($post_data['value'] <= 0) {
            $this->message = '储值扣费金额不能小于0!';
            return false;
        }
        return $this->getArrayResult(__FUNCTION__, $post_data);
    }

    //受限充值接口 http://openapi.1card1.cn/BusinessDoc/Update_ChainStoreLimit
    public function Update_ChainStoreLimit($post_data)
    {
        $default_data = [
            "userAccount"     => $this->default_user_account,
            'chainStoreGuid'  => '',
            'totalLimitValue' => '',
            'limitType'       => '',
            'meno'            => '',
        ];
        $post_data    = array_merge($default_data, $post_data);
        return $this->getArrayResult(__FUNCTION__, $post_data, 'BusinessApi');
    }
}