<?php

namespace OpenApi;

use Exception;

class SubMerchant
{
    /** 接口基础地址 */
    const API_URL = 'https://pay.weixin.qq.com/index.php';
    public $cookies;
    public $merchant_code;
    public $ecc_csrf_token;
    public $is_retry = false;
    public $debug = false;
    public $request_url = '';

    /**
     * 当前请求方法参数
     * @var array
     */
    protected $currentMethod = [];
    public $pid_list = [
        1073 => '服务商刷脸支付',
        1045 => '服务商平台退款',
        1046 => '服务商API退款',
        1010 => '服务商分账',
        1077 => '服务商酒店押金',
    ];

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct($config = array())
    {
        $this->init($config);
    }

    /**
     * @param array $config
     * @throws Exception
     */
    protected function init($config)
    {
        $default_config    = [
            'merchant_code' => '1531081171',
            'cookies'       => '',
            'debug'         => false,
        ];
        $config            = array_merge($default_config, $config);
        $config['cookies'] = $config['cookies'] ?: cache('sub_merchant_cookies:' . $config['merchant_code']);
        if (empty($config['cookies'])) {
            // $this->send_local_login_url();
            //throw new \Exception('缺少cookies参数');
        }
        $this->cookies        = $config['cookies'];
        $this->debug          = $config['debug'];
        $this->merchant_code  = $config['merchant_code'];
        $cookies_array        = tools()::cookies_to_array($this->cookies);
        $this->ecc_csrf_token = $cookies_array['ecc_csrf_cookie'] ?? ''; //需考虑缓存中无 ecc_csrf_cookie 也可以初始化
        $this->is_retry       = false;
        $this->request_url    = '';
    }

    /**
     * 设置debug模式
     * @access public
     * @param bool $debug
     * @return $this
     */
    public function debug($debug = true): SubMerchant
    {
        $this->debug = $debug;
        return $this;
    }

    /**
     * 获取cookies
     * @access public
     * @return string
     */
    public function get_cookies()
    {
        return $this->cookies;
    }

    /**
     * 发送登录链接 //todo 暂时未用到
     * @access public
     * @param string $merchant_code
     * @param string $guid
     * @return mixed
     * @throws Exception
     */
    public function push_send_login_url_job($merchant_code = '', $guid = '')
    {
        $merchant_code = $merchant_code ?: $this->merchant_code;
        $guid          = $guid ?: create_guid();
        //添加异步任务处理该订单
        $job_data = [
            'merchant_code' => $merchant_code,
            'guid'          => $guid,
        ];
        job()->set_job_name('Merchant@send_login_url')->push_job($job_data);
        return true;
    }

    /**
     * 发送登录链接
     * @access public
     * @param string $merchant_code
     * @param string $guid
     * @return mixed
     * @throws Exception
     */
    public function send_login_url($merchant_code = '', $guid = '')
    {
        $login_url     = self::API_URL . '/core/home/<USER>';
        $cookies       = curl()->get($login_url)->get_cookies();
        $cookies_array = tools()::cookies_to_array($cookies);
        if (empty($cookies_array['ecc_csrf_cookie'])) {
            return false;
        }
        $data                      = [
            'type'           => 1,
            'employeeId'     => 0,
            'finger'         => time() . '',
            'ecc_csrf_token' => $cookies_array['ecc_csrf_cookie'],
        ];
        $getQrcodeUrl              = self::API_URL . '/core/home/<USER>' . time() . '000&g_ty=ajax';
        $result                    = curl()->form_params()->set_cookies($cookies)->post($getQrcodeUrl, $data)->get_body();
        $result                    = $this->_parseResult($result);
        $qrcode_session_id         = $result['data']['qrcodeSessionId'];
        $data['qrcode_session_id'] = $qrcode_session_id;
        $data['sign']              = $qrcode_session_id;
//        $login_url                 = 'https://pay.weixin.qq.com/core/home/<USER>' . $qrcode_session_id;
        $login_url = 'https://pay.weixin.qq.com/core/home/<USER>' . $qrcode_session_id; //2023年4月开始只能用扫码打开小程序的
        $array     = [
            'url'      => $login_url,
            'title'    => '【微信】微信商户平台重新登陆提醒',
            'keyword1' => '商户号: ' . $merchant_code,
        ];
        //send_workweixin_textcard($array); //发送登录地址
        $cache_key = 'merchant_login:' . $guid;
        cache($cache_key, ['status' => 3, 'login_url' => $login_url], 600);
        $login_info = [
            'data'      => $data,
            'cookies'   => $cookies,
            'login_url' => $login_url
        ];
        job()->set_job_name('Merchant@get_login_status')->push_job($login_info, 10); // 延迟查询是否登录成功
        return true;
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return $this
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @return bool|array
     */
    protected function retry()
    {
        $this->is_retry = true;
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @param boolean $return_json
     * @return string|array
     * @throws Exception
     */
    protected function _parseResult($result, $return_json = true)
    {
        if ($return_json === false) {
            return $result;
        }
        if (!is_array($result)) {
            if (strpos($this->request_url, 'down_summary_bill') !== false) {
                return $result;
            }
            $this->send_local_login_url();
            throw new Exception('解析返回结果失败');
        }
        if ((isset($result['code']) && $result['code'] !== 0) || (isset($result['errorcode']) && $result['errorcode'] !== 0) || (isset($result['return_code']) && $result['return_code'] != 0 && $result['return_code'] != 200)) {
            $code    = $result['code'] ?? ($result['errorcode'] ?? ($result['return_code'] ?? -1));
            $message = $result['msg'] ?? ($result['return_msg'] ?? '微信商户平台请求出错');
            $msg     = $message . '(' . $code . ')';
            if ($code == 10) {
                //登录超时(10) 则发送登录提醒
                $this->send_local_login_url();
            }
            $retry_code_array = [10001];
            //暂时数据则重试
            if (isset($this->currentMethod['method']) && empty($this->is_retry) && in_array($code, $retry_code_array)) {
                return $this->retry();
            }
            throw new Exception($msg, $code);
        }
        return $result;
    }

    /**
     * 发送登录链接
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_local_login_url()
    {
        if (!is_work_time()) {
            return false;
        }
        $cache_key     = __FUNCTION__ . ':' . $this->merchant_code;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($cache_key);
        if (cache($cache_key)) {
            return false;
        }
        $local_login_url = (string)url('/member/merchant/login', ['bid' => config('app.super_admin_business_guid')], false, config('app.app_host_domain'));
        if (strpos($local_login_url, 'https') === false) {
            $local_login_url = str_replace('http', 'https', $local_login_url);
        }
        $textcard = [
            'url'         => $local_login_url,
            'title'       => '【本地】微信商户平台重新登陆提醒',
            'description' => '商户号: ' . $this->merchant_code,
        ];
        qy_weixin_msg()->textcard($textcard)->send(); //发送本地登录地址,实际并不会请求微信,防止登录超时
        cache($cache_key, format_timestamp(), 15 * 60); //15分钟内不重复发送
        $lock_instance->unlock($lock);
    }

    /**
     * 获取登录状态
     * @access public
     * @param string $cookies
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function get_login_status($cookies, $data)
    {
        $getQrcodeStateUrl = 'https://pay.weixin.qq.com/mch/wechatlogin/login/getQrcodeState?nowTime=' . time() . '000&g_ty=ajax';
        $result            = curl()->setMaxRetries(5)->setRetryDecider(function ($result) {
            return (is_array($result) && isset($result['errcode']) && $result['errcode'] == 104) === false;
        })->set_cookies($cookies)->post($getQrcodeStateUrl, $data);
        $body_array        = $result->get_body();
        if (is_array($body_array) && isset($body_array['errcode']) && $body_array['errcode'] == 104) {
            //登录成功
            $getQrcodeStateCookiesUrl = self::API_URL . '/core/home/<USER>' . time() . '000&g_ty=ajax';
            $result                   = curl()->setMaxRetries(3)->setRetryDecider(function ($result) {
                return (is_array($result) && isset($result['errorcode']) && $result['errorcode'] == 101) === false;
            })->set_cookies($cookies)->form_params()->post($getQrcodeStateCookiesUrl, $data);
            $body_array               = $result->get_body();
            if (is_array($body_array) && isset($body_array['errorcode']) && $body_array['errorcode'] == 101) {
                $cookies = $result->get_cookies(); //登录成功后重新赋值cookies
                //{"errorcode":101,"msg":"login success","data":{"jump":true}} 上方如果获取body 是返回这样
                $cookies_array = tools()::cookies_to_array($cookies);
                if (empty($cookies_array['merchant_code'])) {
                    throw new Exception('cookies不包含merchant_code信息:' . $cookies);
                }
                $merchant_code = $cookies_array['merchant_code'];
                $config        = [
                    'merchant_code' => $merchant_code,
                    'cookies'       => $cookies,
                ];
                $this->init($config);
                $result = $this->check_cookie();
                if ($result === true) {
                    cache('sub_merchant_cookies:' . $merchant_code, $cookies);
                    cache('sub_merchant_cookies_update_time:' . $merchant_code, format_timestamp());
                }
                return $cookies_array;
            } else {
                wr_log('多次二次登录均失败');
                return false;
            }
        }
        return false;
    }

    /**
     * 检查cookies是否有效
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function check_cookie()
    {
        $result = $this->post('/extend/bind_wechat/getBindWechatInfo?g_ty=ajax');
        return (is_array($result) && isset($result['errorcode']) && $result['errorcode'] === 0);
    }

    /**
     * GET请求
     * @access protected
     * @param string $url
     * @param boolean $return_json
     * @return bool|array
     * @throws Exception
     */
    protected function get($url, $return_json = true)
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $this->request_url = self::API_URL . $url;
        if ($this->debug) {
            //debug模式只返回用于请求的数据
            return ['url' => $this->request_url];
        }
        $result = curl()->set_cookies($this->cookies)->get($this->request_url)->get_body();
        return $this->_parseResult($result, $return_json);
    }

    /**
     * POST请求
     * @param string $url
     * @param array $data
     * @param bool $ignore_log
     * @return bool|array
     * @throws Exception
     */
    protected function post($url, $data = [], $ignore_log = false)
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $data['ecc_csrf_token'] = $this->ecc_csrf_token;
        $url                    = self::API_URL . $url;
        if ($this->debug) {
            //debug模式只返回用于请求的数据
            return ['url' => $url, 'data' => $data];
        }
        $result = curl()->ignore_log($ignore_log)->form_params()->set_cookies($this->cookies)->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 获取授权状态
     * @access public
     * @param string $merchant_name_or_merchant_code 商户号或商户名称
     * @param integer $pid 产品ID  1073 默认获取刷脸权限 1046 API退款
     * @return mixed
     * @throws Exception
     */
    public function get_auth_list($merchant_name_or_merchant_code, $pid)
    {
        $data              = [
            'pid'        => $pid,
            'search_key' => $merchant_name_or_merchant_code,
            'page_num'   => 1,
            'page_size'  => 8,
        ];
        $result            = $this->post('/extend/product/query_sub_mch_list?g_ty=ajax', $data);
        $sub_mch_list      = $result['data']['sub_mch_list'];
        $merchant_id_array = [];
        foreach ($sub_mch_list as $mch) {
            if ($mch['authorizedState'] == '已授权') {
                $merchant_id_array[] = $mch['merchantCode'];
            }
        }
        return $merchant_id_array;
    }

    /**
     * 获取授权状态
     * @access public
     * @param string $merchant_name_or_merchant_code 商户号或商户名称
     * @param integer $pid 产品ID  1073 默认获取刷脸权限 1046 API退款
     * @return mixed
     * @throws Exception
     */
    public function get_auth_status($merchant_name_or_merchant_code, $pid)
    {
        $data      = [
            'pid'        => $pid,
            'search_key' => $merchant_name_or_merchant_code,
            'page_num'   => 1,
            'page_size'  => 8,
        ];
        $result    = $this->post('/extend/product/query_sub_mch_list?g_ty=ajax', $data);
        $auth_info = $result['data']['sub_mch_list'][0];
        return $auth_info['authorizedState'];
    }

    /**
     * 修改费率
     * @access public
     * @param integer $merchant_code 商户号
     * @param float $rate 费率 0.2-0.59
     * @return mixed
     * @throws Exception
     */
    public function crate_benchmarking($merchant_code, $rate)
    {
        $this->verify_passwd();
        $data = [
            'merchant_code' => $merchant_code,
            'apply_id'      => '',
            'rate'          => $rate,
            'end_time'      => strtotime('2021-06-30 23:59:59'), //当年最后一秒 2020-12-31 23:59:59
            'addition'      => 'nothing',
            'cert_id'       => 0,
        ];
        return $this->post('/xphp/crate_benchmarking/apply?g_ty=ajax', $data);
    }

    /**
     * 安全校验
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function verify_passwd()
    {
        $public_key   = "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZKSqdvDXeE/Ky2MUYb6X+8OBO\nl4GgBq+7d4B0YcgT8gAhCK+UZRyhOwUIBdyi3YwUp6KuhjGu0s+Yr/WeSL/Gw5XD\nz6hkli4FXvyXjcMyOF+7xmIf4n1KtZT1fECyjhKum2bz0Bvks8dh7mXkWQWsbhUB\nKavzEdMNt58NIxB2CwIDAQAB\n-----END PUBLIC KEY-----";
        $passwd_array = [
            '1266682501' => 'WnVZMG9aWE9CSGxKMVNPb0NCa0V6VTNFczU4MEYwY0FpcThTUHViUUdJUGZkYmZQSStjT2Fsa2NadTFYaHlOeStpVHpBaFpsZk14dDVIL0lsS0ZTNHBtVkJuWk0wY3hIdlJnWWhxNno5Um1HSG5teE1YVzVwWjV1TlZRUnFxaGw4b2QwS1oyNVpGWkpDUFZrVUNHb3QxWDRQWUJkWllsdUY0Vk1FakpVb3hRPQ==',
            '1531081171' => 'WGdzL2tYTWhyUzRqNGVkdDlqeGFmN1d2dElMbHY3aHdhNG9KUS9nQmN1MHJXVUxzdUJtZUZoM1F4d3ZPMTB3Y21Tb1hscUQ2QXlXRVQ5c0IrWEdxaE44THQrVmlzaGJ1elVPN2t6d2xadVdzeWsrejhHdjVsbkozNHhFeEF1S2NkdDR6YkFDa1lQbm1neU5HV2JxZFdUQ3ovOHF5ZHZhdUZYaTg1VlBweE4wPQ=='
        ];
        $passwd       = $passwd_array[$this->merchant_code] ?? '';
        $pin          = 622101;
        $sec_factor   = 2;
        $data         = ['pin' => $pin, 'sec_factor' => $sec_factor];
        $passwd       = base64_encode(tools()::rsa_encode(json_encode($data), $public_key));
        if (empty($passwd)) {
            throw new Exception('服务商商户号:' . $this->merchant_code . '未设置操作密码');
        }
        $this->get_public_key();
        $data = ['passwd' => $passwd, 'sec_factor' => $sec_factor];
        return $this->post('/core/risk_ctrl/verifyPasswd?return_url=https%3A%2F%2Fpay.weixin.qq.com%2Findex.php%2Fpublic%2Fproduct%2Fdetail%3Fpid%3D1073%26productType%3D1&g_ty=ajax', $data);
    }

    /**
     * 获取公钥
     * @access protected
     * @return mixed
     * @throws Exception
     */
    protected function get_public_key()
    {
        return $this->post('/core/risk_ctrl/getPublicKey?g_ty=ajax');
    }

    /**
     * 邀约
     * @access public
     * @param integer $apply_id
     * @param integer $pid 1073 刷脸支付  1046 API退款
     * @return mixed
     * @throws Exception
     */
    public function mmp_authorize_apply($apply_id, $pid)
    {
        $this->verify_passwd();
        $data = [
            'sub_mchid' => $apply_id,
            'pid'       => $pid,
            'cert_id'   => 0,
        ];
        return $this->post('/extend/product/mmp_authorize_apply?g_ty=ajax', $data);
    }

    /**
     * 获取奖励列表
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function get_reward_note($data = [])
    {
        $default_data = [
            'reward_type' => 16,//设备奖励
            'endTime'     => '',
            'beginTime'   => date("Y-m", strtotime("-2 month")), //截止最近2个月奖励数据
            'page'        => 1
        ];
        $data         = array_merge($default_data, $data);
        return $this->post('/extend/commission/getDataTables?g_ty=ajax', $data);
    }

    /**
     * 获取未确认金额
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function do_query_unconfirm_amount()
    {
        return $this->post('/extend/commissionconfirm/do_query_unconfirm_amount?g_ty=ajax');
    }

    /**
     * 获取费率申请列表
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function crate_benchmarking_getapplylist($data = [])
    {
        $default_data = [
            'state'  => 2,  //0 全部 1待审核 2已通过 3已驳回
            'ishttp' => 1,
            'limit'  => 10,
            'offset' => 0,
            '_'      => time()
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/xphp/crate_benchmarking/getapplylist';
        return $this->post($url, $data);
    }

    /**
     * 获取商户信息(从js中提取)
     * @access public
     * @param string $apply_id 申请ID
     * @return mixed
     * @throws Exception
     */
    public function get_merchant_info($apply_id)
    {
        $url    = '/core/applymentnew/index?mode=301&merchantId=' . $apply_id . '#/step4';
        $result = $this->get($url, false);
        $result = tools()::search_str('constant(\'APPLYMENT\',', '.constant', $result);
        $result = rtrim($result, ')');
        $result = json_decode($result, true);
        if (empty($result) || !is_array($result)) {
            throw new Exception('商户信息获取失败');
        }
        return $result;
    }

    /**
     * 获取商户信息(从API中提取)
     * @access public
     * @param string $merchant_id 商户号
     * @return mixed
     * @throws Exception
     */
    public function get_merchant_info_api($merchant_id)
    {
        $url    = '/xphp/crate_benchmarking/getmerchantinfo?merchant_code=' . $merchant_id . '&ishttp=1&_=' . time();
        $result = $this->get($url);
        return $result;
    }

    /**
     * 获取商户类目
     * @access public
     * @param string $apply_id 申请ID
     * @param integer $category_meta_id 类目ID
     * @return mixed
     * @throws Exception
     */
    public function get_merchant_category($apply_id, $category_meta_id)
    {
        $url = '/core/applymentnew/nav_path?mode=301&merchantId=' . $apply_id . '&categoryMetaId=' . $category_meta_id . '&timestamp=' . time();
        return $this->get($url);
    }

    /**
     * 获取类目列表
     * @access public
     * @param string $apply_id 申请ID
     * @return mixed
     * @throws Exception
     */
    public function get_category($apply_id)
    {
        $url = '/core/applymentnew/category?mode=3&merchantId=' . $apply_id . '&navId=1&&timestamp=' . time();
        return $this->get($url);
    }

    /**
     * 获取费率详情
     * @access public
     * @param string $apply_id 申请ID
     * @return mixed
     * @throws Exception
     */
    public function get_rate_detail($apply_id)
    {
        $url = '/xphp/crate_benchmarking/getdetail?apply_id=' . $apply_id . '&ishttp=1&_=' . time();
        return $this->get($url);
    }

    /**
     * 获取消息记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_merchant_innermsg()
    {
        $url = '/extend/cms/get_merchant_innermsg?g_ty=ajax';
        return $this->post($url);
    }

    /**
     * 获取消息记录详情
     * @access public
     * @param integer $message_id 消息ID
     * @return mixed
     * @throws Exception
     */
    public function read_inner_message($message_id)
    {
        $url = '/extend/cms/read_inner_message?g_ty=ajax';
        return $this->post($url, ['messageId' => $message_id]);
    }

    /**
     * 检查是否可以修改费率
     * @access public
     * @param string $merchant_id 商户ID
     * @return mixed
     * @throws Exception
     */
    public function crate_benchmarking_validate($merchant_id)
    {
        $data = [
            'merchant_code' => $merchant_id,
            'ishttp'        => 1,
            'g_ty'          => 'ajax',
        ];
        $url  = '/xphp/crate_benchmarking/validate';
        return $this->post($url, $data);
    }

    /**
     * 获取商户列表
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function merchant_list($data = [])
    {
        $default_data = [
            'merchantId'     => '', //申请单号
            'tenpayMchId'    => '', //商户号
            'companyName'    => '',
            'applymentState' => 6, // 0 全部 1 未提交 2 审核中 3 审核未通过 4 待账户验证 5 待签约 6 审核通过
            'page'           => 1
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/core/affiliate/query_merchant_applyment';
        return $this->post($url, $data, true);
    }

    //   /**
    //    * 商户号找申请单编号
    //    * @access public
    //    * @param integer $merchant_id
    //    * @return string
    //    * @throws \Exception
    //    */
    //   public function find_apply_id($merchant_id)
    //   {
    //       $merchant_info = $this->find_merchant_info(['tenpayMchId' => $merchant_id]);
    //       return $merchant_info['merchantId'];
    //   }

    //   /**
    //    * 申请单编号查找商户号
    //    * @access public
    //    * @param integer apply_id
    //    * @return string
    //    * @throws \Exception
    //    */
    //   public function find_merchant_id($apply_id)
    //   {
    //       $merchant_info = $this->find_merchant_info(['merchantId' => $apply_id]);
    //       return $merchant_info['tenpayMchId'];
    //   }

    /**
     * 获取商户列表2
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function merchants_list($data = [])
    {
        $default_data = [
            'create_time_begin' => 0,
            'create_time_end'   => time() . '000',
            'key'               => '', //申请单编号/商户号/商户名称
            'employee_id'       => '',
            'applyment_state'   => 6, // 0 全部 1 未提交 2 审核中 3 审核未通过 4 待账户验证 5 待签约 6 审核通过
            'page_num'          => 0,
            'page_size'         => 10
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/core/apply4sub/query_applyment_list';
        return $this->post($url, $data);
    }

    //   /**
    //    * 查找单个商户信息,该方法已废弃, merchant_list 获取的数据已经不全了 20200905
    //    * @access public
    //    * @param array $data
    //    * @return array
    //    * @throws \Exception
    //    */
    //   public function find_merchant_info($data)
    //   {
    //       $default_data = [
    //           'merchantId'     => '', //申请单号
    //           'tenpayMchId'    => '', //商户号
    //           'companyName'    => '', //公司名称
    //           'applymentState' => 0, // 0 全部 1 未提交 2 审核中 3 审核未通过 4 待账户验证 5 待签约 6 审核通过
    //           'page'           => 1
    //       ];
    //       $data         = array_merge($default_data, $data);
    //       $result       = $this->merchant_list($data);
    //       if ($result['total'] == 0) {
    //           throw new \Exception('商户信息不存在');
    //       }
    //       return $result['items'][0];
    //   }

    /**
     * 获取商户列表
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function get_apply_micro_list($data = [])
    {
        $default_data = [
            'pageNum'     => 1,
            'mchId'       => '', //申请单编号/商户号
            'companyName' => '', //商户名称
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/xphp/csub_mch_manage/get_apply_micro_list?ishttp=1&timestamp=' . tools()::get_bill_number();
        return $this->post($url, $data, true);
    }

    /**
     * 添加支付APPID
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function add_sub_appid($data = [])
    {
        $default_data = [
            'subAppId'     => '',
            'sub_mchid'    => '', //申请单号
            'actType'      => 'add',
            'isParentBank' => '',
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/extend/sub_dev_setting/set_sub_appid2?g_ty=ajax';
        return $this->post($url, $data);
    }

    /**
     * 查找商户状态
     * @access public
     * @param integer $status
     * @return string
     * @throws Exception
     */
    public function get_status_text($status)
    {
        // 0 全部 1 未提交 2 审核中 3 审核未通过 4 待账户验证 5 待签约 6 审核通过
        $array = [
            1 => '未提交',
            2 => '审核中',
            3 => '审核未通过',
            4 => '待账户验证',
            5 => '待签约',
            6 => '审核通过',
        ];
        return $array[$status] ?? '未知状态';
    }

    /**
     * 服务商-我的设备列表
     * @access public
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function service_provider_equipment_list($data = [])
    {
        //https://pay.weixin.qq.com/index.php/xphp/ciotbuyermch/service_provider_equipment_list?ishttp=1&deviceSn=&deviceModel=&cameraSn=&cameraName=&purState=0&pageIndex=1&pageSize=50
        $url          = '/xphp/ciotbuyermch/service_provider_equipment_list?';
        $default_data = [
            'ishttp'      => 1,
            'deviceSn'    => '', //设备SN
            'deviceModel' => '', //设备型号
            'deviceClass' => '', //设备类型 102 桌面收银
            'cameraSn'    => '', // 摄像头SN
            'cameraName'  => '', // 摄像头SN
            'purState'    => 0, //绑定状态 0 未绑定 1 已绑定
            'pageIndex'   => 1, //页码 1开始
            'pageSize'    => 50 //每页数量
        ];
        $data         = array_merge($default_data, $data);
        $url          .= http_build_query($data);
        return $this->get($url);
    }

    /**
     * 服务商-绑定设备
     * @access public
     * @param array $ids
     * @return array
     * @throws Exception
     */
    public function registration_equipment($ids)
    {
        if (empty($ids)) {
            throw new Exception('设备绑定失败,铺设服务商【' . $this->merchant_code . '】后台查询到设备为空!');
        }
        //https://pay.weixin.qq.com/index.php/xphp/ciotbuyermch/registration_equipment?ishttp=1&ids=68639,68638
        $url = '/xphp/ciotbuyermch/registration_equipment?ishttp=1&ids=' . join(',', $ids);
        return $this->get($url);
    }

    /**
     * 设备商-解绑服务商
     * @access public
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function untying_device($data = [])
    {
        //https://pay.weixin.qq.com/index.php/xphp/ciotftyrmch/untying_device?ishttp=1&_d=ya4dervol2m
        $url          = '/xphp/ciotftyrmch/untying_device?ishttp=1&_d=' . time();
        $default_data = [
            'device_list' => '' //device_list: [{"id":"429535","buyer_mch_code":"1531081171"},{"id":"427766","buyer_mch_code":"1531081171"}]
        ];
        $data         = array_merge($default_data, $data);
        return $this->post($url, $data);
    }

    /**
     * 设备商-设备列表
     * @access public
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function get_device_list($data = [])
    {
        //https://pay.weixin.qq.com/index.php/xphp/ciotftyrmch/get_device_list?ishttp=1&page_index=1&page_size=20&deviceSn=1&cameraSn=2&buyerMchCode=3&deviceClass=102&deviceModel=QD21-L&purState=1&inspectStatus=1&inspectTime=1580918400-1584288000&ecc_csrf_token=6e22df94bf837fb78b754848d748b9f4
        $url          = '/xphp/ciotftyrmch/get_device_list?';
        $default_data = [
            'ishttp'        => 1,
            'page_index'    => 1,//第几页
            'page_size'     => 20,//分页数量
            'buyerMchCode'  => '',//铺设服务商商户号
            'deviceSn'      => '',//设备SN
            'cameraSn'      => '',//摄像头SN
            'deviceClass'   => '',//设备类型 102 桌面收银
            'deviceModel'   => '',//设备型号 例如 QD21-L
            'purState'      => '',//绑定状态 0 未绑定 1 已绑定
            'inspectStatus' => '',//出厂工具检测状态 0 未通过 1 已通过 2 未检测
            'inspectTime'   => '',//出厂工具检测时间 格式 1580918400-1584288000
        ];
        $data         = array_merge($default_data, $data);
        $url          .= http_build_query($data);
        return $this->get($url);
    }

    /**
     * 设备服务商-绑定服务商
     * @access public
     * @param array $data
     * @return mixed
     * @throws Exception
     */
    public function bulk_fill_mch_code($data = [])
    {
        $default_data = [
            'device_list'    => '',
            'buyer_mch_code' => ''
        ];
        $data         = array_merge($default_data, $data);
        $url          = '/xphp/ciotftyrmch/bulk_fill_mch_code?ishttp=1&_d=' . time();
        return $this->post($url, $data);
    }

    /**
     * 获取账户列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_account_list()
    {
        $url = '/core/info/getAccountList?nowTime=' . time() . '&g_ty=ajax';
        return $this->get($url);
    }

    /**
     * 下载资金账单 以字符串形式返回
     * @param string $start_date
     * @param string $end_date
     * @param int $acc_type 1 基本户 5 运营账户
     * @return bool|array
     * @throws Exception
     */
    public function down_summary_bill($start_date, $end_date, $acc_type = 5)
    {
        $url = '/xphp/cfund_bill_nc/down_summary_bill?acc_type=' . $acc_type . '&begin_date=' . $start_date . '&end_date=' . $end_date;
        return $this->get($url);
    }

    /**
     * 重试操作
     * @param string $method
     * @param array $arguments
     * @return bool|array
     * @throws Exception
     */
    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->is_retry) {
            $this->is_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }
}