<?php

namespace OpenApi;

class Zentao
{
    /** 接口基础地址 */
    const BASE_URL = 'https://sz1card1.5upm.com';
    /** 登录账号 */
    public $username;
    /** 登录密码 */
    public $password;
    /** errcode */
    public $errcode = -1;

    /** errmsg */
    public $errmsg = '';

    public $_retry = false;

    protected $cookie;

    /**
     * Zentao constructor.
     * @param string $account
     * @param string $password
     */
    public function __construct($account, $password)
    {
        $this->username = $account;
        $this->password = $password;
    }

    public function create_story($data)
    {
        $default_data = [
            'product'         => '', // 19研发一部，20研发二部，21研发 三部，22运维部，24产品部
            'module'          => 0,
            'plan'            => '',
            'source'          => '',
            'needNotReview[]' => 0, // 是否需要 评审
            'title'           => '',
            'pri'             => 0, // 优先级
            'estimate'        => '',
            'spec'            => '',// 需求描述,支持HTML
            'verify'          => '',
            'mailto[]'        => '',
            'keywords'        => ''
        ];
        $data         = array_merge($default_data, $data);
        $url          = self::BASE_URL . '/story-create.html';
        $result       = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return tools()::search_str('view-', '.html', $result);
    }

    protected function http_request($url, $data = array(), $header = array())
    {
        if (!$this->cookie && !$this->get_login_cookie()) {
            return false;
        }
        $method = empty($data) ? 'GET' : 'POST';
        $result = curl()->form_params()->set_cookies($this->cookie)->set_header($header)->$method($url, $data)->get_body();
        if ($result === false) {
            $this->errmsg = __CLASS__ . __FUNCTION__ . '请求发生错误';
            return false;
        }
        return $this->_parseResult($result);
    }

    protected function get_login_cookie($username = '', $password = '')
    {
        if (!$username || !$password) {
            $username = $this->username;
            $password = $this->password;
        }
        $key = 'zentao:login_cookie:' . $username;
        if ($cookie = cache($key)) {
            return $this->cookie = $cookie;
        }
        $url  = self::BASE_URL . '/user-login.json';
        $data = [
            'account'  => $username,
            'password' => $password
        ];
        $curl = curl()->form_params()->post($url, $data);
        $body = $curl->get_body();
        if ($body['status'] == 'success' && isset($body['user'])) {
            $this->cookie = $curl->get_cookies();
            cache($key, $this->cookie, 600);
            return $this->cookie;
        }
        return false;
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (is_array($result)) {
            if (isset($result['status']) && $result['status'] == 'success' && isset($result['data'])) {
                return json_decode($result['data'], true);
            }
        }
        preg_match("/alert\('([^')]+)'/", $result, $matches);
        if (!empty($matches[1])) {
            $this->errmsg = $matches[1];
            send_qy_wechat($this->errmsg);
            return false;
        }
        if (strstr('self.location=', $result) && strstr('user-login-', $result)) {
            $this->clear_cookies();
            return false;
        }
        return $result;
    }

    //获得禅道后台登陆cookies

    protected function clear_cookies($username = '')
    {
        if (!$username) {
            $username = $this->username;
        }
        return cache('zentao:login_cookie:' . $username, NULL);
    }

    public function create_bug($data)
    {
        $default_data = [
            'product'       => '', // 19研发一部，20研发二部，21研发 三部，22运维部，24产品部
            'module'        => 0,
            'project'       => '',
            'openedBuild[]' => 'trunk', // 是否需要 评审
            'assignedTo'    => '',
            'type'          => 'codeerror',
            'title'         => '',
            'pri'           => 0, // 优先级
            'steps'         => '',// 需求描述,支持HTML
            'files[]'       => '',
            'labels[]'      => '',
            // 'uid'=>'',
            'case'          => 0,
            'caseVersion'   => 0,
            'result'        => 0,
            'testtask'      => 0,
        ];
        $data         = array_merge($default_data, $data);
        $header       = ['Accept' => 'application/json, text/javascript, */*'];
        $url          = self::BASE_URL . '/bug-create-' . $data['product'] . '-0-moduleID=0.html';
        $result       = $this->http_request($url, $data, $header);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return strstr($result, 'bug-browse-') ? true : false;
    }

    public function edit_story($data, $id)
    {
        $default_data = [
            'comment'    => '',
            'stage'      => 'planned', // 改成开发完毕
            'assignedTo' => '',
            'pri'        => 0
        ];
        $data         = array_merge($default_data, $data);
        $detail       = $this->get_detail('story', $id);
        $last_comment = end($detail['actions'])['comment'];
        if ($last_comment == $data['comment']) {
            return true;
        }
        $url    = self::BASE_URL . '/story-edit-' . $id . '.html';
        $result = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return $result;
    }

    public function get_detail($type, $id)
    {
        $url    = self::BASE_URL . '/' . $type . '-view-' . $id . '.json';
        $result = $this->http_request($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            ($this->_retry = true) && $this->clear_cookies();
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    public function edit_bug($data, $id)
    {
        $detail       = $this->get_detail('bug', $id);
        $bug          = $detail['bug'];
        $default_data = [
            'comment'     => '',
            'project'     => $bug ['project'],
            'branch'      => $bug ['branch'],
            'module'      => $bug ['module'],
            'openedBuild' => $bug ['openedBuild'],
            'severity'    => $bug ['severity'],
            'type'        => $bug ['type'],
            'pri'         => $bug ['pri'],
            'status'      => 'active',
            'assignedTo'  => ''
        ];
        $data         = array_merge($default_data, $data);
        $last_comment = end($detail['actions'])['comment'];
        if ($last_comment == $data['comment']) {
            return true;
        }
        $url    = self::BASE_URL . '/bug-edit-' . $id . '.html';
        $result = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        $this->resolve_bug($id);
        return $result;
    }

    public function resolve_bug($id)
    {
        $data   = ['resolution' => 'fixed'];
        $url    = self::BASE_URL . '/bug-resolve-' . $id . '.html';
        $result = $this->http_request($url, $data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function edit_task($data, $id)
    {
        $default_data = [
            'comment'    => '',
            'assignedTo' => ''
        ];
        $data         = array_merge($default_data, $data);
        $detail       = $this->get_detail('task', $id);
        $last_comment = end($detail['actions'])['comment'];
        if ($last_comment == $data['comment']) {
            return true;
        }
        $url    = self::BASE_URL . '/task-edit-' . $id . '.html';
        $result = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return $result;
    }

    public function close_story($id, $data = array())
    {
        //判断是否已经关闭状态
        $detail = $this->get_detail('story', $id);
        if ($detail['story']['status'] == 'closed') {
            return true;
        }
        //开始关闭
        $default_data = [
            'comment'        => '',
            'closedReason'   => 'done', // 改成开发完毕
            'duplicateStory' => '',
            'childStories'   => '',
        ];
        $data         = array_merge($default_data, $data);
        $url          = self::BASE_URL . '/story-close-' . $id . '.html?onlybody=yes';
        $result       = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return $result;
    }

    public function close_bug($id, $data = array())
    {
        //判断是否已经关闭状态
        $detail = $this->get_detail('bug', $id);
        if ($detail['bug']['status'] == 'closed') {
            return true;
        }
        //开始关闭
        $default_data = [
            'comment' => '',
        ];
        $data         = array_merge($default_data, $data);
        $url          = self::BASE_URL . '/bug-close-' . $id . '.html?onlybody=yes';
        $result       = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return $result;
    }

    public function close_task($id, $data = array())
    {
        //判断是否已经关闭状态
        $detail = $this->get_detail('task', $id);
        if ($detail['task']['status'] == 'closed') {
            return true;
        }
        //开始关闭
        $default_data = [
            'comment' => '',
        ];
        $data         = array_merge($default_data, $data);
        $url          = self::BASE_URL . '/task-close-' . $id . '.html?onlybody=yes';
        $result       = $this->http_request($url, $data);
        //       if ($result === false) {
        //           return $this->checkRetry(__FUNCTION__, func_get_args());
        //       }
        return $result;
    }

    public function get_unconfirmed_bugs($productid)
    {
        $url    = self::BASE_URL . '/bug-browse-' . $productid . '-0-unconfirmed-0--103-5-1.json';
        $result = $this->http_request($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    public function get_log($date, $json = false)
    {
        $ext    = $json ? 'json' : 'html';
        $url    = self::BASE_URL . '/company-calendar-27-' . $date . '-' . $date . '-0-0--1-yes.' . $ext;
        $result = $this->http_request($url);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    protected function get_status($type, $id)
    {
        $detail = $this->get_detail($type, $id);
        if ($detail === false) {
            return false;
        }
        return $detail[$type]['status'];
    }

    protected function is_closed_success($result)
    {
        return strstr($result, 'selfClose') ? true : false;
    }
}