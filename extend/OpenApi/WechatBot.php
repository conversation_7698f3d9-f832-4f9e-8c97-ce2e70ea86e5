<?php

namespace OpenApi;

use Exception;
use Throwable;

class WechatBot
{
    /** 接口基础地址 */
    const BASE_URL = 'http://wechat.i.ecarde.cn:7001/';
    /** $token */
    public $token;
    public $type;
    /** errcode */
    public $errcode;
    /** errmsg */
    public $errmsg;
    public $result;
    public $text = null;
    public $disable = true;


    /**
     * Yundama constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {

    }

    public function text($data)
    {
        $path = 'text';
        if ($this->disable) {
            return [];
        }
        return $this->getArrayResult($path, $data);
    }

    public function batch_text($msg, $receiver_list)
    {
        $result = [];
        if ($this->disable) {
            return $result;
        }
        $receiver_array = is_array($receiver_list) ? $receiver_list : explode(",", $receiver_list);
        $receiver_array = array_unique($receiver_array);
        $receiver_array = array_filter($receiver_array);
        foreach ($receiver_array as $receiver) {
            try {
                $data     = [
                    'aters'    => '',//批量发送不支持@
                    'msg'      => $msg,
                    'receiver' => $receiver
                ];
                $result[] = $this->text($data);
            } catch (Throwable|Exception $e) {
                $result[] = [
                    'status' => -1,
                    'data'   => false,
                    'error'  => $e->getMessage(),
                ];
            }
        }
        return $result;
    }

    protected function getArrayResult($path, $data = [], $method = 'POST')
    {
        $method = strtolower($method);
        $result = curl()->set_timeout(10)->$method(self::BASE_URL . $path, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->errcode = -1;
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if ($result['status'] !== 0) {
            $this->errcode = $result['error'] ?? '';
            return false;
        }
        return $result['data'] ?? $result;
    }
}