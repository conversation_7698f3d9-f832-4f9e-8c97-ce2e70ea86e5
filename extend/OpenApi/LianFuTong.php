<?php

namespace OpenApi;


use Exception;

class LianFuTong
{
    /** 接口基础地址 */
    const API_URL = 'http://newfront.liantuobank.com/NewFront/base/gateway.in';
    public $service;
    public $partner_id;
    public $core_merchant_no;
    public $status;
    public $errmsg;
    public $_retry = false;
    protected $partner_key;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $default_config         = [
            'partner_id'       => '*****************',
            'core_merchant_no' => 'EW_N1087491352',
            'partner_key'      => 'f6465866a929c81aed22d4d0efda3c6e',
        ];
        $default_config         = [
            'partner_id'       => '*****************',
            'core_merchant_no' => 'EW_N9361857321',
            'partner_key'      => '44be516adffd5c1e4eee95e4e5978d62',
        ];
        $config                 = array_merge($default_config, $config);
        $this->partner_id       = $config['partner_id'];
        $this->core_merchant_no = $config['core_merchant_no'];
        $this->partner_key      = $config['partner_key'];
        $this->_retry           = false;
    }

    /**
     * 生成签名
     * $paras 请求参数字符串
     * $key 密钥
     * return 生成的签名
     */

    function test_FrontQuery()
    {
        $this->service        = 'front.query';
        $body["out_trade_no"] = "EW_N8366110628_0001aaa23111";//商户订单号
        $body["trade_no"]     = "00060011806081011119020239178664";//交易流水号,支付请求返回结果参数
        $resp                 = $this->request($body); //发送请求
        dump($resp);
        return;
    }

    /**
     * 请求
     * @param array $data
     * @return bool|array
     * @throws Exception
     */
    function request($data)
    {
        if (empty($this->service)) {
            throw new Exception('api 地址不能为空');
        }
        $head["service"]             = $this->service;//订单查询API
        $head["version"]             = '1.0';//默认版本号
        $head["partner_id"]          = $this->partner_id;//联富通线下提供pid
        $head["input_charset"]       = 'UTF-8'; //编码格式，默认只支持UTF-8
        $head["core_merchant_no"]    = $this->core_merchant_no;//联富通后台核心商户编号
        $head["sign_type"]           = 'MD5';//加密方式，默认只支持MD5
        $sign_array                  = array_merge($head, $data);
        $head["sign"]                = $this->createSign($sign_array, $this->partner_key);
        $request_data["requestJson"] = json_encode(['head' => $head, 'body' => $data], JSON_UNESCAPED_UNICODE);
        $result                      = curl()->post(self::API_URL, $request_data)->get_body();
        return $this->_parseResult($result);
    }

    //------------------------------------------------------------
    // 订单查询API
    //------------------------------------------------------------

    /**
     * 创建签名
     * @param array $data
     * @param string $key
     * @return bool|array
     * @throws Exception
     */

    function createSign($data, $key)
    {
        ksort($data);
        $string = md5($this->getSignContent($data) . $key);
        return strtolower($string);
    }
    //------------------------------------------------------------
    // 扫码--公众号支付
    //------------------------------------------------------------

    /**
     * 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @param array $data 需要拼接的数组
     * @return string 拼接完成以后的字符串
     */
    function getSignContent($data)
    {
        $str = "";
        foreach ($data as $key => $val) {
            if ($val != null && $val != "" && $key != "key" && $key != "sign_type") {
                $str .= $key . "=" . $val . "&";
            }
        }
        return rtrim($str, '&');
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            return $result;
        }
        $result = json_decode($result, true);
        if (empty($result) || !is_array($result)) {
            $this->status = 'result error';
            $this->errmsg = '解析返回结果失败';
            return false;
        }
        if (!isset($result['head']) || !isset($result['body'])) {
            $this->status = 'not has head or body';
            $this->errmsg = '缺少head或body字段';
            return false;
        }
        $body = $result['body'];
        if (isset($body['is_success']) && $body['is_success'] === 'F') {
            $this->status = isset($body['error']) ? $body['error'] : -1;
            $this->errmsg = $body['message'];
            return false;
        }
        return $body;
    }

    function JSAPI($data)
    {
        $this->service = "front.jsapi";//扫码--公众号支付
        return $this->request($data); //发送请求
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}