<?php

namespace OpenApi;

use Exception;

class TencentEmail
{
    const API_URL_PREFIX = 'https://api.exmail.qq.com/cgi-bin';
    const MAIL_NEW_COUNT = '/mail/newcount?';
    const TOKEN_GET_URL = '/gettoken?';
    const LOG_MAIL = '/log/mail?';
    public $debug = false;
    public $errCode = 40001;
    public $errMsg = "no access";         //也就是企业号的CorpID
    public $logcallback;
    public $appsecret_cache_key;
    public $_retry = false;
    private $token;    //接收的应用id   CorpID
    private $encodingAesKey;
    private $appid;
    private $appsecret;
    private $access_token;
    private $postxml;
    private $CorpIDxml;
    private $_receive;

    public function __construct($options = [])
    {
        $this->appid               = $options['appid'] ?? '';
        $this->appsecret           = $options['appsecret'] ?? '';
        $this->token               = $options['token'] ?? '';
        $this->encodingAesKey      = $options['encodingAesKey'] ?? '';
        $this->debug               = $options['debug'] ?? false;
        $this->logcallback         = 'trace';
        $this->appsecret_cache_key = 'tencentemail_access_token:' . $this->appid . md5($this->appsecret);
        $this->_retry              = false;
    }

    /**
     * XML编码
     * @param mixed $data 数据
     * @param string $root 根节点名
     * @param string $item 数字索引的子节点名
     * @param string|array $attr 根节点属性
     * @param string $id 数字索引子节点key转换的属性名
     * @param string $encoding 数据编码
     * @return string
     */
    public function xml_encode($data, $root = 'xml', $item = 'item', $attr = '', $id = 'id', $encoding = 'utf-8')
    {
        if (is_array($attr)) {
            $_attr = array();
            foreach ($attr as $key => $value) {
                $_attr[] = "{$key}=\"{$value}\"";
            }
            $attr = implode(' ', $_attr);
        }
        $attr = trim($attr);
        $attr = empty($attr) ? '' : " {$attr}";
        $xml  = "<{$root}{$attr}>";
        $xml  .= self::data_to_xml($data, $item, $id);
        $xml  .= "</{$root}>";
        return $xml;
    }

    /**
     * 数据XML编码
     * @param mixed $data 数据
     * @return string
     */
    public static function data_to_xml($data)
    {
        $xml = '';
        foreach ($data as $key => $val) {
            is_numeric($key) && $key = "item id=\"$key\"";
            $xml .= "<$key>";
            $xml .= (is_array($val) || is_object($val)) ? self::data_to_xml($val) : self::xmlSafeStr($val);
            list($key,) = explode(' ', $key);
            $xml .= "</$key>";
        }
        return $xml;
    }

    public static function xmlSafeStr($str)
    {
        return '<![CDATA[' . preg_replace("/[\\x00-\\x08\\x0b-\\x0c\\x0e-\\x1f]/", '', $str) . ']]>';
    }

    /**
     * 微信验证，包括post来的xml解密
     * @param bool $return 是否返回
     */
    public function valid($return = false)
    {
        $encryptStr = "";
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $postStr = file_get_contents("php://input");
            $array   = (array)simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
            $this->log($postStr);
            if (isset($array['Encrypt'])) {
                $encryptStr      = $array['Encrypt'];
                $this->CorpIDxml = $array['CorpID'] ?? '';
            }
        } else {
            $encryptStr = isset($_GET["echos-tr"]) ? ($_GET["echostr"]) : '';
        }
        if ($encryptStr) {
            //把取到的参数空格转换成+号
            $encryptStr = str_replace(" ", "+", $encryptStr);
            $ret        = $this->checkSignature($encryptStr);
        }
        if (!isset($ret) || !$ret) {
            if (!$return) {
                die('no access');
            } else {
                return false;
            }
        }
        $pc    = new Prpcrypt($this->encodingAesKey);
        $array = $pc->decrypt($encryptStr, $this->appid);
        if (!isset($array[0]) || ($array[0] != 0)) {
            if (!$return) {
                die('解密失败！');
            } else {
                return false;
            }
        }
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $this->postxml = $array[1];
            return ($this->postxml != "");
        } else {
            $echoStr = $array[1];
            if ($return) {
                return $echoStr;
            } else {
                die($echoStr);
            }
        }
        return false;
    }

    protected function log($log)
    {
        if ($this->debug && function_exists($this->logcallback)) {
            if (is_array($log)) $log = print_r($log, true);
            return call_user_func($this->logcallback, $log);
        }
    }

    /**
     * For weixin server validation
     */
    private function checkSignature($str)
    {
        $signature = $_GET["msg_signature"] ?? '';
        $timestamp = $_GET["timestamp"] ?? '';
        $nonce     = $_GET["nonce"] ?? '';
        $tmpArr    = array($str, $this->token, $timestamp, $nonce);// 比普通公众平台多了一个加密的密文
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $shaStr = sha1($tmpStr);
        return $shaStr === $signature;
    }

    /**
     * 获取微信服务器发来的信息
     */
    public function getRev()
    {
        if ($this->_receive) return $this;
        $postStr = $this->postxml;
        $this->log($postStr);
        if (!empty($postStr)) {
            $this->_receive = (array)simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
            if (!isset($this->_receive['CorpID'])) {
                $this->_receive['CorpID'] = $this->CorpIDxml; //当前接收消息的应用id
            }
        }
        return $this;
    }

    /**
     * 获取微信服务器发来的信息
     */
    public function getRevData()
    {
        return $this->_receive;
    }

    /**
     * 获取微信服务器发来的原始加密信息
     */
    public function getRevPostXml()
    {
        return $this->postxml;
    }

    /**
     * 获取接收消息的应用id
     */
    public function getRevCorpID()
    {
        return $this->_receive['CorpID'] ?? false;
    }

    /**
     * 获取消息发送时间
     */
    public function getRevTime()
    {
        return $this->_receive['Time'] ?? false;
    }

    /**
     * 获取签名
     * @param array $arrdata 签名数组
     * @param string $method 签名方法
     * @return boolean|string 签名值
     */
    public function getSignature($arrdata, $method = "sha1")
    {
        if (!function_exists($method)) return false;
        ksort($arrdata);
        $paramstring = "";
        foreach ($arrdata as $key => $value) {
            if (strlen($paramstring) == 0)
                $paramstring .= $key . "=" . $value;
            else
                $paramstring .= "&" . $key . "=" . $value;
        }
        $Sign = $method($paramstring);
        return $Sign;
    }

    /**
     * 生成随机字串
     * @param integer $length 长度，默认为16，最长为32字节
     * @return string
     */
    public function generateNonceStr($length = 16)
    {
        // 密码字符集，可任意添加你需要的字符
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $str;
    }

    public function get_mail_newcount($userid, $data)
    {
        if (!$this->access_token && !$this->getAccessToken()) return false;
        $result = $this->http_post(self::API_URL_PREFIX . self::MAIL_NEW_COUNT . 'access_token=' . $this->access_token . '&userid=' . $userid, self::json_encode($data));
        $json   = json_decode($result, true);
        if ($this->_parseResult($json) === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $json;
    }

    /**
     * 通用auth验证方法
     * @param string $appid
     * @param string $appsecret
     * @param string $token 手动指定access_token，非必要情况不建议用
     * @return string|bool
     * @throws Exception
     */
    public function getAccessToken($appid = '', $appsecret = '', $token = '')
    {
        if (!$appid || !$appsecret) {
            $appid     = $this->appid;
            $appsecret = $this->appsecret;
        }
        if ($token) { //手动指定token，优先使用
            $this->access_token = $token;
            return $this->access_token;
        }
        $authname      = $this->appsecret_cache_key;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($authname, 30 * 1000);

        //获得分布式锁之后开始获取access_token,先判断是否存在缓存
        if ($rs = $this->getCache($authname)) {
            $this->access_token = $rs;
            $lock_instance->unlock();
            return $rs;
        }
        //不存在缓存则调用api获取,并缓存起来
        $result = $this->http_get(self::API_URL_PREFIX . self::TOKEN_GET_URL . 'corpid=' . $appid . '&corpsecret=' . $appsecret);
        //无论是否成功,完成了获取动作都释放锁
        $lock_instance->unlock();
        if ($result) {
            $json = json_decode($result, true);
            if (!$json || $json['errcode'] !== 0) {
                $this->errCode = $json['errcode'];
                $this->errMsg  = $json['errmsg'];
                return false;
            }
            $this->access_token = $json['access_token'];
            $expire             = $json['expires_in'] ? intval($json['expires_in']) - 100 : 7000;
            $this->setCache($authname, $this->access_token, $expire);
            return $this->access_token;
        }
        return false;
    }

    /**
     * 获取缓存，按需重载
     * @param string $cache_name
     * @return mixed
     */
    protected function getCache($cache_name)
    {
        return cache($cache_name);
    }

    /**
     * GET 请求
     * @param string $url
     */
    private function http_get($url)
    {
        return curl()->get($url)->get_body();
        $oCurl = curl_init();
        if (stripos($url, "https://") !== FALSE) {
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
        }
        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1);
        $sContent = curl_exec($oCurl);
        $aStatus  = curl_getinfo($oCurl);
        curl_close($oCurl);
        if (intval($aStatus["http_code"]) == 200) {
            return $sContent;
        } else {
            return false;
        }
    }

    /**
     * 设置缓存，按需重载
     * @param string $cache_name
     * @param mixed $value
     * @param int $expired
     * @return boolean
     */
    protected function setCache($cache_name, $value, $expired)
    {
        return cache($cache_name, $value, $expired);
    }

    /**
     * POST 请求
     * @param string $url
     * @param array $param
     * @param boolean $post_file 是否文件上传
     * @return string content
     */
    private function http_post($url, $param, $post_file = false)
    {
        return curl()->post($url, $param)->get_body();
        $oCurl = curl_init();
        if (stripos($url, "https://") !== FALSE) {
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
        }
        if (is_string($param) || $post_file) {
            $strPOST = $param;
        } else {
            $aPOST = array();
            foreach ($param as $key => $val) {
                $aPOST[] = $key . "=" . urlencode($val);
            }
            $strPOST = join("&", $aPOST);
        }
        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($oCurl, CURLOPT_POST, true);
        curl_setopt($oCurl, CURLOPT_POSTFIELDS, $strPOST);
        $sContent = curl_exec($oCurl);
        $aStatus  = curl_getinfo($oCurl);
        curl_close($oCurl);
        if (intval($aStatus["http_code"]) == 200) {
            return $sContent;
        } else {
            return false;
        }
    }

    /**
     * 微信api不支持中文转义的json结构
     * @param array $arr
     */
    static function json_encode($arr)
    {
        $parts   = array();
        $is_list = false;
        //Find out if the given array is a numerical array
        $keys       = array_keys($arr);
        $max_length = count($arr) - 1;
        if (($keys [0] === 0) && ($keys [$max_length] === $max_length)) { //See if the first key is 0 and last key is length - 1
            $is_list = true;
            for ($i = 0; $i < count($keys); $i++) { //See if each key correspondes to its position
                if ($i != $keys [$i]) { //A key fails at position check.
                    $is_list = false; //It is an associative array.
                    break;
                }
            }
        }
        foreach ($arr as $key => $value) {
            if (is_array($value)) { //Custom handling for arrays
                if ($is_list)
                    $parts [] = self::json_encode($value); /* :RECURSION: */
                else
                    $parts [] = '"' . $key . '":' . self::json_encode($value); /* :RECURSION: */
            } else {
                $str = '';
                if (!$is_list)
                    $str = '"' . $key . '":';
                //Custom handling for multiple data types
                if (!is_string($value) && is_numeric($value) && $value < 2000000000)
                    $str .= $value; //Numbers
                elseif ($value === false)
                    $str .= 'false'; //The booleans
                elseif ($value === true)
                    $str .= 'true';
                else
                    $str .= '"' . addslashes($value) . '"'; //All other things
                // :TODO: Is there any more datatype we should be in the lookout for? (Object?)
                $parts [] = $str;
            }
        }
        $json = implode(',', $parts);
        if ($is_list)
            return '[' . $json . ']'; //Return numerical JSON
        return '{' . $json . '}'; //Return associative JSON
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errCode = 'result error';
            $this->errMsg  = '解析返回结果失败';
            return false;
        }
        if (!isset($result['errcode']) || $result['errcode'] !== 0) {
            $this->errCode = $result['errcode'];
            $this->errMsg  = $result['errmsg'];
            return false;
        }
        return $result;
    }

    /**
     * 接口失败重试
     * @param $method   SDK方法名称
     * @param array $arguments SDK方法参数
     * @return bool|mixed
     */
    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry && in_array($this->errCode, array('40014', '40001', '41001', '42001'))) {
            ($this->_retry = true) && $this->resetAuth();
            $this->errCode = 40001;
            $this->errMsg  = 'no access';
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    /**
     * 删除验证数据
     * @param string $appid
     * @return  bool
     */
    public function resetAuth($appid = '', $appsecret = '')
    {
        if (!$appid) $appid = $this->appid;
        if (!$appsecret) $appsecret = $this->appsecret;
        $this->access_token = '';
        $this->removeCache($this->appsecret_cache_key);
        return true;
    }

    /**
     * 清除缓存，按需重载
     * @param string $cache_name
     * @return boolean
     */
    protected function removeCache($cache_name)
    {
        return cache($cache_name, null);
    }

    public function get_log_mail($data)
    {
        if (!$this->access_token && !$this->getAccessToken()) return false;
        $result = $this->http_post(self::API_URL_PREFIX . self::LOG_MAIL . 'access_token=' . $this->access_token, self::json_encode($data));
        $json   = json_decode($result, true);
        if ($this->_parseResult($json) === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $json;
    }

    /**
     * 过滤文字回复\r\n换行符
     * @param string $text
     * @return string|mixed
     */
    private function _auto_text_filter($text)
    {
        if (!$this->_text_filter) return $text;
        return str_replace("\r\n", "\n", $text);
    }
}


/**
 * PKCS7Encoder class
 *
 * 提供基于PKCS7算法的加解密接口.
 */
class PKCS7Encoder
{
    public static $block_size = 32;

    /**
     * 对需要加密的明文进行填充补位
     * @param string $text 需要进行填充补位操作的明文
     * @return string 补齐明文字符串
     */
    function encode($text)
    {
        $block_size  = PKCS7Encoder::$block_size;
        $text_length = strlen($text);
        //计算需要填充的位数
        $amount_to_pad = PKCS7Encoder::$block_size - ($text_length % PKCS7Encoder::$block_size);
        if ($amount_to_pad == 0) {
            $amount_to_pad = PKCS7Encoder::$block_size;
        }
        //获得补位所用的字符
        $pad_chr = chr($amount_to_pad);
        $tmp     = "";
        for ($index = 0; $index < $amount_to_pad; $index++) {
            $tmp .= $pad_chr;
        }
        return $text . $tmp;
    }

    /**
     * 对解密后的明文进行补位删除
     * @param string $text decrypted解密后的明文
     * @return 删除填充补位后的明文
     */
    function decode($text)
    {

        $pad = ord(substr($text, -1));
        if ($pad < 1 || $pad > PKCS7Encoder::$block_size) {
            $pad = 0;
        }
        return substr($text, 0, (strlen($text) - $pad));
    }

}

/**
 * Prpcrypt class
 *
 * 提供接收和推送给公众平台消息的加解密接口.
 */
class Prpcrypt
{
    public $key;

    function __construct($k)
    {
        $this->key = base64_decode($k . "=");
    }

    /**
     * 兼容老版本php构造函数，不能在 __construct() 方法前边，否则报错
     */
    function Prpcrypt($k)
    {
        $this->key = base64_decode($k . "=");
    }

    /**
     * 对明文进行加密
     * @param string $text 需要加密的明文
     * @return string 加密后的密文
     */
    public function encrypt($text, $appid)
    {

        try {
            $random      = $this->getRandomStr();
            $text        = $random . pack("N", strlen($text)) . $text . $appid;
            $iv          = substr($this->key, 0, 16);
            $pkc_encoder = new PKCS7Encoder;
            $text        = $pkc_encoder->encode($text);
            $encrypted   = openssl_encrypt($text, 'AES-256-CBC', $this->key, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING, $iv);

            // php7.2已废弃mcrypt_module_open方法
            //           //获得16位随机字符串，填充到明文之前
            //           $random = $this->getRandomStr();//"aaaabbbbccccdddd";
            //           $text   = $random . pack("N", strlen($text)) . $text . $appid;
            //           // 网络字节序
            //           $size   = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
            //           $module = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', MCRYPT_MODE_CBC, '');
            //           $iv     = substr($this->key, 0, 16);
            //           //使用自定义的填充方式对明文进行补位填充
            //           $pkc_encoder = new PKCS7Encoder;
            //           $text        = $pkc_encoder->encode($text);
            //           mcrypt_generic_init($module, $this->key, $iv);
            //           //加密
            //           $encrypted = mcrypt_generic($module, $text);
            //           mcrypt_generic_deinit($module);
            //           mcrypt_module_close($module);

            //			print(base64_encode($encrypted));
            //使用BASE64对加密后的字符串进行编码
            return array(ErrorCode::$OK, base64_encode($encrypted));
        } catch (Exception $e) {
            //print $e;
            return array(ErrorCode::$EncryptAESError, null);
        }
    }

    /**
     * 随机生成16位字符串
     * @return string 生成的字符串
     */
    function getRandomStr()
    {

        $str     = "";
        $str_pol = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
        $max     = strlen($str_pol) - 1;
        for ($i = 0; $i < 16; $i++) {
            $str .= $str_pol[mt_rand(0, $max)];
        }
        return $str;
    }

    /**
     * 对密文进行解密
     * @param string $encrypted 需要解密的密文
     * @return string 解密得到的明文
     */
    public function decrypt($encrypted, $appid)
    {
        try {
            $ciphertext_dec = base64_decode($encrypted);
            $iv             = substr($this->key, 0, 16);
            $decrypted      = openssl_decrypt($ciphertext_dec, 'AES-256-CBC', $this->key, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING, $iv);
            // php7.2已废弃mcrypt_module_open方法
            //           //使用BASE64对需要解密的字符串进行解码
            //           $ciphertext_dec = base64_decode($encrypted);
            //           $module         = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', MCRYPT_MODE_CBC, '');
            //           $iv             = substr($this->key, 0, 16);
            //           mcrypt_generic_init($module, $this->key, $iv);
            //           //解密
            //           $decrypted = mdecrypt_generic($module, $ciphertext_dec);
            //           mcrypt_generic_deinit($module);
            //           mcrypt_module_close($module);
        } catch (Exception $e) {
            return array(ErrorCode::$DecryptAESError, null);
        }


        try {
            //去除补位字符
            $pkc_encoder = new PKCS7Encoder;
            $result      = $pkc_encoder->decode($decrypted);
            //去除16位随机字符串,网络字节序和AppId
            if (strlen($result) < 16)
                return "";
            $content     = substr($result, 16, strlen($result));
            $len_list    = unpack("N", substr($content, 0, 4));
            $xml_len     = $len_list[1];
            $xml_content = substr($content, 4, $xml_len);
            $from_appid  = substr($content, $xml_len + 4);
        } catch (Exception $e) {
            //print $e;
            return array(ErrorCode::$IllegalBuffer, null);
        }
        if ($from_appid != $appid)
            return array(ErrorCode::$ValidateAppidError, null);
        return array(0, $xml_content);

    }

}

/**
 * error code
 * 仅用作类内部使用，不用于官方API接口的errCode码
 */
class ErrorCode
{
    public static $OK = 0;
    public static $ValidateSignatureError = 40001;
    public static $ParseXmlError = 40002;
    public static $ComputeSignatureError = 40003;
    public static $IllegalAesKey = 40004;
    public static $ValidateAppidError = 40005;
    public static $EncryptAESError = 40006;
    public static $DecryptAESError = 40007;
    public static $IllegalBuffer = 40008;
    public static $EncodeBase64Error = 40009;
    public static $DecodeBase64Error = 40010;
    public static $GenReturnXmlError = 40011;
    public static $errCode = array(
        '0'     => '无问题',
        '40001' => '签名验证错误',
        '40002' => 'xml解析失败',
        '40003' => 'sha加密生成签名失败',
        '40004' => 'encodingAesKey 非法',
        '40005' => 'appid 校验错误',
        '40006' => 'aes 加密失败',
        '40007' => 'aes 解密失败',
        '40008' => '解密后得到的buffer非法',
        '40009' => 'base64加密失败',
        '40010' => 'base64解密失败',
        '40011' => '生成xml失败',
    );

    public static function getErrText($err)
    {
        return self::$errCode[$err] ?? false;
    }
}
