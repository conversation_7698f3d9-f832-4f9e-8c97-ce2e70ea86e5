<?php

namespace OpenApi;

use Symfony\Component\DomCrawler\Crawler;

class Yikayi
{
    /** 接口基础地址 */
    const BASE_URL = 'http://www.1card1.cn';
    /** 登录账号 */
    public $username;
    /** 登录密码 */
    public $password;
    /** 错误码 */
    public $errcode;
    /** 错误信息 */
    public $errmsg;
    /** 是否已重试 */
    public $_retry = false;
    /** 登录缓存 */
    protected $cookie;

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $options        = [
            'username' => 'xyf',
            'password' => '111aaa...',
        ];
        $this->username = $options['username'];
        $this->password = $options['password'];
    }

    protected function post($url, $data = [])
    {
        return $this->http_request($url, $data, 'POST');
    }

    protected function get($url, $data = [])
    {
        return $this->http_request($url, $data, 'GET');
    }

    protected function http_request($url, $data, $method)
    {
        return curl()->set_allow_redirects(false)->form_params()->ignore_log(true)->set_cookies($this->get_login_cookie())->$method($url, $data)->get_body();
    }

    //获得支撑后台登陆cookies
    public function get_login_cookie($username = '', $password = '')
    {
        if (!$username || !$password) {
            $username = $this->username;
            $password = $this->password;
        }
        $key = 'yky:login_cookie:' . $username;
        if ($cookie = cache($key)) {
            return $this->cookie = $cookie;
        }
        $url            = self::BASE_URL . '/management/login.aspx';
        $response       = curl()->get($url);
        $cookies        = $response->get_cookies();
        $body           = $response->get_body();
        $name_value_arr = $this->get_names_and_values($body);
        $data_arr       = [
            "TextName"        => $username,
            "TextPass"        => $password,
            'ScriptManager1'  => 'UpdatePanel1|btlogin',
            '__EVENTTARGET'   => '',
            '__EVENTARGUMENT' => '',
            'btlogin.x'       => 1,
            'btlogin.y'       => 1,
            '__ASYNCPOST'     => true,
        ];
        $data_arr       = array_merge($name_value_arr, $data_arr);
        $response       = curl()->set_allow_redirects(false)->form_params()->set_cookies($cookies)->post($url, $data_arr);
        $body           = $response->get_body();
        if (!strstr($body, 'main.aspx')) {
            $this->errmsg = '模拟登录失败';
            return false;
        }
        $cookies .= ';' . $response->get_cookies();
        cache($key, $cookies, 600);
        $this->cookie = $cookies;
        return $cookies;
    }

    public function get_names_and_values($body)
    {
        preg_match_all("/name=\"([^>]*)/i", $body, $arr);
        $val_arr = [];
        foreach ($arr[0] as $key => $val) {
            preg_match("/name=\"([^\"]*)/i", $val, $names);
            preg_match("/value=\"([^\"]*)/i", $val, $values);
            if (isset($names[1])) {
                $input_value        = isset($values[1]) ? $values[1] : '';
                $val_arr[$names[1]] = str_replace(' ', '+', $input_value);
            }
        }
        return $val_arr;
    }

    public function get_business_config($sid)
    {
        $url = self::BASE_URL . '/management/Business/BusinessSet.aspx';
        return $this->get($url, ['SID' => $sid]);
    }

    public function get_agent_survey_list_html()
    {
        $url = self::BASE_URL . '/management/DeviceApply/AgentSurveyList.aspx';
        return $this->get($url);
    }

    public function get_agent_survey_list()
    {
        $html    = $this->get_agent_survey_list_html();
        $crawler = new Crawler($html);
        $result  = $crawler->filterXPath('//table[@class="table-box" and  @id="gvAgentSurveyList"]/*')->each(function (Crawler $node, $i) {
            return $node->filterXPath('//tr[@class="alterRow" or @class="row"]/*')->each(function (Crawler $node, $i) {
                return $node->text();
            });
        });
        return array_filter($result);

    }

    public function get_payscore_bind_note_html()
    {
        $url = self::BASE_URL . '/management/DeviceApply/PayscoreBindNoteList.aspx';
        return $this->get($url);
    }

    public function get_payscore_bind_note_list()
    {
        $html    = $this->get_payscore_bind_note_html();
        $crawler = new Crawler($html);
        $result  = $crawler->filterXPath('//table[@class="table-box" and  @id="gvPayscoreBindList"]/*')->each(function (Crawler $node, $i) {
            return $node->filterXPath('//tr[@class="alterRow" or @class="row"]/*')->each(function (Crawler $node, $i) {
                return $node->text();
            });
        });
        return array_filter($result);
    }

    public function get_business_list()
    {
        $url      = self::BASE_URL . '/management/Business/BusinessList.aspx';
        $response = $this->get($url);
        //把内容拆分成多个tr
        $regex = "/<tr[\s\S]*?>([\s\S]*?)<\/tr>/i";
        preg_match_all($regex, $response, $arr);
        $list = [];
        if (empty($arr[1])) {
            return false;
        }
        foreach ($arr[1] as $key => $val) {
            //把每个tr拆分成多个td
            $regex = "/<td[\s\S]*?>([\s\S]*?)<\/td>/i";
            preg_match_all($regex, $val, $array);
            if (!isset($array[1])) {
                break;
            }
            //数组个数15个的说明是商家列表的行,一页有20个
            if (count($array[1]) == 15) {
                $business = [
                    'account'          => $array[1][1],
                    'business_name'    => tools()::search_str("<span>", "</span>", $array[1][2]),
                    'username'         => $array[1][3],
                    'tel'              => $array[1][4],
                    'qq'               => $array[1][5],
                    'register_time '   => $array[1][6],
                    'industry'         => $array[1][8],
                    'version'          => $array[1][9],
                    'soft_type'        => $array[1][10],
                    'expiration_date'  => $array[1][11],
                    'service_username' => $array[1][12],
                    'available_sms'    => intval($array[1][13])
                ];
                foreach ($business as $key => $val) {
                    $business[$key] = str_replace('&nbsp;', '', $val);
                }
                $list[] = $business;
            }
        }
        return $list;
    }

    protected function clear_cookies($username = '')
    {
        if (!$username) {
            $username = $this->username;
        }
        return cache('yky:login_cookie:' . $username, NULL);
    }
}