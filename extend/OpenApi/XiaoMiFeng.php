<?php

namespace OpenApi;

class XiaoMiFeng
{
    const  API_URL = 'https://service.jcthy.com/platformapi/chainup/open';

    public $errcode;
    public $errmsg;
    public $_retry = false;//接口地址
    private $appid = 'jcthy_3928';
    private $secret = 'DDRUKRFA35G4AW8AZV1G';

    /**
     *  constructor.
     * @param array $config
     */
    public function __construct($config = [])
    {

    }

    /**
     * @param array $data
     * @return string
     */
    protected function get_sign(array $data)
    {
        ksort($data);
        $sign_str = '';
        foreach ($data as $key => $val) {
            $sign_str .= $key . $val;
        }
        $sign_str .= $this->secret;
        $sign     = md5($sign_str);
        return strtolower($sign);
    }

    protected function request(string $url, array $data = [])
    {
        $url            = self::API_URL . $url;
        $data['appKey'] = $this->appid;
        $data['sign']   = $this->get_sign($data);
        $result         = curl()->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    //使用code换取token、openId接口
    public function get_token($code)
    {
        $url = '/auth/token';
        return $this->request($url, ['code' => $code]);
    }

    public function refreshToken(array $data)
    {
        $url = '/auth/refreshToken';
        return $this->request($url, $data);
    }

    //赠币接口
    public function refundOrder(array $data)
    {
        $url = '/opay/refundOrder';
        return $this->request($url, $data);
    }

    //用户账户余额（全币种）
    public function accountBalance(array $data)
    {
        $url = '/user/accountBalance';
        return $this->request($url, $data);
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] !== '0') {
            $this->errcode = $result['code'];
            $this->errmsg  = $result['msg'];
            return false;
        }
        return (isset($result['data']) && !empty($result['data'])) ? $result['data'] : $result;
    }


    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }
}