<?php

namespace OpenApi;

use Exception;

class ChangAnCar
{
    public $is_retry = false;
    /**
     * 当前请求方法参数
     * @var array
     */
    protected $currentMethod = [];
    public $unionid = '';
    public $openid = '';

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct($config = array())
    {
        $this->init($config);
    }

    /**
     * @param array $config
     * @throws Exception
     */
    protected function init(array $config)
    {
        $default_config = [
            'unionid' => 'o7sY81F3-5Lb3PSCMSDpHBJbjykY',
            'openid'  => 'ol7KdjvN1XrUhuCq8TLu0VPDKdUg'
        ];
        $config         = array_merge($default_config, $config);
        $this->unionid  = $config['unionid'];
        $this->openid   = $config['openid'];
        $this->is_retry = false;
    }

    /**
     * 获取缓存key
     * @return string
     */
    public function get_cache_key()
    {
        return 'changan_car_token:' . $this->unionid . ':' . $this->openid;
    }

    public function get_token()
    {
        $cache_key = 'changan_car_token:' . $this->unionid . $this->openid;
        $token     = cache($cache_key);
        if (empty($token)) {
            $url       = 'https://service.changan.com.cn/changan-trade-application/api/v1/oauth/third/flag/login/token';
            $result    = $this->get($url, [], true);
            $token     = $result['accessToken'];
            $expiresIn = $result['expiresIn'];

            //需要模拟请求下用户信息接口,否则token依旧不可用
            $url  = 'http://scrm.changan.com.cn/scrm-app-web/user/info';
            $data = ['token' => $token];
            $this->get($url, $data, true);

            cache($cache_key, $token, $expiresIn - 600);
        }
        return $token;
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return $this
     */
    protected function registerApi(string $method, array $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 接口重试
     * @return bool|array
     */
    protected function retry()
    {
        $this->is_retry = true;
        cache($this->get_cache_key(), null);//清空缓存
        return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
    }

    /**
     * 解析返回的结果
     * @param string|array $result
     * @return string|array
     * @throws Exception
     */
    protected function _parseResult($result)
    {
        if (!is_array($result)) {
            throw new Exception('解析返回结果失败!');
        }
        if ((isset($result['resultCode']) && $result['resultCode'] !== 0) || (isset($result['result']) && $result['result'] !== 0)) {
            $code    = $result['resultCode'] ?? ($result['code'] ?? -1);
            $message = $result['resultMsg'] ?? ($result['msg'] ?? '长安订单查询出错');
            $msg     = $message . '(' . $code . ')';
            if (isset($this->currentMethod['method']) && empty($this->is_retry)) {
                return $this->retry();
            }
            throw new Exception($msg, $code);
        }
        return $result['data'] ?? $result;
    }

    /**
     * GET请求
     * @access protected
     * @param string $url
     * @param array $data
     * @param bool $ignore_log
     * @return bool|array
     * @throws Exception
     */
    protected function get(string $url, array $data = [], $ignore_log = false)
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $header = ['unionid' => $this->unionid, 'openid' => $this->openid];
        $result = curl()->set_header($header)->ignore_log($ignore_log)->get($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * POST请求
     * @param string $url
     * @param array $data
     * @param bool $ignore_log
     * @return bool|array
     * @throws Exception
     */
    protected function post(string $url, array $data = [], $ignore_log = false)
    {
        $this->registerApi(__FUNCTION__, func_get_args());
        $header = ['unionid' => $this->unionid, 'openid' => $this->openid];
        $result = curl()->set_header($header)->ignore_log($ignore_log)->form_params()->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }


    /**
     * 获取签名信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function generateSignature()
    {
        $url  = 'https://scrm.changan.com.cn/scrm-app-web/tmjc/generateSignature';
        $data = ['sys' => 'cafan_app', 'token' => $this->get_token()];
        return $this->post($url, $data, true);
    }

    /**
     * 获取订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function getOrderList()
    {
        $result   = $this->generateSignature();
        $data     = [
            'sys'            => 'cafan_app',
            'sign'           => $result['sign'],
            'customerMobile' => $result['customerMobile'],
            'echo'           => $result['echo'],
            'time'           => $result['time'],
        ];
        $signInfo = urlencode(json_encode($data));
        $url      = 'http://cvsses.changan.com.cn/tmjc/orderList?customerMobile=' . $result['customerMobile'] . '&isHistory=false&keyWord=&searchType=customerMobile&pageSize=2&signInfo=' . $signInfo;
        $url      = 'http://47.115.7.168/curl.php?url=' . urlencode($url);
        return $this->get($url);
    }

    /**
     * 获取个人中心登陆地址
     * @access public
     * @return string
     */
    public function get_user_center_url()
    {
        return 'http://scrm.changan.com.cn/scrm-wechat/wechat-server/main/index.html#my/zx-index.html?token=' . $this->get_token();
    }

    /**
     * 获取订单列表地址
     * @access public
     * @return string
     */
    public function get_order_list_url()
    {
        $result = $this->generateSignature();
        return 'http://cvsses.changan.com.cn/tmjc/index.html#/?sys=ca_wechat&customerMobile=' . $result['customerMobile'] . '&time=' . $result['time'] . '&echo=' . $result['echo'] . '&sign=' . $result['sign'];
    }
}