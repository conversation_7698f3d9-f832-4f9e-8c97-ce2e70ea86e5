<?php

namespace OpenApi;


use think\Exception;

class AliyunSms
{
    /** 登录账号 */
    /** 登录密码 */
    const  API_URL = "http://dysmsapi.aliyuncs.com/?";
    /** status */
    public $errcode;
    /** message */
    public $errmsg;
    public $_retry = false;//接口地址
    private $format = 'JSON';
    private $common_params = [];//公共参数,系统参数
    private $private_params = [];//请求参数,业务参数
    private $phone = '';//用户手机号
    private $template_id = '';//短信模板id
    private $access_key_id = 'LTAImDF6NQA8ZG2z';
    private $access_key_secret = 'H8QPA6Wum6ZB7DbIiERl1ONFsR4T3n';
    private $action = 'SendSms';
    private $signature_method = 'HMAC-SHA1';
    private $request_method = 'GET';
    private $sign_name = '一卡';
    private $template_param = "";
    private $out_id = "";

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $this->common_params  = $this->staticCommonParams();
        $this->private_params = $this->setPrivateParams();
    }

    /**
     * 构建请求系统参数
     * @return [type] [description]
     */
    private function staticCommonParams()
    {
        return [
            'AccessKeyId'      => $this->access_key_id,
            'Timestamp'        => $this->getTimestamp(),
            'Format'           => $this->format,
            'SignatureMethod'  => $this->signature_method,
            'SignatureVersion' => '1.0',
            'SignatureNonce'   => uniqid(),
        ];
    }

    private function getTimestamp()
    {
        date_default_timezone_set("GMT"); //设置时区
        $time = date("Y-m-d\TH:i:s\Z");
        date_default_timezone_set("PRC"); //设置回去时区
        // return '2017-07-12T02:42:19Z';
        return $time;
    }

    /**
     * 构建请求业务参数
     */
    private function setPrivateParams()
    {
        return [
            'Action'   => $this->action,
            'Version'  => '2017-05-25',
            'RegionId' => 'cn-hangzhou',
        ];
    }

    public function querySendDetails($phone, $date = '', $biz_id = '')
    {
        try {
            $this->setActionName('QuerySendDetails');
            $this->setQueryPhone($phone);
            if ($biz_id) {
                $this->setBizId($biz_id);
            }
            $this->setSendDate($date);
            $this->setCurrentPage();
            $this->setPageSize();
            $result = $this->execute();
            if ($result === false) {
                return false;
            }
            if ($result['TotalCount'] == 0) {
                $this->errmsg = '发送记录为空';
                return false;
            }
            return $result['SmsSendDetailDTOs']['SmsSendDetailDTO'];
        } catch (Exception $e) {
            $this->errmsg = $e->getMessage();
            return false;
        }
    }

    public function setActionName($name)
    {
        $this->action                   = $name;
        $this->private_params["Action"] = $name;
    }

    public function setQueryPhone($phone)
    {
        if ($this->is_mobile($phone)) {
            $this->phone                         = $phone;
            $this->private_params["PhoneNumber"] = $phone;
            return $this;
        } else {
            throw new Exception('手机号格式不正确');
        }
    }

    private function is_mobile($phone)
    {
        return preg_match("/^1\d{10}$/", $phone);
    }

    public function setBizId($bizId)
    {
        $this->private_params["BizId"] = $bizId;
    }

    public function setSendDate($sendDate = '')
    {
        if (!$sendDate) {
            $sendDate = date('Ymd'); //默认查询当天记录
        }
        $this->private_params["SendDate"] = $sendDate;
    }

    public function setCurrentPage($currentPage = 1)
    {
        $this->private_params["CurrentPage"] = $currentPage;
    }

    //签名算法

    public function setPageSize($pageSize = 10)
    {
        $this->private_params["PageSize"] = $pageSize;
    }

    private function execute()
    {
        $request_params = $this->get_request_params();
        ksort($request_params);
        $params     = array_merge(['Signature' => $this->getSign($request_params)], $request_params);
        $http_query = http_build_query($params);
        $url        = self::API_URL . $http_query;
        $result     = curl()->get($url)->get_body();
        return $this->_parseResult($result);
    }

    private function get_request_params()
    {
        return array_merge($this->common_params, $this->private_params);
    }

    //生产验证码

    public function getSign($params)
    {
        ksort($params);
        foreach ($params as $k => &$v) {
            if (!empty($v)) {
                urlencode($k);
                urlencode($v);
            } else {
                unset($params[$k]);
            }
        }
        $string_query = http_build_query($params);
        $string_query = $this->encode($string_query);
        $str_sms      = $this->request_method . '&' . urlencode('/') . '&' . $this->encode(urlencode($string_query));
        $sign         = base64_encode(hash_hmac('sha1', $str_sms, $this->access_key_secret . '&', true));
        return $sign;
    }

    /**
     * 阿里云通信，短信接口，编码规则
     * @param  [type] $str [description]
     * @return [type]      [description]
     */
    private function encode($str)
    {
        $str = str_replace('+', '%20', $str);
        $str = str_replace('*', '%2A', $str);
        $str = str_replace('%7E', '~', $str);
        return $str;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['Code']) && $result['Code'] !== 'OK') {
            $this->errcode = isset($result['Code']) ? $result['Code'] : -1;
            $this->errmsg  = isset($result['Message']) ? $result['Message'] : '未知错误';
            return false;
        }
        return $result;
    }

    public function sendSms($mobile, $data, $template_id = '', $out_id = '')
    {
        try {
            if (tools()::get_array_value_mb_strlen($data) > 45) {
                $this->errmsg = '发送失败,请将字数控制在45个字以内';
                return false;
            }
            $this->setPhone($mobile);
            $this->setSignName($this->sign_name);
            if ($template_id) {
                $this->setTemplateId($template_id);
            }
            $this->setTemplateParam($data);
            $out_id = $out_id ?: create_guid();
            $this->setOutId($out_id);
            return $this->execute();
        } catch (Exception $e) {
            $this->errmsg = $e->getMessage();
            return false;
        }
    }

    /**
     *接收验证码的手机号码
     * @param [type] $phone [手机号码]
     */
    public function setPhone($phone)
    {
        if ($this->is_mobile($phone)) {
            $this->phone                          = $phone;
            $this->private_params["PhoneNumbers"] = $phone;
            return $this;
        } else {
            throw new Exception('手机号格式不正确');
        }
    }

    public function setSignName($sign_name)
    {
        $this->sign_name                  = $sign_name;
        $this->private_params["SignName"] = $sign_name;
    }

    /**
     * 设置短信模板id
     * @param string $template_id [SMS_104410009]
     */
    public function setTemplateId($template_id)
    {
        $this->template_id                    = $template_id;
        $this->private_params["TemplateCode"] = $template_id;
    }

    public function setTemplateParam(array $array)
    {
        foreach ($array as $key => $val) {
            if (is_int($val)) {
                //如果val是数字则自动转换成字符串
                $array[$key] = strval($val);
            }
        }
        $this->template_param                  = json_encode($array, JSON_UNESCAPED_UNICODE);
        $this->private_params["TemplateParam"] = $this->template_param;

    }

    public function setOutId($out_id)
    {
        $this->out_id                  = $out_id;
        $this->private_params["OutId"] = $out_id;
    }

    /**
     * 获取详细错误信息
     *
     * @param unknown $status
     */
    public function getErrorMessage($status)
    {

        $message = array(
            'InvalidDayuStatus.Malformed'          => '账户短信开通状态不正确',
            'InvalidSignName.Malformed'            => '短信签名不正确或签名状态不正确',
            'InvalidTemplateCode.MalFormed'        => '短信模板Code不正确或者模板状态不正确',
            'InvalidRecNum.Malformed'              => '目标手机号不正确，单次发送数量不能超过100',
            'InvalidParamString.MalFormed'         => '短信模板中变量不是json格式',
            'InvalidParamStringTemplate.Malformed' => '短信模板中变量与模板内容不匹配',
            'InvalidSendSms'                       => '触发业务流控',
            'InvalidDayu.Malformed'                => '变量不能是url，可以将变量固化在模板中'
        );
        if (isset ($message[$status])) {
            return $message[$status];
        }
        return $status;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    private function random($length, $numeric = true)
    {
        // PHP_VERSION < '4.2.0' && mt_srand((double)microtime() * 1000000);
        //$numberic=0表示发送字母加数字的验证码否则发送纯数字验证码，$length表示验证码的长度
        if ($numeric) {
            $hash = sprintf('%0' . $length . 'd', mt_rand(0, pow(10, $length) - 1));
        } else {
            $hash  = '';
            $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjkmnpqrstuvwxyz';
            $max   = strlen($chars) - 1;
            for ($i = 0; $i < $length; $i++) {
                $hash .= $chars[mt_rand(0, $max)];
            }
        }
        return $hash;
    }
}