<?php

namespace OpenApi;


use Exception;

class LianFuTongQuick
{
    /** 接口基础地址 */
    const API_URL = 'http://api.liantuofu.com/open';
    public $service;
    public $merchant_no;
    public $status;
    public $errmsg;
    public $_retry = false;
    protected $partner_key;

    /**
     * WechatPay constructor.
     * @param array $config
     */
    public function __construct($config = array())
    {
        $default_config = [
            'merchant_no' => 'EW_N9361857321',
            'partner_key' => '44be516adffd5c1e4eee95e4e5978d62',
        ];
        //       $default_config = [
        //           'merchant_no' => 'EW_N1087491352',
        //           'partner_key' => 'f6465866a929c81aed22d4d0efda3c6e',
        //       ];
        $default_config = [
            'merchant_no' => 'EW_N2371892437',
            'partner_key' => '44be516adffd5c1e4eee95e4e5978d62',
        ];


        $config            = array_merge($default_config, $config);
        $this->merchant_no = $config['merchant_no'];
        $this->partner_key = $config['partner_key'];
        $this->_retry      = false;
    }

    /**
     * 生成签名
     * $paras 请求参数字符串
     * $key 密钥
     * return 生成的签名
     */

    function test_FrontQuery()
    {
        $this->service        = 'front.query';
        $body["out_trade_no"] = "EW_N8366110628_0001aaa23111";//商户订单号
        $body["trade_no"]     = "00060011806081011119020239178664";//交易流水号,支付请求返回结果参数
        $resp                 = $this->request($body); //发送请求
        dump($resp);
        return;
    }

    /**
     * 请求
     * @param array $data
     * @return bool|array
     * @throws Exception
     */
    function request($data = [])
    {
        if (empty($this->service)) {
            throw new Exception('api 地址不能为空');
        }
        $data["appId"]  = $this->merchant_no;//合作方标识
        $data["random"] = tools()::create_noncestr();//随机数
        $data["sign"]   = $this->createSign($data, $this->partner_key);
        $result         = curl()->post(self::API_URL . $this->service, $data)->get_body();
        return $this->_parseResult($result);
    }

    //------------------------------------------------------------
    // 订单查询API
    //------------------------------------------------------------

    /**
     * 创建签名
     * @param array $data
     * @param string $key
     * @return bool|array
     * @throws Exception
     */

    function createSign($data, $key)
    {
        ksort($data);
        $string = md5($this->getSignContent($data) . "&key=" . $key);
        return strtolower($string);
    }
    //------------------------------------------------------------
    // 扫码--公众号支付
    //------------------------------------------------------------

    /**
     * 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @param array $data 需要拼接的数组
     * @return string 拼接完成以后的字符串
     */
    function getSignContent($data)
    {
        $str = "";
        foreach ($data as $key => $val) {
            if ($val != null && $val != "" && $key != "key" && $key != "sign_type") {
                $str .= $key . "=" . $val . "&";
            }
        }
        return rtrim($str, '&');
    }
    //------------------------------------------------------------
    //支付账单查询API
    //------------------------------------------------------------

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            return $result;
        }
        $result = json_decode($result, true);
        if (empty($result) || !is_array($result)) {
            $this->status = 'result error';
            $this->errmsg = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] === 'FAILED') {
            if (isset($result['subCode']) && isset($result['subMsg'])) {
                $this->status = $result['subCode'];
                $this->errmsg = $result['subMsg'];
            } elseif (isset($result['code']) && isset($result['msg'])) {
                $this->status = $result['code'];
                $this->errmsg = $result['msg'];
            } else {
                $this->status = -1;
                $this->errmsg = '未知错误';
            }
            return false;
        }
        return $result;
    }
    //------------------------------------------------------------
    //支付账单查询API
    //------------------------------------------------------------

    function JSAPI($data)
    {
        $this->service = "/precreate";//扫码--公众号支付
        return $this->request($data); //发送请求
    }

    function test_SIBill()
    {
        $this->service         = "/bill";//扫码--公众号支付
        $head["merchantCode"]  = "EW_N0448953806";//商户编号
        $head["billBeginTime"] = "20180701120000"; //查询开始时间\
        $head["billEndTime"]   = "20180930130000"; //查询结束时间
        return $this->request($head); //发送请求
    }

    function getKey($data)
    {
        $this->service    = "/login";//扫码--公众号支付
        $data["userName"] = "tlduo8";//商户编号
        $data["passWord"] = "a234567"; //查询开始时间\
        return $this->request($data); //发送请求
    }

    function getMerchantList()
    {
        $this->service = "/merchant/list";//扫码--公众号支付
        return $this->request(); //发送请求
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}