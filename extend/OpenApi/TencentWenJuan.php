<?php

namespace OpenApi;

use Exception;

class TencentWenJuan
{
    const API_URL = 'https://wj.qq.com';
    public $id;
    public $hash;
    public $code;
    public $msg;

    /**
     *   constructor.
     */
    public function __construct()
    {

    }

    /**
     * 模拟提交刷脸活动报名
     * @param array|string $merchant_id 商户号或商户号数组
     * @return bool|array
     * @throws Exception
     */
    public function submit_face_pay_active($merchant_id)
    {
        $url    = '/s2/6582077/cd0f';
        $answer = [
            0 => '',//描述部分
            1 => '深圳一卡易科技股份有限公司', //服务商名称（如为渠道商则填渠道商名）*
            2 => '229806337', //服务商商户号（如为渠道商则填渠道号）*
            3 => '', //参与活动的商户名称  （如由服务商统一报名无需填写，商户自行报名必须填写）
            4 => is_array($merchant_id) ? join(';', $merchant_id) : $merchant_id, //子商户号*  （如有多个商户号，请用分号[；] 隔开；例如：1234534321；1237281923；1289301298）
            5 => '桌面单屏刷脸设备', //设备类型（如：青蛙PRO/青蛙/桌面单屏刷脸设备/桌面双屏POS设备/自助大屏）*
            6 => '钱客多电子立牌', //设备类型（如：青蛙PRO；天之河一代青蛙；桑达大屏SC3318）*
            7 => '2.21', //当前设备版本版本（APP/SDK）*
            8 => '2.21.104', //当前SDK版本
            9 => '是' //是否接入海报运营位（SDK模式必填；填是/否）
        ];
        return $this->url($url)->collect_answer($answer);
    }

    /**
     * 模拟提交问卷
     * @param array $answer 答案数据包
     * @return bool|array
     */
    public function collect_answer(array $answer)
    {
        $url          = '/sur/collect_answer';
        $survey_pages = $this->get_survey_pages();
        foreach ($survey_pages[0]['questions'] as $key => $val) {
            $survey_pages[0]['questions'][$key]['text'] = $answer[$key] ?? '';
        }
        $data = [
            'survey_id'     => $this->id,
            'hash'          => $this->hash,
            'answer_survey' => json_encode(['pages' => $survey_pages], JSON_UNESCAPED_UNICODE)
        ];
        return $this->post($url, $data);
    }

    /**
     * 获取默认数据后组装问卷答案
     * @return bool|array
     */
    public function get_survey_pages()
    {
        $result = $this->get_survey();
        if ($result === false) {
            return false;
        }
        $pages        = $result['pages'];
        $survey_pages = [];
        foreach ($pages as $page) {
            $question_array = [];
            foreach ($page['questions'] as $question) {
                $question_array[] = [
                    'id'   => $question['id'],
                    'type' => $question['type'],
                    'text' => '',
                ];
            }
            $survey_pages[] = [
                'id'        => $page['id'],
                'questions' => $question_array
            ];
        }
        return $survey_pages;
    }

    /**
     * 获取默认数据
     * @return bool|array
     */
    protected function get_survey()
    {
        $url    = '/sur/get_survey?id=' . $this->id . '&hash=' . $this->hash . '&logger=1';
        $result = $this->get($url);
        if ($result === false) {
            return false;
        }
        return $result;
    }

    /**
     * HTTP GET
     * @param string $url 请求的url
     * @return bool|array
     */
    public function get(string $url)
    {
        $result = curl()->get(self::API_URL . $url)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * HTTP POST请求
     * @param string $url 请求的url
     * @param array $data 请求的数据
     * @return bool|array
     */
    public function post(string $url, array $data = [])
    {
        $header = ['referer' => self::API_URL . '/s2/' . $this->id . '/' . $this->hash];
        $result = curl()->set_header($header)->post(self::API_URL . $url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult(string $result)
    {
        if (!is_array($result)) {
            $this->code = 'result error';
            $this->msg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['status']) && $result['status'] !== 1) {
            $this->code = $result['status'];
            $this->msg  = $result['info'];
            return false;
        }
        return $result['data'] ?? $result;
    }


    /**
     * 设置url
     * @param string $url
     * @return $this
     * @throws Exception
     */
    public function url(string $url)
    {
        $array = explode('/', self::API_URL . $url);
        if (empty($array[4])) {
            throw new Exception('URL链接不是腾讯问卷链接');
        }
        $this->id   = $array[4];
        $this->hash = $array[5];
        return $this;
    }

    /**
     * 模拟提交收收银员活动报名
     * @param array|string $merchant_id 商户号或商户号数组
     * @return bool|array
     * @throws Exception
     */
    public function submit_checker_active($merchant_id)
    {
        $url    = '/s2/7490578/cc0c';
        $answer = [
            0 => '',
            1 => '深圳一卡易科技股份有限公司', //服务商名称（如为渠道商则填渠道商名）*
            2 => '229806337', //服务商商户号*
            3 => '零售', //商户所属行业
            4 => '',
            4 => is_array($merchant_id) ? join(';', $merchant_id) : $merchant_id, //子商户号*  （如有多个商户号，请用分号[；] 隔开；例如：1234534321；1237281923；1289301298）
            6 => '桌面单屏刷脸设备',
            7 => '钱客多电子立牌',
        ];
        return $this->url($url)->collect_answer($answer);
    }
}