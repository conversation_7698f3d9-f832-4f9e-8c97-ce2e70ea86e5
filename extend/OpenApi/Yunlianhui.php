<?php

namespace OpenApi;

class Yunlianhui
{
    /** 接口基础地址 */
    const BASE_URL = 'https://opentest.yunlianhui.cn/';
    /** 商户OPENID */
    public $client_id;
    public $user_id;

    /** 商户SECRET */
    public $client_secret;
    public $client_credentials;
    public $access_token;
    public $authorize_access_token;

    /** errcode */
    public $errcode;

    /** errmsg */
    public $errmsg;

    /** action */
    public $action;
    public $redirect_uri = 'http://www.yikayi.net/gateway/yunlianhui/';

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $this->client_id     = $options['client_id'];
        $this->client_secret = $options['client_secret'];
        $this->user_id       = isset($options['user_id']) ? $options['user_id'] : '8c4958f8602a7f0daec5428d354b697e';
    }

    public function register_fast($mobile = '***********', $rcm_id = 'ylhadmin')
    {
        $url          = 'api/register_fast';
        $access_token = $this->get_client_credentials();
        $data         = [
            'client_id'    => $this->client_id,
            'access_token' => $access_token,
            'mobile'       => $mobile,
            'rcm_id'       => $rcm_id,
        ];
        return $this->getArrayResult($url, $data);
    }

    public function get_client_credentials()
    {
        $key   = 'ylh:' . $this->client_id . ':client_credentials_access_token';
        $cache = cache($key);
        if ($cache) {
            return $cache;
        }
        $url    = 'token/client_credentials';
        $data   = [
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'client_credentials',
            'scope'         => 'register_fast',
        ];
        $result = $this->getArrayResult($url, $data);
        if ($result) {
            $access_token = $result['access_token'];
            $expires_in   = $result['expires_in'];
            cache($key, $access_token, $expires_in);
            return $access_token;
        }
        return false;
    }

    protected function getArrayResult($url, $data = [])
    {
        $header            = [
            'Content-Type'    => 'application/x-www-form-urlencoded',
            'User-Agent'      => 'ylh-php-sdk',
            'Accept-Encoding' => '*',
        ];
        $data['timestamp'] = !empty($data['timestamp']) ? $data['timestamp'] : format_timestamp();
        $data['sign']      = $this->getSignature($data);
        //       dump($data);
        $url    = self::BASE_URL . $url;
        $result = curl()->set_header($header)->post($url, $data)->get_body();
        //       dump($result);
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 获取签名
     * @param array $data 签名数组
     * @return bool|string 签名值
     */
    protected function getSignature($data)
    {
        ksort($data);
        $params = '';
        foreach ($data as $key => $value) {
            if (!$this->checkEmpty($value)) {
                $params .= $key . $value;
            }
        }
        return strtoupper(MD5($this->client_secret . $params . $this->client_secret));
    }

    //获取客户端访问令牌

    /**
     * 校验$value是否非空
     *
     * @param  $value
     * @return boolean
     *  if not set ,return true;
     *    if is null , return true;
     **/
    protected function checkEmpty($value)
    {
        if (!isset($value))
            return true;
        if ($value === null)
            return true;
        if (trim($value) === "")
            return true;
        return false;
    }

    //快捷注册会员

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)

    {
        if (empty($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['error_code']) && $result['error_code'] !== 0) {
            $this->errcode = $result['error_code'];
            //$this->message = $result['error_description'];
            return false;
        }
        if (isset($result['error']) && $result['error'] != '') {
            $this->errcode = $result['error'];
            $this->errmsg  = $result['error_description'];
            return false;
        }

        return $result;
    }

    public function get_token_by_code($code)
    {
        $data   = [
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'grant_type'    => 'authorization_code',
            'code'          => $code,
            'redirect_uri'  => $this->redirect_uri
        ];
        $url    = 'token/authorize/accesstoken';
        $result = $this->getArrayResult($url, $data);
        if ($result) {
            $access_token = $result['access_token'];
            cache('ylh:' . $result['user_id'] . '_authorize_access_token', $access_token, $result['expires_in']);
        }
        return $result;
    }

    public function redirect_authorize_url($token)
    {
        ob_end_clean();
        $location_url = self::BASE_URL . 'token/authorize?client_id=' . $this->client_id . '&redirect_uri=' . urlencode($this->redirect_uri) . '&state=' . $token . '&response_type=code&scope=points+basic_info';
        header("Location:" . $location_url);
        exit;
    }

    public function token_destroy()
    {
        $url  = 'token/token_destroy';
        $data = [
            'client_id'    => $this->client_id,
            'user_id'      => '',
            'access_token' => '',
            'token'        => '',
            'redirect_uri' => $this->redirect_uri
        ];
        return $result = $this->getArrayResult($url, $data);
    }

    //会员主动撤销访问令牌

    public function get_basic_info()
    {
        $url          = 'api/basic_info';
        $access_token = $this->get_client_credentials();
        $data         = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_authorize_access_token(),
        ];
        return $this->getArrayResult($url, $data);
    }

    //获取基本信息

    public function get_authorize_access_token()
    {
        $key = 'ylh:' . $this->user_id . '_authorize_access_token';
        return cache($key);
    }

    public function basic_info($type = 'point')
    {
        $url  = 'api/account_info';
        $data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_authorize_access_token(),
            // 'type'         => '',// w_point,r_point,point 获取白积分 红积分 库存积分 账户信息//w_point,money_trade 获取 白积分 创业账户信息//   (不传该参数) 获取所有账户信息
        ];
        $type && $data['type'] = $type;
        return $this->getArrayResult($url, $data);
    }

    public function add_points()
    {
        $url  = 'api/points';
        $data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_authorize_access_token(),
            'buyer_mobile' => '***********',//买家手机号
            'money'        => '1',//金额（单位：分）
            'order_msg'    => 'order_msg',//订单信息
        ];
        return $this->getArrayResult($url, $data);
    }

    //积分返还撤销
    public function points_revoke()
    {
        $url  = 'api/points_revoke';
        $data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_authorize_access_token(),
            'order_id'     => '',//为订单号，积分返还接口中得到。
            'reason'       => '',//积分返还撤销理由，请填写具体撤销理由
            'money'        => '',//积分返还撤销的金额（单位：分）。可以填写小于等于原始订单的金额，以便处理一笔订单多次撤销积分返还。
        ];
        return $this->getArrayResult($url, $data);
    }

    public function account_info_multiple()
    {
        $url  = 'api/account_info_multiple';
        $data = [
            'client_id'    => $this->client_id,
            'access_token' => $this->get_authorize_access_token(),
        ];
        return $this->getArrayResult($url, $data);
    }
}