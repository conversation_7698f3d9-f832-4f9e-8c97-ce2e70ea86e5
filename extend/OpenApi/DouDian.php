<?php

namespace OpenApi;


use Exception;

/**
 * Class DouYinApi
 * @package app\common\libs
 */
class DouDian
{
    private $host; //抖音接口API，API调用指南：https://op.jinritemai.com/docs/guide-docs/148/814
    private $appKey; //appKey
    private $appSecret; //appSecret
    private $accessToken; //访问令牌
    private $refreshToken; //刷新令牌
    private $versionNumber; //API协议版本，当前版本为 2
    private $versionNumberStr; //API协议版本，当前版本为 v2

    public function __construct()
    {
        $this->host             = 'https://openapi-fxg.jinritemai.com'; //接口访问地址
        $this->appKey           = '7410007734822667813';
        $this->appSecret        = '736570e1-dcfa-41f3-a4e1-e4b8e6170697';
        $this->versionNumber    = '2';
        $this->versionNumberStr = 'v' . $this->versionNumber;

        //获取access_token,refresh_token放到最后，如果其他的如versionNumber在后面设置则报错："v不可为空"，因为handleToken中调用了versionNumber，但versionNumber此时的值为NULL
        $result = self::handleToken(); //创建Token
//        $result = self::handleToken(false); //刷新Token：提示-"缺少code"，需要建一张第三方表存抖音该店铺的access_token,refresh_token,expire_time信息
        $this->accessToken  = $result['access_token']; //用于出创建token接口之外的其他接口
        $this->refreshToken = $result['refresh_token']; //用于刷新token接口
    }

    /**
     * 处理（创建/刷新）Token的方法
     * 开发指南 > 产品功能 > 授权介绍 -> 自用型应用店铺授权流程：https://op.jinritemai.com/docs/guide-docs/9/21
     * @param bool $createToken 是否调用创建Token的方法
     * @return array
     * @throws \Exception
     */
    public function handleToken($createToken = true)
    {
        if ($createToken) { //调用创建token接口
            $param  = [
                'code'       => '',
                'grant_type' => 'authorization_self',
                'shop_id'    => '19465181', //店铺ID，仅自用型应用有效；若不传，则默认返回最早授权成功店铺对应的token信息
            ];
            $method = 'token.create';

        } else { //调用刷新Token方法
            $param  = [
//                'app_id' => '', //应用key ，长度19位字母和数字组合的字符串，可不传
                'refresh_token' => $this->refreshToken, //注意：传真实的refreshToken值，而不是传REFRESH_TOKEN
                'grant_type'    => 'refresh_token',
            ];
            $method = 'token.refresh';
        }

        $timestamp = time(); //接口请求前记录开始时间，防止过期时间$expireTime失效
        $result    = self::fetch($method, $param);
        if ($result['code'] != 10000) { //请求失败
            throw new Exception($result['msg']);
        } else {
            $data         = $result['data'];
            $accessToken  = $data['access_token']; //accessToken
            $refreshToken = $data['refresh_token']; //refreshToken
            $expireTime   = $timestamp + $data['expires_in']; //Token过期时间 = 当前时间 + 有效时间（秒s）
            //可以利用缓存起来
            return [
                'access_token'  => $accessToken,
                'refresh_token' => $refreshToken,
            ];
        }
    }

    /**
     * 封装抖音接口公共方法
     * PHP调用说明：https://op.jinritemai.com/docs/guide-docs/151/811
     * @param $method 方法名：格式 token.create 方法中转为 token/create
     * @param $param 请求接口需要的参数名
     * @param bool $accessToken url中是否要加上access_token，默认否。
     *              为什么不直接传accessToken的值：在本类中，可以获取到accessToken的值，直接传，但是如果在其他的地方调用就获取不到access_token的值，需要传true/false标识在本类中获取。
     * @param bool $paramJsonAddToUrl 是否把paramJson放到 url 中，根据实际情况
     *          例：实际过程中【订单批量解密接口】不需要放到url中（猜测是这个接口paramJson内容太多，会超出GET的最大内容）
     *              订单批量解密接口：https://op.jinritemai.com/docs/api-docs/15/982
     * @return false|mixed|string
     */
    function fetch($method, $param, $accessToken = false, $paramJsonAddToUrl = true)
    {
        //当前时间戳
        $timestamp = time();

        //PHP中：如果数组为空转为json之后是[]。但接口可能是强类型语言编写的，需要传{}。所以$param为空时，需要把$paramJson设置为{}
        $paramJson = $param ? self::marshal($param) : '{}';

        //获取签名
        $sign = self::sign($method, $timestamp, $paramJson);
        //调用的方法.替换为/
        $methodPath = str_replace('.', '/', $method);
        //拼接url路径
        $url = $this->host . '/' . $methodPath . '?method=' . urlencode($method) . '&app_key=' . urlencode($this->appKey);

        if ($accessToken) {
            $url .= '&access_token=' . urlencode($this->accessToken);
        }

        $url .= '&timestamp=' . urlencode(strval($timestamp)) . '&v=' . urlencode($this->versionNumber) . '&sign=' . $sign;

        if ($paramJsonAddToUrl) {
            $url .= '&param_json=' . $paramJson;
        }

        $url    .= '&sign_method=' . urlencode('hmac-sha256'); //官方接口为非必填，但是不加签名会验证失败
        $result = curl()->json()->post($url, $paramJson)->get_body();
        dump($result);
        return $result;
    }

    //计算签名
    function sign($method, $timestamp, $paramJson)
    {
        $paramPattern = 'app_key' . $this->appKey . 'method' . $method . 'param_json' . $paramJson . 'timestamp' . $timestamp . $this->versionNumberStr;
        $signPattern  = $this->appSecret . $paramPattern . $this->appSecret;
        return hash_hmac("sha256", $signPattern, $this->appSecret);
    }

    //序列化参数，入参必须为关联数组（键值对数组）
    function marshal(array $param)
    {
        self::rec_ksort($param); // 对关联数组中的kv，执行排序，需要递归
        // 重新序列化，确保所有key按字典序排序
        // 加入flag，确保斜杠不被escape，汉字不被escape
        return json_encode($param, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    //关联数组排序，递归
    function rec_ksort(array &$arr)
    {
        $kstring = true;
        foreach ($arr as $k => &$v) {
            if (!is_string($k)) {
                $kstring = false;
            }
            if (is_array($v)) {
                self::rec_ksort($v); //这里的调用方式要和marshal中调用方式一致
            }
        }
        if ($kstring) {
            ksort($arr);
        }
    }
}
