<?php

namespace OpenApi;

use Exception;

class JieShun
{
//    const  API_URL_ENV = 'https://jsopen.jslife.com.cn/openApi';
//    const  API_URL = 'https://syx-open.jslife.com.cn/openApi'; //测试环境
    const  API_URL = 'https://jsopen.jslife.com.cn/openApi'; //正式环境

    public $errcode;
    public $errmsg;
    public $_retry = false;//接口地址
//    private $appid = '989841413043769345'; //测试环境


    private $appid = '1244708731510353922';  //正式环境

    //下方测试密钥
    //private $secret = 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKtNXQet9kUZKt3SwBNcC0ZK3exeAwrFXwy6MwWI/ws0jDtx08LGh78ydoQ5VMw8MudZ5mnzXQtkFr1Zmj0M5z9bz8f5Td1S+zjPFuJSvHa54IajgQD+nPMjl7d9IgBJrYFvMdOojs6voxibzGv/zPMFSZJZmtdoLsYg6gnV2QTpAgMBAAECgYAHu/t6UElxPzynPU+80AgWSWURugDrKJQtD+8jzdauG9ZFDNOh1LLmWBHv0GLawdEOjwmPORS3YfKA5B3Jqd568+ts0a45fXIfzcJtxmUwMSVeEwmLVIKEFWE14xSEKYt/iVH08Qgdi5WDSMvmlQmOonV6DvFblE7U0Uro3w22/QJBANzTtoEpitkcxozrfWNozTvfFrxJFUgVCp9zuxjaBgLPj/MRvvEAOUxYox6gt70pbNsDAgMVrWpg4vonUAM8gkMCQQDGlkMIno3atqCkFc49Xf4/qnhCnK4FyugXtgBYpzXhf/ruSIwyOzgQOxIOPdb1XkDdWoETLMlLuCk36hOK7PdjAkB1Ll+FcN7+UTfHZdtW2jE2WS6+YWXKp2fIr2gUSvWvnic8DCmvx7egHNUKlqyJ06axHYp+yo0IiztHFX9vCDfzAkEApgD2CD6CVlJT9tZDkQkBgDcAUK6oyi7T1P5PS8x7PgW9vXLnvxXBx8+ZSE5pYrNnhaIiQ+UsYB+SVStXKsrsAQJATyWKNa/Rd55Q7zFV35XWNoTSzPnu3nvb+9rF8m22NKXwyRlPVt8CX8+QaTOFmdbkN8n5NHIxR0rbLEnDupp0ow==';

    //下方正式密钥
    private $secret = 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANFz0p2hlPi7QFNlL/c2/6Y8IrwvkZED7KA+FmQ30Y5O0BQ7t9kzmX5N+77l0TVUZlOan2eWMD7NxeluXj86L1Owca5ezxTxgmheccoDfQRuJaYFcRGBTPS6fLRKaQwBHwK4HICnYqV+acPyN05+BPBkUiBjKuFxjuCTjI/WXSd/AgMBAAECgYBCtktdHbwfRTUF2vLeIMxonDrMCbKpp8BUgbIMFaNPWlb4OtUh/0dibWBDu1tZSxqUgmlHSxTuzAe/hAHMzbGIuMkTbZXZ11GCuLOOLcJ+9Eh81lcwzXcHnh+WYRsShRiVoCY6eJk2czwWtZB006F7ExKMig6+y6UVRed0y8NJQQJBAPDP/X2xFWwiVd26uJWndB8WBW5sremx3ssiIVS67nQQT+dxt0GPZcS7DiqILH5eWFRnJF9meICVVJZg3voXFnECQQDeqYNGApt02e1wLgTH6vGHXCQsUckntef6yy0vG3EdwoFbf4LxXprXgIY5aB59c6Pzl7ES6Ov9gplFZaRo5HTvAkBwEDpk3Z2cfrX6w0PW0OBm/vZ0Frsv/4gwe6eLKJqDBHxKtk6TgcnYLTxAarJZ9EmirYTtqF/3ZobAnD/nngrhAkAlGcmt2LFefxF5J7HUPemQzGGaoOYLn9vPhhs5RjFK7CXxwQVogoL6dYfn+NlbvfELnbL7m6P20dQIJC+I6sgxAkEA1DWSNjSefIs8pXLg5wOcg9RmSLnwobm/Sed5yt+VFDK9W4hQBeVS30kalBRYcwNpl4Hv7ahtFW5uJBqagrS5SA==';


//    private $projectCode = '0020200529'; //测试项目编号
    private $projectCode = 'p201198454'; //正式项目编号


    //private $businesserCode = '880075500000002'; //测试商户号
    private $businesserCode = '000000008032813'; //正式商户号

    private $customerId = '3335'; //项目编号
    private $abilityCode = 'kfpt_syfw'; //开放服务编码 车行的是tq.jzgk.a003   人行 tq.rx.a001   商业套餐：kfpt_syfw(接口编码为3c开头的接口)

    /**
     *  constructor.
     * @param array $config
     */
    public function __construct($config = [])
    {
    }

// json转换
    public function encode($value)
    {
        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        } else {
            return $value;
        }
    }

    /**
     * @param array $data
     * @return string
     */
    protected function get_sign($data = [])
    {
        if (empty($data)) {
            return false;
        }
        $json_string = json_encode($data, JSON_UNESCAPED_UNICODE);
        $decodes     = json_decode($json_string, true);
        // 签名
        $keyNames = [];
        foreach ($decodes as $key => $value) {
            $keyNames[] = $key;
        };
        sort($keyNames);
        $sign = '';
        foreach ($keyNames as $KeyName) {
            $sign = $sign . '&' . $KeyName . '=' . $this->encode($decodes[$KeyName]);
        };
        $sign        = substr($sign, 1);
        $private_key = "-----BEGIN RSA PRIVATE KEY-----\n$this->secret\n-----END RSA PRIVATE KEY-----";

        $pkeyid = openssl_get_privatekey($private_key);
        if (empty($pkeyid)) {
            echo "private key resource identifier False!";
            return False;
        }
        $verify = openssl_sign($sign, $signature, $pkeyid, OPENSSL_ALGO_MD5);
        return base64_encode($signature);
    }

    protected function request($serviceId, array $attributes)
    {
        $data = [
            'app_id'      => $this->appid,
            'sign_type'   => 'MD5',
            'charset'     => 'utf-8',
            'format'      => 'form',
            'method'      => $serviceId,
            'projectCode' => $this->projectCode,
            'abilityCode' => $this->abilityCode,
            'timestamp'   => $this->getMillisecond(),
            'biz_content' => [
                'v'   => '2',
                'cid' => $this->businesserCode,
                'p'   => [
                    'serviceId'   => $serviceId,
                    'requestType' => 'DATA',
                    'attributes'  => $attributes
                ]
            ],
        ];
        $sign = $this->get_sign($data);
        if ($sign === false) {
            throw new Exception('签名失败');
        }
        $data['sign'] = $sign;
        $result       = curl()->post(self::API_URL, $data)->get_body();
//        dump(self::API_URL, $data, $result);
        return $this->_parseResult($result);
    }

    public function coupons_receive_coupons($data = [])
    {
        $serviceId  = '3c.coupons.receive.coupons';
        $attributes = [
            "businesserCode" => $this->businesserCode,
            "planNo"         => "",
            "tel"            => ""
        ];
        $data       = array_merge($attributes, $data);
        return $this->request($serviceId, $data);
    }

    public function coupons_query_park($data = [])
    {
        $serviceId  = '3c.coupons.query.park';
        $attributes = [
            "businesserCode" => $this->businesserCode,
        ];
        $data       = array_merge($attributes, $data);
        return $this->request($serviceId, $data);
    }

    public function coupons_modify_status($data = [])
    {
        $serviceId  = '3c.coupons.modify.status';
        $attributes = [
            "planNo"     => 'CPP240527100137763',
            "planStatus" => 'FREEZES',
        ];
        $data       = array_merge($attributes, $data);
        return $this->request($serviceId, $data);
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] !== '10000') {
            $this->errcode = $result['code'];
            $this->errmsg  = $result['msg'];
            return false;
        }
        $result = $result['biz_response'] ?? $result;
        if (isset($result['resultCode']) && $result['resultCode'] == 0) {
            return $result;
        } else {
            $this->errcode = $result['resultCode'] ?? -1;
            $this->errmsg  = $result['message'] ?? '未知错误';
            return false;
        }
    }

    //返回当前的毫秒时间戳
    public function getMillisecond()
    {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }

    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }
}