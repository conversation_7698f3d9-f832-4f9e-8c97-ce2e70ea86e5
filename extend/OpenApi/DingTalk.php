<?php

namespace OpenApi;

class DingTalk
{
    const  API_URL = 'https://oapi.dingtalk.com';//接口地址

    public $errcode;
    public $errmsg;
    public $_retry = false;
    private $appkey = 'ding5s9gcqod2tqjca17'; //wwj
    private $appsecret = 'p7Y_dR9QgT0vMJyv6alRks3nDyTRa3A2N-Rp2OmJViWmbrs2WAcopY2Wk3Ok9ueU';

    /**
     *  constructor.
     * @param array $config
     */
    public function __construct($config = [])
    {

    }

    //获取钉钉打卡记录
    public function attendance_list($post_data = [])
    {
        $url          = '/attendance/list?access_token=ACCESS_TOKEN';
        $default_data = [
            'workDateFrom' => date("Y-m-d 00:00:00"),
            'workDateTo'   => format_timestamp(),
            'userIdList'   => ["01535361695274", "044248343325"], //xyf && wwj
            'offset'       => 0,
            'limit'        => 50
        ];
        return $this->post($url, array_merge($default_data, $post_data));
    }


    protected function get(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function post(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function request(string $url, array $data = [], $method = 'get')
    {
        $url          = self::API_URL . $url;
        $access_token = $this->get_token();
        $url          = str_replace('ACCESS_TOKEN', $access_token, $url);
        $result       = curl()->$method($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    //资产余额
    public function get_token()
    {
        $data   = [
            'appkey'    => $this->appkey,
            'appsecret' => $this->appsecret
        ];
        $url    = self::API_URL . '/gettoken';
        $result = curl()->get($url, $data)->get_body();
        return $result['access_token'];
    }


    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['errcode']) && $result['errcode'] !== 0) {
            $this->errcode = $result['errcode'];
            $this->errmsg  = $result['errmsg'];
            return false;
        }
        return $result['data'] ?? $result;
    }


    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }
}