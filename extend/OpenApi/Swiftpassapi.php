<?php

namespace OpenApi;


use Exception;

class Swiftpassapi
{
    /** 正式环境接口基础地址 */
    const BASE_URL = 'https://interface.swiftpass.cn/sppay-interface-war/gateway';
    /** 测试环境接口基础地址 */
    // const BASE_URL = 'http://35api.test.swiftpass.cn/sppay-interface-war/gateway';
    /** 登录账号 */
    public $partner;
    /** 登录密码 */
    public $key;
    /** status */
    public $errcode;

    /** message */
    public $errmsg;
    public $_retry = false;
    private $charset = 'UTF-8';
    private $dataType = 'xml';
    private $isdeg = false;

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        /** 正式环境配置 */
        $options = [
            'partner' => '105520000010',
            'key'     => 'b946715029628fd4b120d1ba882a6f57',
        ];
        if ($this->isdeg) {
            /** 测试环境配置 */
            $options = [
                'partner' => '100590006610',
                'key'     => '35aa6c203b7b4218713153f6d8dc39ec',
            ];
        }

        $this->partner = $options['partner'];
        $this->key     = $options['key'];
    }

    public function pic_upload($filepath, $type = 1)
    {
        $data                 = [
            'picUpload' => [
                'picType' => $type,
            ]
        ];
        $post_data            = $this->get_post_data($data, __FUNCTION__);
        $post_data['picFile'] = '@' . $filepath;
        $config               = ['url' => self::BASE_URL];
        $obj                  = new Fsockopen();
        $obj->setConfig($config);
        $obj->setPostData($post_data);
        $result = $obj->multipart();
        $result = $this->_parseResult($result);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return (!empty($result['pic'])) ? $result['pic'] : false;
    }

    protected function get_post_data($data, $serviceName)
    {
        $post_data             = [
            'charset'     => $this->charset,
            'data'        => tools()::array_to_xml($data),
            'dataType'    => $this->dataType,
            'partner'     => $this->partner,
            'serviceName' => $serviceName,
        ];
        $post_data['dataSign'] = $this->get_sign($post_data);
        return $post_data;
    }

    protected function get_sign($data)
    {
        ksort($data);
        $getStr = $this->getStr($data) . $this->key;
        $sign   = md5($getStr);
        return $sign;
    }

    protected function getStr($arr, $str = '')
    {
        foreach ($arr as $key => $val) {
            if (is_array($val)) {
                $this->getStr($val, $str);
            }
            $str .= $key . "=" . $val . "&";
        }
        return rtrim($str, '&');
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        $result = tools()::xml2arr($result);
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['isSuccess']) && $result['isSuccess'] !== 'T') {
            $this->errcode = !empty($result['errorCode']) ? $result['errorCode'] : -1;
            $this->errmsg  = !empty($result['errorMsg']) ? $result['errorMsg'] : '系统错误';
            return false;
        }
        return $result;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }

    public function check_subdev_config($partner, $merchant_id)
    {
        $wechat_config = $this->search_subdev_config($merchant_id, $partner);
        if (count($wechat_config['path']) < 2) {
            $this->errmsg = '商户号:' . $merchant_id . ',【支付目录】未配置,仅有' . count($wechat_config['path']) . '个!';
            return false;
        }
        if (empty($wechat_config['appid'])) {
            $this->errmsg = '商户号:' . $merchant_id . '【支付子商户APPID】未配置!';
            return false;
        }
        if (empty($wechat_config['subscribeAppid'])) {
            $this->errmsg = '商户号:' . $merchant_id . '【关注APPID】未配置!';
            return false;
        }
        if (empty($wechat_config['appidMappingsubcribeAppid'])) {
            $this->errmsg = '商户号:' . $merchant_id . '【appidMappingsubcribeAppid】未配置!';
            return false;
        }
        foreach ($wechat_config['appidMappingsubcribeAppid'] as $key => $val) {
            if ($val['subscribeAppid'] == $val['subAppid']) {
                return true;
            }
        }
        return false;
    }

    public function search_subdev_config($merchant_id, $partner)
    {
        //优先用模拟登录方法查找支付配置信息
        $data      = [
            "subdevConf" => [
                "merchantId" => $merchant_id,
                "partner"    => $partner,
            ]
        ];
        $post_data = $this->get_post_data($data, __FUNCTION__);
        $result    = $this->http_request($post_data);
        if ($result === false) {
            //还是失败则重试一次
            $swiftpass = new Swiftpass();
            return $swiftpass->search_subdev_config($merchant_id, $partner);
            //return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        $result = $result['subdevConf'];
        if (is_string($result['path'])) {
            $result['path'] = explode(',', $result['path']);
        }
        if (is_string($result['appid'])) {
            $result['appid'] = explode(',', $result['appid']);
        }
        if (!empty($result['subscribeAppids']) && is_string($result['subscribeAppids'])) {
            $result['subscribeAppids'] = explode(',', $result['subscribeAppids']);
        }
        if (!empty($result['appidMappingsubcribeAppid']) && is_string($result['appidMappingsubcribeAppid'])) {
            $result['appidMappingsubcribeAppid'] = explode('|', $result['appidMappingsubcribeAppid']);
            foreach ($result['appidMappingsubcribeAppid'] as $key => $val) {
                $result['appidMappingsubcribeAppid'][$key] = json_decode($val, true);
            }
        }
        //PS subscribeAppid 为空的时候是空数组,不为空的时候 是返回字符串
        return $result;
    }

    protected function http_request($data)
    {
        $data   = $this->getStr($data);
        $header = ["Content-Type" => "application/x-www-form-urlencoded;charset=utf-8"];
        $result = curl()->set_header($header)->post(self::BASE_URL, $data)->get_body();
        if ($result === false) {
            $this->errmsg = __CLASS__ . __FUNCTION__ . '请求发生错误';
            return false;
        }
        return $this->_parseResult($result);
    }

    public function wechat_pay_config($partner, $merchant_id, $data)
    {
        //默认关注appid和支付appid保持一致
        empty($data['subscribeAppid']) && ($data['subscribeAppid'] = $data['appid']);
        //查询当前的支付配置
        $wechat_config = $this->search_subdev_config($merchant_id, $partner);
        if ($wechat_config === false) {
            return false;
        }
        //配置支付目录
        $paths = $data['path'];
        foreach ($paths as $path) {
            if (!in_array($path, $wechat_config['path'])) {
                $result = $this->add_path($partner, $merchant_id, $path);
                if ($result === false) {
                    return false;
                }
            }
        }
        //配置支付appid
        if (!in_array($data['appid'], $wechat_config['appid'])) {
            $result = $this->add_appid($partner, $merchant_id, $data['appid']);
            if ($result === false) {
                return false;
            }
        }
        //配置关注appid
        if (empty($wechat_config['subscribeAppid'])) {
            $result = $this->add_subscribeAppid($partner, $merchant_id, $data['subscribeAppid']);
            if ($result === false) {
                return false;
            }
        }
        return true;
    }

    public function add_path($partner, $merchant_id, $path)
    {
        $result = $this->add_subdev_config($partner, $merchant_id, 'path', $path);
        if ($result === false) {
            $swiftpass = new Swiftpass();
            $data      = [
                'partner'     => $partner,
                'payCenterId' => 61, //暂时写死微信
                'path'        => $path,
            ];
            $result    = $swiftpass->add_wechat_pay_path($data);
        }
        return $result;
    }

    protected function add_subdev_config($partner, $merchant_id, $config, $value)
    {
        //$config //path,appid,subscribeAppid
        $data = [
            "subdevConf" => [
                "merchantId" => $merchant_id,
                "partner"    => $partner,
                $config      => $value
            ]
        ];
        //配置关注appid时自动追sub_appid
        if (!empty($data['subdevConf']['subscribeAppid'])) {
            $data['subdevConf']['appid'] = $data['subdevConf']['subscribeAppid'];
        }
        $post_data = $this->get_post_data($data, __FUNCTION__);
        $result    = $this->http_request($post_data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //支付配置检查

    public function add_appid($partner, $merchant_id, $appid)
    {
        $result = $this->add_subdev_config($partner, $merchant_id, 'appid', $appid);
        if ($result === false) {
            $swiftpass = new Swiftpass();
            $data      = [
                'partner'     => $partner,
                'payCenterId' => 61, //暂时写死微信
                'appid'       => $appid,
            ];
            $result    = $swiftpass->add_sub_appids($data);
        }
        return $result;
    }

    public function add_subscribeAppid($partner, $merchant_id, $subscribeAppid)
    {
        $result = $this->add_subdev_config($partner, $merchant_id, 'subscribeAppid', $subscribeAppid);
        if ($result === false) {
            $swiftpass = new Swiftpass();
            $data      = [
                'partner'        => $partner,
                'payCenterId'    => 61, //暂时写死微信
                'subscribeAppid' => $subscribeAppid,
            ];
            $result    = $swiftpass->add_subscribe_appid($data);
        }
        return $result;
    }

    public function mch_thi_register_search($merchant_id, $pay_center_id = null)
    {
        if (is_null($pay_center_id)) {
            $pay_center_id = [61, 98];
        } elseif (is_int($pay_center_id)) {
            $pay_center_id = [$pay_center_id];
        }
        if (!is_array($pay_center_id)) {
            $this->errmsg = 'pay_center_id must be array';
            return false;
        }
        $search_result = [];
        foreach ($pay_center_id as $key => $val) {
            $data      = [
                "mchThiRegister" => [
                    "merchantId"  => $merchant_id,
                    "payCenterId" => $val, //61 微信 98 支付宝
                ]
            ];
            $post_data = $this->get_post_data($data, __FUNCTION__);
            $result    = $this->http_request($post_data);
            if ($result !== false) {
                $search_result[] = $result;
            }
        }
        if (empty($search_result)) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $search_result ?: false;
    }

    public function mch_bill_rate_deit($merchant_id, $bill_rate)
    {
        if ($bill_rate < 3.8) {
            $this->errmsg = '费率不能超过低于千分之3.8';
            return false;
        }
        if ($bill_rate > 6) {
            $this->errmsg = '费率不能超过千分之6';
            return false;
        }
        $api_code_list = [
            'pay.weixin.jspay',
            'pay.weixin.micropay',
            'pay.weixin.native',
            'pay.alipay.jspayv3',
            'pay.alipay.micropayv3',
            'pay.alipay.nativev3',
        ];
        foreach ($api_code_list as $key => $val) {
            $data      = [
                "mchPayConf" => [
                    "merchantId" => $merchant_id,
                    "apiCode"    => $val,
                    "billRate"   => $bill_rate,
                ]
            ];
            $post_data = $this->get_post_data($data, __FUNCTION__);
            $result    = $this->http_request($post_data);
            if ($result === false) {
                throw new Exception($merchant_id . $val . '费率修改失败' . $this->errmsg);
                return $this->checkRetry(__FUNCTION__, func_get_args());
            }

        }
        return true;
    }

    //支付类型查询接口

    public function mch_pay_conf_search($merchant_id, $api_code = 'pay.weixin.jspay')
    {
        $data      = [
            "mchPayConf" => [
                "merchantId" => $merchant_id,
                "apiCode"    => $api_code,
            ]
        ];
        $post_data = $this->get_post_data($data, __FUNCTION__);
        $result    = $this->http_request($post_data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result['mchPayConf'];
    }

    //支付类型查询接口

    public function mch_bank_account_search($merchant_id)
    {
        $data      = [
            "bankAccountVo" => [
                "merchantId" => $merchant_id,
            ]
        ];
        $post_data = $this->get_post_data($data, __FUNCTION__);
        $result    = $this->http_request($post_data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result;
    }

    //结算账号查询

    public function get_merchantid_by_outmerchantid($outMerchantId)
    {
        $url               = 'https://api.swiftpass.cn/unifiedAuth';
        $post_data         = [
            'service'       => 'unified.cms.store.query',
            'nonce_str'     => Date('YmdHis') . rand(1000, 9999),
            'outMerchantId' => $outMerchantId,
            'mch_id'        => $this->partner,
        ];
        $post_data['sign'] = $this->get_sign_old($post_data);
        $header            = ["Content-Type: application/x-www-form-urlencoded;charset=utf-8"];
        $data              = tools()::array_to_xml($post_data, 'xml');
        $result            = curl()->set_header($header)->post($url, $data)->get_body();
        $result            = tools()::xml2arr($result);
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        return !empty($result['merchantId_1']) ? $result['merchantId_1'] : false;
    }

    //通过外商户号获取光大商户号接口

    protected function get_sign_old($data)
    {
        ksort($data);
        $str = '';
        foreach ($data as $key => $val) {
            $str .= strtolower($key) . "=" . $val . "&";
        }
        $str  .= 'key=' . $this->key;
        $sign = md5($str);
        return strtoupper($sign);
    }

    //商户报备状态查询

    public function mch_partner_search($merchant_id, $apiCode = 'pay.weixin.jspay')
    {
        $data      = [
            "mchPayConf" => [
                "merchantId" => $merchant_id,
                "apiCode"    => $apiCode,
            ]
        ];
        $post_data = $this->get_post_data($data, __FUNCTION__);
        $result    = $this->http_request($post_data);
        if ($result === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        //成功的时候也有可能是返回空字符串
        if (empty($result['data'])) {
            $this->errmsg = '商户号' . $merchant_id . $apiCode . '商户号为空,可能商户报备失败!';
        }
        return !empty($result['data']) ? $result['data'] : false;
    }
}