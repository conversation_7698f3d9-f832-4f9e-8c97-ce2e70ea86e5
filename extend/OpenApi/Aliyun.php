<?php

namespace OpenApi;


class Aliyun
{
    /** 登录账号 */
    /** 登录密码 */
    /** status */
    public $errcode;
    /** message */
    public $errmsg;

    public $_retry = false;


    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
    }

    public function get_ip_info($ip)
    {
        $host    = "https://dm-81.data.aliyun.com";
        $path    = "/rest/160601/ip/getIpInfo.json";
        $url     = $host . $path . '?ip=' . $ip;
        $appcode = 'e9a2f2c67a554380827913246537f0c7';
        $header  = ["Authorization:" => "APPCODE " . $appcode];
        $result  = curl()->set_header($header)->get($url)->get_body();
        if ($this->_parseResult($result) === false) {
            return $this->checkRetry(__FUNCTION__, func_get_args());
        }
        return $result['data'];
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] !== 0) {
            $this->errcode = isset($result['code']) ? $result['code'] : -1;
            $this->errmsg  = '';
            return false;
        }
        return $result;
    }

    protected function checkRetry($method, $arguments = array())
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array(array($this, $method), $arguments);
        }
        return false;
    }
}