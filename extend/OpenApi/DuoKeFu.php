<?php

namespace OpenApi;

class DuoKeFu
{
    /** 接口基础地址 */
    const BASE_URL = 'http://chat.yikayi.net';


    /** reason */
    public $code = 0;

    /** action */
    public $msg = '';

    public $_retry = false;
    public $user_name = 'admin';
    public $password = '111aaa...';

    /**
     * WechatPay constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {

    }

    public function auto_add_seller_and_kefu($data)
    {
        $url = '/admin/api/add_seller_and_kefu';
        return $this->getArrayResult($url, $data);
    }

    public function get_admin_access_token()
    {
        $cache_key    = 'duo_ke_fu_admin_access_token:' . $this->user_name . '_' . md5($this->password);
        $access_token = cache($cache_key);
        if (!$access_token) {
            $url          = '/admin/login/get_access_token';
            $data         = [
                'username' => $this->user_name,
                'password' => $this->password,
            ];
            $result       = $this->getArrayResult($url, $data, false);
            $expires_in   = $result['expires_in'];
            $access_token = $result['access_token'];
            cache($cache_key, $access_token, $expires_in);
        }
        return $access_token;
    }


    public function getMsg()
    {
        return $this->msg;
    }

    public function getCode()
    {
        return $this->code;
    }

    protected function getArrayResult($url, $data = [], $token = true)
    {
        $url    = self::BASE_URL . $url;
        $header = [
//            'Authorization' => get_system_config('feng_chao_token'),
        ];
        if ($token) {
            $url .= '?access_token=' . $this->get_admin_access_token();
        }
        $result = curl()->form_params()->set_header($header)->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->code = -1;
            $this->msg  = '解析返回结果失败';
            return false;
        }
        if ($result['code'] !== 0) {
            $this->code = $result['code'];
            $this->msg  = $result['msg'];
            return false;
        }
        return $result['data'] ?? $result;
    }
}