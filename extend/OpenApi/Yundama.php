<?php

namespace OpenApi;

class Yundama
{
    //http://www.yundama.com/download/YDMHttp.html
    /** 接口基础地址 */
    const BASE_URL = 'http://api.yundama.com/api.php';
    /** appid */
    public $appid;
    /** appkey */
    public $appkey;
    /** username */
    public $username;
    /** password */
    public $password;
    public $cid;
    /** errcode */
    public $errcode;

    /** errmsg */
    public $errmsg;
    public $result;
    public $text = null;


    /**
     * Yundama constructor.
     * @param array $options
     */
    public function __construct($options = array())
    {
        $default_options = [
            'appid'    => '1444',
            'appkey'   => '9f2e8e1961f407e7486ca8470e9c7a80',
            'username' => 'xieyongfa123',
            'password' => '111aaa...',
        ];
        $options         = array_merge($default_options, $options);
        $this->appkey    = $options['appkey'];
        $this->appid     = $options['appid'];
        $this->username  = $options['username'];
        $this->password  = $options['password'];
    }

    public function upload($file_path, $codetype = 1004)
    {
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'codetype' => $codetype,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'timeout'  => 60,
            'file'     => '@' . $file_path,
            'method'   => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        if ($this->result && !empty($this->result['text'])) {
            $this->text = $this->result['text'];
        }
        return $this;
    }

    protected function getArrayResult($url, $data = [], $method = 'POST')
    {
        $method = strtolower($method);
        $result = curl()->$method($url, $data)->get_body();
        if (false === $this->_parseResult($result)) {
            return false;
        }
        return $result;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->errcode = -1;
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if ($result['ret'] !== 0) {
            $this->errcode = $result['ret'];
            return false;
        }
        return $result;
    }

    public function balance()
    {
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'method'   => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        return $this->result ? $this->result['balance'] : false;
    }

    public function report($flag = false, $cid = '')
    {
        $cid          = $cid ?: $this->cid;
        $flag         = ($flag === false) ? 0 : 1;
        $data         = [
            'username' => $this->username,
            'password' => $this->password,
            'appid'    => $this->appid,
            'appkey'   => $this->appkey,
            'cid'      => $cid,
            'flag'     => $flag,
            'method'   => 'report',
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data);
        return $this->result;
    }

    public function get_text()
    {
        if (!is_array($this->result)) {
            return false;
        }
        if ($this->text) {
            return $this->text;
        }
        $i = 0;
        while (true) {
            $this->text = $this->result();
            if ($this->text || $i > 5) {
                break;
            }
            sleep(3);
            $i++;
        }
        return $this->text;
    }

    public function result($cid = '')
    {
        $cid          = $cid ?: $this->cid;
        $data         = [
            'cid'    => $cid,
            'method' => __FUNCTION__,
        ];
        $this->result = $this->getArrayResult(self::BASE_URL, $data, 'GET');
        return !empty($this->result['text']) ? $this->result['text'] : $this->result;
    }
}