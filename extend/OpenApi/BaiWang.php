<?php
/**
 * 接口SDK
 */

namespace OpenApi;

use Exception;

class BaiWang
{
    static public $debug = false;
    //protected $api_domain = 'https://api.xiandouxian.cn';  //测试接口请求地址
    static public $instance;  //正式接口请求地址
    public $errcode;    //密码盐
    public $errmsg;
    protected $config = array(  //参数配置
                                //'appid'                => 59, //测试环境
                                'appid'                => 72, //正式环境
                                'access_key'           => 'f854c67f069d899e14ca024f8c5b8ff0',
                                'secret_key'           => 'c3ffde5caaa3f78c8729018cf2c6e18e',
                                'sign_code'            => '3af0de3f748054b163d31fcb64e0312e',
                                'commercial_tenant_id' => 4000,
    ); //token缓存时间
    protected $api_domain = 'https://api.100wft.com';
    protected $passwordKey = 'Bw%%36&5$(0&~Ku738KDhg-+63Ud%43zmLJriufkwo';
    protected $token;
    protected $expire = 1200;

    public function __construct($config = [])
    {
        session([]); //tp6下面可能该方法失效了
        $this->config = !empty($config) ? array_merge($this->config, $config) : $this->config;
        if (!isset($this->config['device_id']) || empty($this->config['device_id'])) {
            $this->config['device_id'] = session_id();
        }
        $this->getToken();
    }

    /**
     * 获取token
     * @return string
     * @throws Exception
     */
    public function getToken()
    {
        $cache_key   = 'baiwang:token:' . md5(serialize($this->config));
        $this->token = cache($cache_key);
        if (empty($this->token)) {
            $result = $this->api('/pc.v1.auth/token', $this->config);
            if ($result !== false && !empty($result['token'])) {
                $this->token = $result['token'];
                cache($cache_key, $this->token, $this->expire);
            } else {
                throw new Exception('token获取异常');
            }
        }
        return $this->token;
    }

    /**
     * 通用api接口请求
     * @param string $api_url api地址
     * @param array $data 数据包
     * @param string $no_sign_field 不需要签名的字段
     * @return bool|array
     * @throws Exception
     */
    public function api($api_url, $data, $no_sign_field = '')
    {
        if (!isset($data['token']) && !empty($this->token)) {
            $data['token'] = $this->getToken();
        }
        $data    = $this->signData($data, $no_sign_field);
        $api_url = $this->api_domain . $api_url;
        $result  = curl()->post($api_url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * 数据签名
     * @param array $data
     * @param string $no_sign_field
     * @return array
     */
    protected function signData($data, $no_sign_field = '')
    {
        $not = ['random'];  //不参与签名字段
        if ($no_sign_field) {
            $no_sign_field = explode(',', $no_sign_field);
            $not           = array_merge($not, $no_sign_field);
        }
        $not = array_unique($not);
        if (!is_array($data)) $data = [];
        $tmp = [];
        foreach ($data as $key => $val) {
            if (!in_array($key, $not) && !empty($val)) $tmp[$key] = $val;
        }
        ksort($tmp);
        $query        = http_build_query($tmp) . '&' . $this->config['sign_code'];
        $data['sign'] = md5(urldecode($query));
        return $data;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->errcode = 'result error';
            $this->errmsg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['code']) && $result['code'] !== 1) {
            $this->errcode = isset($result['code']) ? $result['code'] : -1;
            $this->errmsg  = $result['msg'];
            return false;
        }
        return isset($result['data']) ? $result['data'] : $result;
    }

    static public function setDebug($model = true)
    {
        self::$debug = $model;
    }

    /**
     * 静态实例化
     * @param array $config
     * @return self
     */
    static public function getInstance($config = [])
    {
        if (!self::$instance) self::$instance = new self($config);
        return self::$instance;
    }

    /**
     * 获取token
     * @return string
     */
    public function token()
    {
        return $this->token;
    }

    public function getBalance($account)
    {
        $data = ['account' => $account];
        return $this->api('/pc.v1.wallet.Wallet/account', $data);
    }

    public function oauthLogin($callbackUrl)
    {
        $data = [
            'sesid'       => session_id(),
            'callbackUrl' => $callbackUrl,
        ];
        $res  = $this->api('/pc.v1.wallet.oauth/createToken', $data);
        redirect($res['url']);
    }

    //取资金账户 账号或者手机号

    public function updateAccount($data)
    {
        //       $data = [
        //           'openid'       => 'd0e0e14c-e396-1fea-7926-6d2f7b879379',
        //           'account_type' => 'integration',
        //           'value'        => -20,
        //           'memo'         => '测试',
        //           'order_no'     => 'GON9989999845',
        //       ];
        return $this->api('/pc.v1.wallet.Wallet/updateAccount', $data);
    }

    //授权登录

    public function createOrder($data)
    {
        //       $data = [
        //           'seller_openid' => 'd0e0e14c-e396-1fea-7926-6d2f7b879379',
        //           'buyer_openid'  => '7689d29d-b17e-2ff0-1662-e9c997da34a7',
        //           'order_no'      => 'GN9890055570',
        //           'pay_cash'      => 100,
        //           'goods'         => '测试',
        //           'memo'          => '我的将军啊',
        //       ];
        return $this->api('/pc.v1.wallet.Wallet/createOrder', $data);
    }

    //加扣款

    public function getOrder($orderNo)
    {
        $data = ['order_no' => $orderNo];
        return $this->api('/pc.v1.wallet.Wallet/getOrder', $data);
    }

    //提交订单

    public function refundOrder($orderNo, $memo = '')
    {
        $data = ['order_no' => $orderNo, 'memo' => '【退单】' . $memo];
        return $this->api('/pc.v1.wallet.Wallet/refundOrder', $data);
    }

    //查询订单

    public function getUser($oauthToken)
    {
        $data = ['oauthToken' => $oauthToken];
        return $this->api('/pc.v1.wallet.oauth/getUser', $data);
    }

    //退单

    public function getShareUser($code)
    {
        $data = ['code' => $code]; //手机号 账号 分享码
        return $this->api('/pc.v1.wallet.user/shareUser', $data);
    }

    //获取用户信息

    public function getUserInfo($account)
    {
        $data = ['account' => $account]; //手机号 账号
        return $this->api('/pc.v1.wallet.user/getUserInfo', $data);
    }

    //获取分享用户信息

    public function checkAccount($account)
    {
        $data = ['account' => $account];
        return $this->api('/pc.v1.wallet.user/checkAccount', $data);
    }

    //获取分享用户信息

    public function checkMobile($mobile)
    {
        $data = ['mobile' => $mobile];
        return $this->api('/pc.v1.wallet.user/checkMobile', $data);
    }

    //检测账号是否被注册

    public function register($mobile, $rec_mobile = '1000000', $bid)
    {
        $password = rand(100000, 999999);
        $data     = [
            'account'    => $mobile,
            'load_psw'   => $this->password($password),
            'mobile'     => $mobile,
            'promo_code' => $rec_mobile
        ];
        $result   = $this->api('/pc.v1.wallet.user/register', $data);
        if ($result !== false) {
            send_sms('恭喜您成为百望会员！请下载百望商城APP，密码' . $password . '，会员积分当钱花，购物消费最高可省一半！', $mobile, $bid);
        }
        return $result;
    }

    //检测手机号是否被占用

    /**
     * 生成密码
     * @param string $str
     * @return string
     */
    public function password($str)
    {
        return $this->encrypt($str, $this->passwordKey);
    }

    //注册接口

    /**
     * 加密字符串
     * @param string $data 字符串
     * @param string $key 加密key
     * @param integer $expire 有效期（秒）
     * @return string
     */
    protected function encrypt($data, $key, $expire = 0)
    {
        $expire = sprintf('%010d', $expire ? $expire + time() : 0);
        $key    = md5($key);
        $data   = base64_encode($expire . $data);
        $x      = 0;
        $len    = strlen($data);
        $l      = strlen($key);
        $char   = $str = '';

        for ($i = 0; $i < $len; $i++) {
            if ($x == $l) $x = 0;
            $char .= substr($key, $x, 1);
            $x++;
        }

        for ($i = 0; $i < $len; $i++) {
            $str .= chr(ord(substr($data, $i, 1)) + (ord(substr($char, $i, 1))) % 256);
        }
        return str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($str));
    }
}
