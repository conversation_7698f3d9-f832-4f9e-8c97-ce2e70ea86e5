<?php

namespace SendSms\Contracts;

/**
 * 短信发送接口
 * Interface GatewayInterface
 * @package Storage\Contracts
 */
abstract class SendSmsInterface
{
    protected $code;
    protected $msg;

    /**
     * 发送短信
     * @param string $content 短信内容
     * @param string $mobile
     * @param string $sign_name 短信签名
     * @return mixed
     */
    abstract public function send(string $content, string $mobile, string $sign_name = '');

    /**
     * 计算短信条数
     * @param string $content 短信内容
     * @param string $sign_name 短信签名
     * @return integer
     */
    abstract public function calculate_fee(string $content, string $sign_name = '');

    /**
     * 接受短信报告
     * @param array $options 数据
     * @param string $type 类型
     * @return mixed
     */
    abstract public function callback(array $options, $type);

    /**
     * 转换短信内容
     * @param array|string $options 数据
     * @return mixed
     */
    abstract public function convert_content(string $options);

    public function getCode()
    {
        return $this->code;
    }

    public function getMsg()
    {
        return $this->msg;
    }
}
