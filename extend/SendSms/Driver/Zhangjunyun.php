<?php

namespace SendSms\Driver;

use app\model\SmsReplyNote;
use app\model\SmsSendNote;
use app\common\service\NotifyService;
use SendSms\Contracts\Config;
use SendSms\Contracts\Exception;
use SendSms\Contracts\SendSmsInterface;
use think\facade\Db;

/**
 * 掌骏云 https://sms.izjun.com
 * @package SendSms\Driver\Zhangjunyun
 */
class Zhangjunyun extends SendSmsInterface
{
    const  API_URL = "http://47.106.170.137:8001/sms";
    /**
     * 参数
     * @var Config
     */
    protected Config $config;

    /**
     * @param array $config
     * @throws \Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 计算短信条数
     * @param string $content
     * @param string $sign_name
     * @return float
     * @throws \Exception
     */
    public function calculate_fee(string $content, string $sign_name = '')
    {
        $signature  = '【' . $this->config->get('sign_name') . '】';
        $str_length = mb_strlen($content . $signature);
        return ceil($str_length / 67);
    }

    /**
     * 转换短信内容
     * @param string $options 短信内容
     * @return string
     * @throws \Exception
     */
    public function convert_content(string $options)
    {
        return str_replace('+', '_', $options);
    }

    /**
     * 发送短信
     * @param string $content
     * @param string $mobile
     * @param string $sign_name
     * @return array
     * @throws \Exception
     */
    public function send(string $content, string $mobile, string $sign_name = '')
    {
        $url                 = '/api/sendMessageOne'; //单发
        $data['messageList'] = [
            [
                'content' => '【' . ($sign_name ?: $this->config->get('sign_name')) . '】' . $content,
                'phone'   => $mobile,
            ]
        ];
        $result              = $this->request($url, $data);
        $out_id              = create_guid();
        if ($result === false) {
            $return = [
                'status'   => -1,
                'code'     => $this->code,
                'message'  => $this->msg,
                'response' => '',
            ];
        } else {
            $return = [
                'status'      => 0,
                'code'        => $result['code'],
                'out_id'      => $out_id,
                'message'     => '发送成功',
                'request_id'  => '',
                'out_send_id' => $result['data'][0]['msgId'],
                'fee'         => $result['smsCount'],
                'response'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        }
        return $return;
    }

    protected function request($url, array $data = [])
    {
        $url               = self::API_URL . $url;
        $microsecond       = get_microsecond();
        $data['sign']      = $this->generateSignature($microsecond);
        $data['userName']  = $this->config->get('username');
        $data['timestamp'] = $microsecond;
        $data              = json_encode($data);
        $result            = curl()->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    /**
     * @param string $microsecond 毫秒
     * @return string
     */
    protected function generateSignature($microsecond)
    {
        $username = $this->config->get('username');
        $password = $this->config->get('password');
        return md5($username . $microsecond . md5($password));
    }

    protected function getTime()
    {
        return get_microsecond();
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!is_array($result)) {
            $this->code = -1;
            $this->msg  = '结果解析失败';
            return false;
        }
        if ($result['code'] != 0) {
            $this->code = $result['code'];
            $this->msg  = $result['message'];
            return false;
        }
        return $result;
    }

    public function getBalance()
    {
        $url    = '/api/getBalance';
        $result = $this->request($url);
        if ($result === false) {
            $return = [
                'status'  => -1,
                'code'    => $this->code,
                'message' => $this->msg,
            ];
        } else {
            $return = [
                'status'  => 0,
                'code'    => $result['code'],
                'balance' => $result['balance'],
            ];
        }
        return $return;
    }

    public function batchReport()
    {
        throw new \Exception('暂时不支持该方法:' . __FUNCTION__);
    }

    public function batchReply()
    {
        throw new \Exception('暂时不支持该方法:' . __FUNCTION__);
    }

    /**
     * 短信报告 https://doc.zthysms.com/web/?#/1?page_id=34
     * @param array $options
     * @param string $type
     * @return integer
     * @throws \Exception
     */
    public function callback(array $options, $type)
    {
        try {
            switch ($type) {
                case 'sms_report': //短信报告
                    foreach ($options as $data) {
                        if (is_array($data) && isset($data['msgId'])) {
                            $db                = new SmsSendNote();
                            $map               = [
                                ['mobile', '=', $data['phone']],
                                ['out_send_id', '=', $data['msgId']],
                            ];
                            $receive_status    = ($data['status'] == 'DELIVRD') ? 1 : -1;
                            $receive_date_time = $data['receiveTime'];
                            $update_data       = [
                                'receive_code'      => $data['status'],
                                'receive_status'    => $receive_status,
                                'receive_msg'       => $data['status'],
                                'receive_date_time' => $receive_date_time,
                                'receive_seconds'   => Db::raw("TIME_TO_SEC(TIMEDIFF('$receive_date_time', create_time))"),
                            ];
                            $db::update($update_data, $map);
                        }
                    }
                    break;
                case 'reply_sms'://短信回复
                    foreach ($options as $data) {
                        if (is_array($data) && isset($data['msgId'])) {
                            $mobile     = $data['phone'];
                            $msg_id     = $data['msgId'];
                            $content    = $data['content'] ?? '';
                            $reply_time = $data['receiveTime'] ?? '';
//                            send_qy_wechat('收到' . $mobile . '短信回复:' . $content . ';消息ID:' . $msg_id . '回复时间:' . $reply_time);
                            $insert_data = [
                                'guid'       => create_guid(),
                                'mobile'     => $mobile,
                                'content'    => $content,
                                'msg_id'     => $msg_id,
                                'reply_time' => $reply_time,
                            ];
                            if ($msg_id) {
                                //查找bid 和send_note_guid
                                $db_sms_send_note = new SmsSendNote();
                                $before_time      = date('Y-m-d H:i:s', strtotime('-7 day')); //只处理最近3天数据
                                $map              = [
                                    ['mobile', '=', $mobile],
                                    ['out_send_id', '=', $msg_id],
                                    ['create_time', '>', $before_time],
                                ];
                                $send_note        = $db_sms_send_note->field(['guid', 'bid', 'content'])->where($map)->findOrEmpty();
                                $before_content   = null;
                                if (!$send_note->isEmpty()) {
                                    $before_content                = $send_note['content'];
                                    $insert_data['bid']            = $send_note['bid'];
                                    $insert_data['send_note_guid'] = $send_note['guid'];
                                }
                            }
                            $db_sms_reply_note = new SmsReplyNote();
                            $db_sms_reply_note->save($insert_data);
                            if (!empty($insert_data['bid']) && $before_content) {
                                //发送通知
                                $bid  = $insert_data['bid'];
                                $data = [
                                    'url'         => '',
                                    'title'       => '原内容:' . $before_content,
                                    'detail'      => '回复内容:' . $content,
                                    'user'        => '手机号:' . $mobile,
                                    'name'        => '收到客户短信回复',
                                    'remark'      => '如有疑问请联系客服',
                                    'create_time' => format_timestamp(),
                                ];
                                notify()->set_key_name(NotifyService::Notice)->limit_business()->set_data($data)->set_bid($bid)->send();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            return 0;
        } catch (Exception $e) {
            return -1;
        }
    }
}
