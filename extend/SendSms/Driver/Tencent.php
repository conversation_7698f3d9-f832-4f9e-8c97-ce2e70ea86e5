<?php

namespace SendSms\Driver;

use app\model\SmsSendNote;
use app\model\SmsTemplate;
use Exception;
use Qcloud\Sms\SmsMobileStatusPuller;
use Qcloud\Sms\SmsMultiSender;
use Qcloud\Sms\SmsSingleSender;
use Qcloud\Sms\SmsStatusPuller;
use Qcloud\Sms\SmsVoicePromptSender;
use Qcloud\Sms\SmsVoiceVerifyCodeSender;
use SendSms\Contracts\Config;
use SendSms\Contracts\SendSmsInterface;

/**
 * 腾讯云
 * @package Storage\Driver\Tencent
 */
class Tencent extends SendSmsInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;
    protected $appid;
    protected $appkey;

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
        $this->appid  = $this->config->get('appid');
        $this->appkey = $this->config->get('appkey');
    }

    /**
     * 计算短信条数
     * @param string $content
     * @param string $sign_name
     * @return integer
     * @throws Exception
     */
    public function calculate_fee(string $content, string $sign_name = '')
    {
        $signature  = '【' . ($sign_name ?: $this->config->get('sign_name')) . '】';
        $str        = $content . $signature;
        $str_length = mb_strlen($str);
        return ceil($str_length / 67);
    }

    /**
     * 转换短信内容
     * @param string $options 短信内容
     * @return string
     * @throws Exception
     */
    public function convert_content(string $options)
    {
        $sms_template_db = new SmsTemplate();
        $map             = [
            ['channel', '=', 'tencent'],
            ['type', '=', 'system_alarm']
        ];
        $template        = $sms_template_db->where($map)->find();
        $template        = $template['content'];
        $template        = str_replace('{1}', '0', $template);
        $content         = str_replace('{2}', $options, $template);
        //        $i               = 1;
        //        foreach ($options as $key => $val) {
        //            $content = str_replace('{' . $i . '}', $val, $content);
        //            $i++;
        //        }
        return $content;
    }

    /**
     * 接收回调
     * @param array $options
     * @param string $type
     * @return array
     * @throws Exception
     */
    public function callback(array $options, $type)
    {
        try {
            switch ($type) {
                case 'sms_report': //短信报告
                    foreach ($options as $key => $val) {
                        $db             = new SmsSendNote();
                        $map            = [
                            ['mobile', '=', $val['mobile']],
                            ['out_send_id', '=', $val['sid']],
                        ];
                        $receive_status = ($val['report_status'] == 'SUCCESS') ? 1 : -1;
                        $update_data    = [
                            'receive_code'      => $val['errmsg'],
                            'receive_status'    => $receive_status,
                            'receive_msg'       => $val['description'],
                            'receive_date_time' => $val['user_receive_time'],
                        ];
                        $db::update($update_data, $map);
                    }
                    break;
                case 'reply_sms'://短信回复
                    send_qy_wechat('收到' . $options['mobile'] . '短信回复:' . $options['text'] . ';发送于:' . format_timestamp($options['time']));
                    break;
                default:
                    break;
            }
            return ['result' => 0, 'errmsg' => 'OK'];
        } catch (Exception $e) {
            return ['result' => -1, 'errmsg' => $e->getMessage()];
        }
    }

    /**
     * 发送短信
     * @param string $content
     * @param string $mobile
     * @return mixed|boolean|array
     * @throws Exception
     */
    public function send(string $content, string $mobile, string $sign_name = '')
    {
        $result = $this->smsSingleSender($mobile, $content, $sign_name);
        if ($result === false) {
            $return = [
                'status'   => -1,
                'code'     => $this->code,
                'message'  => $this->msg,
                'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        } else {
            $return = [
                'status'      => 0,
                'code'        => $result['result'],
                'message'     => $result['errmsg'],
                'request_id'  => '',
                'out_id'      => '',
                'out_send_id' => $result['sid'],
                'fee'         => $result['fee'],//消耗短信条数
                'response'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        }
        return $return;
    }

    public function smsSingleSender($mobile, $msg)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->send(0, "86", $mobile, $msg, "", "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    //单发短信

    /**
     * 解析返回的结果
     * @param string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (!tools()::is_json($result)) {
            $this->code = 'result error';
            $this->msg  = '解析返回结果失败';
            return false;
        }
        $result = json_decode($result, true);
        if (isset($result['result']) && $result['result'] !== 0) {
            $this->code = $result['result'];
            $this->msg  = $result['errmsg'];
            return false;
        }
        return $result;
    }

    //群发短信

    public function smsMultiSender($mobile_arr, $msg)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->send(0, "86", $mobile_arr, $msg, "", "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    //指定模板ID群发短信
    public function smsSingleSenderByTemplateId($mobile_arr, $params, $template_id)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsSingleSender($this->appid, $this->appkey);
            $result = $sender->sendWithParam("86", $mobile_arr, $template_id, $params, $this->config->get('sign'), "", "");  // 签名参数未提供或者为空时，会使用默认签名发送短信
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    //指定模板ID群发短信
    public function smsMultiSenderByTemplateId($mobile_arr, $params, $template_id)
    {
        try {
            //{1}系统提醒您:{2},请及时处理!
            $sender = new SmsMultiSender($this->appid, $this->appkey);
            $result = $sender->sendWithParam("86", $mobile_arr, $template_id, $params, $this->config->get('sign'), "", "");  // 签名参数未提供或者为空时，会使用默认签名发送短信
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    // 发送语音通知
    public function smsVoicePromptSender($mobile, $msg)
    {
        try {
            $sender = new SmsVoicePromptSender($this->appid, $this->appkey);
            $result = $sender->send("86", $mobile, 2, $msg, 1, "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    // 发送语音验证码
    public function smsVoiceVerifyCodeSender($mobile, $code)
    {
        try {
            $sender = new SmsVoiceVerifyCodeSender($this->appid, $this->appkey);
            $result = $sender->send("86", $mobile, $code, 2, "");
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }


    // 拉取短信发送报告
    public function pullCallback($max = 10)
    {
        try {
            $puller = new SmsStatusPuller($this->appid, $this->appkey);
            $result = $puller->pullCallback($max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    // 拉取短信回执
    public function pullReply($max = 10)
    {
        try {
            $puller = new SmsStatusPuller($this->appid, $this->appkey);
            $result = $puller->pullReply($max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    // 拉取单个手机短信回执
    public function pullCallbackSingleMobile($mobile, $begin_time = null, $end_time = null, $max = 10)
    {
        try {
            $puller     = new SmsMobileStatusPuller($this->appid, $this->appkey);
            $begin_time = $begin_time ?: strtotime(date("Y-m-d"), time());
            $end_time   = $end_time ?: time();
            $result     = $puller->pullCallback("86", $mobile, $begin_time, $end_time, $max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }

    // 拉取单个手机回复状态
    public function pullReplySingleMobile($mobile, $begin_time = null, $end_time = null, $max = 10)
    {
        try {
            $puller     = new SmsMobileStatusPuller($this->appid, $this->appkey);
            $begin_time = $begin_time ?: strtotime(date("Y-m-d"), time());
            $end_time   = $end_time ?: time();
            $result     = $puller->pullReply("86", $mobile, $begin_time, $end_time, $max);
            return $this->_parseResult($result);
        } catch (Exception $e) {
            $this->code = -2;
            $this->msg  = $e->getMessage();
            return false;
        }
    }
}
