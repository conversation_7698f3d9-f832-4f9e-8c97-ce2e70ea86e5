<?php

namespace SendSms\Driver;

use Exception;
use OpenApi\Yky;
use SendSms\Contracts\Config;
use SendSms\Contracts\SendSmsInterface;

/**
 * 一卡易通道
 * @package SendSms\Driver\Yikayi
 */
class Yikayi extends SendSmsInterface
{

    /**
     * 参数
     * @var array
     */
    protected $config;

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 计算短信条数
     * @param string $content
     * @param string $sign_name
     * @return integer
     * @throws Exception
     */
    public function calculate_fee(string $content, string $sign_name = '')
    {
        $signature  = '【一卡易】';
        $str        = $content . $signature;
        $str_length = mb_strlen($str);
        return ceil($str_length / 67);
    }

    /**
     * 转换短信内容
     * @param string $options 短信内容
     * @return string
     * @throws Exception
     */
    public function convert_content(string $options)
    {
        return str_replace('+', '_', $options);
    }

    /**
     * 发送短信
     * @param string $content
     * @param string $mobile
     * @return mixed|boolean|array
     * @throws Exception
     */
    public function send(string $content, string $mobile, string $sign_name = '')
    {
        $sms       = new Yky($this->config);
        $post_data = [
            'mobile'  => $mobile,
            'content' => $content
        ];
        $result    = $sms->SendSms($post_data);
        $out_id    = create_guid();
        if ($result === false) {
            $return = [
                'status'   => -1,
                'code'     => $sms->status,
                'message'  => $sms->message,
                'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        } else {
            $return = [
                'status'      => 0,
                'code'        => $result['status'],
                'out_id'      => $out_id,
                'message'     => $result['message'],
                'request_id'  => '',
                'out_send_id' => '',
                'fee'         => 1,//TODO 后续要计算准确条数
                'response'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        }
        return $return;
    }

    /**
     * 短信报告
     * @param array $options
     * @param string $type
     * @return array
     * @throws Exception
     */
    public function callback(array $options, $type)
    {
        return ['code' => 0, 'msg' => '成功'];
    }
}
