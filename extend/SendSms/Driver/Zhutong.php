<?php

namespace SendSms\Driver;

use app\model\SmsReplyNote;
use app\model\SmsSendNote;
use app\common\service\NotifyService;
use SendSms\Contracts\Config;
use SendSms\Contracts\Exception;
use SendSms\Contracts\SendSmsInterface;
use think\facade\Db;

/**
 * 助通
 * @package SendSms\Driver\Zhutong
 */
class Zhutong extends SendSmsInterface
{

    const  API_URL = "http://hy.mix2.zthysms.com";
    /**
     * 参数
     * @var Config
     */
    protected Config $config;

    /**
     * @param array $config
     * @throws \Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 计算短信条数
     * @param string $content
     * @param string $sign_name
     * @return float
     * @throws \Exception
     */
    public function calculate_fee(string $content, string $sign_name = '')
    {
        $signature  = '【' . $this->config->get('sign_name') . '】';
        $str_length = mb_strlen($content . $signature);
        return ceil($str_length / 67);
    }

    /**
     * 转换短信内容
     * @param string $options 短信内容
     * @return string
     * @throws \Exception
     */
    public function convert_content(string $options)
    {
        return str_replace('+', '_', $options);
    }

    /**
     * 发送短信
     * @param string $content
     * @param string $mobile
     * @param string $sign_name
     * @return array
     * @throws \Exception
     */
    public function send(string $content, string $mobile, string $sign_name = '')
    {
        $url    = (strstr($mobile, ',')) ? '/sendSmsBatch.do' : '/sendSms.do'; //手机号包含逗号则走群发
        $data   = [
            'content' => $content . '【' . ($sign_name ?: $this->config->get('sign_name')) . '】',
            'mobile'  => $mobile,
        ];
        $result = $this->request($url, $data);
        $out_id = create_guid();
        if ($result === false) {
            $return = [
                'status'   => -1,
                'code'     => $this->code,
                'message'  => $this->msg,
                'response' => '',
            ];
        } else {
            $return = [
                'status'      => 0,
                'code'        => $result['status'],
                'out_id'      => $out_id,
                'message'     => '发送成功',
                'request_id'  => '',
                'out_send_id' => $result['message'],
                'fee'         => 1,//TODO 后续要计算准确条数
                'response'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
        }
        return $return;
    }

    protected function request($url, array $data = [])
    {
        $url              = self::API_URL . $url;
        $data['username'] = $this->config->get('username');
        $time_key         = $this->getTime();
        $data['password'] = md5(md5($this->config->get('password')) . $time_key);
        $data['tkey']     = $time_key;
        $result           = curl()->form_params()->post($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    protected function getTime()
    {
        return date('YmdHis');
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (strstr($result, ',')) {
            $arr    = explode(',', $result);
            $result = [
                'status'  => intval($arr[0]),
                'message' => $arr[1],
            ];
        } elseif (is_string($result)) {
            //余额查询接口
            $result = [
                'status'  => 1,
                'message' => intval($result),
            ];
        } else {
            $this->code = -1;
            $this->msg  = '结果解析失败';
            return false;
        }
        if ($result['status'] != 1) {
            $this->code = $result['status'];
            $this->msg  = $result['message'];
            return false;
        }
        return $result;
    }

    public function getBalance()
    {
        $url = '/balance.do';
        return $this->request($url);
    }

    public function batchReport()
    {
        $url = '/batchreport.do';
        return $this->request($url);
    }

    public function batchReply()
    {
        $url = '/batchreply.do';
        return $this->request($url);
    }

    /**
     * 短信报告 https://doc.zthysms.com/web/?#/1?page_id=34
     * @param array $options
     * @param string $type
     * @return integer
     * @throws \Exception
     */
    public function callback(array $options, $type)
    {
        try {
            switch ($type) {
                case 'sms_report': //短信报告
                    foreach ($options as $data) {
                        if (is_array($data) && isset($data['msgId'])) {
                            $db                = new SmsSendNote();
                            $map               = [
                                ['mobile', '=', $data['mobile']],
                                ['out_send_id', '=', $data['msgId']],
                            ];
                            $receive_status    = ($data['code'] == 'DELIVRD') ? 1 : -1;
                            $receive_date_time = $data['reportTime'];
                            $update_data       = [
                                'receive_code'      => $data['code'],
                                'receive_status'    => $receive_status,
                                'receive_msg'       => $data['msg'],
                                'receive_date_time' => $receive_date_time,
                                'receive_seconds'   => Db::raw("TIME_TO_SEC(TIMEDIFF('$receive_date_time', create_time))"),
                            ];
                            $db::update($update_data, $map);
                        }
                    }
                    break;
                case 'reply_sms'://短信回复
                    foreach ($options as $data) {
                        if (is_array($data) && isset($data['msgId'])) {
                            $mobile     = $data['mobile'];
                            $msg_id     = $data['msgId'];
                            $content    = $data['content'] ?? '';
                            $reply_time = $data['createTime'] ?? '';
//                            send_qy_wechat('收到' . $mobile . '短信回复:' . $content . ';消息ID:' . $msg_id . '回复时间:' . $reply_time);
                            $insert_data = [
                                'guid'       => create_guid(),
                                'mobile'     => $mobile,
                                'content'    => $content,
                                'msg_id'     => $msg_id,
                                'reply_time' => $reply_time,
                            ];
                            if ($msg_id) {
                                //查找bid 和send_note_guid
                                $db_sms_send_note = new SmsSendNote();
                                $before_time      = date('Y-m-d H:i:s', strtotime('-7 day')); //只处理最近3天数据
                                $map              = [
                                    ['mobile', '=', $mobile],
                                    ['out_send_id', '=', $msg_id],
                                    ['create_time', '>', $before_time],
                                ];
                                $send_note        = $db_sms_send_note->field(['guid', 'bid', 'content'])->where($map)->findOrEmpty();
                                $before_content   = null;
                                if (!$send_note->isEmpty()) {
                                    $before_content                = $send_note['content'];
                                    $insert_data['bid']            = $send_note['bid'];
                                    $insert_data['send_note_guid'] = $send_note['guid'];
                                }
                            }
                            $db_sms_reply_note = new SmsReplyNote();
                            $db_sms_reply_note->save($insert_data);
                            if (!empty($insert_data['bid']) && $before_content) {
                                //发送通知
                                $bid  = $insert_data['bid'];
                                $data = [
                                    'url'         => '',
                                    'title'       => '原内容:' . $before_content,
                                    'detail'      => '回复内容:' . $content,
                                    'user'        => '手机号:' . $mobile,
                                    'name'        => '收到客户短信回复',
                                    'remark'      => '如有疑问请联系客服',
                                    'create_time' => format_timestamp(),
                                ];
                                notify()->set_key_name(NotifyService::Notice)->limit_business()->set_data($data)->set_bid($bid)->send();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            return 0;
        } catch (Exception $e) {
            return -1;
        }
    }
}
