<?php

namespace SendSms;

use SendSms\Contracts\Exception;
use SendSms\Contracts\SendSmsInterface;
use SendSms\Driver\Aliyun;
use SendSms\Driver\Tencent;
use SendSms\Driver\Yikayi;
use SendSms\Driver\Zhutong;

/**
 * Class SendSms
 * @method <PERSON><PERSON> aliyun($config) static 阿里云短信
 * @method Tencent tencent($config) static 腾讯云短信
 * @method Yikayi yikayi($config) static 一卡易短信
 * @method Zhutong zhutong($config) static 助通短信
 *
 * @package SendSms
 */
class SendSms
{
    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return SendSmsInterface
     * @throws \Exception
     */
    public static function __callStatic(string $name, array $arguments)
    {
        return self::driver($name, reset($arguments));
    }

    /**
     * 指定驱动器
     * @param string $driver 短信驱动
     * @param array $config 驱动配置
     * @return SendSmsInterface
     * @throws \Exception
     */
    public static function driver(string $driver, array $config = [])
    {
        if (!file_exists(__DIR__ . '/Driver/' . ucfirst($driver) . '.php')) {
            throw new Exception("Driver [$driver] is not supported.");
        }
        $gateway = __NAMESPACE__ . '\\Driver\\' . ucfirst($driver);
        return new $gateway($config);
    }
}
