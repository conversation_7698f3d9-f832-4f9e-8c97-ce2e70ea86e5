<?php

namespace Erp\Contracts;

/**
 * Interface GatewayInterface
 * @package Erp\Contracts
 */
abstract class ErpInterface
{
    protected $code;
    protected $msg;
    protected bool $success = false; //业务是否处理成功
    public bool $_retry = false;//接口地址

    protected Config $config;

    /**
     * 添加商品
     * @param array $data 商品
     * @return mixed
     */
    abstract public function add_goods(array $data);


    public function getCode()
    {
        return $this->code;
    }

    public function getMsg()
    {
        return $this->msg;
    }

    public function isSuccess(): bool
    {
        return $this->success === true;
    }
}
