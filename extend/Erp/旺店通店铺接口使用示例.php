<?php

/**
 * 旺店通店铺接口使用示例
 * 
 * 本示例展示如何使用旺店通ERP的店铺查询接口
 * API文档：https://open.wangdian.cn/qyb/open/apidoc/doc?path=shop.php
 */

require_once __DIR__ . '/Erp.php';

use Erp\Erp;

try {
    // 配置参数
    $config = [
        'app_key' => 'your_app_key',        // 接口账号（从开放平台获取）
        'app_secret' => 'your_app_secret',  // 接口密钥（从开放平台获取）
        'sid' => 'your_sid',                // 卖家账号（ERP购买方账号）
        'sandbox' => true,                  // 使用测试环境，正式环境设为false
    ];

    // 创建旺店通实例
    $wdt = Erp::wangdiantong($config);

    echo "=== 旺店通店铺接口使用示例 ===\n\n";

    // 示例1：获取所有店铺（分页）
    echo "1. 获取所有店铺（第1页，每页30条）：\n";
    $result = $wdt->getAllShops(30, 0);
    if ($wdt->isSuccess()) {
        echo "成功获取店铺列表：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            foreach ($result['shoplist'] as $shop) {
                echo "- 店铺：{$shop['shop_name']} (编号：{$shop['shop_no']}, 平台ID：{$shop['platform_id']})\n";
            }
        }
    } else {
        echo "获取失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例2：根据店铺编号查询单个店铺
    echo "2. 根据店铺编号查询单个店铺：\n";
    $shopNo = 'api_test'; // 替换为实际的店铺编号
    $result = $wdt->queryShopByNo($shopNo);
    if ($wdt->isSuccess()) {
        echo "成功查询店铺：\n";
        if (!empty($result['shoplist'])) {
            $shop = $result['shoplist'][0];
            echo "店铺名称：{$shop['shop_name']}\n";
            echo "店铺编号：{$shop['shop_no']}\n";
            echo "平台ID：{$shop['platform_id']}\n";
            echo "联系人：{$shop['contact']}\n";
            echo "手机：{$shop['mobile']}\n";
            echo "地址：{$shop['province_name']} {$shop['city_name']} {$shop['district_name']} {$shop['address']}\n";
            echo "授权状态：" . getAuthStatusText($shop['auth_state']) . "\n";
            echo "是否停用：" . ($shop['is_disabled'] == '1' ? '是' : '否') . "\n";
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例3：根据平台ID查询店铺
    echo "3. 根据平台ID查询店铺（平台ID：127）：\n";
    $result = $wdt->queryShopsByPlatform(127, 10, 0);
    if ($wdt->isSuccess()) {
        echo "成功查询平台店铺：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            foreach ($result['shoplist'] as $shop) {
                echo "- {$shop['shop_name']} (编号：{$shop['shop_no']})\n";
            }
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例4：批量查询多个平台的店铺
    echo "4. 批量查询多个平台的店铺（平台ID：1,2,127）：\n";
    $result = $wdt->queryShopsByPlatforms([1, 2, 127], 20, 0);
    if ($wdt->isSuccess()) {
        echo "成功批量查询店铺：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            $platformGroups = [];
            foreach ($result['shoplist'] as $shop) {
                $platformGroups[$shop['platform_id']][] = $shop;
            }
            foreach ($platformGroups as $platformId => $shops) {
                echo "平台ID {$platformId}：\n";
                foreach ($shops as $shop) {
                    echo "  - {$shop['shop_name']} (编号：{$shop['shop_no']})\n";
                }
            }
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例5：查询启用的店铺
    echo "5. 查询启用的店铺：\n";
    $result = $wdt->queryShopsByStatus(0, 50, 0); // 0表示未停用
    if ($wdt->isSuccess()) {
        echo "成功查询启用店铺：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            foreach ($result['shoplist'] as $shop) {
                $authStatus = getAuthStatusText($shop['auth_state']);
                echo "- {$shop['shop_name']} (编号：{$shop['shop_no']}, 授权状态：{$authStatus})\n";
            }
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例6：查询停用的店铺
    echo "6. 查询停用的店铺：\n";
    $result = $wdt->queryShopsByStatus(1, 50, 0); // 1表示停用
    if ($wdt->isSuccess()) {
        echo "成功查询停用店铺：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            foreach ($result['shoplist'] as $shop) {
                echo "- {$shop['shop_name']} (编号：{$shop['shop_no']})\n";
            }
        } else {
            echo "没有停用的店铺\n";
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }
    echo "\n";

    // 示例7：自定义参数查询
    echo "7. 自定义参数查询（指定多个条件）：\n";
    $customParams = [
        'platform_id' => 127,      // 指定平台ID
        'is_disabled' => 0,        // 只查询启用的店铺
        'page_size' => 10,         // 每页10条
        'page_no' => 0             // 第1页
    ];
    $result = $wdt->queryShops($customParams);
    if ($wdt->isSuccess()) {
        echo "成功自定义查询：\n";
        echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
        if (!empty($result['shoplist'])) {
            foreach ($result['shoplist'] as $shop) {
                echo "- {$shop['shop_name']} (编号：{$shop['shop_no']}, 联系人：{$shop['contact']})\n";
            }
        }
    } else {
        echo "查询失败：" . $wdt->getMsg() . "\n";
    }

} catch (Exception $e) {
    echo "异常错误：" . $e->getMessage() . "\n";
}

/**
 * 获取授权状态文本
 * @param string $authState 授权状态码
 * @return string 授权状态文本
 */
function getAuthStatusText($authState)
{
    $statusMap = [
        '0' => '未授权',
        '1' => '已授权',
        '2' => '授权失效',
        '3' => '授权停用'
    ];
    
    return $statusMap[$authState] ?? '未知状态';
}

/**
 * 店铺数据结构说明：
 * 
 * shoplist 数组中每个店铺包含以下字段：
 * - platform_id: 平台ID
 * - sub_platform_id: 子平台ID
 * - shop_id: 店铺ID
 * - shop_no: 店铺编号
 * - shop_name: 店铺名称
 * - account_id: 平台授权账号ID
 * - account_nick: 平台账号昵称
 * - province: 省份ID
 * - city: 城市ID
 * - district: 区县ID
 * - province_name: 省份名称
 * - city_name: 城市名称
 * - district_name: 区县名称
 * - address: 详细地址
 * - contact: 联系人
 * - zip: 邮编
 * - mobile: 手机号
 * - telno: 固定电话
 * - remark: 备注
 * - pay_account_id: 账款账号
 * - auth_state: 授权状态（0未授权 1已授权 2授权失效 3授权停用）
 * - push_rds_id: 是否启用推送
 * - pay_auth_state: 支付宝授权状态
 * - auth_time: 授权时间
 * - expire_time: session过期时间
 * - re_expire_time: refresh_token过期时间
 * - freeze_auth_to: 冻结授权时间
 * - is_disabled: 是否停用（0未停用 1停用）
 * - modified: 最后修改时间
 * - created: 创建时间
 * - 其他扩展字段...
 */
