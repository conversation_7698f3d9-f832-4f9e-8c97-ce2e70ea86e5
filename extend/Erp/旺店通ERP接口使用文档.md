# 旺店通 ERP 接口使用文档

## 📋 目录

- [功能介绍](#功能介绍)
- [配置说明](#配置说明)
- [API 接口列表](#api接口列表)
- [详细使用示例](#详细使用示例)
- [错误处理](#错误处理)
- [常见问题](#常见问题)
- [技术支持](#技术支持)

## 🎯 功能介绍

旺店通 ERP 接口驱动是一个高度优化的 PHP SDK，采用极简设计理念，提供完整的电商订单管理功能。

### 核心特性

- ✅ **极简 API 设计**：所有业务方法只需一行代码调用
- ✅ **通用参数处理**：支持传入任意业务参数，无需硬编码字段
- ✅ **完整订单生命周期**：创建、查询、发货、取消、退款
- ✅ **物流跟踪支持**：发货通知、物流状态查询、物流公司管理
- ✅ **库存管理**：实时库存查询和同步
- ✅ **批量操作**：支持批量查询订单状态
- ✅ **环境切换**：支持测试环境和生产环境切换
- ✅ **错误处理**：完善的错误处理和重试机制

## ⚙️ 配置说明

### 基础配置参数

| 参数名         | 类型    | 必需 | 说明             | 示例值                  |
| -------------- | ------- | ---- | ---------------- | ----------------------- |
| `app_key`      | string  | ✅   | 应用密钥         | `your_app_key`          |
| `app_secret`   | string  | ✅   | 应用秘钥         | `your_app_secret`       |
| `sid`          | string  | ✅   | 卖家账号（店铺API必需） | `your_sid`        |
| `access_token` | string  | ❌   | 访问令牌         | `your_access_token`     |
| `sandbox`      | boolean | ❌   | 是否使用测试环境 | `false`（默认正式环境） |

### 环境配置

```php
// 正式环境配置
$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'sid' => 'your_sid', // 店铺API必需
    'access_token' => 'your_access_token',
    'sandbox' => false, // 使用正式环境
];

// 测试环境配置
$config = [
    'app_key' => 'test_app_key',
    'app_secret' => 'test_app_secret',
    'access_token' => 'test_access_token',
    'sandbox' => true, // 使用测试环境
];
```

## 📚 API 接口列表

### 订单管理接口

| 方法名                | 功能说明     | API 接口                  | 必需参数                                                               |
| --------------------- | ------------ | ------------------------- | ---------------------------------------------------------------------- |
| `createOrder()`       | 创建订单     | `wdt.trade.push`          | trade_no, buyer_nick, receiver_name, receiver_mobile, receiver_address |
| `queryOrder()`        | 查询订单状态 | `wdt.trade.query`         | trade_no                                                               |
| `cancelOrder()`       | 取消订单     | `wdt.trade.cancel`        | trade_no                                                               |
| `batchQueryOrders()`  | 批量查询订单 | `wdt.trade.batch.query`   | trade_nos                                                              |
| `updateTradeStatus()` | 更新订单状态 | `wdt.trade.status.update` | trade_no, status                                                       |

### 物流管理接口

| 方法名                    | 功能说明         | API 接口                      | 必需参数                               |
| ------------------------- | ---------------- | ----------------------------- | -------------------------------------- |
| `pushLogistics()`         | 发货通知         | `wdt.logistics.sync.push`     | trade_no, logistics_no, logistics_name |
| `queryLogistics()`        | 查询物流状态     | `wdt.logistics.sync.query`    | trade_no                               |
| `getLogisticsCompanies()` | 获取物流公司列表 | `wdt.logistics.companies.get` | 无                                     |

### 库存管理接口

| 方法名         | 功能说明 | API 接口          | 必需参数  |
| -------------- | -------- | ----------------- | --------- |
| `queryStock()` | 查询库存 | `wdt.stock.query` | goods_nos |

### 售后管理接口

| 方法名          | 功能说明     | API 接口           | 必需参数                               |
| --------------- | ------------ | ------------------ | -------------------------------------- |
| `pushRefund()`  | 申请退款     | `wdt.refund.push`  | trade_no, refund_amount, refund_reason |
| `queryRefund()` | 查询退款状态 | `wdt.refund.query` | trade_no                               |

### 店铺管理接口

| 方法名                     | 功能说明           | API 接口  | 必需参数 |
| -------------------------- | ------------------ | --------- | -------- |
| `queryShops()`             | 查询店铺信息       | `shop.php` | 无       |
| `queryShopByNo()`          | 根据编号查询店铺   | `shop.php` | shop_no  |
| `queryShopsByPlatform()`   | 根据平台ID查询店铺 | `shop.php` | platform_id |
| `queryShopsByPlatforms()`  | 批量查询多平台店铺 | `shop.php` | platform_ids |
| `queryShopsByStatus()`     | 根据状态查询店铺   | `shop.php` | is_disabled |
| `getAllShops()`            | 获取所有店铺       | `shop.php` | 无       |

### 通用接口

| 方法名      | 功能说明          | 参数说明                                |
| ----------- | ----------------- | --------------------------------------- |
| `callApi()` | 通用 API 调用方法 | method, apiMethod, data, requiredFields |

## 🚀 详细使用示例

### 1. 基础使用

```php
use Erp\Erp;

// 创建旺店通实例
$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'access_token' => 'your_access_token',
    'sandbox' => false,
];

$wdt = Erp::wangdiantong($config);
```

### 2. 创建订单 - createOrder()

**方法签名：** `createOrder(array $orderData)`

**必需参数：**

- `trade_no` - 订单号
- `buyer_nick` - 买家昵称
- `receiver_name` - 收货人姓名
- `receiver_mobile` - 收货人手机
- `receiver_address` - 收货地址

**完整参数示例：**

```php
$orderData = [
    // === 必需参数 ===
    'trade_no' => 'ORDER20240101001',           // 订单号
    'buyer_nick' => '张三',                     // 买家昵称
    'receiver_name' => '李四',                  // 收货人姓名
    'receiver_mobile' => '13800138000',         // 收货人手机
    'receiver_address' => '北京市朝阳区xxx街道xxx号', // 收货地址

    // === 收货人信息（可选） ===
    'receiver_phone' => '010-12345678',         // 收货人电话
    'receiver_zip' => '100000',                 // 邮编
    'receiver_province' => '北京市',            // 省份
    'receiver_city' => '北京市',                // 城市
    'receiver_district' => '朝阳区',            // 区县

    // === 订单时间信息（可选） ===
    'trade_time' => '2024-01-01 10:00:00',     // 订单时间
    'pay_time' => '2024-01-01 10:05:00',       // 付款时间
    'modified' => '2024-01-01 10:06:00',       // 修改时间

    // === 订单金额信息（可选） ===
    'total_amount' => 299.00,                   // 订单总金额
    'payment' => 289.00,                        // 实付金额
    'post_fee' => 10.00,                        // 邮费
    'cod_fee' => 0.00,                          // 货到付款费用
    'discount_fee' => 10.00,                    // 优惠金额
    'adjust_fee' => 0.00,                       // 手工调整金额

    // === 订单备注信息（可选） ===
    'buyer_message' => '请尽快发货，谢谢！',    // 买家留言
    'seller_memo' => 'VIP客户，优先处理',       // 卖家备注
    'seller_flag' => 1,                         // 卖家标记

    // === 平台信息（可选） ===
    'platform' => 'taobao',                     // 平台来源：taobao, tmall, jd等
    'shop_no' => 'SHOP001',                     // 店铺编号
    'warehouse_no' => 'WH001',                  // 仓库编号
    'tid' => 'TID123456789',                    // 平台订单号

    // === 物流信息（可选） ===
    'logistics_no' => 'SF1234567890',          // 物流单号
    'logistics_name' => '顺丰速运',             // 物流公司
    'logistics_code' => 'SF',                   // 物流公司编码

    // === 发票信息（可选） ===
    'invoice_type' => 1,                        // 发票类型：1-普通发票，2-增值税发票
    'invoice_title' => '北京某某公司',          // 发票抬头
    'invoice_content' => '办公用品',            // 发票内容

    // === 其他信息（可选） ===
    'trade_type' => 1,                          // 订单类型：1-普通订单，2-预售订单
    'trade_status' => 'WAIT_SELLER_SEND_GOODS', // 订单状态
    'is_cod' => 0,                              // 是否货到付款：0-否，1-是
    'step_paid_fee' => 0.00,                    // 定金金额
    'step_trade_status' => '',                  // 分阶段订单状态
    'consign_time' => '',                       // 发货时间
    'end_time' => '',                           // 交易结束时间

    // === 订单商品明细 ===
    'details' => [
        [
            'goods_no' => 'GOODS001',           // 商品编号
            'goods_name' => 'iPhone 15 Pro',   // 商品名称
            'spec_no' => 'SPEC001',             // 规格编号
            'spec_name' => '深空黑 256GB',      // 规格名称
            'num' => 1,                         // 数量
            'price' => 299.00,                  // 单价
            'total_amount' => 299.00,           // 总金额
            'oid' => 'OID001',                  // 子订单号
            'goods_type' => 1,                  // 商品类型
            'refund_status' => 'NO_REFUND',     // 退款状态
            'outer_goods_id' => 'OUTER001',     // 外部商品ID
            'outer_spec_id' => 'OUTER_SPEC001', // 外部规格ID
            'pic_path' => 'https://example.com/pic.jpg', // 商品图片
        ],
        [
            'goods_no' => 'GOODS002',
            'goods_name' => '手机壳',
            'spec_no' => 'SPEC002',
            'spec_name' => '透明硅胶壳',
            'num' => 2,
            'price' => 29.00,
            'total_amount' => 58.00,
            'oid' => 'OID002',
        ]
    ]
];

$result = $wdt->createOrder($orderData);

if ($wdt->isSuccess()) {
    echo "✅ 订单创建成功\n";
    echo "订单号：" . $orderData['trade_no'] . "\n";
    print_r($result);
} else {
    echo "❌ 订单创建失败：" . $wdt->getMsg() . "\n";
    echo "错误码：" . $wdt->getCode() . "\n";
}
```

### 3. 查询订单 - queryOrder()

**方法签名：** `queryOrder(string $tradeNo)`

```php
// 查询单个订单
$result = $wdt->queryOrder('ORDER20240101001');

if ($wdt->isSuccess()) {
    echo "✅ 订单查询成功\n";
    $orderInfo = $result['data'];

    echo "订单号：" . $orderInfo['trade_no'] . "\n";
    echo "订单状态：" . $orderInfo['status'] . "\n";
    echo "订单金额：" . $orderInfo['total_amount'] . "\n";
    echo "买家昵称：" . $orderInfo['buyer_nick'] . "\n";
    echo "收货人：" . $orderInfo['receiver_name'] . "\n";
    echo "收货地址：" . $orderInfo['receiver_address'] . "\n";

    // 显示商品明细
    if (isset($orderInfo['details'])) {
        echo "商品明细：\n";
        foreach ($orderInfo['details'] as $item) {
            echo "  - {$item['goods_name']} x{$item['num']} = ¥{$item['total_amount']}\n";
        }
    }
} else {
    echo "❌ 订单查询失败：" . $wdt->getMsg() . "\n";
}
```

### 4. 批量查询订单 - batchQueryOrders()

**方法签名：** `batchQueryOrders(array $tradeNos)`

```php
$orderNos = ['ORDER20240101001', 'ORDER20240101002', 'ORDER20240101003'];
$result = $wdt->batchQueryOrders($orderNos);

if ($wdt->isSuccess()) {
    echo "✅ 批量查询成功，共查询到 " . count($result['data']) . " 个订单\n";

    foreach ($result['data'] as $order) {
        echo "订单号：{$order['trade_no']}\n";
        echo "  状态：{$order['status']}\n";
        echo "  金额：¥{$order['total_amount']}\n";
        echo "  买家：{$order['buyer_nick']}\n";
        echo "  收货人：{$order['receiver_name']}\n";
        echo "  ----\n";
    }
} else {
    echo "❌ 批量查询失败：" . $wdt->getMsg() . "\n";
}
```

### 5. 发货通知 - pushLogistics()

**方法签名：** `pushLogistics(array $data)`

**必需参数：**

- `trade_no` - 订单号
- `logistics_no` - 物流单号
- `logistics_name` - 物流公司名称

**完整参数示例：**

```php
$logisticsData = [
    // === 必需参数 ===
    'trade_no' => 'ORDER20240101001',           // 订单号
    'logistics_no' => 'SF1234567890123',        // 物流单号
    'logistics_name' => '顺丰速运',             // 物流公司名称

    // === 可选参数 ===
    'logistics_code' => 'SF',                   // 物流公司编码
    'ship_time' => '2024-01-01 15:30:00',      // 发货时间
    'remark' => '已发货，请注意查收',           // 备注信息

    // === 发件人信息（可选） ===
    'sender_name' => '发货仓库',                // 发件人姓名
    'sender_mobile' => '400-1234567',           // 发件人手机
    'sender_phone' => '010-12345678',           // 发件人电话
    'sender_address' => '北京市顺义区仓储中心A区', // 发件人地址
    'sender_zip' => '101300',                   // 发件人邮编

    // === 包裹信息（可选） ===
    'package_weight' => 0.5,                   // 包裹重量（kg）
    'package_volume' => 0.01,                  // 包裹体积（m³）
    'package_count' => 1,                      // 包裹数量
    'insured_value' => 299.00,                 // 保价金额

    // === 其他信息（可选） ===
    'delivery_type' => 1,                      // 配送方式：1-标准快递，2-次日达
    'pay_type' => 1,                           // 付款方式：1-寄付，2-到付
    'is_urgent' => 0,                          // 是否加急：0-否，1-是
];

$result = $wdt->pushLogistics($logisticsData);

if ($wdt->isSuccess()) {
    echo "✅ 发货通知成功\n";
    echo "订单号：" . $logisticsData['trade_no'] . "\n";
    echo "物流单号：" . $logisticsData['logistics_no'] . "\n";
    echo "物流公司：" . $logisticsData['logistics_name'] . "\n";
} else {
    echo "❌ 发货通知失败：" . $wdt->getMsg() . "\n";
}
```

### 6. 查询物流状态 - queryLogistics()

**方法签名：** `queryLogistics(string $tradeNo, string $logisticsNo = '')`

```php
// 方式1：根据订单号查询物流
$result = $wdt->queryLogistics('ORDER20240101001');

if ($wdt->isSuccess()) {
    echo "✅ 物流查询成功\n";
    $logisticsInfo = $result['data'];

    echo "订单号：" . $logisticsInfo['trade_no'] . "\n";
    echo "物流单号：" . $logisticsInfo['logistics_no'] . "\n";
    echo "物流公司：" . $logisticsInfo['logistics_name'] . "\n";
    echo "物流状态：" . $logisticsInfo['status'] . "\n";
    echo "最新轨迹：" . $logisticsInfo['latest_trace'] . "\n";

    // 显示完整物流轨迹
    if (isset($logisticsInfo['traces']) && is_array($logisticsInfo['traces'])) {
        echo "物流轨迹：\n";
        foreach ($logisticsInfo['traces'] as $trace) {
            echo "  {$trace['time']} - {$trace['desc']}\n";
        }
    }
} else {
    echo "❌ 物流查询失败：" . $wdt->getMsg() . "\n";
}

// 方式2：根据订单号和物流单号查询
$result = $wdt->queryLogistics('ORDER20240101001', 'SF1234567890123');
```

### 7. 申请退款 - pushRefund()

**方法签名：** `pushRefund(array $data)`

**必需参数：**

- `trade_no` - 订单号
- `refund_amount` - 退款金额
- `refund_reason` - 退款原因

**完整参数示例：**

```php
$refundData = [
    // === 必需参数 ===
    'trade_no' => 'ORDER20240101001',           // 订单号
    'refund_amount' => 299.00,                  // 退款金额
    'refund_reason' => '商品质量问题',          // 退款原因

    // === 基本信息（可选） ===
    'refund_no' => 'REFUND20240101001',         // 退款单号
    'refund_type' => 2,                         // 退款类型：1-仅退款，2-退货退款
    'goods_status' => 2,                        // 货物状态：1-未收到货，2-已收到货
    'refund_desc' => '商品存在质量缺陷，包装破损，申请退货退款', // 退款描述

    // === 凭证信息（可选） ===
    'evidence_pics' => 'pic1.jpg,pic2.jpg,pic3.jpg', // 凭证图片，多个用逗号分隔
    'evidence_video' => 'video1.mp4',          // 凭证视频

    // === 退货信息（可选） ===
    'return_logistics_no' => 'YTO9876543210',  // 退货物流单号
    'return_logistics_name' => '圆通速递',     // 退货物流公司
    'return_logistics_code' => 'YTO',          // 退货物流公司编码

    // === 联系信息（可选） ===
    'contact_name' => '张三',                   // 联系人姓名
    'contact_mobile' => '13800138000',          // 联系人手机
    'contact_address' => '北京市朝阳区xxx',     // 联系地址

    // === 其他信息（可选） ===
    'refund_fee' => 289.00,                     // 实际退款金额
    'oid' => 'OID001',                          // 子订单号（退部分商品时使用）
    'num' => 1,                                 // 退货数量
    'timeout' => '2024-01-08 23:59:59',        // 超时时间
];

$result = $wdt->pushRefund($refundData);

if ($wdt->isSuccess()) {
    echo "✅ 退款申请成功\n";
    echo "订单号：" . $refundData['trade_no'] . "\n";
    echo "退款金额：¥" . $refundData['refund_amount'] . "\n";
    echo "退款原因：" . $refundData['refund_reason'] . "\n";

    if (isset($result['data']['refund_no'])) {
        echo "退款单号：" . $result['data']['refund_no'] . "\n";
    }
} else {
    echo "❌ 退款申请失败：" . $wdt->getMsg() . "\n";
}
```

### 8. 查询退款状态 - queryRefund()

**方法签名：** `queryRefund(string $tradeNo, string $refundNo = '')`

```php
// 方式1：根据订单号查询退款
$result = $wdt->queryRefund('ORDER20240101001');

if ($wdt->isSuccess()) {
    echo "✅ 退款查询成功\n";
    $refundInfo = $result['data'];

    echo "订单号：" . $refundInfo['trade_no'] . "\n";
    echo "退款单号：" . $refundInfo['refund_no'] . "\n";
    echo "退款状态：" . $refundInfo['refund_status'] . "\n";
    echo "退款金额：¥" . $refundInfo['refund_amount'] . "\n";
    echo "退款原因：" . $refundInfo['refund_reason'] . "\n";
    echo "申请时间：" . $refundInfo['created'] . "\n";

    if (isset($refundInfo['refund_time'])) {
        echo "退款时间：" . $refundInfo['refund_time'] . "\n";
    }
} else {
    echo "❌ 退款查询失败：" . $wdt->getMsg() . "\n";
}

// 方式2：根据订单号和退款单号查询
$result = $wdt->queryRefund('ORDER20240101001', 'REFUND20240101001');
```

### 9. 查询库存 - queryStock()

**方法签名：** `queryStock(array $goodsNos)`

```php
$goodsNos = ['GOODS001', 'GOODS002', 'GOODS003'];
$result = $wdt->queryStock($goodsNos);

if ($wdt->isSuccess()) {
    echo "✅ 库存查询成功\n";

    foreach ($result['data'] as $stock) {
        echo "商品编号：{$stock['goods_no']}\n";
        echo "  商品名称：{$stock['goods_name']}\n";
        echo "  可用库存：{$stock['stock_num']}\n";
        echo "  锁定库存：{$stock['lock_num']}\n";
        echo "  总库存：{$stock['total_num']}\n";
        echo "  ----\n";
    }
} else {
    echo "❌ 库存查询失败：" . $wdt->getMsg() . "\n";
}
```

### 10. 其他方法示例

```php
// 取消订单
$result = $wdt->cancelOrder('ORDER20240101001', '买家要求取消');

// 更新订单状态
$result = $wdt->updateTradeStatus('ORDER20240101001', 'shipped', '订单已发货');

// 获取物流公司列表
$result = $wdt->getLogisticsCompanies();
if ($wdt->isSuccess()) {
    foreach ($result['data'] as $company) {
        echo $company['code'] . ' - ' . $company['name'] . "\n";
    }
}
```

### 11. 通用 API 调用 - callApi()

**方法签名：** `callApi(string $method, string $apiMethod, array $data = [], array $requiredFields = [])`

```php
// 使用通用方法调用任意API
$result = $wdt->callApi(
    'custom_api.php',                    // API方法文件名
    'wdt.custom.method',                 // API接口方法名
    [                                    // 业务数据
        'param1' => 'value1',
        'param2' => 'value2',
        'custom_field' => 'custom_value',
    ],
    ['param1', 'param2']                 // 必需字段
);

if ($wdt->isSuccess()) {
    echo "✅ 自定义API调用成功\n";
    print_r($result);
} else {
    echo "❌ 自定义API调用失败：" . $wdt->getMsg() . "\n";
}
```

## 🔧 完整业务流程示例

```php
use Erp\Erp;

try {
    // 1. 初始化旺店通实例
    $wdt = Erp::wangdiantong([
        'app_key' => 'your_app_key',
        'app_secret' => 'your_app_secret',
        'access_token' => 'your_access_token',
        'sandbox' => false,
    ]);

    echo "🚀 开始完整订单流程演示\n\n";

    // 2. 创建订单
    $orderData = [
        'trade_no' => 'ORDER' . date('YmdHis'),
        'buyer_nick' => '测试买家',
        'receiver_name' => '张三',
        'receiver_mobile' => '13800138000',
        'receiver_address' => '北京市朝阳区测试地址123号',
        'total_amount' => 199.00,
        'payment' => 189.00,
        'post_fee' => 10.00,
        'buyer_message' => '请尽快发货',
        'details' => [
            [
                'goods_no' => 'TEST001',
                'goods_name' => '测试商品',
                'num' => 1,
                'price' => 199.00,
                'total_amount' => 199.00,
            ]
        ]
    ];

    $result = $wdt->createOrder($orderData);
    if (!$wdt->isSuccess()) {
        throw new Exception('订单创建失败：' . $wdt->getMsg());
    }
    echo "✅ 步骤1：订单创建成功 - " . $orderData['trade_no'] . "\n";

    // 3. 查询订单状态
    $result = $wdt->queryOrder($orderData['trade_no']);
    if ($wdt->isSuccess()) {
        echo "✅ 步骤2：订单状态查询成功 - 状态：" . ($result['data']['status'] ?? 'N/A') . "\n";
    }

    // 4. 查询库存
    $result = $wdt->queryStock(['TEST001']);
    if ($wdt->isSuccess()) {
        echo "✅ 步骤3：库存查询成功\n";
    }

    // 5. 发货通知
    $logisticsData = [
        'trade_no' => $orderData['trade_no'],
        'logistics_no' => 'SF' . date('YmdHis'),
        'logistics_name' => '顺丰速运',
        'logistics_code' => 'SF',
        'ship_time' => date('Y-m-d H:i:s'),
        'sender_name' => '测试仓库',
        'sender_mobile' => '400-1234567',
    ];

    $result = $wdt->pushLogistics($logisticsData);
    if ($wdt->isSuccess()) {
        echo "✅ 步骤4：发货通知成功 - 物流单号：" . $logisticsData['logistics_no'] . "\n";
    }

    // 6. 查询物流状态
    $result = $wdt->queryLogistics($orderData['trade_no']);
    if ($wdt->isSuccess()) {
        echo "✅ 步骤5：物流状态查询成功\n";
    }
    
    // 7. 更新订单状态
    $result = $wdt->updateTradeStatus($orderData['trade_no'], 'shipped', '订单已发货');
    if ($wdt->isSuccess()) {
        echo "✅ 步骤6：订单状态更新成功\n";
    }
    
    echo "\n🎉 完整订单流程演示完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误：" . $e->getMessage() . "\n";
}
```

## ⚠️ 错误处理

### 错误检查方法

```php
$result = $wdt->createOrder($orderData);

// 方法1：使用isSuccess()检查
if ($wdt->isSuccess()) {
    echo "✅ 操作成功\n";
    // 处理成功结果
    print_r($result);
} else {
    echo "❌ 操作失败\n";
    echo "错误码：" . $wdt->getCode() . "\n";
    echo "错误信息：" . $wdt->getMsg() . "\n";
}

// 方法2：直接检查返回值
if ($result === false) {
    echo "❌ 操作失败：" . $wdt->getMsg() . "\n";
} else {
    echo "✅ 操作成功\n";
}
```

### 常见错误码

| 错误码  | 错误信息     | 解决方案                     |
| ------- | ------------ | ---------------------------- |
| `-1`    | 缺少必需参数 | 检查并补充必需的参数         |
| `40001` | 签名错误     | 检查app_key和app_secret是否正确 |
| `40002` | 参数错误     | 检查参数格式和类型           |
| `40003` | 订单不存在   | 确认订单号是否正确           |
| `40004` | 权限不足     | 检查access_token是否有效     |
| `40005` | 接口调用频率限制 | 降低调用频率或使用批量接口 |
| `50001` | 系统错误     | 稍后重试或联系技术支持       |

### 异常处理最佳实践

```php
try {
    $wdt = Erp::wangdiantong($config);
    
    $result = $wdt->createOrder($orderData);
    
    if (!$wdt->isSuccess()) {
        // 记录错误日志
        error_log("旺店通API调用失败：" . $wdt->getMsg());
        
        // 根据错误码进行不同处理
        switch ($wdt->getCode()) {
            case -1:
                throw new InvalidArgumentException('参数错误：' . $wdt->getMsg());
            case 40001:
                throw new AuthenticationException('认证失败：' . $wdt->getMsg());
            case 40005:
                // 频率限制，等待后重试
                sleep(1);
                $result = $wdt->createOrder($orderData);
                break;
            default:
                throw new RuntimeException('API调用失败：' . $wdt->getMsg());
        }
    }
    
    // 处理成功结果
    return $result;
    
} catch (Exception $e) {
    // 统一异常处理
    echo "❌ 错误：" . $e->getMessage() . "\n";
    return false;
}
```

## ❓ 常见问题

### Q1: 如何切换测试环境和生产环境？
**A:** 通过配置参数中的 `sandbox` 字段控制：
```php
$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'sandbox' => true,  // true=测试环境，false=生产环境
];
```

### Q2: 订单商品明细如何传递？
**A:** 通过 `details` 数组传递商品信息，系统会自动转换为JSON格式：
```php
'details' => [
    [
        'goods_no' => '商品编号',
        'goods_name' => '商品名称',
        'num' => 数量,
        'price' => 单价,
        'total_amount' => 总金额,
        // 可以添加任意其他字段
        'custom_field' => '自定义值',
    ]
]
```

### Q3: 如何处理签名错误？
**A:** 检查以下几点：
- app_key 和 app_secret 是否正确
- 参数是否按ASCII码升序排序
- 签名字符串拼接是否正确
- MD5加密后是否转为大写

### Q4: 支持哪些物流公司？
**A:** 可以通过 `getLogisticsCompanies()` 方法获取支持的物流公司列表：
```php
$result = $wdt->getLogisticsCompanies();
if ($wdt->isSuccess()) {
    foreach ($result['data'] as $company) {
        echo $company['code'] . ' - ' . $company['name'] . "\n";
    }
}
```

### Q5: 如何进行批量操作？
**A:** 使用批量查询方法：
```php
$orderNos = ['ORDER001', 'ORDER002', 'ORDER003'];
$result = $wdt->batchQueryOrders($orderNos);
```

### Q6: 接口调用频率有限制吗？
**A:** 旺店通API有调用频率限制，建议：
- 避免频繁调用同一接口
- 使用批量接口减少调用次数
- 实现重试机制处理限流
- 在业务逻辑中添加适当的延时

### Q7: 如何传递自定义参数？
**A:** 新版本支持传递任意业务参数，无需硬编码：
```php
$orderData = [
    // 必需参数
    'trade_no' => 'ORDER123',
    'buyer_nick' => '买家',
    // ... 其他必需参数
    
    // 任意自定义参数
    'custom_field1' => '自定义值1',
    'custom_field2' => '自定义值2',
    'platform_specific_field' => '平台特定字段',
];

$result = $wdt->createOrder($orderData);
```

### Q8: 如何调试API调用？
**A:** 建议的调试方法：
```php
// 1. 启用错误日志
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 2. 使用测试环境
$config['sandbox'] = true;

// 3. 检查返回结果
$result = $wdt->createOrder($orderData);
echo "成功状态：" . ($wdt->isSuccess() ? '是' : '否') . "\n";
echo "错误码：" . $wdt->getCode() . "\n";
echo "错误信息：" . $wdt->getMsg() . "\n";
echo "返回数据：\n";
print_r($result);
```

### 15. 店铺管理 - queryShops()

**方法签名：** `queryShops(array $params = [])`

**功能说明：** 查询店铺信息，支持多种查询条件

**参数说明：**
- `shop_no` (可选): 店铺编号
- `platform_id` (可选): 平台ID
- `platform_ids` (可选): 批量平台ID（逗号分隔）
- `is_disabled` (可选): 是否停用（0未停用，1停用）
- `page_size` (可选): 分页大小（1-100，默认30）
- `page_no` (可选): 页号（从0开始，默认0）

**使用示例：**

```php
// 获取所有店铺
$result = $wdt->queryShops();

// 根据店铺编号查询
$result = $wdt->queryShopByNo('shop001');

// 根据平台ID查询
$result = $wdt->queryShopsByPlatform(127, 30, 0);

// 批量查询多个平台
$result = $wdt->queryShopsByPlatforms([1, 2, 127]);

// 查询启用的店铺
$result = $wdt->queryShopsByStatus(0);

// 分页获取所有店铺
$result = $wdt->getAllShops(50, 0);

if ($wdt->isSuccess()) {
    echo "总数量：" . ($result['total_count'] ?? 0) . "\n";
    foreach ($result['shoplist'] as $shop) {
        echo "店铺：{$shop['shop_name']} (编号：{$shop['shop_no']})\n";
        echo "平台ID：{$shop['platform_id']}\n";
        echo "授权状态：{$shop['auth_state']}\n";
        echo "是否停用：{$shop['is_disabled']}\n";
    }
} else {
    echo "查询失败：" . $wdt->getMsg() . "\n";
}
```

### 16. 店铺助手类使用

**类名：** `WangdiantongShopHelper`

**功能说明：** 提供更便捷的店铺管理功能，包含缓存机制

**使用示例：**

```php
use Erp\WangdiantongShopHelper;

$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'sid' => 'your_sid',
    'sandbox' => false
];

$shopHelper = new WangdiantongShopHelper($config);

// 获取所有店铺数据（自动分页）
$allShops = $shopHelper->getAllShopsData();

// 获取店铺详情
$shop = $shopHelper->getShopDetail('shop001');

// 检查店铺状态
$exists = $shopHelper->shopExists('shop001');
$active = $shopHelper->isShopActive('shop001');
$authorized = $shopHelper->isShopAuthorized('shop001');

// 获取店铺统计信息
$stats = $shopHelper->getShopStatistics();

// 搜索店铺
$results = $shopHelper->searchShops('test');

// 获取店铺选项列表（用于下拉选择）
$options = $shopHelper->getShopOptions();

// 批量检查店铺状态
$status = $shopHelper->batchCheckShopStatus(['shop001', 'shop002']);
```

## 📞 技术支持

### 开发调试建议
- 优先使用测试环境进行开发调试
- 启用详细的错误日志记录
- 检查API返回的详细错误信息
- 使用通用callApi方法测试新接口

### 性能优化建议
- 使用批量接口减少API调用次数
- 实现本地缓存减少重复查询
- 合理设置接口调用间隔
- 使用异步处理提高响应速度

### 联系方式
- **旺店通官方文档**：https://open.wangdian.cn/
- **技术支持邮箱**：<EMAIL>
- **开发者社区**：请联系旺店通官方获取QQ群信息

### 更新日志

#### v2.1.0 (2024-08-07)
- ✅ **新增功能**：完整的店铺管理接口支持
- ✅ **店铺查询**：支持多种条件查询店铺信息
- ✅ **助手类**：提供 WangdiantongShopHelper 便捷管理类
- ✅ **缓存机制**：店铺数据自动缓存，提升查询性能
- ✅ **批量操作**：支持批量查询店铺状态
- ✅ **状态检查**：便捷的店铺存在性、启用状态、授权状态检查

#### v2.0.0 (2024-01-01)
- ✅ **重大更新**：全面重构代码架构
- ✅ **极简设计**：所有业务方法简化为一行代码
- ✅ **通用参数**：支持传入任意业务参数，无硬编码限制
- ✅ **性能优化**：代码行数减少70%，执行效率提升
- ✅ **易于维护**：统一的API调用方法，便于扩展和维护

#### v1.0.0 (2023-12-01)
- ✅ 初始版本发布
- ✅ 支持完整的订单生命周期管理
- ✅ 支持物流跟踪和库存查询
- ✅ 支持测试环境和生产环境切换

---

## 📋 总结

旺店通ERP接口驱动v2.0采用了全新的极简设计理念：

### 🎯 核心优势
1. **一行代码调用**：所有业务方法都简化为一行代码
2. **无硬编码限制**：支持传入任意业务参数
3. **高度通用化**：通过callApi方法可调用任意API
4. **完整功能覆盖**：支持订单全生命周期管理

### 🚀 使用建议
- 新项目直接使用新版本API
- 老项目可逐步迁移到新版本
- 充分利用通用参数特性
- 合理使用批量接口提高效率

**注意：** 本文档基于旺店通开放平台API v1.0编写，如有API更新请参考官方最新文档。
