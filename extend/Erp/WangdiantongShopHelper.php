<?php

namespace Erp;

use Erp\Erp;
use Exception;

/**
 * 旺店通店铺接口助手类
 * 
 * 提供更便捷的店铺信息查询和管理功能
 * 
 * 使用示例：
 * ```php
 * $config = [
 *     'app_key' => 'your_app_key',
 *     'app_secret' => 'your_app_secret',
 *     'sid' => 'your_sid',
 *     'sandbox' => false
 * ];
 * 
 * $helper = new WangdiantongShopHelper($config);
 * 
 * // 获取所有店铺
 * $shops = $helper->getAllShopsData();
 * 
 * // 获取店铺详情
 * $shop = $helper->getShopDetail('shop001');
 * 
 * // 检查店铺状态
 * $isActive = $helper->isShopActive('shop001');
 * ```
 */
class WangdiantongShopHelper
{
    /**
     * @var \Erp\Driver\Wangdiantong 旺店通实例
     */
    private $wdt;

    /**
     * @var array 店铺缓存
     */
    private $shopCache = [];

    /**
     * @var int 缓存过期时间（秒）
     */
    private $cacheExpire = 300; // 5分钟

    /**
     * 构造函数
     * @param array $config 配置参数
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->wdt = Erp::wangdiantong($config);
    }

    /**
     * 获取所有店铺数据（自动分页获取全部）
     * @param bool $includeDisabled 是否包含停用的店铺，默认false
     * @return array 店铺数据数组
     */
    public function getAllShopsData(bool $includeDisabled = false): array
    {
        $cacheKey = 'all_shops_' . ($includeDisabled ? 'with_disabled' : 'active_only');
        
        // 检查缓存
        if (isset($this->shopCache[$cacheKey]) && 
            time() - $this->shopCache[$cacheKey]['time'] < $this->cacheExpire) {
            return $this->shopCache[$cacheKey]['data'];
        }

        $allShops = [];
        $pageNo = 0;
        $pageSize = 100;

        do {
            $params = [
                'page_size' => $pageSize,
                'page_no' => $pageNo
            ];

            if (!$includeDisabled) {
                $params['is_disabled'] = 0;
            }

            $result = $this->wdt->queryShops($params);
            
            if (!$this->wdt->isSuccess()) {
                throw new Exception('获取店铺数据失败：' . $this->wdt->getMsg());
            }

            $shops = $result['shoplist'] ?? [];
            $allShops = array_merge($allShops, $shops);
            
            $pageNo++;
        } while (count($shops) == $pageSize);

        // 缓存结果
        $this->shopCache[$cacheKey] = [
            'data' => $allShops,
            'time' => time()
        ];

        return $allShops;
    }

    /**
     * 获取店铺详情
     * @param string $shopNo 店铺编号
     * @return array|null 店铺详情，不存在返回null
     */
    public function getShopDetail(string $shopNo): ?array
    {
        $result = $this->wdt->queryShopByNo($shopNo);
        
        if (!$this->wdt->isSuccess()) {
            return null;
        }

        $shops = $result['shoplist'] ?? [];
        return !empty($shops) ? $shops[0] : null;
    }

    /**
     * 检查店铺是否存在
     * @param string $shopNo 店铺编号
     * @return bool
     */
    public function shopExists(string $shopNo): bool
    {
        return $this->getShopDetail($shopNo) !== null;
    }

    /**
     * 检查店铺是否启用
     * @param string $shopNo 店铺编号
     * @return bool
     */
    public function isShopActive(string $shopNo): bool
    {
        $shop = $this->getShopDetail($shopNo);
        return $shop && $shop['is_disabled'] == '0';
    }

    /**
     * 检查店铺是否已授权
     * @param string $shopNo 店铺编号
     * @return bool
     */
    public function isShopAuthorized(string $shopNo): bool
    {
        $shop = $this->getShopDetail($shopNo);
        return $shop && $shop['auth_state'] == '1';
    }

    /**
     * 获取店铺授权状态文本
     * @param string $shopNo 店铺编号
     * @return string
     */
    public function getShopAuthStatus(string $shopNo): string
    {
        $shop = $this->getShopDetail($shopNo);
        if (!$shop) {
            return '店铺不存在';
        }

        $statusMap = [
            '0' => '未授权',
            '1' => '已授权',
            '2' => '授权失效',
            '3' => '授权停用'
        ];

        return $statusMap[$shop['auth_state']] ?? '未知状态';
    }

    /**
     * 根据平台ID获取店铺列表
     * @param int $platformId 平台ID
     * @param bool $activeOnly 是否只获取启用的店铺，默认true
     * @return array
     */
    public function getShopsByPlatform(int $platformId, bool $activeOnly = true): array
    {
        $allShops = $this->getAllShopsData(!$activeOnly);
        
        return array_filter($allShops, function($shop) use ($platformId, $activeOnly) {
            $platformMatch = $shop['platform_id'] == $platformId;
            if ($activeOnly) {
                return $platformMatch && $shop['is_disabled'] == '0';
            }
            return $platformMatch;
        });
    }

    /**
     * 获取店铺统计信息
     * @return array
     */
    public function getShopStatistics(): array
    {
        $allShops = $this->getAllShopsData(true); // 包含停用的店铺

        $stats = [
            'total' => count($allShops),
            'active' => 0,
            'disabled' => 0,
            'authorized' => 0,
            'unauthorized' => 0,
            'platforms' => []
        ];

        foreach ($allShops as $shop) {
            // 统计启用/停用
            if ($shop['is_disabled'] == '0') {
                $stats['active']++;
            } else {
                $stats['disabled']++;
            }

            // 统计授权状态
            if ($shop['auth_state'] == '1') {
                $stats['authorized']++;
            } else {
                $stats['unauthorized']++;
            }

            // 统计平台分布
            $platformId = $shop['platform_id'];
            if (!isset($stats['platforms'][$platformId])) {
                $stats['platforms'][$platformId] = [
                    'count' => 0,
                    'active' => 0,
                    'authorized' => 0
                ];
            }
            $stats['platforms'][$platformId]['count']++;
            if ($shop['is_disabled'] == '0') {
                $stats['platforms'][$platformId]['active']++;
            }
            if ($shop['auth_state'] == '1') {
                $stats['platforms'][$platformId]['authorized']++;
            }
        }

        return $stats;
    }

    /**
     * 搜索店铺
     * @param string $keyword 关键词（店铺名称或编号）
     * @param bool $activeOnly 是否只搜索启用的店铺，默认true
     * @return array
     */
    public function searchShops(string $keyword, bool $activeOnly = true): array
    {
        $allShops = $this->getAllShopsData(!$activeOnly);
        
        return array_filter($allShops, function($shop) use ($keyword, $activeOnly) {
            $nameMatch = stripos($shop['shop_name'], $keyword) !== false;
            $noMatch = stripos($shop['shop_no'], $keyword) !== false;
            $keywordMatch = $nameMatch || $noMatch;
            
            if ($activeOnly) {
                return $keywordMatch && $shop['is_disabled'] == '0';
            }
            return $keywordMatch;
        });
    }

    /**
     * 获取店铺选项列表（用于下拉选择）
     * @param bool $activeOnly 是否只获取启用的店铺，默认true
     * @return array 格式：[['value' => 'shop_no', 'label' => 'shop_name'], ...]
     */
    public function getShopOptions(bool $activeOnly = true): array
    {
        $shops = $this->getAllShopsData(!$activeOnly);
        $options = [];

        foreach ($shops as $shop) {
            if ($activeOnly && $shop['is_disabled'] != '0') {
                continue;
            }

            $options[] = [
                'value' => $shop['shop_no'],
                'label' => $shop['shop_name'],
                'platform_id' => $shop['platform_id'],
                'auth_state' => $shop['auth_state'],
                'is_disabled' => $shop['is_disabled']
            ];
        }

        return $options;
    }

    /**
     * 清除缓存
     */
    public function clearCache(): void
    {
        $this->shopCache = [];
    }

    /**
     * 设置缓存过期时间
     * @param int $seconds 过期时间（秒）
     */
    public function setCacheExpire(int $seconds): void
    {
        $this->cacheExpire = $seconds;
    }

    /**
     * 获取旺店通实例
     * @return \Erp\Driver\Wangdiantong
     */
    public function getWdtInstance()
    {
        return $this->wdt;
    }

    /**
     * 批量检查店铺状态
     * @param array $shopNos 店铺编号数组
     * @return array 格式：['shop_no' => ['exists' => bool, 'active' => bool, 'authorized' => bool], ...]
     */
    public function batchCheckShopStatus(array $shopNos): array
    {
        $result = [];
        
        foreach ($shopNos as $shopNo) {
            $shop = $this->getShopDetail($shopNo);
            $result[$shopNo] = [
                'exists' => $shop !== null,
                'active' => $shop && $shop['is_disabled'] == '0',
                'authorized' => $shop && $shop['auth_state'] == '1',
                'shop_name' => $shop['shop_name'] ?? '',
                'platform_id' => $shop['platform_id'] ?? ''
            ];
        }

        return $result;
    }
}
