<?php

namespace Erp;

use Erp\Contracts\ErpInterface;
use Erp\Driver\Wanliniu;
use Exception;

/**
 * Class Erp
 * @method Wanliniu wanliniu($config) static 万里牛
 * @method Wangdiantong wangdiantong($config) static 旺店通
 *
 * @package Erp
 */
class Erp
{
    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return ErpInterface
     * @throws Exception
     */
    public static function __callStatic(string $name, array $arguments)
    {
        return self::driver($name, reset($arguments));
    }

    /**
     * 指定驱动器
     * @param string $driver 短信驱动
     * @param array $config 驱动配置
     * @return ErpInterface
     * @throws Exception
     */
    public static function driver(string $driver, array $config = [])
    {
        if (!file_exists(__DIR__ . '/Driver/' . ucfirst($driver) . '.php')) {
            throw new Exception("Driver [$driver] is not supported.");
        }
        $gateway = __NAMESPACE__ . '\\Driver\\' . ucfirst($driver);
        return new $gateway($config);
    }
}
