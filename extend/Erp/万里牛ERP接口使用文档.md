# 万里牛 ERP 接口使用文档

## 📋 目录

- [简介](#简介)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [API 接口列表](#api接口列表)
- [使用示例](#使用示例)
- [错误处理](#错误处理)
- [常见问题](#常见问题)

## 📖 简介

万里牛 ERP 接口驱动是基于万里牛开放平台 API 开发的 PHP SDK，主要提供库存管理、商品管理等 ERP 核心功能。万里牛是专业的电商 ERP 系统，为电商企业提供订单管理、库存管理、财务管理等一体化解决方案。

### 主要特性

- ✅ 支持正式环境和测试环境切换
- ✅ 库存查询和管理功能
- ✅ 自动签名和认证处理
- ✅ 统一的错误处理机制
- ✅ 重试机制支持
- ✅ 详细的日志和调试信息

## 🚀 快速开始

### 安装要求

- PHP >= 7.4
- 支持 curl 扩展
- 万里牛开放平台账号和应用

### 基本使用

```php
<?php
use Erp\Erp;

// 配置参数
$config = [
    'app_key' => 'your_app_key',        // 应用Key
    'secret' => 'your_secret',          // 应用密钥
    'shop_nick' => 'your_shop_nick',    // 店铺昵称
    'sandbox' => false,                 // 是否使用测试环境
];

// 创建实例
$wln = Erp::wanliniu($config);

// 查询库存
$result = $wln->inventories_erp(['goods_no' => 'GOODS001']);
if ($wln->isSuccess()) {
    echo '库存查询成功';
    print_r($result);
} else {
    echo '错误：' . $wln->getMsg();
}
```

## ⚙️ 配置说明

### 配置参数详解

| 参数名    | 类型    | 必填 | 说明                         |
| --------- | ------- | ---- | ---------------------------- |
| app_key   | string  | 是   | 万里牛开放平台应用 Key       |
| secret    | string  | 是   | 万里牛开放平台应用密钥       |
| shop_nick | string  | 是   | 店铺昵称标识                 |
| sandbox   | boolean | 否   | 是否使用测试环境，默认 false |

### 环境配置

#### 正式环境

```php
$config = [
    'app_key' => 'your_app_key',
    'secret' => 'your_secret',
    'shop_nick' => 'your_shop_nick',
    'sandbox' => false, // 或不设置
];
```

#### 测试环境

```php
$config = [
    'app_key' => 'test_app_key',
    'secret' => 'test_secret',
    'shop_nick' => 'test_shop_nick',
    'sandbox' => true, // 使用测试环境
];
```

## 📚 API 接口列表

### 库存管理

| 方法名                   | 功能描述             | 参数         | 返回值       |
| ------------------------ | -------------------- | ------------ | ------------ |
| `inventories_erp($data)` | 查询 ERP 库存        | 查询参数数组 | array\|false |
| `add_goods($data)`       | 添加商品（继承方法） | 商品数据数组 | array\|false |

### 通用方法

| 方法名              | 功能描述         | 参数               | 返回值       |
| ------------------- | ---------------- | ------------------ | ------------ |
| `get($url, $data)`  | 发送 GET 请求    | 接口地址, 参数数组 | array\|false |
| `post($url, $data)` | 发送 POST 请求   | 接口地址, 参数数组 | array\|false |
| `isSuccess()`       | 检查操作是否成功 | 无                 | boolean      |
| `getCode()`         | 获取错误码       | 无                 | int          |
| `getMsg()`          | 获取错误信息     | 无                 | string       |

## 💡 使用示例

### 1. 查询 ERP 库存

```php
// 基本库存查询
$data = [
    'goods_no' => 'GOODS001',  // 商品编号
];
$result = $wln->inventories_erp($data);

if ($wln->isSuccess()) {
    echo "库存查询成功：\n";
    print_r($result);
} else {
    echo "查询失败：" . $wln->getMsg() . "\n";
}
```

### 2. 批量查询库存

```php
// 查询多个商品库存
$goodsList = ['GOODS001', 'GOODS002', 'GOODS003'];

foreach ($goodsList as $goodsNo) {
    $data = ['goods_no' => $goodsNo];
    $result = $wln->inventories_erp($data);

    if ($wln->isSuccess()) {
        echo "商品 {$goodsNo} 库存：\n";
        print_r($result);
    } else {
        echo "商品 {$goodsNo} 查询失败：" . $wln->getMsg() . "\n";
    }

    // 避免请求过于频繁
    usleep(100000); // 0.1秒延迟
}
```

### 3. 带条件的库存查询

```php
// 带更多查询条件
$data = [
    'goods_no' => 'GOODS001',           // 商品编号
    'warehouse_no' => 'WH001',          // 仓库编号
    'spec_no' => 'SPEC001',             // 规格编号
    'page_no' => 1,                     // 页码
    'page_size' => 20,                  // 每页数量
];

$result = $wln->inventories_erp($data);

if ($wln->isSuccess()) {
    echo "详细库存信息：\n";

    // 解析返回数据
    if (isset($result['inventories'])) {
        foreach ($result['inventories'] as $inventory) {
            echo "商品编号：" . $inventory['goods_no'] . "\n";
            echo "可用库存：" . $inventory['stock_num'] . "\n";
            echo "仓库编号：" . $inventory['warehouse_no'] . "\n";
            echo "---\n";
        }
    }
} else {
    echo "查询失败：" . $wln->getMsg() . "\n";
}
```

### 4. 完整业务流程示例

```php
<?php
use Erp\Erp;

// 初始化万里牛ERP
$config = [
    'app_key' => 'your_app_key',
    'secret' => 'your_secret',
    'shop_nick' => 'your_shop_nick',
    'sandbox' => false,
];

$wln = Erp::wanliniu($config);

try {
    // 1. 查询商品库存
    $goodsNo = 'GOODS001';
    $stockData = ['goods_no' => $goodsNo];

    $result = $wln->inventories_erp($stockData);
    if (!$wln->isSuccess()) {
        throw new Exception('库存查询失败：' . $wln->getMsg());
    }

    echo "✅ 库存查询成功\n";

    // 2. 检查库存是否充足
    $availableStock = 0;
    if (isset($result['inventories']) && !empty($result['inventories'])) {
        $inventory = $result['inventories'][0];
        $availableStock = $inventory['stock_num'] ?? 0;

        echo "商品 {$goodsNo} 当前库存：{$availableStock}\n";

        if ($availableStock > 0) {
            echo "✅ 库存充足，可以销售\n";
        } else {
            echo "⚠️ 库存不足，需要补货\n";
        }
    }

    // 3. 根据库存情况进行业务处理
    if ($availableStock >= 10) {
        echo "📦 库存充足，可以进行批量销售\n";
    } elseif ($availableStock > 0) {
        echo "📦 库存有限，建议限量销售\n";
    } else {
        echo "🚫 库存为零，暂停销售\n";
    }

} catch (Exception $e) {
    echo "❌ 错误：" . $e->getMessage() . "\n";
}
```

### 5. 自定义接口调用示例

```php
// 使用通用方法调用其他万里牛API
class CustomWanliniu extends \Erp\Driver\Wanliniu
{
    /**
     * 查询商品信息
     */
    public function queryGoods($goodsNo)
    {
        $url = 'goods/query';
        $data = [
            'goods_no' => $goodsNo,
        ];

        return $this->get($url, $data);
    }

    /**
     * 更新库存
     */
    public function updateStock($goodsNo, $stockNum)
    {
        $url = 'inventories/update';
        $data = [
            'goods_no' => $goodsNo,
            'stock_num' => $stockNum,
        ];

        return $this->post($url, $data);
    }
}

// 使用自定义方法
$customWln = new CustomWanliniu($config);

// 查询商品信息
$goodsInfo = $customWln->queryGoods('GOODS001');
if ($customWln->isSuccess()) {
    echo "商品信息：\n";
    print_r($goodsInfo);
}

// 更新库存
$updateResult = $customWln->updateStock('GOODS001', 100);
if ($customWln->isSuccess()) {
    echo "库存更新成功\n";
}
```

### 6. 错误重试机制示例

```php
// 带重试机制的库存查询
function queryStockWithRetry($wln, $goodsNo, $maxRetries = 3)
{
    $retryCount = 0;

    while ($retryCount < $maxRetries) {
        $result = $wln->inventories_erp(['goods_no' => $goodsNo]);

        if ($wln->isSuccess()) {
            return $result;
        }

        $retryCount++;
        echo "第 {$retryCount} 次重试...\n";

        // 指数退避策略
        sleep(pow(2, $retryCount - 1));
    }

    throw new Exception('重试 ' . $maxRetries . ' 次后仍然失败：' . $wln->getMsg());
}

// 使用重试机制
try {
    $result = queryStockWithRetry($wln, 'GOODS001');
    echo "库存查询成功：\n";
    print_r($result);
} catch (Exception $e) {
    echo "查询失败：" . $e->getMessage() . "\n";
}
```

## 🔧 错误处理

### 错误状态检查

```php
$result = $wln->inventories_erp($data);

// 检查是否成功
if ($wln->isSuccess()) {
    echo '操作成功';
    // 处理成功结果
    print_r($result);
} else {
    echo '操作失败';
    echo '错误码：' . $wln->getCode();
    echo '错误信息：' . $wln->getMsg();
}
```

### 常见错误码

| 错误码 | 错误信息         | 解决方案                |
| ------ | ---------------- | ----------------------- |
| -1     | 解析返回结果失败 | 检查网络连接和 API 地址 |
| -1     | 解析返回结果失败 | 检查返回数据格式        |
| 40001  | 签名错误         | 检查 app_key 和 secret  |
| 40002  | 参数错误         | 检查传入参数格式和值    |
| 40003  | 权限不足         | 检查应用权限配置        |
| 50001  | 系统错误         | 稍后重试或联系技术支持  |

### 异常处理最佳实践

```php
try {
    $result = $wln->inventories_erp($data);

    if (!$wln->isSuccess()) {
        // 记录错误日志
        error_log("万里牛API错误：" . $wln->getCode() . " - " . $wln->getMsg());

        // 根据错误码进行不同处理
        switch ($wln->getCode()) {
            case 40001:
                // 签名错误，检查配置
                throw new Exception('API配置错误，请检查app_key和secret');
                break;
            case 40002:
                // 参数错误
                throw new Exception('请求参数错误：' . $wln->getMsg());
                break;
            case 50001:
                // 系统错误，可以重试
                sleep(1);
                $result = $wln->inventories_erp($data);
                if (!$wln->isSuccess()) {
                    throw new Exception('系统错误，重试后仍然失败');
                }
                break;
            default:
                throw new Exception('API调用失败：' . $wln->getMsg());
        }
    }

    // 处理成功结果
    return $result;

} catch (Exception $e) {
    // 统一异常处理
    echo "处理失败：" . $e->getMessage();
    return false;
}
```

### 网络异常处理

```php
// 处理网络超时和连接异常
function safeApiCall($wln, $method, $params, $timeout = 30)
{
    // 设置超时时间
    ini_set('default_socket_timeout', $timeout);

    try {
        $result = call_user_func_array([$wln, $method], $params);

        if (!$wln->isSuccess()) {
            throw new Exception($wln->getMsg(), $wln->getCode());
        }

        return $result;

    } catch (Exception $e) {
        // 记录异常日志
        error_log("万里牛API调用异常：" . $e->getMessage());

        // 根据异常类型处理
        if (strpos($e->getMessage(), 'timeout') !== false) {
            throw new Exception('网络超时，请稍后重试');
        } elseif (strpos($e->getMessage(), 'connection') !== false) {
            throw new Exception('网络连接失败，请检查网络');
        } else {
            throw $e;
        }
    }
}

// 使用安全调用
try {
    $result = safeApiCall($wln, 'inventories_erp', [['goods_no' => 'GOODS001']]);
    echo "调用成功：\n";
    print_r($result);
} catch (Exception $e) {
    echo "调用失败：" . $e->getMessage() . "\n";
}
```

## ❓ 常见问题

### Q1: 如何获取万里牛应用 Key 和密钥？

**A:**

1. 登录万里牛开放平台 (https://erp-open.hupun.com)
2. 创建应用或查看已有应用
3. 在应用详情中获取 app_key 和 secret
4. 配置店铺昵称 shop_nick

### Q2: 测试环境和正式环境有什么区别？

**A:**

- 测试环境用于开发调试，使用测试数据
- 正式环境用于生产环境，操作真实数据
- 测试环境 API 地址：`https://test-erp-open.hupun.com/api/v1/`
- 正式环境 API 地址：`https://erp-open.hupun.com/api/v1/`

### Q3: 签名验证失败怎么办？

**A:**

1. 检查 app_key 和 secret 是否正确
2. 确认参数编码格式为 UTF-8
3. 检查系统时间是否准确（时间戳相关）
4. 确认签名算法实现正确

### Q4: 库存查询返回空数据怎么办？

**A:**

```php
$result = $wln->inventories_erp(['goods_no' => 'GOODS001']);

if ($wln->isSuccess()) {
    if (empty($result['inventories'])) {
        echo "商品不存在或库存为空\n";
        // 检查商品编号是否正确
        // 检查商品是否已在ERP系统中创建
    } else {
        echo "库存查询成功\n";
        print_r($result['inventories']);
    }
} else {
    echo "查询失败：" . $wln->getMsg() . "\n";
}
```

### Q5: 如何处理 API 调用频率限制？

**A:**

```php
// 添加请求间隔，避免频率限制
function rateLimitedCall($wln, $method, $params, $interval = 0.1)
{
    static $lastCallTime = 0;

    $currentTime = microtime(true);
    $timeDiff = $currentTime - $lastCallTime;

    if ($timeDiff < $interval) {
        usleep(($interval - $timeDiff) * 1000000);
    }

    $result = call_user_func_array([$wln, $method], $params);
    $lastCallTime = microtime(true);

    return $result;
}

// 使用限频调用
$result = rateLimitedCall($wln, 'inventories_erp', [['goods_no' => 'GOODS001']]);
```

### Q6: 如何调试 API 调用？

**A:**

```php
// 开启调试模式
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 记录请求和响应
class DebugWanliniu extends \Erp\Driver\Wanliniu
{
    protected function request(string $url, array $data = [], $method = 'get')
    {
        // 记录请求信息
        error_log("万里牛API请求：" . $url);
        error_log("请求参数：" . json_encode($data));

        $result = parent::request($url, $data, $method);

        // 记录响应信息
        error_log("响应结果：" . json_encode($result));

        return $result;
    }
}

// 使用调试版本
$debugWln = new DebugWanliniu($config);
$result = $debugWln->inventories_erp(['goods_no' => 'GOODS001']);
```

### Q7: 如何扩展更多 API 接口？

**A:**

```php
// 继承万里牛驱动类，添加更多接口
class ExtendedWanliniu extends \Erp\Driver\Wanliniu
{
    /**
     * 查询订单列表
     */
    public function queryOrders($params = [])
    {
        $url = 'orders/query';
        return $this->get($url, $params);
    }

    /**
     * 创建采购单
     */
    public function createPurchase($data)
    {
        $url = 'purchase/create';
        return $this->post($url, $data);
    }

    /**
     * 查询商品信息
     */
    public function queryGoods($goodsNo)
    {
        $url = 'goods/query';
        $data = ['goods_no' => $goodsNo];
        return $this->get($url, $data);
    }
}

// 使用扩展版本
$extendedWln = new ExtendedWanliniu($config);

// 查询订单
$orders = $extendedWln->queryOrders(['status' => 'pending']);

// 查询商品
$goods = $extendedWln->queryGoods('GOODS001');
```

## 📞 技术支持

- 万里牛开放平台文档：https://erp-open.hupun.com/doc
- 技术支持 QQ 群：[群号]
- 邮箱支持：<EMAIL>
- 万里牛官网：https://www.hupun.com

## 📝 更新日志

### v1.0.0 (2024-01-01)

- ✅ 初始版本发布
- ✅ 支持库存查询功能
- ✅ 支持测试环境切换
- ✅ 实现签名认证机制
- ✅ 添加错误处理和重试机制
- ✅ 提供完整的使用文档和示例

### 功能特性

- **库存管理**: 支持 ERP 库存查询，可按商品编号、仓库、规格等条件查询
- **环境切换**: 支持测试环境和正式环境无缝切换
- **错误处理**: 完善的错误处理机制，支持错误重试
- **扩展性**: 易于扩展更多万里牛 API 接口
- **调试支持**: 提供详细的调试信息和日志记录

### 适用场景

- 电商企业库存管理
- 多平台库存同步
- ERP 系统集成
- 供应链管理
- 仓储管理系统

---

_最后更新时间：2024-01-01_
