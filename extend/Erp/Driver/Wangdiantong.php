<?php

namespace Erp\Driver;

use Erp\Contracts\ErpInterface;
use Erp\Contracts\Config;
use Exception;

/**
 * 旺店通ERP接口驱动
 * 
 * 使用示例：
 * ```php
 * use Erp\Erp;
 * 
 * // 配置参数
 * $config = [
 *     'app_key' => 'your_app_key',
 *     'app_secret' => 'your_app_secret',
 *     'access_token' => 'your_access_token', // 可选
 *     'sandbox' => false, // 是否使用测试环境，默认false使用正式环境
 * ];
 * 
 * // 创建实例
 * $wdt = Erp::wangdiantong($config);
 * 
 * // 创建订单
 * $orderData = [
 *     'trade_no' => 'ORDER123456',
 *     'buyer_nick' => '买家昵称',
 *     'receiver_name' => '收货人姓名',
 *     'receiver_mobile' => '13800138000',
 *     'receiver_address' => '收货地址',
 *     'total_amount' => 100.00,
 *     'details' => [
 *         [
 *             'goods_no' => 'GOODS001',
 *             'goods_name' => '商品名称',
 *             'num' => 1,
 *             'price' => 100.00,
 *             'total_amount' => 100.00,
 *         ]
 *     ]
 * ];
 * 
 * $result = $wdt->createOrder($orderData);
 * if ($wdt->isSuccess()) {
 *     echo '订单创建成功';
 * } else {
 *     echo '错误：' . $wdt->getMsg();
 * }
 * 
 * // 查询订单
 * $result = $wdt->queryOrder('ORDER123456');
 * 
 * // 查询库存
 * $result = $wdt->queryStock(['GOODS001', 'GOODS002']);
 * 
 * // 发货通知
 * $logisticsData = [
 *     'trade_no' => 'ORDER123456',
 *     'logistics_no' => 'SF1234567890',
 *     'logistics_name' => '顺丰速运',
 *     'ship_time' => date('Y-m-d H:i:s'),
 * ];
 * $result = $wdt->pushLogistics($logisticsData);
 * 
 * // 查询物流状态
 * $result = $wdt->queryLogistics('ORDER123456');
 * 
 * // 更新订单状态
 * $result = $wdt->updateTradeStatus('ORDER123456', 'shipped', '订单已发货');
 * 
 * // 申请退款
 * $refundData = [
 *     'trade_no' => 'ORDER123456',
 *     'refund_amount' => 50.00,
 *     'refund_reason' => '商品质量问题',
 *     'refund_type' => 2, // 退货退款
 * ];
 * $result = $wdt->pushRefund($refundData);
 * 
 * // 查询退款状态
 * $result = $wdt->queryRefund('ORDER123456');
 * 
 * // 批量查询订单
 * $result = $wdt->batchQueryOrders(['ORDER123456', 'ORDER123457']);
 * 
 * // 获取物流公司列表
 * $result = $wdt->getLogisticsCompanies();
 * ```
 * 
 * @package Erp\Driver\Wangdiantong
 */
class Wangdiantong extends ErpInterface
{
    const API_URL = 'https://open.wangdian.cn/qyb/open/';
    const TEST_API_URL = 'https://sandbox.wangdian.cn/qyb/open/';

    private $app_key = '';
    private $app_secret = '';
    private $access_token = '';
    private $is_sandbox = false;

    /**
     * 构造函数
     * @param array $config 配置参数
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $this->config = new Config($config);
        $this->success = false;
        $this->app_key = $this->config->get('app_key');
        $this->app_secret = $this->config->get('app_secret');
        $this->access_token = $this->config->get('access_token');
        $this->is_sandbox = (bool)$this->config->get('sandbox', false);

        if (empty($this->app_key) || empty($this->app_secret)) {
            throw new Exception('旺店通配置参数不完整：app_key和app_secret不能为空');
        }
    }

    /**
     * 生成API签名 - 根据旺店通API规范
     * @param array $params 请求参数
     * @return string
     */
    protected function generateSign(array $params)
    {
        // 移除签名参数
        unset($params['sign']);

        // 按键名ASCII码升序排序
        ksort($params);

        // 拼接签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && !is_null($value) && $key !== 'sign') {
                // 处理数组和对象参数
                if (is_array($value) || is_object($value)) {
                    $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                }
                $signStr .= $key . '=' . $value . '&';
            }
        }

        // 去掉最后的&符号
        $signStr = rtrim($signStr, '&');

        // 前后加上app_secret
        $signStr = $this->app_secret . $signStr . $this->app_secret;

        // MD5加密并转大写
        return strtoupper(md5($signStr));
    }

    /**
     * 发送GET请求
     * @param string $method API方法名
     * @param array $params 请求参数
     * @return array|false
     */
    protected function get(string $method, array $params = [])
    {
        return $this->request($method, $params, 'GET');
    }

    /**
     * 发送POST请求
     * @param string $method API方法名
     * @param array $params 请求参数
     * @return array|false
     */
    protected function post(string $method, array $params = [])
    {
        return $this->request($method, $params, 'POST');
    }

    /**
     * 发送HTTP请求
     * @param string $method API方法名
     * @param array $params 请求参数
     * @param string $httpMethod HTTP方法
     * @return array|false
     */
    protected function request(string $method, array $params = [], string $httpMethod = 'POST')
    {
        // 根据是否为测试环境选择API地址
        $baseUrl = $this->is_sandbox ? self::TEST_API_URL : self::API_URL;
        $url = $baseUrl . $method;

        // 添加公共参数
        $params['app_key'] = $this->app_key;
        $params['timestamp'] = time();
        $params['format'] = 'json';
        $params['v'] = '1.0';

        if (!empty($this->access_token)) {
            $params['access_token'] = $this->access_token;
        }

        // 生成签名
        $params['sign'] = $this->generateSign($params);

        // 发送请求
        $curl = curl()->set_header(['Content-Type' => 'application/x-www-form-urlencoded']);

        if ($httpMethod === 'GET') {
            $result = $curl->get($url, $params)->get_body();
        } else {
            $result = $curl->post($url, $params)->get_body();
        }

        return $this->parseResult($result);
    }

    /**
     * 解析API返回结果
     * @param array|string $result 原始返回结果
     * @return array|false
     */
    protected function parseResult($result)
    {
        if (empty($result)) {
            $this->code = -1;
            $this->msg = '接口返回结果为空';
            return false;
        }

        if (!is_array($result)) {
            $this->code = -1;
            $this->msg = '解析返回结果失败';
            return false;
        }

        // 检查是否有错误
        if (isset($result['error_response'])) {
            $this->code = $result['error_response']['code'] ?? -1;
            $this->msg = $result['error_response']['msg'] ?? '未知错误';
            return false;
        }

        // 检查业务状态
        if (isset($result['success']) && $result['success'] !== true) {
            $this->code = $result['code'] ?? -1;
            $this->msg = $result['message'] ?? '业务处理失败';
            return false;
        }

        $this->success = true;
        $this->code = 0;
        $this->msg = '成功';

        return $result;
    }

    /**
     * 通用API调用核心方法
     * @param string $method API方法文件名
     * @param string $apiMethod API接口方法名
     * @param array $data 业务数据
     * @param array $requiredFields 必需字段
     * @return array|false
     */
    protected function apiCall(string $method, string $apiMethod, array $data, array $requiredFields = [])
    {
        // 验证必需参数
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $this->code = -1;
                $this->msg = "缺少必需参数: {$field}";
                return false;
            }
        }

        // 构建请求参数
        $params = array_merge(['method' => $apiMethod], array_filter($data));

        // 特殊处理订单商品明细
        if (isset($data['details']) && is_array($data['details'])) {
            $params['details'] = json_encode($data['details'], JSON_UNESCAPED_UNICODE);
        }

        return $this->post($method, $params);
    }

    /**
     * 重试机制
     * @param string $method 方法名
     * @param array $arguments 参数
     * @return mixed
     */
    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }

    /**
     * 本地订单数据映射为旺店通格式
     * @param array $localOrderData 本地订单数据
     * @return array 旺店通格式的订单数据
     */
    public function mapLocalOrderToWdt(array $localOrderData)
    {
        // 基础订单信息映射
        $wdtOrderData = [
            // 必需字段映射
            'trade_no' => $localOrderData['bill_number'] ?? '',                    // 订单号
            'buyer_nick' => $localOrderData['true_name'] ?? '',                   // 买家昵称（使用提货人姓名）
            'receiver_name' => $localOrderData['true_name'] ?? '',                // 收货人姓名
            'receiver_mobile' => $this->formatMobile($localOrderData['mobile'] ?? ''), // 收货人手机（去掉星号）
            'receiver_address' => $localOrderData['address_info'] ?? $localOrderData['address'] ?? '', // 收货地址
            
            // 可选字段映射
            'trade_time' => $localOrderData['create_time'] ?? '',                 // 订单时间
            'pay_time' => $localOrderData['pay_time'] ?? '',                      // 付款时间
            'total_amount' => $localOrderData['total_money'] ?? 0,                // 订单总金额
            'payment' => $this->calculatePayment($localOrderData),                // 实付金额
            'post_fee' => $localOrderData['freight_money'] ?? 0,                  // 邮费
            'discount_fee' => $localOrderData['discount_preferential_money'] ?? 0, // 优惠金额
            
            // 收货人详细信息
            'receiver_province' => $localOrderData['province_name'] ?? '',        // 省份
            'receiver_city' => $localOrderData['city_name'] ?? '',                // 城市
            'receiver_district' => $localOrderData['area_name'] ?? '',            // 区县
            'receiver_zip' => '',                                                 // 邮编（本地数据中没有）
            
            // 订单备注信息
            'buyer_message' => $localOrderData['remark'] ?? '',                   // 买家留言
            'seller_memo' => $this->buildSellerMemo($localOrderData),             // 卖家备注（组合多个字段）
            
            // 平台信息
            'platform' => 'yikayi',                                              // 平台来源
            'shop_no' => $localOrderData['bid'] ?? '',                           // 店铺编号（使用bid）
            'tid' => $localOrderData['guid'] ?? '',                              // 平台订单号（使用guid）
            
            // 订单状态信息
            'trade_status' => $this->mapOrderStatus($localOrderData['status'] ?? 0), // 订单状态映射
            'trade_type' => $localOrderData['type'] == 2 ? 2 : 1,               // 订单类型：1-普通，2-预约自提
            
            // 支付信息
            'cod_fee' => 0,                                                       // 货到付款费用
            'step_paid_fee' => 0,                                                 // 定金金额
            
            // 物流信息（如果已有）
            'logistics_no' => $localOrderData['express_no'] ?? '',               // 物流单号
            'logistics_name' => $localOrderData['express_name'] ?? '',           // 物流公司名称
            'consign_time' => $localOrderData['send_or_pick_up_time'] ?? '',     // 发货时间
            
            // 扩展字段
            'extend_field_1' => $localOrderData['extend_field_1'] ?? '',         // 扩展字段1
            'extend_field_2' => $localOrderData['extend_field_2'] ?? '',         // 扩展字段2
            'extend_field_3' => $localOrderData['extend_field_3'] ?? '',         // 扩展字段3
        ];
        
        // 处理商品明细
        $wdtOrderData['details'] = $this->mapGoodsDetails($localOrderData);
        
        // 处理自提相关信息
        if ($localOrderData['type'] == 2) { // 预约到店自提
            $wdtOrderData['pickup_store_name'] = $localOrderData['store_name'] ?? '';
            $wdtOrderData['pickup_store_address'] = $localOrderData['store_address'] ?? '';
            $wdtOrderData['pickup_store_mobile'] = $localOrderData['store_mobile'] ?? '';
            $wdtOrderData['pickup_code'] = $localOrderData['pick_up_code'] ?? '';
            $wdtOrderData['pickup_time'] = $localOrderData['request_send_or_pick_up_time'] ?? '';
        }
        
        return array_filter($wdtOrderData); // 过滤空值
    }
    
    /**
     * 格式化手机号（去掉星号）
     * @param string $mobile 原始手机号
     * @return string 格式化后的手机号
     */
    private function formatMobile(string $mobile): string
    {
        // 如果包含星号，尝试从扩展字段获取完整手机号
        if (strpos($mobile, '*') !== false) {
            // 这里可以根据实际情况处理，比如从其他字段获取完整手机号
            // 暂时返回原值，实际使用时可能需要其他处理方式
            return $mobile;
        }
        return $mobile;
    }
    
    /**
     * 计算实付金额
     * @param array $localOrderData 本地订单数据
     * @return float 实付金额
     */
    private function calculatePayment(array $localOrderData): float
    {
        $paidWechat = floatval($localOrderData['paid_wechat'] ?? 0);
        $paidMoney = floatval($localOrderData['paid_money'] ?? 0);
        $paidPoint = floatval($localOrderData['paid_point'] ?? 0);
        
        return $paidWechat + $paidMoney + $paidPoint;
    }
    
    /**
     * 构建卖家备注
     * @param array $localOrderData 本地订单数据
     * @return string 卖家备注
     */
    private function buildSellerMemo(array $localOrderData): string
    {
        $memo = [];
        
        // 添加优惠券信息
        if (!empty($localOrderData['coupon_name'])) {
            $memo[] = "优惠券：" . $localOrderData['coupon_name'];
        }
        
        // 添加支付方式信息
        $paymentInfo = [];
        if (!empty($localOrderData['paid_coupon']) && $localOrderData['paid_coupon'] > 0) {
            $paymentInfo[] = "券支付：¥" . $localOrderData['paid_coupon'];
        }
        if (!empty($localOrderData['paid_wechat']) && $localOrderData['paid_wechat'] > 0) {
            $paymentInfo[] = "微信支付：¥" . $localOrderData['paid_wechat'];
        }
        if (!empty($localOrderData['paid_money']) && $localOrderData['paid_money'] > 0) {
            $paymentInfo[] = "储值支付：¥" . $localOrderData['paid_money'];
        }
        
        if (!empty($paymentInfo)) {
            $memo[] = "支付方式：" . implode('，', $paymentInfo);
        }
        
        // 添加身份证信息
        if (!empty($localOrderData['id_card_number'])) {
            $memo[] = "身份证：" . $localOrderData['id_card_number'];
        }
        
        // 添加自提码
        if (!empty($localOrderData['pick_up_code'])) {
            $memo[] = "自提码：" . $localOrderData['pick_up_code'];
        }
        
        return implode('；', $memo);
    }
    
    /**
     * 映射订单状态
     * @param int $localStatus 本地订单状态
     * @return string 旺店通订单状态
     */
    private function mapOrderStatus(int $localStatus): string
    {
        $statusMap = [
            -3 => 'TRADE_CLOSED_BY_REFUND',    // 已退款
            -2 => 'TRADE_CLOSED',              // 已取消
            -1 => 'WAIT_BUYER_PAY',            // 待支付
            0  => 'WAIT_SELLER_SEND_GOODS',    // 待发货/待自提
            1  => 'WAIT_BUYER_CONFIRM_GOODS',  // 已发货/已自提
            2  => 'TRADE_FINISHED',            // 已完成
        ];
        
        return $statusMap[$localStatus] ?? 'WAIT_SELLER_SEND_GOODS';
    }
    
    /**
     * 映射商品明细
     * @param array $localOrderData 本地订单数据
     * @return array 旺店通格式的商品明细
     */
    private function mapGoodsDetails(array $localOrderData): array
    {
        $details = [];
        
        // 从goods_list获取商品明细
        if (!empty($localOrderData['goods_list']) && is_array($localOrderData['goods_list'])) {
            foreach ($localOrderData['goods_list'] as $goods) {
                $details[] = [
                    'goods_no' => $goods['goods_guid'] ?? $goods['guid'] ?? '',           // 商品编号
                    'goods_name' => $goods['goods_name'] ?? $goods['name'] ?? '',         // 商品名称
                    'spec_no' => $goods['sku_guid'] ?? '',                                // 规格编号
                    'spec_name' => $goods['specs'] ?? '',                                 // 规格名称
                    'num' => intval($goods['num'] ?? $goods['amount'] ?? 1),              // 数量
                    'price' => floatval($goods['goods_price'] ?? $goods['price'] ?? 0),   // 单价
                    'total_amount' => floatval($goods['goods_price'] ?? $goods['price'] ?? 0) * intval($goods['num'] ?? $goods['amount'] ?? 1), // 总金额
                    'oid' => $goods['guid'] ?? '',                                        // 子订单号
                    'pic_path' => $goods['pic'] ?? '',                                    // 商品图片
                    'outer_goods_id' => $goods['goods_guid'] ?? '',                       // 外部商品ID
                    'outer_spec_id' => $goods['sku_guid'] ?? '',                          // 外部规格ID
                ];
            }
        }
        // 如果goods_list为空，尝试从goods_info获取
        elseif (!empty($localOrderData['goods_info']) && is_array($localOrderData['goods_info'])) {
            foreach ($localOrderData['goods_info'] as $goods) {
                $details[] = [
                    'goods_no' => $goods['guid'] ?? '',                                   // 商品编号
                    'goods_name' => $goods['name'] ?? '',                                 // 商品名称
                    'spec_no' => $goods['sku_guid'] ?? '',                                // 规格编号
                    'spec_name' => $goods['specs'] ?? '',                                 // 规格名称
                    'num' => intval($goods['amount'] ?? 1),                               // 数量
                    'price' => floatval($goods['price'] ?? 0),                            // 单价
                    'total_amount' => floatval($goods['price'] ?? 0) * intval($goods['amount'] ?? 1), // 总金额
                    'pic_path' => $goods['pic'] ?? '',                                    // 商品图片
                    'outer_goods_id' => $goods['guid'] ?? '',                             // 外部商品ID
                ];
            }
        }
        
        return $details;
    }
    
    /**
     * 创建订单 - 旺店通订单推送接口
     * @param array $orderData 订单数据
     * @return array|false
     */
    public function createOrder(array $orderData)
    {
        $requiredFields = ['trade_no', 'buyer_nick', 'receiver_name', 'receiver_mobile', 'receiver_address'];
        return $this->callApi('trade_push.php', 'wdt.trade.push', $orderData, $requiredFields);
    }
    
    /**
     * 创建订单（使用本地订单数据）
     * @param array $localOrderData 本地订单数据
     * @return array|false
     */
    public function createOrderFromLocal(array $localOrderData)
    {
        // 先映射数据格式
        $wdtOrderData = $this->mapLocalOrderToWdt($localOrderData);
        
        // 然后调用创建订单接口
        return $this->createOrder($wdtOrderData);
    }

    /**
     * 通用API调用方法
     * @param string $method API方法文件名
     * @param string $apiMethod API接口方法名
     * @param array $data 业务数据
     * @param array $requiredFields 必需字段（可选）
     * @return array|false
     */
    public function callApi(string $method, string $apiMethod, array $data = [], array $requiredFields = [])
    {
        return $this->apiCall($method, $apiMethod, $data, $requiredFields);
    }

    // 简化的业务方法
    public function queryOrder(string $tradeNo)
    {
        return $this->callApi('trade_query.php', 'wdt.trade.query', ['trade_no' => $tradeNo]);
    }
    public function cancelOrder(string $tradeNo, string $reason = '')
    {
        return $this->callApi('trade_cancel.php', 'wdt.trade.cancel', array_filter(['trade_no' => $tradeNo, 'reason' => $reason]));
    }
    public function queryStock(array $goodsNos)
    {
        return $this->callApi('stock_query.php', 'wdt.stock.query', ['goods_nos' => implode(',', $goodsNos)]);
    }
    public function pushLogistics(array $data)
    {
        return $this->callApi('logistics_sync_push.php', 'wdt.logistics.sync.push', $data, ['trade_no', 'logistics_no', 'logistics_name']);
    }
    public function queryLogistics(string $tradeNo, string $logisticsNo = '')
    {
        return $this->callApi('logistics_sync_query.php', 'wdt.logistics.sync.query', array_filter(['trade_no' => $tradeNo, 'logistics_no' => $logisticsNo]));
    }
    public function updateTradeStatus(string $tradeNo, string $status, string $remark = '')
    {
        return $this->callApi('trade_status_update.php', 'wdt.trade.status.update', array_filter(['trade_no' => $tradeNo, 'status' => $status, 'remark' => $remark]));
    }
    public function pushRefund(array $data)
    {
        return $this->callApi('refund_push.php', 'wdt.refund.push', $data, ['trade_no', 'refund_amount', 'refund_reason']);
    }
    public function queryRefund(string $tradeNo, string $refundNo = '')
    {
        return $this->callApi('refund_query.php', 'wdt.refund.query', array_filter(['trade_no' => $tradeNo, 'refund_no' => $refundNo]));
    }
    public function batchQueryOrders(array $tradeNos)
    {
        return $this->callApi('trade_batch_query.php', 'wdt.trade.batch.query', ['trade_nos' => implode(',', $tradeNos)]);
    }
    public function getLogisticsCompanies()
    {
        return $this->callApi('logistics_companies.php', 'wdt.logistics.companies.get');
    }

    /**
     * 实现抽象方法：添加商品
     * @param array $data 商品数据
     * @return array|false
     */
    public function add_goods(array $data)
    {
        // 旺店通暂不支持商品添加，返回提示
        $this->code = -1;
        $this->msg = '旺店通暂不支持商品添加功能';
        return false;
    }
}
