<?php

namespace Erp\Driver;

use Erp\Contracts\ErpInterface;
use Erp\Contracts\Config;
use Exception;

/**
 * 阿里云
 * @package Erp\Driver\Wanliniu
 */
class Wanliniu extends ErpInterface
{
    const API_URL = 'https://erp-open.hupun.com/api/v1/';
    const TEST_API_URL = 'https://test-erp-open.hupun.com/api/v1/';
    private $app_key = '';
    private $secret = '';
    private $shop_nick = '';
    private $is_sandbox = false;

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config = [])
    {
        $this->config    = new Config($config);
        $this->success   = false;
        $this->app_key   = $this->config->get('app_key');
        $this->secret    = $this->config->get('secret');
        $this->shop_nick = $this->config->get('shop_nick');
        $this->is_sandbox = $this->config->get('sandbox', false);
    }

    /**
     * @param array $data
     * @return string
     */
    protected function get_sign(array $data)
    {
        ksort($data);
        $sign_str = $this->secret;
        foreach ($data as $key => $val) {
            if ($val !== '' && !is_null($val)) {
                $sign_str .= $key . $val;
            }
        }
        $sign_str .= $this->secret;
        $sign     = md5($sign_str);
        return strtoupper($sign);
    }

    protected function get(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function post(string $url, array $data = [])
    {
        return $this->request($url, $data, __FUNCTION__);
    }

    protected function request(string $url, array $data = [], $method = 'get')
    {
        // 根据是否为测试环境选择API地址
        $baseUrl = $this->is_sandbox ? self::TEST_API_URL : self::API_URL;
        $url               = $baseUrl . $url;
        $data['app_key']   = $this->app_key;
        $data['format']    = 'json';
        $data['sign']      = $this->get_sign($data);
        $data['timestamp'] = $this->get_microtime();
        $result            = curl()->set_header(['Content-Type' => 'application/x-www-form-urlencoded'])->$method($url, $data)->get_body();
        return $this->_parseResult($result);
    }

    protected function get_microtime()
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }

    /**
     * 解析返回的结果
     * @param array|string $result
     * @return array|bool
     */
    protected function _parseResult($result)
    {
        if (empty($result) || !is_array($result)) {
            $this->code = -1;
            $this->msg  = '解析返回结果失败';
            return false;
        }
        if (isset($result['success']) && $result['success'] !== true) {
            $this->code = $result['error_code'];
            $this->msg  = $result['error_msg'];
            return false;
        }
        $this->success = true;
        return isset($result['response']) ? json_decode($result['response'], true) : $result;
    }


    protected function checkRetry($method, $arguments = [])
    {
        if (!$this->_retry) {
            $this->_retry = true;
            return call_user_func_array([$this, $method], $arguments);
        }
        return false;
    }

    //库存
    public function inventories_erp($data = [])
    {
        $url = 'inventories/erp';
        return $this->get($url, $data);
    }

    public function add_goods($data = [])
    {
        $url = 'inventories/erp';
        return $this->get($url, $data);
    }
}
