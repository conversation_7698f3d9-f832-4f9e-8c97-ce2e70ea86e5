<?php

/**
 * 旺店通店铺助手类使用示例
 * 
 * 展示如何使用 WangdiantongShopHelper 类进行店铺管理
 */

require_once __DIR__ . '/WangdiantongShopHelper.php';

use Erp\WangdiantongShopHelper;

try {
    // 配置参数
    $config = [
        'app_key' => 'your_app_key',        // 接口账号
        'app_secret' => 'your_app_secret',  // 接口密钥
        'sid' => 'your_sid',                // 卖家账号
        'sandbox' => true,                  // 使用测试环境
    ];

    // 创建店铺助手实例
    $shopHelper = new WangdiantongShopHelper($config);

    echo "=== 旺店通店铺助手类使用示例 ===\n\n";

    // 示例1：获取所有店铺数据
    echo "1. 获取所有启用的店铺：\n";
    $allShops = $shopHelper->getAllShopsData(false); // false表示不包含停用的店铺
    echo "共找到 " . count($allShops) . " 个启用的店铺\n";
    foreach ($allShops as $shop) {
        echo "- {$shop['shop_name']} (编号：{$shop['shop_no']}, 平台ID：{$shop['platform_id']})\n";
    }
    echo "\n";

    // 示例2：获取店铺详情
    echo "2. 获取指定店铺详情：\n";
    $shopNo = 'api_test'; // 替换为实际的店铺编号
    $shopDetail = $shopHelper->getShopDetail($shopNo);
    if ($shopDetail) {
        echo "店铺名称：{$shopDetail['shop_name']}\n";
        echo "店铺编号：{$shopDetail['shop_no']}\n";
        echo "联系人：{$shopDetail['contact']}\n";
        echo "手机：{$shopDetail['mobile']}\n";
        echo "地址：{$shopDetail['province_name']} {$shopDetail['city_name']} {$shopDetail['district_name']}\n";
        echo "授权状态：" . $shopHelper->getShopAuthStatus($shopNo) . "\n";
        echo "是否启用：" . ($shopHelper->isShopActive($shopNo) ? '是' : '否') . "\n";
    } else {
        echo "店铺不存在或查询失败\n";
    }
    echo "\n";

    // 示例3：检查店铺状态
    echo "3. 检查店铺状态：\n";
    $testShops = ['api_test', 'shop001', 'shop002']; // 替换为实际的店铺编号
    foreach ($testShops as $shopNo) {
        echo "店铺 {$shopNo}：\n";
        echo "  - 存在：" . ($shopHelper->shopExists($shopNo) ? '是' : '否') . "\n";
        echo "  - 启用：" . ($shopHelper->isShopActive($shopNo) ? '是' : '否') . "\n";
        echo "  - 已授权：" . ($shopHelper->isShopAuthorized($shopNo) ? '是' : '否') . "\n";
        echo "  - 授权状态：" . $shopHelper->getShopAuthStatus($shopNo) . "\n";
    }
    echo "\n";

    // 示例4：根据平台ID获取店铺
    echo "4. 根据平台ID获取店铺（平台ID：127）：\n";
    $platformShops = $shopHelper->getShopsByPlatform(127, true); // true表示只获取启用的店铺
    echo "平台ID 127 共有 " . count($platformShops) . " 个启用的店铺\n";
    foreach ($platformShops as $shop) {
        echo "- {$shop['shop_name']} (编号：{$shop['shop_no']})\n";
    }
    echo "\n";

    // 示例5：获取店铺统计信息
    echo "5. 店铺统计信息：\n";
    $stats = $shopHelper->getShopStatistics();
    echo "总店铺数：{$stats['total']}\n";
    echo "启用店铺：{$stats['active']}\n";
    echo "停用店铺：{$stats['disabled']}\n";
    echo "已授权店铺：{$stats['authorized']}\n";
    echo "未授权店铺：{$stats['unauthorized']}\n";
    echo "平台分布：\n";
    foreach ($stats['platforms'] as $platformId => $platformStats) {
        echo "  - 平台ID {$platformId}：总数 {$platformStats['count']}, 启用 {$platformStats['active']}, 已授权 {$platformStats['authorized']}\n";
    }
    echo "\n";

    // 示例6：搜索店铺
    echo "6. 搜索店铺（关键词：test）：\n";
    $searchResults = $shopHelper->searchShops('test', true); // true表示只搜索启用的店铺
    echo "搜索到 " . count($searchResults) . " 个匹配的店铺\n";
    foreach ($searchResults as $shop) {
        echo "- {$shop['shop_name']} (编号：{$shop['shop_no']})\n";
    }
    echo "\n";

    // 示例7：获取店铺选项列表（用于前端下拉选择）
    echo "7. 获取店铺选项列表：\n";
    $shopOptions = $shopHelper->getShopOptions(true); // true表示只获取启用的店铺
    echo "可选店铺：\n";
    foreach ($shopOptions as $option) {
        $authText = $option['auth_state'] == '1' ? '已授权' : '未授权';
        echo "- {$option['label']} (值：{$option['value']}, 平台ID：{$option['platform_id']}, {$authText})\n";
    }
    echo "\n";

    // 示例8：批量检查店铺状态
    echo "8. 批量检查店铺状态：\n";
    $shopNosToCheck = ['api_test', 'shop001', 'shop002', 'nonexistent_shop'];
    $batchResults = $shopHelper->batchCheckShopStatus($shopNosToCheck);
    foreach ($batchResults as $shopNo => $status) {
        echo "店铺 {$shopNo}：\n";
        echo "  - 存在：" . ($status['exists'] ? '是' : '否') . "\n";
        if ($status['exists']) {
            echo "  - 店铺名称：{$status['shop_name']}\n";
            echo "  - 平台ID：{$status['platform_id']}\n";
            echo "  - 启用：" . ($status['active'] ? '是' : '否') . "\n";
            echo "  - 已授权：" . ($status['authorized'] ? '是' : '否') . "\n";
        }
    }
    echo "\n";

    // 示例9：缓存管理
    echo "9. 缓存管理示例：\n";
    echo "设置缓存过期时间为60秒\n";
    $shopHelper->setCacheExpire(60);
    
    echo "第一次获取店铺数据（从API获取）\n";
    $start = microtime(true);
    $shops1 = $shopHelper->getAllShopsData();
    $time1 = microtime(true) - $start;
    echo "耗时：" . round($time1 * 1000, 2) . "ms\n";
    
    echo "第二次获取店铺数据（从缓存获取）\n";
    $start = microtime(true);
    $shops2 = $shopHelper->getAllShopsData();
    $time2 = microtime(true) - $start;
    echo "耗时：" . round($time2 * 1000, 2) . "ms\n";
    
    echo "清除缓存\n";
    $shopHelper->clearCache();
    
    echo "清除缓存后再次获取（从API获取）\n";
    $start = microtime(true);
    $shops3 = $shopHelper->getAllShopsData();
    $time3 = microtime(true) - $start;
    echo "耗时：" . round($time3 * 1000, 2) . "ms\n";
    echo "\n";

    // 示例10：直接使用旺店通实例
    echo "10. 直接使用旺店通实例：\n";
    $wdtInstance = $shopHelper->getWdtInstance();
    $result = $wdtInstance->queryShops(['page_size' => 5, 'page_no' => 0]);
    if ($wdtInstance->isSuccess()) {
        echo "直接调用成功，获取到 " . count($result['shoplist'] ?? []) . " 个店铺\n";
    } else {
        echo "直接调用失败：" . $wdtInstance->getMsg() . "\n";
    }

} catch (Exception $e) {
    echo "异常错误：" . $e->getMessage() . "\n";
    echo "错误文件：" . $e->getFile() . "\n";
    echo "错误行号：" . $e->getLine() . "\n";
}

echo "\n=== 示例结束 ===\n";

/**
 * 使用建议：
 * 
 * 1. 生产环境使用：
 *    - 将 sandbox 设置为 false
 *    - 使用正式环境的 app_key、app_secret 和 sid
 *    - 适当设置缓存过期时间，避免频繁调用API
 * 
 * 2. 错误处理：
 *    - 使用 try-catch 捕获异常
 *    - 检查方法返回值，处理null情况
 *    - 记录错误日志，便于问题排查
 * 
 * 3. 性能优化：
 *    - 合理使用缓存，减少API调用次数
 *    - 批量操作时使用批量方法
 *    - 根据业务需求选择是否包含停用店铺
 * 
 * 4. 数据处理：
 *    - 店铺数据结构参考API文档
 *    - 注意授权状态和启用状态的区别
 *    - 平台ID对应关系参考旺店通平台代码表
 */
