<?php

namespace Oauth\Driver;

use app\common\exceptions\NotNotifyException;
use app\model\Member;
use Exception;
use Oauth\Contracts\OauthInterface;


class Wechat extends OauthInterface
{
    /**
     * 登录
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function login(array $data)
    {
        if (empty($data['code'])) {
            throw new NotNotifyException('请传入code');
        }
        $code              = $data['code'];
        $bid               = $data['bid'];
        $config            = get_config_by_bid($bid);
        $appid             = $config['appid'];
        $share_member_guid = $data['share_member_guid'] ?? null;
        $wechat            = weixin($appid)::WeChatOauth();
        $result            = $wechat->getOauthAccessToken($code);
        $openid            = $result['openid'];
        $db_member         = new Member();
        $map               = [
            'bid'    => $bid,
            'openid' => $openid,
            'appid'  => $appid,
        ];
        $member            = $db_member->get_member_info($map, false);
        //非会员注册
        if (!$member) {
            $data   = [
                'bid'               => $bid,
                'from'              => 2,
                'appid'             => $appid,
                'openid'            => $openid,
                'share_member_guid' => $share_member_guid,
            ];
            $member = $db_member->register($data);
            if (!$member) {
                throw new Exception('会员注册失败');
            }
            $member = $db_member->get_member_info($map);
        }
        $member['member_guid'] = $member['guid'];
        $result['member_info'] = $member;
        $result['oauth_token'] = $result['access_token'];
        $result['appid']       = $appid;
        $result['openid']      = $openid;
        return $result;
    }

    /**
     * 获取用户信息
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function get_user_info(array $data)
    {
        $appid  = $data['appid'];
        $wechat = weixin($appid)::WeChatOauth();
        // 解码数据
        $access_token        = $data['oauth_token'];
        $openid              = $data['openid'];
        $user_info           = $wechat->getUserInfo($access_token, $openid);
        $update_data         = [
            'name'     => $user_info['nickname'],
            'head_img' => $user_info['headimgurl'],
            'sex'      => $user_info['sex'],
            'union_id' => !empty($user_info['unionid']) ? $user_info['unionid'] : '',
        ];
        $update_data['bid']  = $data['bid'];
        $update_data['guid'] = $data['member_guid'];
        $db_member           = new Member();
        $member              = $db_member->update_member_info($update_data);
        return $member;
    }

    /**
     * 获取授权链接
     * @param array $data
     * @return string
     * @throws Exception
     */
    public function get_oauth_url(array $data)
    {
        $bid          = $data['bid'];
        $redirect_url = $data['redirect_url'];
        $config       = get_config_by_bid($bid);
        $wechat       = weixin($config['appid'])::WeChatOauth();
        return $wechat->getOauthRedirect($redirect_url, $bid);
    }
}