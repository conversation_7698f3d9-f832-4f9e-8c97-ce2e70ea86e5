<?php

namespace Oauth\Driver;

use app\model\Member;
use Exception;
use Oauth\Contracts\OauthInterface;


class Web extends OauthInterface
{
    /**
     * 登录
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function login(array $data)
    {
        //通过手机号 验证码登录
        $mobile    = $data['mobile'];
        $bid       = $data['bid'];
        $password  = $data['password'];
        $db_member = new Member();
        $map       = [
            'bid'      => $bid,
            'mobile'   => $mobile,
            'password' => $password,
        ];
        $member    = $db_member->get_member_info($map);
        if (!$member) {
            throw new Exception('用户信息不存在');
        }
        $member['member_guid'] = $member['guid'];
        $result['member_info'] = $member;
        $result['oauth_token'] = '';
        $result['openid']      = '';
        $result['appid']       = '';
        return $result;
    }

    /**
     * 获取用户信息
     * @param array $data
     * @return array
     */
    public function get_user_info(array $data)
    {
        return [];
    }

    /**
     * 获取授权链接
     * @param array $data
     * @return string
     */
    public function get_oauth_url(array $data)
    {
        return '';
    }
}