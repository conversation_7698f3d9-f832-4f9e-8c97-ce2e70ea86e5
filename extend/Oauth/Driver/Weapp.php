<?php

namespace Oauth\Driver;

use app\common\exceptions\NotNotifyException;
use app\model\Member;
use app\model\WeappUserInfo;
use Exception;
use Oauth\Contracts\OauthInterface;

class Weapp extends OauthInterface
{
    /**
     * 登录
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function login(array $data)
    {
        if (empty($data['code'])) {
            throw new NotNotifyException('请传入code');
        }
        if (empty($data['appid'])) {
            throw new NotNotifyException('请传入appid');
        }
        // 解码数据
        $code              = $data['code'];
        $bid               = $data['bid'];
        $appid             = $data['appid'];
        $share_member_guid = $data['share_member_guid'] ?? null;
        $mini              = weixin($appid)::WeMiniCrypt();
        $result            = $mini->session($code);
        if (empty($result['openid'])) {
            throw new Exception($result['errmsg'], $result['errcode']);
        }
        $db_weapp_user_info = new WeappUserInfo();
        $db_weapp_user_info->save_user_info($appid, $result);
        $openid    = $result['openid'];
        $db_member = new Member();
        $map       = [
            'bid'    => $bid,
            'openid' => $openid,
            'appid'  => $appid,
        ];
        $member    = $db_member->get_member_info($map, false);
        if (!$member) {
            $data   = [
                'bid'               => $bid,
                'from'              => 1,
                'appid'             => $appid,
                'openid'            => $openid,
                'share_member_guid' => $share_member_guid,
            ];
            $member = $db_member->register($data);
            if (!$member) {
                throw new Exception('会员注册失败');
            }
        }
        $member['member_guid'] = $member['guid'];
        $result['member_info'] = $member;
        $result['oauth_token'] = $result['session_key'];
        $result['openid']      = $openid;
        $result['appid']       = $appid;
        return $result;
    }

    /**
     * 获取用户信息
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function get_user_info(array $data)
    {
        $user_info = [];
        // 解码数据
        if (!empty($data['iv']) && !empty($data['encrypted_data'])) {
            $iv             = $data['iv'];
            $encrypted_data = $data['encrypted_data'];
            $appid          = $data['appid'];
            $mini           = weixin($appid)::WeMiniCrypt();
            if (!empty($data['code'])) {
                $user_info = $mini->decodeData($data['code'], $iv, $encrypted_data);
            } else {
                $user_info = $mini->decode($iv, $data['oauth_token'], $encrypted_data);
            }
            if (!is_array($user_info)) {
                throw new NotNotifyException('user_info解密失败!');
            }
        } elseif (!empty($data['user_info'])) {
            $user_info = $data['user_info'];
        } else {
            throw new Exception('传入参数不完整');
        }
        $update_data         = [
            'name'     => tools()::emoji_encode($user_info['nickName']),
            'head_img' => $user_info['avatarUrl'],
            'sex'      => $user_info['gender'],
            'union_id' => !empty($user_info['unionId']) ? $user_info['unionId'] : '',
        ];
        $update_data['bid']  = $data['bid'];
        $update_data['guid'] = $data['member_guid'];
        $db_member           = new Member();
        return $db_member->update_member_info($update_data);
    }

    /**
     * 获取授权链接
     * @param array $data
     * @return string
     */
    public function get_oauth_url(array $data)
    {
        return '';
    }
}