<?php

namespace Oauth;

use Exception;
use <PERSON><PERSON><PERSON>\Contracts\OauthInterface;
use <PERSON>aut<PERSON>\Driver\Alipay;
use <PERSON><PERSON><PERSON>\Driver\Weapp;
use <PERSON>auth\Driver\Web;
use <PERSON>auth\Driver\Wechat;

/**
 * Class Oauth
 * @method Alipay alipay($config = []) static 支付宝
 * @method Weapp weapp($config = []) static 小程序
 * @method Wechat wechat($config = []) static 公众号
 * @method Web web($config = []) static WEB网页
 *
 * @package SendSms
 */
class Oauth
{
    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return OauthInterface
     * @throws Exception
     */
    public static function __callStatic(string $name, array $arguments)
    {
        return self::driver($name, reset($arguments));
    }

    /**
     * 指定驱动器
     * @param string $driver 短信驱动
     * @param array $config 驱动配置
     * @return OauthInterface
     * @throws Exception
     */
    public static function driver(string $driver, array $config = [])
    {
        if (!file_exists(__DIR__ . '/Driver/' . ucfirst($driver) . '.php')) {
            throw new Exception("Driver [$driver] is not supported.");
        }
        $gateway = __NAMESPACE__ . '\\Driver\\' . ucfirst($driver);
        return new $gateway($config);
    }
}
