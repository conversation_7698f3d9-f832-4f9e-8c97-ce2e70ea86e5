<?php

namespace Oauth\Contracts;

/**
 * Interface GatewayInterface
 * @package Storage\Contracts
 */
abstract class OauthInterface
{
    protected $code;
    protected $msg;

    /**
     * 登录
     * @param array $data
     * @return mixed
     */
    abstract public function login(array $data);

    /**
     * 获取用户信息
     * @param array $data
     * @return mixed
     */
    abstract public function get_user_info(array $data);

    /**
     * 获取授权链接
     * @param array $data
     * @return mixed
     */
    abstract public function get_oauth_url(array $data);

    public function getCode()
    {
        return $this->code;
    }

    public function getMsg()
    {
        return $this->msg;
    }
}
