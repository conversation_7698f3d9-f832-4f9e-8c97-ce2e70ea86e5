<?php

// +----------------------------------------------------------------------
// | WeChatDeveloper
// +----------------------------------------------------------------------
// | 版权所有 2014~2018 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 官方网站: http://think.ctolog.com
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/WeChatDeveloper
// +----------------------------------------------------------------------

namespace Juhe;


use app\model\CouponSendNote;
use app\model\JuheRechargeOrder;
use Exception;
use Juhe\Contracts\Basic;
use Juhe\Exceptions\InvalidArgumentException;

/**
 * 话费充值
 * Class RechargeMobile
 * @package Juhe
 */
class RechargeMobile extends Basic
{
    /**
     * @param array $options
     */
    public function __construct(array $options)
    {
        if (empty($options['appkey_mobile'])) {
            throw new InvalidArgumentException("Missing Config -- [appkey_mobile]");
        }
        $options['app_key'] = $options['appkey_mobile'];
        parent::__construct($options);
    }

    /**
     * 检测手机号码及金额是否能充值
     * @param string $mobile 手机号
     * @param int $amount 面值(元)
     * @return bool|array
     */
    public function telCheck($mobile, $amount)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/telcheck';
        $data = [
            "phoneno" => $mobile,
            'cardnum' => $amount
        ];
        return $this->callPostApi($url, $data);
    }

    /**
     * 根据手机号和面值查询商品信息
     * @param string $mobile 手机号
     * @param int $amount 面值(元)
     * @return bool|array
     */
    public function telQuery($mobile, $amount)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/telquery';
        $data = ["phoneno" => $mobile, 'cardnum' => $amount];
        return $this->callPostApi($url, $data);
    }

    /**
     * 预下单方法
     * @param array $data 数据包
     * @return bool|array
     */
    public function apply($data)
    {
        try {
            $amount       = $data['amount'];
            $default_data = [
                'guid'          => $data['order_guid'],
                'proid'         => '',
                'value'         => $amount,
                'cardnum'       => $amount,
                'charge_type'   => 0, //加油卡类型 （1:中石化、2:中石油；默认为1)
                'game_userid'   => $data['recharge_card_id'], //充值卡号 也就是手机号
                'gas_card_name' => '',
                'gas_card_tel'  => $data['mobile'],//订单手机号
            ];
            $data         = array_merge($default_data, $data);
            $db           = new JuheRechargeOrder();
            $db->save($data);
            return $data;
        } catch (Exception $e) {
            $msg = '下单失败:' . $e->getMessage();
            wr_log($msg, 1);
            $this->err_msg = $msg;
            return false;
        }
    }

    /**
     * 提交话费充值
     * @param string $order_guid 订单记录标识
     * @return bool|array
     */
    public function recharge($order_guid)
    {
        $job_name = 'Juhe@mobile_recharge';
        $order    = $this->get_order_note($order_guid);
        //先查询当前是否余额充足
        if (!$this->isEnough($order['value'])) {
            //暂停任务
            job()->set_job_name($job_name)->pause();
            $this->err_msg = '余额不足以充值' . $order['value'] . '元话费~';
            wr_log($this->err_msg);
            return false;
        }
        //余额充足则请求聚合API充值话费
        $result = $this->recharge_api($order['game_userid'], $order['cardnum'], $order['orderid']);
        if ($result !== false) {
            //充值成功,先更新订单表
            $update_data = [
                'status'       => 1, //提交成功
                'third_billno' => $result['sporder_id'],
                'result'       => '提交成功',
            ];
            $this->update_order_note($order_guid, $update_data);
            //更新优惠券发送记录状态
            $note   = new CouponSendNote();
            $data   = ['relation_guid' => $order_guid];
            $map    = [
                ['bid', '=', $order['bid']],
                ['guid', '=', $order['relation_guid']],
            ];
            $update = $note::update($data, $map);
            //最后重启失败任务
            $this->restart_jobs($job_name);
        } else {
            $update_data = [
                'status' => -1, //提交失败
                'result' => $this->err_msg,
            ];
            $this->update_order_note($order_guid, $update_data);
        }
        return $result;
    }

    public function get_order_note($order_guid)
    {
        $db_juhe_order = new JuheRechargeOrder();
        $map           = [
            ['bid', '=', $this->bid],
            ['guid', '=', $order_guid],
        ];
        return $db_juhe_order->where($map)->find();
    }

    /**
     * 充值接口
     * @param string $mobile 手机号
     * @param int $amount 面值(元)
     * @param string $order_id 商户订单号
     * @return bool|array
     */
    public function recharge_api($mobile, $amount, $order_id)
    {
        $url  = 'http://op.juhe.cn/ofpay/mobile/onlineorder';
        $data = [
            "phoneno" => $mobile,
            'cardnum' => $amount,
            'orderid' => $order_id,
            'sign'    => $this->getSignature($mobile . $amount . $order_id)
        ];
        return $this->callPostApi($url, $data);
    }

    public function update_order_note($order_guid, $update_data)
    {
        $db_juhe_order = new JuheRechargeOrder();
        $map           = [
            ['bid', '=', $this->bid],
            ['guid', '=', $order_guid],
        ];
        return $db_juhe_order::update($update_data, $map);
    }

    /**
     * 更新充值订单状态
     * @param string $order_guid 订单号
     * @return bool|array
     */
    public function update_order_status($order_guid)
    {
        $order = $this->get_order_note($order_guid);
        if ($order['third_status'] != 1) {
            $result = $this->query($order['orderid']);
            if ($result) {
                //查询成功则更新状态
                $update_data = [
                    'uordercash'    => $result['uordercash'],
                    'third_billno'  => $result['sporder_id'],
                    'third_status'  => $result['game_state'],
                    'third_message' => $result['err_msg'] ?? '',
                ];
                return $this->update_order_note($order_guid, $update_data);
            }
        }
        return true;
    }

    /**
     * 订单查询接口
     * @param string $order_id 商户订单号
     * @return bool|array
     */
    public function query($order_id)
    {
        $url = 'http://op.juhe.cn/ofpay/mobile/ordersta';
        return $this->callPostApi($url, ['orderid' => $order_id]);
    }

    /**
     * 加油卡充值回调接口
     * @return string
     */
    public function notify()
    {
        try {
            //校验接口
            $notify = $this->verify();
            if ($notify === false) {
                return '';
            }
            $lock_instance = get_distributed_instance();
            $sporder_id    = $notify['sporder_id'];//聚合订单号
            $lock          = $lock_instance->get_lock($sporder_id);
            $order_id      = $notify['orderid']; //商户的单号
            $third_status  = $notify['sta']; //充值状态
            $third_message = $notify['err_msg'] ?? ''; //充值状态
            $db            = new JuheRechargeOrder();
            $map           = [
                ['bid', '=', $this->bid],
                ['orderid', '=', $order_id]
            ];
            //先查询订单
            $order = $db->where($map)->find();
            if (empty($order)) {
                return '';
            }
            if ($order['third_status'] != 1) {
                //状态未成功则更新订单并处理逻辑
                $update_data = [
                    'third_status'  => $third_status,
                    'third_message' => $third_message,
                ];
                $this->update_order_note($order['guid'], $update_data);//更新订单记录
                $mobile = $order['game_userid'];
                if ($notify['success'] === true) {
                    $time_text = date("Y年m月d日h时i分");
                    $msg       = '温馨提示:' . $time_text . ',您已成功充值' . $order['value'] . '元至手机号:' . $mobile . '帐户,请知悉!';
                    send_sms($msg, $mobile, $order['bid']);
                } else {
                    //充值失败,根据自身业务逻辑进行后续处理
                    wr_log('话费:' . $mobile . '充值失败:' . $third_message, 0, $order['bid']);
                }
            }
            $lock_instance->unlock($lock);
            return 'success';
        } catch (Exception $e) {
            wr_log('Juhe_mobile_recharge_notify处理异常:' . $e->getMessage(), 1);
            return '';
        }
    }
}