<?php

// +----------------------------------------------------------------------
// | WeChatDeveloper
// +----------------------------------------------------------------------
// | 版权所有 2014~2018 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 官方网站: http://think.ctolog.com
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/WeChatDeveloper
// +----------------------------------------------------------------------

namespace Juhe\Contracts;

use app\model\JuheRechargeOrder;
use Juhe\Exceptions\InvalidArgumentException;

/**
 * Class BasicWeChat
 * @package WeChat\Contracts
 */
class Basic
{
    /**
     * 当前配置
     * @var DataArray
     */
    public $config;
    public $bid;
    public $openid;
    public $secret;
    public $balance_secret;
    /**
     * 错误信息
     * @var string
     */
    public $err_msg;
    public $err_code;

    /**
     * 当前请求方法参数
     * @var array
     */
    private $currentMethod = [];

    /**
     * 是否重试
     * @var bool
     */
    private $isTry = false;

    /**
     * Basic constructor.
     * @param array $options
     */
    public function __construct(array $options)
    {
        if (empty($options['juheOpenId'])) {
            throw new InvalidArgumentException("Missing Config -- [juheOpenId]");
        }
        if (empty($options['bid'])) {
            throw new InvalidArgumentException("Missing Config -- [bid]");
        }
        $this->openid         = $options['juheOpenId'];
        $this->secret         = $options['app_key'];
        $this->balance_secret = $options['appkey_oil'];
        $this->bid            = $options['bid'];
        $options              = [
            'openid' => $this->openid,
            'secret' => $this->secret,
            'bid'    => $this->bid,
        ];
        $this->config         = new DataArray($options);
    }

    public function getError()
    {
        return $this->err_msg;
    }

    /**
     * @param float $amount 需要充值的金额(元)
     * @return bool
     */
    public function isEnough($amount)
    {
        $balance = $this->getBalance();
        if ($balance === false) {
            return false;
        }
        $juhe_recharge_credit = (int)get_system_config('juhe_recharge_credit');
        $result               = ((float)$balance['money'] - (float)$amount) > $juhe_recharge_credit;
        if ($result === false) {
            //如果余额不足,则写入缓存
            cache($this->getBalanceCacheKey(), time());
            $this->not_enough_notify($this->bid);
        }
        return $result;
    }

    public function getBalance()
    {
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($this->openid);
        $url           = "http://op.juhe.cn/ofpay/sinopec/yue";
        $data          = [
            "timestamp" => time(),
            "key"       => $this->balance_secret,
            "sign"      => strtolower(MD5($this->openid . $this->balance_secret . time()))
        ];
        $result        = $this->callPostApi($url, $data);
        $lock_instance->unlock($lock);
        return $result;
    }

    /**
     * 接口通用POST请求方法
     * @param string $url 接口URL
     * @param array $data POST提交接口参数
     * @return array
     */
    public function callPostApi($url, array $data)
    {
        $data['key'] = $data['key'] ?? $this->secret;
        $this->registerApi(__FUNCTION__, func_get_args());
        return $this->_parseResult($this->httpPost($url, $data));
    }

    /**
     * 注册当前请求接口
     * @param string $method 当前接口方法
     * @param array $arguments 请求参数
     * @return mixed
     */
    protected function registerApi($method, $arguments = [])
    {
        $this->currentMethod = ['method' => $method, 'arguments' => $arguments];
        return $this;
    }

    /**
     * 解析返回的结果
     * @param array $result
     * @return bool|array
     */
    protected function _parseResult($result)
    {
        if (empty($result)) {
            $this->err_msg = '解析返回结果失败';
            return false;
        }
        if ($result['error_code'] !== 0) {
            $this->err_code = $result['error_code'];
            $this->err_msg  = $result['reason'];
            return false;
        }
        return (!empty($result['result'])) ? $result['result'] : $result;
    }

    /**
     * 以POST获取接口数据并转为数组
     * @param string $url 接口地址
     * @param array $data 请求数据
     * @return array
     */
    protected function httpPost($url, array $data)
    {
        return curl()->form_params()->post($url, $data)->get_body();
    }

    /**
     * 获取余额缓存key
     * @return string
     */
    public function getBalanceCacheKey()
    {
        return 'juhe:balance:' . $this->openid;
    }

    protected function not_enough_notify($bid)
    {
        $key           = 'juhe:not_enough_juhe_notify:' . $bid;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        //获得分布式锁之后开始获取判断是否存在缓存
        if (cache($key)) {
            //如果存在缓存则不通知
            $lock_instance->unlock();
            return;
        }
        cache($key, format_timestamp(), 3600 * 2); //2小时内不重复通知
        $db                      = new JuheRechargeOrder();
        $Wait_for_recharge_money = $db->get_wait_for_recharge_money($bid);
        send_sms('聚合平台余额已经不足,请及时充值,当前等待充值:' . intval($Wait_for_recharge_money) . '元', '13178551606', $bid);
        $lock_instance->unlock($lock);
    }

    /**
     * 重启任务
     * @param string $job_name 任务名
     * @return mixed
     */
    public function restart_jobs($job_name)
    {
        $is_not_not_enough = cache($this->getBalanceCacheKey());
        if ($is_not_not_enough) {
            cache($this->getBalanceCacheKey(), null);
            job()->set_queue_name('failed')->set_job_name('Juhe@' . $job_name)->restart();
            wr_log($job_name . ' queue restart success', 1);
        }
    }

    /**
     * 接受话费\加油卡\流量充值业务 异步通知参数 参考示例
     * @return bool|array
     */
    public function verify()
    {
        $request    = request();
        $params     = $request->param();
        $local_sign = md5($this->secret . $params['sporder_id'] . $params['orderid']); //本地sign校验值
        if ($local_sign === $params['sign']) {
            $params['success'] = $params['sta'] == 1 ? true : false;
            wr_log('收到聚合API推送,订单号:' . $params['sporder_id']);
            return $params;
        }
        return false;
    }

    /**
     * 接口通用GET请求方法
     * @param string $url 接口URL
     * @return array
     */
    public function callGetApi($url, array $data = [])
    {
        $data['key'] = $this->secret;
        $this->registerApi(__FUNCTION__, func_get_args());
        return $this->_parseResult($this->httpGet($url, $data));
    }

    /**
     * 以GET获取接口数据并转为数组
     * @param string $url 接口地址
     * @param array $data 请求数据
     * @return array
     */
    protected function httpGet($url, array $data = [])
    {
        return curl()->get($url, $data)->get_body();
    }

    /**
     * 获取签名
     * @param string $string 签名字符串
     * @return string 签名值
     */
    protected function getSignature($string)
    {
        return strtolower(MD5($this->openid . $this->secret . $string));
    }

    /**
     * 重试方法
     * @return bool|mixed
     */
    protected function retry()
    {
        if (!$this->isTry) {
            $this->isTry = true;
            return call_user_func_array([$this, $this->currentMethod['method']], $this->currentMethod['arguments']);
        }
        return false;
    }
}