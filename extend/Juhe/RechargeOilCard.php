<?php

// +----------------------------------------------------------------------
// | WeChatDeveloper
// +----------------------------------------------------------------------
// | 版权所有 2014~2018 广州楚才信息科技有限公司 [ http://www.cuci.cc ]
// +----------------------------------------------------------------------
// | 官方网站: http://think.ctolog.com
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zoujingli/WeChatDeveloper
// +----------------------------------------------------------------------

namespace Juhe;

use app\model\JuheRechargeOrder;
use Exception;
use Juhe\Contracts\Basic;
use Juhe\Exceptions\InvalidArgumentException;

/**
 * 油卡充值
 * Class Card
 * @package WeChat
 */
class RechargeOilCard extends Basic
{
    /**
     * BasicWeChat constructor.
     * @param array $options
     */
    public function __construct(array $options)
    {
        if (empty($options['appkey_oil'])) {
            throw new InvalidArgumentException("Missing Config -- [appkey_oil]");
        }
        $options['app_key'] = $options['appkey_oil'];
        parent::__construct($options);
    }

    /**
     * 提交加油卡充值
     * @param string $order_guid 订单记录标识
     * @return bool|array
     */
    public function recharge($order_guid)
    {
        $job_name = 'Juhe@recharge';
        $order    = $this->get_order_note($order_guid);
        if (!tools()::is_oil_card_id($order['game_userid'])) {
            $this->err_msg = '油卡号错误,请核对!';
            $update_data   = [
                'status' => -1, //提交失败
                'result' => $this->err_msg,
            ];
            $this->update_order_note($order_guid, $update_data);
            return false;
        }
        //       if (get_system_config('pause_juhe_recharge') == 1) {
        //           $this->err_msg = '本地系统维护,暂停充值!';
        //           return false;
        //       }
        //先查询当前是否余额充足
        if (!$this->isEnough($order['value'])) {
            $this->err_msg = '余额不足以充值' . $order['value'] . '元油卡~';
            $update_data   = [
                'result' => '当前余额不足',
            ];
            $this->update_order_note($order_guid, $update_data);
            //暂停任务
            job()->set_job_name($job_name)->pause();
            wr_log($this->err_msg);
            return false;
        }
        //余额充足则请求聚合API充值油卡
        $data   = [
            'proid'       => $order['proid'],
            'cardnum'     => $order['cardnum'],
            'orderid'     => $order['orderid'],
            'game_userid' => $order['game_userid'],
            'gasCardTel'  => $order['gas_card_tel'],
            'gasCardName' => $order['gas_card_name'],      //持卡人姓名,选填
            'chargeType'  => $order['charge_type'],         //加油卡类型 （1:中石化、2:中石油；默认为1)
        ];
        $result = $this->recharge_api($data);
        if ($result !== false) {
            //充值成功,先更新订单表
            $update_data = [
                'status'       => 1, //提交成功
                'third_billno' => $result['sporder_id'],
                'result'       => '提交成功',
            ];
            $this->update_order_note($order_guid, $update_data);
            //最后重启失败任务
            //$this->restart_jobs($job_name);
        } else {
            $update_data = [
                'status' => -1, //提交失败
                'result' => $this->err_msg,
            ];
            $this->update_order_note($order_guid, $update_data);
        }
        return $result;
    }

    public function get_order_note($order_guid)
    {
        $db_juhe_order = new JuheRechargeOrder();
        $map           = [
            ['bid', '=', $this->bid],
            ['guid', '=', $order_guid],
        ];
        return $db_juhe_order->where($map)->findOrEmpty();
    }

    public function update_order_note($order_guid, $update_data)
    {
        $db_juhe_order = new JuheRechargeOrder();
        $map           = [
            ['bid', '=', $this->bid],
            ['guid', '=', $order_guid],
        ];
        return $db_juhe_order::update($update_data, $map);
    }

    /**
     * 充值接口
     * @param array $data 数据包
     * @return bool|array
     */
    public function recharge_api($data)
    {
        $url          = 'http://op.juhe.cn/ofpay/sinopec/onlineorder';
        $data['sign'] = $this->getSignature($data['proid'] . $data['cardnum'] . $data['game_userid'] . $data['orderid']);
        return $this->callPostApi($url, $data);
    }

    /**
     * 预下单方法
     * @param array $data 数据包
     * @return bool|array
     */
    public function apply($data)
    {
        try {
            $amount       = $data['amount'];
            $game_userid  = $data['recharge_card_id'];
            $proid        = self::get_oil_proid($game_userid, $amount);
            $default_data = [
                'guid'          => $data['order_guid'],
                'proid'         => $proid,
                'value'         => $amount,
                'cardnum'       => ($proid == 10008) ? $amount : 1,
                'charge_type'   => ($proid == 10008) ? 2 : 1, //加油卡类型 （1:中石化、2:中石油；默认为1)
                'game_userid'   => $game_userid,
                'gas_card_name' => '',
                'gas_card_tel'  => $data['mobile'],
            ];
            $data         = array_merge($default_data, $data);
            $db           = new JuheRechargeOrder();
            $db->save($data);
            return $data;
        } catch (Exception $e) {
            $msg = '下单失败:' . $e->getMessage();
            wr_log($msg, 1);
            $this->err_msg = $msg;
            return false;
        }
    }

    /**
     * 通过油卡号获得产品id
     * @param string $card_id 油卡号
     * @param int $value 充值金额
     * @return int
     */
    protected static function get_oil_proid($card_id, $value)
    {
        if (tools()::start_with($card_id, 9)) {
            return 10008;
        }
        $value = (int)$value;
        switch ($value) {
            case 50:
                return 10000;
            case 100:
                return 10001;
            case 200:
                return 10002;
            case 500:
                return 10003;
            case 1000:
                return 10004;
            default:
                return false;
        }
    }

    /**
     * 更新充值订单状态
     * @param string $order_guid 订单号
     * @return bool|array
     */
    public function update_order_status($order_guid)
    {
        $order = $this->get_order_note($order_guid);
        if ($order['third_status'] != 1) {
            $result = $this->query($order['orderid']);
            if ($result) {
                //查询成功则更新状态
                $update_data = [
                    'uordercash'    => $result['uordercash'],
                    'third_billno'  => $result['sporder_id'],
                    'third_status'  => $result['game_state'],
                    'third_message' => $result['err_msg'],
                ];
                return $this->update_order_note($order_guid, $update_data);
            }
        }
        return true;
    }

    /**
     * 加油卡充值状态查询
     * @param string $order_id 订单号
     * @return bool|array
     */
    public function query($order_id)
    {
        $url = 'http://op.juhe.cn/ofpay/sinopec/ordersta';
        return $this->callPostApi($url, ['orderid' => $order_id]);
    }

    /**
     * 加油卡充值回调接口
     * @return string
     */
    public function notify()
    {
        $notify = $this->verify();
        if ($notify === false) {
            return '';
        }
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($notify['sporder_id']);
        try {
            $order_id      = $notify['orderid']; //商户的单号
            $third_status  = $notify['sta']; //充值状态
            $third_message = $notify['err_msg']; //充值状态
            $db            = new JuheRechargeOrder();
            $map           = [
                ['bid', '=', $this->bid],
                ['orderid', '=', $order_id]
            ];
            //先查询订单
            $order = $db->where($map)->find();
            if (empty($order)) {
                return '';
            }
            if ($order['third_status'] != 1) {
                //状态未成功则更新订单并处理逻辑
                $update_data = [
                    'third_status'  => $third_status,
                    'third_message' => $third_message,
                ];
                $this->update_order_note($order['guid'], $update_data);//更新订单记录
                $game_user_id_suffix = substr($order['game_userid'], -4, 4); //油卡尾号
                if ($notify['success'] === true) {
                    $time_text = date("Y年m月d日h时i分");
                    $msg       = '温馨提示:' . $time_text . ',您已成功充值' . $order['value'] . '元至油卡尾号' . $game_user_id_suffix . '帐户,请知悉!';
                    send_sms($msg, $order['mobile'], $order['bid']);
                } else {
                    //充值失败,根据自身业务逻辑进行后续处理
                    wr_log('油卡号:' . $order['game_userid'] . '充值失败:' . $third_message, 0, $order['bid']);
                }
            }
            $lock_instance->unlock($lock);
            return 'success';
        } catch (Exception $e) {
            wr_log('Juhe_oil_recharge_notify处理异常:' . $e->getMessage(), 1);
            $lock_instance->unlock($lock);
            return '';
        }
    }
}