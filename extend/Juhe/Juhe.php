<?php


namespace Juhe;


/**
 * Class BasicWeChat
 * @package WeChat\Contracts
 */


/**
 * 聚合数据服务
 * @package service
 * <AUTHOR> <<EMAIL>>
 * @date 2018/05/19
 *
 * @method RechargeMobile RechargeMobile($config) static 话费充值
 * @method RechargeOilCard RechargeOilCard($config) static 油卡充值
 */
class Juhe
{

    /**
     * 静态初始化对象
     * @param array $config
     * @param int $type
     * @return RechargeOilCard|RechargeMobile
     */
    public static function get_instance($config, $type)
    {
        $array = ['RechargeOilCard', 'RechargeMobile'];
        if (!empty($array[$type - 1])) {
            return self::instance($array[$type - 1], $config);
        }
    }

    /**
     * 创建指定聚合接口实例
     * @param string $name 需要加载的接口实例名称
     * @param array $config 参数
     * @return mixed
     */
    public static function instance($name, $config)
    {
        $class_name = __NAMESPACE__ . "\\" . $name;
        if (class_exists($class_name)) {
            return new $class_name($config);
        }
    }

    /**
     * 静态初始化对象
     * @param string $name
     * @param array $arguments
     * @return RechargeOilCard|RechargeMobile
     */
    public static function __callStatic($name, $arguments)
    {
        return self::instance($name, isset($arguments[0]) ? $arguments[0] : []);
    }
}