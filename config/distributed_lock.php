<?php
// +----------------------------------------------------------------------
// | 节流设置
// +----------------------------------------------------------------------
return [
    // 缓存键前缀，防止键值与其他应用冲突
    'prefix' => 'distributed_lock_',
    // list的key需要分布式锁的方法, value为需要的参数为空则默认使用当前请求路径作为key
    'list'   => [
        //'app\test\controller\Index@index'                               => ['id'],
        'app\api\controller\v1\Merchant@find_appid_by_business_account' => [],
        'app\api\controller\v1\Merchant@update_pay_appid_status'        => [],
        'app\api\controller\v1\Merchant@update_rate_list'               => [],
        'app\api\controller\v1\Merchant@report_cookies'                 => [],
        'app\controller\member_api\v1\Merchant@apply_change_rate'       => ['sub_mch_id'],
        'app\controller\member_api\v1\CouponExchange@money'             => ['code'],
        'app\controller\member_api\v1\CouponExchange@exchange'          => ['code'],
        'app\controller\member_api\v1\CouponExchange@wechat'            => ['code'],
        //2023年维护member_api
        'app\controller\member_api\v1\GoodsOrder@submit'                => ['_bid', '_member_guid'],
        'app\controller\member_api\v1\Code@submit_order'                => ['_bid', '_member_guid'],

        //2023年维护admin_api
        'app\controller\member_api\v1\Business@recharge'                => ['guid'],
        'app\controller\member_api\v1\Member@recharge'                  => ['_bid', 'member_guid'],
        'app\controller\member_api\v1\Member@recharge_point'            => ['_bid', 'member_guid'],
        'app\controller\member_api\v1\Member@deduct_money'              => ['_bid', 'member_guid'],
        'app\controller\member_api\v1\GoodsOrder@send_out_goods'        => ['_bid'],
        'app\controller\member_api\v1\GoodsOrder@refund'                => ['_bid', 'guid'],
    ],
];