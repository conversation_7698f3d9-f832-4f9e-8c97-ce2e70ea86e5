/*
 Navicat Premium Data Transfer

 Source Server         : (主库)***********
 Source Server Type    : MySQL
 Source Server Version : 50732 (5.7.32-log)
 Source Host           : ***********:3306
 Source Schema         : platform

 Target Server Type    : MySQL
 Target Server Version : 50732 (5.7.32-log)
 File Encoding         : 65001

 Date: 06/03/2023 16:39:02
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for alipay_config
-- ----------------------------
DROP TABLE IF EXISTS `alipay_config`;
CREATE TABLE `alipay_config`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_id`            bigint(20) NOT NULL COMMENT '应用ID',
    `app_auth_token`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户授权令牌,通过该令牌来帮助商户发起请求，完成业务',
    `user_id`           bigint(20) NOT NULL DEFAULT 0 COMMENT '授权商户的ID',
    `auth_app_id`       bigint(20) NOT NULL COMMENT '授权商户的AppId（如果有服务窗，则为服务窗的AppId）',
    `expires_in`        int(11) NOT NULL DEFAULT 0 COMMENT '令牌有效期',
    `re_expires_in`     int(11) NOT NULL DEFAULT 0 COMMENT '刷新令牌有效期',
    `app_refresh_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '刷新令牌时使用,刷新令牌后，我们会保证老的app_auth_token从刷新开始10分钟内可继续使用，请及时替换为最新token',
    `app_auth_info`     json NULL COMMENT '授权信息',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 禁用',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `app_id`(`app_id`, `auth_app_id`) USING BTREE COMMENT '同一个支付宝ID唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for anjufang_rank_note
-- ----------------------------
DROP TABLE IF EXISTS `anjufang_rank_note`;
CREATE TABLE `anjufang_rank_note`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `city_rank`     int(11) NOT NULL DEFAULT 0 COMMENT '市排名',
    `district_rank` int(11) NOT NULL DEFAULT 0 COMMENT '区排名',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for api_log
-- ----------------------------
DROP TABLE IF EXISTS `api_log`;
CREATE TABLE `api_log`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `url`          varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `user_agent`   varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `referer`      varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `header`       longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `cookies`      longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `request_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `method`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `parameter`    longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `result`       longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `ip`           char(15) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '',
    `request_time` datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX          `request_time`(`request_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2774342 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for appid_openid_mapping
-- ----------------------------
DROP TABLE IF EXISTS `appid_openid_mapping`;
CREATE TABLE `appid_openid_mapping`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商家标识',
    `sid`                 bigint(20) NOT NULL DEFAULT 0 COMMENT '商家id',
    `member_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '会员标识',
    `member_id`           bigint(20) NOT NULL DEFAULT 0 COMMENT '会员id',
    `appid`               char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT 'APPID',
    `openid`              char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'OPENID',
    `appid_type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 公众号 2 小程序',
    `relation_appid`      char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '关联APPID',
    `relation_openid`     char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '关联OPENID',
    `relation_appid_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 公众号 2 小程序',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1959 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for area
-- ----------------------------
DROP TABLE IF EXISTS `area`;
CREATE TABLE `area`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `pid`       int(11) NOT NULL DEFAULT 0 COMMENT '父ID',
    `city_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '区号',
    `ad_code`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '行政代码',
    `name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
    `lng`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '经度',
    `lat`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '纬度',
    `level`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3266 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '高德行政区域数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for area_rule
-- ----------------------------
DROP TABLE IF EXISTS `area_rule`;
CREATE TABLE `area_rule`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `name`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '规则名称',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `type`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型 1 可选 0不可选',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 停用',
    `memo`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`       datetime(3) NOT NULL COMMENT '创建日期',
    `update_time`       datetime(3) NOT NULL COMMENT '更新日期',
    `rule_type`         tinyint(2) NOT NULL DEFAULT 0 COMMENT '规则类型 暂时未启用, 均是指定具体省市',
    `area_id_list`      json NULL COMMENT '具体省市区id',
    `limit_coupon_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '限制卡券类型 0 不限制 1 限制',
    `coupon_item_guid`  json NULL COMMENT '优惠券明细guid',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `title`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `content`     text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容html',
    `key`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标识',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auction_activity
-- ----------------------------
DROP TABLE IF EXISTS `auction_activity`;
CREATE TABLE `auction_activity`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `title`               varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `goods_category_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '拍卖商品分类',
    `article_guid_json`   json                                                    NOT NULL COMMENT '文章列表',
    `article_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '模板标识',
    `number`              int(10) NOT NULL DEFAULT 0 COMMENT '当天第几笔',
    `file_url`            varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '原始文件url',
    `description`         text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '富文本说明',
    `offer_count`         int(10) NOT NULL DEFAULT 0 COMMENT '出价人数',
    `status`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 下线 1 上线',
    `pic`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片地址',
    `amount`              int(10) NOT NULL COMMENT '商品总件数',
    `begin_time`          datetime NULL DEFAULT NULL COMMENT '活动开始时间',
    `end_time`            datetime NULL DEFAULT NULL COMMENT '活动结束时间',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auction_goods
-- ----------------------------
DROP TABLE IF EXISTS `auction_goods`;
CREATE TABLE `auction_goods`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `no`             varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '编号',
    `brand`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '品牌',
    `type`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '类型',
    `model`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '型号',
    `memory`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '内存',
    `colour`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '颜色',
    `standard`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '制式',
    `carrier`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '运营商',
    `condition`      varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '成色',
    `number`         int(10) NOT NULL DEFAULT 0 COMMENT '序号',
    `activity_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '活动标识',
    `name`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
    `remark`         varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `amount`         int(10) NOT NULL DEFAULT 0 COMMENT '数量',
    `starting_price` decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '参考起拍价格',
    `create_time`    datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 26876 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auction_goods_order
-- ----------------------------
DROP TABLE IF EXISTS `auction_goods_order`;
CREATE TABLE `auction_goods_order`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `goods_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `express_no`  varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递单号',
    `express`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递名称',
    `offer_price` decimal(10, 0) NULL DEFAULT 0 COMMENT '出价价格',
    `result`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '原因',
    `status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 -1 竞拍失败 0竞拍中 1 竞拍成功',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auth
-- ----------------------------
DROP TABLE IF EXISTS `auth`;
CREATE TABLE `auth`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '归属代理商',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `salt`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '生成token的加盐',
    `token`        varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '根据IP+域名+时间生成的jwt密钥',
    `domain`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '授权域名',
    `ip`           char(15) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '授权IP',
    `expired_time` datetime NULL DEFAULT NULL COMMENT '授权到期时间',
    `status`       tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 0 禁用 1 正常',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    `memo`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `domain_ip`(`domain`, `ip`) USING BTREE COMMENT '域名+IP 唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auth_verify
-- ----------------------------
DROP TABLE IF EXISTS `auth_verify`;
CREATE TABLE `auth_verify`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请求ID',
    `url`                  varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '访问的URL',
    `method`               char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求方式',
    `parameter`            json NULL COMMENT '请求参数',
    `header_info`          json NULL COMMENT 'http头信息',
    `server_info`          json NULL COMMENT 'http请求信息',
    `http_client_ip`       varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'http头IP',
    `remote_addr`          varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'tcp层IP',
    `http_via`             varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '代理ip',
    `http_forwarded`       varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `http_x_forwarded`     varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `http_x_forwarded_for` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `x_version`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商城版本',
    `x_request_info`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径信息',
    `x_key`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求密钥',
    `x_plugins`            json NULL COMMENT '商城插件',
    `x_domain`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商城域名',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '验证结果 0 未通过 1 通过',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9238 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bank_list
-- ----------------------------
DROP TABLE IF EXISTS `bank_list`;
CREATE TABLE `bank_list`
(
    `branch_bank_id`   char(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `branch_bank_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bank_name`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bankid`           char(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `province`         char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `province_id`      char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `city`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `city_id`          char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`branch_bank_id`) USING BTREE,
    INDEX              `id`(`branch_bank_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for banner
-- ----------------------------
DROP TABLE IF EXISTS `banner`;
CREATE TABLE `banner`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `type`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 提货banner\r\n2 卡券商城banner \r\n3产品商城 长图 \r\n4 产品商城 视频\r\n5 卡券提交后轮播图',
    `title`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '标题',
    `img_url`           varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片地址',
    `path`              varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '小程序路径',
    `url`               varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '链接,支持H5,小程序',
    `sort`              tinyint(255) NOT NULL DEFAULT 0 COMMENT '序号,越大越靠前',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 0下线 1上线',
    `limit_coupon_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '限制卡券类型 0 不限制 1 限制',
    `coupon_item_guid`  json NULL COMMENT '关联卡券guid,支持多个',
    `video_url`         varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '视频链接',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 816 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `sort`        int(10) NOT NULL DEFAULT 1 COMMENT '排序,越小越靠前',
    `icon`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '品牌名称',
    `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 0 下线 1 上线',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for business
-- ----------------------------
DROP TABLE IF EXISTS `business`;
CREATE TABLE `business`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '主键',
    `type`                    tinyint(2) NOT NULL DEFAULT 3 COMMENT '1 商家账户 2 代理商账户 3超级管理员',
    `account`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家账号',
    `business_name`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户名称',
    `principal_name`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主体名称',
    `version_guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '版本标识',
    `true_name`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `total_money`             decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '累计充值货款',
    `money`                   decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '剩余货款',
    `mobile`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `province_id`             int(10) NOT NULL DEFAULT 0 COMMENT '省份',
    `city_id`                 int(10) NOT NULL DEFAULT 0 COMMENT '市',
    `area_id`                 int(10) NOT NULL DEFAULT 0 COMMENT '区',
    `email`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系邮箱',
    `total_sms_num`           int(11) NOT NULL DEFAULT 0 COMMENT '累计充值短信',
    `sms_num`                 int(11) NULL DEFAULT 0 COMMENT '可用短信数量',
    `wxapp_admin_id`          int(10) NOT NULL DEFAULT 0 COMMENT '小程序管理账户ID',
    `qq`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系QQ',
    `wechat_openid`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
    `appid`                   int(10) NOT NULL DEFAULT 0 COMMENT '开放平台appid',
    `secret`                  char(32) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '开放平台密钥',
    `license_status`          tinyint(2) NOT NULL DEFAULT 1 COMMENT '1试用版 2正式版',
    `total_license_num`       int(11) NOT NULL DEFAULT 0 COMMENT '累计授权个数',
    `total_translate_num`     int(10) NOT NULL DEFAULT 0 COMMENT '累计翻译授权数',
    `translate_num`           int(10) NOT NULL DEFAULT 0 COMMENT '翻译授权数',
    `total_express_order_num` int(11) NOT NULL COMMENT '累计电子面单授权数量',
    `express_order_num`       int(11) NOT NULL DEFAULT 0 COMMENT '电子面单授权数量',
    `license_num`             int(11) NOT NULL DEFAULT 0 COMMENT '可用授权个数',
    `mall_store_id`           int(10) NOT NULL DEFAULT 0 COMMENT '小程序店铺id',
    `memo`                    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `first_year_fee`          int(11) NOT NULL DEFAULT 0 COMMENT '首年费用',
    `renewal_amount`          int(11) NOT NULL DEFAULT 0 COMMENT '续费金额 元',
    `brokerage_rate`          decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '支付分润比例',
    `expired_time`            datetime                                                NOT NULL COMMENT '账户到期时间',
    `login_background_pic`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录背景图地址',
    `status`                  tinyint(2) NULL DEFAULT 1 COMMENT '状态1正常',
    `last_login_time`         datetime NULL DEFAULT NULL COMMENT '最后登录时间',
    `annual_service_fee`      decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '年服务费',
    `create_time`             datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL COMMENT '更新时间',
    `parent_user_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '跟踪人用户guid',
    `parent_guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父级标识',
    `agent_version_guid_list` json NULL COMMENT '该用户可开通的版本guid列表',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '注销时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `appid`(`appid`) USING BTREE COMMENT 'appid不能重复',
    UNIQUE INDEX `account`(`account`) USING BTREE COMMENT '账号不允许重复',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 630 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for business_recharge_note
-- ----------------------------
DROP TABLE IF EXISTS `business_recharge_note`;
CREATE TABLE `business_recharge_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `recharge_bid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `source`       tinyint(2) NOT NULL DEFAULT 1 COMMENT '来源 1 后台操作 2 在线支付充值',
    `amount`       int(10) NOT NULL DEFAULT 0,
    `type`         tinyint(2) NOT NULL COMMENT '1 余额 2 短信',
    `balance`      int(10) NOT NULL DEFAULT 0 COMMENT '充值后余额',
    `memo`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注信息',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 415 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for business_renew_note
-- ----------------------------
DROP TABLE IF EXISTS `business_renew_note`;
CREATE TABLE `business_renew_note`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '被续期的商家sid',
    `operator_bid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作商家 bid',
    `operator_user_id`    int(11) NOT NULL DEFAULT 0 COMMENT '操作工号id',
    `operator_user_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作工号guid',
    `before_expired_time` datetime NULL DEFAULT NULL,
    `after_expired_time`  datetime NULL DEFAULT NULL,
    `source`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 后台操作 2 自助续费',
    `renew_days`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `memo`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`         datetime(3) NULL DEFAULT NULL,
    `update_time`         datetime(3) NULL DEFAULT NULL,
    `type`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 正式购买 2 临时延期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 627 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for business_rule
-- ----------------------------
DROP TABLE IF EXISTS `business_rule`;
CREATE TABLE `business_rule`
(
    `business_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `rule_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限标识',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    UNIQUE INDEX `bid_rule_guid`(`business_guid`, `rule_guid`) USING BTREE COMMENT '商家&权限唯一'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家&权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for business_version
-- ----------------------------
DROP TABLE IF EXISTS `business_version`;
CREATE TABLE `business_version`
(
    `business_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `version_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '功能包标识 version表type=2',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    UNIQUE INDEX `bid_rule_guid`(`business_guid`, `version_guid`) USING BTREE COMMENT '商家&权限唯一'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家&权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cart
-- ----------------------------
DROP TABLE IF EXISTS `cart`;
CREATE TABLE `cart`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `goods_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品标识',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员标识',
    `attr`        json NULL,
    `amount`      int(10) NOT NULL COMMENT '商品数量',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 5030 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for changan_order
-- ----------------------------
DROP TABLE IF EXISTS `changan_order`;
CREATE TABLE `changan_order`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `order_no`    char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `data`        json                                                NOT NULL,
    `create_time` datetime(3) NOT NULL,
    `update_time` datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17023 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cms_member
-- ----------------------------
DROP TABLE IF EXISTS `cms_member`;
CREATE TABLE `cms_member`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `true_name`    char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `ship_number`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '保单号',
    `plate_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '车牌号',
    `mobile`       char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `openid`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for consume_queue_note
-- ----------------------------
DROP TABLE IF EXISTS `consume_queue_note`;
CREATE TABLE `consume_queue_note`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT 'guid主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '商家标识',
    `bill_number`      char(30) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '消费单据号',
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '会员登记门店guid',
    `store_name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员登记门店名称',
    `relation_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '返现关联订单标识',
    `reward_money`     decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '返现金额',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '会员标识',
    `openid`           char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '用户openid',
    `card_id`          char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员卡号',
    `true_name`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员姓名',
    `head_img`         varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员头像',
    `number`           bigint(20) NOT NULL DEFAULT 0 COMMENT '排号',
    `multiple`         int(10) NOT NULL DEFAULT 0 COMMENT '倍数',
    `reward_number`    bigint(20) NOT NULL DEFAULT 0 COMMENT '排号XX消费后将返回本单',
    `status`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态-2 已撤销  -1  返还失败  0 待返还 1 返还成功',
    `message`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `consume_time`     datetime(3) NOT NULL COMMENT '消费时间',
    `reward_time`      datetime(3) NULL DEFAULT NULL COMMENT '奖励时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `third_bill_no`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '返现订单号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT '主键'
) ENGINE = MyISAM AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coronavirus_province
-- ----------------------------
DROP TABLE IF EXISTS `coronavirus_province`;
CREATE TABLE `coronavirus_province`
(
    `last_update_date` date NULL DEFAULT '2020-01-01',
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `province_pinyin`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `province_name`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`      datetime(3) NULL DEFAULT NULL,
    `update_time`      datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coronavirus_province_data_item
-- ----------------------------
DROP TABLE IF EXISTS `coronavirus_province_data_item`;
CREATE TABLE `coronavirus_province_data_item`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `province_pinyin` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `date`            date                                                    NOT NULL COMMENT '日期',
    `create_time`     datetime(3) NULL DEFAULT NULL,
    `update_time`     datetime(3) NULL DEFAULT NULL,
    `con_num`         int(11) NULL DEFAULT NULL COMMENT '累计确诊',
    `sus_num`         int(11) NULL DEFAULT NULL,
    `cure_num`        int(11) NULL DEFAULT NULL COMMENT '累计治愈',
    `death_num`       int(11) NULL DEFAULT NULL COMMENT '累计死亡',
    `econ_num`        int(11) NULL DEFAULT NULL COMMENT '现存确诊',
    `loc_asym_num`    int(11) NULL DEFAULT NULL COMMENT '新增本土无症状',
    `loc_incr_num`    int(11) NULL DEFAULT NULL COMMENT '新增本土确诊',
    `asymptom_num`    int(11) NULL DEFAULT NULL,
    `conadd`          int(11) NULL DEFAULT NULL COMMENT '累计确诊比较昨日变化',
    `cureadd`         int(11) NULL DEFAULT NULL COMMENT '累计治愈比较昨日变化',
    `deathadd`        int(11) NULL DEFAULT NULL COMMENT '累计死亡比较昨日变化',
    `susadd`          int(11) NULL DEFAULT NULL,
    `asymptomadd`     int(11) NULL DEFAULT NULL,
    `jwsr_num`        int(11) NULL DEFAULT NULL COMMENT '境外输入人员累计确诊',
    `jwsradd`         int(11) NULL DEFAULT NULL COMMENT '境外输入人员新增确诊',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_province_pinyin_date`(`province_pinyin`, `date`) USING BTREE,
    INDEX             `province_pinyin`(`province_pinyin`) USING BTREE,
    INDEX             `date`(`date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38186 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon
-- ----------------------------
DROP TABLE IF EXISTS `coupon`;
CREATE TABLE `coupon`
(
    `id`                               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `name`                             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券名称',
    `banner_pic`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '横图',
    `pic`                              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '图片',
    `pic1`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pic2`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pic3`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `video_url`                        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '视频链接',
    `sort`                             int(10) NOT NULL DEFAULT 1 COMMENT '排序,越小越靠前',
    `send_num`                         int(11) NOT NULL DEFAULT 1 COMMENT '可提货总次数(目前用于电子卡密)',
    `exchange_goods_num`               int(11) NOT NULL DEFAULT 0 COMMENT '单次可提货产品数量',
    `goods_item_guid`                  json NULL COMMENT '关联产品guid',
    `category_guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类别guid',
    `share_status`                     tinyint(2) NULL DEFAULT 0 COMMENT '支持转赠 0 关闭 1开启',
    `date_rule_guid`                   json NULL COMMENT '日期规则guid数组,如果为空则执行日期规则,不能任意选择',
    `type`                             tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型 1 产品(提货卡-数量) 2 产品(提货卡-金额) 3 充值卡,不限制商品',
    `require_pay`                      tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否需要支付(用于关联产品后提货支付用) 1 需要 0无需支付(已废弃)',
    `cycle_delivery_min_interval_days` int(10) NOT NULL DEFAULT 0 COMMENT '周期配送最小间隔天数',
    `cycle_delivery`                   tinyint(2) NOT NULL DEFAULT 0 COMMENT '周期配送 0 关闭 1开启',
    `available_amount`                 int(11) NOT NULL DEFAULT 0 COMMENT '可用数量,库存',
    `selling_original_price`           decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '销售原价(默认0 不展示)',
    `active_price`                     decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '激活价格(0代表不允许付费激活)',
    `selling_price`                    decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '售价',
    `value`                            decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '面值',
    `operator_user_id`                 int(11) NOT NULL COMMENT '创建者id',
    `single_openid_max_exchange_times` int(10) NOT NULL DEFAULT 0 COMMENT '单个微信openid最大领取次数,0代表不限制',
    `expire_time`                      datetime NULL DEFAULT NULL COMMENT '有效期',
    `used_rate_limit_type`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 不限制 1 每天 2每周 3每月 4每季度 5每年',
    `used_rate_limit_num`              int(11) NOT NULL DEFAULT 0 COMMENT '周期内限制使用数量, 大于0才有意义',
    `description`                      text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
    `send_type`                        tinyint(2) NOT NULL DEFAULT 1 COMMENT ' 1 在线销售 2 免费领取  4 仅仅做展示',
    `online_status`                    tinyint(2) NOT NULL DEFAULT 1 COMMENT '上线状态',
    `status`                           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0 禁用',
    `delete_time`                      datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`                      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                      datetime(3) NOT NULL COMMENT '更新时间',
    `share_reward_money`               decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '分销奖励金额',
    `everyday_exchange_limit_type`     tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 不限制 1 限制',
    `everyday_exchange_limit_num`      int(10) NOT NULL DEFAULT 0 COMMENT '每日限制兑换数量',
    `receive_rate_limit_type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 不限制 1每天 2 每周 3 每月 4每季度 5每年 6 累计限制',
    `receive_rate_limit_num`           int(10) NOT NULL DEFAULT 0 COMMENT '限制领取次数 0 代表不限制',
    `coupon_used_reward_money`         decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '核销赠送储值金额',
    `simple_description`               text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '简介,会展示在提货页面',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1605 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_active_order
-- ----------------------------
DROP TABLE IF EXISTS `coupon_active_order`;
CREATE TABLE `coupon_active_order`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `user_id`               int(10) NOT NULL DEFAULT 0 COMMENT '用户id',
    `user_guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `total_money`           decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '订单总额',
    `coupon_code`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '卡号',
    `bill_number`           char(100) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '订单号',
    `coupon_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '优惠券标识',
    `coupon_send_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '优惠券发送记录标识',
    `appid`                 char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT 'appid',
    `openid`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
    `remark`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态  -1 待支付  1 已支付',
    `third_order_number`    char(100) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '三方支付单号, pay_order 中的bill_number',
    `pay_time`              datetime NULL DEFAULT NULL COMMENT '支付成功时间',
    `pay_type`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付类型 1 微信 ',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 745 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_apply_note
-- ----------------------------
DROP TABLE IF EXISTS `coupon_apply_note`;
CREATE TABLE `coupon_apply_note`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家唯一标识',
    `bill_number`     bigint(20) NOT NULL COMMENT '订单号',
    `status`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0  待审核 1 审核通过',
    `total_money`     decimal(18, 4)                                      NOT NULL COMMENT '总价值',
    `total_amount`    int(11) NOT NULL COMMENT '申请总张数',
    `apply_time`      datetime                                            NOT NULL COMMENT '申请时间',
    `apply_user_id`   int(11) NOT NULL COMMENT '申请人',
    `examine_time`    datetime NULL DEFAULT NULL COMMENT '审核时间',
    `examine_user_id` int(11) NULL DEFAULT NULL COMMENT '生成者',
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_bid_bill_number`(`bid`, `bill_number`) USING BTREE COMMENT '订单号唯一',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_apply_note_item
-- ----------------------------
DROP TABLE IF EXISTS `coupon_apply_note_item`;
CREATE TABLE `coupon_apply_note_item`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家唯一标识',
    `true_name`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '姓名',
    `mobile`                 char(11) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '手机号',
    `coupon_apply_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '申请记录唯一标识',
    `coupon_guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '优惠券唯一标识',
    `amount`                 int(11) NOT NULL COMMENT '张数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 163 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_category
-- ----------------------------
DROP TABLE IF EXISTS `coupon_category`;
CREATE TABLE `coupon_category`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `parent_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '父类',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `alias`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '别名',
    `mini_pic`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小图',
    `pic`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类别图片',
    `name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类别名称',
    `description`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `sort`             int(10) NOT NULL DEFAULT 0 COMMENT '序号,越大越靠前',
    `status`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user_id`   int(10) NOT NULL DEFAULT 0 COMMENT '创建用户id',
    `create_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '创建用户guid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 471 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_generate_note
-- ----------------------------
DROP TABLE IF EXISTS `coupon_generate_note`;
CREATE TABLE `coupon_generate_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `batch`                bigint(20) NULL DEFAULT 0 COMMENT '批次码2018010100001递增格式',
    `coupon_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '券唯一标识',
    `skip_number`          varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '跳过数字,多个用逗号隔开',
    `start_serial_number`  int(11) NULL DEFAULT NULL COMMENT '起始卡号',
    `end_serial_number`    int(11) NULL DEFAULT NULL COMMENT '截止卡号',
    `way`                  tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 系统生成 2 批量导入 3 申请制卡',
    `file_url`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '导入excel的时候文件地址 冗余下便于排查问题',
    `send_value`           decimal(18, 2)                                          NOT NULL DEFAULT 0.00,
    `send_num`             int(10) NOT NULL DEFAULT 1 COMMENT '可兑换总次数',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 -1 待激活  0 已激活',
    `send_remark`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '生成备注',
    `prefix`               varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '前缀',
    `amount`               int(11) NOT NULL COMMENT '生成数量',
    `apply_time`           datetime NULL DEFAULT NULL COMMENT '申请时间',
    `apply_user_id`        int(11) NOT NULL COMMENT '申请人',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联guid,如申请记录guid',
    `owner_user_id`        int(11) NULL DEFAULT NULL COMMENT '归属',
    `operator_user_id`     int(11) NULL DEFAULT NULL COMMENT '生成者',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `availability_time`    datetime NULL DEFAULT NULL COMMENT '生效时间',
    `used_rate_limit_num`  int(10) NOT NULL DEFAULT 0 COMMENT '周期内限制使用数量, 大于0才有意义',
    `used_rate_limit_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 不限制 1 每天 2每周 3每月 4每季度 5每年',
    `expire_time`          datetime NULL DEFAULT NULL COMMENT '过期时间',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `safe_level`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '安全等级 0 未保护 1 保护中',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `bid_start_code`(`bid`, `start_serial_number`, `prefix`) USING BTREE,
    UNIQUE INDEX `bid_end_code`(`bid`, `end_serial_number`, `prefix`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2661 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_goods_item
-- ----------------------------
DROP TABLE IF EXISTS `coupon_goods_item`;
CREATE TABLE `coupon_goods_item`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家id',
    `goods_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品guid',
    `coupon_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '卡券guid',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `bid_goods_coupon`(`bid`, `goods_guid`, `coupon_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16326 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_plan
-- ----------------------------
DROP TABLE IF EXISTS `coupon_plan`;
CREATE TABLE `coupon_plan`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `name`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '方案名称',
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `status`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 ( 0 待审核 1 审核通过 )',
    `member_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `delete_time`        datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    `simple_description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '简介',
    `goods_num`          int(10) NOT NULL DEFAULT 0 COMMENT '商品个数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_plan_goods_item
-- ----------------------------
DROP TABLE IF EXISTS `coupon_plan_goods_item`;
CREATE TABLE `coupon_plan_goods_item`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家id',
    `goods_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品guid',
    `coupon_plan_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '卡券方案guid',
    `status`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `bid_goods_coupon`(`bid`, `goods_guid`, `coupon_plan_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_sell_order
-- ----------------------------
DROP TABLE IF EXISTS `coupon_sell_order`;
CREATE TABLE `coupon_sell_order`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `member_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `total_money`           decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '订单总额',
    `bill_number`           char(100) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '订单号',
    `coupon_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '优惠券标识',
    `num`                   int(10) NOT NULL DEFAULT 0 COMMENT '购买张数',
    `coupon_send_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '优惠券发送记录标识',
    `appid`                 char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT 'appid',
    `openid`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
    `remark`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '提货备注',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态  -1 待支付  1 已支付',
    `third_order_number`    char(100) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '三方支付单号, pay_order 中的bill_number',
    `pay_time`              datetime NULL DEFAULT NULL COMMENT '支付成功时间',
    `pay_type`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付类型 1 微信 ',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    `share_user_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者用户guid',
    `share_member_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者会员guid',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 741 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_send_note
-- ----------------------------
DROP TABLE IF EXISTS `coupon_send_note`;
CREATE TABLE `coupon_send_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家唯一标识',
    `generate_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成记录唯一标识',
    `batch`                bigint(20) NOT NULL DEFAULT 0 COMMENT '批次码',
    `coupon_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '优惠券唯一标识',
    `goods_item_guid`      json NULL COMMENT '关联产品guid,如卡号级别有则优先取卡号级,否则取卡券级',
    `prefix`               varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '卡前缀',
    `serial_number`        bigint(20) NOT NULL DEFAULT 0 COMMENT '序列号',
    `code`                 varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '券号',
    `password`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '密码',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 -1 未激活  0 未使用 1 已使用',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员唯一标识',
    `used_mobile`          char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '使用者手机号',
    `mobile`               char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接受者手机号,冗余',
    `used_rate_limit_num`  int(10) NOT NULL DEFAULT 0 COMMENT '周期内限制使用数量, 大于0才有意义',
    `used_rate_limit_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 不限制 1 每天 2每周 3每月 4每季度 5每年',
    `owner_user_id`        int(11) NULL DEFAULT NULL COMMENT '所有者,用于限定发送',
    `operator_user_id`     int(11) NULL DEFAULT 0 COMMENT '操作者,一般是发送者',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联guid,一般是使用记录,结合used_way使用',
    `send_time`            datetime NULL DEFAULT NULL COMMENT '发送时间',
    `send_value`           decimal(18, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '发送金额',
    `used_value`           decimal(18, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '已用金额',
    `used_num`             int(10) NOT NULL DEFAULT 0 COMMENT '已用张数',
    `send_num`             int(10) NOT NULL DEFAULT 1 COMMENT '发送张数',
    `send_remark`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '发送备注',
    `revoke_time`          datetime NULL DEFAULT NULL COMMENT '撤销时间',
    `way`                  tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 实体卡制作生成  2 线上购买 3 后台手动发送 4 被转赠 5 在线领取',
    `used_way`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '使用途径 1 油卡 2 手机话费 3 储值 4 微信零钱包',
    `active_user_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '激活用户guid',
    `active_time`          datetime NULL DEFAULT NULL COMMENT '激活时间,用于后续统计',
    `delete_time`          datetime NULL DEFAULT NULL COMMENT '删除时间',
    `used_time`            datetime NULL DEFAULT NULL COMMENT '被使用时间',
    `availability_time`    datetime NULL DEFAULT NULL COMMENT '生效时间',
    `expire_time`          datetime NULL DEFAULT NULL COMMENT '过期时间',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `uk_bid_code`(`bid`, `code`) USING BTREE COMMENT '同一商家下券号唯一',
    INDEX                  `ix_bid_coupon_guid`(`bid`, `coupon_guid`) USING BTREE COMMENT '商家_券',
    INDEX                  `bid`(`bid`) USING BTREE,
    INDEX                  `bid_guid`(`guid`, `bid`) USING BTREE,
    INDEX                  `bid_generate_guid`(`bid`, `generate_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1136613 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_share_note
-- ----------------------------
DROP TABLE IF EXISTS `coupon_share_note`;
CREATE TABLE `coupon_share_note`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家id',
    `share_member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员id',
    `coupon_guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '卡券id',
    `share_send_note_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分享的发送记录id',
    `share_used_note_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享的使用记录id',
    `share_value`            decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '转赠金额',
    `share_num`              int(10) NOT NULL DEFAULT 1 COMMENT '转赠数量',
    `receive_send_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接受者发送记录id',
    `receive_member_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接受者会员id',
    `status`                 tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待领取 1 已领取',
    `receive_time`           datetime(3) NULL DEFAULT NULL COMMENT '接收时间',
    `share_time`             datetime(3) NOT NULL COMMENT '分享时间',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 924 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupon_used_note
-- ----------------------------
DROP TABLE IF EXISTS `coupon_used_note`;
CREATE TABLE `coupon_used_note`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `coupon_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券标识',
    `coupon_send_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券发送记录标识',
    `used_num`         int(10) NOT NULL COMMENT '使用张数',
    `used_value`       decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '使用金额',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `relation_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联guid 一般和way配合使用',
    `way`              tinyint(2) NULL DEFAULT 1 COMMENT '使用途径 1 卡券兑换产品 2 转赠给好友 3 充入余额 4 到店扣费',
    `used_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '核销用户guid',
    `used_user_id`     int(11) NOT NULL DEFAULT 0 COMMENT '核销用户id',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56393 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crontab
-- ----------------------------
DROP TABLE IF EXISTS `crontab`;
CREATE TABLE `crontab`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '商家标识(可为空)',
    `connections`       varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '队列驱动名称,留空则使用默认驱动',
    `name`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名',
    `queue_name`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT 'default' COMMENT '队列名称 default 或 async',
    `class`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类名',
    `priority`          tinyint(3) NOT NULL DEFAULT 0 COMMENT '优先级,越小越靠优先',
    `payload`           json                                                    NOT NULL COMMENT '参数',
    `last_execute_time` datetime                                                NOT NULL DEFAULT '2018-01-01 00:00:00' COMMENT '上次执行时间',
    `next_execute_time` datetime                                                NOT NULL COMMENT '下次执行时间',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '0禁用 1启用',
    `crontab_string`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT 'crontab表达式',
    `interval_sec`      int(11) NOT NULL DEFAULT 60 COMMENT '执行间隔',
    `interval_unit`     char(10) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT 'second' COMMENT '执行间隔周期',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 144 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crontab_class
-- ----------------------------
DROP TABLE IF EXISTS `crontab_class`;
CREATE TABLE `crontab_class`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `name`                   varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
    `type`                   tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 系统任务 2同步一卡易数据任务',
    `default_crontab_string` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'crontab周期',
    `default_interval_sec`   int(11) NOT NULL DEFAULT 60 COMMENT '默认执行间隔',
    `default_interval_unit`  char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT 'second' COMMENT '默认执行间隔单位',
    `class`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '执行类',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crontab_execute_note
-- ----------------------------
DROP TABLE IF EXISTS `crontab_execute_note`;
CREATE TABLE `crontab_execute_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `crontab_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
    `execute_time` datetime(3) NOT NULL COMMENT '执行时间',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10196279 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crop_wechat_config
-- ----------------------------
DROP TABLE IF EXISTS `crop_wechat_config`;
CREATE TABLE `crop_wechat_config`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `name`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '应用名称',
    `agentid`        bigint(20) NOT NULL DEFAULT 0 COMMENT '应用的id',
    `appid`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '功能的appid',
    `appsecret`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '高级调用功能的密钥',
    `token`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用接口的Token',
    `encodingaeskey` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '加密用的EncodingAESKey',
    `status`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time`    datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `appid_agent_id`(`agentid`, `appid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for date_rule
-- ----------------------------
DROP TABLE IF EXISTS `date_rule`;
CREATE TABLE `date_rule`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `name`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '规则名称',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `type`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型 1 可选 0不可选',
    `memo`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `end_date`          date NULL DEFAULT NULL COMMENT '截止日期',
    `start_date`        date NULL DEFAULT NULL COMMENT '开始日期',
    `date`              json NULL COMMENT '日期对象 年月日格式',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 停用',
    `create_time`       datetime(3) NOT NULL COMMENT '创建日期',
    `update_time`       datetime(3) NOT NULL COMMENT '更新日期',
    `weekday`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '周几',
    `rule_type`         tinyint(2) NOT NULL DEFAULT 0 COMMENT '日期规则 1 连续日期 2不规则日期 3星期几 4每年月日 5每月几日',
    `month_days`        json NULL COMMENT '每月几日，数组，rule_type=5时使用',
    `limit_coupon_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '限制卡券类型 0 不限制 1 限制',
    `coupon_item_guid`  json NULL COMMENT '优惠券guid list',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device
-- ----------------------------
DROP TABLE IF EXISTS `device`;
CREATE TABLE `device`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `sn`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备SN号',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '所属商家',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 禁用 1 启用',
    `last_login_bid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '最后登录商家的标识',
    `last_login_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '最后登录工号标识',
    `address`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地址',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `last_login_time`      datetime(3) NULL DEFAULT NULL COMMENT '设备最后登录时间',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `sn`(`sn`) USING BTREE COMMENT 'sn唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_access_reward_note
-- ----------------------------
DROP TABLE IF EXISTS `device_access_reward_note`;
CREATE TABLE `device_access_reward_note`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '设备SN号',
    `camera_code`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '摄像头编号',
    `money`             decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '奖励金额',
    `month`             date                                                    NOT NULL COMMENT '微信下发奖励月份',
    `real_reward_month` date NULL DEFAULT NULL COMMENT '实际奖励发放月份',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `SN`(`camera_code`, `sn_code`) USING BTREE,
    INDEX               `sn_code`(`sn_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11151 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_ad
-- ----------------------------
DROP TABLE IF EXISTS `device_ad`;
CREATE TABLE `device_ad`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `device_sn`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '设备SN号',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属商家',
    `sort`        tinyint(3) NOT NULL DEFAULT 1 COMMENT '排序.越小越靠前',
    `ad_type`     tinyint(2) NOT NULL DEFAULT 1 COMMENT '广告类型,1 支付前广告 2 支付后广告',
    `type`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '1图片2视频',
    `src`         varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片或视频地址,网络路径',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 禁用 1 启用',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_lively_reward_note
-- ----------------------------
DROP TABLE IF EXISTS `device_lively_reward_note`;
CREATE TABLE `device_lively_reward_note`
(
    `id`      bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备SN号',
    `month`   date NOT NULL COMMENT '微信下发奖励月份',
    `money`   decimal(18, 2) NULL DEFAULT 200.00,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `SN`(`sn_code`, `money`) USING BTREE,
    INDEX     `sn_code`(`sn_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4083 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_pay_reward_note
-- ----------------------------
DROP TABLE IF EXISTS `device_pay_reward_note`;
CREATE TABLE `device_pay_reward_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备号',
    `month`        date                                                   NOT NULL COMMENT '月份',
    `trade_count`  int(11) NOT NULL DEFAULT 0 COMMENT '交易笔数',
    `trade_money`  decimal(18, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '交易金额',
    `reward_money` decimal(18, 1)                                         NOT NULL DEFAULT 0.0 COMMENT '奖励金额',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 136415 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_submit_note
-- ----------------------------
DROP TABLE IF EXISTS `device_submit_note`;
CREATE TABLE `device_submit_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备SN',
    `camera_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '摄像头SN',
    `month`       datetime NULL DEFAULT NULL COMMENT '报名月份',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         ` sn_code`(`sn_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16013 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_trade_note
-- ----------------------------
DROP TABLE IF EXISTS `device_trade_note`;
CREATE TABLE `device_trade_note`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT,
    `begin_date`                 date NULL DEFAULT NULL,
    `end_date`                   date                                                    NOT NULL COMMENT '截止月份',
    `sn_code`                    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `valid_user_count`           int(10) NOT NULL DEFAULT 0 COMMENT '累计去重用户数',
    `month_day_valid_user_count` int(10) NOT NULL DEFAULT 0 COMMENT '当月日去重用户数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `sn_code_month`(`sn_code`, `end_date`, `begin_date`) USING BTREE,
    INDEX                        `sn_code`(`sn_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 313931 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_trade_note_everyday
-- ----------------------------
DROP TABLE IF EXISTS `device_trade_note_everyday`;
CREATE TABLE `device_trade_note_everyday`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `sn_code`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备编号',
    `trade_date`           date                                                   NOT NULL COMMENT '交易日期',
    `agent_account`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代理商账号',
    `agent_name`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '代理商名称',
    `business_account`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家账号',
    `business_name`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家名称',
    `day_valid_user_count` int(10) NOT NULL DEFAULT 0 COMMENT '日去重用户数',
    `create_time`          datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `sn_code_trade_date`(`sn_code`, `trade_date`, `business_account`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4307 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for douyin_config
-- ----------------------------
DROP TABLE IF EXISTS `douyin_config`;
CREATE TABLE `douyin_config`
(
    `id`                              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `client_key`                      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '应用ID',
    `open_id`                         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '抖音商户open_id',
    `access_token`                    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户授权令牌,通过该令牌来帮助商户发起请求，完成业务',
    `access_token_expires_time`       datetime                                                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '令牌过期时间',
    `refresh_token`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '刷新令牌时使用,刷新令牌后，我们会保证老的app_auth_token从刷新开始10分钟内可继续使用，请及时替换为最新token',
    `refresh_token_expires_time`      datetime                                                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '刷新令牌过期时间',
    `scope`                           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '授权信息',
    `status`                          tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 禁用',
    `create_time`                     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                     datetime(3) NOT NULL COMMENT '更新时间',
    `access_token_last_refresh_time`  datetime NULL DEFAULT NULL COMMENT 'access_token最后刷新时间',
    `refresh_token_last_refresh_time` datetime NULL DEFAULT NULL COMMENT 'refresh_token最后刷新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `app_id`(`client_key`, `open_id`) USING BTREE COMMENT '同一个支付宝ID唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for email_config
-- ----------------------------
DROP TABLE IF EXISTS `email_config`;
CREATE TABLE `email_config`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `account`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账户名称',
    `host`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '服务器的名称',
    `username`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '发件人地址',
    `from`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '发件人地址',
    `from_name`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发件人姓名',
    `password`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '邮箱密码',
    `secure`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'ssl' COMMENT 'ssl或者tls',
    `port`        int(10) NOT NULL DEFAULT 465 COMMENT '端口,QQ邮箱25,腾讯邮箱465',
    `memo`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `account`(`account`) USING BTREE COMMENT '账户唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express
-- ----------------------------
DROP TABLE IF EXISTS `express`;
CREATE TABLE `express`
(
    `id`                                  bigint(20) NOT NULL AUTO_INCREMENT,
    `sort`                                int(10) NOT NULL DEFAULT 1 COMMENT '排序,越小越优先',
    `code`                                varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '编码',
    `alias_name`                          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '别名',
    `name`                                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '快递公司名称',
    `bid`                                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `status`                              tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 0 关闭 1 下线',
    `create_time`                         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                         datetime(3) NOT NULL COMMENT '更新时间',
    `query_route_express_channel_id`      int(11) NOT NULL DEFAULT 0 COMMENT '付费查询物流通道ID',
    `kuaidiniao_code`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递鸟编码,用于电子面单',
    `wanwei_code`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '万维易源快递编码,为空则传 auto 部分公司必须指定,否则无法查询',
    `cainiao_code`                        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '菜鸟编码',
    `kuaidi_yibai_code`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递100编码',
    `web_express_query_url`               varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '查单链接 支持变量 {EXPRESS_NO} 和 {EXPRESS_CODE}',
    `free_query_route_express_channel_id` tinyint(2) NOT NULL DEFAULT 0 COMMENT '免费查询快递通道',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `code`(`code`) USING BTREE COMMENT '编码唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_auth_jd
-- ----------------------------
DROP TABLE IF EXISTS `express_auth_jd`;
CREATE TABLE `express_auth_jd`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `access_expire`      datetime NULL DEFAULT NULL,
    `access_token`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `client_id`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `code`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `refresh_expire`     datetime NULL DEFAULT NULL,
    `refresh_token`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `seller_id`          char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `express_channel_id` int(11) NOT NULL DEFAULT 0,
    `is_debug`           tinyint(2) NULL DEFAULT 0 COMMENT '是否测试环境',
    `customer_code`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户编码',
    `create_time`        datetime(3) NULL DEFAULT NULL,
    `update_time`        datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_business
-- ----------------------------
DROP TABLE IF EXISTS `express_business`;
CREATE TABLE `express_business`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `express_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '快递编码',
    `sort`         int(10) NOT NULL DEFAULT 1 COMMENT '排序',
    `status`       tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 关闭 1启用',
    `create_time`  datetime(3) NOT NULL,
    `update_time`  datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_channel
-- ----------------------------
DROP TABLE IF EXISTS `express_channel`;
CREATE TABLE `express_channel`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属用户,NULL代表平台通道',
    `priority`          tinyint(2) NOT NULL DEFAULT 1 COMMENT '优先级',
    `name`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通道名称 如 快递鸟',
    `driver`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '1' COMMENT '驱动',
    `channel_parameter` json                                                    NOT NULL COMMENT '通道级参数',
    `is_free`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否免费 0 否 1 是',
    `auth_url`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '授权链接,暂时支持京东',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0禁用',
    `memo`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `is_debug`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否沙箱环境  0 否 1 是',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付通道' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_config
-- ----------------------------
DROP TABLE IF EXISTS `express_config`;
CREATE TABLE `express_config`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `name`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '通道名称',
    `channel_id`               int(10) NOT NULL DEFAULT 0 COMMENT '通道ID',
    `bid`                      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `status`                   tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 禁用 1 启用',
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    `member_id`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
    `customer_name`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `customer_pwd`             varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `send_site`                varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `send_staff`               varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `month_code`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `ware_house_id`            varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货仓编码',
    `custom_area`              varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家自定义区域',
    `trans_type`               tinyint(2) NULL DEFAULT 1 COMMENT '运输方式 1- 陆运 2- 空运 不填默认为1	',
    `shipper_code`             varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递公司编码 详细编码参考《快递鸟接口支持快递公司编码.xlsx》	',
    `logistic_code`            varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递单号(仅宅急送可用)',
    `thr_order_code`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方订单号 (ShipperCode为JD且ExpType为1时必填)	',
    `pay_type`                 tinyint(2) NULL DEFAULT NULL COMMENT '邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付(仅SF支持)	',
    `exp_type`                 varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '快递类型：1-标准快件 ,详细快递类型参考《快递公司快递业务类型.xlsx》	',
    `is_return_sign_bill`      tinyint(2) NULL DEFAULT NULL COMMENT '是否要求签回单 1- 要求 0-不要求',
    `operate_require`          varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签回单操作要求(如：签名、盖章、身份证复印件等)	',
    `cost`                     decimal(18, 2) NULL DEFAULT 0.00 COMMENT '快递运费',
    `other_cost`               decimal(18, 2) NULL DEFAULT 0.00 COMMENT '其他费用',
    `is_notice`                tinyint(2) NULL DEFAULT 1 COMMENT '是否通知快递员上门揽件 0- 通知 1- 不通知 不填则默认为1	',
    `start_date`               datetime NULL DEFAULT NULL COMMENT '上门取货时间段:\"yyyy-MM-dd HH:mm:ss\"格式化，本文中所有时间格式相同',
    `end_date`                 datetime NULL DEFAULT NULL COMMENT '上门取货时间段:\"yyyy-MM-dd HH:mm:ss\"格式化，本文中所有时间格式相同',
    `weight`                   decimal(10, 3) NULL DEFAULT NULL COMMENT '包裹总重量kg 当为快运的订单时必填，不填时快递鸟将根据各个快运公司要求传对应的默认值	',
    `quantity`                 tinyint(2) NULL DEFAULT NULL COMMENT '包裹数(最多支持30件) 一个包裹对应一个运单号，如果是大于1个包裹，返回则按照子母件的方式返回母运单号和子运单号',
    `volume`                   decimal(18, 3) NULL DEFAULT NULL COMMENT '包裹总体积m3 当为快运的订单时必填，不填时快递鸟将根据各个快运公司要求传对应的默认值	',
    `remark`                   varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `add_services`             json NULL COMMENT 'Name	String(20) 增值服务名称 可选\r\nValue	String(30) 增值服务值 可选\r\nCustomerID String(30) 客户标识（选填） 否',
    `commodity`                json NULL COMMENT 'GoodsName	String(100)	商品名称	是\r\nGoodsCode	String(100)	商品编码	否\r\nGoodsquantity	Int(5)	商品数量	否\r\nGoodsPrice	Double(10)	商品价格	否\r\nGoodsWeight	Double(10,3)	商品重量kg	否\r\nGoodsDesc	String(50)	商品描述	否\r\nGoodsVol	Double(15,3)	商品体积m3	否',
    `is_return_print_template` tinyint(2) NULL DEFAULT NULL COMMENT '返回电子面单模板：0-不需要；1-需要	',
    `is_send_message`          tinyint(2) NULL DEFAULT 0 COMMENT '是否订阅短信：0-不需要；1-需要 ',
    `template_size`            varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板规格(默认的模板无需传值，非默认模板传对应模板尺寸)	',
    `packing_type`             tinyint(2) NULL DEFAULT 0 COMMENT '包装类型(快运字段)默认为0； 0- 纸 1- 纤 2- 木 3- 托膜 4- 木托 99-其他	',
    `delivery_method`          tinyint(2) NULL DEFAULT 0 COMMENT '送货方式(快运字段)默认为0； 0- 自提 1- 送货上门（不含上楼） 2- 送货上楼	',
    `sender_name`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送人名称',
    `sender_company`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '(选填)发件人公司',
    `sender_tel`               varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话与手机，必填一个 是',
    `sender_mobile`            varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话与手机，必填一个 是',
    `sender_post_code`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件地邮编(ShipperCode为EMS、YZPY、YZBK时必填) 可选',
    `sender_province_name`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件省 (如广东省，不要缺少"省"； 如是直辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区等)',
    `sender_city_name`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件市(如深圳市，不要缺少"市； 如是市辖区，请直接传北京市、上海市等")',
    `sender_exp_area_name`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件区/县(如福田区，不要缺少"区"或"县")',
    `sender_address`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件详细地址',
    `memo`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `user_guid_json`           json NULL COMMENT '用户guid',
    `data_range_type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '数据查看范围 0 不限制 1 查看部分用户',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_order
-- ----------------------------
DROP TABLE IF EXISTS `express_order`;
CREATE TABLE `express_order`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `channel_id`               int(10) NOT NULL COMMENT '通道id',
    `relation_guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关联guid,一般是订单guid',
    `bid`                      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `bill_number`              char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
    `status`                   tinyint(2) NOT NULL DEFAULT 0 COMMENT '-2 已撤单 -1 失败 0 待处理 1已处理',
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    `member_id`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
    `customer_name`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `customer_pwd`             varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `send_site`                varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `send_staff`               varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `month_code`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值',
    `ware_house_id`            varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货仓编码',
    `custom_area`              varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家自定义区域',
    `trans_type`               tinyint(2) NULL DEFAULT 1 COMMENT '运输方式 1- 陆运 2- 空运 不填默认为1	',
    `shipper_code`             varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递公司编码 详细编码参考《快递鸟接口支持快递公司编码.xlsx》	',
    `logistic_code`            varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递单号(仅宅急送可用)',
    `thr_order_code`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方订单号 (ShipperCode为JD且ExpType为1时必填)	',
    `order_code`               varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号(自定义，不可重复)',
    `pay_type`                 tinyint(2) NULL DEFAULT NULL COMMENT '邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付(仅SF支持)	',
    `exp_type`                 varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '快递类型：1-标准快件 ,详细快递类型参考《快递公司快递业务类型.xlsx》	',
    `is_return_sign_bill`      tinyint(2) NULL DEFAULT NULL COMMENT '是否要求签回单 1- 要求 0-不要求',
    `operate_require`          varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签回单操作要求(如：签名、盖章、身份证复印件等)	',
    `cost`                     decimal(18, 2) NULL DEFAULT 0.00 COMMENT '快递运费',
    `other_cost`               decimal(18, 2) NULL DEFAULT 0.00 COMMENT '其他费用',
    `receiver`                 json NULL COMMENT '收件人信息\r\nCompany	String(30) 收件人公司 否\r\nName	String(30) 收件人	是\r\nTel	String(20) 电话与手机，必填一个 是\r\nMobile	String(20)\r\nPostCode String(10) 收件人邮编 可选\r\nProvinceName String(20)	收件省 	是\r\nCityName String（20）	收件市	是\r\nExpAreaName String（20）	收件区/县 是\r\nAddress	String(100) 收件人详细地址	是',
    `sender`                   json NULL COMMENT '发件人信息\r\nCompany	String(30) 发件人公司 否\r\nName	String(30) 发件人	是\r\nTel	String(20) 电话与手机，必填一个 是\r\nMobile	String(20)\r\nPostCode String(10) 发件地邮编(ShipperCode为EMS、YZPY、YZBK时必填) 可选\r\nProvinceName String(20) 发件省 是\r\nCityName String(20) 发件市	是\r\nExpAreaName String(20) 发件区/县 是\r\nAddress	String(100) 发件人详细地址	是',
    `is_notice`                tinyint(2) NULL DEFAULT 1 COMMENT '是否通知快递员上门揽件 0- 通知 1- 不通知 不填则默认为1	',
    `start_date`               datetime NULL DEFAULT NULL COMMENT '上门取货时间段:\"yyyy-MM-dd HH:mm:ss\"格式化，本文中所有时间格式相同',
    `end_date`                 datetime NULL DEFAULT NULL COMMENT '上门取货时间段:\"yyyy-MM-dd HH:mm:ss\"格式化，本文中所有时间格式相同',
    `weight`                   decimal(10, 3) NULL DEFAULT NULL COMMENT '包裹总重量kg 当为快运的订单时必填，不填时快递鸟将根据各个快运公司要求传对应的默认值	',
    `quantity`                 tinyint(2) NULL DEFAULT NULL COMMENT '包裹数(最多支持30件) 一个包裹对应一个运单号，如果是大于1个包裹，返回则按照子母件的方式返回母运单号和子运单号',
    `volume`                   decimal(18, 3) NULL DEFAULT NULL COMMENT '包裹总体积m3 当为快运的订单时必填，不填时快递鸟将根据各个快运公司要求传对应的默认值	',
    `remark`                   varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `add_services`             json NULL COMMENT 'Name	String(20) 增值服务名称 可选\r\nValue	String(30) 增值服务值 可选\r\nCustomerID String(30) 客户标识（选填） 否',
    `commodity`                json NULL COMMENT 'GoodsName	String(100)	商品名称	是\r\nGoodsCode	String(100)	商品编码	否\r\nGoodsquantity	Int(5)	商品数量	否\r\nGoodsPrice	Double(10)	商品价格	否\r\nGoodsWeight	Double(10,3)	商品重量kg	否\r\nGoodsDesc	String(50)	商品描述	否\r\nGoodsVol	Double(15,3)	商品体积m3	否',
    `is_return_print_template` tinyint(2) NULL DEFAULT NULL COMMENT '返回电子面单模板：0-不需要；1-需要	',
    `is_send_message`          tinyint(2) NULL DEFAULT 0 COMMENT '是否订阅短信：0-不需要；1-需要 ',
    `template_size`            varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板规格(默认的模板无需传值，非默认模板传对应模板尺寸)	',
    `packing_type`             tinyint(2) NULL DEFAULT 0 COMMENT '包装类型(快运字段)默认为0； 0- 纸 1- 纤 2- 木 3- 托膜 4- 木托 99-其他	',
    `delivery_method`          tinyint(2) NULL DEFAULT 0 COMMENT '送货方式(快运字段)默认为0； 0- 自提 1- 送货上门（不含上楼） 2- 送货上楼	',
    `response_data`            json NULL COMMENT '返回数据',
    `message`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原因',
    `print_tpl`                longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '打印模板',
    `dest_code`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求成功后返回',
    `express_code`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'express表的code',
    `express_no`               varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '如请求成功后返回的单号,用于回填业务表',
    `express_config_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板id',
    `cancel_time`              datetime(3) NULL DEFAULT NULL COMMENT '订单取消时间',
    `pdf_url`                  varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '面单pdf文件url,用于打印',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `order_code`(`order_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2174 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for express_query_note
-- ----------------------------
DROP TABLE IF EXISTS `express_query_note`;
CREATE TABLE `express_query_note`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `express_code`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递公司编码',
    `express_no`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递单号',
    `express_channel_id` int(11) NOT NULL DEFAULT 0 COMMENT '查询通道',
    `order_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单状态',
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家id',
    `user_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
    `member_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员id',
    `result`             json NULL COMMENT '返回结果',
    `create_time`        datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `from_cache`         tinyint(2) NULL DEFAULT 0 COMMENT '是否来自缓存',
    `mobile`             varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `express_status`     tinyint(2) NOT NULL DEFAULT -1 COMMENT '-1 初始化 ',
    `message`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '查询返回结果',
    `way`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 会员 2 用户 3 批量查单',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 118195 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for extend_field
-- ----------------------------
DROP TABLE IF EXISTS `extend_field`;
CREATE TABLE `extend_field`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `key_name`    varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `status`      tinyint(2) NOT NULL DEFAULT 1,
    `required`    tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 选填 1必填',
    `create_time` datetime(3) NOT NULL,
    `update_time` datetime(3) NOT NULL,
    `type`        varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `option`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `placeholder` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `bid_key_name`(`bid`, `key_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fast_register_weapp
-- ----------------------------
DROP TABLE IF EXISTS `fast_register_weapp`;
CREATE TABLE `fast_register_weapp`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `create_bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家标识',
    `name`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业名（需与工商部门登记信息一致）；如果是"无主体名称个体工商户"则填"个体户+法人姓名"，例如"个体户张三"',
    `code`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业代码',
    `code_type`            tinyint(2) NOT NULL DEFAULT 0 COMMENT '企业代码类型 1：统一社会信用代码（18 位） 2：组织机构代码（9 位 xxxxxxxx-x） 3：营业执照注册号(15 位)',
    `legal_persona_wechat` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '法人微信号',
    `legal_persona_name`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '法人姓名（绑定银行卡）',
    `component_phone`      varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '第三方联系电话',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '小程序APPID所属商家',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '-1 失败 0 待发起 1 已发起 2 成功',
    `appid`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '小程序appid,审核通过后有',
    `auth_code`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用于获取小程序信息',
    `examine_time`         datetime NULL DEFAULT NULL COMMENT '审核时间',
    `examine_status`       int(10) NOT NULL DEFAULT -1 COMMENT '-1 初始值',
    `examine_result`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '审核结果',
    `delete_time`          datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for favorites
-- ----------------------------
DROP TABLE IF EXISTS `favorites`;
CREATE TABLE `favorites`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `relation_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '收藏对象标识(商品)',
    `member_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员标识',
    `type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 商品 2 卡券 3 专题',
    `status`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1收藏 0取消',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `title`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '反馈标题',
    `content`     text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '反馈内容',
    `status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态',
    `delete_time` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `reply_time`  datetime NULL DEFAULT NULL,
    `reply`       text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '反馈人',
    `mobile`      varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
    `image_list`  json NULL COMMENT '图片列表',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file_upload_note
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_note`;
CREATE TABLE `file_upload_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `user_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `storage_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `hash`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `key`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `url`          varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `upload_time`  datetime                                                 NOT NULL,
    `result`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `status`       tinyint(2) NOT NULL DEFAULT 1,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 82427 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for form
-- ----------------------------
DROP TABLE IF EXISTS `form`;
CREATE TABLE `form`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家标识 空代表系统内置表单',
    `title`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '表单名称',
    `json`        json                                                    NOT NULL COMMENT '表单结构',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `html`        text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'html',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for freight_template
-- ----------------------------
DROP TABLE IF EXISTS `freight_template`;
CREATE TABLE `freight_template`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '主键',
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `start_fee`       decimal(18, 2)                                         NOT NULL COMMENT '首N件多少元',
    `add_fee`         decimal(18, 2)                                         NOT NULL COMMENT '每增加N件多少元',
    `area_id_list`    json NULL COMMENT '适用省市,为空代表全国适用',
    `add_standard`    int(10) NOT NULL DEFAULT 1 COMMENT '每增加N件',
    `start_standard`  int(10) NOT NULL COMMENT '首N件',
    `type`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 按件数 2按重量',
    `name`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板名称',
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    `area_limit_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 不限制地区 1限制部分地区',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gift_package
-- ----------------------------
DROP TABLE IF EXISTS `gift_package`;
CREATE TABLE `gift_package`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `coupon_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `type`         char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `coupon_title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `wechat_value` decimal(8, 2) NULL DEFAULT 0.00,
    `value`        decimal(8, 2) NULL DEFAULT NULL,
    `yikayi_value` decimal(8, 2) NULL DEFAULT NULL,
    `status`       tinyint(2) NULL DEFAULT NULL,
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for gong_ji_jin
-- ----------------------------
DROP TABLE IF EXISTS `gong_ji_jin`;
CREATE TABLE `gong_ji_jin`
(
    `id`     int(10) NOT NULL AUTO_INCREMENT,
    `month`  date NULL DEFAULT NULL,
    `name`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `money`  decimal(18, 2) NULL DEFAULT NULL,
    `mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 872 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods
-- ----------------------------
DROP TABLE IF EXISTS `goods`;
CREATE TABLE `goods`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `brand_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `type`                tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 实物商品 2 微信零钱',
    `category_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类别guid',
    `tag_guid`            json NULL COMMENT '标签列表',
    `name`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
    `extra_charges_name`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '附加费名称',
    `extra_charges_price` decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '附加费',
    `video_url`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '视频url',
    `pic`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品主图',
    `pic1`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主图1',
    `pic2`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主图2',
    `pic3`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主图3',
    `is_attribute`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 单规格 1多规格',
    `sort`                int(10) NOT NULL DEFAULT 0 COMMENT '序号,越小越靠前',
    `stock`               int(10) NOT NULL DEFAULT -1 COMMENT '库存数量 -1 代表不展示库存',
    `level`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '等级',
    `min_choose_num`      int(11) NOT NULL DEFAULT 0 COMMENT '最少选择数量',
    `max_choose_num`      int(11) NOT NULL DEFAULT 1 COMMENT '最多选择数量',
    `original_price`      decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '原价',
    `cost_price`          decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '成本价',
    `price`               decimal(10, 2)                                          NOT NULL COMMENT '价格',
    `show_price`          decimal(10, 2)                                          NOT NULL DEFAULT -1.00 COMMENT '展示价格,默认 -1 代表不用此价格',
    `category`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '系列',
    `short_url`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '短链接网址',
    `sales`               int(10) NOT NULL DEFAULT 0 COMMENT '销量,-1代表不展示',
    `virtual_sales`       int(10) NOT NULL DEFAULT 0 COMMENT '虚拟销量 ,默认0',
    `number`              char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '编号',
    `specs`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格 如 50KG',
    `unit`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单位 如 件,盒,箱',
    `ctn_specs`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '箱规',
    `weight`              float(10, 4
) NOT NULL DEFAULT 0.0000 COMMENT '重量',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `create_user_id` int(11) NOT NULL DEFAULT 0 COMMENT '创建者id',
  `create_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建者guid',
  `owner_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '归属用户guid',
  `owner_user_id` int(11) NOT NULL DEFAULT 0 COMMENT '归属用户id',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
  `freight_template_guid_list` json NULL COMMENT '运费模板列表',
  `freight_template_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '运费模板标识,为空标识包邮',
  `enable_input` tinyint(2) NOT NULL DEFAULT 0 COMMENT '提货流程中开启输入 0 禁用 1启用',
  `show_exchange_button` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否展示兑换按钮 和购物车按钮互斥',
  `brokerage_money` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '独立分佣金额',
  `brokerage_money_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 全局 2独立',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 8570 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_attr
-- ----------------------------
DROP TABLE IF EXISTS `goods_attr`;
CREATE TABLE `goods_attr`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `delete_time`           datetime NULL DEFAULT NULL,
    `update_time`           datetime NULL DEFAULT NULL COMMENT '更新时间',
    `create_time`           datetime NULL DEFAULT NULL COMMENT '创建时间',
    `goods_attr_name`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格名称(黑色,白色)',
    `goods_attr_group_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格分组标识(颜色,尺码)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 3166 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_attr_group
-- ----------------------------
DROP TABLE IF EXISTS `goods_attr_group`;
CREATE TABLE `goods_attr_group`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 593 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_category
-- ----------------------------
DROP TABLE IF EXISTS `goods_category`;
CREATE TABLE `goods_category`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `parent_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '父类',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `alias`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '别名',
    `mini_pic`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小图',
    `pic`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类别图片',
    `name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类别名称',
    `description`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `sort`             int(10) NOT NULL DEFAULT 0 COMMENT '序号,越大越靠前',
    `status`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user_id`   int(10) NOT NULL DEFAULT 0 COMMENT '创建用户id',
    `create_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '创建用户guid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 923 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_category_article
-- ----------------------------
DROP TABLE IF EXISTS `goods_category_article`;
CREATE TABLE `goods_category_article`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `title`               varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `sort`                int(10) NOT NULL DEFAULT 1 COMMENT '排序 序号越大越靠前',
    `content`             text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容html',
    `goods_category_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '类别标识',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_category_discount
-- ----------------------------
DROP TABLE IF EXISTS `goods_category_discount`;
CREATE TABLE `goods_category_discount`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户唯一标识',
    `discount`            decimal(10, 2)                                      NOT NULL COMMENT '折扣系数',
    `goods_category_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类别唯一标识',
    `member_group_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员级别唯一标识',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 213 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_order
-- ----------------------------
DROP TABLE IF EXISTS `goods_order`;
CREATE TABLE `goods_order`
(
    `id`                                  bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL COMMENT '主键',
    `parent_order_guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL DEFAULT '' COMMENT '父订单id',
    `bid`                                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL COMMENT '商家标识',
    `bill_number`                         char(100) CHARACTER SET utf8 COLLATE utf8_general_ci          NOT NULL COMMENT '订单号',
    `parent_bill_number`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL DEFAULT '' COMMENT '父订单号',
    `type`                                tinyint(2) NOT NULL COMMENT '1 快递发货 2 预约到店自提',
    `way`                                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 卡券提货 2 产品商城',
    `cycle_times`                         int(10) NOT NULL DEFAULT 1 COMMENT '周期次数,普通订单1 子订单>1',
    `member_guid`                         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `total_amount`                        int(10) NOT NULL DEFAULT 1 COMMENT '总商品件数',
    `discount_preferential_money`         decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '折扣优惠总额',
    `total_money`                         decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '订单总额',
    `goods_info`                          json NULL COMMENT '商品详情json(用于列表展示或者导出)',
    `goods_money`                         decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '产品金额',
    `extra_charges_name`                  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '附加费名称(用于展示订单详情)',
    `extra_charges_money`                 decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '附加费总额',
    `freight_money`                       decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '运费金额',
    `coupon_guid`                         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL COMMENT '优惠券标识',
    `coupon_send_note_guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL COMMENT '优惠券发送记录标识(用于记录券支付抵扣的金额)',
    `coupon_used_note_guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL COMMENT '优惠券使用记录标识',
    `appid`                               char(50) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL DEFAULT '' COMMENT 'appid',
    `openid`                              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT 'openid',
    `user_guid`                           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL DEFAULT '' COMMENT '订单创建用户guid',
    `owner_user_id`                       int(10) NOT NULL DEFAULT 0 COMMENT '订单归属用户id,用于指派发货',
    `owner_user_guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单归属用户guid,用于指派发货',
    `user_id`                             int(10) NOT NULL DEFAULT 0 COMMENT '订单创建用户id',
    `refund_user_guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退单用户guid',
    `refund_user_id`                      int(10) NOT NULL DEFAULT 0 COMMENT '退单用户id',
    `goods_item_guid`                     json                                                          NOT NULL COMMENT '商品明细',
    `true_name`                           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '提货人姓名',
    `mobile`                              char(20) CHARACTER SET utf8 COLLATE utf8_general_ci           NOT NULL DEFAULT '' COMMENT '提货人手机号',
    `id_card_number`                      varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '身份证号',
    `remark_image_list`                   json NULL COMMENT '留言图片列表',
    `remark`                              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '提货备注',
    `status`                              tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态   -3 已退款  -2 已取消  -1 待支付  0(待发货/待自提)  1(已发货/已自提)  2 已完成(确认收货)',
    `province_id`                         int(10) NOT NULL DEFAULT 0 COMMENT '省份id',
    `city_id`                             int(10) NOT NULL DEFAULT 0 COMMENT '市id',
    `area_id`                             int(10) NOT NULL DEFAULT 0 COMMENT '区id',
    `address`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '详细地址',
    `ip`                                  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci        NOT NULL DEFAULT '' COMMENT 'ip地址',
    `third_order_number`                  char(100) CHARACTER SET utf8 COLLATE utf8_general_ci          NOT NULL DEFAULT '' COMMENT '三方支付单号, pay_order 中的bill_number',
    `pay_time`                            datetime NULL DEFAULT NULL COMMENT '支付成功时间',
    `send_type`                           tinyint(2) NOT NULL DEFAULT 0 COMMENT '发货类型 1 输入单号发货 2 导入excel  3 电子面单 4到店核销 5无需物流',
    `paid_money`                          decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '储值支付金额',
    `point_use_num`                       decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '积分抵扣数量',
    `paid_point`                          decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '积分支付金额',
    `paid_coupon`                         decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '优惠券支付金额',
    `paid_wechat`                         decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '微信支付金额',
    `pay_type`                            tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付类型 1 移动支付 2 纯会员支付',
    `used_value`                          decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '使用金额',
    `coupon_use_num`                      int(11) NOT NULL DEFAULT 0 COMMENT '优惠券使用张数',
    `used_num`                            int(11) NOT NULL DEFAULT 1 COMMENT '使用次数',
    `express_code`                        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '快递公司id',
    `express_no`                          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '快递单号',
    `send_out_goods_remark`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '发货备注',
    `send_or_pick_up_time`                datetime NULL DEFAULT NULL COMMENT '发货/自提时间',
    `send_or_pick_up_user_id`             int(10) NOT NULL DEFAULT 0 COMMENT '发货用户id',
    `send_or_pick_up_user_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货用户guid',
    `express_no_2`                        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '包裹2 快递单号',
    `express_code_2`                      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '包裹2 快递公司id',
    `send_out_goods_remark_2`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '包裹2 发货备注',
    `send_or_pick_up_time_2`              datetime NULL DEFAULT NULL COMMENT '包裹2 发货时间',
    `send_or_pick_up_user_id_2`           int(11) NOT NULL DEFAULT 0 COMMENT '包裹2 发货用户id',
    `send_or_pick_up_user_guid_2`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '包裹2 发货用户guid',
    `request_send_or_pick_up_store_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预约自提门店',
    `request_send_or_pick_up_time`        date NULL DEFAULT NULL COMMENT '期望发货/自提时间',
    `pick_up_code`                        int(10) NOT NULL DEFAULT 0 COMMENT '自提核销码 1开头9位数字,便于区分其他核销场景',
    `confirm_time`                        datetime NULL DEFAULT NULL COMMENT '确认收货时间(用于商城订单)',
    `refund_result`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款结果',
    `refund_time`                         datetime NULL DEFAULT NULL COMMENT '退款时间(用于商城订单)',
    `cancel_time`                         datetime NULL DEFAULT NULL COMMENT '取消订单时间(用于商城订单)',
    `share_user_guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者用户guid',
    `share_member_guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者会员guid',
    `create_time`                         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                         datetime(3) NOT NULL COMMENT '更新时间',
    `extend_field_1`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '扩展字段1',
    `extend_field_2`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '扩展字段2',
    `extend_field_3`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL COMMENT '扩展字段3',
    `print_times`                         int(10) NOT NULL DEFAULT 0 COMMENT '电子面单打印次数(不代表一定成功)',
    `last_print_time`                     datetime NULL DEFAULT NULL COMMENT '最后打印时间',
    `express_no_route_status`             tinyint(2) NOT NULL DEFAULT -1 COMMENT '单号1 物流状态',
    `express_no_route_last_update_time`   datetime(3) NULL DEFAULT NULL COMMENT '单号1 物流最后更新时间',
    `express_no_2_route_status`           tinyint(2) NOT NULL DEFAULT -1 COMMENT '单号2 物流状态',
    `express_no_2_route_last_update_time` datetime(3) NULL DEFAULT NULL COMMENT '单号2 物流最后更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX                                 `bid_guid`(`guid`, `bid`) USING BTREE,
    INDEX                                 `bid_bill_number`(`bid`, `bill_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61477 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_order_item
-- ----------------------------
DROP TABLE IF EXISTS `goods_order_item`;
CREATE TABLE `goods_order_item`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `order_type`  tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 商城订单 2 卡券提货订单',
    `goods_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品标识',
    `goods_price` decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '金额',
    `order_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '订单标识',
    `amount`      int(10) NOT NULL DEFAULT 1 COMMENT '商品数量',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `attr_list`   json NULL COMMENT 'SKU文本',
    `sku_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SKU唯一标识',
    `status`      int(2) NOT NULL DEFAULT 0 COMMENT '状态 0待处理 1已处理',
    `goods_name`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 63727 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_price_monitor
-- ----------------------------
DROP TABLE IF EXISTS `goods_price_monitor`;
CREATE TABLE `goods_price_monitor`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `platform`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台 jd,taobao',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商品名称',
    `url`         varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品链接',
    `goods_id`    bigint(20) NOT NULL DEFAULT 0 COMMENT '商品ID',
    `end_time`    datetime NULL DEFAULT NULL COMMENT '监控截止时间',
    `begin_time`  datetime NULL DEFAULT NULL COMMENT '监控开始时间',
    `begin_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '最初价格',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0停用',
    `last_price`  decimal(10, 2) NULL DEFAULT NULL COMMENT '最新价格',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `goods_sku`;
CREATE TABLE `goods_sku`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `goods_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商品标识',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `price`                decimal(10, 2)                                          NOT NULL COMMENT '价格',
    `stock`                int(10) NOT NULL COMMENT '库存数量',
    `delete_time`          datetime NULL DEFAULT NULL COMMENT '删除时间',
    `sku_text`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'sku描述',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `goods_attr_guid_list` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `picture`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 7319 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for goods_sku_item
-- ----------------------------
DROP TABLE IF EXISTS `goods_sku_item`;
CREATE TABLE `goods_sku_item`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `goods_sku_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'sku商品guid',
    `goods_attr_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格属性guid,精确到 颜色:白色',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_goods_sku_guid_goods_attr_guid`(`goods_sku_guid`, `goods_attr_guid`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 8185 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for haoping_order
-- ----------------------------
DROP TABLE IF EXISTS `haoping_order`;
CREATE TABLE `haoping_order`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `appid`                char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pic`                  varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '好评图片链接,多个用逗号隔开',
    `openid`               char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
    `message`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `give_amount`          decimal(10, 2) NULL DEFAULT NULL COMMENT '返现金额,元',
    `reason`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
    `order_no_status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '订单号状态, -1作废 1正常 ',
    `operator_userid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员用户id',
    `order_no`             char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
    `mch_billno`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `third_billno`         char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方单号,比如红包/企业付款的成功订单号',
    `wechat_status`        tinyint(2) NULL DEFAULT 0 COMMENT '-1 失败 0 等待发送 1成功',
    `red_packet_send_time` datetime NULL DEFAULT NULL COMMENT '红包发送成功时间',
    `last_examine_time`    datetime NULL DEFAULT NULL COMMENT '最后审核时间',
    `status`               tinyint(2) NOT NULL DEFAULT -2 COMMENT '-2 等待提交 -1 拒绝 0 待审核 1 审核通过',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 253 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for haoping_order_history
-- ----------------------------
DROP TABLE IF EXISTS `haoping_order_history`;
CREATE TABLE `haoping_order_history`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `appid`                char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pic`                  varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '好评图片链接,多个用逗号隔开',
    `openid`               char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
    `message`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `give_amount`          decimal(10, 2) NULL DEFAULT NULL COMMENT '返现金额,元',
    `reason`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
    `order_no_status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '订单号状态, -1作废 1正常 ',
    `operator_userid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员用户id',
    `order_no`             char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
    `mch_billno`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `third_billno`         char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方单号,比如红包/企业付款的成功订单号',
    `wechat_status`        tinyint(2) NULL DEFAULT 0 COMMENT '-1 失败 0 等待发送 1成功',
    `red_packet_send_time` datetime NULL DEFAULT NULL COMMENT '红包发送成功时间',
    `last_examine_time`    datetime NULL DEFAULT NULL COMMENT '最后审核时间',
    `status`               tinyint(2) NOT NULL DEFAULT -2 COMMENT '-2 等待提交 -1 拒绝 0 待审核 1 审核通过',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 18702 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for his
-- ----------------------------
DROP TABLE IF EXISTS `his`;
CREATE TABLE `his`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `adm_dr`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pat_sex`         varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pat_tel`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `adm_date`        date NULL DEFAULT NULL,
    `pat_addr`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pat_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `fee_total`       decimal(18, 2) NULL DEFAULT NULL,
    `pat_cre_no`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `yky_bill_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `status`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '-2 无需处理 0 待处理 1 已处理',
    `data`            json NULL,
    `create_time`     datetime(3) NOT NULL,
    `update_time`     datetime(3) NOT NULL,
    `hos_id`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `adm_type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `memo`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX             `guid_bid`(`guid`, `bid`) USING BTREE,
    INDEX             `adm_dr`(`adm_dr`) USING BTREE,
    INDEX             `adm_date`(`adm_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 319433 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for img_map
-- ----------------------------
DROP TABLE IF EXISTS `img_map`;
CREATE TABLE `img_map`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `local_img`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `remote_img`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time` datetime NULL DEFAULT NULL,
    `update_time` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 19055 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for industry
-- ----------------------------
DROP TABLE IF EXISTS `industry`;
CREATE TABLE `industry`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `sort`        int(10) NOT NULL DEFAULT 1 COMMENT '排序,越小越靠前',
    `icon`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '品牌名称',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 0 下线 1上线',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jobs
-- ----------------------------
DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `queue`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
    `priority`       tinyint(3) NOT NULL DEFAULT 1 COMMENT '优先级,数字越大优先级越高',
    `payload`        json                                                    NOT NULL COMMENT '任务内容',
    `attempts`       tinyint(3) UNSIGNED NOT NULL COMMENT '失败重试次数',
    `reserved`       tinyint(3) UNSIGNED NOT NULL COMMENT '执行次数',
    `reserved_at`    int(10) UNSIGNED NULL DEFAULT NULL COMMENT '实际执行时间',
    `reserved_time`  datetime NULL DEFAULT NULL COMMENT '实际执行时间',
    `available_at`   int(10) UNSIGNED NOT NULL COMMENT '预计执行时间',
    `available_time` datetime NULL DEFAULT NULL COMMENT '预计执行时间',
    `created_at`     int(10) UNSIGNED NOT NULL COMMENT '任务创建时间',
    `created_time`   datetime(3) NULL DEFAULT NULL COMMENT '任务创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1405429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for jobs_execute_note
-- ----------------------------
DROP TABLE IF EXISTS `jobs_execute_note`;
CREATE TABLE `jobs_execute_note`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `pid`                int(10) NOT NULL DEFAULT 0 COMMENT '进程PID',
    `job_id`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
    `execute_begin_time` datetime(3) NOT NULL COMMENT '执行开始时间',
    `execute_end_time`   datetime(3) NOT NULL COMMENT '执行结束时间',
    `used_time`          decimal(18, 6)                                         NOT NULL COMMENT '耗时',
    `job_payload`        json                                                   NOT NULL COMMENT '任务主体',
    `way`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '队列来源',
    `status`             tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 失败 1 成功',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12144978 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jobs_failed
-- ----------------------------
DROP TABLE IF EXISTS `jobs_failed`;
CREATE TABLE `jobs_failed`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `connection` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `queue`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `payload`    json                                                    NOT NULL,
    `exception`  longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `fail_time`  datetime                                                NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 173728 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jobs_history
-- ----------------------------
DROP TABLE IF EXISTS `jobs_history`;
CREATE TABLE `jobs_history`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `queue`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
    `priority`       tinyint(3) NOT NULL DEFAULT 1 COMMENT '优先级,数字越大优先级越高',
    `payload`        json                                                    NOT NULL COMMENT '任务内容',
    `attempts`       tinyint(3) UNSIGNED NOT NULL COMMENT '失败重试次数',
    `reserved`       tinyint(3) UNSIGNED NOT NULL COMMENT '执行次数',
    `reserved_at`    int(10) UNSIGNED NULL DEFAULT NULL COMMENT '实际执行时间',
    `reserved_time`  datetime NULL DEFAULT NULL COMMENT '实际执行时间',
    `available_at`   int(10) UNSIGNED NOT NULL COMMENT '预计执行时间',
    `available_time` datetime NULL DEFAULT NULL COMMENT '预计执行时间',
    `created_at`     int(10) UNSIGNED NOT NULL COMMENT '任务创建时间',
    `created_time`   datetime(3) NULL DEFAULT NULL COMMENT '任务创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29871 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for juhe_recharge_order
-- ----------------------------
DROP TABLE IF EXISTS `juhe_recharge_order`;
CREATE TABLE `juhe_recharge_order`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商家唯一标识',
    `mobile`        char(11) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '手机号',
    `type`          tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 油卡充值 2话费充值',
    `way`           tinyint(2) NOT NULL COMMENT '途径',
    `meno`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `member_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '本地会员唯一标识',
    `proid`         int(10) NULL DEFAULT NULL COMMENT '产品id',
    `value`         int(11) NULL DEFAULT NULL COMMENT '油卡或者话费面值',
    `cardnum`       int(6) NOT NULL COMMENT '充值数量',
    `orderid`       char(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '订单id',
    `game_userid`   char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户id',
    `gas_card_tel`  char(11) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '手机号',
    `gas_card_name` char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `charge_type`   tinyint(1) NULL DEFAULT NULL COMMENT '充值类型',
    `third_billno`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '聚合订单号',
    `third_status`  tinyint(1) NULL DEFAULT -1 COMMENT '第三方状态 -1 等待查询 0充值中 1成功 9 失败',
    `third_message` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '第三方结果',
    `uordercash`    decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '订单扣除金额,进货价格',
    `result`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果',
    `relation_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联记录,比如卡密来源',
    `user_id`       int(11) NULL DEFAULT NULL COMMENT '订单所属用户',
    `status`        tinyint(1) NULL DEFAULT 0 COMMENT '状态 0 等待充值 1成功 -1 失败',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_bid_orderid`(`orderid`, `bid`) USING BTREE COMMENT '订单号唯一',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 23860 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pid`            int(10) NOT NULL DEFAULT 0 COMMENT '进程PID',
    `remark`         longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简单说明',
    `bid`            char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT '商户唯一标识 空代表系统日志',
    `user_id`        int(11) NOT NULL DEFAULT 0 COMMENT '登录用户id',
    `url`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网址',
    `referer`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
    `user_agent`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器标识',
    `ip`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT 'IP地址',
    `ip_region_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ip信息',
    `create_time`    datetime(3) NOT NULL COMMENT '时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `userId`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5960715 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台操作日志表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for media
-- ----------------------------
DROP TABLE IF EXISTS `media`;
CREATE TABLE `media`
(
    `id`                          bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `bid`                         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `sort`                        int(10) NULL DEFAULT NULL,
    `category_guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `tag_guid`                    json NULL,
    `pic`                         varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `pic1`                        varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `name`                        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `number`                      bigint(20) NULL DEFAULT NULL,
    `video`                       varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`                 datetime(3) NULL DEFAULT NULL,
    `update_time`                 datetime(3) NULL DEFAULT NULL,
    `delete_time`                 datetime NULL DEFAULT NULL,
    `status`                      tinyint(2) NULL DEFAULT 1,
    `type_guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `source_url`                  varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `water_mark_background_color` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `video_url`                   varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `memo`                        varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 160 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for media_category
-- ----------------------------
DROP TABLE IF EXISTS `media_category`;
CREATE TABLE `media_category`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `parent_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '父类',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `icon`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小图',
    `pic`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类别图片',
    `name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类别名称',
    `sort`             int(10) NOT NULL DEFAULT 0 COMMENT '序号,越大越靠前',
    `status`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user_id`   int(10) NOT NULL DEFAULT 0 COMMENT '创建用户id',
    `create_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '创建用户guid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 256 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for media_type
-- ----------------------------
DROP TABLE IF EXISTS `media_type`;
CREATE TABLE `media_type`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `parent_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '父类',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `icon`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小图',
    `pic`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类别图片',
    `name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类别名称',
    `sort`             int(10) NOT NULL DEFAULT 0 COMMENT '序号,越大越靠前',
    `status`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user_id`   int(10) NOT NULL DEFAULT 0 COMMENT '创建用户id',
    `create_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '创建用户guid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 254 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member
-- ----------------------------
DROP TABLE IF EXISTS `member`;
CREATE TABLE `member`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `name`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `card_id`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '卡号',
    `mobile`                char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `yky_member_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '在一卡易会员系统中的会员GUID',
    `member_group_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员级别guid',
    `head_img`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '头像',
    `openid`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
    `password`              char(6) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '会员密码,6位纯数字',
    `sex`                   tinyint(2) NOT NULL DEFAULT 0 COMMENT '性别',
    `point`                 decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '积分',
    `money`                 decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '余额',
    `brokerage`             decimal(18, 2)                                          NOT NULL COMMENT '佣金',
    `from`                  tinyint(2) NOT NULL DEFAULT 0 COMMENT '来源 0 PC后台手动添加  默认 1 小程序 2 公众号H5 3 支付宝H5 4 PC端自动注册 5 手机端自动注册 ',
    `status`                tinyint(2) NOT NULL DEFAULT 1 COMMENT '卡状态 0锁定 1正常',
    `level`                 tinyint(2) NOT NULL DEFAULT 0 COMMENT '用户等级',
    `user_type`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '用户类型 用于权限控制',
    `political_outlook`     tinyint(2) NOT NULL DEFAULT 0 COMMENT '政治面貌 0 未知 1 群众 2党员',
    `room_number`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '门牌号',
    `recommend_member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '推荐人标识',
    `union_id`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'union_id标识',
    `oil_card_id`           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '油卡卡号',
    `is_distributor`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否分销商',
    `delete_time`           datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    `appid`                 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT 'appid 和 openid配合使用',
    `share_member_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者会员guid',
    `share_user_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享者用户guid',
    `brokerage_ratio`       decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '分销比例',
    `brokerage_probability` decimal(10, 2)                                          NOT NULL DEFAULT 1.00 COMMENT '分销概率,用于丢单,虚拟数据',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_bid_card_id`(`bid`, `card_id`) USING BTREE COMMENT '同一商家卡号唯一',
    UNIQUE INDEX `uk_guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `uk_bid_yky_member_guid`(`bid`, `yky_member_guid`) USING BTREE COMMENT '同一商家一卡易标识唯一',
    INDEX                   `bid`(`bid`) USING BTREE COMMENT 'bid',
    INDEX                   `openid`(`openid`) USING BTREE COMMENT 'openid',
    INDEX                   `bid_guid`(`guid`, `bid`) USING BTREE COMMENT 'bid_guid'
) ENGINE = InnoDB AUTO_INCREMENT = 115100 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for member_address
-- ----------------------------
DROP TABLE IF EXISTS `member_address`;
CREATE TABLE `member_address`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `province_id` int(10) NOT NULL COMMENT '省份ID',
    `city_id`     int(10) NOT NULL COMMENT '市ID',
    `area_id`     int(10) NOT NULL COMMENT '区ID',
    `address`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
    `mobile`      char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '手机号',
    `true_name`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '姓名',
    `is_default`  tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否默认地址',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 5270 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '会员地址信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_brokerage_cash_note
-- ----------------------------
DROP TABLE IF EXISTS `member_brokerage_cash_note`;
CREATE TABLE `member_brokerage_cash_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '提现单据号',
    `examine_time`         datetime NULL DEFAULT NULL COMMENT '审核时间',
    `relation_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '关联标识,用于退单追溯',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `cash_money`           decimal(18, 2)                                         NOT NULL COMMENT '提现金额',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '-1 已拒绝 0 待审核 1已通过,待打款  2 打款成功',
    `operator_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户标识',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `pay_type`             varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'wechat/alipay/bank',
    `wechat_true_name`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信姓名',
    `wechat_account`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信号',
    `alipay_true_name`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付宝姓名',
    `alipay_account`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付宝账号(手机号或者邮箱)',
    `bank_true_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行卡姓名',
    `bank_name`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行名称(如建设银行)',
    `bank_account`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行卡卡号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 21103 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_brokerage_note
-- ----------------------------
DROP TABLE IF EXISTS `member_brokerage_note`;
CREATE TABLE `member_brokerage_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单据号',
    `relation_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '关联标识,用于退单追溯',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `yky_member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '一卡易会员标识',
    `balance`              decimal(18, 2)                                         NOT NULL COMMENT '充值后余额',
    `brokerage`            decimal(18, 2)                                         NOT NULL COMMENT '金额',
    `type`                 tinyint(2) NOT NULL COMMENT '类型 -1 扣除 1 增加',
    `way`                  tinyint(2) NOT NULL COMMENT '途径',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 成功 -1 撤销',
    `operator_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户标识',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `order_money`          decimal(10, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '订单金额',
    `unique_code`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '唯一码,用于防止重复充值和扣费',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `unique_code`(`unique_code`) USING BTREE COMMENT 'unique_code 为了保证幂等',
    UNIQUE INDEX `bid_relation_guid`(`bid`, `relation_guid`) USING BTREE COMMENT '同一个商家 同一个关联标识 唯一,防止重复奖励',
    INDEX                  `bid`(`bid`) USING BTREE COMMENT '商家标识索引'
) ENGINE = InnoDB AUTO_INCREMENT = 21274 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_distributor_apply_note
-- ----------------------------
DROP TABLE IF EXISTS `member_distributor_apply_note`;
CREATE TABLE `member_distributor_apply_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `member_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `name`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `mobile`       varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `status`       tinyint(2) NOT NULL DEFAULT 0 COMMENT ' -1 未通过 0 待审核 1  已通过',
    `examine_time` datetime NULL DEFAULT NULL,
    `create_time`  datetime(3) NOT NULL,
    `update_time`  datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 230 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_group
-- ----------------------------
DROP TABLE IF EXISTS `member_group`;
CREATE TABLE `member_group`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `sort`                  tinyint(4) NOT NULL DEFAULT 0 COMMENT '序号,越小越优先',
    `name`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '级别名称',
    `image`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
    `discount_ratio`        decimal(4, 2) NULL DEFAULT 1.00 COMMENT '折扣 0.01-1.00',
    `point_ratio`           decimal(4, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '积分系数 0.00-10.00',
    `type`                  tinyint(1) NULL DEFAULT 0 COMMENT '类型',
    `level`                 int(2) NOT NULL DEFAULT 0 COMMENT '等级',
    `big_image`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '大图',
    `big_image2`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `is_default`            tinyint(1) NOT NULL DEFAULT 0 COMMENT '默认开卡级别',
    `brokerage_ratio`       decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '分佣比例(默认不分佣)',
    `brokerage_probability` decimal(10, 2)                                          NOT NULL DEFAULT 1.00 COMMENT '分佣概率',
    `is_distributor`        tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否分销商',
    `status`                tinyint(1) NULL DEFAULT 1 COMMENT '状态 0 禁用 1 启用',
    `memo`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注信息',
    `description`           text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 89 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for member_group_article
-- ----------------------------
DROP TABLE IF EXISTS `member_group_article`;
CREATE TABLE `member_group_article`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '商家标识',
    `title`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `date`              datetime                                                NOT NULL COMMENT '日期',
    `content`           text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
    `user_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '操作员',
    `member_group_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员级别标识',
    `status`            tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_group_discount
-- ----------------------------
DROP TABLE IF EXISTS `member_group_discount`;
CREATE TABLE `member_group_discount`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户唯一标识',
    `discount`          decimal(10, 2)                                      NOT NULL DEFAULT 1.00 COMMENT '折扣系数 0.01-1.00',
    `type`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 卡券',
    `object_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '对象唯一标识 如卡券guid',
    `member_group_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员级别唯一标识',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_money_note
-- ----------------------------
DROP TABLE IF EXISTS `member_money_note`;
CREATE TABLE `member_money_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单据号',
    `relation_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '关联标识,用于退单追溯',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `yky_member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '一卡易会员标识',
    `balance`              decimal(18, 2)                                         NOT NULL COMMENT '充值后余额',
    `money`                decimal(18, 2)                                         NOT NULL COMMENT '金额',
    `type`                 tinyint(2) NOT NULL COMMENT '类型 -1 扣除 1 增加',
    `way`                  tinyint(2) NOT NULL COMMENT '途径 1 后台充值  2微信充值 3 卡密兑换 4 开卡赠送 5商城购物抵扣 6 后台扣除  7 余额提现 8 核销优惠券赠送',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 成功 -1 撤销',
    `operator_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户标识',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 25388 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_point_note
-- ----------------------------
DROP TABLE IF EXISTS `member_point_note`;
CREATE TABLE `member_point_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单据号',
    `relation_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '关联标识,用于退单追溯',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `yky_member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '一卡易会员标识',
    `balance`              decimal(18, 2)                                         NOT NULL COMMENT '充值后余额',
    `point`                decimal(18, 2)                                         NOT NULL COMMENT '金额',
    `type`                 tinyint(2) NOT NULL COMMENT '类型 -1 扣除 1 增加',
    `way`                  tinyint(2) NOT NULL COMMENT '途径 1 购物赠送 2 购物抵扣 3 后台赠送 4后台扣除',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 成功 -1 撤销',
    `operator_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户标识',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 151 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for member_score_note
-- ----------------------------
DROP TABLE IF EXISTS `member_score_note`;
CREATE TABLE `member_score_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `images`               json NULL COMMENT '文件',
    `score_detail`         json NULL COMMENT '打分详情',
    `comment`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '评语',
    `score`                decimal(18, 0)                                          NOT NULL DEFAULT 0 COMMENT '打分计数',
    `type`                 tinyint(2) NOT NULL COMMENT '类型 -1 扣除 1 增加',
    `operator_member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '操作用户guid',
    `way`                  tinyint(2) NOT NULL DEFAULT 0 COMMENT '途径',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1216 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for merchant
-- ----------------------------
DROP TABLE IF EXISTS `merchant`;
CREATE TABLE `merchant`
(
    `id`                              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `account`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '软件账号',
    `vip_account`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '云会员账号',
    `vip_guid`                        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '云会员账户唯一标识',
    `out_merchant_id`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外商户号',
    `wxappid`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信appid',
    `sub_mchid`                       int(10) NULL DEFAULT NULL COMMENT '交易识别码',
    `examine_status`                  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '审核状态',
    `wechat_pay_config_status`        tinyint(1) NOT NULL DEFAULT 0 COMMENT '微信支付配置状态,-1异常,0待确认,1已配置',
    `payment_channel_activate_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付通道激活状态,-1异常 0待确认1全部激活',
    `store_apply_status`              tinyint(1) NOT NULL DEFAULT 0 COMMENT '门店进件状态0未进件1进件成功',
    `activate_status`                 varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '激活状态',
    `merchant_type`                   varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商户类型,大商户,普通商户,加盟商户,直营商户',
    `merchant_id`                     varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '光大商户号',
    `name`                            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家名称',
    `msg`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大商户进件返回的信息',
    `originid`                        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信公众号原始id',
    `qrcodeurl`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号二维码',
    `status`                          tinyint(1) NOT NULL DEFAULT 0 COMMENT '-1异常,0等待进件 1进件成功',
    `memo`                            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `relation_guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '进件记录guid',
    `swiftpass_update_time`           datetime                                                NOT NULL COMMENT '威富通系统创建时间',
    `swiftpass_create_time`           datetime                                                NOT NULL COMMENT '威富通系统更新时间',
    `apply_time`                      datetime NULL DEFAULT NULL COMMENT '进件时间',
    `create_time`                     datetime                                                NOT NULL COMMENT '创建时间',
    `update_time`                     datetime                                                NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `merchant_id`(`merchant_id`) USING BTREE COMMENT '商户号不可重复'
) ENGINE = InnoDB AUTO_INCREMENT = 38778 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for money_exchange_note
-- ----------------------------
DROP TABLE IF EXISTS `money_exchange_note`;
CREATE TABLE `money_exchange_note`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家唯一标识',
    `member_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员唯一标识',
    `billno`          char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户订单号',
    `money`           decimal(18, 4) NULL DEFAULT NULL COMMENT '储值金额',
    `money_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '储值记录唯一标识',
    `order_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单记录唯一标识(油卡充值)',
    `third_billno`    char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方订单号',
    `status`          tinyint(2) NULL DEFAULT 0 COMMENT '0等待处理 1成功 -1失败',
    `message`         varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for money_recharge_rule
-- ----------------------------
DROP TABLE IF EXISTS `money_recharge_rule`;
CREATE TABLE `money_recharge_rule`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `recharge_money` decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '充值金额',
    `gift_money`     decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '赠送储值',
    `status`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '0禁用1启用',
    `gift_point`     decimal(18, 0)                                      NOT NULL DEFAULT 0 COMMENT '赠送积分',
    `create_time`    datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for network
-- ----------------------------
DROP TABLE IF EXISTS `network`;
CREATE TABLE `network`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `time_to_dns`            int(10) NOT NULL DEFAULT -1 COMMENT 'DNS解析时间',
    `time_to_connect`        int(10) NOT NULL DEFAULT -1 COMMENT '网络连接时间',
    `time_to_first_byte`     int(10) NOT NULL DEFAULT -1 COMMENT '首字节返回时间',
    `time_to_request`        int(10) NOT NULL DEFAULT -1 COMMENT '请求时间',
    `time_to_response`       int(10) NOT NULL DEFAULT -1 COMMENT '返回时间',
    `time_to_first_screen`   int(10) NOT NULL DEFAULT -1 COMMENT '首屏时间',
    `time_to_dom`            int(10) NOT NULL DEFAULT -1 COMMENT 'DOM渲染时间',
    `type`                   tinyint(2) NOT NULL DEFAULT -1 COMMENT '类型',
    `url`                    varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '网址',
    `time_to_response_ready` int(10) NOT NULL DEFAULT -1 COMMENT '请求准备时间',
    `time_to_process`        int(10) NOT NULL DEFAULT -1 COMMENT '请求过程时间',
    `time_to_receive`        int(10) NOT NULL DEFAULT -1 COMMENT '请求接收时间',
    `request_count`          int(4) NOT NULL DEFAULT -1 COMMENT '请求数量',
    `http_code`              int(5) NOT NULL DEFAULT -1 COMMENT '状态码',
    `args`                   varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '参数',
    `protocol`               char(10) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT 'http或者https',
    `city_id`                int(10) NOT NULL DEFAULT -1 COMMENT '地区ID',
    `region`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '地区',
    `user_agent`             varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'UserAgent',
    `ip`                     char(15) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '0' COMMENT 'IP地址',
    `domain`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '域名',
    `state`                  char(20) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '状态',
    `events`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '事件名称',
    `time_stamp`             datetime                                                 NOT NULL COMMENT '上传时间',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2111626 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice
-- ----------------------------
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `date`              date                                                    NOT NULL COMMENT '日期',
    `title`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `content`           text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
    `rule_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '权限guid',
    `version_guid_json` json NULL COMMENT '版本guid json',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    `view_times`        int(11) NOT NULL COMMENT '浏览次数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice_view_note
-- ----------------------------
DROP TABLE IF EXISTS `notice_view_note`;
CREATE TABLE `notice_view_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `notice_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `sid`         int(11) NULL DEFAULT NULL,
    `user_id`     int(11) NULL DEFAULT NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notify_key_name_list
-- ----------------------------
DROP TABLE IF EXISTS `notify_key_name_list`;
CREATE TABLE `notify_key_name_list`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `key_name`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '键值',
    `title`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notify_log
-- ----------------------------
DROP TABLE IF EXISTS `notify_log`;
CREATE TABLE `notify_log`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `type`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 小精灵通知',
    `content`     json NOT NULL COMMENT '通知内容',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 234 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notify_record
-- ----------------------------
DROP TABLE IF EXISTS `notify_record`;
CREATE TABLE `notify_record`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '商家标识',
    `member_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '会员标识',
    `user_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号标识',
    `to_user`            varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '发送对象',
    `status`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '-1 失败  0 待发送 1 成功',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    `result`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结果',
    `notify_template_id` int(10) NOT NULL DEFAULT 0 COMMENT '模板id',
    `content`            text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '通知内容',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 158922 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notify_template
-- ----------------------------
DROP TABLE IF EXISTS `notify_template`;
CREATE TABLE `notify_template`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT,
    `default_template_id` bigint(20) NOT NULL COMMENT '默认模板id',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `template`            text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板,可能是string或者json',
    `status`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 关闭',
    `param`               json NULL COMMENT '暂时不用',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 209 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notify_template_default
-- ----------------------------
DROP TABLE IF EXISTS `notify_template_default`;
CREATE TABLE `notify_template_default`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `key_name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '键值',
    `provider`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'system 系统平台通知 business 商户通知 member 会员通知',
    `template`      text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板,可能是string或者json',
    `driver`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'sms/wechat/weapp/email/ding_talk/work_weixin/log',
    `variable_list` json                                                    NOT NULL COMMENT '可选变量列表',
    `status`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 下线 ',
    `is_super`      tinyint(2) NULL DEFAULT 0 COMMENT '是否超级管理员才可见',
    `param`         json NULL COMMENT '扩展参数,如 模板消息的id等',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    `rule_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '关联权限guid',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pid`            int(10) NOT NULL DEFAULT 0 COMMENT '进程PID',
    `http_code`      int(4) NOT NULL DEFAULT 0 COMMENT 'http状态码',
    `path`           varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网址',
    `code`           tinyint(2) NOT NULL COMMENT '状态',
    `msg`            text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '结果',
    `params`         json NULL COMMENT '请求参数',
    `bid`            char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT '商户唯一标识 空代表系统日志',
    `member_id`      int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `user_id`        int(11) NOT NULL DEFAULT 0 COMMENT '登录用户id',
    `user_agent`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器标识',
    `ip`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT 'IP地址',
    `ip_region_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ip信息',
    `create_time`    datetime(3) NOT NULL COMMENT '时间',
    `app_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '应用名称',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `user_id`(`user_id`) USING BTREE,
    INDEX            `bid`(`bid`) USING BTREE,
    INDEX            `create_time`(`create_time`) USING BTREE,
    INDEX            `path`(`path`(191)) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1401352 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台操作日志表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for page
-- ----------------------------
DROP TABLE IF EXISTS `page`;
CREATE TABLE `page`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `title`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
    `path`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '路径',
    `rule_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '关联权限',
    `version_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '关联版本guid',
    `status`       tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `sort`         tinyint(3) NOT NULL DEFAULT 1 COMMENT '排序',
    `parent_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '父级guid',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `path`(`path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for parameter
-- ----------------------------
DROP TABLE IF EXISTS `parameter`;
CREATE TABLE `parameter`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `key_name`    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '键值',
    `value`       longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '值',
    `object`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '对象',
    `object_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '对象guid',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `keyname`(`bid`, `key_name`, `object`, `object_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3712 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for parameter_default
-- ----------------------------
DROP TABLE IF EXISTS `parameter_default`;
CREATE TABLE `parameter_default`
(
    `id`            int(8) NOT NULL AUTO_INCREMENT,
    `sort`          int(8) NULL DEFAULT NULL COMMENT '排序',
    `regex`         varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正则校验',
    `title`         varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
    `type`          varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '类型',
    `dict`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '字典名称',
    `object`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '空代表全局, 暂时支持 coupon',
    `key_name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '键值',
    `rule_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限guid,用于动态展示参数设置',
    `version_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本guid,用于动态展示参数设置',
    `option`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '待选项',
    `default_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '默认值',
    `group`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组',
    `description`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `create_time`   datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `keyname`(`key_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 204 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for pay_bill
-- ----------------------------
DROP TABLE IF EXISTS `pay_bill`;
CREATE TABLE `pay_bill`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `appid`                      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众账号ID',
    `mch_id`                     bigint(20) NOT NULL COMMENT '商户号',
    `sub_mch_id`                 bigint(20) NOT NULL COMMENT '特约商户号',
    `device_info`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '设备号',
    `transaction_id`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '微信订单号',
    `out_trade_no`               varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商户订单号',
    `openid`                     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '用户标识',
    `trade_type`                 varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '交易类型',
    `trade_state`                varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '交易状态 SUCCESS|REFUND',
    `trade_time`                 datetime                                                NOT NULL COMMENT '交易时间',
    `bank_type`                  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '付款银行',
    `fee_type`                   varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '货币种类',
    `settlement_total_fee`       decimal(18, 2)                                          NOT NULL COMMENT '应结订单金额',
    `coupon_fee`                 decimal(18, 2)                                          NOT NULL COMMENT '代金券金额',
    `refund_transaction_id`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '微信退款单号',
    `refund_out_trade_no`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商户退款单号',
    `refund_fee`                 decimal(18, 2)                                          NOT NULL COMMENT '退款金额',
    `recharge_coupon_refund_fee` decimal(18, 2)                                          NOT NULL COMMENT '充值券退款金额',
    `refund_type`                varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '退款类型',
    `refund_state`               varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '退款状态',
    `body`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
    `attach`                     varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户数据包',
    `fee`                        decimal(18, 4)                                          NOT NULL COMMENT '手续费',
    `rate`                       decimal(18, 4)                                          NOT NULL COMMENT '费率',
    `total_fee`                  decimal(18, 2)                                          NOT NULL COMMENT '订单金额',
    `apply_refund_fee`           decimal(18, 2)                                          NOT NULL COMMENT '申请退款金额',
    `rate_remark`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '费率备注',
    `create_time`                datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `transaction_id_trade_state`(`transaction_id`, `trade_state`, `refund_transaction_id`) USING BTREE COMMENT '交易单号|类型唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 46491 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付对账单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_bill_alipay
-- ----------------------------
DROP TABLE IF EXISTS `pay_bill_alipay`;
CREATE TABLE `pay_bill_alipay`
(
    `id`                   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `transaction_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '支付宝交易号',
    `out_trade_no`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户订单号',
    `trade_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '业务类型',
    `goods_name`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
    `trade_create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '交易创建时间',
    `trade_finish_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '交易完成时间',
    `store_number`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '门店编号',
    `store_name`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店名称',
    `operator`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '操作员',
    `terminal_number`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '终端号',
    `account`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '对方账户',
    `order_money`          decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '订单金额（元）',
    `receiv_money`         decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '商家实收（元）',
    `red_packets`          decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '支付宝红包（元）',
    `set_points_treasure`  decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '集分宝（元）',
    `discount`             decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '支付宝优惠（元）',
    `business_discount`    decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '商家优惠（元）',
    `coupon_money`         decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '券核销金额（元）',
    `coupon_name`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '券名称',
    `business_red_packets` decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '商家红包消费金额（元）',
    `card_monty`           decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '卡消费金额（元）',
    `refund_no`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '退款批次号/请求号',
    `fee`                  decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '服务费（元）',
    `profit_money`         decimal(19, 4)                                                NOT NULL DEFAULT 0.0000 COMMENT '分润（元）',
    `memo`                 varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `pid`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '支付宝pid',
    `create_time`          datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `transaction_id`(`transaction_id`) USING BTREE COMMENT '交易单号唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 105 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '对账单(支付宝通道)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_bill_copy
-- ----------------------------
DROP TABLE IF EXISTS `pay_bill_copy`;
CREATE TABLE `pay_bill_copy`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `appid`                      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众账号ID',
    `mch_id`                     bigint(20) NOT NULL COMMENT '商户号',
    `sub_mch_id`                 bigint(20) NOT NULL COMMENT '特约商户号',
    `device_info`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '设备号',
    `transaction_id`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '微信订单号',
    `out_trade_no`               varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商户订单号',
    `openid`                     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '用户标识',
    `trade_type`                 varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '交易类型',
    `trade_state`                varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '交易状态 SUCCESS|REFUND',
    `trade_time`                 datetime                                                NOT NULL COMMENT '交易时间',
    `bank_type`                  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '付款银行',
    `fee_type`                   varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '货币种类',
    `settlement_total_fee`       decimal(18, 2)                                          NOT NULL COMMENT '应结订单金额',
    `coupon_fee`                 decimal(18, 2)                                          NOT NULL COMMENT '代金券金额',
    `refund_transaction_id`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '微信退款单号',
    `refund_out_trade_no`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商户退款单号',
    `refund_fee`                 decimal(18, 2)                                          NOT NULL COMMENT '退款金额',
    `recharge_coupon_refund_fee` decimal(18, 2)                                          NOT NULL COMMENT '充值券退款金额',
    `refund_type`                varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '退款类型',
    `refund_state`               varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '退款状态',
    `body`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
    `attach`                     varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户数据包',
    `fee`                        decimal(18, 4)                                          NOT NULL COMMENT '手续费',
    `rate`                       decimal(18, 4)                                          NOT NULL COMMENT '费率',
    `total_fee`                  decimal(18, 2)                                          NOT NULL COMMENT '订单金额',
    `apply_refund_fee`           decimal(18, 2)                                          NOT NULL COMMENT '申请退款金额',
    `rate_remark`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '费率备注',
    `create_time`                datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `transaction_id_trade_state`(`transaction_id`, `trade_state`, `refund_transaction_id`) USING BTREE COMMENT '交易单号|类型唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 433980 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付对账单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_brokerage
-- ----------------------------
DROP TABLE IF EXISTS `pay_brokerage`;
CREATE TABLE `pay_brokerage`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '该笔分润所属商户',
    `user_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '该笔分润所属商户的工号标识',
    `parent_bid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '出资方bid',
    `parent_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '出资方对应工号标识',
    `level`            tinyint(2) NOT NULL COMMENT '几级分润',
    `transaction_id`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '三方交易单号',
    `out_trade_no`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户订单号',
    `order_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '订单唯一标识',
    `order_bid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '订单所属商家唯一标识',
    `trade_money`      decimal(18, 2)                                         NOT NULL COMMENT '交易金额(元)',
    `fee`              decimal(18, 2)                                         NOT NULL COMMENT '手续费金额(元)',
    `fee_rate`         decimal(18, 4)                                         NOT NULL DEFAULT 0.0000 COMMENT '手续费比例 如 0.006',
    `brokerage_rate`   decimal(18, 4)                                         NOT NULL COMMENT '佣金比例 1表示 100%',
    `brokerage_money`  decimal(18, 4)                                         NOT NULL COMMENT '佣金金额',
    `trade_type`       tinyint(2) NOT NULL COMMENT '交易类型 1 支付 2退款',
    `trade_time`       datetime                                               NOT NULL COMMENT '交易时间',
    `create_time`      datetime(3) NOT NULL COMMENT '分润时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `level`(`level`, `order_guid`) USING BTREE COMMENT '同一个订单号同一个层级唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付分润表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_brokerage_note
-- ----------------------------
DROP TABLE IF EXISTS `pay_brokerage_note`;
CREATE TABLE `pay_brokerage_note`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `type`             tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型 1 全量数据 2 商户间数据',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '分润商户',
    `user_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户',
    `parent_bid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '出资商户',
    `parent_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出资用户',
    `trade_count`      int(10) NOT NULL DEFAULT 0 COMMENT '交易笔数',
    `trade_month`      date                                                    NOT NULL COMMENT '交易月份',
    `trade_money`      decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '交易金额',
    `brokerage_money`  decimal(18, 2)                                          NOT NULL COMMENT '分润金额',
    `status`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '-1已拒绝 0待申请 1 待审核 2已通过',
    `memo`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `bid_month`(`bid`, `user_guid`, `parent_bid`, `parent_user_guid`, `trade_money`) USING BTREE COMMENT '同月份唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_channel
-- ----------------------------
DROP TABLE IF EXISTS `pay_channel`;
CREATE TABLE `pay_channel`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属用户,NULL代表平台通道',
    `priority`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '优先级',
    `name`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通道名称 如 微信直连通道',
    `channel_provider_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '通道路由ID',
    `type`                tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 官方通道 2 银行通道',
    `scene_id`            bigint(20) NOT NULL COMMENT '默认支持的场景ID',
    `channel_parameter`   json                                                    NOT NULL COMMENT '通道级参数',
    `status`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0禁用',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付通道' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_channel_provider
-- ----------------------------
DROP TABLE IF EXISTS `pay_channel_provider`;
CREATE TABLE `pay_channel_provider`
(
    `id`                     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`                   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通道名称，如微信直连',
    `key`                    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通道代号，如weixin',
    `class`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '处理类',
    `init_channel_parameter` json NULL COMMENT '初始化通道参数',
    `init_pay_parameter`     json NULL COMMENT '初始化支付参数,需和通道支付参数合并',
    `scene_id`               bigint(20) NOT NULL COMMENT '支付类型代码，二进制标识，每一位表示一种支付方式',
    `status`                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道提供者表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_order
-- ----------------------------
DROP TABLE IF EXISTS `pay_order`;
CREATE TABLE `pay_order`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `user_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '工号标识',
    `total_fee`          int(10) NOT NULL COMMENT '交易金额,单位分',
    `refund_fee`         int(10) NOT NULL DEFAULT 0 COMMENT '退款金额,单位分',
    `mch_id`             varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户号',
    `bill_number`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
    `trade_type`         tinyint(2) NULL DEFAULT NULL COMMENT '交易类型,支付还是退款',
    `third_refund_no`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方退单单号',
    `third_trade_no`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方支付订单号',
    `scene`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付场景 扫码付 公众号支付 等',
    `channel`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '通道,微信/支付宝 等',
    `body`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对交易或商品的描述',
    `buyer_id`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户唯一标识(例如openid或userid)',
    `pay_parameter_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '支付参数guid',
    `client_ip`          varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '客户端IP',
    `client_id`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '设备ID',
    `attach`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下单附带参数',
    `job_attach`         text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异步任务数据包',
    `profit_sharing`     tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否指定服务商分账\r\nY-是，需要分账\r\nN-否，不分账\r\n字母要求大写，不传默认不分账\r\n数据库 用 1 0 区分',
    `way`                tinyint(2) NULL DEFAULT NULL COMMENT '业务类型 1 收款 2 一卡易受限充值 3 付费进微信群',
    `notify_data`        varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回调数据',
    `remark`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `status`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '交易状态 -1 已退款 0 等待支付 1 支付成功 ',
    `refund_time`        datetime(3) NULL DEFAULT NULL COMMENT '退款成功时间',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    `trade_time`         datetime NULL DEFAULT NULL COMMENT '交易时间或者退款时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 10311 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付订单' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for pay_parameter
-- ----------------------------
DROP TABLE IF EXISTS `pay_parameter`;
CREATE TABLE `pay_parameter`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商户标识',
    `class`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '处理类',
    `channel_id`  int(11) NOT NULL DEFAULT 0 COMMENT '支付通道ID',
    `scene_id`    bigint(20) NOT NULL COMMENT '支付场景ID',
    `parameter`   json NULL COMMENT '支付参数',
    `priority`    tinyint(2) NOT NULL DEFAULT 1 COMMENT '优先级',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 禁用',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `bid_channel_id`(`bid`, `channel_id`) USING BTREE COMMENT '同一个商家同一通道不允许重复',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 261 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `pay_qrcode`;
CREATE TABLE `pay_qrcode`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `bill_number`  char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '生成订单号',
    `num`          int(11) NOT NULL DEFAULT 0 COMMENT '生成数量',
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '生成商户',
    `user_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '生成工号',
    `status`       tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待生成 1 生成完毕',
    `download_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下载链接',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_qrcode_item
-- ----------------------------
DROP TABLE IF EXISTS `pay_qrcode_item`;
CREATE TABLE `pay_qrcode_item`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '二维码ID',
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '二维码唯一标识',
    `short_url_code` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '对应短链标识',
    `bill_number`    char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '生成订单号',
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '归属代理商标识',
    `bind_bid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '绑定商家标识',
    `bind_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '绑定工号标识',
    `bind_time`      datetime(3) NULL DEFAULT NULL COMMENT '绑定时间',
    `create_time`    datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT '主键不允许重复',
    UNIQUE INDEX `code`(`short_url_code`) USING BTREE COMMENT '短链接不允许重复'
) ENGINE = InnoDB AUTO_INCREMENT = 116 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pay_scene
-- ----------------------------
DROP TABLE IF EXISTS `pay_scene`;
CREATE TABLE `pay_scene`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `scene_id`    bigint(20) NOT NULL COMMENT '支付',
    `drive`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通道',
    `scene`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场景',
    `description` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付类型简称,微信公众号支付',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `scene_id`(`scene_id`) USING BTREE COMMENT '场景值唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付场景表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for payroll
-- ----------------------------
DROP TABLE IF EXISTS `payroll`;
CREATE TABLE `payroll`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `month`       date NULL DEFAULT NULL,
    `type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `data`        varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `money`       decimal(18, 4) NULL DEFAULT NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 412165 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for profit_sharing_order
-- ----------------------------
DROP TABLE IF EXISTS `profit_sharing_order`;
CREATE TABLE `profit_sharing_order`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '商家唯一标识',
    `mch_id`                 char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '商户号',
    `sub_mch_id`             char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '子商户号',
    `appid`                  char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '公众账号ID',
    `sub_appid`              char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '子公众号ID',
    `transaction_id`         char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '微信支付订单号',
    `trade_order_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '交易订单记录标识',
    `out_order_no`           char(64) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '商户系统内部的分账单号，商户系统内部唯一。',
    `order_id`               char(32) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '	\r\n微信分账单号',
    `description`            varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分账完结的原因描述',
    `close_reason`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关单原因',
    `receivers`              json                                                   NOT NULL COMMENT '分账列表',
    `sharing_amount`         int(11) NOT NULL DEFAULT 0 COMMENT '预计分账金额',
    `success_sharing_amount` int(11) NOT NULL DEFAULT 0 COMMENT '实际分账金额',
    `trade_money`            int(11) NOT NULL DEFAULT 0 COMMENT '交易金额',
    `status`                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '分账单状态： \r\nACCEPTED—受理成功 \r\nPROCESSING—处理中 \r\nFINISHED—处理完成 \r\nCLOSED—处理失败，已关单 ',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `out_order_no`(`out_order_no`) USING BTREE COMMENT '商户单号唯一',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for profit_sharing_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `profit_sharing_order_detail`;
CREATE TABLE `profit_sharing_order_detail`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `order_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '分账订单记录标识',
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商家唯一标识',
    `transaction_id` char(32) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '微信支付订单号',
    `out_order_no`   char(64) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商户系统内部的分账单号，商户系统内部唯一。',
    `description`    varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账完结的原因描述',
    `type`           varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账接收方类型:MERCHANT_ID：商户ID \r\nPERSONAL_WECHATID：个人微信号PERSONAL_OPENID：个人openid（由父商户APPID转换得到）PERSONAL_SUB_OPENID: 个人sub_openid（由子商户APPID转换得到）',
    `account`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账接收方帐号:类型是MERCHANT_ID时，是商户ID \r\n类型是PERSONAL_WECHATID时，是个人微信号 \r\n类型是PERSONAL_OPENID时，是个人openid \r\n类型是PERSONAL_SUB_OPENID时，是个人sub_openid',
    `name`           varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分账接收方全称:	\r\n分账接收方类型是MERCHANT_ID时，是商户全称（必传） \r\n分账接收方类型是PERSONAL_NAME 时，是个人姓名（必传） \r\n分账接收方类型是PERSONAL_OPENID时，是个人姓名（选传，传则校验） \r\n分账接收方类型是PERSONAL_SUB_OPENID时，是个人姓名（选传，传则校验）',
    `amount`         int(11) NOT NULL DEFAULT 0 COMMENT '分账金额',
    `status`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 成功 0 已删除',
    `result`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账结果',
    `finish_time`    datetime NULL DEFAULT NULL COMMENT '分账成功时间',
    `create_time`    datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for profit_sharing_receiver
-- ----------------------------
DROP TABLE IF EXISTS `profit_sharing_receiver`;
CREATE TABLE `profit_sharing_receiver`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商家标识',
    `mch_id`      char(32) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商户号',
    `sub_mch_id`  char(32) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '子商户号',
    `appid`       char(32) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '公众账号ID',
    `sub_appid`   char(32) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '子商户公众账号ID',
    `type`        varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账接收方类型:MERCHANT_ID：商户ID \r\nPERSONAL_WECHATID：个人微信号PERSONAL_OPENID：个人openid（由父商户APPID转换得到）PERSONAL_SUB_OPENID: 个人sub_openid（由子商户APPID转换得到）',
    `account`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分账接收方帐号:类型是MERCHANT_ID时，是商户ID \r\n类型是PERSONAL_WECHATID时，是个人微信号 \r\n类型是PERSONAL_OPENID时，是个人openid \r\n类型是PERSONAL_SUB_OPENID时，是个人sub_openid',
    `name`        varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分账接收方全称:	\r\n分账接收方类型是MERCHANT_ID时，是商户全称（必传） \r\n分账接收方类型是PERSONAL_NAME 时，是个人姓名（必传） \r\n分账接收方类型是PERSONAL_OPENID时，是个人姓名（选传，传则校验） \r\n分账接收方类型是PERSONAL_SUB_OPENID时，是个人姓名（选传，传则校验）',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 成功 0 已删除',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for questionnaire_epidemic_situation
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_epidemic_situation`;
CREATE TABLE `questionnaire_epidemic_situation`
(
    `id`                                           bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                                         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                                          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `member_guid`                                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `name`                                         char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `mobile`                                       char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `id_card`                                      char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证',
    `address`                                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
    `temperature`                                  decimal(18, 2) NULL DEFAULT NULL COMMENT '体温',
    `has_epidemic_situation_symptom`               tinyint(2) NULL DEFAULT NULL COMMENT '您 14 天内是否有发热、干咳、乏力、咽痛、嗅(味)觉减退、腹泻等症状',
    `has_been_to_high_risk_area`                   tinyint(2) NULL DEFAULT NULL COMMENT '近14天内是否到过境外以及境内中高风险地区，或有病例报告的社区',
    `been_to_high_risk_area`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `has_contact_high_risk_personnel`              tinyint(2) NULL DEFAULT NULL COMMENT '近 14 天内是否接触过来自境外以及境内中高风险地区的发热和/或有呼吸道症状的患者?',
    `contact_high_risk_personnel`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `contact_epidemic_situation_personnel`         tinyint(2) NULL DEFAULT NULL COMMENT '近14天内是否接触过确诊病例或无症状感染者(核酸检测阳性者)?',
    `living_environment_happen_epidemic_situation` tinyint(2) NULL DEFAULT NULL COMMENT '近 14 天内您的家庭、办公室、学校或托幼机构班级、车间等集体单位是否出现2例及以上发热和/或呼吸道症状的聚集性病例',
    `from_city_type`                               tinyint(2) NULL DEFAULT NULL COMMENT '您14 天内从哪个地区来本市',
    `from_city_value`                              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '具体来自城市',
    `image_list`                                   json NULL COMMENT '图片列表',
    `create_time`                                  datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                                  datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `memo`                                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `come_back_time`                               datetime NULL DEFAULT NULL COMMENT '回来时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recycle_bin
-- ----------------------------
DROP TABLE IF EXISTS `recycle_bin`;
CREATE TABLE `recycle_bin`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `table`       varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bill_number` char(30) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `batch`       int(10) NOT NULL DEFAULT 1 COMMENT '单次删除如果数量过大分批写入,避免data字段溢出',
    `map`         json NULL,
    `count`       int(11) NOT NULL DEFAULT 0,
    `data`        longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `create_time` datetime(3) NOT NULL,
    `update_time` datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2097 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for region
-- ----------------------------
DROP TABLE IF EXISTS `region`;
CREATE TABLE `region`
(
    `id`        bigint(19) UNSIGNED NOT NULL AUTO_INCREMENT,
    `code`      bigint(19) UNSIGNED NOT NULL COMMENT '地区ID',
    `name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '地区名称',
    `parent_id` bigint(19) UNSIGNED NOT NULL COMMENT '父级地区',
    `level`     tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '地区类型 级别',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `region_id`(`code`) USING BTREE,
    INDEX       `parent_id`(`parent_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 627437 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '全国省市区街道数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for reward_point_order
-- ----------------------------
DROP TABLE IF EXISTS `reward_point_order`;
CREATE TABLE `reward_point_order`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '主键',
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '商家标识',
    `driver`          varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '驱动',
    `way`             tinyint(2) NOT NULL DEFAULT 1 COMMENT '1,消费奖励 2 推荐人奖励 3 储值消费后奖励给门店老板',
    `seller_mobile`   char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '卖家手机号',
    `buyer_mobile`    char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '买家手机号',
    `yky_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '一卡易消费单号',
    `message`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '返回消息',
    `level`           tinyint(2) NOT NULL DEFAULT 1 COMMENT '奖励层级',
    `status`          tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 -1失败 0处理中 1成功 -2已退单',
    `order_id`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '订单号',
    `refund_amount`   decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '退款金额',
    `total_amount`    decimal(10, 2)                                          NOT NULL COMMENT '消费金额 分',
    `out_trade_no`    char(100) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '外订单号',
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_bid_out_trade_no_way`(`bid`, `out_trade_no`, `way`) USING BTREE COMMENT '同一种途径,BID和外商户号不能重复',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 11329 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci    NOT NULL,
    `bid`         char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci    NOT NULL COMMENT '商家唯一标识',
    `type`        tinyint(2) NOT NULL DEFAULT 2 COMMENT '1管理员,2操作员',
    `name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简单说明',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1272 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for role_rule
-- ----------------------------
DROP TABLE IF EXISTS `role_rule`;
CREATE TABLE `role_rule`
(
    `bid`       char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家唯一标识',
    `role_guid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色唯一标识',
    `rule_guid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限唯一标识',
    UNIQUE INDEX `uk_role_guid_rule_guid`(`role_guid`, `rule_guid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for rule
-- ----------------------------
DROP TABLE IF EXISTS `rule`;
CREATE TABLE `rule`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL,
    `parent_guid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级菜单唯一标识',
    `auto_log`    tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否自动记录日志',
    `name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'url地址',
    `title`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
    `icon`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
    `is_auth`     tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否鉴权',
    `type`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 接口 1 菜单 2 权限 ',
    `sort`        int(4) NOT NULL DEFAULT 255 COMMENT '排序',
    `status`      tinyint(1) NULL DEFAULT 1 COMMENT '状态1可用,0不可用',
    `update_time` datetime(3) NOT NULL DEFAULT '2018-01-01 00:00:00.000' COMMENT '更新时间',
    `create_time` datetime(3) NOT NULL DEFAULT '2018-01-01 00:00:00.000' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX         `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 863 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限&菜单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for short_url
-- ----------------------------
DROP TABLE IF EXISTS `short_url`;
CREATE TABLE `short_url`
(
    `title`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
    `code`         varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin    NOT NULL DEFAULT '',
    `long_url`     varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '长链接',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    `expires_time` datetime NULL DEFAULT NULL COMMENT '链接失效时间 NULL代表永久有效',
    PRIMARY KEY (`code`) USING BTREE,
    UNIQUE INDEX `code`(`code`) USING BTREE COMMENT 'code唯一'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for short_url_params
-- ----------------------------
DROP TABLE IF EXISTS `short_url_params`;
CREATE TABLE `short_url_params`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '所属商家',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '主键',
    `code`        varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin    NOT NULL DEFAULT '' COMMENT '短链接code',
    `title`       varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '链接标题',
    `type`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '类型 1: 易货买单链接',
    `base_url`    varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '基础链接',
    `params`      json NULL COMMENT '扩展参数',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `code`(`code`) USING BTREE COMMENT 'code唯一',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sms_callback
-- ----------------------------
DROP TABLE IF EXISTS `sms_callback`;
CREATE TABLE `sms_callback`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型',
    `channel`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通道',
    `result`      varchar(10240) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 46061 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sms_channel
-- ----------------------------
DROP TABLE IF EXISTS `sms_channel`;
CREATE TABLE `sms_channel`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属用户,NULL代表平台通道',
    `driver`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '驱动',
    `priority`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '优先级',
    `name`                varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通道名称 如 助通通道',
    `channel_provider_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '通道路由ID',
    `channel_parameter`   json                                                    NOT NULL COMMENT '通道级参数',
    `status`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0禁用',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付通道' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sms_code_note
-- ----------------------------
DROP TABLE IF EXISTS `sms_code_note`;
CREATE TABLE `sms_code_note`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家唯一标识',
    `mobile`       char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机号',
    `status`       tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态',
    `code`         int(6) NOT NULL COMMENT '验证码',
    `ip`           char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ip地址',
    `sending_time` datetime                                            NOT NULL COMMENT '发送成功时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 886 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sms_reply_note
-- ----------------------------
DROP TABLE IF EXISTS `sms_reply_note`;
CREATE TABLE `sms_reply_note`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `mobile`         varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `content`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `msg_id`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `reply_time`     datetime                                                NOT NULL,
    `create_time`    datetime(3) NOT NULL,
    `update_time`    datetime(3) NOT NULL,
    `send_note_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sms_send_note
-- ----------------------------
DROP TABLE IF EXISTS `sms_send_note`;
CREATE TABLE `sms_send_note`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '商户标识',
    `channel`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '通道标识',
    `mobile`            char(11) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '手机号',
    `content`           varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '短信内容',
    `send_date_time`    datetime NULL DEFAULT NULL COMMENT '发送时间',
    `receive_status`    tinyint(2) NOT NULL DEFAULT 0 COMMENT '接受状态 -1失败 0 等待接收 1 接受成功',
    `receive_msg`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接受描述',
    `receive_code`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收状态码',
    `receive_date_time` datetime NULL DEFAULT NULL COMMENT '接收时间',
    `request_id`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求标识,暂时适用阿里云',
    `out_send_id`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部发送唯一标识',
    `fee`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '短信计费条数',
    `message`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果描述',
    `code`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回状态码',
    `response`          varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求返回的数据包',
    `status`            tinyint(2) NULL DEFAULT 0 COMMENT '短信发送状态 0 等待发送 -1 发送失败 1 发送成功',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `out_id`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部ID,暂时适用阿里云',
    `sign_name`         varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '短信签名',
    `send_sign_name`    varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '实际发送签名',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 71556 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sms_template
-- ----------------------------
DROP TABLE IF EXISTS `sms_template`;
CREATE TABLE `sms_template`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `channel`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '通道',
    `name`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
    `type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板类型',
    `content`     varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `template_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistic_task
-- ----------------------------
DROP TABLE IF EXISTS `statistic_task`;
CREATE TABLE `statistic_task`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务id',
    `name`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '任务名称',
    `db`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '数据库',
    `sql`           text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'sql语句',
    `require_total` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否需要合计',
    `email_address` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '收件人,多个英文 ; 隔开',
    `status`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 启用 0 停用',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for storage
-- ----------------------------
DROP TABLE IF EXISTS `storage`;
CREATE TABLE `storage`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `type`        char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `config`      json NULL COMMENT '配置参数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for store
-- ----------------------------
DROP TABLE IF EXISTS `store`;
CREATE TABLE `store`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT 'guid标识',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '商家标识',
    `sort`              int(10) NOT NULL DEFAULT 1 COMMENT '序号.越小越靠前(无定位排序的时候)',
    `pic1`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `pic`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '形象图片',
    `store_name`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '门店名称',
    `mobile`            char(20) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '手机号',
    `industry_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '行业',
    `brand_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '品牌',
    `latitude`          decimal(10, 7)                                           NOT NULL DEFAULT 0.0000000 COMMENT '经度',
    `user_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '默认挂靠用户guid',
    `longitude`         decimal(10, 7)                                           NOT NULL DEFAULT 0.0000000 COMMENT '纬度',
    `description`       varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
    `province_id`       int(11) NOT NULL DEFAULT 0 COMMENT '省份id',
    `city_id`           int(11) NOT NULL DEFAULT 0 COMMENT '市id',
    `area_id`           int(11) NOT NULL DEFAULT 0 COMMENT '区id',
    `address`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '详细地址',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '0下线 1上线',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `business_time`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '营业时间 00:00 --24:00',
    `business_week_day` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '营业日 周日  1,2,3,4,5,6,0',
    `pic2`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `pic3`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `icon`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 156 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for store_tmpl
-- ----------------------------
DROP TABLE IF EXISTS `store_tmpl`;
CREATE TABLE `store_tmpl`
(
    `id`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `title`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `class1`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `class2`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `person_used` int(11) NULL DEFAULT NULL,
    `invalid`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `reason`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    `page`        int(11) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchant
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchant`;
CREATE TABLE `sub_merchant`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `merchant_id`        bigint(20) NOT NULL DEFAULT 0 COMMENT '商户号',
    `merchant_name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商户名称',
    `industry`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '行业',
    `wechat_appid`       varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '微信公众号appid',
    `business_account`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商家账号',
    `pay_appid`          varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '支付APPID',
    `jsapi_path`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '支付目录',
    `subscribe_appid`    varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '关注APPID',
    `appid_config_list`  varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'APPID配置列表,从微信获取',
    `jsapi_path_list`    varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '支付目录,从微信获取',
    `merchant_status`    tinyint(2) NOT NULL DEFAULT 0 COMMENT '-1 其他 0 未知 1 有效 2 错误商户号 3商户号正确但是归属关系不正确',
    `pay_appid_status`   tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 未知 1 平台公众号 2 商户自己公众号',
    `pay_status`         tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 待审核 1 已通过',
    `status`             tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 -1 待发起授权 0 待商户同意 1 已通过',
    `result`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '最新结果',
    `apply_openid`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '申请人OPENID',
    `apply_time`         datetime(3) NULL DEFAULT NULL COMMENT '申请提交时间',
    `apply_appid`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '申请人APPID',
    `submit_time`        datetime(3) NULL DEFAULT NULL COMMENT '发起授权时间',
    `examine_time`       datetime(3) NULL DEFAULT NULL COMMENT '审核通过时间',
    `config_update_time` datetime(3) NULL DEFAULT NULL COMMENT '配置更新时间',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `merchant_id`(`merchant_id`) USING BTREE COMMENT '商户号不允许重复'
) ENGINE = InnoDB AUTO_INCREMENT = 7481 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchant_message
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchant_message`;
CREATE TABLE `sub_merchant_message`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `message_id`       bigint(20) NOT NULL COMMENT '消息ID',
    `message_title`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '消息标题',
    `message_category` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '消息类别',
    `message_time`     datetime                                                NOT NULL COMMENT '消息时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7024 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchant_query_note
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchant_query_note`;
CREATE TABLE `sub_merchant_query_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `merchant_id` char(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `appid`       char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `openid`      char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `way`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '途径',
    `status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 待通知 1已通知',
    `notify_time` datetime(3) NULL DEFAULT NULL COMMENT '通知时间',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9792 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchant_rate_apply_note
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchant_rate_apply_note`;
CREATE TABLE `sub_merchant_rate_apply_note`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `merchant_id`         char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商户号',
    `after_rate`          decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '审核通过后费率',
    `rate`                decimal(10, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '申请费率',
    `category`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '行业',
    `merchant_name`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
    `merchant_short_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商户简称',
    `apply_openid`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请人openid',
    `apply_appid`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请人appid',
    `status`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待处理 1已通过',
    `apply_id`            int(10) NOT NULL DEFAULT 0 COMMENT '申请单ID',
    `examine_time`        datetime NULL DEFAULT NULL COMMENT '审核通过时间',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `merchant_id_apply_id`(`merchant_id`, `apply_id`) USING BTREE COMMENT '商户号+申请编号不允许重复'
) ENGINE = InnoDB AUTO_INCREMENT = 6119 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchants
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchants`;
CREATE TABLE `sub_merchants`
(
    `id`                    bigint(20) NOT NULL COMMENT '申请单单号',
    `partner_merchant_id`   bigint(20) NOT NULL DEFAULT 0 COMMENT '服务商商户号',
    `merchant_id`           bigint(20) NOT NULL DEFAULT 0 COMMENT '子商户号',
    `applyment_modify_time` datetime NULL DEFAULT NULL COMMENT '申请单更新时间',
    `merchant_name`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
    `company_name`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '公司名称',
    `category`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '行业',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `merchant_id`(`merchant_id`) USING BTREE COMMENT '商户号不允许重复'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sub_merchants_list
-- ----------------------------
DROP TABLE IF EXISTS `sub_merchants_list`;
CREATE TABLE `sub_merchants_list`
(
    `id`                    bigint(20) NOT NULL COMMENT '申请单单号',
    `merchant_id`           bigint(20) NOT NULL DEFAULT 0 COMMENT '商户号',
    `applyment_modify_time` datetime NULL DEFAULT NULL COMMENT '申请单更新时间',
    `merchant_name`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
    `company_name`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '公司名称',
    `category`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '行业',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `merchant_id`(`merchant_id`) USING BTREE COMMENT '商户号不允许重复'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_area
-- ----------------------------
DROP TABLE IF EXISTS `system_area`;
CREATE TABLE `system_area`
(
    `id`       int(10) UNSIGNED NOT NULL COMMENT 'ID',
    `name`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地区名',
    `pid`      int(10) NOT NULL COMMENT '父ID',
    `zip_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮编',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX      `parentid`(`pid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `title`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
    `type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'text' COMMENT '类型',
    `group`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分组',
    `key_name`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '键名',
    `value`       text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '值',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for table_example
-- ----------------------------
DROP TABLE IF EXISTS `table_example`;
CREATE TABLE `table_example`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员标识',
    `delete_time` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tag
-- ----------------------------
DROP TABLE IF EXISTS `tag`;
CREATE TABLE `tag`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `sort`        int(10) NOT NULL DEFAULT 1 COMMENT '排序,越小越靠前',
    `icon`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '品牌名称',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '0下架 1上架',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for team
-- ----------------------------
DROP TABLE IF EXISTS `team`;
CREATE TABLE `team`
(
    `id`         mediumint(8) NOT NULL AUTO_INCREMENT,
    `username`   varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
    `userpass`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `userpass2`  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `fullname`   varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `weixinid`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信id',
    `crmpsw`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'CRM登陆密码',
    `sale_value` float(11, 0
) NULL DEFAULT NULL COMMENT '当月销售额',
  `year_value` float(11, 0) NULL DEFAULT 0 COMMENT '全年销售额',
  `level` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '级别ABCD级别',
  `team` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在分组',
  `tel` char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `qq` char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'qq号',
  `email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `lastnotice` datetime NULL DEFAULT NULL,
  `owner` char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'CRM中用户标识',
  `XTSID` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用于模拟登陆CRM的cookies',
  `guid` char(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `group` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for temp
-- ----------------------------
DROP TABLE IF EXISTS `temp`;
CREATE TABLE `temp`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `key`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `value`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '测试',
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for temp2
-- ----------------------------
DROP TABLE IF EXISTS `temp2`;
CREATE TABLE `temp2`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `key`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `value`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '测试',
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for test
-- ----------------------------
DROP TABLE IF EXISTS `test`;
CREATE TABLE `test`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `key`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `json`        json NULL,
    `content`     text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `time`        datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `update_time` datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3),
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for topic
-- ----------------------------
DROP TABLE IF EXISTS `topic`;
CREATE TABLE `topic`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                   char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL COMMENT '主键',
    `bid`                    char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL,
    `type`                   int(11) NOT NULL COMMENT '分类',
    `title`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
    `sub_title`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '副标题（未用）',
    `content`                longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专题内容',
    `layout`                 smallint(1) NOT NULL DEFAULT 0 COMMENT '布局方式：0=小图，1=大图模式',
    `sort`                   int(11) NOT NULL DEFAULT 1 COMMENT '排序：升序 越小越靠前',
    `cover_pic`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图',
    `read_count`             int(11) NOT NULL DEFAULT 0 COMMENT '阅读量',
    `agree_count`            int(11) NOT NULL DEFAULT 0 COMMENT '点赞数（未用）',
    `virtual_read_count`     int(11) NOT NULL DEFAULT 0 COMMENT '虚拟阅读量',
    `virtual_agree_count`    int(11) NOT NULL DEFAULT 0 COMMENT '虚拟点赞数（未用）',
    `virtual_favorite_count` int(11) NOT NULL DEFAULT 0 COMMENT '虚拟收藏量',
    `qrcode_pic`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义分享图片(海报图)',
    `app_share_title`        varchar(65) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '自定义分享标题',
    `is_chosen`              tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否精选',
    `status`                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 下线 1 上线',
    `is_delete`              tinyint(11) NOT NULL DEFAULT 0 COMMENT '删除',
    `delete_time`            datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3),
    `create_time`            datetime(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000' COMMENT '删除时间',
    `update_time`            datetime(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000' COMMENT '创建时间',
    `pic_list`               longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `detail`                 longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `abstract`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '摘要',
    `tag`                    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '标签',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                    `store_id`(`bid`) USING BTREE,
    INDEX                    `is_delete`(`is_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for translate_note
-- ----------------------------
DROP TABLE IF EXISTS `translate_note`;
CREATE TABLE `translate_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `user_id`     int(11) NOT NULL DEFAULT 0,
    `user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `source_lang` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `target_lang` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `source_text` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `target_text` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `status`      tinyint(2) NOT NULL DEFAULT 0,
    `code`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `length`      int(10) NOT NULL DEFAULT 0,
    `create_time` datetime(3) NOT NULL,
    `update_time` datetime(3) NOT NULL,
    `message`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `response`    text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `sid`         bigint(20) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX         `bid_guid`(`guid`, `bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trial_application
-- ----------------------------
DROP TABLE IF EXISTS `trial_application`;
CREATE TABLE `trial_application`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键guid',
    `company`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公司名称',
    `true_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '联系人',
    `mobile`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机号',
    `bid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家guid',
    `member_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员guid',
    `ip`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册ip',
    `ip_region_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ip信息',
    `version_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '版本guid',
    `status`         tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待审核 1 已通过',
    `create_time`    datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`    datetime(3) NOT NULL COMMENT '更新时间',
    `examine_time`   datetime(3) NULL DEFAULT NULL COMMENT '审核时间',
    `account`        varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '登录账号',
    `password`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '初始密码',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 116 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for txt_file
-- ----------------------------
DROP TABLE IF EXISTS `txt_file`;
CREATE TABLE `txt_file`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `appid`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `file_name`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `file_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `memo`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`  datetime(3) NULL DEFAULT NULL,
    `update_time`  datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for union_line_number
-- ----------------------------
DROP TABLE IF EXISTS `union_line_number`;
CREATE TABLE `union_line_number`
(
    `id`        int(11) NOT NULL DEFAULT 0,
    `bkid`      varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bkname`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `tel`       char(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bkaddress` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `page`      int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for upgrade_note
-- ----------------------------
DROP TABLE IF EXISTS `upgrade_note`;
CREATE TABLE `upgrade_note`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `date`              date                                                     NOT NULL COMMENT '日期',
    `title`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '标题',
    `rule_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '关联权限guid',
    `version_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '版本guid',
    `version_number`    varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '版本号',
    `src_file`          varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '升级包文件',
    `content`           varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '内容',
    `status`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    `version_guid_json` json NULL COMMENT '版本guid json',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                      char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL COMMENT '主键guid',
    `bid`                       char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL COMMENT '商家唯一标识',
    `yky_user_guid`             char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT '关联一卡易用户guid',
    `yky_chain_store_guid`      char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT '关联一卡易门店guid',
    `parent_guid`               char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `account`                   char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL COMMENT '工号',
    `name`                      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户姓名',
    `email`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮件地址',
    `password`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户密码',
    `role_guid`                 char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `status`                    tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `sex`                       tinyint(2) NULL DEFAULT 0 COMMENT '0：保密 1：男 2：女',
    `money`                     decimal(18, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '资金余额',
    `store_guid`                char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作门店guid',
    `head`                      varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像地址',
    `birthday`                  date NULL DEFAULT '1000-01-01' COMMENT '生日',
    `login_failed_times`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '登录失败次数',
    `last_login_failed_time`    datetime NULL DEFAULT NULL COMMENT '最后登录失败时间',
    `last_password_update_time` datetime NULL DEFAULT NULL COMMENT '密码最后更新时间',
    `last_login_time`           datetime NULL DEFAULT NULL COMMENT '最后登录时间',
    `usb_key_decrypt_key`       char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT 'ukey解密密钥',
    `usb_key_token`             char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT 'ukey密码',
    `usb_key_id`                char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '' COMMENT 'ukey编号',
    `enable_usb_key`            tinyint(2) NOT NULL DEFAULT 0 COMMENT '开启ukey登录(默认关闭)',
    `openid_bak`                char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '在平台的openid,用于推送消息(已废弃)',
    `tel`                       char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电话号码',
    `brokerage_ratio`           decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '分销比例',
    `memo`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `brokerage_probability`     decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '分销概率,用于丢单,虚拟数据(已废弃)',
    `create_time`               datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`               datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time`               datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `user_guid_json`            json NULL,
    `data_range_type`           tinyint(2) NULL DEFAULT 0 COMMENT '0 无限制 1 限制部分用户',
    `goods_guid_json`           json NULL COMMENT '商品guid_json 用于供应商自动指派订单',
    `goods_category_guid_json`  json NULL COMMENT '商品类别guid json 用于供应商自动指派订单',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `account`(`bid`, `account`, `create_time`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX                       `bid_guid`(`guid`, `bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1718 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for user_bind_note
-- ----------------------------
DROP TABLE IF EXISTS `user_bind_note`;
CREATE TABLE `user_bind_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `from`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程序',
    `way`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 公众号 2小程序',
    `openid`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信openid',
    `appid`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '公众号或者小程序appid',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态 1 绑定 0 解绑',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 575 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_money_note
-- ----------------------------
DROP TABLE IF EXISTS `user_money_note`;
CREATE TABLE `user_money_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单据号',
    `relation_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `relation_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL DEFAULT '' COMMENT '关联标识,用于退单追溯',
    `user_guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '会员标识',
    `balance`              decimal(18, 2)                                         NOT NULL COMMENT '充值后余额',
    `money`                decimal(18, 2)                                         NOT NULL COMMENT '金额',
    `type`                 tinyint(2) NOT NULL COMMENT '类型 -1 扣除 1 增加',
    `way`                  tinyint(2) NOT NULL COMMENT '途径 1 商品订单分享奖励',
    `status`               tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 成功 -1 撤销',
    `operator_user_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户标识',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `unique_code`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '唯一码',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX                  `bid`(`bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`
(
    `id`          mediumint(8) NOT NULL AUTO_INCREMENT,
    `username`    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `fullname`    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `tel`         varchar(13) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `qq`          varchar(13) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `email`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `reg_time`    datetime NULL DEFAULT NULL,
    `reg_ip`      varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `guid`        char(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `wechat_id`   varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `source`      varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `referer`     varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `company`     varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `industry`    varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `agent`       varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `agentname`   varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `is_exist`    varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `is_intocrm`  varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `addRes`      varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `crmId`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `sourcename`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `state`       int(8) NULL DEFAULT 1,
    `update_time` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3365 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '',
    `name`              char(255) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '版本名称',
    `status`            tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1启用 0 禁用',
    `trial_days`        int(10) NOT NULL DEFAULT 7 COMMENT '免费试用天数,0代表永久有效',
    `type`              tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型 1 软件版本 2 功能包',
    `memo`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
    `default_home_page` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '默认首页',
    `logo`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'logo图片',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for version_page
-- ----------------------------
DROP TABLE IF EXISTS `version_page`;
CREATE TABLE `version_page`
(
    `page_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `version_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '版本唯一标识',
    UNIQUE INDEX `uk_page_guid_version_guid`(`page_guid`, `version_guid`) USING BTREE COMMENT '同一个页面同一个规则唯一'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for version_rule
-- ----------------------------
DROP TABLE IF EXISTS `version_rule`;
CREATE TABLE `version_rule`
(
    `rule_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `version_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '版本唯一标识',
    UNIQUE INDEX `uk_rule_guid_version_guid`(`rule_guid`, `version_guid`) USING BTREE COMMENT '同一个版本同一个规则唯一'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for wb_region
-- ----------------------------
DROP TABLE IF EXISTS `wb_region`;
CREATE TABLE `wb_region`
(
    `id` double NULL DEFAULT NULL,
    `region_code`         varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `region_name`         varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `fid` double NULL DEFAULT NULL,
    `region_name_en`      varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `region_shortname_en` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `level`               tinyint(2) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for weapp_apply_privacy_interface_note
-- ----------------------------
DROP TABLE IF EXISTS `weapp_apply_privacy_interface_note`;
CREATE TABLE `weapp_apply_privacy_interface_note`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `api_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `component_appid` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `appid`           char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `status`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 审核中 2 审核不通过 3 审核通过',
    `reason`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原因',
    `audit_id`        char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核单id',
    `audit_time`      datetime NULL DEFAULT NULL COMMENT '审核时间',
    `apply_time`      datetime NULL DEFAULT NULL COMMENT '申请时间',
    `create_time`     datetime(3) NOT NULL,
    `update_time`     datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for weapp_commit_note
-- ----------------------------
DROP TABLE IF EXISTS `weapp_commit_note`;
CREATE TABLE `weapp_commit_note`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `component_appid` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '平台appid',
    `appid`           char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '小程序APPID',
    `template_id`     int(10) NOT NULL COMMENT '模板ID',
    `ext_json`        json NULL COMMENT '扩展信息',
    `user_version`    varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '版本号',
    `user_desc`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
    `status`          tinyint(2) NOT NULL DEFAULT -1 COMMENT '-1发布失败 0等待发布 1 发布成功,等待提交审核 2 提审成功',
    `reason`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '当status=1，审核被拒绝时，返回的拒绝原因',
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1459 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for weapp_submit_note
-- ----------------------------
DROP TABLE IF EXISTS `weapp_submit_note`;
CREATE TABLE `weapp_submit_note`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL,
    `component_appid`       char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '平台APPID',
    `appid`                 char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '小程序APPID',
    `submit_data`           json NULL COMMENT '提交审核数据',
    `auditid`               int(10) NOT NULL DEFAULT 0 COMMENT '审核编号',
    `status`                tinyint(2) NOT NULL DEFAULT -2 COMMENT '-3 撤销审核-2 等待提交 -1提交失败 0为审核成功，1为审核失败，2为审核中 , 3发布成功 ,4审核延后',
    `reason`                varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '当status=1，审核被拒绝时，返回的拒绝原因',
    `screenshot`            varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '当 status = 1 时，会返回审核失败的小程序截图示例 用 | 分隔的 media_id 的列表，可通过获取永久素材接口拉取截图内容',
    `audit_time`            datetime NULL DEFAULT NULL COMMENT '审核通过时间',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    `screenshot_image_list` json NULL COMMENT '截图图片列表 web可访问的url ',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 573 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for weapp_template_draft_list
-- ----------------------------
DROP TABLE IF EXISTS `weapp_template_draft_list`;
CREATE TABLE `weapp_template_draft_list`
(
    `id`                       int(10) NOT NULL AUTO_INCREMENT,
    `component_appid`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `source_miniprogram`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小程序名称',
    `source_miniprogram_appid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT 'APPID',
    `template_type`            tinyint(2) NOT NULL DEFAULT 0 COMMENT '0对应普通模板，1对应标准模板',
    `developer`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开发者',
    `draft_id`                 int(10) NOT NULL COMMENT '草稿id',
    `user_desc`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本描述',
    `user_version`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本',
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1772 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for weapp_template_list
-- ----------------------------
DROP TABLE IF EXISTS `weapp_template_list`;
CREATE TABLE `weapp_template_list`
(
    `id`                       int(10) NOT NULL AUTO_INCREMENT,
    `component_appid`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `source_miniprogram`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '小程序名称',
    `source_miniprogram_appid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT 'APPID',
    `developer`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开发者',
    `template_id`              int(10) NOT NULL COMMENT '模版id',
    `user_desc`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本描述',
    `user_version`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本',
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`, `component_appid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64429 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for weapp_url_link
-- ----------------------------
DROP TABLE IF EXISTS `weapp_url_link`;
CREATE TABLE `weapp_url_link`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `appid`       char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `path`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `query`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `url_link`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    `env_version` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for weapp_user_info
-- ----------------------------
DROP TABLE IF EXISTS `weapp_user_info`;
CREATE TABLE `weapp_user_info`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `appid`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `openid`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `unionid`     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `session_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `appid_openid`(`appid`, `openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 387 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for website_contact
-- ----------------------------
DROP TABLE IF EXISTS `website_contact`;
CREATE TABLE `website_contact`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `url`         varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '网站地址',
    `email`       varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮箱,多个用英文分号隔开',
    `facebook`    varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'Facebook联系方式,多个用英文分号隔开',
    `memo`        varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注,如失败原因',
    `status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待处理 -1处理失败 1处理完毕',
    `delete_time` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16411 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for websocket_user
-- ----------------------------
DROP TABLE IF EXISTS `websocket_user`;
CREATE TABLE `websocket_user`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `channel`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '通道websocket或者socket',
    `uid`                 varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '客户端标识',
    `platform`            varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台 web app weapp 等',
    `type`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '类型 会员 还是 用户',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家标识',
    `user_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户标识',
    `member_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `first_login_time`    datetime NULL DEFAULT NULL COMMENT '首次登陆时间',
    `last_login_time`     datetime NULL DEFAULT NULL COMMENT '最后一次登陆时间',
    `status`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 离线 1 在线',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    `last_login_out_time` datetime NULL DEFAULT NULL COMMENT '最后退出时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uid_channel`(`uid`, `channel`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 532 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_activity
-- ----------------------------
DROP TABLE IF EXISTS `wechat_activity`;
CREATE TABLE `wechat_activity`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `appid`         char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `card_id`       varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `activity_id`   int(10) NOT NULL DEFAULT 0 COMMENT '活动ID',
    `activity_info` json NULL,
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_card
-- ----------------------------
DROP TABLE IF EXISTS `wechat_card`;
CREATE TABLE `wechat_card`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `appid`         char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `type`          tinyint(2) NOT NULL DEFAULT 1 COMMENT '1代金券 2 会员卡',
    `card_id`       varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `card_info`     json NULL,
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    `audit_time`    datetime NULL DEFAULT NULL COMMENT '审核时间',
    `refuse_reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核拒绝原因',
    `audit_status`  tinyint(2) NOT NULL DEFAULT 0 COMMENT '审核状态 -1 未通过 0待审核 1 已通过',
    `status`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '0 禁用 1启用',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_component_verify_ticket
-- ----------------------------
DROP TABLE IF EXISTS `wechat_component_verify_ticket`;
CREATE TABLE `wechat_component_verify_ticket`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `component_appid`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '开放平台appid',
    `component_verify_ticket` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '开放平台ticket',
    `create_time`             datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 338182 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_config
-- ----------------------------
DROP TABLE IF EXISTS `wechat_config`;
CREATE TABLE `wechat_config`
(
    `id`                        bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `component_appid`           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台APPID',
    `authorizer_appid`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众号或小程序APPID',
    `encodingaeskey`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '加密密钥',
    `authorizer_appsecret`      varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'appsecret',
    `authorizer_access_token`   varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序Token',
    `authorizer_encodingaeskey` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '解密密钥',
    `authorizer_expires_in`     bigint(20) NULL DEFAULT NULL COMMENT '有效期（在授权的公众号或小程序具备API权限时，才有此返回值）',
    `token`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `authorizer_refresh_token`  varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序刷新Token',
    `func_info`                 varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序集权',
    `principal_name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序的主体名称',
    `nick_name`                 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序昵称',
    `head_img`                  varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序头像',
    `expires_in`                bigint(20) NULL DEFAULT NULL COMMENT 'Token有效时间',
    `service_type`              tinyint(2) NULL DEFAULT NULL COMMENT '公众号或小程序实际类型',
    `authorization_info`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '授权权限信息',
    `service_type_info`         tinyint(2) NULL DEFAULT NULL COMMENT '服务类型信息 1订阅号 2服务号\r\n授权方公众号类型，0代表订阅号，1代表由历史老帐号升级后的订阅号，2代表服务号\r\n\r\n',
    `verify_type`               tinyint(2) NULL DEFAULT NULL COMMENT '公众号或小程序实际认证类型',
    `verify_type_info`          tinyint(2) NULL DEFAULT NULL COMMENT '公众号或小程序认证类型\r\n授权方认证类型，-1代表未认证，0代表微信认证，1代表新浪微博认证，2代表腾讯微博认证，3代表已资质认证通过但还未通过名称认证，4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证\r\n',
    `user_name`                 varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序原始账号',
    `alias`                     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序别名',
    `qrcode_url`                varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号或小程序二维码地址',
    `business_info`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用以了解以下功能的开通状况（0代表未开通，1代表已开通）：\n\n open_store:是否开通微信门店功能\n\n open_scan:是否开通微信扫商品功能\n\n open_pay:是否开通微信支付功能\n\n open_card:是否开通微信卡券功能\n\n open_shake:是否开通微信摇一摇功能',
    `idc`                       tinyint(1) UNSIGNED NULL DEFAULT NULL,
    `mini_program_info`         json NULL COMMENT '小程序配置信息',
    `meno`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `partnerkey`                char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信支付密钥',
    `ssl_cer`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户证书CER文件路径',
    `ssl_cer_string`            text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商户证书CER文件字符串',
    `ssl_key`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户证书KEY文件路径',
    `ssl_key_string`            text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商户证书KEY文件字符串',
    `open_appid`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '开放平台帐号的APPID',
    `mch_id`                    char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户身份标识',
    `type`                      tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 公众号 2 小程序',
    `auto_commit_trial_version` tinyint(2) NOT NULL DEFAULT 0 COMMENT '自动提交测试版本',
    `status`                    tinyint(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态(1正常授权,0取消授权)',
    `domain_list`               varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '域名列表',
    `domain_status`             tinyint(2) NULL DEFAULT 0 COMMENT '域名获取状态',
    `account_info`              json NULL COMMENT '账号信息',
    `create_time`               datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`               datetime(3) NOT NULL COMMENT '更新时间',
    `unauthorized_time`         datetime NULL DEFAULT NULL COMMENT '解除授权时间',
    `realname_status`           tinyint(2) NOT NULL DEFAULT -1 COMMENT '实名验证状态',
    `principal_type`            tinyint(2) NOT NULL DEFAULT -1 COMMENT '主体类型',
    `wx_verify_info`            json NULL COMMENT '认证信息',
    `signature_info`            json NULL COMMENT '介绍信息',
    `head_image_info`           json NULL COMMENT '头像信息',
    `account_type`              tinyint(2) NOT NULL DEFAULT -1 COMMENT '帐号类型（1：订阅号，2：服务号，3：小程序）',
    `credential`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主体标识，非个人主体时返回的是企业或者政府或其他组织的代号',
    `customer_type`             tinyint(2) NOT NULL DEFAULT -1 COMMENT '认证类型；如果未完成微信认证则返回0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `index_wechat_config_authorizer_appid_component_appid`(`authorizer_appid`, `component_appid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 399 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for wechat_device
-- ----------------------------
DROP TABLE IF EXISTS `wechat_device`;
CREATE TABLE `wechat_device`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `app_scene`        varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT 'APP模式/SDK模式',
    `buyer_mch_code`   varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '铺设服务商商户号',
    `buyer_mch_name`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '铺设服务商名称',
    `camera_name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '摄像头名称',
    `camera_sn`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '摄像头编号',
    `device_category`  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '设备类型',
    `device_class`     varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '设备形态',
    `device_model`     varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '设备型号',
    `device_sn`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备SN',
    `inspect_status`   varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出厂检测状态',
    `inspect_time`     datetime NULL DEFAULT NULL COMMENT '出厂检测时间',
    `mch_code`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `operating_system` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作系统',
    `pur_state`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定状态',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `sn`(`device_sn`) USING BTREE COMMENT '设备SN唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 29744 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_device_activation_note
-- ----------------------------
DROP TABLE IF EXISTS `wechat_device_activation_note`;
CREATE TABLE `wechat_device_activation_note`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `activation_time` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22601 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_device_map
-- ----------------------------
DROP TABLE IF EXISTS `wechat_device_map`;
CREATE TABLE `wechat_device_map`
(
    `id`      bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SN',
    `mch_id`  char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户号',
    `date`    date NULL DEFAULT NULL COMMENT '截止日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64234 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_func_info
-- ----------------------------
DROP TABLE IF EXISTS `wechat_func_info`;
CREATE TABLE `wechat_func_info`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `type`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 公众号 2 小程序',
    `func_id`     int(10) NOT NULL DEFAULT 0,
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `mutual`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否互斥',
    `create_time` datetime(3) NULL DEFAULT NULL,
    `update_time` datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_give_carmera
-- ----------------------------
DROP TABLE IF EXISTS `wechat_give_carmera`;
CREATE TABLE `wechat_give_carmera`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `carmera_code`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `sn_code`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `business_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `carmera_owner` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 111 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_group
-- ----------------------------
DROP TABLE IF EXISTS `wechat_group`;
CREATE TABLE `wechat_group`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '商家标识',
    `content`     varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
    `price`       decimal(10, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '进群费用',
    `title`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '群名称',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `qrcode`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '群二维码',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_merchant_applyment
-- ----------------------------
DROP TABLE IF EXISTS `wechat_merchant_applyment`;
CREATE TABLE `wechat_merchant_applyment`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `create_bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `applyment_id`           char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '申请单编号',
    `sign_url`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签约链接',
    `applyment_state`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态\r\n1、APPLYMENT_STATE_EDITTING（编辑中）：提交申请发生错误导致，请尝试重新提交。\r\n2、APPLYMENT_STATE_AUDITING（审核中）：申请单正在审核中，超级管理员用微信打开"签约链接"，完成绑定微信号后，申请单进度将通过微信公众号通知超级管理员，引导完成后续步骤。\r\n3、APPLYMENT_STATE_REJECTED（已驳回）：请按照驳回原因修改申请资料，超级管理员用微信打开"签约链接"，完成绑定微信号，后续申请单进度将通过微信公众号通知超级管理员。\r\n4、APPLYMENT_STATE_TO_BE_CONFIRMED（待账户验证）：请超级管理员使用微信打开返回的"签约链接"，根据页面指引完成账户验证。\r\n5、APPLYMENT_STATE_TO_BE_SIGNED（待签约）：请超级管理员使用微信打开返回的"签约链接"，根据页面指引完成签约。\r\n6、APPLYMENT_STATE_SIGNING（开通权限中）：系统开通相关权限中，请耐心等待。\r\n7、APPLYMENT_STATE_FINISHED（已完成）：商户入驻申请已完成。\r\n8、APPLYMENT_STATE_CANCELED（已作废）：申请单已被撤销。',
    `applyment_state_msg`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态描述',
    `create_time`            datetime(3) NOT NULL,
    `update_time`            datetime(3) NOT NULL,
    `sub_mchid`              char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '子商户号,进件成功后返回',
    `auto_add_pay_parameter` tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否需要自动配置支付参数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `applyment_id`(`applyment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_red_packet_order
-- ----------------------------
DROP TABLE IF EXISTS `wechat_red_packet_order`;
CREATE TABLE `wechat_red_packet_order`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'guid主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家唯一标识',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员唯一标识',
    `mch_billno`           char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户单号',
    `way`                  tinyint(2) NOT NULL COMMENT '业务途径,1好评返现业务',
    `mch_id`               char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信商户号',
    `sub_mch_id`           char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信子商户号',
    `wxappid`              char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信appid',
    `msgappid`             char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消息触达的appid',
    `send_name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送名称',
    `re_openid`            char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收红包的openid',
    `total_amount`         int(11) NULL DEFAULT NULL COMMENT '发送金额 单位分',
    `total_num`            int(5) NULL DEFAULT NULL COMMENT '发送数量 一般是1',
    `remark`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '活动描述 ',
    `wishing`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '祝福语',
    `client_ip`            char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端ip',
    `act_name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '活动名称',
    `scene_id`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '场景id',
    `risk_info`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '活动信息',
    `consume_mch_id`       char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资金授权商户号',
    `reason`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
    `send_listid`          char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '红包订单的微信单号',
    `refund_amount`        int(10) NULL DEFAULT NULL COMMENT '红包退款金额',
    `refund_time`          datetime NULL DEFAULT NULL COMMENT '红包退款时间',
    `rcv_time`             datetime NULL DEFAULT NULL COMMENT '接收时间',
    `red_packet_send_time` datetime NULL DEFAULT NULL COMMENT '发送成功时间',
    `send_time`            datetime NULL DEFAULT NULL COMMENT '红包发送时间/微信返回',
    `red_packet_status`    char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SENDING:发放中 \r\nSENT:已发放待领取 \r\nFAILED：发放失败 \r\nRECEIVED:已领取 \r\nRFUND_ING:退款中 \r\nREFUND:已退款 ',
    `status`               tinyint(1) NOT NULL DEFAULT 0,
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `mch_billno_bid`(`mch_billno`, `bid`) USING BTREE,
    INDEX                  `bid`(`bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18254 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_scene_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `wechat_scene_qrcode`;
CREATE TABLE `wechat_scene_qrcode`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `appid`         char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `qrcode_url`    varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `scene_id`      char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `info`          json NULL,
    `create_time`   datetime(3) NULL DEFAULT NULL,
    `update_time`   datetime(3) NULL DEFAULT NULL,
    `type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 扫码绑定用户接受订单',
    `qrcode_ticket` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `user_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`, `appid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 248 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_template
-- ----------------------------
DROP TABLE IF EXISTS `wechat_template`;
CREATE TABLE `wechat_template`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `type`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板类型',
    `template_no` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '模板编号,用于调用api开通或者删除',
    `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_template_list
-- ----------------------------
DROP TABLE IF EXISTS `wechat_template_list`;
CREATE TABLE `wechat_template_list`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL,
    `type`        char(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '微信appid',
    `template_id` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '模板id',
    `appid`       char(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '公众号',
    `status`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态  1启用 0禁用',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `template_no` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '模板编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 157 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_transfers_order
-- ----------------------------
DROP TABLE IF EXISTS `wechat_transfers_order`;
CREATE TABLE `wechat_transfers_order`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT 'guid主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商家标识',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '会员标识',
    `appid`            char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信appid',
    `way`              tinyint(2) NOT NULL COMMENT '业务途径 1积分兑换 2 魔术包提现 3 卡券提现 4 卡券分销奖励',
    `mchid`            char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户号',
    `partner_trade_no` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交易单号',
    `openid`           char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信openid',
    `re_user_name`     char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
    `amount`           int(10) NOT NULL COMMENT '转账金额',
    `desc`             varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
    `status`           tinyint(1) NOT NULL DEFAULT 0 COMMENT '-1 失败 0 等待处理 1成功',
    `memo`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
    `message`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回消息',
    `payment_no`       char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单号',
    `payment_time`     datetime NULL DEFAULT NULL COMMENT '交易时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `bid_partner_trade_no`(`bid`, `partner_trade_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14049 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for wechat_user_info
-- ----------------------------
DROP TABLE IF EXISTS `wechat_user_info`;
CREATE TABLE `wechat_user_info`
(
    `id`                               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '主键',
    `appid`                            char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '公众号APPID',
    `openid`                           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户的标识，对当前公众号唯一',
    `subscribe`                        tinyint(4) NULL DEFAULT NULL COMMENT '用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。',
    `nickname`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户的昵称',
    `sex`                              tinyint(4) NULL DEFAULT NULL COMMENT '用户的性别，值为1时是男性，值为2时是女性，值为0时是未知',
    `language`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户的语言，简体中文为zh_CN',
    `city`                             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户所在城市',
    `province`                         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户所在国家',
    `country`                          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户所在省份',
    `headimgurl`                       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。',
    `subscribe_time`                   int(11) NULL DEFAULT NULL COMMENT '用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间',
    `unsubscribe_time`                 int(11) NULL DEFAULT NULL COMMENT '取消关注时间',
    `last_update_snsapi_userinfo_time` datetime NULL DEFAULT NULL COMMENT '最后一次强授权时间',
    `unionid`                          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段',
    `remark`                           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注',
    `groupid`                          tinyint(4) NULL DEFAULT NULL COMMENT '用户所在的分组ID',
    `tagid_list`                       json NULL COMMENT '用户被打上的标签ID列表',
    `qr_scene`                         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '场景',
    `qr_scene_str`                     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '场景字符',
    `subscribe_scene`                  varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENEPROFILE LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_OTHERS 其他',
    `latitude`                         decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
    `longitude`                        decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
    `precision`                        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地理位置精度',
    `create_time`                      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `openid_appid`(`openid`, `appid`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 230616 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for worker
-- ----------------------------
DROP TABLE IF EXISTS `worker`;
CREATE TABLE `worker`
(
    `id`                   int(8) NOT NULL,
    `email`                varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '逸创云客服邮箱',
    `responsiblesector`    varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责部门',
    `is_solved_work_order` int(8) NULL DEFAULT 0 COMMENT '已受理工单数量',
    `wechat_openid`        varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid/一卡易总部',
    `username`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `status`               tinyint(2) NULL DEFAULT 1 COMMENT '1正常 -1离职',
    `is_tester`            tinyint(2) NULL DEFAULT 1 COMMENT '是否测试人员',
    `productid`            int(8) NULL DEFAULT NULL COMMENT '禅道默认编号',
    `chandao_username`     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '禅道用户名',
    `qywechat_username`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业号用户名',
    `create_time`          datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xmf_access_token
-- ----------------------------
DROP TABLE IF EXISTS `xmf_access_token`;
CREATE TABLE `xmf_access_token`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `yky_member_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易会员标识',
    `token`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '令牌',
    `openid`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限',
    `expired_time`      datetime NULL DEFAULT NULL COMMENT 'token过期时间',
    `refresh_token`     varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '令牌刷新token',
    `last_refresh_time` datetime(3) NULL DEFAULT NULL COMMENT '最后刷新时间',
    `mobile`            char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for xmf_order
-- ----------------------------
DROP TABLE IF EXISTS `xmf_order`;
CREATE TABLE `xmf_order`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `yky_member_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '一卡易会员标识',
    `order_id`            char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家端订单id，全局唯一',
    `dec_money`           decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '扣除金额',
    `asset_type`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '支付账户类型：AB的AB部分，默认到可用账户 201',
    `huilv`               decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '人民币美元汇率',
    `bili`                decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '小蜜蜂货币',
    `rate`                decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '手续费',
    `exchange_rate`       decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '汇率',
    `exchange_rate_zh_cn` decimal(20, 18)                                         NOT NULL DEFAULT 0.000000000000000000 COMMENT '汇率_人民币',
    `openid`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收款用户的唯一id，开放平台授权给应用的用户唯一标识',
    `user_id`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收款用户uid(与openid必须存在一个) 同时存在优先取openid	',
    `after_amount`        decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '实际金额',
    `order_amount`        decimal(18, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '金额',
    `pay_coin_symbol`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '支付币种，大写如BTC',
    `status`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态  -1 失败 0 待处理 1 成功',
    `result`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果',
    `order_num`           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '返回数据',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `bid_order_id`(`bid`, `order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_activity_submit
-- ----------------------------
DROP TABLE IF EXISTS `yky_activity_submit`;
CREATE TABLE `yky_activity_submit`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `type`          tinyint(2) NOT NULL DEFAULT 0 COMMENT '报名类型 1商家 2代理商 3商户号',
    `activity_type` int(11) NOT NULL DEFAULT 0 COMMENT '活动类型 1 到店红包 2 刷脸立减 3 收银员奖励',
    `account`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '报名账号',
    `apply_openid`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请人openid',
    `apply_appid`   varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请人appid',
    `status`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 0 待处理 1已通过',
    `remark`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
    `examine_time`  datetime NULL DEFAULT NULL COMMENT '审核通过时间',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32222 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_agent_map
-- ----------------------------
DROP TABLE IF EXISTS `yky_agent_map`;
CREATE TABLE `yky_agent_map`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `appid`         char(50) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '公众号appid',
    `openid`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '微信用户id',
    `agent_account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代理商账号',
    `status`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态,1启用0禁用',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 441 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_bill
-- ----------------------------
DROP TABLE IF EXISTS `yky_bill`;
CREATE TABLE `yky_bill`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `member_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `bill_number`        char(30) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '单据号',
    `relation_rule_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联规则guid,防止重复奖励',
    `type`               tinyint(2) NOT NULL COMMENT '类型 1 送券',
    `info`               json                                                    NOT NULL COMMENT '信息 json',
    `status`             tinyint(2) NOT NULL COMMENT '状态',
    `message`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结果',
    `create_time`        datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`        datetime(3) NOT NULL COMMENT '更新时间',
    `relation_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联guid',
    `coupon_send_guid`   varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX ```bid``, ``member_guid``, ``bill_number``, ``relation_rule_guid```(`bid`, `member_guid`, `bill_number`, `relation_rule_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58913 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_chain_store
-- ----------------------------
DROP TABLE IF EXISTS `yky_chain_store`;
CREATE TABLE `yky_chain_store`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `store_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '门店名称',
    `contact`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '联系人',
    `tel`               varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '联系电话',
    `address`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '地址',
    `is_hide_html5`     tinyint(2) NOT NULL COMMENT '是否上架',
    `parent_store_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级门店',
    `description`       text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
    `meno`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
    `latitude`          float(10, 6
) NULL DEFAULT 0.000000 COMMENT '经度',
  `longitude` float(10, 6) NULL DEFAULT 0.000000 COMMENT '纬度',
  `enable_point_limit` tinyint(2) NOT NULL COMMENT '是否开启积分受限',
  `available_limit_point` decimal(18, 4) NOT NULL COMMENT '可用积分受限额',
  `enable_value_limit` tinyint(2) NOT NULL COMMENT '是否开启储值受限',
  `available_limit_value` decimal(18, 4) NOT NULL COMMENT '可用储值受限额',
  `available_limit_sms` decimal(18, 4) NOT NULL COMMENT '短信受限余额',
  `enable_sms_limit` tinyint(2) NOT NULL COMMENT '是否开启短信受限',
  `province_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '省份',
  `city_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '市',
  `county_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '区',
  `third_account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方平台商户账号/手机号(已废弃)',
  `reward_card_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '返利卡号',
  `rate` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '费率(已废弃)',
  `image_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `created_ime` datetime NULL DEFAULT NULL COMMENT '店铺创建时间',
  `create_time` datetime(3) NOT NULL COMMENT '数据库创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '数据库更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1942 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_consume_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_consume_note`;
CREATE TABLE `yky_consume_note`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '唯一标识',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员唯一标识',
    `card_id`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `is_undo`              tinyint(2) NOT NULL COMMENT '是否撤销',
    `chain_store_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '门店GUID',
    `way_desc`             char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务途径描述',
    `way`                  tinyint(2) NULL DEFAULT NULL COMMENT '业务途径',
    `bill_number`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '单据号',
    `meno`                 varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `true_name`            varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员姓名',
    `total_money`          decimal(18, 4)                                          NOT NULL COMMENT '应付金额',
    `total_paid`           decimal(18, 4)                                          NOT NULL COMMENT '实付金额',
    `paid_money`           decimal(18, 4)                                          NOT NULL COMMENT '现金支付',
    `paid_coupon`          decimal(18, 4)                                          NOT NULL COMMENT '优惠券支付',
    `paid_other`           decimal(18, 4)                                          NOT NULL COMMENT '其他支付',
    `paid_value`           decimal(18, 4)                                          NOT NULL COMMENT '储值支付',
    `paid_point`           decimal(18, 4)                                          NOT NULL COMMENT '积分支付',
    `paid_card`            decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '银行卡支付',
    `paid_thirdpay`        decimal(18, 4)                                          NOT NULL COMMENT '移动支付',
    `point`                decimal(18, 4)                                          NOT NULL COMMENT '获得积分',
    `thirdpay_type`        tinyint(2) NOT NULL COMMENT '移动支付类型',
    `store_name`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作店面',
    `user_account`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作工号',
    `operate_time`         datetime                                                NOT NULL COMMENT '操作时间',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `relation_bill_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联单号',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '处理状态 -1 处理失败 0 待处理 1 处理成功',
    `message`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理结果描述',
    `reward_message`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '奖励状态结果描述',
    `reward_status`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '奖励状态 -1 失败 0 等待奖励 1 奖励成功',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX                  `operate_time`(`operate_time`) USING BTREE,
    INDEX                  `bid_guid`(`guid`, `bid`) USING BTREE COMMENT '商家+guid索引',
    INDEX                  `bid`(`bid`) USING BTREE,
    INDEX                  `bid_member_guid`(`bid`, `member_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1696803 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for yky_consume_note_detail
-- ----------------------------
DROP TABLE IF EXISTS `yky_consume_note_detail`;
CREATE TABLE `yky_consume_note_detail`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `consume_note_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消费记录唯一标识',
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商户唯一标识',
    `goods_item_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商品唯一标识',
    `barcode`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
    `goods_item_sku_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '商品SKU描述',
    `sku_code`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'SKU编码',
    `good_item_name`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
    `total_money`         decimal(18, 6)                                          NOT NULL COMMENT '总金额',
    `number`              int(11) NOT NULL COMMENT '数量',
    `old_price`           decimal(18, 4)                                          NOT NULL COMMENT '原价',
    `paid_money`          decimal(18, 6)                                          NOT NULL COMMENT '实付金额',
    `discount`            decimal(10, 4)                                          NOT NULL COMMENT '折扣',
    `point`               decimal(18, 4)                                          NOT NULL COMMENT '获得积分',
    `meno`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`         datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX                 `bid_guid`(`guid`, `bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_consume_reward_value_rule
-- ----------------------------
DROP TABLE IF EXISTS `yky_consume_reward_value_rule`;
CREATE TABLE `yky_consume_reward_value_rule`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `title`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
    `goods_item_guid`   json NULL COMMENT '消费项目guid',
    `member_group_guid` json NULL COMMENT '会员级别guid',
    `chain_store_guid`  json NULL COMMENT '门店guid',
    `min_value`         decimal(19, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '起始金额(含)',
    `max_value`         decimal(19, 2)                                      NOT NULL DEFAULT 0.00 COMMENT '截止金额',
    `reward_rate`       decimal(10, 4)                                      NOT NULL DEFAULT 0.0000 COMMENT '赠送比例',
    `create_time`       datetime(3) NULL DEFAULT NULL COMMENT '规则创建时间',
    `update_time`       datetime(3) NULL DEFAULT NULL COMMENT '规则更新时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    `status`            tinyint(2) NULL DEFAULT 1 COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX               `bid`(`bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_coupon
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon`;
CREATE TABLE `yky_coupon`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券唯一标识',
    `bid`                        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `title`                      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券名称',
    `type_name`                  varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券类型名称',
    `coupon_value`               decimal(19, 4) NULL DEFAULT NULL COMMENT '面值（TypeName为折扣券时表示折扣系数，为代金券时表示面值）',
    `min_consume_value`          decimal(19, 4) NULL DEFAULT NULL COMMENT '最低消费金额',
    `content`                    text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '优惠详情（请用Unicode解码）',
    `total_count`                int(10) NULL DEFAULT 0 COMMENT '预发总数量',
    `send_count`                 int(10) NULL DEFAULT 0 COMMENT '已发送数量',
    `used_count`                 int(10) NULL DEFAULT 0 COMMENT '已核销数量',
    `end_date`                   varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '有效期描述',
    `create_time`                datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_time`              datetime NULL DEFAULT NULL COMMENT '最近更新时间',
    `user_account`               varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `use_store_guid_limit`       text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '优惠券限制核销店面唯一标识',
    `send_store_guid_limit`      text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '优惠券限制发送店面唯一标识',
    `use_store_name_limit`       varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券限制核销店面名称',
    `send_store_name_limit`      varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券限制发送店面名称',
    `goods_item_guid_limit`      text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限定使用消费项目唯一标识',
    `start_date_time`            datetime NULL DEFAULT NULL COMMENT '起始时间(不为空时,与EndDateTime组合表示有效期)',
    `send_date_time`             datetime NULL DEFAULT NULL COMMENT '截止时间(不为空时,与StartDateTime组合表示有效期)',
    `delay_days`                 int(10) NULL DEFAULT NULL COMMENT '不为空且时,与DurationDays组合表示 从下发第DelayDays日起，DurationDays内可用',
    `duration_days`              int(10) NULL DEFAULT NULL COMMENT '不为空且大于0时,与DelayDays组合表示 从下发第DelayDays日起，DurationDays内可用',
    `goods_item_type_guid_limit` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限定使用消费项目类别唯一标识\r\n',
    `limit_type`                 tinyint(2) NULL DEFAULT NULL COMMENT '限定类型（0：未限定，1：限定消费项目可用，2：限定消费项目不可用，3：限定消费项目类别可用，4：限定消费项目类别不可用）',
    `limit_per_type`             int(10) NULL DEFAULT NULL COMMENT '限定核销周期，与SSUseLimitNum共同生效（LimitPerType周期内，限用SSUseLimitNum张）\r\nLimitPerType=0表示不限周期，5 表示每天，4表示每周，3表示每月',
    `s_s_use_limit_num`          int(10) NULL DEFAULT NULL COMMENT '限定核销数量，与LimitPerType共同生效（LimitPerType周期内，限用SSUseLimitNum张，SSUseLimitNum=0表示不限定核销张数）',
    `image_path`                 varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '形象图片',
    `update_time`                datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 604 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_coupon_exchange_order
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_exchange_order`;
CREATE TABLE `yky_coupon_exchange_order`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商家唯一标识',
    `coupon_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '优惠券唯一标识',
    `coupon_send_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '优惠券发送记录唯一标识',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '会员唯一标识',
    `openid`           char(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '微信openid,暂时冗余',
    `cardid`           char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员卡号,暂时冗余',
    `exchange_type`    tinyint(2) NOT NULL COMMENT '兑换类型,1 储值  2 微信',
    `exchange_value`   float(12, 4
) NOT NULL COMMENT '兑换的面值多大',
  `third_billno` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三方单号',
  `result` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态',
  `meno` char(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `coupon_send_guid`(`coupon_send_guid`) USING BTREE,
  UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 18785 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for yky_coupon_send_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_send_note`;
CREATE TABLE `yky_coupon_send_note`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'guid主键',
    `bid`                        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `coupon_guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '券唯一标识',
    `card_id`                    char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `user_account`               char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送工号',
    `chain_store_guids`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '限制使用门店guid',
    `goods_item_guid_limit`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品限制guid',
    `goods_item_type_guid_limit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品类别限制guid',
    `limit_type`                 tinyint(2) NULL DEFAULT 0 COMMENT '	限定类型（0：未限定，1：限定消费项目可用，2：限定消费项目不可用，3：限定消费项目类别可用，4：限定消费项目类别不可用）',
    `title`                      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券标题',
    `type_name`                  char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券类型',
    `coupon_value`               decimal(18, 4) NULL DEFAULT NULL COMMENT '额度(代金券单位：元,折扣券单位：百分比)',
    `min_consume_value`          decimal(18, 4) NULL DEFAULT NULL COMMENT '消费满X元可用',
    `content`                    text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '优惠详情（请用Unicode解码）',
    `start_date`                 datetime NULL DEFAULT NULL COMMENT '生效日期',
    `end_date`                   datetime NULL DEFAULT NULL COMMENT '失效日期',
    `send_count`                 int(10) NULL DEFAULT NULL COMMENT '发送张数',
    `mobile`                     char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `flag`                       tinyint(2) NULL DEFAULT NULL COMMENT '状态 1可用 0不可用(相对获取时刻)',
    `coupon_code`                bigint(20) NULL DEFAULT NULL COMMENT '券号',
    `enable_count`               int(10) NULL DEFAULT NULL COMMENT '可用张数',
    `used_count`                 int(10) NULL DEFAULT NULL COMMENT '已用张数',
    `operate_time`               datetime NULL DEFAULT NULL COMMENT '发送时间',
    `send_fission_count`         int(10) NULL DEFAULT NULL COMMENT '优惠券已裂变张数',
    `fission_count`              int(10) NULL DEFAULT NULL COMMENT '优惠券可裂变张数',
    `image_path`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券图片',
    `create_time`                datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`                datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2813 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_coupon_send_rule
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_send_rule`;
CREATE TABLE `yky_coupon_send_rule`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `title`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
    `type`                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 单次消费赠送 2区间时间累计消费赠送',
    `member_group_guid`    json NULL COMMENT '会员级别guid',
    `chain_store_guid`     json NULL COMMENT '门店guid',
    `goods_item_guid`      json NULL COMMENT '消费项目guid',
    `min_value`            decimal(19, 2) NULL DEFAULT NULL COMMENT '起始金额(含)',
    `max_value`            decimal(19, 2) NULL DEFAULT NULL COMMENT '截止金额',
    `coupon_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优惠券guid',
    `num`                  int(10) NULL DEFAULT 0 COMMENT '赠送张数(暂时是倍数赠送)',
    `create_time`          datetime(3) NULL DEFAULT NULL COMMENT '规则创建时间',
    `update_time`          datetime(3) NULL DEFAULT NULL COMMENT '规则更新时间',
    `delete_time`          datetime NULL DEFAULT NULL COMMENT '删除时间',
    `status`               tinyint(2) NULL DEFAULT 1 COMMENT '状态',
    `goods_name_blacklist` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '商品名称 黑名单,多个,隔开 命中则不发券',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    INDEX                  `bid`(`bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_coupon_send_rule_item
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_send_rule_item`;
CREATE TABLE `yky_coupon_send_rule_item`
(
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `rule_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规则标识',
    `coupon_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券标识',
    `num`         int(11) NOT NULL DEFAULT 0 COMMENT '赠送张数',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    `delete_time` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`guid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_coupon_transfer_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_transfer_note`;
CREATE TABLE `yky_coupon_transfer_note`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `send_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '发送记录唯一标识',
    `old_send_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原发送记录唯一标识',
    `from_card_id`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '转出卡号',
    `coupon_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '券唯一标识',
    `to_card_id`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '转入卡号',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 18336 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for yky_coupon_used_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_coupon_used_note`;
CREATE TABLE `yky_coupon_used_note`
(
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `card_id`          char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `title`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `coupon_type`      varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `coupon_type_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `coupon_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `is_undo`          tinyint(2) NULL DEFAULT NULL,
    `mobile`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `user_account`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bill_number`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `used_count`       int(11) NOT NULL DEFAULT 0,
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `store_name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `operate_time`     datetime NULL DEFAULT NULL,
    `coupon_code`      varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`      datetime(3) NULL DEFAULT NULL,
    `update_time`      datetime(3) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_daily_reward_order
-- ----------------------------
DROP TABLE IF EXISTS `yky_daily_reward_order`;
CREATE TABLE `yky_daily_reward_order`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `rate`             decimal(10, 6)                                         NOT NULL DEFAULT 0.000000 COMMENT '返还比例',
    `yky_bill_number`  varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '奖励单号',
    `reward_card_id`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '奖励卡号',
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店标识',
    `level`            tinyint(2) NOT NULL DEFAULT 1 COMMENT '推荐层级',
    `trade_date`       date NULL DEFAULT NULL COMMENT '交易日期',
    `trade_money`      decimal(18, 4)                                         NOT NULL COMMENT '消费总额',
    `consume_value`    decimal(18, 4) NULL DEFAULT NULL COMMENT '需要消费金额',
    `reward_value`     decimal(18, 4)                                         NOT NULL COMMENT '奖励金额',
    `status`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态 -1失败 0等待处理 1成功',
    `message`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `uk_bid_chain_store_guid_trade_date_level`(`bid`, `chain_store_guid`, `trade_date`, `level`) USING BTREE COMMENT '同一门店一天奖励一次'
) ENGINE = InnoDB AUTO_INCREMENT = 3811 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_device
-- ----------------------------
DROP TABLE IF EXISTS `yky_device`;
CREATE TABLE `yky_device`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `sn_code`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `user_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `business_account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `business_name`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `user_account`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `store_name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
    `create_time`      datetime(3) NOT NULL,
    `update_time`      datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 163 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_goods
-- ----------------------------
DROP TABLE IF EXISTS `yky_goods`;
CREATE TABLE `yky_goods`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `barcode`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `name`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `gooods_item_typename`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `gooods_item_type_guid`    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `price`                    decimal(10, 2) NULL DEFAULT NULL,
    `image_path`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `is_bargain`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `reference_price`          decimal(10, 2) NULL DEFAULT NULL,
    `bargain_price`            decimal(10, 2) NULL DEFAULT NULL,
    `allow_bargain_discount`   varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `allow_bargain_point`      varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `is_hide`                  varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `unit`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `description`              longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    `meno`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `modified_time`            datetime NULL DEFAULT NULL,
    `delete_time`              datetime NULL DEFAULT NULL,
    `reward_recommended_point` decimal(10, 0) NULL DEFAULT 0 COMMENT '推荐人奖励积分数',
    `modified_user`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 2873 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_goods_type
-- ----------------------------
DROP TABLE IF EXISTS `yky_goods_type`;
CREATE TABLE `yky_goods_type`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商品类别唯一标识',
    `bid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `type`                    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '1、系统内置类别；2、计时类别（高级版本特有）；3、自定义类别',
    `name`                    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品类别名称',
    `meno`                    varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `index_no`                int(10) NULL DEFAULT 0 COMMENT '类别序号',
    `level_identity`          bigint(20) NULL DEFAULT NULL COMMENT '如果 (LevelIdentity &0x00FF0000)==0,则是一级类别\r\n如果 (LevelIdentity &0x00FF0000)>0,则是二级类别',
    `store_name_limit`        text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限制使用店面（店面名称，用逗号隔开）',
    `chain_store_guids_limit` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限制使用店面（店面唯一标识，用逗号隔开）',
    `create_time`             datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL COMMENT '更新时间',
    `child_types`             json                                                    NOT NULL COMMENT '子类别',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 320 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member
-- ----------------------------
DROP TABLE IF EXISTS `yky_member`;
CREATE TABLE `yky_member`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `bid`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `card_id`               varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员卡号',
    `member_group_name`     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员级别名称',
    `member_group_guid`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员级别标识',
    `true_name`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `sex`                   varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
    `mobile`                varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `id_card`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证',
    `tel`                   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话',
    `email`                 varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
    `address`               varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
    `chain_store_guid`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登记门店标识',
    `chain_store_name`      varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登记门店名称',
    `bind_status`           tinyint(2) NULL DEFAULT NULL COMMENT '绑定状态',
    `user_account`          varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号',
    `freezed_value`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冻结储值',
    `register_source`       tinyint(2) NULL DEFAULT NULL COMMENT '注册来源',
    `register_time`         datetime NULL DEFAULT NULL COMMENT '注册时间',
    `last_expense_time`     datetime NULL DEFAULT NULL COMMENT '最后消费时间',
    `total_point`           decimal(19, 4) NULL DEFAULT NULL COMMENT '累计积分',
    `available_point`       decimal(19, 4) NULL DEFAULT NULL COMMENT '可用积分',
    `total_value`           decimal(19, 4) NULL DEFAULT NULL COMMENT '累计储值',
    `available_value`       decimal(19, 4) NULL DEFAULT NULL COMMENT '可用储值',
    `total_money`           decimal(19, 4) NULL DEFAULT NULL COMMENT '累计支付金额',
    `meno`                  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `recommend_member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '推荐人guid',
    `recommend_card_id`     varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '推荐人卡号',
    `is_deleted`            varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否删除',
    `is_locked`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否锁定',
    `is_lunar`              varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否农历',
    `real_birth_day`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生日',
    `birthday`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生日',
    `modified_time`         datetime NULL DEFAULT NULL COMMENT '修改时间',
    `modified_user`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改工号',
    `province_name`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份',
    `city_name`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市',
    `county_name`           varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区',
    `image_path`            varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
    `third_open_id`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
    `duration_time`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '	有效期',
    `message`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '处理结果',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '处理状态 -1失败 0待处理 1成功',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `Guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56037 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_all_point_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_all_point_note`;
CREATE TABLE `yky_member_all_point_note`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `bill_number`      char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '单据号',
    `type`             tinyint(2) NOT NULL COMMENT '类型',
    `way`              tinyint(2) NOT NULL COMMENT '途径',
    `point`            decimal(19, 4)                                          NOT NULL COMMENT '积分值',
    `last_point`       decimal(19, 4)                                          NOT NULL COMMENT '当前可用积分',
    `meno`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
    `user_account`     varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '操作工号',
    `operate_time`     datetime                                                NOT NULL COMMENT '操作时间',
    `is_undo`          tinyint(2) NOT NULL COMMENT '是否撤销',
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '操作门店标识',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `card_id`          varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `true_name`        varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `mobile`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `store_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店名称',
    `message`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '结果',
    `status`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态 -1失败 0 待处理 1 成功',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX              `bid`(`bid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1256916 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_all_value_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_all_value_note`;
CREATE TABLE `yky_member_all_value_note`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `bill_number`      char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '单据号',
    `type`             tinyint(2) NOT NULL COMMENT '类型',
    `way`              tinyint(2) NOT NULL COMMENT '途径',
    `value`            decimal(19, 2)                                          NOT NULL COMMENT '金额',
    `paid_money`       decimal(19, 2) NULL DEFAULT NULL COMMENT '实付金额',
    `meno`             varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
    `user_account`     varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '操作工号',
    `operate_time`     datetime                                                NOT NULL COMMENT '操作时间',
    `is_undo`          tinyint(2) NOT NULL COMMENT '是否撤销',
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '操作门店标识',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员标识',
    `card_id`          varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `true_name`        varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `thirdpay_type`    tinyint(2) NULL DEFAULT NULL COMMENT '第三方支付类型',
    `paid_thirdpay`    decimal(18, 2) NULL DEFAULT NULL COMMENT '第三方支付金额',
    `other_pay_type`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他支付类型',
    `paid_other`       decimal(18, 2) NULL DEFAULT NULL COMMENT '其他支付',
    `paid_card`        decimal(18, 2) NULL DEFAULT NULL COMMENT '银行卡记账',
    `paid_cash`        decimal(18, 2) NULL DEFAULT NULL COMMENT '现金支付金额',
    `mobile`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `store_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店名称',
    `status`           tinyint(2) NOT NULL DEFAULT 0 COMMENT '处理状态',
    `message`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '处理结果',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    INDEX              `bid`(`bid`) USING BTREE,
    INDEX              `bid_member_guid`(`bid`, `member_guid`) USING BTREE,
    INDEX              `operate_time`(`operate_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 585504 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_delay_bill
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_delay_bill`;
CREATE TABLE `yky_member_delay_bill`
(
    `id`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bill_number`      varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据号',
    `user_account`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号',
    `member_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店标识',
    `operate_time`     datetime NULL DEFAULT NULL COMMENT '操作时间',
    `delay_after_time` date NULL DEFAULT NULL COMMENT '延期后到期时间',
    `card_id`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `true_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `store_name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店名称',
    `bill_type_id`     int(10) NULL DEFAULT NULL COMMENT '业务类型',
    `type`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付方式',
    `paid_money`       decimal(18, 4) NULL DEFAULT NULL COMMENT '支付金额',
    `create_time`      datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_group
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_group`;
CREATE TABLE `yky_member_group`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '级别唯一标识',
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `group_name`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '级别名称',
    `init_value`             decimal(18, 2) NULL DEFAULT NULL COMMENT '初始积分',
    `init_point`             decimal(18, 2) NULL DEFAULT NULL COMMENT '初始储值',
    `meno`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `card_price`             decimal(10, 2) NULL DEFAULT NULL COMMENT '售卡金额',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    `chain_store_guid_limit` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限定店面Guid（多个用逗号隔开）',
    `chain_store_name_limit` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '限定店面名称（多个用逗号隔开）',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 229 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_publish_goods
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_publish_goods`;
CREATE TABLE `yky_member_publish_goods`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '主键',
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '商家标识',
    `goods_name`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
    `goods_price`          decimal(10, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '商品价格',
    `image_path`           varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
    `description`          varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品描述',
    `is_publish_to_mall`   tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否发布到商城',
    `goods_item_type_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类别Guid',
    `goods_item_guid`      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发布成功后商品Guid',
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员guid',
    `create_time`          datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL COMMENT '更新时间',
    `full_image_path`      varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '图片地址',
    `delete_time`          datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
    `mobile`               char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系手机号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 108 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_upgrade_bill
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_upgrade_bill`;
CREATE TABLE `yky_member_upgrade_bill`
(
    `id`                       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`                      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `bill_number`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据号',
    `user_account`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号',
    `member_guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员标识',
    `chain_store_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店id',
    `operate_time`             datetime NULL DEFAULT NULL COMMENT '操作时间',
    `meno`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `before_member_group_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '升级前会员级别',
    `after_member_group_guid`  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '升级后会员级别',
    `card_id`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `true_name`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `store_name`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店名称',
    `before_group_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '升级前会员级别',
    `after_group_name`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '升级后会员级别',
    `bill_type_id`             int(10) NULL DEFAULT NULL COMMENT '业务类型',
    `type`                     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式',
    `value`                    decimal(18, 4) NULL DEFAULT NULL COMMENT '支付金额',
    `create_time`              datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_upgrade_return_bill
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_upgrade_return_bill`;
CREATE TABLE `yky_member_upgrade_return_bill`
(
    `id`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bill_number`          varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bill_type`            int(10) NULL DEFAULT NULL,
    `card_id`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `chain_store_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `detail_content`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `guid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `meno`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `old_account`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `old_bill_number`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `old_chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `old_operate_time`     datetime NULL DEFAULT NULL,
    `operate_time`         datetime NULL DEFAULT NULL,
    `s_i_d`                int(11) NULL DEFAULT 0,
    `true_name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `user_account`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `create_time`          datetime(3) NULL DEFAULT NULL,
    `update_time`          datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_member_value_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_member_value_note`;
CREATE TABLE `yky_member_value_note`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '主键',
    `bid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家标识',
    `user_account`            varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '操作工号',
    `card_id`                 varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '卡号',
    `value`                   decimal(18, 4)                                          NOT NULL COMMENT '充值金额',
    `value_plus`              decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '赠送金额',
    `paid_money`              decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '实付金额',
    `member_chain_store_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '会员登记门店标识',
    `member_guid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '会员标识',
    `memo`                    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`             datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL COMMENT '更新时间',
    `relation_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '关联guid',
    `bill_number`             char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消费成功单号',
    `way`                     tinyint(2) NOT NULL COMMENT '途径',
    `status`                  tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态码',
    `message`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回消息',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `way_relation_guid`(`relation_guid`, `way`) USING BTREE COMMENT '同一个单号,同一途径唯一',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 88740 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_plus_member
-- ----------------------------
DROP TABLE IF EXISTS `yky_plus_member`;
CREATE TABLE `yky_plus_member`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `card_id`               char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '会员卡号',
    `yky_member_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '一卡易会员guid',
    `total_buy_times`       int(10) NOT NULL DEFAULT 0 COMMENT '累积购买或续费次数',
    `total_consume_times`   int(10) NOT NULL DEFAULT 0 COMMENT 'plus会员期间累计消费次数',
    `total_consume_money`   decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT 'plus会员期间累计消费金额(仅限加油业务)',
    `total_add_value_times` int(10) NOT NULL DEFAULT 0 COMMENT 'plus会员期间累计充值次数',
    `total_add_value_money` decimal(18, 2)                                      NOT NULL DEFAULT 0.00 COMMENT 'plus会员期间累计充值金额(实收部分)',
    `first_buy_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '首次购买级别单据号',
    `first_buy_date_time`   datetime                                            NOT NULL COMMENT '首次购买日期',
    `last_buy_bill_number`  char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最后续费单号',
    `last_update_date`      date                                                NOT NULL DEFAULT '1970-01-01' COMMENT '最后统计日期(每日更新一次)',
    `last_buy_date_time`    datetime NULL DEFAULT NULL COMMENT '最后续费日期',
    `expired_time`          datetime NULL DEFAULT NULL COMMENT 'plus卡到期时间, 暂时未用到',
    `total_coupon_send_num` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券发放总张数',
    `total_coupon_used_num` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券使用总张数',
    `delete_time`           datetime NULL DEFAULT NULL COMMENT '删除时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `bid_yky_member_guid`(`bid`, `yky_member_guid`, `delete_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8394 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_plus_member_order
-- ----------------------------
DROP TABLE IF EXISTS `yky_plus_member_order`;
CREATE TABLE `yky_plus_member_order`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '主键',
    `card_id`                  char(30) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '卡号',
    `yky_member_guid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '一卡易会员标识',
    `bid`                      char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '商家标识',
    `start_time`               datetime                                               NOT NULL COMMENT '本周期开始时间',
    `end_time`                 datetime                                               NOT NULL COMMENT '本周期截止时间',
    `bill_type`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '1 购卡 2 续期 3 PLUS会员充值赠送 4 非PLUS会员充值赠送',
    `consume_times`            int(10) NOT NULL DEFAULT 0 COMMENT '本周期消费次数',
    `consume_money`            decimal(18, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '本周期消费金额',
    `add_value_times`          int(10) NOT NULL DEFAULT 0 COMMENT '本周期充值次数',
    `add_value_money`          decimal(18, 2)                                         NOT NULL COMMENT '本周期充值金额',
    `coupon_send_num`          int(10) NOT NULL DEFAULT 0 COMMENT '发送优惠券张数',
    `bill_number`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '购卡或者延期的单据号',
    `bill_operate_time`        datetime                                               NOT NULL COMMENT '订单操作时间',
    `return_bill_operate_time` datetime NULL DEFAULT NULL COMMENT '退单时间',
    `return_bill_number`       varbinary(100) NOT NULL DEFAULT '' COMMENT '退单单号',
    `compensate_time`          datetime NULL DEFAULT NULL COMMENT '补偿时间',
    `compensate_status`        tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否补偿  0 未补偿 1正常已补偿 2 提前补偿',
    `last_update_date`         date                                                   NOT NULL DEFAULT '1970-01-01' COMMENT '最后统计日期',
    `delete_time`              datetime NULL DEFAULT NULL COMMENT '删除时间',
    `create_time`              datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`              datetime(3) NOT NULL COMMENT '更新时间',
    `reach_the_standard`       tinyint(2) NOT NULL DEFAULT 0 COMMENT '0 未达标 1 已达标 \r\nadd_value_money+consume_money\r\n大于 YkyCouponSendRule的min_value',
    `reach_the_standard_time`  datetime NULL DEFAULT NULL COMMENT '达标时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE,
    UNIQUE INDEX `yky_member_guid_bill_number`(`yky_member_guid`, `bill_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25362 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_point_note
-- ----------------------------
DROP TABLE IF EXISTS `yky_point_note`;
CREATE TABLE `yky_point_note`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
    `bid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户唯一标识',
    `member_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会员唯一标识',
    `cardid`      char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '卡号',
    `mobile`      char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `openid`      char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信openid',
    `type`        tinyint(1) NULL DEFAULT NULL COMMENT '类型',
    `true_name`   char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `point`       float(12, 4
) NULL DEFAULT NULL COMMENT '积分值',
  `yky_billno` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易单号',
  `plate_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '车牌号',
  `ship_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '保单号',
  `info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '信息',
  `meno` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0,等待充值,1成功,-1失败',
  `message` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回信息',
  `operate_userid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员id',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '更新时间',
  `refund_yky_billno` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易退单单号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for yky_point_to_value
-- ----------------------------
DROP TABLE IF EXISTS `yky_point_to_value`;
CREATE TABLE `yky_point_to_value`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '主键',
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `member_guid`           char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '会员唯一标识',
    `card_id`               varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '卡号',
    `reward_date`           date                                                    NOT NULL COMMENT '奖励日期',
    `enable_point`          decimal(18, 4)                                          NOT NULL COMMENT '当前可用积分',
    `yky_inc_money_ratio`   decimal(10, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '赠送三方money比例',
    `yky_inc_value_ratio`   decimal(10, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '赠送储值比例',
    `yky_dec_point_ratio`   decimal(10, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '扣除积分比例',
    `dec_point`             decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '扣除积分金额',
    `inc_value`             decimal(18, 4)                                          NOT NULL COMMENT '奖励储值',
    `inc_money`             decimal(18, 4)                                          NOT NULL DEFAULT 0.0000 COMMENT '赠送三方金额',
    `dec_point_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '扣除积分的单号',
    `inc_value_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '赠送储值单号',
    `inc_money_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '赠送三方money单号',
    `dec_point_status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '扣除积分状态',
    `inc_value_status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '赠送储值状态',
    `inc_money_status`      tinyint(2) NOT NULL DEFAULT 0 COMMENT '赠送三方money状态',
    `status`                tinyint(2) NOT NULL DEFAULT 0 COMMENT '-1失败,0等待奖励,1奖励成功',
    `message`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '返回信息',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一',
    UNIQUE INDEX `bid_member_guid_date`(`bid`, `member_guid`, `reward_date`) USING BTREE COMMENT '同一个会员同一天唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 20915 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_point_value_exchange_order
-- ----------------------------
DROP TABLE IF EXISTS `yky_point_value_exchange_order`;
CREATE TABLE `yky_point_value_exchange_order`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '',
    `bid`               char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '商家唯一标识',
    `member_guid`       char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '会员唯一标识',
    `openid`            char(100) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '微信openid',
    `cardid`            char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '会员卡号',
    `mobile`            char(11) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '会员手机号',
    `billno`            char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商户订单号',
    `yky_billno`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '一卡易返回的扣除积分或储值的单据号',
    `type`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '类型 1 积分 2 储值 ',
    `value`             decimal(18, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '花费多少积分/储值',
    `rate`              decimal(10, 2)                                           NOT NULL DEFAULT 1.00 COMMENT '兑换比例',
    `exchange_type`     tinyint(1) NOT NULL DEFAULT 0 COMMENT '兑换类型,1 微信 2 储值 3 加油卡 4云联宝积分 5 百望积分',
    `exchange_value`    decimal(18, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '实际兑换金额',
    `params`            varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预留参数',
    `third_billno`      char(50) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '第三方订单号',
    `relation_guid`     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '关联订单标识',
    `status`            tinyint(1) NOT NULL DEFAULT 0 COMMENT '处理状态 -1处理失败 0 待处理 1处理成功',
    `result`            varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '处理结果',
    `examine_time`      datetime(3) NULL DEFAULT NULL COMMENT '审核时间',
    `examine_user_guid` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '审核人员',
    `examine_status`    tinyint(2) NOT NULL DEFAULT 0 COMMENT '审核状态 -1 拒绝 0 待审核 1 通过',
    `create_time`       datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`       datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for yky_sync_job
-- ----------------------------
DROP TABLE IF EXISTS `yky_sync_job`;
CREATE TABLE `yky_sync_job`
(
    `id`          bigint(20) NOT NULL COMMENT '任务id',
    `job_key`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务key',
    `job_name`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
    `action`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '方法名',
    `class`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类名',
    `model`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '对应模型',
    `order_by`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '排序字段名',
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_sync_job_crontab
-- ----------------------------
DROP TABLE IF EXISTS `yky_sync_job_crontab`;
CREATE TABLE `yky_sync_job_crontab`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                  char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL,
    `bid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '商家标识(可为空)',
    `type`                  tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 普通任务 2 同步一卡易数据任务',
    `connections`           varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '队列驱动名称,留空则使用默认驱动',
    `name`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名',
    `yky_sync_job_key_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一卡易同步key_name',
    `class`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类名',
    `priority`              tinyint(3) NOT NULL DEFAULT 0 COMMENT '优先级,越小越靠优先',
    `payload`               json                                                    NOT NULL COMMENT '参数',
    `last_execute_time`     datetime                                                NOT NULL DEFAULT '2018-01-01 00:00:00' COMMENT '上次执行时间',
    `next_execute_time`     datetime                                                NOT NULL COMMENT '下次执行时间',
    `status`                tinyint(2) NOT NULL DEFAULT 1 COMMENT '0禁用 1启用',
    `crontab_string`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT 'crontab表达式',
    `interval_sec`          int(11) NOT NULL DEFAULT 60 COMMENT '执行间隔',
    `interval_unit`         char(10) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT 'second' COMMENT '执行间隔周期',
    `create_time`           datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for yky_user
-- ----------------------------
DROP TABLE IF EXISTS `yky_user`;
CREATE TABLE `yky_user`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `guid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '工号唯一标识',
    `bid`                     char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '商家唯一标识',
    `user_account`            varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '工号（用户）',
    `user_group_guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '权限Guid',
    `group_name`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '权限名称',
    `user_group_type`         tinyint(2) NOT NULL COMMENT '权限类型（1、系统内置，管理员，2、其他）',
    `true_name`               varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '姓名',
    `mobile`                  char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '手机号',
    `tel`                     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '电话',
    `chain_store_guid`        char(36) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '工号所属店面唯一标识',
    `store_name`              varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '工号所属店面名称',
    `meno`                    varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
    `status`                  tinyint(2) NOT NULL COMMENT '工号状态（0、正常，1、锁定）',
    `store_name_limit`        text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '查看店面名称（多个用英文逗号隔开）',
    `chain_store_guids_limit` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '查看店面Guid（多个用英文逗号隔开）',
    `create_time`             datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1108 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_user_map
-- ----------------------------
DROP TABLE IF EXISTS `yky_user_map`;
CREATE TABLE `yky_user_map`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`         char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`          char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    `appid`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '公众号appid',
    `openid`       char(50) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL COMMENT '微信用户id',
    `user_name`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '一卡易账号',
    `user_account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '一卡易工号',
    `password`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '一卡易登录密码',
    `status`       tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态,1启用0禁用',
    `create_time`  datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`  datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for yky_wechat_official_accounts
-- ----------------------------
DROP TABLE IF EXISTS `yky_wechat_official_accounts`;
CREATE TABLE `yky_wechat_official_accounts`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `login_account`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '登陆账号',
    `login_password`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '登陆密码',
    `nick_name`            varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '公众号名称',
    `appid`                char(20) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT 'APPID',
    `principal_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '主体名称',
    `status`               tinyint(2) NOT NULL DEFAULT 0 COMMENT '0未认证 1已认证',
    `qrcode_url`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '二维码',
    `admin_user_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '管理员姓名',
    `renew_user_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '续费人员',
    `origin_id`            char(50) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL COMMENT '原始ID',
    `last_renew_time`      date                                                    NOT NULL COMMENT '最后续费时间',
    `memo`                 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `update_time`          datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `create_time`          datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `principal_short_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主体简称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 69 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ylh_access_token
-- ----------------------------
DROP TABLE IF EXISTS `ylh_access_token`;
CREATE TABLE `ylh_access_token`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
    `bid`                    char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家标识',
    `yky_rec_member_card_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易推荐人卡号',
    `yky_account`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易账号',
    `yky_chain_store_guid`   char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店标识',
    `yky_user`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易工号',
    `access_token`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '令牌',
    `scope`                  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限',
    `token_type`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'token类型',
    `expired_time`           datetime NULL DEFAULT NULL COMMENT 'access_token过期时间',
    `license_status`         tinyint(2) NULL DEFAULT 0 COMMENT '0 试用状态 1 正式使用',
    `license_expired_time`   datetime NULL DEFAULT NULL COMMENT '授权过期时间',
    `rcm_id`                 varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '推荐人ID',
    `refresh_token`          varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '令牌刷新token',
    `member_id`              varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '云联惠会员ID',
    `mobile`                 char(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `status`                 tinyint(2) NOT NULL DEFAULT 1 COMMENT '1 启用 0 禁用',
    `ylh_user_id`            char(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '云联惠user_id',
    `create_time`            datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 452 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ylh_point_order
-- ----------------------------
DROP TABLE IF EXISTS `ylh_point_order`;
CREATE TABLE `ylh_point_order`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`            char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '0' COMMENT '主键',
    `bid`             char(36) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '商家标识',
    `user_id`         char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '云联惠user_id',
    `way`             tinyint(2) NULL DEFAULT 1 COMMENT '1,消费奖励 2 推荐人奖励',
    `buyer_mobile`    char(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '买家手机号',
    `yky_bill_number` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一卡易消费单号',
    `first_time`      tinyint(1) NULL DEFAULT NULL COMMENT '是否首次',
    `order_status`    varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单状态',
    `message`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回消息',
    `level`           tinyint(2) NOT NULL COMMENT '奖励层级',
    `status`          tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 -1失败 0处理中 1成功 -2已退单',
    `order_id`        char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
    `refund_amount`   decimal(10, 2) NULL DEFAULT 0.00 COMMENT '退款金额',
    `total_amount`    decimal(10, 2)                                       NOT NULL COMMENT '消费金额 分',
    `out_trade_no`    char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '外订单号',
    `num`             int(2) NULL DEFAULT -1,
    `create_time`     datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`     datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_bid_out_trade_no`(`bid`, `out_trade_no`) USING BTREE COMMENT 'BID和外商户号不能重复',
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 70838 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ylh_reward_order
-- ----------------------------
DROP TABLE IF EXISTS `ylh_reward_order`;
CREATE TABLE `ylh_reward_order`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `bid`                 char(36) CHARACTER SET utf8 COLLATE utf8_general_ci    NOT NULL,
    `rate`                decimal(10, 4)                                         NOT NULL DEFAULT 0.0000 COMMENT '返还比例',
    `yky_bill_number`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '奖励单号',
    `reward_card_id`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '奖励卡号',
    `ylh_user_id`         varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '云联惠用户ID',
    `consume_value`       decimal(18, 4)                                         NOT NULL COMMENT '消费总额',
    `consume_bill_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消费单号',
    `status`              tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态 -1失败 0等待处理 1成功',
    `reward_value`        decimal(18, 4)                                         NOT NULL COMMENT '奖励金额',
    `message`             varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结果',
    `create_time`         datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`         datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `guid`(`guid`) USING BTREE COMMENT 'guid唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zendao
-- ----------------------------
DROP TABLE IF EXISTS `zendao`;
CREATE TABLE `zendao`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `data`        json NULL,
    `status`      tinyint(2) NOT NULL DEFAULT 0,
    `create_time` datetime(3) NOT NULL,
    `update_time` datetime(3) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 82173 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for coupon_amount
-- ----------------------------
DROP VIEW IF EXISTS `coupon_amount`;
CREATE
ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `coupon_amount` AS
select `c`.`guid`                  AS `guid`,
       `c`.`bid`                   AS `bid`,
       `c`.`name`                  AS `name`,
       `c`.`type`                  AS `type`,
       `c`.`available_amount`      AS `available_amount`,
       `c`.`value`                 AS `value`,
       `c`.`operator_user_id`      AS `operator_user_id`,
       `c`.`expire_time`           AS `expire_time`,
       `c`.`description`           AS `description`,
       `c`.`status`                AS `status`,
       `c`.`delete_time`           AS `delete_time`,
       `c`.`create_time`           AS `create_time`,
       `c`.`update_time`           AS `update_time`,
       ifnull(`tab`.`total`, 0)    AS `total`,
       ifnull(`tab`.`not_used`, 0) AS `not_used`,
       ifnull(`tab`.`used`, 0)     AS `used`
from (`platform`.`coupon` `c` left join (select `a`.`bid`                                 AS `bid`,
                                                `a`.`coupon_guid`                         AS `coupon_guid`,
                                                count(1)                                  AS `total`,
                                                count(if((`a`.`status` = 0), TRUE, NULL)) AS `not_used`,
                                                count(if((`a`.`status` = 1), TRUE, NULL)) AS `used`
                                         from `platform`.`coupon_send_note` `a`
                                         group by `a`.`coupon_guid`) `tab`
      on (((`c`.`guid` = `tab`.`coupon_guid`) and (`c`.`bid` = `tab`.`bid`))));

-- ----------------------------
-- View structure for notify_liantuofu
-- ----------------------------
DROP VIEW IF EXISTS `notify_liantuofu`;
CREATE
ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `notify_liantuofu` AS
select `notify_log`.`id`                                                    AS `id`,
       json_unquote(json_extract(`notify_log`.`content`, '$.sign'))         AS `sign`,
       json_unquote(json_extract(`notify_log`.`content`, '$.appId'))        AS `appid`,
       json_unquote(json_extract(`notify_log`.`content`, '$.mobile'))       AS `mobile`,
       json_unquote(json_extract(`notify_log`.`content`, '$.memberId'))     AS `member_id`,
       json_unquote(json_extract(`notify_log`.`content`, '$.eventType'))    AS `event_type`,
       json_unquote(json_extract(`notify_log`.`content`, '$.createTime'))   AS `event_time`,
       json_unquote(json_extract(`notify_log`.`content`, '$.notifyType'))   AS `notify_type`,
       json_unquote(json_extract(`notify_log`.`content`, '$.memberCardNo')) AS `member_card_no`,
       json_unquote(json_extract(`notify_log`.`content`, '$.merchantCode')) AS `merchant_code`,
       `notify_log`.`content`                                               AS `content`,
       `notify_log`.`create_time`                                           AS `create_time`
from `notify_log`
where (`notify_log`.`type` = 1);

-- ----------------------------
-- Procedure structure for do_replace
-- ----------------------------
DROP PROCEDURE IF EXISTS `do_replace`;
delimiter;;
CREATE PROCEDURE `do_replace`(in orig_str varchar (100), in new_str varchar (100), in db_name varchar (100),
                              in t_name varchar (100))
BEGIN

#Routine
body goes here...

DECLARE cul_name VARCHAR(50);

DECLARE
done int default 0;

DECLARE
cur CURSOR FOR
SELECT COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS
where TABLE_SCHEMA = db_name
  and TABLE_NAME = t_name;

DECLARE
CONTINUE HANDLER FOR NOT FOUND SET done = 1;

OPEN cur;

FETCH cur INTO cul_name;

WHILE
(done<>1) do

set @update_sql=CONCAT("Update `",t_name,"` SET `",cul_name,"` =REPLACE(`",cul_name,"`,'",orig_str,"','",new_str,"');");

prepare stmt from @update_sql;

execute stmt;

FETCH cur INTO cul_name;

END WHILE;

CLOSE cur;

END
;;
delimiter ;

-- ----------------------------
-- Function structure for getChildList
-- ----------------------------
DROP FUNCTION IF EXISTS `getChildList`;
delimiter;;
CREATE FUNCTION `getChildList`(`user_guid` VARCHAR (100), `bid` VARCHAR (100))
    RETURNS varchar(1000) CHARSET utf8
BEGIN
	#Routine
body goes here...
       DECLARE sTemp VARCHAR(1000);
       DECLARE
temp_user_guid VARCHAR(3000);
       DECLARE
temp_bid VARCHAR(3000);
       SET
sTemp = '0';
			 SET
temp_user_guid =cast(user_guid as CHAR(100));
			 SET
temp_bid =cast(bid as CHAR(100));
       WHILE
temp_user_guid is not null DO
         SET sTemp = concat(sTemp,',',temp_user_guid);
SELECT group_concat(user_guid)
INTO temp_user_guid
FROM `user`
where FIND_IN_SET(parent_guid, temp_user_guid) > 0
  and bid = temp_bid;
END WHILE;
RETURN sTemp;
END
;;
delimiter ;

-- ----------------------------
-- Function structure for getChildListNew
-- ----------------------------
DROP FUNCTION IF EXISTS `getChildListNew`;
delimiter;;
CREATE FUNCTION `getChildListNew`(`user_id` int, `business_guid` char)
    RETURNS varchar(1000) CHARSET utf8
BEGIN
	#Routine
body goes here...
   DECLARE sTemp VARCHAR(1000);
   DECLARE
business_guid VARCHAR(36);
       SET
sTemp = '0';
       SET
user_id =cast(id as CHAR);
       WHILE
user_id is not null DO
         SET sTemp = concat(sTemp,',',user_id);
SELECT group_concat(id)
INTO user_id
FROM user
where FIND_IN_SET(pid, user_id) > 0
  and bid = business_guid;
END WHILE;
RETURN sTemp;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for init_replace
-- ----------------------------
DROP PROCEDURE IF EXISTS `init_replace`;
delimiter;;
CREATE PROCEDURE `init_replace`(in orig_str varchar (255), in new_str varchar (255), in db_name varchar (100))
BEGIN

#Routine
body goes here...

DECLARE t_name VARCHAR(100);

DECLARE
done int default 0;

DECLARE
cur CURSOR FOR
SELECT DISTINCT table_name as name
FROM INFORMATION_SCHEMA.TABLES
WHERE table_schema = db_name;

DECLARE
CONTINUE HANDLER FOR NOT FOUND SET done = 1;

OPEN cur;

FETCH cur INTO t_name;

WHILE
(done<>1) do

call do_replace(orig_str,new_str,db_name,t_name);

FETCH cur INTO t_name;

END WHILE;

END
;;
delimiter ;

-- ----------------------------
-- Function structure for JSON_INTERSECT
-- ----------------------------
DROP FUNCTION IF EXISTS `JSON_INTERSECT`;
delimiter;;
CREATE FUNCTION `JSON_INTERSECT`(Array1 VARCHAR (1024), Array2 VARCHAR (1024))
    RETURNS varchar(1024) CHARSET utf8
  DETERMINISTIC
BEGIN
    DECLARE
x int;
    DECLARE
val, output varchar(1024);
    SET
output = '[]';
    SET
x = 0;
    IF
JSON_LENGTH(Array2) < JSON_LENGTH(Array1) THEN
        SET val = Array2;
        SET
Array2 = Array1;
        SET
Array1 = val;
END IF;
    WHILE
x < JSON_LENGTH(Array1) DO
        SET val = JSON_EXTRACT(Array1, CONCAT('$[',x,']'));
        IF
JSON_CONTAINS(Array2,val) THEN
            SET output = JSON_MERGE(output,val);
END IF;
        SET
x = x + 1;
END WHILE;
    IF
JSON_LENGTH(output) = 0 THEN
        RETURN NULL;
ELSE
        RETURN output;
END IF;
END
;;
delimiter ;

-- ----------------------------
-- Event structure for e_del_api_log
-- ----------------------------
DROP
EVENT IF EXISTS `e_del_api_log`;
delimiter ;;
CREATE
EVENT `e_del_api_log`
ON SCHEDULE
EVERY '1' DAY STARTS '2022-05-20 02:05:00'
DO
BEGIN
DELETE
FROM api_log
where DATE(request_time)<=DATE(DATE_SUB(NOW(),INTERVAL 1 MONTH));
END
;;
delimiter ;

-- ----------------------------
-- Event structure for e_del_crontab_execute_note
-- ----------------------------
DROP
EVENT IF EXISTS `e_del_crontab_execute_note`;
delimiter ;;
CREATE
EVENT `e_del_crontab_execute_note`
ON SCHEDULE
EVERY '1' DAY STARTS '2022-03-01 02:00:00'
DO
BEGIN
DELETE
FROM crontab_execute_note
where DATE(create_time)<=DATE(DATE_SUB(NOW(),INTERVAL 7 DAY));
END
;;
delimiter ;

-- ----------------------------
-- Event structure for e_del_jobs_execute_note
-- ----------------------------
DROP
EVENT IF EXISTS `e_del_jobs_execute_note`;
delimiter ;;
CREATE
EVENT `e_del_jobs_execute_note`
ON SCHEDULE
EVERY '1' DAY STARTS '2022-05-24 02:10:00'
DO
BEGIN
DELETE
FROM jobs_execute_note
where DATE(create_time)<=DATE(DATE_SUB(NOW(),INTERVAL 7 DAY));
END
;;
delimiter ;

SET
FOREIGN_KEY_CHECKS = 1;
