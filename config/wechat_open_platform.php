<?php
// +----------------------------------------------------------------------
// | 微信开放平台密钥
// +----------------------------------------------------------------------

return [
    [
        'domain'                    => ['www.yikayi.net', 'api.ecarde.cn', 'readonly.yikayi.net', 'admin.yikayi.net', 'dev.yikayi.net', '127.0.0.1'], // 域名
        'cachepath'                 => '', // 缓存地址
        'access_token_cache_prefix' => 'wechat_access_token:',//token缓存前缀
        'component_appid'           => 'wxa0c0db9b3bec99f9', // 服务应用APPID
        'component_appsecret'       => '41ad3423c43891398a65a1f7de9aeb3a', // 服务应用AppScert
        'component_token'           => 'ecarde', // 服务应用 Token
        'component_encodingaeskey'  => 'uyKuj23kfULVLOERUJ83cd35mfscw2qqc57ggeac4w3', // 服务应用加密KEY
        'component_verify_ticket'   => '', // 微信服务推送的Ticket（一般不用给，SDK自动维护）
    ],
    [
        'domain'                    => ['wxweeker.kuxiang168.com'], // 域名
        'cachepath'                 => '', // 缓存地址
        'access_token_cache_prefix' => 'wechat_access_token:',//token缓存前缀
        'component_appid'           => 'wx53a68148ceb174a8', // 服务应用APPID
        'component_appsecret'       => 'c579af4ea5f2a4af22059154800f77ee', // 服务应用AppScert
        'component_token'           => 'wxweeker', // 服务应用 Token
        'component_encodingaeskey'  => 'uyKuj23kfULVLOERUJ83cd35mfscw2qqc57ggeac4w0', // 服务应用加密KEY
        'component_verify_ticket'   => '', // 微信服务推送的Ticket（一般不用给，SDK自动维护）
    ]
];