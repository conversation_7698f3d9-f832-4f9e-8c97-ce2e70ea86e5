<?php
// +----------------------------------------------------------------------
// | 应用设置
// +----------------------------------------------------------------------

return [
    // 应用名称
    'app_name'                             => 'app',
    // 应用地址
    'app_host'                             => env('app.host', ''),
    // 主服务器公网IP
    'master_wan_ip'                        => '*************',
    // 主服务器内网IP
    'master_lan_ip'                        => '*************',
    // 从服务器公网IP,数据库
    'slave_wan_ip'                         => '*************',
    // 从服务器内网IP,数据库
    'slave_lan_ip'                         => '*************',

    // 应用的命名空间
    'app_namespace'                        => '',
    // 是否启用路由
    'with_route'                           => true,
    // 默认应用
    'default_app'                          => 'index',
    // 默认时区
    'default_timezone'                     => 'Asia/Shanghai',

    // 应用映射（自动多应用模式有效）
    'app_map'                              => [],
    // 域名绑定（自动多应用模式有效）
    'domain_bind'                          => [],
    // 禁止URL访问的应用列表（自动多应用模式有效）
    'deny_app_list'                        => ['command', 'common', 'event', 'listener', 'queue', 'subscribe', 'middleware'],

    // 异常页面的模板文件
    //    'exception_tmpl'                       => app()->getThinkPath() . 'tpl/think_exception.tpl',
    'exception_tmpl'                       => app()->getRootPath() . '/extend/tpl/think_exception.tpl',

    // 错误显示信息,非调试模式有效
    'error_message'                        => '服务器繁忙！请稍后再试～',
    // 显示错误信息
    'show_error_msg'                       => false,
    //超级管理员guid
    'super_admin_business_guid'            => '26810245-d97e-81b6-c1cf-0215fd8f347c',
    'recharge_pay_parameter_business_guid' => 'f37eb109-70ac-9e6e-a089-ecefebd3d396',

    // +----------------------------------------------------------------------
    // | 相关开发密钥设置,部分已经写在数据库了
    // +----------------------------------------------------------------------
    'jwt_key'                              => 'PM089K2VA7W3EG6J1NBXCFLQZO45HISTRUYD',
    'aes_key'                              => '02df91f6f6876bNBXCFLQZO45H3d993ba4fd',
    'not_log_url'                          => 'sz1card1.5upm.com,api.weixin.qq.com/cgi-bin/user/info,downloadbill,get_merchant_innermsg',
    'wechat_partner_appid'                 => 'wx07ce368b922c9841',
    'wechat_queues'                        => 'transfer,send_red_packet',
    'crop_wechat_config'                   => [
        'defalut_appid'   => 'wwfe969e93feed1d0f',
        'defalut_agentid' => 1000002,
    ],
    'workweixin'                           => [
        'token'            => 'QA0Z2DKBUy1BLDAHO',    //填写应用接口的Token
        'encodingaeskey'   => '2T9jbBAUsGvxhYF331Drzy6U5bo5gp1x1OEvmEIfwz7',//填写加密用的EncodingAESKey
        'appid'            => 'wx3b5649a58bff1e0b',    //填写高级调用功能的appid
        'appsecret'        => '3w3zzbroBkt-PHxq4E2Ppl8bnlx8i1gt58RfBbZkv7G8xcX8mnVkeG79yQTrvZ9Q', //填写高级调用功能的密钥
        'debug'            => false,
        'logcallback'      => 'trace',
        'agent_appsecrets' => [
            0 => 'xna_ilya3sO8lk6K930FXgn5QBlcRWut_vG-98FqPqo', //0
            1 => 'H45KYGZNk8m-EfwXtJRJ2n4izd3RlHXQfprJV_ieZJE', //1
            3 => 'kBzLOl1z7Z4Y37JDD4DhTHqaXTDvco921yGhl-mNtmg', //3
        ],
    ],
    //短信充值的开发密钥
    'sms_recharge_config'                  => [
        'openid' => 'D5F211181F604F648AB301F84FB332F2',
        'secret' => 'LLQNAZ'
    ],
    'files_domain'                         => 'file.yikayi.net',
    'app_host_domain'                      => 'www.yikayi.net',
    'break_match_str_list'                 => [
        'server has gone away',
        'no connection to the server',
        'Lost connection',
        'is dead or not enabled',
        'Error while sending',
        'decryption failed or bad record mac',
        'server closed the connection unexpectedly',
        'SSL connection has been closed unexpectedly',
        'Error writing data to the connection',
        'Resource deadlock avoided',
        'failed with errno',
        'Broken pipe or closed connection', //rabbit
        'Connection reset by peer',//rabbit
    ]
];
