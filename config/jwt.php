<?php


return [
    'secret'            => env('JWT_SECRET', 'PM089K2VA7W3EG6J1NBXCFLQZO45HISTRUYD'),
    //Asymmetric key
    'public_key'        => env('JWT_PUBLIC_KEY'),
    'private_key'       => env('JWT_PRIVATE_KEY'),
    'password'          => env('JWT_PASSWORD'),
    //JWT time to live
    'ttl'               => env('JWT_TTL', 3600 * 24),
    //Refresh time to live
    'refresh_ttl'       => env('JWT_REFRESH_TTL', 3600 * 12),
    //JWT hashing algorithm
    'algo'              => env('JWT_ALGO', 'HS256'),
    //token获取方式，数组靠前值优先
    'token_mode'        => ['header', 'param', 'cookie', 'middleware'],
    'blacklist_storage' => thans\jwt\provider\storage\Tp6::class,
];