<?php

/**
 * 客服WebSocket服务配置
 * 迁移自 sub_app/qingkefu_socket/config.php
 */
return [
    'version'         => '2.0',
    // websocket 对外服务端口
    'ws_port'         => 2021,//2020被老客服系统占用
    'register_port'   => 2238,//1238被老客服系统占用
    'start_port'      => 3900,//2900被老客服系统占用

    // http api 服务对外端口
    'api_port'        => 2945,
    // gateway worker number
    'gateway_worker'  => 1,
    // business worker number
    'business_worker' => 1,
    // 是否开启客服只允许单点登录 0 不开启 1 开启,
    // 切换需要重启
    'single_login'    => 1,
    // 是否开启 ssl
    'is_open_ssl'     => !is_host(),
    'context'         => [
        'ssl' => [
            'local_cert'  => '/usr/local/nginx/conf/cert/www.yikayi.net.pem', // 服务器的证书绝对路径
            'local_pk'    => '/usr/local/nginx/conf/cert/www.yikayi.net.key', // 服务器的证书绝对路径
            'verify_peer' => false
        ]
    ],
];
