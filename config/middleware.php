<?php
// 中间件配置
return [
    // 别名或分组
    'alias'    => [
        'admin'      => [
            app\middleware\Admin::class,
            app\middleware\WechatOpenidMappingPlatformWechatOpenid::class,
            app\middleware\AdminPlatformWechatAuth::class,
            app\middleware\AdminPlatformWechatAuthLogin::class,
            app\middleware\AdminWechatUserInfo::class,
        ],
        'admin_api/v1'  => [
            app\middleware\AllowCrossDomain::class,
            app\middleware\AdminApi::class,
            app\middleware\BusinessStatusVerify::class,
            app\middleware\CheckWechatBindStatus::class,
            app\middleware\AutoRefreshAccessToken::class,
            app\middleware\AppendHeaderUserInfo::class,
            app\middleware\Validate::class,
            think\middleware\Throttle::class,
            app\middleware\DistributedLock::class,
            app\middleware\RequestCache::class,
            app\middleware\AdminApiLog::class,
        ],
        'api/v1'        => [
            app\middleware\AllowCrossDomain::class,
            app\middleware\Validate::class,
            app\middleware\Api::class,
            app\middleware\DistributedLock::class,
            //      think\middleware\Throttle::class,
        ],
        'gateway'    => [
            app\middleware\Validate::class,
        ],
        'index'      => [
            app\middleware\AllowCrossDomain::class,
            app\middleware\Validate::class,
            app\middleware\CheckTxtFile::class,
        ],
        'mall/v1'       => [
            app\middleware\Mall::class,
        ],
        'member'     => [
            app\middleware\CheckTxtFile::class,
            app\middleware\Member::class,
            app\middleware\QueueTrigger::class,
        ],
        'member_api/v1' => [
            app\middleware\MemberApi::class,
            app\middleware\AppendHeaderMemberInfo::class,
            app\middleware\Validate::class,
            app\middleware\CheckWeappOpenidMappingWechatOpenid::class,
            app\middleware\CheckMemberMobile::class,
            app\middleware\BusinessStatusVerify::class,
            app\middleware\MemberStatusVerify::class,
            app\middleware\DistributedLock::class,
            app\middleware\RequestCache::class,
            app\middleware\MemberApiLog::class,
        ],
        'openapi/v1'    => [
            app\middleware\AllowCrossDomain::class,
            app\middleware\AllowMethod::class,
            app\middleware\Openapi::class,
            app\middleware\Validate::class,
            app\middleware\DistributedLock::class,
        ],
        'paimai'     => [
            app\middleware\Paimai::class,
        ],
        'test'       => [
            app\middleware\CheckAllowMethod::class
        ],
    ],
    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [],
];
