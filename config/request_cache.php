<?php
// +----------------------------------------------------------------------
// | 节流设置
// +----------------------------------------------------------------------
return [
    // 缓存键前缀，防止键值与其他应用冲突
    'request_cache_key_prefix' => 'request_cache:',
    // 设置需要访问
    'list'                     => [
        'app\controller\admin_api\v1\Business@info'               => [
            'exp' => 30,
        ],
        'app\controller\member_api\v1\Goods@quick'                => [
            'exp' => 30,
        ],
        'app\controller\member_api\v1\CouponBook@index'           => [
            'exp' => 30,
        ],
        'app\controller\member_api\v1\CouponBook@goods_list'      => [
            'exp' => 30,
        ],
        'app\controller\member_api\v1\Code@get_start_page_config' => [
            'exp' => 120,
        ],
        'app\controller\member_api\v1\House@zhu_jian_ju'          => [
            'exp' => 120,
        ],
    ],
];
