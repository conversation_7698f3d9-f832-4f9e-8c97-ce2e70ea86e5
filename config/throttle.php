<?php
// +----------------------------------------------------------------------
// | 节流设置
// +----------------------------------------------------------------------
use think\middleware\Throttle;
use think\Request;
use think\Response;
use think\middleware\throttle\CounterFixed;
use think\middleware\throttle\CounterSlider;
use think\middleware\throttle\LeakyBucket;
use think\middleware\throttle\TokenBucket;

return [
    // 缓存键前缀，防止键值与其他应用冲突
    'prefix'          => 'throttle:',
    // 缓存的键值，默认使用来源ip
    'key'             => function (Throttle $throttle, Request $request) {
        $throttle_list = config('throttle.list');
        $request_path  = app()->parseClass('controller', $request->controller()) . '@' . $request->action();
        if (!isset($throttle_list[$request_path])) {
            return false;
        }
        $array = $throttle_list[$request_path];
        if (isset($array['rate'])) {
            $throttle->setRate($array['rate']);// 设置频率
        }
        if (isset($array['driver'])) {
            $throttle->setDriverClass($array['driver']);// 设置限流策略
        }
        if (empty($array['key'])) {
            return true;
        } elseif (is_array($array['key'])) {
            foreach ($array['key'] as $key) {
                if (isset($params[$key])) {
                    $request_path .= $params[$key];
                }
                return $request_path;
            }
        } elseif (is_string($array['key'])) {
            return $array['key'];
        } else {
            return true;
        }
    },
    // 要被限制的请求类型, eg: GET POST PUT DELETE HEAD 等
    'visit_method'    => ['GET', 'POST', 'HEAD'],
    // 设置访问频率，例如 '10/m' 指的是允许每分钟请求10次;'10/60'指允许每60秒请求10次。值 null 表示不限制， eg: null 10/m  20/h  300/d 200/300
    'visit_rate'      => null,
    // 访问受限时返回的http状态码 429
    'visit_fail_code' => 200,
    // 访问受限时访问的文本信息
    // 'visit_fail_text' => '访问频率受到限制，请稍等__WAIT__秒再试',
    'visit_fail_text' => '{"code":-1,"msg":"您的请求频率过快，请稍等__WAIT__秒再试","time": __TIME__ }',
    // 设置需要访问
    'list'            => [
        'app\controller\admin_api\v1\CouponGenerateNote@export' => [
            'key'    => null,
            'rate'   => '1/2s',
            'driver' => CounterSlider::class
        ],
        'app\controller\admin_api\v1\GoodsOrder@export'         => [
            'key'    => null,
            'rate'   => '1/2s',
            'driver' => CounterSlider::class
        ],

    ],
    //   /*
    // * 设置节流算法，组件提供了四种算法：
    // *  - CounterFixed ：计数固定窗口
    // *  - CounterSlider: 滑动窗口
    // *  - TokenBucket : 令牌桶算法
    // *  - LeakyBucket : 漏桶限流算法
    // */
    //   'driver_name' => CounterFixed::class,
    //   // 响应体中设置速率限制的头部信息，含义见：https://docs.github.com/en/rest/overview/resources-in-the-rest-api#rate-limiting
    //   'visit_enable_show_rate_limit' => true,
    //   // 访问受限时返回的响应
    //   'visit_fail_response' => function (Throttle $throttle, Request $request, int $wait_seconds) {
    //       return Response::create('Too many requests, try again after ' . $wait_seconds . ' seconds.')->code(429);
    //   },
];
