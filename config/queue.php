<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'default'     => 'database',
    'later'       => 'amqp',
    'connections' => [
        'sync'     => [
            'type' => 'sync',
        ],
        'database' => [
            'type'       => 'database',
            'queue'      => 'default',
            'table'      => 'jobs',
            'connection' => null,
            'auto'       => true,
        ],
        //        'swoole'   => [
        //            'type'    => 'swoole',
        //            'queue'   => 'default',
        //            'host'    => '127.0.0.1',
        //            'port'    => 9501,
        //            'timeout' => 0.5,
        //        ],
        'redis'    => [
            'type'       => 'redis',
            'queue'      => 'default',
            'host'       => env('master_wan_ip', config('app.master_lan_ip')),
            'port'       => 6379,
            'password'   => 'XieYongFa123',
            'select'     => 0,
            'timeout'    => 0,
            'persistent' => false,
            'auto'       => false,
        ],
        'amqp'     => [
            'type'       => 'amqp',
            'queue'      => 'default',
            'host'       => env('master_wan_ip', config('app.master_lan_ip')),
            'username'   => env('amqp.username', 'admin'),
            'password'   => env('amqp.password', '111aaa...'),
            'port'       => env('amqp.hostport', '5672'),
            'vhost'      => env('amqp.vhost', '/'),
            'timeout'    => 0,
            'persistent' => false,
            'auto'       => false,
        ],
    ],
    'failed'      => [
        'type'  => 'database',
        'table' => 'jobs_failed',
    ],
    //越前优先级越高
    'priority'    => [
        'app\\queue\\controller\\Tools@git_web_hook_callback',
        'app\\queue\\controller\\GoodsOrder@after_pay_success',
        'app\\queue\\controller\\Code@code_buy_pay_success_callback',
        'app\\queue\\controller\\Queue@call',
        'app\\queue\\controller\\GoodsOrder@after_submit_order',
        'app\\queue\\controller\\GoodsOrder@send_out_goods',
        'app\\queue\\controller\\Pay@notify',
        'app\\queue\\controller\\Pay@query_order',
        'app\\queue\\controller\\Code@send',
        'app\\queue\\controller\\GoodsOrder@after_confirm',
        'app\\queue\\controller\\GoodsOrder@after_send_out_goods',
        'app\\queue\\controller\\GoodsOrder@auto_confirm',
        'app\\queue\\controller\\Notify@send',

        'app\\queue\\controller\\SyncYikayiNote@get_consume_note',
        'app\\queue\\controller\\Yikayi@reward_coupon',
        'app\\queue\\controller\\Yikayi@reward_value',
        'app\\queue\\controller\\Reward@reward_by_point',
        'app\\queue\\controller\\Reward@register_member',
        'app\\queue\\controller\\Yikayi@auto_value_cash',
        'app\\queue\\controller\\Weixin@send_red_packet',
        'app\\queue\\controller\\Yikayi@recharge_limit',
        'app\\queue\\controller\\Yikayi@reward',
        'app\\queue\\controller\\Yikayi@reward_other_chain_store',
        'app\\queue\\controller\\Yikayi@reward_chain_store_card_third_point',
        'app\\queue\\controller\\Yikayi@reward_chain_store_card',
        'app\\queue\\controller\\Tools@push_website_contact_job',

        'app\\queue\\controller\\Weixin@send_qy_wechat',
        'app\\queue\\controller\\Weixin@send_template_message',
        'app\\queue\\controller\\Yikayi@point_to_value_everyday',

        'app\\queue\\controller\\Yikayi@get_consume_note_detail',
        'app\\queue\\controller\\Yky@refresh_yky_plus_member_order_statistics',
        'app\\queue\\controller\\Yky@refresh_yky_plus_member_statistics',
        'app\\queue\\controller\\Yky@reward_coupon_of_plus_order',
        'app\\queue\\controller\\Donghua@reward',
        'app\\queue\\controller\\Donghua@reward_v2',
        'app\\queue\\controller\\GoodsOrder@update_express_route',

        'app\\queue\\controller\\Tools@sleep',
    ]
];
