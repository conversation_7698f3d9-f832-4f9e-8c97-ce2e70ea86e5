<?php

return [
    // 默认使用的数据库连接配置
    'default'         => env('database.driver', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 时间字段配置 配置格式：create_time,update_time
    'datetime_field'  => '',

    // 数据库连接配置信息
    'connections'     => [
        'mysql'         => [
            // 数据库类型
            'type'            => env('database.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('master_wan_ip', config('app.master_lan_ip')),
            // 数据库名
            'database'        => env('database.database', 'platform'),
            // 用户名
            'username'        => env('database.username', 'root'),
            // 密码
            'password'        => env('database.password', 'mmbaidu.com'),
            // 端口
            'hostport'        => env('database.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码
            'charset'         => env('DB_CHARSET', 'utf8mb4'),
            // 数据库表前缀
            'prefix'          => env('database.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => true,
        ],
        'mysql_slave'   => [
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => '*************',
            // 数据库名
            'database'        => 'mysql',
            // 数据库用户名
            'username'        => 'root',
            // 数据库密码
            'password'        => 'mmbaidu.com',
            // 数据库连接端口
            'hostport'        => '3306',
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => 'utf8',
            // 数据库表前缀
            'prefix'          => '',
            // 是否需要断线重连
            'break_reconnect' => true,
        ],
        'mysql_slave_2' => [
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => '*************',
            // 数据库名
            'database'        => 'mysql',
            // 数据库用户名
            'username'        => 'root',
            // 数据库密码
            'password'        => 'mmbaidu.com',
            // 数据库连接端口
            'hostport'        => '3306',
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => 'utf8',
            // 数据库表前缀
            'prefix'          => '',
            // 是否需要断线重连
            'break_reconnect' => true,
        ],
        'mysql_slave_3' => [
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => '*************',
            // 数据库名
            'database'        => 'mysql',
            // 数据库用户名
            'username'        => 'root',
            // 数据库密码
            'password'        => 'mmbaidu.com',
            // 数据库连接端口
            'hostport'        => '3306',
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => 'utf8',
            // 数据库表前缀
            'prefix'          => '',
            // 是否需要断线重连
            'break_reconnect' => true,
        ],

        'mysql_deploy'  => [
            // 启用分布式数据库
            'deploy'          => 1,
            //读写分离
            'rw_separate'     => true,
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => env('master_wan_ip', config('app.master_lan_ip')) . ',' . env('slave_wan_ip', config('app.slave_lan_ip')),
            //            'hostname'        => '*************,*************',
            
            // 数据库名
            'database'        => 'platform',
            // 数据库用户名
            'username'        => ['root', 'root'],
            'password'        => ['mmbaidu.com', 'mmbaidu.com'],

            //            'username'        => ['root', 'slave'],
            //            // 数据库密码
            //            'password'        => ['mmbaidu.com', '(nxO(XOwtE#TVHgdmtO/#XlPZES)Kjz:'],
            // 数据库连接端口
            'hostport'        => '3306',
            // 数据库字符集
            'charset'         => 'utf8',
            // 数据库表前缀
            'prefix'          => '',
            // 是否需要断线重连
            'break_reconnect' => true,
        ],


        //         'mysql_slave_3' => [
        //             // 数据库类型
        //             'type'            => 'mysql',
        //             // 服务器地址
        //             'hostname'        => '*************',
        //             // 数据库名
        //             'database'        => 'mysql',
        //             // 数据库用户名
        //             'username'        => 'root',
        //             // 数据库密码
        //             'password'        => 'mmbaidu.com',
        //             // 数据库连接端口
        //             'hostport'        => '3307',
        //             // 数据库连接参数
        //             'params'          => [],
        //             // 数据库编码默认采用utf8
        //             'charset'         => 'utf8',
        //             // 数据库表前缀
        //             'prefix'          => '',
        //             // 是否需要断线重连
        //             'break_reconnect' => true,
        //         ],
        'mysql_host'    => [
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => '127.0.0.1',
            // 数据库名
            'database'        => 'platform',
            // 用户名
            'username'        => 'root',
            // 密码
            'password'        => 'root',
            // 端口
            'hostport'        => '3306',
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => 'utf8',
            // 数据库表前缀
            'prefix'          => '',

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => true,
        ],
        't_muyw_cn'     => [
            // 数据库类型
            'type'     => 'mysql',
            // 服务器地址
            'hostname' => '**************',
            // 数据库名
            'database' => 't_muyw_cn',
            // 数据库用户名
            'username' => 'root',
            // 数据库密码
            'password' => '47107188188.com',
            // 数据库连接端口
            'hostport' => '3306',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => 'ims_sqtg_sun_',
        ],
        'xyyx_tlduo_cn' => [
            // 数据库类型
            'type'     => 'mysql',
            // 服务器地址
            'hostname' => '*************',
            // 数据库名
            'database' => 'xyyx_tlduo_cn',
            // 数据库用户名
            'username' => 'xyyx_tlduo_cn',
            // 数据库密码
            'password' => '5AEch2sHM8jpTBDr',
            // 数据库连接端口
            'hostport' => '3306',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => 'oscshop_',
        ],
        'ys_tlduo_cn'   => [
            // 数据库类型
            'type'     => 'mysql',
            // 服务器地址
            'hostname' => '*************',
            // 数据库名
            'database' => 'ys_tlduo_cn',
            // 数据库用户名
            'username' => 'ys_tlduo_cn',
            // 数据库密码
            'password' => 'xcFNXfXApwKie3rK',
            // 数据库连接端口
            'hostport' => '3306',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => 'oscshop_',
        ],
        'hqyp_tlduo_cn' => [
            // 数据库类型
            'type'     => 'mysql',
            // 服务器地址
            'hostname' => '*************',
            // 数据库名
            'database' => 'hqyp_tlduo_cn',
            // 数据库用户名
            'username' => 'hqyp_tlduo_cn',
            // 数据库密码
            'password' => 'sTp2EJKEAN6aAdih',
            // 数据库连接端口
            'hostport' => '3306',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => 'oscshop_',
        ],
        'management'    => [
            // 数据库类型
            'type'     => 'mysql',
            // 数据库连接DSN配置
            'dsn'      => '',
            // 服务器地址
            'hostname' => '***********',
            // 数据库名
            'database' => 'management',
            // 数据库用户名
            'username' => 'tech_xyf',
            // 数据库密码
            'password' => 'xyfAbc2021...',
            // 数据库连接端口
            'hostport' => '',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => '',
        ],
        'mps'           => [
            // 数据库类型
            'type'     => 'mysql',
            // 数据库连接DSN配置
            'dsn'      => '',
            // 服务器地址
            'hostname' => '***********',
            // 数据库名
            'database' => 'mps',
            // 数据库用户名
            'username' => 'tech_xyf',
            // 数据库密码
            'password' => 'xyfAbc2021...',
            // 数据库连接端口
            'hostport' => '',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => '',
        ],
        'zhibaiwei'     => [
            // 数据库类型
            'type'     => 'sqlsrv',
            // 服务器地址
            'hostname' => '**************',
            // 数据库名
            'database' => 'bwposwit_gxs',
            // 数据库用户名
            'username' => 'sa',
            // 数据库密码
            'password' => 'Bw8848!@#$%^',
            // 数据库连接端口
            'hostport' => '7319',
            // 数据库连接参数
            'params'   => [],
            // 数据库编码默认采用utf8
            'charset'  => 'utf8',
            // 数据库表前缀
            'prefix'   => '',
        ],
        // 更多的数据库配置信息
    ],
];
