<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
use app\command\Canal;
use app\command\Crontab;
use app\command\CrontabWorkerman;
use app\command\Curd\Curd;
use app\command\DatabaseBackup;
use app\command\Db;
use app\command\Device;
use app\command\GenerateRandom;
use app\command\Merchant;
use app\command\MerchantRate;
use app\command\Merchants;
use app\command\MerchantsList;
use app\command\QueueMonitor;
use app\command\QueueMultiProcesses;
use app\command\QueuePcntl;
use app\command\QueuePool;
use app\command\QueuePush;
use app\command\QueueSwoolePools;
use app\command\QueueSwooleTask;
use app\command\SwoolePool;
use app\command\SwooleProcessPool;
use app\command\SyncWechatUserList;
use app\command\Test;
use app\command\UpdateTableColumnValue;
use app\command\WatchAppUpdate;
use app\command\ExportTask;
use app\command\WorkermanGatewayWorker;
use app\command\WorkermanServer;
use app\command\WorkermanSocket;
use app\command\WorkermanSocketIO;
use app\command\WorkermanWorker;
use app\command\YuYue;
use app\command\ZhuJuanJu;
use app\command\GenerateAreaData;
use app\command\KefuSocket;
use app\command\KefuRegister;
use app\command\KefuGateway;
use app\command\KefuWorker;

return [
    //TP8支持设置运行用户, 可以解决日志权限问题
    'user'     => 'nginx',
    // 指令定义
    'commands' => [
        'crontab'                   => Crontab::class,
        'crontab_workerman'         => CrontabWorkerman::class,
        'queue_swoole_task'         => QueueSwooleTask::class,
        'queue_swoole_pools'        => QueueSwoolePools::class,
        'queue_multi_processes'     => QueueMultiProcesses::class,
        'queue_monitor'             => QueueMonitor::class,
        'queue_pcntl'               => QueuePcntl::class,
        'queue_pool'                => QueuePool::class,
        'queue_push'                => QueuePush::class,
        'swoole_pool'               => SwoolePool::class,
        'swoole_process_pool'       => SwooleProcessPool::class,
        'test'                      => Test::class,
        'zhu_jian_ju'               => ZhuJuanJu::class,
        // 'jobs'                      => Jobs::class,
        'merchant:update'           => Merchant::class,
        'merchants:update'          => Merchants::class,
        'merchants_list:update'     => MerchantsList::class,
        'merchant_rate:update'      => MerchantRate::class,
        'device:list'               => Device::class,
        'sync_wechat_user_list'     => SyncWechatUserList::class,
        'update_table_column_value' => UpdateTableColumnValue::class,
        'remove_field_cache'        => Db::class,
        'canal'                     => Canal::class,
        'database_backup'           => DatabaseBackup::class,
        'watch_app_update'          => WatchAppUpdate::class,
        'export_task'               => ExportTask::class,
        'yu_yue'                    => YuYue::class,
        'workerman:gateway'         => WorkermanGatewayWorker::class,
        'workerman:server'          => WorkermanServer::class,
        'workerman:socket'          => WorkermanSocket::class,
        'workerman:socketio'        => WorkermanSocketIO::class,
        'workerman'                 => WorkermanWorker::class, //暂时不兼容 缺乏HTTP
        'curd'                      => Curd::class,
        'generate:area'             => GenerateAreaData::class,
        'generate:random'           => GenerateRandom::class,
        'kefu_socket'                => KefuSocket::class,
        'kefu_register'               => KefuRegister::class,
        'kefu_gateway'                => KefuGateway::class,
        'kefu_worker'                 => KefuWorker::class,
    ],
];
