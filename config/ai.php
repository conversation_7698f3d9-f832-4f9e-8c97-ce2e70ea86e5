<?php
/**
 * AI模型配置文件
 * 基于 vendor/cyokup/easy-aichat 项目实际支持的模型
 */

return [
    'list'   => [
        // +----------------------------------------------------------------------
        // | 智谱AI GLM-4配置 (Glm4.php)
        // +----------------------------------------------------------------------
        'glm4'      => [
            'name'                => '智谱AI GLM-4',
            'api_key'             => env('GLM4_API_KEY', '3569bacb6db1469f84134585624e9bb1.i88eAMgEqW7MGH8m'),
            'base_url'            => 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
            'models'              => [
                'glm-4-flash' => '免费模型，适合日常对话',
                'glm-4'       => '标准模型',
                'glm-4-plus'  => '增强模型',
                'glm-4-air'   => '轻量模型',
                'glm-4-long'  => '长文本模型',
            ],
            'default_model'       => 'glm-4-flash',
            'default_params'      => [
                'temperature' => 0.7,
                'top_p'       => 0.9,
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://open.bigmodel.cn/ 获取API密钥',
            'icon'                => '🧠',
            'logo'                => '/static/img/ai/bigmodel.png',
            'color'               => '#4CAF50',
            'available'           => true,
            'supports_embeddings' => true,
            'supports_stream'     => true,
        ],

        // +----------------------------------------------------------------------
        // | 智谱AI GLM-4V配置 (Glm4V.php - 多模态模型)
        // +----------------------------------------------------------------------
        'glm4v'     => [
            'name'                => '智谱AI GLM-4v',
            'api_key'             => env('GLM4V_API_KEY', env('GLM4_API_KEY', '3569bacb6db1469f84134585624e9bb1.i88eAMgEqW7MGH8m')), // 优先使用专用密钥，否则复用GLM-4密钥
            'base_url'            => 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
            'models'              => [
                'glm-4v' => '多模态模型，支持图像理解和图文混合对话',
            ],
            'default_model'       => 'glm-4v-flash',
            'default_params'      => [
                'temperature' => 0.95, // GLM-4V官方推荐参数
                'top_p'       => 0.7,        // GLM-4V官方推荐参数
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://open.bigmodel.cn/ 获取支持GLM-4V的API密钥',
            'icon'                => '👁️',
            'logo'                => '/static/img/ai/bigmodel.png',
            'color'               => '#2E7D32',
            'available'           => true, // 默认禁用，需要专门的API密钥
            'supports_embeddings' => false,
            'supports_stream'     => true,
            'supports_vision'     => true, // 支持视觉功能
            'max_image_size'      => '20MB',
            'supported_formats'   => ['JPEG', 'PNG', 'GIF', 'WebP'],
            'note'                => 'GLM-4V需要支持多模态功能的API密钥，可能与普通GLM-4密钥不同',
        ],

        // +----------------------------------------------------------------------
        // | 讯飞星火配置 (Spark.php - WebSocket版本)
        // +----------------------------------------------------------------------
        'spark'     => [
            'name'                => '讯飞星火',
            'api_key'             => env('SPARK_API_KEY', '9f948dd09828d50e4e4a4dc46a911088'),
            'app_id'              => env('SPARK_APP_ID', '7cf59f91'),
            'api_secret'          => env('SPARK_API_SECRET', 'OTJmZjhiMDkzODU4OTU5N2VmMjZiNzMw'),
            'base_url'            => 'wss://spark-api.xf-yun.com/v1.1/chat',
            'models'              => [
                'lite'        => '星火Lite模型',
                'generalv3.5' => '星火3.5模型',
                'generalv3'   => '星火3.0模型',
                'general'     => '星火2.0模型',
            ],
            'default_model'       => 'lite',
            'default_params'      => [
                'temperature' => 0.5,
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://console.xfyun.cn/ 获取API密钥、APP ID和API Secret',
            'icon'                => '⚡',
            'logo'                => '/static/img/ai/logo.svg',
            'color'               => '#FF9800',
            'available'           => true,
            'supports_embeddings' => false,
            'supports_stream'     => true,
        ],

        // +----------------------------------------------------------------------
        // | 讯飞星火HTTP配置 (SparkHttp.php - HTTP版本)
        // +----------------------------------------------------------------------
        'sparkHttp' => [
            'name'                => '讯飞星火HTTP',
            'api_key'             => env('SPARK_HTTP_API_PASSWORD', 'QSqykNTPuWEtbePvBzsY:jsOvUiVAICdqypeIJhvz'),
            'base_url'            => 'https://spark-api-open.xf-yun.com/v1/chat/completions',
            'models'              => [
                'lite'        => '星火Lite模型',
                'generalv3.5' => '星火3.5模型',
                'generalv3'   => '星火3.0模型',
            ],
            'default_model'       => 'lite',
            'default_params'      => [
                'temperature' => 0.5,
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://console.xfyun.cn/ 获取API Password',
            'icon'                => '⚡',
            'logo'                => '/static/img/ai/logo.svg',
            'color'               => '#FF5722',
            'available'           => false,
            'supports_embeddings' => false,
            'supports_stream'     => true,
        ],

        // +----------------------------------------------------------------------
        // | 阿里通义千问配置 (Qwen.php)
        // +----------------------------------------------------------------------
        'qwen'      => [
            'name'                => '阿里通义千问',
            'api_key'             => env('QWEN_API_KEY', ''),
            'base_url'            => 'https://dashscope.aliyuncs.com/api/v1',
            'models'              => [
                'qwen-turbo'           => '通义千问Turbo',
                'qwen-plus'            => '通义千问Plus',
                'qwen-max'             => '通义千问Max',
                'qwen-max-longcontext' => '通义千问Max长文本',
            ],
            'default_model'       => 'qwen-turbo',
            'default_params'      => [
                'temperature' => 0.7,
                'top_p'       => 0.9,
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://dashscope.console.aliyun.com/ 获取API密钥',
            'icon'                => '🌟',
            'logo'                => '/static/img/ai/qwen.jpg',
            'color'               => '#9C27B0',
            'available'           => false,
            'supports_embeddings' => true,
            'supports_stream'     => true,
        ],

        // +----------------------------------------------------------------------
        // | Moonshot AI配置 (Moonshot.php)
        // +----------------------------------------------------------------------
        'moonshot'  => [
            'name'                => 'Moonshot AI',
            'api_key'             => env('MOONSHOT_API_KEY', ''),
            'base_url'            => 'https://api.moonshot.cn/v1',
            'models'              => [
                'moonshot-v1-8k'   => 'Moonshot 8K模型',
                'moonshot-v1-32k'  => 'Moonshot 32K模型',
                'moonshot-v1-128k' => 'Moonshot 128K模型',
            ],
            'default_model'       => 'moonshot-v1-8k',
            'default_params'      => [
                'temperature' => 0.3,
                'max_tokens'  => 1024,
            ],
            'config_help'         => '请在 https://platform.moonshot.cn/ 获取API密钥',
            'icon'                => '🚀',
            'logo'                => '/static/img/ai/kimi.jpg',
            'color'               => '#3F51B5',
            'available'           => false,
            'supports_embeddings' => false,
            'supports_stream'     => true,
        ],
    ],

    // +----------------------------------------------------------------------
    // | 全局配置
    // +----------------------------------------------------------------------
    'global' => [
        // 默认启用的AI模型
        'default_available'  => ['glm4'],

        // 请求超时时间（秒）
        'timeout'            => 60,

        // 最大并发数
        'max_concurrent'     => 5,

        // 是否启用缓存
        'enable_cache'       => false,

        // 缓存时间（秒）
        'cache_ttl'          => 3600,

        // 是否记录日志
        'enable_log'         => true,

        // 日志级别
        'log_level'          => 'info',

        // 支持的功能
        'supported_gateways' => [
            'glm4'      => 'Cyokup\\EasyAiChat\\Gateways\\Glm4',
            'glm4v'     => 'Cyokup\\EasyAiChat\\Gateways\\Glm4v',
            'spark'     => 'Cyokup\\EasyAiChat\\Gateways\\Spark',
            'sparkHttp' => 'Cyokup\\EasyAiChat\\Gateways\\SparkHttp',
            'qwen'      => 'Cyokup\\EasyAiChat\\Gateways\\Qwen',
            'moonshot'  => 'Cyokup\\EasyAiChat\\Gateways\\Moonshot',
        ],
    ],
];
