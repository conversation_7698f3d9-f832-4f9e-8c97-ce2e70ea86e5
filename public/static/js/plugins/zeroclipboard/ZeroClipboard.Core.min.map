{"version": 3, "file": "ZeroClipboard.Core.min.js", "sources": ["ZeroClipboard.Core.js"], "names": ["window", "undefined", "_zcSwfVersion", "_currentElement", "_copyTarget", "_window", "_document", "document", "_navigator", "navigator", "_setTimeout", "setTimeout", "_clearTimeout", "clearTimeout", "_setInterval", "setInterval", "_clearInterval", "clearInterval", "_getComputedStyle", "getComputedStyle", "_encodeURIComponent", "encodeURIComponent", "_ActiveXObject", "ActiveXObject", "_Error", "Error", "_parseInt", "Number", "parseInt", "_parseFloat", "parseFloat", "_isNaN", "isNaN", "_now", "Date", "now", "_keys", "Object", "keys", "_hasOwn", "prototype", "hasOwnProperty", "_slice", "Array", "slice", "_unwrap", "unwrapper", "el", "wrap", "unwrap", "div", "createElement", "unwrappedDiv", "nodeType", "e", "_args", "argumentsObj", "call", "_extend", "i", "len", "arg", "prop", "src", "copy", "args", "arguments", "target", "length", "_deepCopy", "source", "_pick", "obj", "newObj", "_omit", "indexOf", "_deleteOwnProperties", "_containedBy", "ancestorEl", "ownerDocument", "parentNode", "_getDirPathOfUrl", "url", "dir", "split", "lastIndexOf", "_getCurrentScriptUrlFromErrorStack", "stack", "matches", "match", "_getCurrentScriptUrlFromError", "err", "sourceURL", "fileName", "_getCurrentScriptUrl", "jsPath", "scripts", "currentScript", "getElementsByTagName", "readyState", "_getUnanimousScriptParentDir", "jsDir", "_getDefaultSwfPath", "_isWindows", "isWindowsRegex", "test", "appVersion", "platform", "userAgent", "_pageIsFramed", "opener", "top", "parent", "_pageIsXhtml", "documentElement", "nodeName", "_flashState", "bridge", "version", "pluginType", "sandboxed", "disabled", "outdated", "insecure", "unavailable", "degraded", "deactivated", "overdue", "ready", "_minimumFlashVersion", "_handlers", "_clipData", "_clipDataFormatMap", "_flashCheckTimeout", "_swfFallbackCheckInterval", "_eventMessages", "error", "flash-sandboxed", "flash-disabled", "flash-outdated", "flash-insecure", "flash-unavailable", "flash-degraded", "flash-deactivated", "flash-overdue", "version-mismatch", "clipboard-error", "config-mismatch", "swf-not-found", "browser-unsupported", "_errorsThatOnlyOccurAfterFlashLoads", "_flashStateErrorNames", "_flashStateErrorNameMatchingRegex", "RegExp", "map", "errorName", "replace", "join", "_flashStateEnabledErrorNameMatchingRegex", "filter", "_globalConfig", "swfPath", "trustedDomains", "location", "host", "cacheBust", "forceEnhancedClipboard", "flashLoadTimeout", "autoActivate", "bubbleEvents", "fixLineEndings", "containerId", "containerClass", "swfObjectId", "hoverClass", "activeClass", "forceHandCursor", "title", "zIndex", "_config", "options", "for<PERSON>ach", "_isValidHtml4Id", "_state", "_detectSandbox", "browser", "isSupported", "_isBrowserSupported", "flash", "zeroclipboard", "ZeroClipboard", "config", "addEventListener", "_isFlashUnusable", "_on", "eventType", "listener", "events", "added", "toLowerCase", "key", "on", "push", "emit", "type", "name", "jsVersion", "swfVersion", "_off", "foundIndex", "perEventHandlers", "off", "splice", "_listeners", "_emit", "event", "eventCopy", "returnVal", "tmp", "_createEvent", "_preprocessEvent", "_dispatchCallbacks", "this", "_mapClipDataToFlash", "data", "formatMap", "_getSwfPathProtocol", "swfPathFirstTwoChars", "swfProtocol", "protocol", "_create", "max<PERSON><PERSON>", "previousState", "isFlashUnusable", "_embedSwf", "_destroy", "clearData", "blur", "_unembedSwf", "_setData", "format", "dataObj", "dataFormat", "_fixLineEndings", "_clearData", "_getData", "_focus", "element", "_removeClass", "_addClass", "newTitle", "getAttribute", "htmlBridge", "_getHtmlBridge", "setAttribute", "useHandCursor", "_getStyle", "_setHandCursor", "_reposition", "_blur", "removeAttribute", "style", "left", "width", "height", "_activeElement", "id", "relatedTarget", "currentTarget", "timeStamp", "msg", "message", "minimumVersion", "pageProtocol", "clipboardData", "setData", "_mapClipResultsFromFlash", "_get<PERSON><PERSON><PERSON>Target", "_addMouseData", "targetEl", "relatedTargetId", "getElementById", "srcElement", "fromElement", "toElement", "pos", "_getElementPosition", "screenLeft", "screenX", "screenTop", "screenY", "scrollLeft", "body", "scrollTop", "pageX", "_stageX", "pageY", "_stageY", "clientX", "clientY", "moveX", "movementX", "moveY", "movementY", "x", "y", "offsetX", "offsetY", "layerX", "layerY", "_shouldPerformAsync", "_dispatchCallback", "func", "context", "async", "apply", "wildcardTypeHandlers", "specificTypeHandlers", "handlers", "concat", "originalContext", "handleEvent", "_getSandboxStatusFromErrorEvent", "isSandboxed", "sourceIsSwf", "_source", "_clearTimeoutsAndPolling", "wasDeactivated", "textContent", "htmlContent", "value", "outerHTML", "innerHTML", "innerText", "_queueEmitClipboardErrors", "_safeActiveElement", "focus", "_fireMouseEvent", "bubbles", "cancelable", "aftercopyEvent", "errors", "errorEvent", "success", "doc", "defaults", "view", "defaultView", "canBubble", "detail", "button", "which", "createEvent", "dispatchEvent", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "initMouseEvent", "_watchForSwfFallbackContent", "pollWait", "Math", "min", "fallbackContentId", "_isElementVisible", "_createHtmlBridge", "container", "className", "position", "_getSafeZIndex", "flashBridge", "_escapeXmlValue", "val", "chr", "allowScriptAccess", "_determineScriptAccess", "allowNetworking", "<PERSON><PERSON>s", "_vars", "swfUrl", "_cacheBust", "divToBeReplaced", "append<PERSON><PERSON><PERSON>", "tmpDiv", "usingActiveX", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "display", "removeSwfFromIE", "<PERSON><PERSON><PERSON><PERSON>", "clipData", "newClipData", "text", "html", "rtf", "clipResults", "newResults", "tmpHash", "path", "domain", "domains", "str", "trustedOriginsExpanded", "_extractDomain", "originOrUrl", "protocolIndex", "pathIndex", "_extractAllDomains", "origins", "resultsArray", "currentDomain", "configOptions", "swfDomain", "activeElement", "c", "cl", "classNames", "getPropertyValue", "getBoundingClientRect", "elRect", "pageXOffset", "pageYOffset", "leftBorderWidth", "clientLeft", "topBorderWidth", "clientTop", "leftBodyOffset", "topBodyOffset", "bodyRect", "htmlRect", "right", "bottom", "styles", "hasCssHeight", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasCssTop", "hasCssLeft", "cssKnows", "rect", "isVisible", "visibility", "enabled", "setHandCursor", "content", "replaceRegex", "doNotReassessFlashSupport", "effectiveScriptOrigin", "frame", "frameError", "frameElement", "hasAttribute", "_detectFlashSupport", "parseFlashVersion", "desc", "isPepperFlash", "flashPlayerFileName", "inspectPlugin", "plugin", "hasFlash", "flashVersion", "description", "filename", "isPPAPI", "ax", "mimeType", "isActiveX", "plugins", "mimeTypes", "enabledPlugin", "GetVariable", "e1", "e2", "e3", "_createClient", "state", "create", "destroy", "getData", "activate", "deactivate", "define", "amd", "module", "exports"], "mappings": ";;;;;;;;CAQA,SAAUA,EAAQC,GAChB,YAKA,IAqTIC,GAUAC,EAKAC,EApUAC,EAAUL,EAAQM,EAAYD,EAAQE,SAAUC,EAAaH,EAAQI,UAAWC,EAAcL,EAAQM,WAAYC,EAAgBP,EAAQQ,aAAcC,EAAeT,EAAQU,YAAaC,EAAiBX,EAAQY,cAAeC,EAAoBb,EAAQc,iBAAkBC,EAAsBf,EAAQgB,mBAAoBC,EAAiBjB,EAAQkB,cAAeC,EAASnB,EAAQoB,MAAOC,EAAYrB,EAAQsB,OAAOC,UAAYvB,EAAQuB,SAAUC,EAAcxB,EAAQsB,OAAOG,YAAczB,EAAQyB,WAAYC,EAAS1B,EAAQsB,OAAOK,OAAS3B,EAAQ2B,MAAOC,EAAO5B,EAAQ6B,KAAKC,IAAKC,EAAQ/B,EAAQgC,OAAOC,KAAMC,EAAUlC,EAAQgC,OAAOG,UAAUC,eAAgBC,EAASrC,EAAQsC,MAAMH,UAAUI,MAAOC,EAAU,WACzsB,GAAIC,GAAY,SAASC,GACvB,MAAOA,GAET,IAA4B,kBAAjB1C,GAAQ2C,MAAiD,kBAAnB3C,GAAQ4C,OACvD,IACE,GAAIC,GAAM5C,EAAU6C,cAAc,OAC9BC,EAAe/C,EAAQ4C,OAAOC,EACb,KAAjBA,EAAIG,UAAkBD,GAA0C,IAA1BA,EAAaC,WACrDP,EAAYzC,EAAQ4C,QAEtB,MAAOK,IAEX,MAAOR,MAQLS,EAAQ,SAASC,GACnB,MAAOd,GAAOe,KAAKD,EAAc,IAQ/BE,EAAU,WACZ,GAAIC,GAAGC,EAAKC,EAAKC,EAAMC,EAAKC,EAAMC,EAAOV,EAAMW,WAAYC,EAASF,EAAK,MACzE,KAAKN,EAAI,EAAGC,EAAMK,EAAKG,OAAYR,EAAJD,EAASA,IACtC,GAAuB,OAAlBE,EAAMI,EAAKN,IACd,IAAKG,IAAQD,GACPtB,EAAQkB,KAAKI,EAAKC,KACpBC,EAAMI,EAAOL,GACbE,EAAOH,EAAIC,GACPK,IAAWH,GAAQA,IAAS/D,IAC9BkE,EAAOL,GAAQE,GAMzB,OAAOG,IAQLE,EAAY,SAASC,GACvB,GAAIN,GAAML,EAAGC,EAAKE,CAClB,IAAsB,gBAAXQ,IAAiC,MAAVA,GAA6C,gBAApBA,GAAOjB,SAChEW,EAAOM,MACF,IAA6B,gBAAlBA,GAAOF,OAEvB,IADAJ,KACKL,EAAI,EAAGC,EAAMU,EAAOF,OAAYR,EAAJD,EAASA,IACpCpB,EAAQkB,KAAKa,EAAQX,KACvBK,EAAKL,GAAKU,EAAUC,EAAOX,SAG1B,CACLK,IACA,KAAKF,IAAQQ,GACP/B,EAAQkB,KAAKa,EAAQR,KACvBE,EAAKF,GAAQO,EAAUC,EAAOR,KAIpC,MAAOE,IAULO,EAAQ,SAASC,EAAKlC,GAExB,IAAK,GADDmC,MACKd,EAAI,EAAGC,EAAMtB,EAAK8B,OAAYR,EAAJD,EAASA,IACtCrB,EAAKqB,IAAMa,KACbC,EAAOnC,EAAKqB,IAAMa,EAAIlC,EAAKqB,IAG/B,OAAOc,IASLC,EAAQ,SAASF,EAAKlC,GACxB,GAAImC,KACJ,KAAK,GAAIX,KAAQU,GACY,KAAvBlC,EAAKqC,QAAQb,KACfW,EAAOX,GAAQU,EAAIV,GAGvB,OAAOW,IAQLG,EAAuB,SAASJ,GAClC,GAAIA,EACF,IAAK,GAAIV,KAAQU,GACXjC,EAAQkB,KAAKe,EAAKV,UACbU,GAAIV,EAIjB,OAAOU,IAQLK,EAAe,SAAS9B,EAAI+B,GAC9B,GAAI/B,GAAsB,IAAhBA,EAAGM,UAAkBN,EAAGgC,eAAiBD,IAAuC,IAAxBA,EAAWzB,UAAkByB,EAAWC,eAAiBD,EAAWC,gBAAkBhC,EAAGgC,eAAyC,IAAxBD,EAAWzB,WAAmByB,EAAWC,eAAiBD,IAAe/B,EAAGgC,eACtP,EAAG,CACD,GAAIhC,IAAO+B,EACT,OAAO,CAET/B,GAAKA,EAAGiC,iBACDjC,EAEX,QAAO,GAQLkC,EAAmB,SAASC,GAC9B,GAAIC,EAKJ,OAJmB,gBAARD,IAAoBA,IAC7BC,EAAMD,EAAIE,MAAM,KAAK,GAAGA,MAAM,KAAK,GACnCD,EAAMD,EAAItC,MAAM,EAAGsC,EAAIG,YAAY,KAAO,IAErCF,GAQLG,EAAqC,SAASC,GAChD,GAAIL,GAAKM,CAYT,OAXqB,gBAAVD,IAAsBA,IAC/BC,EAAUD,EAAME,MAAM,sIAClBD,GAAWA,EAAQ,GACrBN,EAAMM,EAAQ,IAEdA,EAAUD,EAAME,MAAM,kEAClBD,GAAWA,EAAQ,KACrBN,EAAMM,EAAQ,MAIbN,GAQLQ,EAAgC,WAClC,GAAIR,GAAKS,CACT,KACE,KAAM,IAAInE,GACV,MAAO8B,GACPqC,EAAMrC,EAKR,MAHIqC,KACFT,EAAMS,EAAIC,WAAaD,EAAIE,UAAYP,EAAmCK,EAAIJ,QAEzEL,GAQLY,EAAuB,WACzB,GAAIC,GAAQC,EAASrC,CACrB,IAAIrD,EAAU2F,gBAAkBF,EAASzF,EAAU2F,cAAclC,KAC/D,MAAOgC,EAGT,IADAC,EAAU1F,EAAU4F,qBAAqB,UAClB,IAAnBF,EAAQ5B,OACV,MAAO4B,GAAQ,GAAGjC,KAAO9D,CAE3B,IAAI,eAAiB+F,EAAQ,IAAMzF,SAAS4C,cAAc,WACxD,IAAKQ,EAAIqC,EAAQ5B,OAAQT,KACvB,GAA8B,gBAA1BqC,EAAQrC,GAAGwC,aAAiCJ,EAASC,EAAQrC,GAAGI,KAClE,MAAOgC,EAIb,OAA6B,YAAzBzF,EAAU6F,aAA6BJ,EAASC,EAAQA,EAAQ5B,OAAS,GAAGL,KACvEgC,GAELA,EAASL,KACJK,EAEF9F,GAULmG,EAA+B,WACjC,GAAIzC,GAAG0C,EAAON,EAAQC,EAAU1F,EAAU4F,qBAAqB,SAC/D,KAAKvC,EAAIqC,EAAQ5B,OAAQT,KAAO,CAC9B,KAAMoC,EAASC,EAAQrC,GAAGI,KAAM,CAC9BsC,EAAQ,IACR,OAGF,GADAN,EAASd,EAAiBc,GACb,MAATM,EACFA,EAAQN,MACH,IAAIM,IAAUN,EAAQ,CAC3BM,EAAQ,IACR,QAGJ,MAAOA,IAASpG,GASdqG,EAAqB,WACvB,GAAID,GAAQpB,EAAiBa,MAA2BM,KAAkC,EAC1F,OAAOC,GAAQ,qBAQbE,EAAa,WACf,GAAIC,GAAiB,2CACrB,SAAShG,IAAegG,EAAeC,KAAKjG,EAAWkG,YAAc,KAAOF,EAAeC,KAAKjG,EAAWmG,UAAY,KAA2D,MAAnDnG,EAAWoG,WAAa,IAAIjC,QAAQ,aAMjKkC,EAAgB,WAClB,MAAyB,OAAlBxG,EAAQyG,WAAqBzG,EAAQ0G,KAAO1G,GAAWA,EAAQ0G,OAAS1G,EAAQ2G,QAAU3G,GAAWA,EAAQ2G,WAOlHC,EAAsD,SAAvC3G,EAAU4G,gBAAgBC,SAKzCC,GACFC,OAAQ,KACRC,QAAS,QACTC,WAAY,UACZC,UAAW,KACXC,SAAU,KACVC,SAAU,KACVC,SAAU,KACVC,YAAa,KACbC,SAAU,KACVC,YAAa,KACbC,QAAS,KACTC,MAAO,MAOLC,EAAuB,SASvBC,KAeAC,KAKAC,EAAqB,KAKrBC,EAAqB,EAKrBC,EAA4B,EAK5BC,GACFP,MAAO,qCACPQ,OACEC,kBAAmB,qEACnBC,iBAAkB,sHAClBC,iBAAkB,iDAClBC,iBAAkB,mHAClBC,oBAAqB,iEACrBC,iBAAkB,+EAClBC,oBAAqB,0TACrBC,gBAAiB,+EACjBC,mBAAoB,kFACpBC,kBAAmB,0GACnBC,kBAAmB,6DACnBC,gBAAiB,6HACjBC,sBAAuB,+EAQvBC,GAAwC,oBAAqB,iBAAkB,gBAAiB,mBAAoB,kBAAmB,mBAMvIC,GAA0B,kBAAmB,iBAAkB,iBAAkB,iBAAkB,oBAAqB,iBAAkB,oBAAqB,iBAK/JC,EAAoC,GAAIC,QAAO,WAAaF,EAAsBG,IAAI,SAASC,GACjG,MAAOA,GAAUC,QAAQ,UAAW,MACnCC,KAAK,KAAO,MAMXC,EAA2C,GAAIL,QAAO,WAAaF,EAAsBQ,OAAO,SAASJ,GAC3G,MAAqB,mBAAdA,IACND,IAAI,SAASC,GACd,MAAOA,GAAUC,QAAQ,UAAW,MACnCC,KAAK,KAAO,MAKXG,GACFC,QAAS3D,IACT4D,eAAgB7J,EAAQ8J,SAASC,MAAS/J,EAAQ8J,SAASC,SAC3DC,WAAW,EACXC,wBAAwB,EACxBC,iBAAkB,IAClBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,YAAa,mCACbC,eAAgB,iCAChBC,YAAa,oCACbC,WAAY,yBACZC,YAAa,0BACbC,iBAAiB,EACjBC,MAAO,KACPC,OAAQ,WAMNC,EAAU,SAASC,GACE,gBAAZA,KAAwBA,GAAa,UAAYA,IAC1DhJ,EAAMgJ,GAASC,QAAQ,SAASvH,GAC9B,GAAI,iEAAiE2C,KAAK3C,GACxEkG,EAAclG,GAAQsH,EAAQtH,OACzB,IAA0B,MAAtBsD,EAAYC,OACrB,GAAa,gBAATvD,GAAmC,gBAATA,EAAwB,CACpD,IAAIwH,GAAgBF,EAAQtH,IAG1B,KAAM,IAAIrC,OAAM,kBAAoBqC,EAAO,8CAF3CkG,GAAclG,GAAQsH,EAAQtH,OAKhCkG,GAAclG,GAAQsH,EAAQtH,IAKtC,EAAA,GAAuB,gBAAZsH,KAAwBA,EAMnC,MAAO/G,GAAU2F,EALf,IAAIzH,EAAQkB,KAAKuG,EAAeoB,GAC9B,MAAOpB,GAAcoB,KAUvBG,GAAS,WAEX,MADAC,OAEEC,QAAS/H,EAAQa,EAAM/D,GAAc,YAAa,WAAY,UAAW,gBACvEkL,YAAaC,OAEfC,MAAOlH,EAAM0C,GAAe,WAC5ByE,eACEvE,QAASwE,GAAcxE,QACvByE,OAAQD,GAAcC,YAQxBJ,GAAsB,WACxB,SAAUrL,EAAU0L,kBAAoB3L,EAAQgC,OAAOC,MAAQjC,EAAQsC,MAAMH,UAAUkH,MAMrFuC,GAAmB,WACrB,SAAU7E,EAAYI,WAAaJ,EAAYK,UAAYL,EAAYM,UAAYN,EAAYQ,aAAeR,EAAYS,UAAYT,EAAYU,cAMhJoE,GAAM,SAASC,EAAWC,GAC5B,GAAIzI,GAAGC,EAAKyI,EAAQC,IAWpB,IAVyB,gBAAdH,IAA0BA,EACnCE,EAASF,EAAUI,cAAcnH,MAAM,OACT,gBAAd+G,KAA0BA,GAAe,UAAYA,IAAkC,mBAAbC,IAC1FhK,EAAM+J,GAAWd,QAAQ,SAASmB,GAChC,GAAIJ,GAAWD,EAAUK,EACD,mBAAbJ,IACTN,GAAcW,GAAGD,EAAKJ,KAIxBC,GAAUA,EAAOjI,QAAUgI,EAAU,CACvC,IAAKzI,EAAI,EAAGC,EAAMyI,EAAOjI,OAAYR,EAAJD,EAASA,IACxCwI,EAAYE,EAAO1I,GAAGiG,QAAQ,MAAO,IACrC0C,EAAMH,IAAa,EACdjE,EAAUiE,KACbjE,EAAUiE,OAEZjE,EAAUiE,GAAWO,KAAKN,EAO5B,IALIE,EAAMtE,OAASZ,EAAYY,OAC7B8D,GAAca,MACZC,KAAM,UAGNN,EAAM9D,MAAO,CAOf,IANKmD,MACHG,GAAca,MACZC,KAAM,QACNC,KAAM,wBAGLlJ,EAAI,EAAGC,EAAM2F,EAAsBnF,OAAYR,EAAJD,EAASA,IACvD,GAAIyD,EAAYmC,EAAsB5F,GAAGiG,QAAQ,UAAW,QAAS,EAAM,CACzEkC,GAAca,MACZC,KAAM,QACNC,KAAMtD,EAAsB5F,IAE9B,OAGAzD,IAAkBD,GAAa6L,GAAcxE,UAAYpH,GAC3D4L,GAAca,MACZC,KAAM,QACNC,KAAM,mBACNC,UAAWhB,GAAcxE,QACzByF,WAAY7M,KAKpB,MAAO4L,KAMLkB,GAAO,SAASb,EAAWC,GAC7B,GAAIzI,GAAGC,EAAKqJ,EAAYZ,EAAQa,CAahC,IAZyB,IAArBhJ,UAAUE,OACZiI,EAASjK,EAAM8F,GACe,gBAAdiE,IAA0BA,EAC1CE,EAASF,EAAUI,cAAcnH,MAAM,OACT,gBAAd+G,KAA0BA,GAAe,UAAYA,IAAkC,mBAAbC,IAC1FhK,EAAM+J,GAAWd,QAAQ,SAASmB,GAChC,GAAIJ,GAAWD,EAAUK,EACD,mBAAbJ,IACTN,GAAcqB,IAAIX,EAAKJ,KAIzBC,GAAUA,EAAOjI,OACnB,IAAKT,EAAI,EAAGC,EAAMyI,EAAOjI,OAAYR,EAAJD,EAASA,IAGxC,GAFAwI,EAAYE,EAAO1I,GAAGiG,QAAQ,MAAO,IACrCsD,EAAmBhF,EAAUiE,GACzBe,GAAoBA,EAAiB9I,OACvC,GAAIgI,EAEF,IADAa,EAAaC,EAAiBvI,QAAQyH,GAChB,KAAfa,GACLC,EAAiBE,OAAOH,EAAY,GACpCA,EAAaC,EAAiBvI,QAAQyH,EAAUa,OAGlDC,GAAiB9I,OAAS,CAKlC,OAAO0H,KAMLuB,GAAa,SAASlB,GACxB,GAAInI,EAMJ,OAJEA,GADuB,gBAAdmI,IAA0BA,EAC5B9H,EAAU6D,EAAUiE,KAAe,KAEnC9H,EAAU6D,IAQjBoF,GAAQ,SAASC,GACnB,GAAIC,GAAWC,EAAWC,CAE1B,OADAH,GAAQI,GAAaJ,GAChBA,IAGDK,GAAiBL,GAGF,UAAfA,EAAMX,MAAoBxF,EAAYW,WAAY,EAC7C+D,GAAca,MACnBC,KAAM,QACNC,KAAM,mBAGVW,EAAY9J,KAAY6J,GACxBM,GAAmBpK,KAAKqK,KAAMN,GACX,SAAfD,EAAMX,OACRc,EAAMK,GAAoB5F,GAC1BsF,EAAYC,EAAIM,KAChB5F,EAAqBsF,EAAIO,WAEpBR,GAnBP,QAyBES,GAAsB,WACxB,GAAIjE,GAAUD,EAAcC,SAAW,GAAIkE,EAAuBlE,EAAQrH,MAAM,EAAG,GAAIwL,EAAcnE,EAAQrH,MAAM,EAAGqH,EAAQtF,QAAQ,OAAS,EAC/I,OAAgC,SAAzBwJ,EAAkC,QAAmC,OAAzBA,GAAiD,KAAhBC,EAAqB/N,EAAQ8J,SAASkE,SAAWD,GAMnIE,GAAU,WACZ,GAAIC,GAASH,EAAaI,EAAgBpH,EAAYI,SACtD,OAAKmE,OAQLH,KACiC,iBAAtBpE,GAAYY,QACrBZ,EAAYY,OAAQ,QAElBZ,EAAYI,YAAcgH,GAAiBpH,EAAYI,aAAc,GACvEJ,EAAYY,OAAQ,EACpB8D,GAAca,MACZC,KAAM,QACNC,KAAM,qBAEEf,GAAc2C,mBAA4C,OAAvBrH,EAAYC,SACzD+G,EAAcF,KACVE,GAAeA,IAAgB/N,EAAQ8J,SAASkE,SAClDvC,GAAca,MACZC,KAAM,QACNC,KAAM,oBAGR0B,EAAUvE,EAAcO,iBACD,gBAAZgE,IAAwBA,GAAW,IAC5ClG,EAAqB3H,EAAY,WACQ,iBAA5B0G,GAAYU,cACrBV,EAAYU,aAAc,GAExBV,EAAYU,eAAgB,GAC9BgE,GAAca,MACZC,KAAM,QACNC,KAAM,uBAGT0B,IAELnH,EAAYW,SAAU,EACtB2G,UAxCFtH,EAAYY,OAAQ,MACpB8D,IAAca,MACZC,KAAM,QACNC,KAAM,0BA6CR8B,GAAW,WACb7C,GAAc8C,YACd9C,GAAc+C,OACd/C,GAAca,KAAK,WACnBmC,KACAhD,GAAcqB,OAMZ4B,GAAW,SAASC,EAAQhB,GAC9B,GAAIiB,EACJ,IAAsB,gBAAXD,IAAuBA,GAA0B,mBAAThB,GACjDiB,EAAUD,EACVlD,GAAc8C,gBACT,CAAA,GAAsB,gBAAXI,KAAuBA,EAIvC,MAHAC,MACAA,EAAQD,GAAUhB,EAIpB,IAAK,GAAIkB,KAAcD,GACK,gBAAfC,IAA2BA,GAAc3M,EAAQkB,KAAKwL,EAASC,IAA8C,gBAAxBD,GAAQC,IAA4BD,EAAQC,KAC1I/G,EAAU+G,GAAcC,GAAgBF,EAAQC,MAQlDE,GAAa,SAASJ,GACF,mBAAXA,IACTpK,EAAqBuD,GACrBC,EAAqB,MACM,gBAAX4G,IAAuBzM,EAAQkB,KAAK0E,EAAW6G,UACxD7G,GAAU6G,IAOjBK,GAAW,SAASL,GACtB,MAAsB,mBAAXA,GACF3K,EAAU8D,GACU,gBAAX6G,IAAuBzM,EAAQkB,KAAK0E,EAAW6G,GACxD7G,EAAU6G,GADZ,QAQLM,GAAS,SAASC,GACpB,GAAMA,GAAgC,IAArBA,EAAQlM,SAAzB,CAGIlD,IACFqP,GAAarP,EAAiB6J,EAAce,aACxC5K,IAAoBoP,GACtBC,GAAarP,EAAiB6J,EAAcc,aAGhD3K,EAAkBoP,EAClBE,GAAUF,EAASvF,EAAcc,WACjC,IAAI4E,GAAWH,EAAQI,aAAa,UAAY3F,EAAciB,KAC9D,IAAwB,gBAAbyE,IAAyBA,EAAU,CAC5C,GAAIE,GAAaC,GAAezI,EAAYC,OACxCuI,IACFA,EAAWE,aAAa,QAASJ,GAGrC,GAAIK,GAAgB/F,EAAcgB,mBAAoB,GAAyC,YAAjCgF,GAAUT,EAAS,SACjFU,IAAeF,GACfG,OAMEC,GAAQ,WACV,GAAIP,GAAaC,GAAezI,EAAYC,OACxCuI,KACFA,EAAWQ,gBAAgB,SAC3BR,EAAWS,MAAMC,KAAO,MACxBV,EAAWS,MAAMtJ,IAAM,UACvB6I,EAAWS,MAAME,MAAQ,MACzBX,EAAWS,MAAMG,OAAS,OAExBrQ,IACFqP,GAAarP,EAAiB6J,EAAcc,YAC5C0E,GAAarP,EAAiB6J,EAAce,aAC5C5K,EAAkB,OAOlBsQ,GAAiB,WACnB,MAAOtQ,IAAmB,MAMxBmL,GAAkB,SAASoF,GAC7B,MAAqB,gBAAPA,IAAmBA,GAAM,+BAA+BjK,KAAKiK,IAMzE/C,GAAe,SAASJ,GAC1B,GAAIpB,EAOJ,IANqB,gBAAVoB,IAAsBA,GAC/BpB,EAAYoB,EACZA,MAC0B,gBAAVA,IAAsBA,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAMX,OACvFT,EAAYoB,EAAMX,MAEfT,EAAL,CAGAA,EAAYA,EAAUI,eACjBgB,EAAMpJ,SAAW,4BAA4BsC,KAAK0F,IAA4B,UAAdA,GAAwC,oBAAfoB,EAAMV,QAClGU,EAAMpJ,OAAS/D,GAEjBsD,EAAQ6J,GACNX,KAAMT,EACNhI,OAAQoJ,EAAMpJ,QAAUhE,GAAmB,KAC3CwQ,cAAepD,EAAMoD,eAAiB,KACtCC,cAAexJ,GAAeA,EAAYC,QAAU,KACpDwJ,UAAWtD,EAAMsD,WAAa5O,KAAU,MAE1C,IAAI6O,GAAMvI,EAAegF,EAAMX,KA4C/B,OA3CmB,UAAfW,EAAMX,MAAoBW,EAAMV,MAAQiE,IAC1CA,EAAMA,EAAIvD,EAAMV,OAEdiE,IACFvD,EAAMwD,QAAUD,GAEC,UAAfvD,EAAMX,MACRlJ,EAAQ6J,GACNpJ,OAAQ,KACRmD,QAASF,EAAYE,UAGN,UAAfiG,EAAMX,OACJpD,EAAkC/C,KAAK8G,EAAMV,OAC/CnJ,EAAQ6J,GACNpJ,OAAQ,KACR6M,eAAgB/I,IAGhB6B,EAAyCrD,KAAK8G,EAAMV,OACtDnJ,EAAQ6J,GACNjG,QAASF,EAAYE,UAGN,mBAAfiG,EAAMV,MACRnJ,EAAQ6J,GACN0D,aAAc5Q,EAAQ8J,SAASkE,SAC/BD,YAAaF,QAIA,SAAfX,EAAMX,OACRW,EAAM2D,eACJC,QAASrF,GAAcqF,QACvBvC,UAAW9C,GAAc8C,YAGV,cAAfrB,EAAMX,OACRW,EAAQ6D,GAAyB7D,EAAOnF,IAEtCmF,EAAMpJ,SAAWoJ,EAAMoD,gBACzBpD,EAAMoD,cAAgBU,GAAkB9D,EAAMpJ,SAEzCmN,GAAc/D,KAMnB8D,GAAoB,SAASE,GAC/B,GAAIC,GAAkBD,GAAYA,EAAS5B,cAAgB4B,EAAS5B,aAAa,wBACjF,OAAO6B,GAAkBlR,EAAUmR,eAAeD,GAAmB,MAMnEF,GAAgB,SAAS/D,GAC3B,GAAIA,GAAS,8CAA8C9G,KAAK8G,EAAMX,MAAO,CAC3E,GAAI8E,GAAanE,EAAMpJ,OACnBwN,EAA6B,eAAfpE,EAAMX,MAAyBW,EAAMoD,cAAgBpD,EAAMoD,cAAgB1Q,EACzF2R,EAA2B,cAAfrE,EAAMX,MAAwBW,EAAMoD,cAAgBpD,EAAMoD,cAAgB1Q,EACtF4R,EAAMC,GAAoBJ,GAC1BK,EAAa1R,EAAQ0R,YAAc1R,EAAQ2R,SAAW,EACtDC,EAAY5R,EAAQ4R,WAAa5R,EAAQ6R,SAAW,EACpDC,EAAa7R,EAAU8R,KAAKD,WAAa7R,EAAU4G,gBAAgBiL,WACnEE,EAAY/R,EAAU8R,KAAKC,UAAY/R,EAAU4G,gBAAgBmL,UACjEC,EAAQT,EAAIvB,MAAiC,gBAAlB/C,GAAMgF,QAAuBhF,EAAMgF,QAAU,GACxEC,EAAQX,EAAI9K,KAAgC,gBAAlBwG,GAAMkF,QAAuBlF,EAAMkF,QAAU,GACvEC,EAAUJ,EAAQH,EAClBQ,EAAUH,EAAQH,EAClBL,EAAUD,EAAaW,EACvBR,EAAUD,EAAYU,EACtBC,EAAmC,gBAApBrF,GAAMsF,UAAyBtF,EAAMsF,UAAY,EAChEC,EAAmC,gBAApBvF,GAAMwF,UAAyBxF,EAAMwF,UAAY,QAC7DxF,GAAMgF,cACNhF,GAAMkF,QACb/O,EAAQ6J,GACNmE,WAAYA,EACZC,YAAaA,EACbC,UAAWA,EACXI,QAASA,EACTE,QAASA,EACTI,MAAOA,EACPE,MAAOA,EACPE,QAASA,EACTC,QAASA,EACTK,EAAGN,EACHO,EAAGN,EACHE,UAAWD,EACXG,UAAWD,EACXI,QAAS,EACTC,QAAS,EACTC,OAAQ,EACRC,OAAQ,IAGZ,MAAO9F,IAQL+F,GAAsB,SAAS/F,GACjC,GAAIpB,GAAYoB,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAMX,MAAQ,EACzE,QAAQ,gCAAgCnG,KAAK0F,IAQ3CoH,GAAoB,SAASC,EAAMC,EAASxP,EAAMyP,GAChDA,EACFhT,EAAY,WACV8S,EAAKG,MAAMF,EAASxP,IACnB,GAEHuP,EAAKG,MAAMF,EAASxP,IASpB4J,GAAqB,SAASN,GAChC,GAAuB,gBAAVA,IAAsBA,GAASA,EAAMX,KAAlD,CAGA,GAAI8G,GAAQJ,GAAoB/F,GAC5BqG,EAAuB1L,EAAU,SACjC2L,EAAuB3L,EAAUqF,EAAMX,UACvCkH,EAAWF,EAAqBG,OAAOF,EAC3C,IAAIC,GAAYA,EAAS1P,OAAQ,CAC/B,GAAIT,GAAGC,EAAK4P,EAAMC,EAASjG,EAAWwG,EAAkBlG,IACxD,KAAKnK,EAAI,EAAGC,EAAMkQ,EAAS1P,OAAYR,EAAJD,EAASA,IAC1C6P,EAAOM,EAASnQ,GAChB8P,EAAUO,EACU,gBAATR,IAA8C,kBAAlBnT,GAAQmT,KAC7CA,EAAOnT,EAAQmT,IAEG,gBAATA,IAAqBA,GAAoC,kBAArBA,GAAKS,cAClDR,EAAUD,EACVA,EAAOA,EAAKS,aAEM,kBAATT,KACThG,EAAY9J,KAAY6J,GACxBgG,GAAkBC,EAAMC,GAAWjG,GAAakG,IAItD,MAAO5F,QAOLoG,GAAkC,SAAS3G,GAC7C,GAAI4G,GAAc,IAIlB,QAHItN,KAAkB,GAAS0G,GAAwB,UAAfA,EAAMX,MAAoBW,EAAMV,MAAoE,KAA5DvD,EAAoC3E,QAAQ4I,EAAMV,SAChIsH,GAAc,GAETA,GAOLvG,GAAmB,SAASL,GAC9B,GAAIgC,GAAUhC,EAAMpJ,QAAUhE,GAAmB,KAC7CiU,EAAgC,QAAlB7G,EAAM8G,OAExB,cADO9G,GAAM8G,QACL9G,EAAMX,MACb,IAAK,QACJ,GAAIuH,GAA6B,oBAAf5G,EAAMV,MAA8BqH,GAAgC3G,EAC3D,kBAAhB4G,KACT/M,EAAYI,UAAY2M,GAEP,wBAAf5G,EAAMV,KACRnJ,EAAQ0D,GACNK,UAAU,EACVC,UAAU,EACVE,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,OAAO,IAE8C,KAA9CuB,EAAsB5E,QAAQ4I,EAAMV,MAC7CnJ,EAAQ0D,GACNK,SAAyB,mBAAf8F,EAAMV,KAChBnF,SAAyB,mBAAf6F,EAAMV,KAChBlF,SAAyB,mBAAf4F,EAAMV,KAChBjF,YAA4B,sBAAf2F,EAAMV,KACnBhF,SAAyB,mBAAf0F,EAAMV,KAChB/E,YAA4B,sBAAfyF,EAAMV,KACnB9E,QAAwB,kBAAfwF,EAAMV,KACf7E,OAAO,IAEe,qBAAfuF,EAAMV,OACf3M,EAAgBqN,EAAMR,WACtBrJ,EAAQ0D,GACNK,UAAU,EACVC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,OAAO,KAGXsM,IACA,MAED,KAAK,QACJpU,EAAgBqN,EAAMR,UACtB,IAAIwH,GAAiBnN,EAAYU,eAAgB,CACjDpE,GAAQ0D,GACNI,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,QAASwM,EACTvM,OAAQuM,IAEVD,IACA,MAED,KAAK,aACJlU,EAAcmP,CACd,MAED,KAAK,OACJ,GAAIiF,GAAaC,EAAalD,EAAWhE,EAAMoD,eACzCxI,EAAU,eAAgBA,EAAU,eAAkBoJ,IAAakD,EAAclD,EAASmD,OAASnD,EAASoD,WAAapD,EAASqD,aAAeJ,EAAcjD,EAASmD,OAASnD,EAASiD,aAAejD,EAASsD,YACtNtH,EAAM2D,cAActC,YACpBrB,EAAM2D,cAAcC,QAAQ,aAAcqD,GACtCC,IAAgBD,GAClBjH,EAAM2D,cAAcC,QAAQ,YAAasD,KAEjCtM,EAAU,eAAiBoF,EAAMpJ,SAAWqQ,EAAcjH,EAAMpJ,OAAOwL,aAAa,0BAC9FpC,EAAM2D,cAActC,YACpBrB,EAAM2D,cAAcC,QAAQ,aAAcqD,GAE5C,MAED,KAAK,YACJM,GAA0BvH,GAC1BzB,GAAc8C,YACVW,GAAWA,IAAYwF,MAAwBxF,EAAQyF,OACzDzF,EAAQyF,OAEV,MAED,KAAK,aACJlJ,GAAckJ,MAAMzF,GAChBvF,EAAcS,gBAAiB,GAAQ2J,IACrC7E,GAAWA,IAAYhC,EAAMoD,gBAAkB9L,EAAa0I,EAAMoD,cAAepB,IACnF0F,GAAgBvR,KAAY6J,GAC1BX,KAAM,aACNsI,SAAS,EACTC,YAAY,KAGhBF,GAAgBvR,KAAY6J,GAC1BX,KAAM,eAGV,MAED,KAAK,YACJd,GAAc+C,OACV7E,EAAcS,gBAAiB,GAAQ2J,IACrC7E,GAAWA,IAAYhC,EAAMoD,gBAAkB9L,EAAa0I,EAAMoD,cAAepB,IACnF0F,GAAgBvR,KAAY6J,GAC1BX,KAAM,aACNsI,SAAS,EACTC,YAAY,KAGhBF,GAAgBvR,KAAY6J,GAC1BX,KAAM,cAGV,MAED,KAAK,aACJ6C,GAAUF,EAASvF,EAAce,aAC7Bf,EAAcS,gBAAiB,GAAQ2J,GACzCa,GAAgBvR,KAAY6J,GAC1BX,KAAMW,EAAMX,KAAKhK,MAAM,KAG3B,MAED,KAAK,WACJ4M,GAAaD,EAASvF,EAAce,aAChCf,EAAcS,gBAAiB,GAAQ2J,GACzCa,GAAgBvR,KAAY6J,GAC1BX,KAAMW,EAAMX,KAAKhK,MAAM,KAG3B,MAED,KAAK,SACJxC,EAAc,KACV4J,EAAcS,gBAAiB,GAAQ2J,GACzCa,GAAgBvR,KAAY6J,GAC1BX,KAAMW,EAAMX,KAAKhK,MAAM,KAG3B,MAED,KAAK,aACAoH,EAAcS,gBAAiB,GAAQ2J,GACzCa,GAAgBvR,KAAY6J,GAC1BX,KAAMW,EAAMX,KAAKhK,MAAM,MAK7B,MAAI,8CAA8C6D,KAAK8G,EAAMX,OACpD,EADT,QAQEkI,GAA4B,SAASM,GACvC,GAAIA,EAAeC,QAAUD,EAAeC,OAAOjR,OAAS,EAAG,CAC7D,GAAIkR,GAAajR,EAAU+Q,EAC3B1R,GAAQ4R,GACN1I,KAAM,QACNC,KAAM,0BAEDyI,GAAWC,QAClB7U,EAAY,WACVoL,GAAca,KAAK2I,IAClB,KASHL,GAAkB,SAAS1H,GAC7B,GAAMA,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAjD,CAGA,GAAIjK,GAAGa,EAASoJ,EAAMpJ,QAAU,KAAMqR,EAAMrR,GAAUA,EAAOY,eAAiBzE,EAAWmV,GACvFC,KAAMF,EAAIG,aAAetV,EACzBuV,WAAW,EACXT,YAAY,EACZU,OAAuB,UAAftI,EAAMX,KAAmB,EAAI,EACrCkJ,OAA+B,gBAAhBvI,GAAMwI,MAAqBxI,EAAMwI,MAAQ,EAA4B,gBAAjBxI,GAAMuI,OAAsBvI,EAAMuI,OAASN,EAAIQ,YAAc,EAAI,GACnI/R,EAAOP,EAAQ+R,EAAUlI,EACvBpJ,IAGDqR,EAAIQ,aAAe7R,EAAO8R,gBAC5BhS,GAASA,EAAK2I,KAAM3I,EAAK2R,UAAW3R,EAAKkR,WAAYlR,EAAKyR,KAAMzR,EAAK4R,OAAQ5R,EAAK+N,QAAS/N,EAAKiO,QAASjO,EAAKyO,QAASzO,EAAK0O,QAAS1O,EAAKiS,QAASjS,EAAKkS,OAAQlS,EAAKmS,SAAUnS,EAAKoS,QAASpS,EAAK6R,OAAQ7R,EAAK0M,eAC/MrN,EAAIkS,EAAIQ,YAAY,eAChB1S,EAAEgT,iBACJhT,EAAEgT,eAAe3C,MAAMrQ,EAAGW,GAC1BX,EAAE+Q,QAAU,KACZlQ,EAAO8R,cAAc3S,OAoBvBiT,GAA8B,WAChC,GAAIhI,GAAUvE,EAAcO,gBAC5B,IAAuB,gBAAZgE,IAAwBA,GAAW,EAAG,CAC/C,GAAIiI,GAAWC,KAAKC,IAAI,IAAKnI,EAAU,IACnCoI,EAAoB3M,EAAca,YAAc,kBACpDvC,GAA4BxH,EAAa,WACvC,GAAIiC,GAAKzC,EAAUmR,eAAekF,EAC9BC,IAAkB7T,KACpBuR,KACAlN,EAAYU,YAAc,KAC1BgE,GAAca,MACZC,KAAM,QACNC,KAAM,oBAGT2J,KAOHK,GAAoB,WACtB,GAAIC,GAAYxW,EAAU6C,cAAc,MASxC,OARA2T,GAAUpG,GAAK1G,EAAcW,YAC7BmM,EAAUC,UAAY/M,EAAcY,eACpCkM,EAAUzG,MAAM2G,SAAW,WAC3BF,EAAUzG,MAAMC,KAAO,MACvBwG,EAAUzG,MAAMtJ,IAAM,UACtB+P,EAAUzG,MAAME,MAAQ,MACxBuG,EAAUzG,MAAMG,OAAS,MACzBsG,EAAUzG,MAAMnF,OAAS,GAAK+L,GAAejN,EAAckB,QACpD4L,GAMLjH,GAAiB,SAASqH,GAE5B,IADA,GAAItH,GAAasH,GAAeA,EAAYlS,WACrC4K,GAAsC,WAAxBA,EAAWzI,UAAyByI,EAAW5K,YAClE4K,EAAaA,EAAW5K,UAE1B,OAAO4K,IAAc,MAMnBuH,GAAkB,SAASC,GAC7B,MAAmB,gBAARA,IAAqBA,EAGzBA,EAAIxN,QAAQ,WAAY,SAASyN,GACtC,OAAQA,GACP,IAAK,IACJ,MAAO,QAER,KAAK,IACJ,MAAO,OAER,KAAK,IACJ,MAAO,QAER,KAAK,IACJ,MAAO,MAER,KAAK,IACJ,MAAO,MAER,SACC,MAAOA,MApBFD,GA8BP1I,GAAY,WACd,GAAI9K,GAAKsT,EAAc9P,EAAYC,OAAQyP,EAAYjH,GAAeqH,EACtE,KAAKA,EAAa,CAChB,GAAII,GAAoBC,GAAuBlX,EAAQ8J,SAASC,KAAMJ,GAClEwN,EAAwC,UAAtBF,EAAgC,OAAS,MAC3DG,EAAYC,GAAMhU,GACpBoJ,UAAWhB,GAAcxE,SACxB0C,IACC2N,EAAS3N,EAAcC,QAAU2N,GAAW5N,EAAcC,QAASD,EACnE/C,KACF0Q,EAASR,GAAgBQ,IAE3Bb,EAAYD,IACZ,IAAIgB,GAAkBvX,EAAU6C,cAAc,MAC9C2T,GAAUgB,YAAYD,GACtBvX,EAAU8R,KAAK0F,YAAYhB,EAC3B,IAAIiB,GAASzX,EAAU6C,cAAc,OACjC6U,EAA0C,YAA3B5Q,EAAYG,UAC/BwQ,GAAOnD,UAAY,eAAiB5K,EAAca,YAAc,WAAab,EAAca,YAAc,iCAAwCmN,EAAe,uDAAyD,8CAAgDL,EAAS,KAAO,KAAOK,EAAe,8BAAgCL,EAAS,MAAQ,IAAM,0CAA4CL,EAAoB,2CAAkDE,EAAkB,gHAAiIC,EAAY,eAAsBzN,EAAca,YAAc,0CACzqBqM,EAAca,EAAOE,WACrBF,EAAS,KACTlV,EAAQqU,GAAapL,cAAgBA,GACrCgL,EAAUoB,aAAahB,EAAaW,GACpCtB,KAYF,MAVKW,KACHA,EAAc5W,EAAU0J,EAAca,aAClCqM,IAAgBtT,EAAMsT,EAAY9S,UACpC8S,EAAcA,EAAYtT,EAAM,KAE7BsT,GAAeJ,IAClBI,EAAcJ,EAAUmB,aAG5B7Q,EAAYC,OAAS6P,GAAe,KAC7BA,GAMLpI,GAAc,WAChB,GAAIoI,GAAc9P,EAAYC,MAC9B,IAAI6P,EAAa,CACf,GAAItH,GAAaC,GAAeqH,EAC5BtH,KAC6B,YAA3BxI,EAAYG,YAA4B,cAAgB2P,IAC1DA,EAAY7G,MAAM8H,QAAU,OAC5B,QAAUC,KACR,GAA+B,IAA3BlB,EAAY/Q,WAAkB,CAChC,IAAK,GAAIrC,KAAQoT,GACkB,kBAAtBA,GAAYpT,KACrBoT,EAAYpT,GAAQ,KAGpBoT,GAAYlS,YACdkS,EAAYlS,WAAWqT,YAAYnB,GAEjCtH,EAAW5K,YACb4K,EAAW5K,WAAWqT,YAAYzI,OAGpClP,GAAY0X,EAAiB,SAI7BlB,EAAYlS,YACdkS,EAAYlS,WAAWqT,YAAYnB,GAEjCtH,EAAW5K,YACb4K,EAAW5K,WAAWqT,YAAYzI,KAIxC0E,KACAlN,EAAYY,MAAQ,KACpBZ,EAAYC,OAAS,KACrBD,EAAYU,YAAc,KAC1BV,EAAYO,SAAW,KACvBzH,EAAgBD,IAShB8N,GAAsB,SAASuK,GACjC,GAAIC,MAAkBtK,IACtB,IAA0B,gBAAbqK,IAAyBA,EAAtC,CAGA,IAAK,GAAIpJ,KAAcoJ,GACrB,GAAIpJ,GAAc3M,EAAQkB,KAAK6U,EAAUpJ,IAA+C,gBAAzBoJ,GAASpJ,IAA4BoJ,EAASpJ,GAC3G,OAAQA,EAAW3C,eAClB,IAAK,aACL,IAAK,OACL,IAAK,WACL,IAAK,aACJgM,EAAYC,KAAOF,EAASpJ,GAC5BjB,EAAUuK,KAAOtJ,CACjB,MAED,KAAK,YACL,IAAK,OACL,IAAK,WACL,IAAK,aACJqJ,EAAYE,KAAOH,EAASpJ,GAC5BjB,EAAUwK,KAAOvJ,CACjB,MAED,KAAK,kBACL,IAAK,WACL,IAAK,MACL,IAAK,WACL,IAAK,UACL,IAAK,YACJqJ,EAAYG,IAAMJ,EAASpJ,GAC3BjB,EAAUyK,IAAMxJ,EAQtB,OACElB,KAAMuK,EACNtK,UAAWA,KASXmD,GAA2B,SAASuH,EAAa1K,GACnD,GAA6B,gBAAhB0K,KAA4BA,GAAoC,gBAAd1K,KAA0BA,EACvF,MAAO0K,EAET,IAAIC,KACJ,KAAK,GAAI9U,KAAQ6U,GACf,GAAIpW,EAAQkB,KAAKkV,EAAa7U,GAC5B,GAAa,WAATA,EAAmB,CACrB8U,EAAW9U,GAAQ6U,EAAY7U,GAAQ6U,EAAY7U,GAAMlB,UACzD,KAAK,GAAIe,GAAI,EAAGC,EAAMgV,EAAW9U,GAAMM,OAAYR,EAAJD,EAASA,IACtDiV,EAAW9U,GAAMH,GAAGqL,OAASf,EAAU2K,EAAW9U,GAAMH,GAAGqL,YAExD,IAAa,YAATlL,GAA+B,SAATA,EAC/B8U,EAAW9U,GAAQ6U,EAAY7U,OAC1B,CACL8U,EAAW9U,KACX,IAAI+U,GAAUF,EAAY7U,EAC1B,KAAK,GAAIoL,KAAc2J,GACjB3J,GAAc3M,EAAQkB,KAAKoV,EAAS3J,IAAe3M,EAAQkB,KAAKwK,EAAWiB,KAC7E0J,EAAW9U,GAAMmK,EAAUiB,IAAe2J,EAAQ3J,IAM5D,MAAO0J,IAULhB,GAAa,SAASkB,EAAM1N,GAC9B,GAAIf,GAAuB,MAAXe,GAAmBA,GAAWA,EAAQf,aAAc,CACpE,OAAIA,IAC4B,KAAtByO,EAAKnU,QAAQ,KAAc,IAAM,KAAO,WAAa1C,IAEtD,IAUPyV,GAAQ,SAAStM,GACnB,GAAIzH,GAAGC,EAAKmV,EAAQC,EAASC,EAAM,GAAIC,IAQvC,IAPI9N,EAAQlB,iBAC4B,gBAA3BkB,GAAQlB,eACjB8O,GAAY5N,EAAQlB,gBACuB,gBAA3BkB,GAAQlB,gBAA+B,UAAYkB,GAAQlB,iBAC3E8O,EAAU5N,EAAQlB,iBAGlB8O,GAAWA,EAAQ5U,OACrB,IAAKT,EAAI,EAAGC,EAAMoV,EAAQ5U,OAAYR,EAAJD,EAASA,IACzC,GAAIpB,EAAQkB,KAAKuV,EAASrV,IAAMqV,EAAQrV,IAA4B,gBAAfqV,GAAQrV,GAAiB,CAE5E,GADAoV,EAASI,GAAeH,EAAQrV,KAC3BoV,EACH,QAEF,IAAe,MAAXA,EAAgB,CAClBG,EAAuB9U,OAAS,EAChC8U,EAAuBxM,KAAKqM,EAC5B,OAEFG,EAAuBxM,KAAKiH,MAAMuF,GAA0BH,EAAQ,KAAOA,EAAQ1Y,EAAQ8J,SAASkE,SAAW,KAAO0K,IAgB5H,MAZIG,GAAuB9U,SACzB6U,GAAO,kBAAoB7X,EAAoB8X,EAAuBrP,KAAK,OAEzEuB,EAAQd,0BAA2B,IACrC2O,IAAQA,EAAM,IAAM,IAAM,+BAEO,gBAAxB7N,GAAQP,aAA4BO,EAAQP,cACrDoO,IAAQA,EAAM,IAAM,IAAM,eAAiB7X,EAAoBgK,EAAQP,cAExC,gBAAtBO,GAAQ0B,WAA0B1B,EAAQ0B,YACnDmM,IAAQA,EAAM,IAAM,IAAM,aAAe7X,EAAoBgK,EAAQ0B,YAEhEmM,GASLE,GAAiB,SAASC,GAC5B,GAAmB,MAAfA,GAAuC,KAAhBA,EACzB,MAAO,KAGT,IADAA,EAAcA,EAAYxP,QAAQ,aAAc,IAC5B,KAAhBwP,EACF,MAAO,KAET,IAAIC,GAAgBD,EAAYzU,QAAQ,KACxCyU,GAAgC,KAAlBC,EAAuBD,EAAcA,EAAYxW,MAAMyW,EAAgB,EACrF,IAAIC,GAAYF,EAAYzU,QAAQ,IAEpC,OADAyU,GAA4B,KAAdE,EAAmBF,EAAgC,KAAlBC,GAAsC,IAAdC,EAAkB,KAAOF,EAAYxW,MAAM,EAAG0W,GACjHF,GAAuD,SAAxCA,EAAYxW,MAAM,IAAI2J,cAChC,KAEF6M,GAAe,MAQpB7B,GAAyB,WAC3B,GAAIgC,GAAqB,SAASC,GAChC,GAAI7V,GAAGC,EAAK8J,EAAK+L,IAIjB,IAHuB,gBAAZD,KACTA,GAAYA,IAEW,gBAAZA,KAAwBA,GAAqC,gBAAnBA,GAAQpV,OAC7D,MAAOqV,EAET,KAAK9V,EAAI,EAAGC,EAAM4V,EAAQpV,OAAYR,EAAJD,EAASA,IACzC,GAAIpB,EAAQkB,KAAK+V,EAAS7V,KAAO+J,EAAMyL,GAAeK,EAAQ7V,KAAM,CAClE,GAAY,MAAR+J,EAAa,CACf+L,EAAarV,OAAS,EACtBqV,EAAa/M,KAAK,IAClB,OAEgC,KAA9B+M,EAAa9U,QAAQ+I,IACvB+L,EAAa/M,KAAKgB,GAIxB,MAAO+L,GAET,OAAO,UAASC,EAAeC,GAC7B,GAAIC,GAAYT,GAAeQ,EAAc1P,QAC3B,QAAd2P,IACFA,EAAYF,EAEd,IAAIxP,GAAiBqP,EAAmBI,EAAczP,gBAClDtG,EAAMsG,EAAe9F,MACzB,IAAIR,EAAM,EAAG,CACX,GAAY,IAARA,GAAmC,MAAtBsG,EAAe,GAC9B,MAAO,QAET,IAA8C,KAA1CA,EAAevF,QAAQ+U,GACzB,MAAY,KAAR9V,GAAa8V,IAAkBE,EAC1B,aAEF,SAGX,MAAO,YASP7E,GAAqB,WACvB,IACE,MAAOzU,GAAUuZ,cACjB,MAAOlU,GACP,MAAO,QASP8J,GAAY,SAASF,EAASmF,GAChC,GAAIoF,GAAGC,EAAIhD,EAAWiD,IAItB,IAHqB,gBAAVtF,IAAsBA,IAC/BsF,EAAatF,EAAMtP,MAAM,QAEvBmK,GAAgC,IAArBA,EAAQlM,UAAkB2W,EAAW5V,OAAS,EAAG,CAE9D,IADA2S,GAAa,KAAOxH,EAAQwH,WAAa,IAAM,KAAKnN,QAAQ,cAAe,KACtEkQ,EAAI,EAAGC,EAAKC,EAAW5V,OAAY2V,EAAJD,EAAQA,IACW,KAAjD/C,EAAUpS,QAAQ,IAAMqV,EAAWF,GAAK,OAC1C/C,GAAaiD,EAAWF,GAAK,IAGjC/C,GAAYA,EAAUnN,QAAQ,aAAc,IACxCmN,IAAcxH,EAAQwH,YACxBxH,EAAQwH,UAAYA,GAGxB,MAAOxH,IAQLC,GAAe,SAASD,EAASmF,GACnC,GAAIoF,GAAGC,EAAIhD,EAAWiD,IAItB,IAHqB,gBAAVtF,IAAsBA,IAC/BsF,EAAatF,EAAMtP,MAAM,QAEvBmK,GAAgC,IAArBA,EAAQlM,UAAkB2W,EAAW5V,OAAS,GACvDmL,EAAQwH,UAAW,CAErB,IADAA,GAAa,IAAMxH,EAAQwH,UAAY,KAAKnN,QAAQ,cAAe,KAC9DkQ,EAAI,EAAGC,EAAKC,EAAW5V,OAAY2V,EAAJD,EAAQA,IAC1C/C,EAAYA,EAAUnN,QAAQ,IAAMoQ,EAAWF,GAAK,IAAK,IAE3D/C,GAAYA,EAAUnN,QAAQ,aAAc,IACxCmN,IAAcxH,EAAQwH,YACxBxH,EAAQwH,UAAYA,GAI1B,MAAOxH,IAULS,GAAY,SAASjN,EAAIe,GAC3B,GAAI4Q,GAAQxT,EAAkB6B,EAAI,MAAMkX,iBAAiBnW,EACzD,OAAa,WAATA,GACG4Q,GAAmB,SAAVA,GACQ,MAAhB3R,EAAGoE,SAKJuN,EAJM,WAYX5C,GAAsB,SAAS/O,GACjC,GAAI8O,IACFvB,KAAM,EACNvJ,IAAK,EACLwJ,MAAO,EACPC,OAAQ,EAEV,IAAIzN,EAAGmX,sBAAuB,CAC5B,GAAIC,GAASpX,EAAGmX,wBACZE,EAAc/Z,EAAQ+Z,YACtBC,EAAcha,EAAQga,YACtBC,EAAkBha,EAAU4G,gBAAgBqT,YAAc,EAC1DC,EAAiBla,EAAU4G,gBAAgBuT,WAAa,EACxDC,EAAiB,EACjBC,EAAgB,CACpB,IAA8C,aAA1C3K,GAAU1P,EAAU8R,KAAM,YAA4B,CACxD,GAAIwI,GAAWta,EAAU8R,KAAK8H,wBAC1BW,EAAWva,EAAU4G,gBAAgBgT,uBACzCQ,GAAiBE,EAAStK,KAAOuK,EAASvK,MAAQ,EAClDqK,EAAgBC,EAAS7T,IAAM8T,EAAS9T,KAAO,EAEjD8K,EAAIvB,KAAO6J,EAAO7J,KAAO8J,EAAcE,EAAkBI,EACzD7I,EAAI9K,IAAMoT,EAAOpT,IAAMsT,EAAcG,EAAiBG,EACtD9I,EAAItB,MAAQ,SAAW4J,GAASA,EAAO5J,MAAQ4J,EAAOW,MAAQX,EAAO7J,KACrEuB,EAAIrB,OAAS,UAAY2J,GAASA,EAAO3J,OAAS2J,EAAOY,OAASZ,EAAOpT,IAE3E,MAAO8K,IAQL+E,GAAoB,SAAS7T,GAC/B,IAAKA,EACH,OAAO,CAET,IAAIiY,GAAS9Z,EAAkB6B,EAAI,KACnC,KAAKiY,EACH,OAAO,CAET,IAAIC,GAAepZ,EAAYmZ,EAAOxK,QAAU,EAC5C0K,EAAcrZ,EAAYmZ,EAAOzK,OAAS,EAC1C4K,EAAYtZ,EAAYmZ,EAAOjU,MAAQ,EACvCqU,EAAavZ,EAAYmZ,EAAO1K,OAAS,EACzC+K,EAAWJ,GAAgBC,GAAeC,GAAaC,EACvDE,EAAOD,EAAW,KAAOvJ,GAAoB/O,GAC7CwY,EAA+B,SAAnBP,EAAO7C,SAA4C,aAAtB6C,EAAOQ,aAA8BH,KAAcC,IAASL,GAAgBK,EAAK9K,OAAS,KAAO0K,GAAeI,EAAK/K,MAAQ,KAAO4K,GAAaG,EAAKvU,KAAO,KAAOqU,GAAcE,EAAKhL,MAAQ,GAC5O,OAAOiL,IAQLjH,GAA2B,WAC7B1T,EAAcyH,GACdA,EAAqB,EACrBrH,EAAesH,GACfA,EAA4B,GAQ1B4H,GAAc,WAChB,GAAIN,EACJ,IAAIzP,IAAoByP,EAAaC,GAAezI,EAAYC,SAAU,CACxE,GAAIwK,GAAMC,GAAoB3R,EAC9BuD,GAAQkM,EAAWS,OACjBE,MAAOsB,EAAItB,MAAQ,KACnBC,OAAQqB,EAAIrB,OAAS,KACrBzJ,IAAK8K,EAAI9K,IAAM,KACfuJ,KAAMuB,EAAIvB,KAAO,KACjBpF,OAAQ,GAAK+L,GAAejN,EAAckB,YAU5C+E,GAAiB,SAASwL,GACxBrU,EAAYY,SAAU,IACpBZ,EAAYC,QAAsD,kBAArCD,GAAYC,OAAOqU,cAClDtU,EAAYC,OAAOqU,cAAcD,GAEjCrU,EAAYY,OAAQ,IAUtBiP,GAAiB,SAASG,GAC5B,GAAI,qBAAqB3Q,KAAK2Q,GAC5B,MAAOA,EAET,IAAIlM,EAMJ,OALmB,gBAARkM,IAAqBrV,EAAOqV,GAEb,gBAARA,KAChBlM,EAAS+L,GAAevV,EAAU0V,EAAK,MAFvClM,EAASkM,EAIc,gBAAXlM,GAAsBA,EAAS,QAQ3CiE,GAAkB,SAASwM,GAC7B,GAAIC,GAAe,eAUnB,OATuB,gBAAZD,IAAwB3R,EAAcU,kBAAmB,IAC9DnE,IACE,4BAA4BE,KAAKkV,KACnCA,EAAUA,EAAQ/R,QAAQgS,EAAc,SAEjC,KAAKnV,KAAKkV,KACnBA,EAAUA,EAAQ/R,QAAQgS,EAAc,QAGrCD,GAaLnQ,GAAiB,SAASqQ,GAC5B,GAAIC,GAAuBC,EAAOC,EAAYxN,EAAgBpH,EAAYI,UAAW2M,EAAc,IAEnG,IADA0H,EAA4BA,KAA8B,EACtDhV,KAAkB,EACpBsN,GAAc,MACT,CACL,IACE4H,EAAQ/b,EAAOic,cAAgB,KAC/B,MAAO3Y,GACP0Y,GACEnP,KAAMvJ,EAAEuJ,KACRkE,QAASzN,EAAEyN,SAGf,GAAIgL,GAA4B,IAAnBA,EAAM1Y,UAAqC,WAAnB0Y,EAAM5U,SACzC,IACEgN,EAAc4H,EAAMG,aAAa,WACjC,MAAO5Y,GACP6Q,EAAc,SAEX,CACL,IACE2H,EAAwBvb,SAASwY,QAAU,KAC3C,MAAOzV,GACPwY,EAAwB,MAEI,OAA1BA,GAAkCE,GAAkC,kBAApBA,EAAWnP,MAA4B,kDAAkDpG,KAAKuV,EAAWjL,QAAQxE,kBACnK4H,GAAc,IAQpB,MAJA/M,GAAYI,UAAY2M,EACpB3F,IAAkB2F,GAAgB0H,GACpCM,GAAoB7a,GAEf6S,GAWLgI,GAAsB,SAAS5a,GAQjC,QAAS6a,GAAkBC,GACzB,GAAI7W,GAAU6W,EAAK5W,MAAM,SAEzB,OADAD,GAAQpB,OAAS,EACVoB,EAAQqE,KAAK,KAEtB,QAASyS,GAAcC,GACrB,QAASA,IAAwBA,EAAsBA,EAAoBhQ,iBAAmB,0EAA0E9F,KAAK8V,IAA2D,kBAAnCA,EAAoB3Z,MAAM,MAEjO,QAAS4Z,GAAcC,GACjBA,IACFC,GAAW,EACPD,EAAOnV,UACTqV,EAAeP,EAAkBK,EAAOnV,WAErCqV,GAAgBF,EAAOG,cAC1BD,EAAeP,EAAkBK,EAAOG,cAEtCH,EAAOI,WACTC,EAAUR,EAAcG,EAAOI,YAzBrC,GAAIJ,GAAQM,EAAIC,EAAUN,GAAW,EAAOO,GAAY,EAAOH,GAAU,EAAOH,EAAe,EA6B/F,IAAInc,EAAW0c,SAAW1c,EAAW0c,QAAQ9Y,OAC3CqY,EAASjc,EAAW0c,QAAQ,mBAC5BV,EAAcC,GACVjc,EAAW0c,QAAQ,yBACrBR,GAAW,EACXC,EAAe,gBAEZ,IAAInc,EAAW2c,WAAa3c,EAAW2c,UAAU/Y,OACtD4Y,EAAWxc,EAAW2c,UAAU,iCAChCV,EAASO,GAAYA,EAASI,cAC9BZ,EAAcC,OACT,IAA6B,mBAAlBlb,GAA+B,CAC/C0b,GAAY,CACZ,KACEF,EAAK,GAAIxb,GAAc,mCACvBmb,GAAW,EACXC,EAAeP,EAAkBW,EAAGM,YAAY,aAChD,MAAOC,GACP,IACEP,EAAK,GAAIxb,GAAc,mCACvBmb,GAAW,EACXC,EAAe,SACf,MAAOY,GACP,IACER,EAAK,GAAIxb,GAAc,iCACvBmb,GAAW,EACXC,EAAeP,EAAkBW,EAAGM,YAAY,aAChD,MAAOG,GACPP,GAAY,KAKpB7V,EAAYK,SAAWiV,KAAa,EACpCtV,EAAYM,SAAWiV,GAAgB9a,EAAY8a,GAAgB9a,EAAYoG,GAC/Eb,EAAYE,QAAUqV,GAAgB,QACtCvV,EAAYG,WAAauV,EAAU,SAAWG,EAAY,UAAYP,EAAW,WAAa,UAKhGP,IAAoB7a,GAIpBkK,IAAe,EAMf,IAAIM,IAAgB,WAClB,MAAMgC,gBAAgBhC,SAGqB,kBAAhCA,IAAc2R,eACvB3R,GAAc2R,cAAc9J,MAAM7F,KAAMvK,EAAMW,aAHvC,GAAI4H,IAafA,IAAcxE,QAAU,eAQxBwE,GAAcC,OAAS,WACrB,MAAOZ,GAAQwI,MAAM7F,KAAMvK,EAAMW,aAQnC4H,GAAc4R,MAAQ,WACpB,MAAOnS,IAAOoI,MAAM7F,KAAMvK,EAAMW,aAQlC4H,GAAc2C,gBAAkB,WAC9B,MAAOxC,IAAiB0H,MAAM7F,KAAMvK,EAAMW,aAQ5C4H,GAAcW,GAAK,WACjB,MAAOP,IAAIyH,MAAM7F,KAAMvK,EAAMW,aAU/B4H,GAAcqB,IAAM,WAClB,MAAOH,IAAK2G,MAAM7F,KAAMvK,EAAMW,aAQhC4H,GAAcgI,SAAW,WACvB,MAAOzG,IAAWsG,MAAM7F,KAAMvK,EAAMW,aAQtC4H,GAAca,KAAO,WACnB,MAAOW,IAAMqG,MAAM7F,KAAMvK,EAAMW,aAQjC4H,GAAc6R,OAAS,WACrB,MAAOrP,IAAQqF,MAAM7F,KAAMvK,EAAMW,aAQnC4H,GAAc8R,QAAU,WACtB,MAAOjP,IAASgF,MAAM7F,KAAMvK,EAAMW,aAQpC4H,GAAcqF,QAAU,WACtB,MAAOpC,IAAS4E,MAAM7F,KAAMvK,EAAMW,aASpC4H,GAAc8C,UAAY,WACxB,MAAOQ,IAAWuE,MAAM7F,KAAMvK,EAAMW,aAStC4H,GAAc+R,QAAU,WACtB,MAAOxO,IAASsE,MAAM7F,KAAMvK,EAAMW,aAWpC4H,GAAckJ,MAAQlJ,GAAcgS,SAAW,WAC7C,MAAOxO,IAAOqE,MAAM7F,KAAMvK,EAAMW,aAUlC4H,GAAc+C,KAAO/C,GAAciS,WAAa,WAC9C,MAAO5N,IAAMwD,MAAM7F,KAAMvK,EAAMW,aAQjC4H,GAAc+N,cAAgB,WAC5B,MAAOpJ,IAAekD,MAAM7F,KAAMvK,EAAMW,aAEpB,kBAAX8Z,SAAyBA,OAAOC,IACzCD,OAAO,WACL,MAAOlS,MAEkB,gBAAXoS,SAAuBA,QAAoC,gBAAnBA,QAAOC,SAAwBD,OAAOC,QAC9FD,OAAOC,QAAUrS,GAEjB9L,EAAO8L,cAAgBA,IAExB,WACD,MAAOgC,OAAQ9N", "sourcesContent": ["/*!\n * ZeroClipboard\n * The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface\n * Copyright (c) 2009-2017 <PERSON>, <PERSON>\n * Licensed MIT\n * http://zeroclipboard.github.io/\n * v2.4.0-beta.1\n */\n(function(window, undefined) {\n  \"use strict\";\n  /**\n * Store references to critically important global functions that may be\n * overridden on certain web pages.\n */\n  var _window = window, _document = _window.document, _navigator = _window.navigator, _setTimeout = _window.setTimeout, _clearTimeout = _window.clearTimeout, _setInterval = _window.setInterval, _clearInterval = _window.clearInterval, _getComputedStyle = _window.getComputedStyle, _encodeURIComponent = _window.encodeURIComponent, _ActiveXObject = _window.ActiveXObject, _Error = _window.Error, _parseInt = _window.Number.parseInt || _window.parseInt, _parseFloat = _window.Number.parseFloat || _window.parseFloat, _isNaN = _window.Number.isNaN || _window.isNaN, _now = _window.Date.now, _keys = _window.Object.keys, _hasOwn = _window.Object.prototype.hasOwnProperty, _slice = _window.Array.prototype.slice, _unwrap = function() {\n    var unwrapper = function(el) {\n      return el;\n    };\n    if (typeof _window.wrap === \"function\" && typeof _window.unwrap === \"function\") {\n      try {\n        var div = _document.createElement(\"div\");\n        var unwrappedDiv = _window.unwrap(div);\n        if (div.nodeType === 1 && unwrappedDiv && unwrappedDiv.nodeType === 1) {\n          unwrapper = _window.unwrap;\n        }\n      } catch (e) {}\n    }\n    return unwrapper;\n  }();\n  /**\n * Convert an `arguments` object into an Array.\n *\n * @returns The arguments as an Array\n * @private\n */\n  var _args = function(argumentsObj) {\n    return _slice.call(argumentsObj, 0);\n  };\n  /**\n * Shallow-copy the owned, enumerable properties of one object over to another, similar to jQuery's `$.extend`.\n *\n * @returns The target object, augmented\n * @private\n */\n  var _extend = function() {\n    var i, len, arg, prop, src, copy, args = _args(arguments), target = args[0] || {};\n    for (i = 1, len = args.length; i < len; i++) {\n      if ((arg = args[i]) != null) {\n        for (prop in arg) {\n          if (_hasOwn.call(arg, prop)) {\n            src = target[prop];\n            copy = arg[prop];\n            if (target !== copy && copy !== undefined) {\n              target[prop] = copy;\n            }\n          }\n        }\n      }\n    }\n    return target;\n  };\n  /**\n * Return a deep copy of the source object or array.\n *\n * @returns Object or Array\n * @private\n */\n  var _deepCopy = function(source) {\n    var copy, i, len, prop;\n    if (typeof source !== \"object\" || source == null || typeof source.nodeType === \"number\") {\n      copy = source;\n    } else if (typeof source.length === \"number\") {\n      copy = [];\n      for (i = 0, len = source.length; i < len; i++) {\n        if (_hasOwn.call(source, i)) {\n          copy[i] = _deepCopy(source[i]);\n        }\n      }\n    } else {\n      copy = {};\n      for (prop in source) {\n        if (_hasOwn.call(source, prop)) {\n          copy[prop] = _deepCopy(source[prop]);\n        }\n      }\n    }\n    return copy;\n  };\n  /**\n * Makes a shallow copy of `obj` (like `_extend`) but filters its properties based on a list of `keys` to keep.\n * The inverse of `_omit`, mostly. The big difference is that these properties do NOT need to be enumerable to\n * be kept.\n *\n * @returns A new filtered object.\n * @private\n */\n  var _pick = function(obj, keys) {\n    var newObj = {};\n    for (var i = 0, len = keys.length; i < len; i++) {\n      if (keys[i] in obj) {\n        newObj[keys[i]] = obj[keys[i]];\n      }\n    }\n    return newObj;\n  };\n  /**\n * Makes a shallow copy of `obj` (like `_extend`) but filters its properties based on a list of `keys` to omit.\n * The inverse of `_pick`.\n *\n * @returns A new filtered object.\n * @private\n */\n  var _omit = function(obj, keys) {\n    var newObj = {};\n    for (var prop in obj) {\n      if (keys.indexOf(prop) === -1) {\n        newObj[prop] = obj[prop];\n      }\n    }\n    return newObj;\n  };\n  /**\n * Remove all owned, enumerable properties from an object.\n *\n * @returns The original object without its owned, enumerable properties.\n * @private\n */\n  var _deleteOwnProperties = function(obj) {\n    if (obj) {\n      for (var prop in obj) {\n        if (_hasOwn.call(obj, prop)) {\n          delete obj[prop];\n        }\n      }\n    }\n    return obj;\n  };\n  /**\n * Determine if an element is contained within another element.\n *\n * @returns Boolean\n * @private\n */\n  var _containedBy = function(el, ancestorEl) {\n    if (el && el.nodeType === 1 && el.ownerDocument && ancestorEl && (ancestorEl.nodeType === 1 && ancestorEl.ownerDocument && ancestorEl.ownerDocument === el.ownerDocument || ancestorEl.nodeType === 9 && !ancestorEl.ownerDocument && ancestorEl === el.ownerDocument)) {\n      do {\n        if (el === ancestorEl) {\n          return true;\n        }\n        el = el.parentNode;\n      } while (el);\n    }\n    return false;\n  };\n  /**\n * Get the URL path's parent directory.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getDirPathOfUrl = function(url) {\n    var dir;\n    if (typeof url === \"string\" && url) {\n      dir = url.split(\"#\")[0].split(\"?\")[0];\n      dir = url.slice(0, url.lastIndexOf(\"/\") + 1);\n    }\n    return dir;\n  };\n  /**\n * Get the current script's URL by throwing an `Error` and analyzing it.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrlFromErrorStack = function(stack) {\n    var url, matches;\n    if (typeof stack === \"string\" && stack) {\n      matches = stack.match(/^(?:|[^:@]*@|.+\\)@(?=http[s]?|file)|.+?\\s+(?: at |@)(?:[^:\\(]+ )*[\\(]?)((?:http[s]?|file):\\/\\/[\\/]?.+?\\/[^:\\)]*?)(?::\\d+)(?::\\d+)?/);\n      if (matches && matches[1]) {\n        url = matches[1];\n      } else {\n        matches = stack.match(/\\)@((?:http[s]?|file):\\/\\/[\\/]?.+?\\/[^:\\)]*?)(?::\\d+)(?::\\d+)?/);\n        if (matches && matches[1]) {\n          url = matches[1];\n        }\n      }\n    }\n    return url;\n  };\n  /**\n * Get the current script's URL by throwing an `Error` and analyzing it.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrlFromError = function() {\n    var url, err;\n    try {\n      throw new _Error();\n    } catch (e) {\n      err = e;\n    }\n    if (err) {\n      url = err.sourceURL || err.fileName || _getCurrentScriptUrlFromErrorStack(err.stack);\n    }\n    return url;\n  };\n  /**\n * Get the current script's URL.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrl = function() {\n    var jsPath, scripts, i;\n    if (_document.currentScript && (jsPath = _document.currentScript.src)) {\n      return jsPath;\n    }\n    scripts = _document.getElementsByTagName(\"script\");\n    if (scripts.length === 1) {\n      return scripts[0].src || undefined;\n    }\n    if (\"readyState\" in (scripts[0] || document.createElement(\"script\"))) {\n      for (i = scripts.length; i--; ) {\n        if (scripts[i].readyState === \"interactive\" && (jsPath = scripts[i].src)) {\n          return jsPath;\n        }\n      }\n    }\n    if (_document.readyState === \"loading\" && (jsPath = scripts[scripts.length - 1].src)) {\n      return jsPath;\n    }\n    if (jsPath = _getCurrentScriptUrlFromError()) {\n      return jsPath;\n    }\n    return undefined;\n  };\n  /**\n * Get the unanimous parent directory of ALL script tags.\n * If any script tags are either (a) inline or (b) from differing parent\n * directories, this method must return `undefined`.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getUnanimousScriptParentDir = function() {\n    var i, jsDir, jsPath, scripts = _document.getElementsByTagName(\"script\");\n    for (i = scripts.length; i--; ) {\n      if (!(jsPath = scripts[i].src)) {\n        jsDir = null;\n        break;\n      }\n      jsPath = _getDirPathOfUrl(jsPath);\n      if (jsDir == null) {\n        jsDir = jsPath;\n      } else if (jsDir !== jsPath) {\n        jsDir = null;\n        break;\n      }\n    }\n    return jsDir || undefined;\n  };\n  /**\n * Get the presumed location of the \"ZeroClipboard.swf\" file, based on the location\n * of the executing JavaScript file (e.g. \"ZeroClipboard.js\", etc.).\n *\n * @returns String\n * @private\n */\n  var _getDefaultSwfPath = function() {\n    var jsDir = _getDirPathOfUrl(_getCurrentScriptUrl()) || _getUnanimousScriptParentDir() || \"\";\n    return jsDir + \"ZeroClipboard.swf\";\n  };\n  /**\n * Is the client's operating system some version of Windows?\n *\n * @returns Boolean\n * @private\n */\n  var _isWindows = function() {\n    var isWindowsRegex = /win(dows|[\\s]?(nt|me|ce|xp|vista|[\\d]+))/i;\n    return !!_navigator && (isWindowsRegex.test(_navigator.appVersion || \"\") || isWindowsRegex.test(_navigator.platform || \"\") || (_navigator.userAgent || \"\").indexOf(\"Windows\") !== -1);\n  };\n  /**\n * Keep track of if the page is framed (in an `iframe`). This can never change.\n * @private\n */\n  var _pageIsFramed = function() {\n    return _window.opener == null && (!!_window.top && _window != _window.top || !!_window.parent && _window != _window.parent);\n  }();\n  /**\n * Keep track of if the page is XHTML (vs. HTML), which requires that everything\n * be rendering in XML mode.\n * @private\n */\n  var _pageIsXhtml = _document.documentElement.nodeName === \"html\";\n  /**\n * Keep track of the state of the Flash object.\n * @private\n */\n  var _flashState = {\n    bridge: null,\n    version: \"0.0.0\",\n    pluginType: \"unknown\",\n    sandboxed: null,\n    disabled: null,\n    outdated: null,\n    insecure: null,\n    unavailable: null,\n    degraded: null,\n    deactivated: null,\n    overdue: null,\n    ready: null\n  };\n  /**\n * The minimum Flash Player version required to use ZeroClipboard completely.\n * @readonly\n * @private\n */\n  var _minimumFlashVersion = \"11.0.0\";\n  /**\n * The ZeroClipboard library version number, as reported by Flash, at the time the SWF was compiled.\n */\n  var _zcSwfVersion;\n  /**\n * Keep track of all event listener registrations.\n * @private\n */\n  var _handlers = {};\n  /**\n * Keep track of the currently activated element.\n * @private\n */\n  var _currentElement;\n  /**\n * Keep track of the element that was activated when a `copy` process started.\n * @private\n */\n  var _copyTarget;\n  /**\n * Keep track of data for the pending clipboard transaction.\n * @private\n */\n  var _clipData = {};\n  /**\n * Keep track of data formats for the pending clipboard transaction.\n * @private\n */\n  var _clipDataFormatMap = null;\n  /**\n * Keep track of the Flash availability check timeout.\n * @private\n */\n  var _flashCheckTimeout = 0;\n  /**\n * Keep track of SWF network errors interval polling.\n * @private\n */\n  var _swfFallbackCheckInterval = 0;\n  /**\n * The `message` store for events\n * @private\n */\n  var _eventMessages = {\n    ready: \"Flash communication is established\",\n    error: {\n      \"flash-sandboxed\": \"Attempting to run Flash in a sandboxed iframe, which is impossible\",\n      \"flash-disabled\": \"Flash is disabled or not installed. May also be attempting to run Flash in a sandboxed iframe, which is impossible.\",\n      \"flash-outdated\": \"Flash is too outdated to support ZeroClipboard\",\n      \"flash-insecure\": \"Flash will be unable to communicate due to a protocol mismatch between your `swfPath` configuration and the page\",\n      \"flash-unavailable\": \"Flash is unable to communicate bidirectionally with JavaScript\",\n      \"flash-degraded\": \"Flash is unable to preserve data fidelity when communicating with JavaScript\",\n      \"flash-deactivated\": \"Flash is too outdated for your browser and/or is configured as click-to-activate.\\nThis may also mean that the ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity.\\nMay also be attempting to run Flash in a sandboxed iframe, which is impossible.\",\n      \"flash-overdue\": \"Flash communication was established but NOT within the acceptable time limit\",\n      \"version-mismatch\": \"ZeroClipboard JS version number does not match ZeroClipboard SWF version number\",\n      \"clipboard-error\": \"At least one error was thrown while ZeroClipboard was attempting to inject your data into the clipboard\",\n      \"config-mismatch\": \"ZeroClipboard configuration does not match Flash's reality\",\n      \"swf-not-found\": \"The ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity\",\n      \"browser-unsupported\": \"The browser does not support the required HTML DOM and JavaScript features\"\n    }\n  };\n  /**\n * The `name`s of `error` events that can only occur is Flash has at least\n * been able to load the SWF successfully.\n * @private\n */\n  var _errorsThatOnlyOccurAfterFlashLoads = [ \"flash-unavailable\", \"flash-degraded\", \"flash-overdue\", \"version-mismatch\", \"config-mismatch\", \"clipboard-error\" ];\n  /**\n * The `name`s of `error` events that should likely result in the `_flashState`\n * variable's property values being updated.\n * @private\n */\n  var _flashStateErrorNames = [ \"flash-sandboxed\", \"flash-disabled\", \"flash-outdated\", \"flash-insecure\", \"flash-unavailable\", \"flash-degraded\", \"flash-deactivated\", \"flash-overdue\" ];\n  /**\n * A RegExp to match the `name` property of `error` events related to Flash.\n * @private\n */\n  var _flashStateErrorNameMatchingRegex = new RegExp(\"^flash-(\" + _flashStateErrorNames.map(function(errorName) {\n    return errorName.replace(/^flash-/, \"\");\n  }).join(\"|\") + \")$\");\n  /**\n * A RegExp to match the `name` property of `error` events related to Flash,\n * which is enabled.\n * @private\n */\n  var _flashStateEnabledErrorNameMatchingRegex = new RegExp(\"^flash-(\" + _flashStateErrorNames.filter(function(errorName) {\n    return errorName !== \"flash-disabled\";\n  }).map(function(errorName) {\n    return errorName.replace(/^flash-/, \"\");\n  }).join(\"|\") + \")$\");\n  /**\n * ZeroClipboard configuration defaults for the Core module.\n * @private\n */\n  var _globalConfig = {\n    swfPath: _getDefaultSwfPath(),\n    trustedDomains: _window.location.host ? [ _window.location.host ] : [],\n    cacheBust: true,\n    forceEnhancedClipboard: false,\n    flashLoadTimeout: 3e4,\n    autoActivate: true,\n    bubbleEvents: true,\n    fixLineEndings: true,\n    containerId: \"global-zeroclipboard-html-bridge\",\n    containerClass: \"global-zeroclipboard-container\",\n    swfObjectId: \"global-zeroclipboard-flash-bridge\",\n    hoverClass: \"zeroclipboard-is-hover\",\n    activeClass: \"zeroclipboard-is-active\",\n    forceHandCursor: false,\n    title: null,\n    zIndex: 999999999\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.config`.\n * @private\n */\n  var _config = function(options) {\n    if (typeof options === \"object\" && options && !(\"length\" in options)) {\n      _keys(options).forEach(function(prop) {\n        if (/^(?:forceHandCursor|title|zIndex|bubbleEvents|fixLineEndings)$/.test(prop)) {\n          _globalConfig[prop] = options[prop];\n        } else if (_flashState.bridge == null) {\n          if (prop === \"containerId\" || prop === \"swfObjectId\") {\n            if (_isValidHtml4Id(options[prop])) {\n              _globalConfig[prop] = options[prop];\n            } else {\n              throw new Error(\"The specified `\" + prop + \"` value is not valid as an HTML4 Element ID\");\n            }\n          } else {\n            _globalConfig[prop] = options[prop];\n          }\n        }\n      });\n    }\n    if (typeof options === \"string\" && options) {\n      if (_hasOwn.call(_globalConfig, options)) {\n        return _globalConfig[options];\n      }\n      return;\n    }\n    return _deepCopy(_globalConfig);\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.state`.\n * @private\n */\n  var _state = function() {\n    _detectSandbox();\n    return {\n      browser: _extend(_pick(_navigator, [ \"userAgent\", \"platform\", \"appName\", \"appVersion\" ]), {\n        isSupported: _isBrowserSupported()\n      }),\n      flash: _omit(_flashState, [ \"bridge\" ]),\n      zeroclipboard: {\n        version: ZeroClipboard.version,\n        config: ZeroClipboard.config()\n      }\n    };\n  };\n  /**\n * Does this browser support all of the necessary DOM and JS features necessary?\n * @private\n */\n  var _isBrowserSupported = function() {\n    return !!(_document.addEventListener && _window.Object.keys && _window.Array.prototype.map);\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.isFlashUnusable`.\n * @private\n */\n  var _isFlashUnusable = function() {\n    return !!(_flashState.sandboxed || _flashState.disabled || _flashState.outdated || _flashState.unavailable || _flashState.degraded || _flashState.deactivated);\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.on`.\n * @private\n */\n  var _on = function(eventType, listener) {\n    var i, len, events, added = {};\n    if (typeof eventType === \"string\" && eventType) {\n      events = eventType.toLowerCase().split(/\\s+/);\n    } else if (typeof eventType === \"object\" && eventType && !(\"length\" in eventType) && typeof listener === \"undefined\") {\n      _keys(eventType).forEach(function(key) {\n        var listener = eventType[key];\n        if (typeof listener === \"function\") {\n          ZeroClipboard.on(key, listener);\n        }\n      });\n    }\n    if (events && events.length && listener) {\n      for (i = 0, len = events.length; i < len; i++) {\n        eventType = events[i].replace(/^on/, \"\");\n        added[eventType] = true;\n        if (!_handlers[eventType]) {\n          _handlers[eventType] = [];\n        }\n        _handlers[eventType].push(listener);\n      }\n      if (added.ready && _flashState.ready) {\n        ZeroClipboard.emit({\n          type: \"ready\"\n        });\n      }\n      if (added.error) {\n        if (!_isBrowserSupported()) {\n          ZeroClipboard.emit({\n            type: \"error\",\n            name: \"browser-unsupported\"\n          });\n        }\n        for (i = 0, len = _flashStateErrorNames.length; i < len; i++) {\n          if (_flashState[_flashStateErrorNames[i].replace(/^flash-/, \"\")] === true) {\n            ZeroClipboard.emit({\n              type: \"error\",\n              name: _flashStateErrorNames[i]\n            });\n            break;\n          }\n        }\n        if (_zcSwfVersion !== undefined && ZeroClipboard.version !== _zcSwfVersion) {\n          ZeroClipboard.emit({\n            type: \"error\",\n            name: \"version-mismatch\",\n            jsVersion: ZeroClipboard.version,\n            swfVersion: _zcSwfVersion\n          });\n        }\n      }\n    }\n    return ZeroClipboard;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.off`.\n * @private\n */\n  var _off = function(eventType, listener) {\n    var i, len, foundIndex, events, perEventHandlers;\n    if (arguments.length === 0) {\n      events = _keys(_handlers);\n    } else if (typeof eventType === \"string\" && eventType) {\n      events = eventType.toLowerCase().split(/\\s+/);\n    } else if (typeof eventType === \"object\" && eventType && !(\"length\" in eventType) && typeof listener === \"undefined\") {\n      _keys(eventType).forEach(function(key) {\n        var listener = eventType[key];\n        if (typeof listener === \"function\") {\n          ZeroClipboard.off(key, listener);\n        }\n      });\n    }\n    if (events && events.length) {\n      for (i = 0, len = events.length; i < len; i++) {\n        eventType = events[i].replace(/^on/, \"\");\n        perEventHandlers = _handlers[eventType];\n        if (perEventHandlers && perEventHandlers.length) {\n          if (listener) {\n            foundIndex = perEventHandlers.indexOf(listener);\n            while (foundIndex !== -1) {\n              perEventHandlers.splice(foundIndex, 1);\n              foundIndex = perEventHandlers.indexOf(listener, foundIndex);\n            }\n          } else {\n            perEventHandlers.length = 0;\n          }\n        }\n      }\n    }\n    return ZeroClipboard;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.handlers`.\n * @private\n */\n  var _listeners = function(eventType) {\n    var copy;\n    if (typeof eventType === \"string\" && eventType) {\n      copy = _deepCopy(_handlers[eventType]) || null;\n    } else {\n      copy = _deepCopy(_handlers);\n    }\n    return copy;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.emit`.\n * @private\n */\n  var _emit = function(event) {\n    var eventCopy, returnVal, tmp;\n    event = _createEvent(event);\n    if (!event) {\n      return;\n    }\n    if (_preprocessEvent(event)) {\n      return;\n    }\n    if (event.type === \"ready\" && _flashState.overdue === true) {\n      return ZeroClipboard.emit({\n        type: \"error\",\n        name: \"flash-overdue\"\n      });\n    }\n    eventCopy = _extend({}, event);\n    _dispatchCallbacks.call(this, eventCopy);\n    if (event.type === \"copy\") {\n      tmp = _mapClipDataToFlash(_clipData);\n      returnVal = tmp.data;\n      _clipDataFormatMap = tmp.formatMap;\n    }\n    return returnVal;\n  };\n  /**\n * Get the protocol of the configured SWF path.\n * @private\n */\n  var _getSwfPathProtocol = function() {\n    var swfPath = _globalConfig.swfPath || \"\", swfPathFirstTwoChars = swfPath.slice(0, 2), swfProtocol = swfPath.slice(0, swfPath.indexOf(\"://\") + 1);\n    return swfPathFirstTwoChars === \"\\\\\\\\\" ? \"file:\" : swfPathFirstTwoChars === \"//\" || swfProtocol === \"\" ? _window.location.protocol : swfProtocol;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.create`.\n * @private\n */\n  var _create = function() {\n    var maxWait, swfProtocol, previousState = _flashState.sandboxed;\n    if (!_isBrowserSupported()) {\n      _flashState.ready = false;\n      ZeroClipboard.emit({\n        type: \"error\",\n        name: \"browser-unsupported\"\n      });\n      return;\n    }\n    _detectSandbox();\n    if (typeof _flashState.ready !== \"boolean\") {\n      _flashState.ready = false;\n    }\n    if (_flashState.sandboxed !== previousState && _flashState.sandboxed === true) {\n      _flashState.ready = false;\n      ZeroClipboard.emit({\n        type: \"error\",\n        name: \"flash-sandboxed\"\n      });\n    } else if (!ZeroClipboard.isFlashUnusable() && _flashState.bridge === null) {\n      swfProtocol = _getSwfPathProtocol();\n      if (swfProtocol && swfProtocol !== _window.location.protocol) {\n        ZeroClipboard.emit({\n          type: \"error\",\n          name: \"flash-insecure\"\n        });\n      } else {\n        maxWait = _globalConfig.flashLoadTimeout;\n        if (typeof maxWait === \"number\" && maxWait >= 0) {\n          _flashCheckTimeout = _setTimeout(function() {\n            if (typeof _flashState.deactivated !== \"boolean\") {\n              _flashState.deactivated = true;\n            }\n            if (_flashState.deactivated === true) {\n              ZeroClipboard.emit({\n                type: \"error\",\n                name: \"flash-deactivated\"\n              });\n            }\n          }, maxWait);\n        }\n        _flashState.overdue = false;\n        _embedSwf();\n      }\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.destroy`.\n * @private\n */\n  var _destroy = function() {\n    ZeroClipboard.clearData();\n    ZeroClipboard.blur();\n    ZeroClipboard.emit(\"destroy\");\n    _unembedSwf();\n    ZeroClipboard.off();\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.setData`.\n * @private\n */\n  var _setData = function(format, data) {\n    var dataObj;\n    if (typeof format === \"object\" && format && typeof data === \"undefined\") {\n      dataObj = format;\n      ZeroClipboard.clearData();\n    } else if (typeof format === \"string\" && format) {\n      dataObj = {};\n      dataObj[format] = data;\n    } else {\n      return;\n    }\n    for (var dataFormat in dataObj) {\n      if (typeof dataFormat === \"string\" && dataFormat && _hasOwn.call(dataObj, dataFormat) && typeof dataObj[dataFormat] === \"string\" && dataObj[dataFormat]) {\n        _clipData[dataFormat] = _fixLineEndings(dataObj[dataFormat]);\n      }\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.clearData`.\n * @private\n */\n  var _clearData = function(format) {\n    if (typeof format === \"undefined\") {\n      _deleteOwnProperties(_clipData);\n      _clipDataFormatMap = null;\n    } else if (typeof format === \"string\" && _hasOwn.call(_clipData, format)) {\n      delete _clipData[format];\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.getData`.\n * @private\n */\n  var _getData = function(format) {\n    if (typeof format === \"undefined\") {\n      return _deepCopy(_clipData);\n    } else if (typeof format === \"string\" && _hasOwn.call(_clipData, format)) {\n      return _clipData[format];\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.focus`/`ZeroClipboard.activate`.\n * @private\n */\n  var _focus = function(element) {\n    if (!(element && element.nodeType === 1)) {\n      return;\n    }\n    if (_currentElement) {\n      _removeClass(_currentElement, _globalConfig.activeClass);\n      if (_currentElement !== element) {\n        _removeClass(_currentElement, _globalConfig.hoverClass);\n      }\n    }\n    _currentElement = element;\n    _addClass(element, _globalConfig.hoverClass);\n    var newTitle = element.getAttribute(\"title\") || _globalConfig.title;\n    if (typeof newTitle === \"string\" && newTitle) {\n      var htmlBridge = _getHtmlBridge(_flashState.bridge);\n      if (htmlBridge) {\n        htmlBridge.setAttribute(\"title\", newTitle);\n      }\n    }\n    var useHandCursor = _globalConfig.forceHandCursor === true || _getStyle(element, \"cursor\") === \"pointer\";\n    _setHandCursor(useHandCursor);\n    _reposition();\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.blur`/`ZeroClipboard.deactivate`.\n * @private\n */\n  var _blur = function() {\n    var htmlBridge = _getHtmlBridge(_flashState.bridge);\n    if (htmlBridge) {\n      htmlBridge.removeAttribute(\"title\");\n      htmlBridge.style.left = \"0px\";\n      htmlBridge.style.top = \"-9999px\";\n      htmlBridge.style.width = \"1px\";\n      htmlBridge.style.height = \"1px\";\n    }\n    if (_currentElement) {\n      _removeClass(_currentElement, _globalConfig.hoverClass);\n      _removeClass(_currentElement, _globalConfig.activeClass);\n      _currentElement = null;\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.activeElement`.\n * @private\n */\n  var _activeElement = function() {\n    return _currentElement || null;\n  };\n  /**\n * Check if a value is a valid HTML4 `ID` or `Name` token.\n * @private\n */\n  var _isValidHtml4Id = function(id) {\n    return typeof id === \"string\" && id && /^[A-Za-z][A-Za-z0-9_:\\-\\.]*$/.test(id);\n  };\n  /**\n * Create or update an `event` object, based on the `eventType`.\n * @private\n */\n  var _createEvent = function(event) {\n    var eventType;\n    if (typeof event === \"string\" && event) {\n      eventType = event;\n      event = {};\n    } else if (typeof event === \"object\" && event && typeof event.type === \"string\" && event.type) {\n      eventType = event.type;\n    }\n    if (!eventType) {\n      return;\n    }\n    eventType = eventType.toLowerCase();\n    if (!event.target && (/^(copy|aftercopy|_click)$/.test(eventType) || eventType === \"error\" && event.name === \"clipboard-error\")) {\n      event.target = _copyTarget;\n    }\n    _extend(event, {\n      type: eventType,\n      target: event.target || _currentElement || null,\n      relatedTarget: event.relatedTarget || null,\n      currentTarget: _flashState && _flashState.bridge || null,\n      timeStamp: event.timeStamp || _now() || null\n    });\n    var msg = _eventMessages[event.type];\n    if (event.type === \"error\" && event.name && msg) {\n      msg = msg[event.name];\n    }\n    if (msg) {\n      event.message = msg;\n    }\n    if (event.type === \"ready\") {\n      _extend(event, {\n        target: null,\n        version: _flashState.version\n      });\n    }\n    if (event.type === \"error\") {\n      if (_flashStateErrorNameMatchingRegex.test(event.name)) {\n        _extend(event, {\n          target: null,\n          minimumVersion: _minimumFlashVersion\n        });\n      }\n      if (_flashStateEnabledErrorNameMatchingRegex.test(event.name)) {\n        _extend(event, {\n          version: _flashState.version\n        });\n      }\n      if (event.name === \"flash-insecure\") {\n        _extend(event, {\n          pageProtocol: _window.location.protocol,\n          swfProtocol: _getSwfPathProtocol()\n        });\n      }\n    }\n    if (event.type === \"copy\") {\n      event.clipboardData = {\n        setData: ZeroClipboard.setData,\n        clearData: ZeroClipboard.clearData\n      };\n    }\n    if (event.type === \"aftercopy\") {\n      event = _mapClipResultsFromFlash(event, _clipDataFormatMap);\n    }\n    if (event.target && !event.relatedTarget) {\n      event.relatedTarget = _getRelatedTarget(event.target);\n    }\n    return _addMouseData(event);\n  };\n  /**\n * Get a relatedTarget from the target's `data-clipboard-target` attribute\n * @private\n */\n  var _getRelatedTarget = function(targetEl) {\n    var relatedTargetId = targetEl && targetEl.getAttribute && targetEl.getAttribute(\"data-clipboard-target\");\n    return relatedTargetId ? _document.getElementById(relatedTargetId) : null;\n  };\n  /**\n * Add element and position data to `MouseEvent` instances\n * @private\n */\n  var _addMouseData = function(event) {\n    if (event && /^_(?:click|mouse(?:over|out|down|up|move))$/.test(event.type)) {\n      var srcElement = event.target;\n      var fromElement = event.type === \"_mouseover\" && event.relatedTarget ? event.relatedTarget : undefined;\n      var toElement = event.type === \"_mouseout\" && event.relatedTarget ? event.relatedTarget : undefined;\n      var pos = _getElementPosition(srcElement);\n      var screenLeft = _window.screenLeft || _window.screenX || 0;\n      var screenTop = _window.screenTop || _window.screenY || 0;\n      var scrollLeft = _document.body.scrollLeft + _document.documentElement.scrollLeft;\n      var scrollTop = _document.body.scrollTop + _document.documentElement.scrollTop;\n      var pageX = pos.left + (typeof event._stageX === \"number\" ? event._stageX : 0);\n      var pageY = pos.top + (typeof event._stageY === \"number\" ? event._stageY : 0);\n      var clientX = pageX - scrollLeft;\n      var clientY = pageY - scrollTop;\n      var screenX = screenLeft + clientX;\n      var screenY = screenTop + clientY;\n      var moveX = typeof event.movementX === \"number\" ? event.movementX : 0;\n      var moveY = typeof event.movementY === \"number\" ? event.movementY : 0;\n      delete event._stageX;\n      delete event._stageY;\n      _extend(event, {\n        srcElement: srcElement,\n        fromElement: fromElement,\n        toElement: toElement,\n        screenX: screenX,\n        screenY: screenY,\n        pageX: pageX,\n        pageY: pageY,\n        clientX: clientX,\n        clientY: clientY,\n        x: clientX,\n        y: clientY,\n        movementX: moveX,\n        movementY: moveY,\n        offsetX: 0,\n        offsetY: 0,\n        layerX: 0,\n        layerY: 0\n      });\n    }\n    return event;\n  };\n  /**\n * Determine if an event's registered handlers should be execute synchronously or asynchronously.\n *\n * @returns {boolean}\n * @private\n */\n  var _shouldPerformAsync = function(event) {\n    var eventType = event && typeof event.type === \"string\" && event.type || \"\";\n    return !/^(?:(?:before)?copy|destroy)$/.test(eventType);\n  };\n  /**\n * Control if a callback should be executed asynchronously or not.\n *\n * @returns `undefined`\n * @private\n */\n  var _dispatchCallback = function(func, context, args, async) {\n    if (async) {\n      _setTimeout(function() {\n        func.apply(context, args);\n      }, 0);\n    } else {\n      func.apply(context, args);\n    }\n  };\n  /**\n * Handle the actual dispatching of events to client instances.\n *\n * @returns `undefined`\n * @private\n */\n  var _dispatchCallbacks = function(event) {\n    if (!(typeof event === \"object\" && event && event.type)) {\n      return;\n    }\n    var async = _shouldPerformAsync(event);\n    var wildcardTypeHandlers = _handlers[\"*\"] || [];\n    var specificTypeHandlers = _handlers[event.type] || [];\n    var handlers = wildcardTypeHandlers.concat(specificTypeHandlers);\n    if (handlers && handlers.length) {\n      var i, len, func, context, eventCopy, originalContext = this;\n      for (i = 0, len = handlers.length; i < len; i++) {\n        func = handlers[i];\n        context = originalContext;\n        if (typeof func === \"string\" && typeof _window[func] === \"function\") {\n          func = _window[func];\n        }\n        if (typeof func === \"object\" && func && typeof func.handleEvent === \"function\") {\n          context = func;\n          func = func.handleEvent;\n        }\n        if (typeof func === \"function\") {\n          eventCopy = _extend({}, event);\n          _dispatchCallback(func, context, [ eventCopy ], async);\n        }\n      }\n    }\n    return this;\n  };\n  /**\n * Check an `error` event's `name` property to see if Flash has\n * already loaded, which rules out possible `iframe` sandboxing.\n * @private\n */\n  var _getSandboxStatusFromErrorEvent = function(event) {\n    var isSandboxed = null;\n    if (_pageIsFramed === false || event && event.type === \"error\" && event.name && _errorsThatOnlyOccurAfterFlashLoads.indexOf(event.name) !== -1) {\n      isSandboxed = false;\n    }\n    return isSandboxed;\n  };\n  /**\n * Preprocess any special behaviors, reactions, or state changes after receiving this event.\n * Executes only once per event emitted, NOT once per client.\n * @private\n */\n  var _preprocessEvent = function(event) {\n    var element = event.target || _currentElement || null;\n    var sourceIsSwf = event._source === \"swf\";\n    delete event._source;\n    switch (event.type) {\n     case \"error\":\n      var isSandboxed = event.name === \"flash-sandboxed\" || _getSandboxStatusFromErrorEvent(event);\n      if (typeof isSandboxed === \"boolean\") {\n        _flashState.sandboxed = isSandboxed;\n      }\n      if (event.name === \"browser-unsupported\") {\n        _extend(_flashState, {\n          disabled: false,\n          outdated: false,\n          unavailable: false,\n          degraded: false,\n          deactivated: false,\n          overdue: false,\n          ready: false\n        });\n      } else if (_flashStateErrorNames.indexOf(event.name) !== -1) {\n        _extend(_flashState, {\n          disabled: event.name === \"flash-disabled\",\n          outdated: event.name === \"flash-outdated\",\n          insecure: event.name === \"flash-insecure\",\n          unavailable: event.name === \"flash-unavailable\",\n          degraded: event.name === \"flash-degraded\",\n          deactivated: event.name === \"flash-deactivated\",\n          overdue: event.name === \"flash-overdue\",\n          ready: false\n        });\n      } else if (event.name === \"version-mismatch\") {\n        _zcSwfVersion = event.swfVersion;\n        _extend(_flashState, {\n          disabled: false,\n          outdated: false,\n          insecure: false,\n          unavailable: false,\n          degraded: false,\n          deactivated: false,\n          overdue: false,\n          ready: false\n        });\n      }\n      _clearTimeoutsAndPolling();\n      break;\n\n     case \"ready\":\n      _zcSwfVersion = event.swfVersion;\n      var wasDeactivated = _flashState.deactivated === true;\n      _extend(_flashState, {\n        sandboxed: false,\n        disabled: false,\n        outdated: false,\n        insecure: false,\n        unavailable: false,\n        degraded: false,\n        deactivated: false,\n        overdue: wasDeactivated,\n        ready: !wasDeactivated\n      });\n      _clearTimeoutsAndPolling();\n      break;\n\n     case \"beforecopy\":\n      _copyTarget = element;\n      break;\n\n     case \"copy\":\n      var textContent, htmlContent, targetEl = event.relatedTarget;\n      if (!(_clipData[\"text/html\"] || _clipData[\"text/plain\"]) && targetEl && (htmlContent = targetEl.value || targetEl.outerHTML || targetEl.innerHTML) && (textContent = targetEl.value || targetEl.textContent || targetEl.innerText)) {\n        event.clipboardData.clearData();\n        event.clipboardData.setData(\"text/plain\", textContent);\n        if (htmlContent !== textContent) {\n          event.clipboardData.setData(\"text/html\", htmlContent);\n        }\n      } else if (!_clipData[\"text/plain\"] && event.target && (textContent = event.target.getAttribute(\"data-clipboard-text\"))) {\n        event.clipboardData.clearData();\n        event.clipboardData.setData(\"text/plain\", textContent);\n      }\n      break;\n\n     case \"aftercopy\":\n      _queueEmitClipboardErrors(event);\n      ZeroClipboard.clearData();\n      if (element && element !== _safeActiveElement() && element.focus) {\n        element.focus();\n      }\n      break;\n\n     case \"_mouseover\":\n      ZeroClipboard.focus(element);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        if (element && element !== event.relatedTarget && !_containedBy(event.relatedTarget, element)) {\n          _fireMouseEvent(_extend({}, event, {\n            type: \"mouseenter\",\n            bubbles: false,\n            cancelable: false\n          }));\n        }\n        _fireMouseEvent(_extend({}, event, {\n          type: \"mouseover\"\n        }));\n      }\n      break;\n\n     case \"_mouseout\":\n      ZeroClipboard.blur();\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        if (element && element !== event.relatedTarget && !_containedBy(event.relatedTarget, element)) {\n          _fireMouseEvent(_extend({}, event, {\n            type: \"mouseleave\",\n            bubbles: false,\n            cancelable: false\n          }));\n        }\n        _fireMouseEvent(_extend({}, event, {\n          type: \"mouseout\"\n        }));\n      }\n      break;\n\n     case \"_mousedown\":\n      _addClass(element, _globalConfig.activeClass);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_mouseup\":\n      _removeClass(element, _globalConfig.activeClass);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_click\":\n      _copyTarget = null;\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_mousemove\":\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n    }\n    if (/^_(?:click|mouse(?:over|out|down|up|move))$/.test(event.type)) {\n      return true;\n    }\n  };\n  /**\n * Check an \"aftercopy\" event for clipboard errors and emit a corresponding \"error\" event.\n * @private\n */\n  var _queueEmitClipboardErrors = function(aftercopyEvent) {\n    if (aftercopyEvent.errors && aftercopyEvent.errors.length > 0) {\n      var errorEvent = _deepCopy(aftercopyEvent);\n      _extend(errorEvent, {\n        type: \"error\",\n        name: \"clipboard-error\"\n      });\n      delete errorEvent.success;\n      _setTimeout(function() {\n        ZeroClipboard.emit(errorEvent);\n      }, 0);\n    }\n  };\n  /**\n * Dispatch a synthetic MouseEvent.\n *\n * @returns `undefined`\n * @private\n */\n  var _fireMouseEvent = function(event) {\n    if (!(event && typeof event.type === \"string\" && event)) {\n      return;\n    }\n    var e, target = event.target || null, doc = target && target.ownerDocument || _document, defaults = {\n      view: doc.defaultView || _window,\n      canBubble: true,\n      cancelable: true,\n      detail: event.type === \"click\" ? 1 : 0,\n      button: typeof event.which === \"number\" ? event.which - 1 : typeof event.button === \"number\" ? event.button : doc.createEvent ? 0 : 1\n    }, args = _extend(defaults, event);\n    if (!target) {\n      return;\n    }\n    if (doc.createEvent && target.dispatchEvent) {\n      args = [ args.type, args.canBubble, args.cancelable, args.view, args.detail, args.screenX, args.screenY, args.clientX, args.clientY, args.ctrlKey, args.altKey, args.shiftKey, args.metaKey, args.button, args.relatedTarget ];\n      e = doc.createEvent(\"MouseEvents\");\n      if (e.initMouseEvent) {\n        e.initMouseEvent.apply(e, args);\n        e._source = \"js\";\n        target.dispatchEvent(e);\n      }\n    }\n  };\n  /**\n * Continuously poll the DOM until either:\n *  (a) the fallback content becomes visible, or\n *  (b) we receive an event from SWF (handled elsewhere)\n *\n * IMPORTANT:\n * This is NOT a necessary check but it can result in significantly faster\n * detection of bad `swfPath` configuration and/or network/server issues [in\n * supported browsers] than waiting for the entire `flashLoadTimeout` duration\n * to elapse before detecting that the SWF cannot be loaded. The detection\n * duration can be anywhere from 10-30 times faster [in supported browsers] by\n * using this approach.\n *\n * @returns `undefined`\n * @private\n */\n  var _watchForSwfFallbackContent = function() {\n    var maxWait = _globalConfig.flashLoadTimeout;\n    if (typeof maxWait === \"number\" && maxWait >= 0) {\n      var pollWait = Math.min(1e3, maxWait / 10);\n      var fallbackContentId = _globalConfig.swfObjectId + \"_fallbackContent\";\n      _swfFallbackCheckInterval = _setInterval(function() {\n        var el = _document.getElementById(fallbackContentId);\n        if (_isElementVisible(el)) {\n          _clearTimeoutsAndPolling();\n          _flashState.deactivated = null;\n          ZeroClipboard.emit({\n            type: \"error\",\n            name: \"swf-not-found\"\n          });\n        }\n      }, pollWait);\n    }\n  };\n  /**\n * Create the HTML bridge element to embed the Flash object into.\n * @private\n */\n  var _createHtmlBridge = function() {\n    var container = _document.createElement(\"div\");\n    container.id = _globalConfig.containerId;\n    container.className = _globalConfig.containerClass;\n    container.style.position = \"absolute\";\n    container.style.left = \"0px\";\n    container.style.top = \"-9999px\";\n    container.style.width = \"1px\";\n    container.style.height = \"1px\";\n    container.style.zIndex = \"\" + _getSafeZIndex(_globalConfig.zIndex);\n    return container;\n  };\n  /**\n * Get the HTML element container that wraps the Flash bridge object/element.\n * @private\n */\n  var _getHtmlBridge = function(flashBridge) {\n    var htmlBridge = flashBridge && flashBridge.parentNode;\n    while (htmlBridge && htmlBridge.nodeName === \"OBJECT\" && htmlBridge.parentNode) {\n      htmlBridge = htmlBridge.parentNode;\n    }\n    return htmlBridge || null;\n  };\n  /**\n *\n * @private\n */\n  var _escapeXmlValue = function(val) {\n    if (typeof val !== \"string\" || !val) {\n      return val;\n    }\n    return val.replace(/[\"&'<>]/g, function(chr) {\n      switch (chr) {\n       case '\"':\n        return \"&quot;\";\n\n       case \"&\":\n        return \"&amp;\";\n\n       case \"'\":\n        return \"&apos;\";\n\n       case \"<\":\n        return \"&lt;\";\n\n       case \">\":\n        return \"&gt;\";\n\n       default:\n        return chr;\n      }\n    });\n  };\n  /**\n * Create the SWF object.\n *\n * @returns The SWF object reference.\n * @private\n */\n  var _embedSwf = function() {\n    var len, flashBridge = _flashState.bridge, container = _getHtmlBridge(flashBridge);\n    if (!flashBridge) {\n      var allowScriptAccess = _determineScriptAccess(_window.location.host, _globalConfig);\n      var allowNetworking = allowScriptAccess === \"never\" ? \"none\" : \"all\";\n      var flashvars = _vars(_extend({\n        jsVersion: ZeroClipboard.version\n      }, _globalConfig));\n      var swfUrl = _globalConfig.swfPath + _cacheBust(_globalConfig.swfPath, _globalConfig);\n      if (_pageIsXhtml) {\n        swfUrl = _escapeXmlValue(swfUrl);\n      }\n      container = _createHtmlBridge();\n      var divToBeReplaced = _document.createElement(\"div\");\n      container.appendChild(divToBeReplaced);\n      _document.body.appendChild(container);\n      var tmpDiv = _document.createElement(\"div\");\n      var usingActiveX = _flashState.pluginType === \"activex\";\n      tmpDiv.innerHTML = '<object id=\"' + _globalConfig.swfObjectId + '\" name=\"' + _globalConfig.swfObjectId + '\" ' + 'width=\"100%\" height=\"100%\" ' + (usingActiveX ? 'classid=\"clsid:d27cdb6e-ae6d-11cf-96b8-444553540000\"' : 'type=\"application/x-shockwave-flash\" data=\"' + swfUrl + '\"') + \">\" + (usingActiveX ? '<param name=\"movie\" value=\"' + swfUrl + '\"/>' : \"\") + '<param name=\"allowScriptAccess\" value=\"' + allowScriptAccess + '\"/>' + '<param name=\"allowNetworking\" value=\"' + allowNetworking + '\"/>' + '<param name=\"menu\" value=\"false\"/>' + '<param name=\"wmode\" value=\"transparent\"/>' + '<param name=\"flashvars\" value=\"' + flashvars + '\"/>' + '<div id=\"' + _globalConfig.swfObjectId + '_fallbackContent\">&nbsp;</div>' + \"</object>\";\n      flashBridge = tmpDiv.firstChild;\n      tmpDiv = null;\n      _unwrap(flashBridge).ZeroClipboard = ZeroClipboard;\n      container.replaceChild(flashBridge, divToBeReplaced);\n      _watchForSwfFallbackContent();\n    }\n    if (!flashBridge) {\n      flashBridge = _document[_globalConfig.swfObjectId];\n      if (flashBridge && (len = flashBridge.length)) {\n        flashBridge = flashBridge[len - 1];\n      }\n      if (!flashBridge && container) {\n        flashBridge = container.firstChild;\n      }\n    }\n    _flashState.bridge = flashBridge || null;\n    return flashBridge;\n  };\n  /**\n * Destroy the SWF object.\n * @private\n */\n  var _unembedSwf = function() {\n    var flashBridge = _flashState.bridge;\n    if (flashBridge) {\n      var htmlBridge = _getHtmlBridge(flashBridge);\n      if (htmlBridge) {\n        if (_flashState.pluginType === \"activex\" && \"readyState\" in flashBridge) {\n          flashBridge.style.display = \"none\";\n          (function removeSwfFromIE() {\n            if (flashBridge.readyState === 4) {\n              for (var prop in flashBridge) {\n                if (typeof flashBridge[prop] === \"function\") {\n                  flashBridge[prop] = null;\n                }\n              }\n              if (flashBridge.parentNode) {\n                flashBridge.parentNode.removeChild(flashBridge);\n              }\n              if (htmlBridge.parentNode) {\n                htmlBridge.parentNode.removeChild(htmlBridge);\n              }\n            } else {\n              _setTimeout(removeSwfFromIE, 10);\n            }\n          })();\n        } else {\n          if (flashBridge.parentNode) {\n            flashBridge.parentNode.removeChild(flashBridge);\n          }\n          if (htmlBridge.parentNode) {\n            htmlBridge.parentNode.removeChild(htmlBridge);\n          }\n        }\n      }\n      _clearTimeoutsAndPolling();\n      _flashState.ready = null;\n      _flashState.bridge = null;\n      _flashState.deactivated = null;\n      _flashState.insecure = null;\n      _zcSwfVersion = undefined;\n    }\n  };\n  /**\n * Map the data format names of the \"clipData\" to Flash-friendly names.\n *\n * @returns A new transformed object.\n * @private\n */\n  var _mapClipDataToFlash = function(clipData) {\n    var newClipData = {}, formatMap = {};\n    if (!(typeof clipData === \"object\" && clipData)) {\n      return;\n    }\n    for (var dataFormat in clipData) {\n      if (dataFormat && _hasOwn.call(clipData, dataFormat) && typeof clipData[dataFormat] === \"string\" && clipData[dataFormat]) {\n        switch (dataFormat.toLowerCase()) {\n         case \"text/plain\":\n         case \"text\":\n         case \"air:text\":\n         case \"flash:text\":\n          newClipData.text = clipData[dataFormat];\n          formatMap.text = dataFormat;\n          break;\n\n         case \"text/html\":\n         case \"html\":\n         case \"air:html\":\n         case \"flash:html\":\n          newClipData.html = clipData[dataFormat];\n          formatMap.html = dataFormat;\n          break;\n\n         case \"application/rtf\":\n         case \"text/rtf\":\n         case \"rtf\":\n         case \"richtext\":\n         case \"air:rtf\":\n         case \"flash:rtf\":\n          newClipData.rtf = clipData[dataFormat];\n          formatMap.rtf = dataFormat;\n          break;\n\n         default:\n          break;\n        }\n      }\n    }\n    return {\n      data: newClipData,\n      formatMap: formatMap\n    };\n  };\n  /**\n * Map the data format names from Flash-friendly names back to their original \"clipData\" names (via a format mapping).\n *\n * @returns A new transformed object.\n * @private\n */\n  var _mapClipResultsFromFlash = function(clipResults, formatMap) {\n    if (!(typeof clipResults === \"object\" && clipResults && typeof formatMap === \"object\" && formatMap)) {\n      return clipResults;\n    }\n    var newResults = {};\n    for (var prop in clipResults) {\n      if (_hasOwn.call(clipResults, prop)) {\n        if (prop === \"errors\") {\n          newResults[prop] = clipResults[prop] ? clipResults[prop].slice() : [];\n          for (var i = 0, len = newResults[prop].length; i < len; i++) {\n            newResults[prop][i].format = formatMap[newResults[prop][i].format];\n          }\n        } else if (prop !== \"success\" && prop !== \"data\") {\n          newResults[prop] = clipResults[prop];\n        } else {\n          newResults[prop] = {};\n          var tmpHash = clipResults[prop];\n          for (var dataFormat in tmpHash) {\n            if (dataFormat && _hasOwn.call(tmpHash, dataFormat) && _hasOwn.call(formatMap, dataFormat)) {\n              newResults[prop][formatMap[dataFormat]] = tmpHash[dataFormat];\n            }\n          }\n        }\n      }\n    }\n    return newResults;\n  };\n  /**\n * Will look at a path, and will create a \"?noCache={time}\" or \"&noCache={time}\"\n * query param string to return. Does NOT append that string to the original path.\n * This is useful because ExternalInterface often breaks when a Flash SWF is cached.\n *\n * @returns The `noCache` query param with necessary \"?\"/\"&\" prefix.\n * @private\n */\n  var _cacheBust = function(path, options) {\n    var cacheBust = options == null || options && options.cacheBust === true;\n    if (cacheBust) {\n      return (path.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"noCache=\" + _now();\n    } else {\n      return \"\";\n    }\n  };\n  /**\n * Creates a query string for the FlashVars param.\n * Does NOT include the cache-busting query param.\n *\n * @returns FlashVars query string\n * @private\n */\n  var _vars = function(options) {\n    var i, len, domain, domains, str = \"\", trustedOriginsExpanded = [];\n    if (options.trustedDomains) {\n      if (typeof options.trustedDomains === \"string\") {\n        domains = [ options.trustedDomains ];\n      } else if (typeof options.trustedDomains === \"object\" && \"length\" in options.trustedDomains) {\n        domains = options.trustedDomains;\n      }\n    }\n    if (domains && domains.length) {\n      for (i = 0, len = domains.length; i < len; i++) {\n        if (_hasOwn.call(domains, i) && domains[i] && typeof domains[i] === \"string\") {\n          domain = _extractDomain(domains[i]);\n          if (!domain) {\n            continue;\n          }\n          if (domain === \"*\") {\n            trustedOriginsExpanded.length = 0;\n            trustedOriginsExpanded.push(domain);\n            break;\n          }\n          trustedOriginsExpanded.push.apply(trustedOriginsExpanded, [ domain, \"//\" + domain, _window.location.protocol + \"//\" + domain ]);\n        }\n      }\n    }\n    if (trustedOriginsExpanded.length) {\n      str += \"trustedOrigins=\" + _encodeURIComponent(trustedOriginsExpanded.join(\",\"));\n    }\n    if (options.forceEnhancedClipboard === true) {\n      str += (str ? \"&\" : \"\") + \"forceEnhancedClipboard=true\";\n    }\n    if (typeof options.swfObjectId === \"string\" && options.swfObjectId) {\n      str += (str ? \"&\" : \"\") + \"swfObjectId=\" + _encodeURIComponent(options.swfObjectId);\n    }\n    if (typeof options.jsVersion === \"string\" && options.jsVersion) {\n      str += (str ? \"&\" : \"\") + \"jsVersion=\" + _encodeURIComponent(options.jsVersion);\n    }\n    return str;\n  };\n  /**\n * Extract the domain (e.g. \"github.com\") from an origin (e.g. \"https://github.com\") or\n * URL (e.g. \"https://github.com/zeroclipboard/zeroclipboard/\").\n *\n * @returns the domain\n * @private\n */\n  var _extractDomain = function(originOrUrl) {\n    if (originOrUrl == null || originOrUrl === \"\") {\n      return null;\n    }\n    originOrUrl = originOrUrl.replace(/^\\s+|\\s+$/g, \"\");\n    if (originOrUrl === \"\") {\n      return null;\n    }\n    var protocolIndex = originOrUrl.indexOf(\"//\");\n    originOrUrl = protocolIndex === -1 ? originOrUrl : originOrUrl.slice(protocolIndex + 2);\n    var pathIndex = originOrUrl.indexOf(\"/\");\n    originOrUrl = pathIndex === -1 ? originOrUrl : protocolIndex === -1 || pathIndex === 0 ? null : originOrUrl.slice(0, pathIndex);\n    if (originOrUrl && originOrUrl.slice(-4).toLowerCase() === \".swf\") {\n      return null;\n    }\n    return originOrUrl || null;\n  };\n  /**\n * Set `allowScriptAccess` based on `trustedDomains` and `window.location.host` vs. `swfPath`.\n *\n * @returns The appropriate script access level.\n * @private\n */\n  var _determineScriptAccess = function() {\n    var _extractAllDomains = function(origins) {\n      var i, len, tmp, resultsArray = [];\n      if (typeof origins === \"string\") {\n        origins = [ origins ];\n      }\n      if (!(typeof origins === \"object\" && origins && typeof origins.length === \"number\")) {\n        return resultsArray;\n      }\n      for (i = 0, len = origins.length; i < len; i++) {\n        if (_hasOwn.call(origins, i) && (tmp = _extractDomain(origins[i]))) {\n          if (tmp === \"*\") {\n            resultsArray.length = 0;\n            resultsArray.push(\"*\");\n            break;\n          }\n          if (resultsArray.indexOf(tmp) === -1) {\n            resultsArray.push(tmp);\n          }\n        }\n      }\n      return resultsArray;\n    };\n    return function(currentDomain, configOptions) {\n      var swfDomain = _extractDomain(configOptions.swfPath);\n      if (swfDomain === null) {\n        swfDomain = currentDomain;\n      }\n      var trustedDomains = _extractAllDomains(configOptions.trustedDomains);\n      var len = trustedDomains.length;\n      if (len > 0) {\n        if (len === 1 && trustedDomains[0] === \"*\") {\n          return \"always\";\n        }\n        if (trustedDomains.indexOf(currentDomain) !== -1) {\n          if (len === 1 && currentDomain === swfDomain) {\n            return \"sameDomain\";\n          }\n          return \"always\";\n        }\n      }\n      return \"never\";\n    };\n  }();\n  /**\n * Get the currently active/focused DOM element.\n *\n * @returns the currently active/focused element, or `null`\n * @private\n */\n  var _safeActiveElement = function() {\n    try {\n      return _document.activeElement;\n    } catch (err) {\n      return null;\n    }\n  };\n  /**\n * Add a class to an element, if it doesn't already have it.\n *\n * @returns The element, with its new class added.\n * @private\n */\n  var _addClass = function(element, value) {\n    var c, cl, className, classNames = [];\n    if (typeof value === \"string\" && value) {\n      classNames = value.split(/\\s+/);\n    }\n    if (element && element.nodeType === 1 && classNames.length > 0) {\n      className = (\" \" + (element.className || \"\") + \" \").replace(/[\\t\\r\\n\\f]/g, \" \");\n      for (c = 0, cl = classNames.length; c < cl; c++) {\n        if (className.indexOf(\" \" + classNames[c] + \" \") === -1) {\n          className += classNames[c] + \" \";\n        }\n      }\n      className = className.replace(/^\\s+|\\s+$/g, \"\");\n      if (className !== element.className) {\n        element.className = className;\n      }\n    }\n    return element;\n  };\n  /**\n * Remove a class from an element, if it has it.\n *\n * @returns The element, with its class removed.\n * @private\n */\n  var _removeClass = function(element, value) {\n    var c, cl, className, classNames = [];\n    if (typeof value === \"string\" && value) {\n      classNames = value.split(/\\s+/);\n    }\n    if (element && element.nodeType === 1 && classNames.length > 0) {\n      if (element.className) {\n        className = (\" \" + element.className + \" \").replace(/[\\t\\r\\n\\f]/g, \" \");\n        for (c = 0, cl = classNames.length; c < cl; c++) {\n          className = className.replace(\" \" + classNames[c] + \" \", \" \");\n        }\n        className = className.replace(/^\\s+|\\s+$/g, \"\");\n        if (className !== element.className) {\n          element.className = className;\n        }\n      }\n    }\n    return element;\n  };\n  /**\n * Attempt to interpret the element's CSS styling. If `prop` is `\"cursor\"`,\n * then we assume that it should be a hand (\"pointer\") cursor if the element\n * is an anchor element (\"a\" tag).\n *\n * @returns The computed style property.\n * @private\n */\n  var _getStyle = function(el, prop) {\n    var value = _getComputedStyle(el, null).getPropertyValue(prop);\n    if (prop === \"cursor\") {\n      if (!value || value === \"auto\") {\n        if (el.nodeName === \"A\") {\n          return \"pointer\";\n        }\n      }\n    }\n    return value;\n  };\n  /**\n * Get the absolutely positioned coordinates of a DOM element.\n *\n * @returns Object containing the element's position, width, and height.\n * @private\n */\n  var _getElementPosition = function(el) {\n    var pos = {\n      left: 0,\n      top: 0,\n      width: 0,\n      height: 0\n    };\n    if (el.getBoundingClientRect) {\n      var elRect = el.getBoundingClientRect();\n      var pageXOffset = _window.pageXOffset;\n      var pageYOffset = _window.pageYOffset;\n      var leftBorderWidth = _document.documentElement.clientLeft || 0;\n      var topBorderWidth = _document.documentElement.clientTop || 0;\n      var leftBodyOffset = 0;\n      var topBodyOffset = 0;\n      if (_getStyle(_document.body, \"position\") === \"relative\") {\n        var bodyRect = _document.body.getBoundingClientRect();\n        var htmlRect = _document.documentElement.getBoundingClientRect();\n        leftBodyOffset = bodyRect.left - htmlRect.left || 0;\n        topBodyOffset = bodyRect.top - htmlRect.top || 0;\n      }\n      pos.left = elRect.left + pageXOffset - leftBorderWidth - leftBodyOffset;\n      pos.top = elRect.top + pageYOffset - topBorderWidth - topBodyOffset;\n      pos.width = \"width\" in elRect ? elRect.width : elRect.right - elRect.left;\n      pos.height = \"height\" in elRect ? elRect.height : elRect.bottom - elRect.top;\n    }\n    return pos;\n  };\n  /**\n * Determine is an element is visible somewhere within the document (page).\n *\n * @returns Boolean\n * @private\n */\n  var _isElementVisible = function(el) {\n    if (!el) {\n      return false;\n    }\n    var styles = _getComputedStyle(el, null);\n    if (!styles) {\n      return false;\n    }\n    var hasCssHeight = _parseFloat(styles.height) > 0;\n    var hasCssWidth = _parseFloat(styles.width) > 0;\n    var hasCssTop = _parseFloat(styles.top) >= 0;\n    var hasCssLeft = _parseFloat(styles.left) >= 0;\n    var cssKnows = hasCssHeight && hasCssWidth && hasCssTop && hasCssLeft;\n    var rect = cssKnows ? null : _getElementPosition(el);\n    var isVisible = styles.display !== \"none\" && styles.visibility !== \"collapse\" && (cssKnows || !!rect && (hasCssHeight || rect.height > 0) && (hasCssWidth || rect.width > 0) && (hasCssTop || rect.top >= 0) && (hasCssLeft || rect.left >= 0));\n    return isVisible;\n  };\n  /**\n * Clear all existing timeouts and interval polling delegates.\n *\n * @returns `undefined`\n * @private\n */\n  var _clearTimeoutsAndPolling = function() {\n    _clearTimeout(_flashCheckTimeout);\n    _flashCheckTimeout = 0;\n    _clearInterval(_swfFallbackCheckInterval);\n    _swfFallbackCheckInterval = 0;\n  };\n  /**\n * Reposition the Flash object to cover the currently activated element.\n *\n * @returns `undefined`\n * @private\n */\n  var _reposition = function() {\n    var htmlBridge;\n    if (_currentElement && (htmlBridge = _getHtmlBridge(_flashState.bridge))) {\n      var pos = _getElementPosition(_currentElement);\n      _extend(htmlBridge.style, {\n        width: pos.width + \"px\",\n        height: pos.height + \"px\",\n        top: pos.top + \"px\",\n        left: pos.left + \"px\",\n        zIndex: \"\" + _getSafeZIndex(_globalConfig.zIndex)\n      });\n    }\n  };\n  /**\n * Sends a signal to the Flash object to display the hand cursor if `true`.\n *\n * @returns `undefined`\n * @private\n */\n  var _setHandCursor = function(enabled) {\n    if (_flashState.ready === true) {\n      if (_flashState.bridge && typeof _flashState.bridge.setHandCursor === \"function\") {\n        _flashState.bridge.setHandCursor(enabled);\n      } else {\n        _flashState.ready = false;\n      }\n    }\n  };\n  /**\n * Get a safe value for `zIndex`\n *\n * @returns an integer, or \"auto\"\n * @private\n */\n  var _getSafeZIndex = function(val) {\n    if (/^(?:auto|inherit)$/.test(val)) {\n      return val;\n    }\n    var zIndex;\n    if (typeof val === \"number\" && !_isNaN(val)) {\n      zIndex = val;\n    } else if (typeof val === \"string\") {\n      zIndex = _getSafeZIndex(_parseInt(val, 10));\n    }\n    return typeof zIndex === \"number\" ? zIndex : \"auto\";\n  };\n  /**\n * Ensure OS-compliant line endings, i.e. \"\\r\\n\" on Windows, \"\\n\" elsewhere\n *\n * @returns string\n * @private\n */\n  var _fixLineEndings = function(content) {\n    var replaceRegex = /(\\r\\n|\\r|\\n)/g;\n    if (typeof content === \"string\" && _globalConfig.fixLineEndings === true) {\n      if (_isWindows()) {\n        if (/((^|[^\\r])\\n|\\r([^\\n]|$))/.test(content)) {\n          content = content.replace(replaceRegex, \"\\r\\n\");\n        }\n      } else if (/\\r/.test(content)) {\n        content = content.replace(replaceRegex, \"\\n\");\n      }\n    }\n    return content;\n  };\n  /**\n * Attempt to detect if ZeroClipboard is executing inside of a sandboxed iframe.\n * If it is, Flash Player cannot be used, so ZeroClipboard is dead in the water.\n *\n * @see {@link http://lists.w3.org/Archives/Public/public-whatwg-archive/2014Dec/0002.html}\n * @see {@link https://github.com/zeroclipboard/zeroclipboard/issues/511}\n * @see {@link http://zeroclipboard.github.io/test-iframes.html}\n *\n * @returns `true` (is sandboxed), `false` (is not sandboxed), or `null` (uncertain)\n * @private\n */\n  var _detectSandbox = function(doNotReassessFlashSupport) {\n    var effectiveScriptOrigin, frame, frameError, previousState = _flashState.sandboxed, isSandboxed = null;\n    doNotReassessFlashSupport = doNotReassessFlashSupport === true;\n    if (_pageIsFramed === false) {\n      isSandboxed = false;\n    } else {\n      try {\n        frame = window.frameElement || null;\n      } catch (e) {\n        frameError = {\n          name: e.name,\n          message: e.message\n        };\n      }\n      if (frame && frame.nodeType === 1 && frame.nodeName === \"IFRAME\") {\n        try {\n          isSandboxed = frame.hasAttribute(\"sandbox\");\n        } catch (e) {\n          isSandboxed = null;\n        }\n      } else {\n        try {\n          effectiveScriptOrigin = document.domain || null;\n        } catch (e) {\n          effectiveScriptOrigin = null;\n        }\n        if (effectiveScriptOrigin === null || frameError && frameError.name === \"SecurityError\" && /(^|[\\s\\(\\[@])sandbox(es|ed|ing|[\\s\\.,!\\)\\]@]|$)/.test(frameError.message.toLowerCase())) {\n          isSandboxed = true;\n        }\n      }\n    }\n    _flashState.sandboxed = isSandboxed;\n    if (previousState !== isSandboxed && !doNotReassessFlashSupport) {\n      _detectFlashSupport(_ActiveXObject);\n    }\n    return isSandboxed;\n  };\n  /**\n * Detect the Flash Player status, version, and plugin type.\n *\n * @see {@link https://code.google.com/p/doctype-mirror/wiki/ArticleDetectFlash#The_code}\n * @see {@link http://stackoverflow.com/questions/12866060/detecting-pepper-ppapi-flash-with-javascript}\n *\n * @returns `undefined`\n * @private\n */\n  var _detectFlashSupport = function(ActiveXObject) {\n    var plugin, ax, mimeType, hasFlash = false, isActiveX = false, isPPAPI = false, flashVersion = \"\";\n    /**\n   * Derived from Apple's suggested sniffer.\n   * @param {String} desc e.g. \"Shockwave Flash 7.0 r61\"\n   * @returns {String} \"7.0.61\"\n   * @private\n   */\n    function parseFlashVersion(desc) {\n      var matches = desc.match(/[\\d]+/g);\n      matches.length = 3;\n      return matches.join(\".\");\n    }\n    function isPepperFlash(flashPlayerFileName) {\n      return !!flashPlayerFileName && (flashPlayerFileName = flashPlayerFileName.toLowerCase()) && (/^(pepflashplayer\\.dll|libpepflashplayer\\.so|pepperflashplayer\\.plugin)$/.test(flashPlayerFileName) || flashPlayerFileName.slice(-13) === \"chrome.plugin\");\n    }\n    function inspectPlugin(plugin) {\n      if (plugin) {\n        hasFlash = true;\n        if (plugin.version) {\n          flashVersion = parseFlashVersion(plugin.version);\n        }\n        if (!flashVersion && plugin.description) {\n          flashVersion = parseFlashVersion(plugin.description);\n        }\n        if (plugin.filename) {\n          isPPAPI = isPepperFlash(plugin.filename);\n        }\n      }\n    }\n    if (_navigator.plugins && _navigator.plugins.length) {\n      plugin = _navigator.plugins[\"Shockwave Flash\"];\n      inspectPlugin(plugin);\n      if (_navigator.plugins[\"Shockwave Flash 2.0\"]) {\n        hasFlash = true;\n        flashVersion = \"********\";\n      }\n    } else if (_navigator.mimeTypes && _navigator.mimeTypes.length) {\n      mimeType = _navigator.mimeTypes[\"application/x-shockwave-flash\"];\n      plugin = mimeType && mimeType.enabledPlugin;\n      inspectPlugin(plugin);\n    } else if (typeof ActiveXObject !== \"undefined\") {\n      isActiveX = true;\n      try {\n        ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash.7\");\n        hasFlash = true;\n        flashVersion = parseFlashVersion(ax.GetVariable(\"$version\"));\n      } catch (e1) {\n        try {\n          ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash.6\");\n          hasFlash = true;\n          flashVersion = \"6.0.21\";\n        } catch (e2) {\n          try {\n            ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash\");\n            hasFlash = true;\n            flashVersion = parseFlashVersion(ax.GetVariable(\"$version\"));\n          } catch (e3) {\n            isActiveX = false;\n          }\n        }\n      }\n    }\n    _flashState.disabled = hasFlash !== true;\n    _flashState.outdated = flashVersion && _parseFloat(flashVersion) < _parseFloat(_minimumFlashVersion);\n    _flashState.version = flashVersion || \"0.0.0\";\n    _flashState.pluginType = isPPAPI ? \"pepper\" : isActiveX ? \"activex\" : hasFlash ? \"netscape\" : \"unknown\";\n  };\n  /**\n * Invoke the Flash detection algorithms immediately upon inclusion so we're not waiting later.\n */\n  _detectFlashSupport(_ActiveXObject);\n  /**\n * Always assess the `sandboxed` state of the page at important Flash-related moments.\n */\n  _detectSandbox(true);\n  /**\n * A shell constructor for `ZeroClipboard` client instances.\n *\n * @constructor\n */\n  var ZeroClipboard = function() {\n    if (!(this instanceof ZeroClipboard)) {\n      return new ZeroClipboard();\n    }\n    if (typeof ZeroClipboard._createClient === \"function\") {\n      ZeroClipboard._createClient.apply(this, _args(arguments));\n    }\n  };\n  /**\n * The ZeroClipboard library's version number.\n *\n * @static\n * @readonly\n * @property {string}\n */\n  ZeroClipboard.version = \"2.4.0-beta.1\";\n  /**\n * Update or get a copy of the ZeroClipboard global configuration.\n * Returns a copy of the current/updated configuration.\n *\n * @returns Object\n * @static\n */\n  ZeroClipboard.config = function() {\n    return _config.apply(this, _args(arguments));\n  };\n  /**\n * Diagnostic method that describes the state of the browser, Flash Player, and ZeroClipboard.\n *\n * @returns Object\n * @static\n */\n  ZeroClipboard.state = function() {\n    return _state.apply(this, _args(arguments));\n  };\n  /**\n * Check if Flash is unusable for any reason: disabled, outdated, deactivated, etc.\n *\n * @returns Boolean\n * @static\n */\n  ZeroClipboard.isFlashUnusable = function() {\n    return _isFlashUnusable.apply(this, _args(arguments));\n  };\n  /**\n * Register an event listener.\n *\n * @returns `ZeroClipboard`\n * @static\n */\n  ZeroClipboard.on = function() {\n    return _on.apply(this, _args(arguments));\n  };\n  /**\n * Unregister an event listener.\n * If no `listener` function/object is provided, it will unregister all listeners for the provided `eventType`.\n * If no `eventType` is provided, it will unregister all listeners for every event type.\n *\n * @returns `ZeroClipboard`\n * @static\n */\n  ZeroClipboard.off = function() {\n    return _off.apply(this, _args(arguments));\n  };\n  /**\n * Retrieve event listeners for an `eventType`.\n * If no `eventType` is provided, it will retrieve all listeners for every event type.\n *\n * @returns array of listeners for the `eventType`; if no `eventType`, then a map/hash object of listeners for all event types; or `null`\n */\n  ZeroClipboard.handlers = function() {\n    return _listeners.apply(this, _args(arguments));\n  };\n  /**\n * Event emission receiver from the Flash object, forwarding to any registered JavaScript event listeners.\n *\n * @returns For the \"copy\" event, returns the Flash-friendly \"clipData\" object; otherwise `undefined`.\n * @static\n */\n  ZeroClipboard.emit = function() {\n    return _emit.apply(this, _args(arguments));\n  };\n  /**\n * Create and embed the Flash object.\n *\n * @returns The Flash object\n * @static\n */\n  ZeroClipboard.create = function() {\n    return _create.apply(this, _args(arguments));\n  };\n  /**\n * Self-destruct and clean up everything, including the embedded Flash object.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.destroy = function() {\n    return _destroy.apply(this, _args(arguments));\n  };\n  /**\n * Set the pending data for clipboard injection.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.setData = function() {\n    return _setData.apply(this, _args(arguments));\n  };\n  /**\n * Clear the pending data for clipboard injection.\n * If no `format` is provided, all pending data formats will be cleared.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.clearData = function() {\n    return _clearData.apply(this, _args(arguments));\n  };\n  /**\n * Get a copy of the pending data for clipboard injection.\n * If no `format` is provided, a copy of ALL pending data formats will be returned.\n *\n * @returns `String` or `Object`\n * @static\n */\n  ZeroClipboard.getData = function() {\n    return _getData.apply(this, _args(arguments));\n  };\n  /**\n * Sets the current HTML object that the Flash object should overlay. This will put the global\n * Flash object on top of the current element; depending on the setup, this may also set the\n * pending clipboard text data as well as the Flash object's wrapping element's title attribute\n * based on the underlying HTML element and ZeroClipboard configuration.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.focus = ZeroClipboard.activate = function() {\n    return _focus.apply(this, _args(arguments));\n  };\n  /**\n * Un-overlays the Flash object. This will put the global Flash object off-screen; depending on\n * the setup, this may also unset the Flash object's wrapping element's title attribute based on\n * the underlying HTML element and ZeroClipboard configuration.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.blur = ZeroClipboard.deactivate = function() {\n    return _blur.apply(this, _args(arguments));\n  };\n  /**\n * Returns the currently focused/\"activated\" HTML element that the Flash object is wrapping.\n *\n * @returns `HTMLElement` or `null`\n * @static\n */\n  ZeroClipboard.activeElement = function() {\n    return _activeElement.apply(this, _args(arguments));\n  };\n  if (typeof define === \"function\" && define.amd) {\n    define(function() {\n      return ZeroClipboard;\n    });\n  } else if (typeof module === \"object\" && module && typeof module.exports === \"object\" && module.exports) {\n    module.exports = ZeroClipboard;\n  } else {\n    window.ZeroClipboard = ZeroClipboard;\n  }\n})(function() {\n  return this || window;\n}());"]}