/**
 * @license
 * Video.js 7.20.2 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/main/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/main/LICENSE>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).videojs=t()}(this,function(){"use strict";for(var e,l="7.20.2",n={},s=function(e,t){return n[e]=n[e]||[],t&&(n[e]=n[e].concat(t)),n[e]},i=function(e,t){t=s(e).indexOf(t);return!(t<=-1)&&(n[e]=n[e].slice(),n[e].splice(t,1),!0)},c={prefixed:!0},t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror","fullscreen"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror","-webkit-full-screen"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror","-moz-full-screen"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError","-ms-fullscreen"]],r=t[0],o=0;o<t.length;o++)if(t[o][1]in document){e=t[o];break}if(e){for(var a=0;a<e.length;a++)c[r[a]]=e[a];c.prefixed=e[0]!==r[0]}var u=[],h=function(s,o){return function(e,t,n){var i,r=o.levels[t],t=new RegExp("^("+r+")$");"log"!==e&&n.unshift(e.toUpperCase()+":"),n.unshift(s+":"),u&&(u.push([].concat(n)),i=u.length-1e3,u.splice(0,0<i?i:0)),!window.console||(i=!(i=window.console[e])&&"debug"===e?window.console.info||window.console.log:i)&&r&&t.test(e)&&i[Array.isArray(n)?"apply":"call"](window.console,n)}};var d=function t(n){function i(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];s("log",r,t)}var r="info",s=h(n,i);return i.createLogger=function(e){return t(n+": "+e)},i.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:r},i.level=function(e){if("string"==typeof e){if(!i.levels.hasOwnProperty(e))throw new Error('"'+e+'" in not a valid log level');r=e}return r},(i.history=function(){return u?[].concat(u):[]}).filter=function(t){return(u||[]).filter(function(e){return new RegExp(".*"+t+".*").test(e[0])})},i.history.clear=function(){u&&(u.length=0)},i.history.disable=function(){null!==u&&(u.length=0,u=null)},i.history.enable=function(){null===u&&(u=[])},i.error=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s("error",r,t)},i.warn=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s("warn",r,t)},i.debug=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s("debug",r,t)},i}("VIDEOJS"),p=d.createLogger,f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function v(e,t){return e(t={exports:{}},t.exports),t.exports}var g=v(function(e){function t(){return e.exports=t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},t.apply(this,arguments)}e.exports=t}),m=Object.prototype.toString,_=function(e){return T(e)?Object.keys(e):[]};function y(t,n){_(t).forEach(function(e){return n(t[e],e)})}function b(n){for(var e=arguments.length,t=new Array(1<e?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return Object.assign?g.apply(void 0,[n].concat(t)):(t.forEach(function(e){e&&y(e,function(e,t){n[t]=e})}),n)}function T(e){return!!e&&"object"==typeof e}function k(e){return T(e)&&"[object Object]"===m.call(e)&&e.constructor===Object}function C(e,t){if(!e||!t)return"";if("function"!=typeof window.getComputedStyle)return"";var n;try{n=window.getComputedStyle(e)}catch(e){return""}return n?n.getPropertyValue(t)||n[t]:""}var w=window.navigator&&window.navigator.userAgent||"",E=/AppleWebKit\/([\d.]+)/i.exec(w),S=E?parseFloat(E.pop()):null,x=/iPod/i.test(w),P=(Ht=w.match(/OS (\d+)_/i))&&Ht[1]?Ht[1]:null,j=/Android/i.test(w),A=function(){var e=w.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);return t&&n?parseFloat(e[1]+"."+e[2]):t||null}(),I=j&&A<5&&S<537,M=/Firefox/i.test(w),N=/Edg/i.test(w),O=!N&&(/Chrome/i.test(w)||/CriOS/i.test(w)),L=(qt=w.match(/(Chrome|CriOS)\/(\d+)/))&&qt[2]?parseFloat(qt[2]):null,D=Xt=!(Xt=(Xt=/MSIE\s(\d+)\.\d/.exec(w))&&parseFloat(Xt[1]))&&/Trident\/7.0/i.test(w)&&/rv:11.0/.test(w)?11:Xt,F=/Safari/i.test(w)&&!O&&!j&&!N,R=/Windows/i.test(w),B=Boolean(X()&&("ontouchstart"in window||window.navigator.maxTouchPoints||window.DocumentTouch&&window.document instanceof window.DocumentTouch)),H=/iPad/i.test(w)||F&&B&&!/iPhone/i.test(w),V=/iPhone/i.test(w)&&!H,U=V||H||x,K=(F||U)&&!O,W=Object.freeze({__proto__:null,IS_IPOD:x,IOS_VERSION:P,IS_ANDROID:j,ANDROID_VERSION:A,IS_NATIVE_ANDROID:I,IS_FIREFOX:M,IS_EDGE:N,IS_CHROME:O,CHROME_VERSION:L,IE_VERSION:D,IS_SAFARI:F,IS_WINDOWS:R,TOUCH_ENABLED:B,IS_IPAD:H,IS_IPHONE:V,IS_IOS:U,IS_ANY_SAFARI:K});function z(e){return"string"==typeof e&&Boolean(e.trim())}function q(e){if(0<=e.indexOf(" "))throw new Error("class has illegal whitespace characters")}function X(){return document===window.document}function $(e){return T(e)&&1===e.nodeType}function G(){try{return window.parent!==window.self}catch(e){return!0}}function Y(n){return function(e,t){if(!z(e))return document[n](null);t=$(t=z(t)?document.querySelector(t):t)?t:document;return t[n]&&t[n](e)}}function Q(e,n,t,i){void 0===e&&(e="div"),void 0===n&&(n={}),void 0===t&&(t={});var r=document.createElement(e);return Object.getOwnPropertyNames(n).forEach(function(e){var t=n[e];-1!==e.indexOf("aria-")||"role"===e||"type"===e?(d.warn("Setting attributes in the second argument of createEl()\nhas been deprecated. Use the third argument instead.\ncreateEl(type, properties, attributes). Attempting to set "+e+" to "+t+"."),r.setAttribute(e,t)):"textContent"===e?J(r,t):r[e]===t&&"tabIndex"!==e||(r[e]=t)}),Object.getOwnPropertyNames(t).forEach(function(e){r.setAttribute(e,t[e])}),i&&me(r,i),r}function J(e,t){return"undefined"==typeof e.textContent?e.innerText=t:e.textContent=t,e}function Z(e,t){t.firstChild?t.insertBefore(e,t.firstChild):t.appendChild(e)}function ee(e,t){return q(t),e.classList?e.classList.contains(t):new RegExp("(^|\\s)"+t+"($|\\s)").test(e.className)}function te(e,t){return e.classList?e.classList.add(t):ee(e,t)||(e.className=(e.className+" "+t).trim()),e}function ne(e,t){return e?(e.classList?e.classList.remove(t):(q(t),e.className=e.className.split(/\s+/).filter(function(e){return e!==t}).join(" ")),e):(d.warn("removeClass was called with an element that doesn't exist"),null)}function ie(e,t,n){var i=ee(e,t);if((n="boolean"!=typeof(n="function"==typeof n?n(e,t):n)?!i:n)!==i)return(n?te:ne)(e,t),e}function re(n,i){Object.getOwnPropertyNames(i).forEach(function(e){var t=i[e];null===t||"undefined"==typeof t||!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)})}function se(e){var t={},n=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(e&&e.attributes&&0<e.attributes.length)for(var i=e.attributes,r=i.length-1;0<=r;r--){var s=i[r].name,o=i[r].value;"boolean"!=typeof e[s]&&-1===n.indexOf(","+s+",")||(o=null!==o),t[s]=o}return t}function oe(e,t){return e.getAttribute(t)}function ae(e,t,n){e.setAttribute(t,n)}function le(e,t){e.removeAttribute(t)}function ce(){document.body.focus(),document.onselectstart=function(){return!1}}function ue(){document.onselectstart=function(){return!0}}function he(e){if(e&&e.getBoundingClientRect&&e.parentNode){var t=e.getBoundingClientRect(),n={};return["bottom","height","left","right","top","width"].forEach(function(e){void 0!==t[e]&&(n[e]=t[e])}),n.height||(n.height=parseFloat(C(e,"height"))),n.width||(n.width=parseFloat(C(e,"width"))),n}}function de(e){if(!e||e&&!e.offsetParent)return{left:0,top:0,width:0,height:0};for(var t=e.offsetWidth,n=e.offsetHeight,i=0,r=0;e.offsetParent&&e!==document[c.fullscreenElement];)i+=e.offsetLeft,r+=e.offsetTop,e=e.offsetParent;return{left:i,top:r,width:t,height:n}}function pe(e,t){var n={x:0,y:0};if(U)for(var i=e;i&&"html"!==i.nodeName.toLowerCase();){var r,s=C(i,"transform");/^matrix/.test(s)?(r=s.slice(7,-1).split(/,\s/).map(Number),n.x+=r[4],n.y+=r[5]):/^matrix3d/.test(s)&&(s=s.slice(9,-1).split(/,\s/).map(Number),n.x+=s[12],n.y+=s[13]),i=i.parentNode}var o={},a=de(t.target),l=de(e),c=l.width,u=l.height,e=t.offsetY-(l.top-a.top),a=t.offsetX-(l.left-a.left);return t.changedTouches&&(a=t.changedTouches[0].pageX-l.left,e=t.changedTouches[0].pageY+l.top,U&&(a-=n.x,e-=n.y)),o.y=1-Math.max(0,Math.min(1,e/u)),o.x=Math.max(0,Math.min(1,a/c)),o}function fe(e){return T(e)&&3===e.nodeType}function ve(e){for(;e.firstChild;)e.removeChild(e.firstChild);return e}function ge(e){return"function"==typeof e&&(e=e()),(Array.isArray(e)?e:[e]).map(function(e){return $(e="function"==typeof e?e():e)||fe(e)?e:"string"==typeof e&&/\S/.test(e)?document.createTextNode(e):void 0}).filter(function(e){return e})}function me(t,e){return ge(e).forEach(function(e){return t.appendChild(e)}),t}function _e(e,t){return me(ve(e),t)}function ye(e){return void 0===e.button&&void 0===e.buttons||(0===e.button&&void 0===e.buttons||("mouseup"===e.type&&0===e.button&&0===e.buttons||0===e.button&&1===e.buttons))}var be,Te=Y("querySelector"),ke=Y("querySelectorAll"),Ce=Object.freeze({__proto__:null,isReal:X,isEl:$,isInFrame:G,createEl:Q,textContent:J,prependTo:Z,hasClass:ee,addClass:te,removeClass:ne,toggleClass:ie,setAttributes:re,getAttributes:se,getAttribute:oe,setAttribute:ae,removeAttribute:le,blockTextSelection:ce,unblockTextSelection:ue,getBoundingClientRect:he,findPosition:de,getPointerPosition:pe,isTextNode:fe,emptyEl:ve,normalizeContent:ge,appendContent:me,insertContent:_e,isSingleLeftClick:ye,$:Te,$$:ke}),we=!1,Ee=function(){if(!1!==be.options.autoSetup){var e=Array.prototype.slice.call(document.getElementsByTagName("video")),t=Array.prototype.slice.call(document.getElementsByTagName("audio")),n=Array.prototype.slice.call(document.getElementsByTagName("video-js")),i=e.concat(t,n);if(i&&0<i.length)for(var r=0,s=i.length;r<s;r++){var o=i[r];if(!o||!o.getAttribute){Se(1);break}void 0===o.player&&null!==o.getAttribute("data-setup")&&be(o)}else we||Se(1)}};function Se(e,t){X()&&(t&&(be=t),window.setTimeout(Ee,e))}function xe(){we=!0,window.removeEventListener("load",xe)}X()&&("complete"===document.readyState?xe():window.addEventListener("load",xe));function Pe(e){var t=document.createElement("style");return t.className=e,t}function je(e,t){e.styleSheet?e.styleSheet.cssText=t:e.textContent=t}var Ae=3;window.WeakMap||(cn=function(){function e(){this.vdata="vdata"+Math.floor(window.performance&&window.performance.now()||Date.now()),this.data={}}var t=e.prototype;return t.set=function(e,t){var n=e[this.vdata]||Ae++;return e[this.vdata]||(e[this.vdata]=n),this.data[n]=t,this},t.get=function(e){var t=e[this.vdata];if(t)return this.data[t];d("We have no data for this element",e)},t.has=function(e){return e[this.vdata]in this.data},t.delete=function(e){var t=e[this.vdata];t&&(delete this.data[t],delete e[this.vdata])},e}());var Ie,Me=new(window.WeakMap?WeakMap:cn);function Ne(e,t){var n;Me.has(e)&&(0===(n=Me.get(e)).handlers[t].length&&(delete n.handlers[t],e.removeEventListener?e.removeEventListener(t,n.dispatcher,!1):e.detachEvent&&e.detachEvent("on"+t,n.dispatcher)),Object.getOwnPropertyNames(n.handlers).length<=0&&(delete n.handlers,delete n.dispatcher,delete n.disabled),0===Object.getOwnPropertyNames(n).length&&Me.delete(e))}function Oe(t,n,e,i){e.forEach(function(e){t(n,e,i)})}function Le(e){if(e.fixed_)return e;function t(){return!0}function n(){return!1}if(!e||!e.isPropagationStopped||!e.isImmediatePropagationStopped){var i,r,s,o=e||window.event;for(i in e={},o)"layerX"!==i&&"layerY"!==i&&"keyLocation"!==i&&"webkitMovementX"!==i&&"webkitMovementY"!==i&&"path"!==i&&("returnValue"===i&&o.preventDefault||(e[i]=o[i]));e.target||(e.target=e.srcElement||document),e.relatedTarget||(e.relatedTarget=e.fromElement===e.target?e.toElement:e.fromElement),e.preventDefault=function(){o.preventDefault&&o.preventDefault(),e.returnValue=!1,o.returnValue=!1,e.defaultPrevented=!0},e.defaultPrevented=!1,e.stopPropagation=function(){o.stopPropagation&&o.stopPropagation(),e.cancelBubble=!0,o.cancelBubble=!0,e.isPropagationStopped=t},e.isPropagationStopped=n,e.stopImmediatePropagation=function(){o.stopImmediatePropagation&&o.stopImmediatePropagation(),e.isImmediatePropagationStopped=t,e.stopPropagation()},e.isImmediatePropagationStopped=n,null!==e.clientX&&void 0!==e.clientX&&(r=document.documentElement,s=document.body,e.pageX=e.clientX+(r&&r.scrollLeft||s&&s.scrollLeft||0)-(r&&r.clientLeft||s&&s.clientLeft||0),e.pageY=e.clientY+(r&&r.scrollTop||s&&s.scrollTop||0)-(r&&r.clientTop||s&&s.clientTop||0)),e.which=e.charCode||e.keyCode,null!==e.button&&void 0!==e.button&&(e.button=1&e.button?0:4&e.button?1:2&e.button?2:0)}return e.fixed_=!0,e}var De=function(){if("boolean"!=typeof Ie){Ie=!1;try{var e=Object.defineProperty({},"passive",{get:function(){Ie=!0}});window.addEventListener("test",null,e),window.removeEventListener("test",null,e)}catch(e){}}return Ie},Fe=["touchstart","touchmove"];function Re(o,e,t){if(Array.isArray(e))return Oe(Re,o,e,t);Me.has(o)||Me.set(o,{});var a=Me.get(o);a.handlers||(a.handlers={}),a.handlers[e]||(a.handlers[e]=[]),t.guid||(t.guid=Ae++),a.handlers[e].push(t),a.dispatcher||(a.disabled=!1,a.dispatcher=function(e,t){if(!a.disabled){e=Le(e);var n=a.handlers[e.type];if(n)for(var i=n.slice(0),r=0,s=i.length;r<s&&!e.isImmediatePropagationStopped();r++)try{i[r].call(o,e,t)}catch(e){d.error(e)}}}),1===a.handlers[e].length&&(o.addEventListener?(t=!1,De()&&-1<Fe.indexOf(e)&&(t={passive:!0}),o.addEventListener(e,a.dispatcher,t)):o.attachEvent&&o.attachEvent("on"+e,a.dispatcher))}function Be(e,t,n){if(Me.has(e)){var i=Me.get(e);if(i.handlers){if(Array.isArray(t))return Oe(Be,e,t,n);var r=function(e,t){i.handlers[t]=[],Ne(e,t)};if(void 0!==t){var s=i.handlers[t];if(s)if(n){if(n.guid)for(var o=0;o<s.length;o++)s[o].guid===n.guid&&s.splice(o--,1);Ne(e,t)}else r(e,t)}else for(var a in i.handlers)Object.prototype.hasOwnProperty.call(i.handlers||{},a)&&r(e,a)}}}function He(e,t,n){var i=Me.has(e)?Me.get(e):{},r=e.parentNode||e.ownerDocument;return"string"==typeof t?t={type:t,target:e}:t.target||(t.target=e),t=Le(t),i.dispatcher&&i.dispatcher.call(e,t,n),r&&!t.isPropagationStopped()&&!0===t.bubbles?He.call(null,r,t,n):!r&&!t.defaultPrevented&&t.target&&t.target[t.type]&&(Me.has(t.target)||Me.set(t.target,{}),r=Me.get(t.target),t.target[t.type]&&(r.disabled=!0,"function"==typeof t.target[t.type]&&t.target[t.type](),r.disabled=!1)),!t.defaultPrevented}function Ve(e,t,n){if(Array.isArray(t))return Oe(Ve,e,t,n);function i(){Be(e,t,i),n.apply(this,arguments)}i.guid=n.guid=n.guid||Ae++,Re(e,t,i)}function Ue(e,t,n){function i(){Be(e,t,i),n.apply(this,arguments)}i.guid=n.guid=n.guid||Ae++,Re(e,t,i)}function Ke(e,t,n){return t.guid||(t.guid=Ae++),(e=t.bind(e)).guid=n?n+"_"+t.guid:t.guid,e}function We(t,n){var i=window.performance.now();return function(){var e=window.performance.now();n<=e-i&&(t.apply(void 0,arguments),i=e)}}function ze(i,r,s,o){var a;function e(){var e=this,t=arguments,n=function(){n=a=null,s||i.apply(e,t)};!a&&s&&i.apply(e,t),o.clearTimeout(a),a=o.setTimeout(n,r)}return void 0===o&&(o=window),e.cancel=function(){o.clearTimeout(a),a=null},e}function qe(){}var Xe,$e=Object.freeze({__proto__:null,fixEvent:Le,on:Re,off:Be,trigger:He,one:Ve,any:Ue});qe.prototype.allowedEvents_={},qe.prototype.addEventListener=qe.prototype.on=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},Re(this,e,t),this.addEventListener=n},qe.prototype.removeEventListener=qe.prototype.off=function(e,t){Be(this,e,t)},qe.prototype.one=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},Ve(this,e,t),this.addEventListener=n},qe.prototype.any=function(e,t){var n=this.addEventListener;this.addEventListener=function(){},Ue(this,e,t),this.addEventListener=n},qe.prototype.dispatchEvent=qe.prototype.trigger=function(e){var t=e.type||e;e=Le(e="string"==typeof e?{type:t}:e),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](e),He(this,e)},qe.prototype.queueTrigger=function(e){var t=this;Xe=Xe||new Map;var n=e.type||e,i=Xe.get(this);i||(i=new Map,Xe.set(this,i));var r=i.get(n);i.delete(n),window.clearTimeout(r);r=window.setTimeout(function(){0===i.size&&(i=null,Xe.delete(t)),t.trigger(e)},0);i.set(n,r)};function Ge(e){return"function"==typeof e.name?e.name():"string"==typeof e.name?e.name:e.name_||(e.constructor&&e.constructor.name?e.constructor.name:typeof e)}function Ye(e){return"string"==typeof e&&/\S/.test(e)||Array.isArray(e)&&!!e.length}function Qe(e,t,n){if(!e||!e.nodeName&&!nt(e))throw new Error("Invalid target for "+Ge(t)+"#"+n+"; must be a DOM node or evented object.")}function Je(e,t,n){if(!Ye(e))throw new Error("Invalid event type for "+Ge(t)+"#"+n+"; must be a non-empty string or array.")}function Ze(e,t,n){if("function"!=typeof e)throw new Error("Invalid listener for "+Ge(t)+"#"+n+"; must be a function.")}function et(e,t,n){var i,r,s=t.length<3||t[0]===e||t[0]===e.eventBusEl_,t=s?(i=e.eventBusEl_,3<=t.length&&t.shift(),r=t[0],t[1]):(i=t[0],r=t[1],t[2]);return Qe(i,e,n),Je(r,e,n),Ze(t,e,n),{isTargetingSelf:s,target:i,type:r,listener:t=Ke(e,t)}}function tt(e,t,n,i){Qe(e,e,t),e.nodeName?$e[t](e,n,i):e[t](n,i)}var nt=function(t){return t instanceof qe||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},it={on:function(){for(var e=this,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];var r,s=et(this,n,"on"),o=s.isTargetingSelf,a=s.target,l=s.type,c=s.listener;tt(a,"on",l,c),o||((r=function(){return e.off(a,l,c)}).guid=c.guid,(o=function(){return e.off("dispose",r)}).guid=c.guid,tt(this,"on","dispose",r),tt(a,"on","dispose",o))},one:function(){for(var r=this,e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=et(this,t,"one"),s=i.isTargetingSelf,o=i.target,a=i.type,l=i.listener;s?tt(o,"one",a,l):((s=function e(){r.off(o,a,e);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];l.apply(null,n)}).guid=l.guid,tt(o,"one",a,s))},any:function(){for(var r=this,e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=et(this,t,"any"),s=i.isTargetingSelf,o=i.target,a=i.type,l=i.listener;s?tt(o,"any",a,l):((s=function e(){r.off(o,a,e);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];l.apply(null,n)}).guid=l.guid,tt(o,"any",a,s))},off:function(e,t,n){!e||Ye(e)?Be(this.eventBusEl_,e,t):(t=t,Qe(e=e,this,"off"),Je(t,this,"off"),Ze(n,this,"off"),n=Ke(this,n),this.off("dispose",n),e.nodeName?(Be(e,t,n),Be(e,"dispose",n)):nt(e)&&(e.off(t,n),e.off("dispose",n)))},trigger:function(e,t){Qe(this.eventBusEl_,this,"trigger");var n=e&&"string"!=typeof e?e.type:e;if(!Ye(n)){n="Invalid event type for "+Ge(this)+"#trigger; must be a non-empty string or object with a type key that has a non-empty value.";if(!e)throw new Error(n);(this.log||d).error(n)}return He(this.eventBusEl_,e,t)}};function rt(e,t){t=(t=void 0===t?{}:t).eventBusKey;if(t){if(!e[t].nodeName)throw new Error('The eventBusKey "'+t+'" does not refer to an element.');e.eventBusEl_=e[t]}else e.eventBusEl_=Q("span",{className:"vjs-event-bus"});return b(e,it),e.eventedCallbacks&&e.eventedCallbacks.forEach(function(e){e()}),e.on("dispose",function(){e.off(),[e,e.el_,e.eventBusEl_].forEach(function(e){e&&Me.has(e)&&Me.delete(e)}),window.setTimeout(function(){e.eventBusEl_=null},0)}),e}var st={state:{},setState:function(e){var n,i=this;return y(e="function"==typeof e?e():e,function(e,t){i.state[t]!==e&&((n=n||{})[t]={from:i.state[t],to:e}),i.state[t]=e}),n&&nt(this)&&this.trigger({changes:n,type:"statechanged"}),n}};function ot(e,t){return b(e,st),e.state=b({},e.state,t),"function"==typeof e.handleStateChanged&&nt(e)&&e.on("statechanged",e.handleStateChanged),e}function at(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toLowerCase()})}function lt(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toUpperCase()})}function ct(){for(var n={},e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.forEach(function(e){e&&y(e,function(e,t){k(e)?(k(n[t])||(n[t]={}),n[t]=ct(n[t],e)):n[t]=e})}),n}var ut=window.Map||function(){function e(){this.map_={}}var t=e.prototype;return t.has=function(e){return e in this.map_},t.delete=function(e){var t=this.has(e);return delete this.map_[e],t},t.set=function(e,t){return this.map_[e]=t,this},t.forEach=function(e,t){for(var n in this.map_)e.call(t,this.map_[n],n,this)},e}(),ht=window.Set||function(){function e(){this.set_={}}var t=e.prototype;return t.has=function(e){return e in this.set_},t.delete=function(e){var t=this.has(e);return delete this.set_[e],t},t.add=function(e){return this.set_[e]=1,this},t.forEach=function(e,t){for(var n in this.set_)e.call(t,n,n,this)},e}(),dt=v(function(e,t){function n(e){if(!e||"object"!=typeof e||(t=e.which||e.keyCode||e.charCode)&&(e=t),"number"==typeof e)return a[e];var t=String(e),e=i[t.toLowerCase()];return e||((e=r[t.toLowerCase()])?e:1===t.length?t.charCodeAt(0):void 0)}n.isEventKey=function(e,t){if(e&&"object"==typeof e){var n=e.which||e.keyCode||e.charCode;if(null==n)return!1;if("string"==typeof t){e=i[t.toLowerCase()];if(e)return e===n;if(e=r[t.toLowerCase()])return e===n}else if("number"==typeof t)return t===n;return!1}};for(var i=(t=e.exports=n).code=t.codes={backspace:8,tab:9,enter:13,shift:16,ctrl:17,alt:18,"pause/break":19,"caps lock":20,esc:27,space:32,"page up":33,"page down":34,end:35,home:36,left:37,up:38,right:39,down:40,insert:45,delete:46,command:91,"left command":91,"right command":93,"numpad *":106,"numpad +":107,"numpad -":109,"numpad .":110,"numpad /":111,"num lock":144,"scroll lock":145,"my computer":182,"my calculator":183,";":186,"=":187,",":188,"-":189,".":190,"/":191,"`":192,"[":219,"\\":220,"]":221,"'":222},r=t.aliases={windows:91,"⇧":16,"⌥":18,"⌃":17,"⌘":91,ctl:17,control:17,option:18,pause:19,break:19,caps:20,return:13,escape:27,spc:32,spacebar:32,pgup:33,pgdn:34,ins:45,del:46,cmd:91},s=97;s<123;s++)i[String.fromCharCode(s)]=s-32;for(var s=48;s<58;s++)i[s-48]=s;for(s=1;s<13;s++)i["f"+s]=s+111;for(s=0;s<10;s++)i["numpad "+s]=s+96;var o,a=t.names=t.title={};for(s in i)a[i[s]]=s;for(o in r)i[o]=r[o]});dt.code,dt.codes,dt.aliases,dt.names,dt.title;var pt=function(){function o(e,t,n){var i=this;!e&&this.play?this.player_=e=this:this.player_=e,this.isDisposed_=!1,this.parentComponent_=null,this.options_=ct({},this.options_),t=this.options_=ct(this.options_,t),this.id_=t.id||t.el&&t.el.id,this.id_||(e=e&&e.id&&e.id()||"no_player",this.id_=e+"_component_"+Ae++),this.name_=t.name||null,t.el?this.el_=t.el:!1!==t.createEl&&(this.el_=this.createEl()),t.className&&this.el_&&t.className.split(" ").forEach(function(e){return i.addClass(e)}),!1!==t.evented&&(rt(this,{eventBusKey:this.el_?"el_":null}),this.handleLanguagechange=this.handleLanguagechange.bind(this),this.on(this.player_,"languagechange",this.handleLanguagechange)),ot(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},this.setTimeoutIds_=new ht,this.setIntervalIds_=new ht,this.rafIds_=new ht,this.namedRafs_=new ut,(this.clearingTimersOnDispose_=!1)!==t.initChildren&&this.initChildren(),this.ready(n),!1!==t.reportTouchActivity&&this.enableTouchActivity()}var e=o.prototype;return e.dispose=function(e){if(void 0===e&&(e={}),!this.isDisposed_){if(this.readyQueue_&&(this.readyQueue_.length=0),this.trigger({type:"dispose",bubbles:!1}),this.isDisposed_=!0,this.children_)for(var t=this.children_.length-1;0<=t;t--)this.children_[t].dispose&&this.children_[t].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.parentComponent_=null,this.el_&&(this.el_.parentNode&&(e.restoreEl?this.el_.parentNode.replaceChild(e.restoreEl,this.el_):this.el_.parentNode.removeChild(this.el_)),this.el_=null),this.player_=null}},e.isDisposed=function(){return Boolean(this.isDisposed_)},e.player=function(){return this.player_},e.options=function(e){return e&&(this.options_=ct(this.options_,e)),this.options_},e.el=function(){return this.el_},e.createEl=function(e,t,n){return Q(e,t,n)},e.localize=function(e,n,t){void 0===t&&(t=e);var i=this.player_.language&&this.player_.language(),r=this.player_.languages&&this.player_.languages(),s=r&&r[i],i=i&&i.split("-")[0],i=r&&r[i],t=t;return s&&s[e]?t=s[e]:i&&i[e]&&(t=i[e]),t=n?t.replace(/\{(\d+)\}/g,function(e,t){t=n[t-1];return"undefined"==typeof t?e:t}):t},e.handleLanguagechange=function(){},e.contentEl=function(){return this.contentEl_||this.el_},e.id=function(){return this.id_},e.name=function(){return this.name_},e.children=function(){return this.children_},e.getChildById=function(e){return this.childIndex_[e]},e.getChild=function(e){if(e)return this.childNameIndex_[e]},e.getDescendant=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var t=t.reduce(function(e,t){return e.concat(t)},[]),i=this,r=0;r<t.length;r++)if(!(i=i.getChild(t[r]))||!i.getChild)return;return i},e.addChild=function(e,t,n){if(void 0===t&&(t={}),void 0===n&&(n=this.children_.length),"string"==typeof e){var i=lt(e),r=t.componentClass||i;t.name=i;var s=o.getComponent(r);if(!s)throw new Error("Component "+r+" does not exist");if("function"!=typeof s)return null;s=new s(this.player_||this,t)}else s=e;return s.parentComponent_&&s.parentComponent_.removeChild(s),this.children_.splice(n,0,s),s.parentComponent_=this,"function"==typeof s.id&&(this.childIndex_[s.id()]=s),(i=i||s.name&&lt(s.name()))&&(this.childNameIndex_[i]=s,this.childNameIndex_[at(i)]=s),"function"==typeof s.el&&s.el()&&(i=null,this.children_[n+1]&&(this.children_[n+1].el_?i=this.children_[n+1].el_:$(this.children_[n+1])&&(i=this.children_[n+1])),this.contentEl().insertBefore(s.el(),i)),s},e.removeChild=function(e){if((e="string"==typeof e?this.getChild(e):e)&&this.children_){for(var t,n=!1,i=this.children_.length-1;0<=i;i--)if(this.children_[i]===e){n=!0,this.children_.splice(i,1);break}n&&(e.parentComponent_=null,this.childIndex_[e.id()]=null,this.childNameIndex_[lt(e.name())]=null,this.childNameIndex_[at(e.name())]=null,(t=e.el())&&t.parentNode===this.contentEl()&&this.contentEl().removeChild(e.el()))}},e.initChildren=function(){var n,t,e,i=this,r=this.options_.children;r&&(n=this.options_,t=o.getComponent("Tech"),(e=Array.isArray(r)?r:Object.keys(r)).concat(Object.keys(this.options_).filter(function(t){return!e.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(e){var t,e="string"==typeof e?r[t=e]||i.options_[t]||{}:(t=e.name,e);return{name:t,opts:e}}).filter(function(e){e=o.getComponent(e.opts.componentClass||lt(e.name));return e&&!t.isTech(e)}).forEach(function(e){var t=e.name,e=e.opts;!1!==(e=void 0!==n[t]?n[t]:e)&&((e=!0===e?{}:e).playerOptions=i.options_.playerOptions,(e=i.addChild(t,e))&&(i[t]=e))}))},e.buildCSSClass=function(){return""},e.ready=function(e,t){if(void 0===t&&(t=!1),e)return this.isReady_?void(t?e.call(this):this.setTimeout(e,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(e))},e.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var e=this.readyQueue_;this.readyQueue_=[],e&&0<e.length&&e.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},e.$=function(e,t){return Te(e,t||this.contentEl())},e.$$=function(e,t){return ke(e,t||this.contentEl())},e.hasClass=function(e){return ee(this.el_,e)},e.addClass=function(e){te(this.el_,e)},e.removeClass=function(e){ne(this.el_,e)},e.toggleClass=function(e,t){ie(this.el_,e,t)},e.show=function(){this.removeClass("vjs-hidden")},e.hide=function(){this.addClass("vjs-hidden")},e.lockShowing=function(){this.addClass("vjs-lock-showing")},e.unlockShowing=function(){this.removeClass("vjs-lock-showing")},e.getAttribute=function(e){return oe(this.el_,e)},e.setAttribute=function(e,t){ae(this.el_,e,t)},e.removeAttribute=function(e){le(this.el_,e)},e.width=function(e,t){return this.dimension("width",e,t)},e.height=function(e,t){return this.dimension("height",e,t)},e.dimensions=function(e,t){this.width(e,!0),this.height(t)},e.dimension=function(e,t,n){if(void 0!==t)return-1!==(""+(t=null===t||t!=t?0:t)).indexOf("%")||-1!==(""+t).indexOf("px")?this.el_.style[e]=t:this.el_.style[e]="auto"===t?"":t+"px",void(n||this.trigger("componentresize"));if(!this.el_)return 0;t=this.el_.style[e],n=t.indexOf("px");return-1!==n?parseInt(t.slice(0,n),10):parseInt(this.el_["offset"+lt(e)],10)},e.currentDimension=function(e){var t=0;if("width"!==e&&"height"!==e)throw new Error("currentDimension only accepts width or height value");return t=C(this.el_,e),0!==(t=parseFloat(t))&&!isNaN(t)||(e="offset"+lt(e),t=this.el_[e]),t},e.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},e.currentWidth=function(){return this.currentDimension("width")},e.currentHeight=function(){return this.currentDimension("height")},e.focus=function(){this.el_.focus()},e.blur=function(){this.el_.blur()},e.handleKeyDown=function(e){this.player_&&(dt.isEventKey(e,"Tab")||e.stopPropagation(),this.player_.handleKeyDown(e))},e.handleKeyPress=function(e){this.handleKeyDown(e)},e.emitTapEvents=function(){var n,t=0,i=null;this.on("touchstart",function(e){1===e.touches.length&&(i={pageX:e.touches[0].pageX,pageY:e.touches[0].pageY},t=window.performance.now(),n=!0)}),this.on("touchmove",function(e){var t;1<e.touches.length?n=!1:i&&(t=e.touches[0].pageX-i.pageX,e=e.touches[0].pageY-i.pageY,10<Math.sqrt(t*t+e*e)&&(n=!1))});function e(){n=!1}this.on("touchleave",e),this.on("touchcancel",e),this.on("touchend",function(e){!(i=null)===n&&window.performance.now()-t<200&&(e.preventDefault(),this.trigger("tap"))})},e.enableTouchActivity=function(){var t,n,e;this.player()&&this.player().reportUserActivity&&(t=Ke(this.player(),this.player().reportUserActivity),this.on("touchstart",function(){t(),this.clearInterval(n),n=this.setInterval(t,250)}),e=function(e){t(),this.clearInterval(n)},this.on("touchmove",t),this.on("touchend",e),this.on("touchcancel",e))},e.setTimeout=function(e,t){var n,i=this;return e=Ke(this,e),this.clearTimersOnDispose_(),n=window.setTimeout(function(){i.setTimeoutIds_.has(n)&&i.setTimeoutIds_.delete(n),e()},t),this.setTimeoutIds_.add(n),n},e.clearTimeout=function(e){return this.setTimeoutIds_.has(e)&&(this.setTimeoutIds_.delete(e),window.clearTimeout(e)),e},e.setInterval=function(e,t){e=Ke(this,e),this.clearTimersOnDispose_();t=window.setInterval(e,t);return this.setIntervalIds_.add(t),t},e.clearInterval=function(e){return this.setIntervalIds_.has(e)&&(this.setIntervalIds_.delete(e),window.clearInterval(e)),e},e.requestAnimationFrame=function(e){var t,n=this;return this.supportsRaf_?(this.clearTimersOnDispose_(),e=Ke(this,e),t=window.requestAnimationFrame(function(){n.rafIds_.has(t)&&n.rafIds_.delete(t),e()}),this.rafIds_.add(t),t):this.setTimeout(e,1e3/60)},e.requestNamedAnimationFrame=function(e,t){var n=this;if(!this.namedRafs_.has(e)){this.clearTimersOnDispose_(),t=Ke(this,t);var i=this.requestAnimationFrame(function(){t(),n.namedRafs_.has(e)&&n.namedRafs_.delete(e)});return this.namedRafs_.set(e,i),e}},e.cancelNamedAnimationFrame=function(e){this.namedRafs_.has(e)&&(this.cancelAnimationFrame(this.namedRafs_.get(e)),this.namedRafs_.delete(e))},e.cancelAnimationFrame=function(e){return this.supportsRaf_?(this.rafIds_.has(e)&&(this.rafIds_.delete(e),window.cancelAnimationFrame(e)),e):this.clearTimeout(e)},e.clearTimersOnDispose_=function(){var i=this;this.clearingTimersOnDispose_||(this.clearingTimersOnDispose_=!0,this.one("dispose",function(){[["namedRafs_","cancelNamedAnimationFrame"],["rafIds_","cancelAnimationFrame"],["setTimeoutIds_","clearTimeout"],["setIntervalIds_","clearInterval"]].forEach(function(e){var t=e[0],n=e[1];i[t].forEach(function(e,t){return i[n](t)})}),i.clearingTimersOnDispose_=!1}))},o.registerComponent=function(e,t){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var n=o.getComponent("Tech"),i=n&&n.isTech(t),n=o===t||o.prototype.isPrototypeOf(t.prototype);if(i||!n){var r=i?"techs must be registered using Tech.registerTech()":"must be a Component subclass";throw new Error('Illegal component, "'+e+'"; '+r+".")}e=lt(e),o.components_||(o.components_={});r=o.getComponent("Player");if("Player"===e&&r&&r.players){var s=r.players,r=Object.keys(s);if(s&&0<r.length&&r.map(function(e){return s[e]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return o.components_[e]=t,o.components_[at(e)]=t},o.getComponent=function(e){if(e&&o.components_)return o.components_[e]},o}();pt.prototype.supportsRaf_="function"==typeof window.requestAnimationFrame&&"function"==typeof window.cancelAnimationFrame,pt.registerComponent("Component",pt);var ft=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e};var vt=function(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t};function gt(e,t,n,i){return function(e,t,n){if("number"!=typeof t||t<0||n<t)throw new Error("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+n+").")}(e,i,n.length-1),n[i][t]}function mt(e){var t=void 0===e||0===e.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:e.length,start:gt.bind(null,"start",0,e),end:gt.bind(null,"end",1,e)};return window.Symbol&&window.Symbol.iterator&&(t[window.Symbol.iterator]=function(){return(e||[]).values()}),t}function _t(e,t){return Array.isArray(e)?mt(e):void 0===e||void 0===t?mt():mt([[e,t]])}function yt(e,t){var n,i,r=0;if(!t)return 0;e&&e.length||(e=_t(0,0));for(var s=0;s<e.length;s++)n=e.start(s),r+=(i=t<(i=e.end(s))?t:i)-n;return r/t}function bt(e){if(e instanceof bt)return e;"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:T(e)&&("number"==typeof e.code&&(this.code=e.code),b(this,e)),this.message||(this.message=bt.defaultMessages[this.code]||"")}bt.prototype.code=0,bt.prototype.message="",bt.prototype.status=null,bt.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],bt.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var Tt=0;Tt<bt.errorTypes.length;Tt++)bt[bt.errorTypes[Tt]]=Tt,bt.prototype[bt.errorTypes[Tt]]=Tt;var kt=function(e,t){var n,i=null;try{n=JSON.parse(e,t)}catch(e){i=e}return[i,n]};function Ct(e){return null!=e&&"function"==typeof e.then}function wt(e){Ct(e)&&e.then(null,function(e){})}function Et(i){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,t,n){return i[t]&&(e[t]=i[t]),e},{cues:i.cues&&Array.prototype.map.call(i.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})})}var St=function(e){var t=e.$$("track"),n=Array.prototype.map.call(t,function(e){return e.track});return Array.prototype.map.call(t,function(e){var t=Et(e.track);return e.src&&(t.src=e.src),t}).concat(Array.prototype.filter.call(e.textTracks(),function(e){return-1===n.indexOf(e)}).map(Et))},xt=function(e,n){return e.forEach(function(e){var t=n.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(e){return t.addCue(e)})}),n.textTracks()},Pt="vjs-modal-dialog",jt=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.handleKeyDown_=function(e){return n.handleKeyDown(e)},n.close_=function(e){return n.close(e)},n.opened_=n.hasBeenOpened_=n.hasBeenFilled_=!1,n.closeable(!n.options_.uncloseable),n.content(n.options_.content),n.contentEl_=Q("div",{className:Pt+"-content"},{role:"document"}),n.descEl_=Q("p",{className:Pt+"-description vjs-control-text",id:n.el().getAttribute("aria-describedby")}),J(n.descEl_,n.description()),n.el_.appendChild(n.descEl_),n.el_.appendChild(n.contentEl_),n}vt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},t.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,i.prototype.dispose.call(this)},t.buildCSSClass=function(){return Pt+" vjs-hidden "+i.prototype.buildCSSClass.call(this)},t.label=function(){return this.localize(this.options_.label||"Modal Window")},t.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},t.open=function(){var e;this.opened_||(e=this.player(),this.trigger("beforemodalopen"),this.opened_=!0,!this.options_.fillAlways&&(this.hasBeenOpened_||this.hasBeenFilled_)||this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.on("keydown",this.handleKeyDown_),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0)},t.opened=function(e){return"boolean"==typeof e&&this[e?"open":"close"](),this.opened_},t.close=function(){var e;this.opened_&&(e=this.player(),this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.off("keydown",this.handleKeyDown_),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose())},t.closeable=function(e){var t,n;return"boolean"==typeof e&&(t=this.closeable_=!!e,n=this.getChild("closeButton"),t&&!n&&(e=this.contentEl_,this.contentEl_=this.el_,n=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=e,this.on(n,"close",this.close_)),!t&&n&&(this.off(n,"close",this.close_),this.removeChild(n),n.dispose())),this.closeable_},t.fill=function(){this.fillWith(this.content())},t.fillWith=function(e){var t=this.contentEl(),n=t.parentNode,i=t.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,n.removeChild(t),this.empty(),_e(t,e),this.trigger("modalfill"),i?n.insertBefore(t,i):n.appendChild(t);t=this.getChild("closeButton");t&&n.appendChild(t.el_)},t.empty=function(){this.trigger("beforemodalempty"),ve(this.contentEl()),this.trigger("modalempty")},t.content=function(e){return"undefined"!=typeof e&&(this.content_=e),this.content_},t.conditionalFocus_=function(){var e=document.activeElement,t=this.player_.el_;this.previouslyActiveEl_=null,!t.contains(e)&&t!==e||(this.previouslyActiveEl_=e,this.focus())},t.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null)},t.handleKeyDown=function(e){if(e.stopPropagation(),dt.isEventKey(e,"Escape")&&this.closeable())return e.preventDefault(),void this.close();if(dt.isEventKey(e,"Tab")){for(var t,n=this.focusableEls_(),i=this.el_.querySelector(":focus"),r=0;r<n.length;r++)if(i===n[r]){t=r;break}document.activeElement===this.el_&&(t=0),e.shiftKey&&0===t?(n[n.length-1].focus(),e.preventDefault()):e.shiftKey||t!==n.length-1||(n[0].focus(),e.preventDefault())}},t.focusableEls_=function(){var e=this.el_.querySelectorAll("*");return Array.prototype.filter.call(e,function(e){return(e instanceof window.HTMLAnchorElement||e instanceof window.HTMLAreaElement)&&e.hasAttribute("href")||(e instanceof window.HTMLInputElement||e instanceof window.HTMLSelectElement||e instanceof window.HTMLTextAreaElement||e instanceof window.HTMLButtonElement)&&!e.hasAttribute("disabled")||e instanceof window.HTMLIFrameElement||e instanceof window.HTMLObjectElement||e instanceof window.HTMLEmbedElement||e.hasAttribute("tabindex")&&-1!==e.getAttribute("tabindex")||e.hasAttribute("contenteditable")})},e}(pt);jt.prototype.options_={pauseOnOpen:!0,temporary:!0},pt.registerComponent("ModalDialog",jt);var At,It=function(i){function e(e){var t;void 0===e&&(e=[]),(t=i.call(this)||this).tracks_=[],Object.defineProperty(ft(t),"length",{get:function(){return this.tracks_.length}});for(var n=0;n<e.length;n++)t.addTrack(e[n]);return t}vt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this,n=this.tracks_.length;""+n in this||Object.defineProperty(this,n,{get:function(){return this.tracks_[n]}}),-1===this.tracks_.indexOf(e)&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack",target:this})),e.labelchange_=function(){t.trigger({track:e,type:"labelchange",target:t})},nt(e)&&e.addEventListener("labelchange",e.labelchange_)},t.removeTrack=function(e){for(var t,n=0,i=this.length;n<i;n++)if(this[n]===e){(t=this[n]).off&&t.off(),this.tracks_.splice(n,1);break}t&&this.trigger({track:t,type:"removetrack",target:this})},t.getTrackById=function(e){for(var t=null,n=0,i=this.length;n<i;n++){var r=this[n];if(r.id===e){t=r;break}}return t},e}(qe);for(At in It.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack",labelchange:"labelchange"},It.prototype.allowedEvents_)It.prototype["on"+At]=null;function Mt(e,t){for(var n=0;n<e.length;n++)Object.keys(e[n]).length&&t.id!==e[n].id&&(e[n].enabled=!1)}function Nt(e,t){for(var n=0;n<e.length;n++)Object.keys(e[n]).length&&t.id!==e[n].id&&(e[n].selected=!1)}function Ot(e){var t=["protocol","hostname","port","pathname","search","hash","host"],n=document.createElement("a");n.href=e;for(var i={},r=0;r<t.length;r++)i[t[r]]=n[t[r]];return"http:"===i.protocol&&(i.host=i.host.replace(/:80$/,"")),"https:"===i.protocol&&(i.host=i.host.replace(/:443$/,"")),i.protocol||(i.protocol=window.location.protocol),i.host||(i.host=window.location.host),i}function Lt(e){var t;return e.match(/^https?:\/\//)||((t=document.createElement("a")).href=e,e=t.href),e}function Dt(e){if("string"==typeof e){e=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/.exec(e);if(e)return e.pop().toLowerCase()}return""}function Ft(e,t){return void 0===t&&(t=window.location),(":"===(e=Ot(e)).protocol?t:e).protocol+e.host!==t.protocol+t.host}var Rt=function(i){function e(e){for(var t,n=(e=void 0===e?[]:e).length-1;0<=n;n--)if(e[n].enabled){Mt(e,e[n]);break}return(t=i.call(this,e)||this).changing_=!1,t}vt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this;e.enabled&&Mt(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&(e.enabledChange_=function(){t.changing_||(t.changing_=!0,Mt(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("enabledchange",e.enabledChange_))},t.removeTrack=function(e){i.prototype.removeTrack.call(this,e),e.removeEventListener&&e.enabledChange_&&(e.removeEventListener("enabledchange",e.enabledChange_),e.enabledChange_=null)},e}(It),Bt=function(i){function e(e){for(var t,n=(e=void 0===e?[]:e).length-1;0<=n;n--)if(e[n].selected){Nt(e,e[n]);break}return(t=i.call(this,e)||this).changing_=!1,Object.defineProperty(ft(t),"selectedIndex",{get:function(){for(var e=0;e<this.length;e++)if(this[e].selected)return e;return-1},set:function(){}}),t}vt(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this;e.selected&&Nt(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&(e.selectedChange_=function(){t.changing_||(t.changing_=!0,Nt(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("selectedchange",e.selectedChange_))},t.removeTrack=function(e){i.prototype.removeTrack.call(this,e),e.removeEventListener&&e.selectedChange_&&(e.removeEventListener("selectedchange",e.selectedChange_),e.selectedChange_=null)},e}(It),E=function(n){function e(){return n.apply(this,arguments)||this}vt(e,n);var t=e.prototype;return t.addTrack=function(e){var t=this;n.prototype.addTrack.call(this,e),this.queueChange_||(this.queueChange_=function(){return t.queueTrigger("change")}),this.triggerSelectedlanguagechange||(this.triggerSelectedlanguagechange_=function(){return t.trigger("selectedlanguagechange")}),e.addEventListener("modechange",this.queueChange_);-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",this.triggerSelectedlanguagechange_)},t.removeTrack=function(e){n.prototype.removeTrack.call(this,e),e.removeEventListener&&(this.queueChange_&&e.removeEventListener("modechange",this.queueChange_),this.selectedlanguagechange_&&e.removeEventListener("modechange",this.triggerSelectedlanguagechange_))},e}(It),Ht=function(){function e(e){void 0===e&&(e=[]),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var t=0,n=e.length;t<n;t++)this.addTrackElement_(e[t])}var t=e.prototype;return t.addTrackElement_=function(e){var t=this.trackElements_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.trackElements_[t]}}),-1===this.trackElements_.indexOf(e)&&this.trackElements_.push(e)},t.getTrackElementByTrack_=function(e){for(var t,n=0,i=this.trackElements_.length;n<i;n++)if(e===this.trackElements_[n].track){t=this.trackElements_[n];break}return t},t.removeTrackElement_=function(e){for(var t=0,n=this.trackElements_.length;t<n;t++)if(e===this.trackElements_[t]){this.trackElements_[t].track&&"function"==typeof this.trackElements_[t].track.off&&this.trackElements_[t].track.off(),"function"==typeof this.trackElements_[t].off&&this.trackElements_[t].off(),this.trackElements_.splice(t,1);break}},e}(),Vt=function(){function t(e){t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}var e=t.prototype;return e.setCues_=function(e){var t=this.length||0,n=0,i=e.length;this.cues_=e,this.length_=e.length;function r(e){""+e in this||Object.defineProperty(this,""+e,{get:function(){return this.cues_[e]}})}if(t<i)for(n=t;n<i;n++)r.call(this,n)},e.getCueById=function(e){for(var t=null,n=0,i=this.length;n<i;n++){var r=this[n];if(r.id===e){t=r;break}}return t},t}(),Ut={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},Kt={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},Wt={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},zt={disabled:"disabled",hidden:"hidden",showing:"showing"},S=function(s){function e(e){void 0===e&&(e={});var t,n=s.call(this)||this,i={id:e.id||"vjs_track_"+Ae++,kind:e.kind||"",language:e.language||""},r=e.label||"";for(t in i)!function(e){Object.defineProperty(ft(n),e,{get:function(){return i[e]},set:function(){}})}(t);return Object.defineProperty(ft(n),"label",{get:function(){return r},set:function(e){e!==r&&(r=e,this.trigger("labelchange"))}}),n}return vt(e,s),e}(qe),qt=Object.freeze({__proto__:null,parseUrl:Ot,getAbsoluteURL:Lt,getFileExtension:Dt,isCrossOrigin:Ft}),Xt="undefined"!=typeof window?window:"undefined"!=typeof f?f:"undefined"!=typeof self?self:{},$t=Xt,Gt=function(e){if(!e)return!1;var t=Yt.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)},Yt=Object.prototype.toString;en.httpHandler=function(i,r){return void 0===r&&(r=!1),function(e,t,n){if(e)i(e);else if(400<=t.statusCode&&t.statusCode<=599){e=n;if(r)if($t.TextDecoder){t=function(e){void 0===e&&(e="");return e.toLowerCase().split(";").reduce(function(e,t){var n=t.split("="),t=n[0],n=n[1];return"charset"===t.trim()?n.trim():e},"utf-8")}(t.headers&&t.headers["content-type"]);try{e=new TextDecoder(t).decode(n)}catch(e){}}else e=String.fromCharCode.apply(null,new Uint8Array(n));i({cause:e})}else i(null,n)}};
/**
   * @license
   * slighly modified parse-headers 2.0.2 <https://github.com/kesla/parse-headers/>
   * Copyright (c) 2014 David Björklund
   * Available under the MIT license
   * <https://github.com/kesla/parse-headers/blob/master/LICENCE>
   */
var Qt=function(e){var i={};return e&&e.trim().split("\n").forEach(function(e){var t=e.indexOf(":"),n=e.slice(0,t).trim().toLowerCase(),t=e.slice(t+1).trim();"undefined"==typeof i[n]?i[n]=t:Array.isArray(i[n])?i[n].push(t):i[n]=[i[n],t]}),i},Jt=en,x=en;function Zt(e,t,n){var i=e;return Gt(t)?(n=t,"string"==typeof e&&(i={uri:e})):i=g({},t,{uri:e}),i.callback=n,i}function en(e,t,n){return tn(t=Zt(e,t,n))}function tn(i){if("undefined"==typeof i.callback)throw new Error("callback argument missing");var r=!1,s=function(e,t,n){r||(r=!0,i.callback(e,t,n))};function o(){var e=void 0,e=c.response||c.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(c);if(v)try{e=JSON.parse(e)}catch(e){}return e}function t(e){return clearTimeout(l),(e=!(e instanceof Error)?new Error(""+(e||"Unknown XMLHttpRequest Error")):e).statusCode=0,s(e,g)}function e(){if(!a){clearTimeout(l);var e=i.useXDR&&void 0===c.status?200:1223===c.status?204:c.status,t=g,n=null;return 0!==e?(t={body:o(),statusCode:e,method:h,headers:{},url:u,rawRequest:c},c.getAllResponseHeaders&&(t.headers=Qt(c.getAllResponseHeaders()))):n=new Error("Internal XMLHttpRequest Error"),s(n,t,t.body)}}var n,a,l,c=i.xhr||null,u=(c=c||new(i.cors||i.useXDR?en.XDomainRequest:en.XMLHttpRequest)).url=i.uri||i.url,h=c.method=i.method||"GET",d=i.body||i.data,p=c.headers=i.headers||{},f=!!i.sync,v=!1,g={body:void 0,headers:{},statusCode:0,method:h,url:u,rawRequest:c};if("json"in i&&!1!==i.json&&(v=!0,p.accept||p.Accept||(p.Accept="application/json"),"GET"!==h&&"HEAD"!==h&&(p["content-type"]||p["Content-Type"]||(p["Content-Type"]="application/json"),d=JSON.stringify(!0===i.json?d:i.json))),c.onreadystatechange=function(){4===c.readyState&&setTimeout(e,0)},c.onload=e,c.onerror=t,c.onprogress=function(){},c.onabort=function(){a=!0},c.ontimeout=t,c.open(h,u,!f,i.username,i.password),f||(c.withCredentials=!!i.withCredentials),!f&&0<i.timeout&&(l=setTimeout(function(){var e;a||(a=!0,c.abort("timeout"),(e=new Error("XMLHttpRequest timeout")).code="ETIMEDOUT",t(e))},i.timeout)),c.setRequestHeader)for(n in p)p.hasOwnProperty(n)&&c.setRequestHeader(n,p[n]);else if(i.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return;return 1}(i.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in i&&(c.responseType=i.responseType),"beforeSend"in i&&"function"==typeof i.beforeSend&&i.beforeSend(c),c.send(d||null),c}en.XMLHttpRequest=$t.XMLHttpRequest||function(){},en.XDomainRequest="withCredentials"in new en.XMLHttpRequest?en.XMLHttpRequest:$t.XDomainRequest,function(e,t){for(var n=0;n<e.length;n++)t(e[n])}(["get","put","post","patch","head","delete"],function(i){en["delete"===i?"del":i]=function(e,t,n){return(t=Zt(e,t,n)).method=i.toUpperCase(),tn(t)}}),Jt.default=x;function nn(e,t){var n=new window.WebVTT.Parser(window,window.vttjs,window.WebVTT.StringDecoder()),i=[];n.oncue=function(e){t.addCue(e)},n.onparsingerror=function(e){i.push(e)},n.onflush=function(){t.trigger({type:"loadeddata",target:t})},n.parse(e),0<i.length&&(window.console&&window.console.groupCollapsed&&window.console.groupCollapsed("Text Track parsing errors for "+t.src),i.forEach(function(e){return d.error(e)}),window.console&&window.console.groupEnd&&window.console.groupEnd()),n.flush()}function rn(e,i){var t={uri:e};(e=Ft(e))&&(t.cors=e),(e="use-credentials"===i.tech_.crossOrigin())&&(t.withCredentials=e),Jt(t,Ke(this,function(e,t,n){return e?d.error(e,t):(i.loaded_=!0,void("function"!=typeof window.WebVTT?i.tech_&&i.tech_.any(["vttjsloaded","vttjserror"],function(e){return"vttjserror"!==e.type?nn(n,i):void d.error("vttjs failed to load, stopping trying to process "+i.src)}):nn(n,i)))}))}var sn=function(s){function e(e){var t;if(!(e=void 0===e?{}:e).tech)throw new Error("A tech was not provided.");var e=ct(e,{kind:Wt[e.kind]||"subtitles",language:e.language||e.srclang||""}),n=zt[e.mode]||"disabled",i=e.default;"metadata"!==e.kind&&"chapters"!==e.kind||(n="hidden"),(t=s.call(this,e)||this).tech_=e.tech,t.cues_=[],t.activeCues_=[],t.preload_=!1!==t.tech_.preloadTextTracks;var r=new Vt(t.cues_),o=new Vt(t.activeCues_),a=!1;t.timeupdateHandler=Ke(ft(t),function(){this.tech_.isDisposed()||(this.tech_.isReady_&&(this.activeCues=this.activeCues,a&&(this.trigger("cuechange"),a=!1)),this.rvf_=this.tech_.requestVideoFrameCallback(this.timeupdateHandler))});return t.tech_.one("dispose",function(){t.stopTracking()}),"disabled"!==n&&t.startTracking(),Object.defineProperties(ft(t),{default:{get:function(){return i},set:function(){}},mode:{get:function(){return n},set:function(e){zt[e]&&n!==e&&(n=e,this.preload_||"disabled"===n||0!==this.cues.length||rn(this.src,this),this.stopTracking(),"disabled"!==n&&this.startTracking(),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?r:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return o;for(var e=this.tech_.currentTime(),t=[],n=0,i=this.cues.length;n<i;n++){var r=this.cues[n];(r.startTime<=e&&r.endTime>=e||r.startTime===r.endTime&&r.startTime<=e&&r.startTime+.5>=e)&&t.push(r)}if(a=!1,t.length!==this.activeCues_.length)a=!0;else for(var s=0;s<t.length;s++)-1===this.activeCues_.indexOf(t[s])&&(a=!0);return this.activeCues_=t,o.setCues_(this.activeCues_),o},set:function(){}}}),e.src?(t.src=e.src,t.preload_||(t.loaded_=!0),(t.preload_||"subtitles"!==e.kind&&"captions"!==e.kind)&&rn(t.src,ft(t))):t.loaded_=!0,t}vt(e,s);var t=e.prototype;return t.startTracking=function(){this.rvf_=this.tech_.requestVideoFrameCallback(this.timeupdateHandler)},t.stopTracking=function(){this.rvf_&&(this.tech_.cancelVideoFrameCallback(this.rvf_),this.rvf_=void 0)},t.addCue=function(e){var t=e;if(window.vttjs&&!(e instanceof window.vttjs.VTTCue)){for(var n in t=new window.vttjs.VTTCue(e.startTime,e.endTime,e.text),e)n in t||(t[n]=e[n]);t.id=e.id,t.originalCue_=e}for(var i=this.tech_.textTracks(),r=0;r<i.length;r++)i[r]!==this&&i[r].removeCue(t);this.cues_.push(t),this.cues.setCues_(this.cues_)},t.removeCue=function(e){for(var t=this.cues_.length;t--;){var n=this.cues_[t];if(n===e||n.originalCue_&&n.originalCue_===e){this.cues_.splice(t,1),this.cues.setCues_(this.cues_);break}}},e}(S);sn.prototype.allowedEvents_={cuechange:"cuechange"};P=function(i){function e(e){var t=ct(e=void 0===e?{}:e,{kind:Kt[e.kind]||""}),e=i.call(this,t)||this,n=!1;return Object.defineProperty(ft(e),"enabled",{get:function(){return n},set:function(e){"boolean"==typeof e&&e!==n&&(n=e,this.trigger("enabledchange"))}}),t.enabled&&(e.enabled=t.enabled),e.loaded_=!0,e}return vt(e,i),e}(S),F=function(i){function e(e){var t=ct(e=void 0===e?{}:e,{kind:Ut[e.kind]||""}),e=i.call(this,t)||this,n=!1;return Object.defineProperty(ft(e),"selected",{get:function(){return n},set:function(e){"boolean"==typeof e&&e!==n&&(n=e,this.trigger("selectedchange"))}}),t.selected&&(e.selected=t.selected),e}return vt(e,i),e}(S),H=function(r){function e(e){var t;void 0===e&&(e={});var n=r.call(this)||this,i=new sn(e);return n.kind=i.kind,n.src=i.src,n.srclang=i.language,n.label=i.label,n.default=i.default,Object.defineProperties(ft(n),{readyState:{get:function(){return t}},track:{get:function(){return i}}}),t=0,i.addEventListener("loadeddata",function(){t=2,n.trigger({type:"load",target:ft(n)})}),n}return vt(e,r),e}(qe);H.prototype.allowedEvents_={load:"load"},H.NONE=0,H.LOADING=1,H.LOADED=2,H.ERROR=3;var on={audio:{ListClass:Rt,TrackClass:P,capitalName:"Audio"},video:{ListClass:Bt,TrackClass:F,capitalName:"Video"},text:{ListClass:E,TrackClass:sn,capitalName:"Text"}};Object.keys(on).forEach(function(e){on[e].getterName=e+"Tracks",on[e].privateName=e+"Tracks_"});var an={remoteText:{ListClass:E,TrackClass:sn,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Ht,TrackClass:H,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},ln=g({},on,an);an.names=Object.keys(an),on.names=Object.keys(on),ln.names=[].concat(an.names).concat(on.names);var cn="undefined"!=typeof f?f:"undefined"!=typeof window?window:{},un="undefined"!=typeof document?document:(un=cn["__GLOBAL_DOCUMENT_CACHE@4"])||(cn["__GLOBAL_DOCUMENT_CACHE@4"]={}),Xt=un,hn=Object.create||function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return dn.prototype=e,new dn};function dn(){}function pn(e,t){this.name="ParsingError",this.code=e.code,this.message=t||e.message}function fn(e){function t(e,t,n,i){return 3600*(0|e)+60*(0|t)+(0|n)+(0|i)/1e3}e=e.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);return e?e[3]?t(e[1],e[2],e[3].replace(":",""),e[4]):59<e[1]?t(e[1],e[2],0,e[4]):t(0,e[1],e[2],e[4]):null}function vn(){this.values=hn(null)}function gn(e,t,n,i){var r,s,o=i?e.split(i):[e];for(r in o)"string"==typeof o[r]&&(2===(s=o[r].split(n)).length&&t(s[0],s[1]))}function mn(t,e,o){var n=t;function i(){var e=fn(t);if(null===e)throw new pn(pn.Errors.BadTimeStamp,"Malformed timestamp: "+n);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function r(){t=t.replace(/^\s+/,"")}if(r(),e.startTime=i(),r(),"--\x3e"!==t.substr(0,3))throw new pn(pn.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+n);t=t.substr(3),r(),e.endTime=i(),r(),function(e,t){var s=new vn;gn(e,function(e,t){switch(e){case"region":for(var n=o.length-1;0<=n;n--)if(o[n].id===t){s.set(e,o[n].region);break}break;case"vertical":s.alt(e,t,["rl","lr"]);break;case"line":var i=t.split(","),r=i[0];s.integer(e,r),s.percent(e,r)&&s.set("snapToLines",!1),s.alt(e,r,["auto"]),2===i.length&&s.alt("lineAlign",i[1],["start","center","end"]);break;case"position":i=t.split(","),s.percent(e,i[0]),2===i.length&&s.alt("positionAlign",i[1],["start","center","end"]);break;case"size":s.percent(e,t);break;case"align":s.alt(e,t,["start","center","end","left","right"])}},/:/,/\s/),t.region=s.get("region",null),t.vertical=s.get("vertical","");try{t.line=s.get("line","auto")}catch(e){}t.lineAlign=s.get("lineAlign","start"),t.snapToLines=s.get("snapToLines",!0),t.size=s.get("size",100);try{t.align=s.get("align","center")}catch(e){t.align=s.get("align","middle")}try{t.position=s.get("position","auto")}catch(e){t.position=s.get("position",{start:0,left:0,center:50,middle:50,end:100,right:100},t.align)}t.positionAlign=s.get("positionAlign",{start:"start",left:"start",center:"center",middle:"center",end:"end",right:"end"},t.align)}(t,e)}((pn.prototype=hn(Error.prototype)).constructor=pn).Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},vn.prototype={set:function(e,t){this.get(e)||""===t||(this.values[e]=t)},get:function(e,t,n){return n?this.has(e)?this.values[e]:t[n]:this.has(e)?this.values[e]:t},has:function(e){return e in this.values},alt:function(e,t,n){for(var i=0;i<n.length;++i)if(t===n[i]){this.set(e,t);break}},integer:function(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))},percent:function(e,t){return!!(t.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&0<=(t=parseFloat(t))&&t<=100)&&(this.set(e,t),!0)}};var _n=Xt.createElement&&Xt.createElement("textarea"),yn={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},bn={white:"rgba(255,255,255,1)",lime:"rgba(0,255,0,1)",cyan:"rgba(0,255,255,1)",red:"rgba(255,0,0,1)",yellow:"rgba(255,255,0,1)",magenta:"rgba(255,0,255,1)",blue:"rgba(0,0,255,1)",black:"rgba(0,0,0,1)"},Tn={v:"title",lang:"lang"},kn={rt:"ruby"};function Cn(e,t){for(var n,i,r,s,o,a,l,c,u,h,d=e.document.createElement("div"),p=d,f=[];null!==(n=function(){if(!t)return null;var e=t.match(/^([^<]*)(<[^>]*>?)?/);return e=e[1]||e[2],t=t.substr(e.length),e}());)"<"!==n[0]?p.appendChild(e.document.createTextNode((o=n,_n.innerHTML=o,o=_n.textContent,_n.textContent="",o))):"/"!==n[1]?(s=fn(n.substr(1,n.length-2)))?(i=e.document.createProcessingInstruction("timestamp",s),p.appendChild(i)):(r=n.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/))&&(c=r[1],u=r[3],h=void 0,h=yn[c],(i=h?(h=e.document.createElement(h),(c=Tn[c])&&u&&(h[c]=u.trim()),h):null)&&(a=p,kn[(l=i).localName]&&kn[l.localName]!==a.localName||(r[2]&&((s=r[2].split(".")).forEach(function(e){var t=/^bg_/.test(e),e=t?e.slice(3):e;bn.hasOwnProperty(e)&&(e=bn[e],i.style[t?"background-color":"color"]=e)}),i.className=s.join(" ")),f.push(r[1]),p.appendChild(i),p=i))):f.length&&f[f.length-1]===n.substr(2).replace(">","")&&(f.pop(),p=p.parentNode);return d}var wn=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function En(e){var t=[],n="";if(!e||!e.childNodes)return"ltr";function s(e,t){for(var n=t.childNodes.length-1;0<=n;n--)e.push(t.childNodes[n])}for(s(t,e);n=function e(t){if(!t||!t.length)return null;var n=t.pop(),i=n.textContent||n.innerText;if(i){var r=i.match(/^.*(\n|\r)/);return r?r[t.length=0]:i}return"ruby"===n.tagName?e(t):n.childNodes?(s(t,n),e(t)):void 0}(t);)for(var i=0;i<n.length;i++)if(function(e){for(var t=0;t<wn.length;t++){var n=wn[t];if(e>=n[0]&&e<=n[1])return 1}}(n.charCodeAt(i)))return"rtl";return"ltr"}function Sn(){}function xn(e,t,n){Sn.call(this),this.cue=t,this.cueDiv=Cn(e,t.text);var i={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(i,this.cueDiv),this.div=e.document.createElement("div"),i={direction:En(this.cueDiv),writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===t.align?"center":t.align,font:n.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(i),this.div.appendChild(this.cueDiv);var r=0;switch(t.positionAlign){case"start":r=t.position;break;case"center":r=t.position-t.size/2;break;case"end":r=t.position-t.size}""===t.vertical?this.applyStyles({left:this.formatStyle(r,"%"),width:this.formatStyle(t.size,"%")}):this.applyStyles({top:this.formatStyle(r,"%"),height:this.formatStyle(t.size,"%")}),this.move=function(e){this.applyStyles({top:this.formatStyle(e.top,"px"),bottom:this.formatStyle(e.bottom,"px"),left:this.formatStyle(e.left,"px"),right:this.formatStyle(e.right,"px"),height:this.formatStyle(e.height,"px"),width:this.formatStyle(e.width,"px")})}}function Pn(e){var t,n,i,r;e.div&&(t=e.div.offsetHeight,n=e.div.offsetWidth,i=e.div.offsetTop,r=(r=e.div.childNodes)&&(r=r[0])&&r.getClientRects&&r.getClientRects(),e=e.div.getBoundingClientRect(),r=r?Math.max(r[0]&&r[0].height||0,e.height/r.length):0),this.left=e.left,this.right=e.right,this.top=e.top||i,this.height=e.height||t,this.bottom=e.bottom||i+(e.height||t),this.width=e.width||n,this.lineHeight=void 0!==r?r:e.lineHeight}function jn(e,t,a,l){var n,i=new Pn(t),r=t.cue,s=function(e){if("number"==typeof e.line&&(e.snapToLines||0<=e.line&&e.line<=100))return e.line;if(!e.track||!e.track.textTrackList||!e.track.textTrackList.mediaElement)return-1;for(var t=e.track,n=t.textTrackList,i=0,r=0;r<n.length&&n[r]!==t;r++)"showing"===n[r].mode&&i++;return-1*++i}(r),o=[];if(r.snapToLines){switch(r.vertical){case"":o=["+y","-y"],n="height";break;case"rl":o=["+x","-x"],n="width";break;case"lr":o=["-x","+x"],n="width"}var c=i.lineHeight,u=c*Math.round(s),h=a[n]+c,d=o[0];Math.abs(u)>h&&(u=u<0?-1:1,u*=Math.ceil(h/c)*c),s<0&&(u+=""===r.vertical?a.height:a.width,o=o.reverse()),i.move(d,u)}else{var p=i.lineHeight/a.height*100;switch(r.lineAlign){case"center":s-=p/2;break;case"end":s-=p}switch(r.vertical){case"":t.applyStyles({top:t.formatStyle(s,"%")});break;case"rl":t.applyStyles({left:t.formatStyle(s,"%")});break;case"lr":t.applyStyles({right:t.formatStyle(s,"%")})}o=["+y","-x","+x","-y"],i=new Pn(t)}i=function(e,t){for(var n,i=new Pn(e),r=1,s=0;s<t.length;s++){for(;e.overlapsOppositeAxis(a,t[s])||e.within(a)&&e.overlapsAny(l);)e.move(t[s]);if(e.within(a))return e;var o=e.intersectPercentage(a);o<r&&(n=new Pn(e),r=o),e=new Pn(i)}return n||i}(i,o);t.move(i.toCSSCompatValues(a))}function An(){}Sn.prototype.applyStyles=function(e,t){for(var n in t=t||this.div,e)e.hasOwnProperty(n)&&(t.style[n]=e[n])},Sn.prototype.formatStyle=function(e,t){return 0===e?0:e+t},(xn.prototype=hn(Sn.prototype)).constructor=xn,Pn.prototype.move=function(e,t){switch(t=void 0!==t?t:this.lineHeight,e){case"+x":this.left+=t,this.right+=t;break;case"-x":this.left-=t,this.right-=t;break;case"+y":this.top+=t,this.bottom+=t;break;case"-y":this.top-=t,this.bottom-=t}},Pn.prototype.overlaps=function(e){return this.left<e.right&&this.right>e.left&&this.top<e.bottom&&this.bottom>e.top},Pn.prototype.overlapsAny=function(e){for(var t=0;t<e.length;t++)if(this.overlaps(e[t]))return!0;return!1},Pn.prototype.within=function(e){return this.top>=e.top&&this.bottom<=e.bottom&&this.left>=e.left&&this.right<=e.right},Pn.prototype.overlapsOppositeAxis=function(e,t){switch(t){case"+x":return this.left<e.left;case"-x":return this.right>e.right;case"+y":return this.top<e.top;case"-y":return this.bottom>e.bottom}},Pn.prototype.intersectPercentage=function(e){return Math.max(0,Math.min(this.right,e.right)-Math.max(this.left,e.left))*Math.max(0,Math.min(this.bottom,e.bottom)-Math.max(this.top,e.top))/(this.height*this.width)},Pn.prototype.toCSSCompatValues=function(e){return{top:this.top-e.top,bottom:e.bottom-this.bottom,left:this.left-e.left,right:e.right-this.right,height:this.height,width:this.width}},Pn.getSimpleBoxPosition=function(e){var t=e.div?e.div.offsetHeight:e.tagName?e.offsetHeight:0,n=e.div?e.div.offsetWidth:e.tagName?e.offsetWidth:0,i=e.div?e.div.offsetTop:e.tagName?e.offsetTop:0;return{left:(e=e.div?e.div.getBoundingClientRect():e.tagName?e.getBoundingClientRect():e).left,right:e.right,top:e.top||i,height:e.height||t,bottom:e.bottom||i+(e.height||t),width:e.width||n}},An.StringDecoder=function(){return{decode:function(e){if(!e)return"";if("string"!=typeof e)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}},An.convertCueToDOMTree=function(e,t){return e&&t?Cn(e,t):null};An.processCues=function(i,r,e){if(!i||!r||!e)return null;for(;e.firstChild;)e.removeChild(e.firstChild);var s=i.document.createElement("div");if(s.style.position="absolute",s.style.left="0",s.style.right="0",s.style.top="0",s.style.bottom="0",s.style.margin="1.5%",e.appendChild(s),function(e){for(var t=0;t<e.length;t++)if(e[t].hasBeenReset||!e[t].displayState)return 1}(r)){var o=[],a=Pn.getSimpleBoxPosition(s),l={font:Math.round(.05*a.height*100)/100+"px sans-serif"};!function(){for(var e,t,n=0;n<r.length;n++)t=r[n],e=new xn(i,t,l),s.appendChild(e.div),jn(0,e,a,o),t.displayState=e.div,o.push(Pn.getSimpleBoxPosition(e))}()}else for(var t=0;t<r.length;t++)s.appendChild(r[t].displayState)},(An.Parser=function(e,t,n){n||(n=t,t={}),t=t||{},this.window=e,this.vttjs=t,this.state="INITIAL",this.buffer="",this.decoder=n||new TextDecoder("utf8"),this.regionList=[]}).prototype={reportOrThrowError:function(e){if(!(e instanceof pn))throw e;this.onparsingerror&&this.onparsingerror(e)},parse:function(e){var i=this;function t(){for(var e=i.buffer,t=0;t<e.length&&"\r"!==e[t]&&"\n"!==e[t];)++t;var n=e.substr(0,t);return"\r"===e[t]&&++t,"\n"===e[t]&&++t,i.buffer=e.substr(t),n}function n(e){e.match(/X-TIMESTAMP-MAP/)?gn(e,function(e,t){var n;"X-TIMESTAMP-MAP"===e&&(t=t,n=new vn,gn(t,function(e,t){switch(e){case"MPEGT":n.integer(e+"S",t);break;case"LOCA":n.set(e+"L",fn(t))}},/[^\d]:/,/,/),i.ontimestampmap&&i.ontimestampmap({MPEGTS:n.get("MPEGTS"),LOCAL:n.get("LOCAL")}))},/=/):gn(e,function(e,t){var r;"Region"===e&&(t=t,r=new vn,gn(t,function(e,t){switch(e){case"id":r.set(e,t);break;case"width":r.percent(e,t);break;case"lines":r.integer(e,t);break;case"regionanchor":case"viewportanchor":var n=t.split(",");if(2!==n.length)break;var i=new vn;if(i.percent("x",n[0]),i.percent("y",n[1]),!i.has("x")||!i.has("y"))break;r.set(e+"X",i.get("x")),r.set(e+"Y",i.get("y"));break;case"scroll":r.alt(e,t,["up"])}},/=/,/\s/),r.has("id")&&((t=new(i.vttjs.VTTRegion||i.window.VTTRegion)).width=r.get("width",100),t.lines=r.get("lines",3),t.regionAnchorX=r.get("regionanchorX",0),t.regionAnchorY=r.get("regionanchorY",100),t.viewportAnchorX=r.get("viewportanchorX",0),t.viewportAnchorY=r.get("viewportanchorY",100),t.scroll=r.get("scroll",""),i.onregion&&i.onregion(t),i.regionList.push({id:r.get("id"),region:t})))},/:/)}e&&(i.buffer+=i.decoder.decode(e,{stream:!0}));try{if("INITIAL"===i.state){if(!/\r\n|\n/.test(i.buffer))return this;var r,s=(r=t()).match(/^WEBVTT([ \t].*)?$/);if(!s||!s[0])throw new pn(pn.Errors.BadSignature);i.state="HEADER"}for(var o=!1;i.buffer;){if(!/\r\n|\n/.test(i.buffer))return this;switch(o?o=!1:r=t(),i.state){case"HEADER":/:/.test(r)?n(r):r||(i.state="ID");continue;case"NOTE":r||(i.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(r)){i.state="NOTE";break}if(!r)continue;i.cue=new(i.vttjs.VTTCue||i.window.VTTCue)(0,0,"");try{i.cue.align="center"}catch(e){i.cue.align="middle"}if(i.state="CUE",-1===r.indexOf("--\x3e")){i.cue.id=r;continue}case"CUE":try{mn(r,i.cue,i.regionList)}catch(e){i.reportOrThrowError(e),i.cue=null,i.state="BADCUE";continue}i.state="CUETEXT";continue;case"CUETEXT":var a=-1!==r.indexOf("--\x3e");if(!r||a&&(o=!0)){i.oncue&&i.oncue(i.cue),i.cue=null,i.state="ID";continue}i.cue.text&&(i.cue.text+="\n"),i.cue.text+=r.replace(/\u2028/g,"\n").replace(/u2029/g,"\n");continue;case"BADCUE":r||(i.state="ID");continue}}}catch(e){i.reportOrThrowError(e),"CUETEXT"===i.state&&i.cue&&i.oncue&&i.oncue(i.cue),i.cue=null,i.state="INITIAL"===i.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),!t.cue&&"HEADER"!==t.state||(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new pn(pn.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var In=An,Mn={"":1,lr:1,rl:1},Nn={start:1,center:1,end:1,left:1,right:1,auto:1,"line-left":1,"line-right":1};function On(e){return"string"==typeof e&&(!!Nn[e.toLowerCase()]&&e.toLowerCase())}function Ln(e,t,n){this.hasBeenReset=!1;var i="",r=!1,s=e,o=t,a=n,l=null,c="",u=!0,h="auto",d="start",p="auto",f="auto",v=100,g="center";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return i},set:function(e){i=""+e}},pauseOnExit:{enumerable:!0,get:function(){return r},set:function(e){r=!!e}},startTime:{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e)throw new TypeError("Start time must be set to a number.");s=e,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return o},set:function(e){if("number"!=typeof e)throw new TypeError("End time must be set to a number.");o=e,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return a},set:function(e){a=""+e,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return l},set:function(e){l=e,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return c},set:function(e){e="string"==typeof(e=e)&&(!!Mn[e.toLowerCase()]&&e.toLowerCase());if(!1===e)throw new SyntaxError("Vertical: an invalid or illegal direction string was specified.");c=e,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return u},set:function(e){u=!!e,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return h},set:function(e){if("number"!=typeof e&&"auto"!==e)throw new SyntaxError("Line: an invalid number or illegal string was specified.");h=e,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return d},set:function(e){e=On(e);e&&(d=e,this.hasBeenReset=!0)}},position:{enumerable:!0,get:function(){return p},set:function(e){if(e<0||100<e)throw new Error("Position must be between 0 and 100.");p=e,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return f},set:function(e){e=On(e);e&&(f=e,this.hasBeenReset=!0)}},size:{enumerable:!0,get:function(){return v},set:function(e){if(e<0||100<e)throw new Error("Size must be between 0 and 100.");v=e,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return g},set:function(e){e=On(e);if(!e)throw new SyntaxError("align: an invalid or illegal alignment string was specified.");g=e,this.hasBeenReset=!0}}}),this.displayState=void 0}Ln.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var Dn=Ln,Fn={"":!0,up:!0};function Rn(e){return"number"==typeof e&&0<=e&&e<=100}function Bn(){var t=100,n=3,i=0,r=100,s=0,o=100,a="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!Rn(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return n},set:function(e){if("number"!=typeof e)throw new TypeError("Lines must be set to a number.");n=e}},regionAnchorY:{enumerable:!0,get:function(){return r},set:function(e){if(!Rn(e))throw new Error("RegionAnchorX must be between 0 and 100.");r=e}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(e){if(!Rn(e))throw new Error("RegionAnchorY must be between 0 and 100.");i=e}},viewportAnchorY:{enumerable:!0,get:function(){return o},set:function(e){if(!Rn(e))throw new Error("ViewportAnchorY must be between 0 and 100.");o=e}},viewportAnchorX:{enumerable:!0,get:function(){return s},set:function(e){if(!Rn(e))throw new Error("ViewportAnchorX must be between 0 and 100.");s=e}},scroll:{enumerable:!0,get:function(){return a},set:function(e){e="string"==typeof(e=e)&&(!!Fn[e.toLowerCase()]&&e.toLowerCase());!1===e||(a=e)}}})}var Hn=v(function(e){e=e.exports={WebVTT:In,VTTCue:Dn,VTTRegion:Bn};$t.vttjs=e,$t.WebVTT=e.WebVTT;var t=e.VTTCue,n=e.VTTRegion,i=$t.VTTCue,r=$t.VTTRegion;e.shim=function(){$t.VTTCue=t,$t.VTTRegion=n},e.restore=function(){$t.VTTCue=i,$t.VTTRegion=r},$t.VTTCue||e.shim()});Hn.WebVTT,Hn.VTTCue,Hn.VTTRegion;var Vn=function(i){function n(t,e){var n;return void 0===e&&(e=function(){}),(t=void 0===t?{}:t).reportTouchActivity=!1,(n=i.call(this,null,t,e)||this).onDurationChange_=function(e){return n.onDurationChange(e)},n.trackProgress_=function(e){return n.trackProgress(e)},n.trackCurrentTime_=function(e){return n.trackCurrentTime(e)},n.stopTrackingCurrentTime_=function(e){return n.stopTrackingCurrentTime(e)},n.disposeSourceHandler_=function(e){return n.disposeSourceHandler(e)},n.queuedHanders_=new Set,n.hasStarted_=!1,n.on("playing",function(){this.hasStarted_=!0}),n.on("loadstart",function(){this.hasStarted_=!1}),ln.names.forEach(function(e){e=ln[e];t&&t[e.getterName]&&(n[e.privateName]=t[e.getterName])}),n.featuresProgressEvents||n.manualProgressOn(),n.featuresTimeupdateEvents||n.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(e){!1===t["native"+e+"Tracks"]&&(n["featuresNative"+e+"Tracks"]=!1)}),!1===t.nativeCaptions||!1===t.nativeTextTracks?n.featuresNativeTextTracks=!1:!0!==t.nativeCaptions&&!0!==t.nativeTextTracks||(n.featuresNativeTextTracks=!0),n.featuresNativeTextTracks||n.emulateTextTracks(),n.preloadTextTracks=!1!==t.preloadTextTracks,n.autoRemoteTextTracks_=new ln.text.ListClass,n.initTrackListeners(),t.nativeControlsForTouch||n.emitTapEvents(),n.constructor&&(n.name_=n.constructor.name||"Unknown Tech"),n}vt(n,i);var e=n.prototype;return e.triggerSourceset=function(e){var t=this;this.isReady_||this.one("ready",function(){return t.setTimeout(function(){return t.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},e.manualProgressOn=function(){this.on("durationchange",this.onDurationChange_),this.manualProgress=!0,this.one("ready",this.trackProgress_)},e.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange_)},e.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval(Ke(this,function(){var e=this.bufferedPercent();this.bufferedPercent_!==e&&this.trigger("progress"),1===(this.bufferedPercent_=e)&&this.stopTrackingProgress()}),500)},e.onDurationChange=function(e){this.duration_=this.duration()},e.buffered=function(){return _t(0,0)},e.bufferedPercent=function(){return yt(this.buffered(),this.duration_)},e.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime_),this.on("pause",this.stopTrackingCurrentTime_)},e.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime_),this.off("pause",this.stopTrackingCurrentTime_)},e.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.dispose=function(){this.clearTracks(on.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),i.prototype.dispose.call(this)},e.clearTracks=function(e){var r=this;(e=[].concat(e)).forEach(function(e){for(var t=r[e+"Tracks"]()||[],n=t.length;n--;){var i=t[n];"text"===e&&r.removeRemoteTextTrack(i),t.removeTrack(i)}})},e.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],t=e.length;t--;){var n=e[t];this.removeRemoteTextTrack(n)}},e.reset=function(){},e.crossOrigin=function(){},e.setCrossOrigin=function(){},e.error=function(e){return void 0!==e&&(this.error_=new bt(e),this.trigger("error")),this.error_},e.played=function(){return this.hasStarted_?_t(0,0):_t()},e.play=function(){},e.setScrubbing=function(){},e.scrubbing=function(){},e.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.initTrackListeners=function(){var r=this;on.names.forEach(function(e){function t(){r.trigger(e+"trackchange")}var n=on[e],i=r[n.getterName]();i.addEventListener("removetrack",t),i.addEventListener("addtrack",t),r.on("dispose",function(){i.removeEventListener("removetrack",t),i.removeEventListener("addtrack",t)})})},e.addWebVttScript_=function(){var e,t=this;window.WebVTT||(document.body.contains(this.el())?!this.options_["vtt.js"]&&k(Hn)&&0<Object.keys(Hn).length?this.trigger("vttjsloaded"):((e=document.createElement("script")).src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",e.onload=function(){t.trigger("vttjsloaded")},e.onerror=function(){t.trigger("vttjserror")},this.on("dispose",function(){e.onload=null,e.onerror=null}),window.WebVTT=!0,this.el().parentNode.appendChild(e)):this.ready(this.addWebVttScript_))},e.emulateTextTracks=function(){function t(e){return i.addTrack(e.track)}function n(e){return i.removeTrack(e.track)}var e=this,i=this.textTracks(),r=this.remoteTextTracks();r.on("addtrack",t),r.on("removetrack",n),this.addWebVttScript_();function s(){return e.trigger("texttrackchange")}function o(){s();for(var e=0;e<i.length;e++){var t=i[e];t.removeEventListener("cuechange",s),"showing"===t.mode&&t.addEventListener("cuechange",s)}}o(),i.addEventListener("change",o),i.addEventListener("addtrack",o),i.addEventListener("removetrack",o),this.on("dispose",function(){r.off("addtrack",t),r.off("removetrack",n),i.removeEventListener("change",o),i.removeEventListener("addtrack",o),i.removeEventListener("removetrack",o);for(var e=0;e<i.length;e++)i[e].removeEventListener("cuechange",s)})},e.addTextTrack=function(e,t,n){if(!e)throw new Error("TextTrack kind is required but was not provided");return function(e,t,n,i,r){void 0===r&&(r={});var s=e.textTracks();return r.kind=t,n&&(r.label=n),i&&(r.language=i),r.tech=e,r=new ln.text.TrackClass(r),s.addTrack(r),r}(this,e,t,n)},e.createRemoteTextTrack=function(e){e=ct(e,{tech:this});return new an.remoteTextEl.TrackClass(e)},e.addRemoteTextTrack=function(e,t){var n=this,i=this.createRemoteTextTrack(e=void 0===e?{}:e);return!0!==t&&!1!==t&&(d.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),t=!0),this.remoteTextTrackEls().addTrackElement_(i),this.remoteTextTracks().addTrack(i.track),!0!==t&&this.ready(function(){return n.autoRemoteTextTracks_.addTrack(i.track)}),i},e.removeRemoteTextTrack=function(e){var t=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(t),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},e.getVideoPlaybackQuality=function(){return{}},e.requestPictureInPicture=function(){var e=this.options_.Promise||window.Promise;if(e)return e.reject()},e.disablePictureInPicture=function(){return!0},e.setDisablePictureInPicture=function(){},e.requestVideoFrameCallback=function(e){var t=this,n=Ae++;return this.paused()?(this.queuedHanders_.add(n),this.one("playing",function(){t.queuedHanders_.has(n)&&(t.queuedHanders_.delete(n),e())})):this.requestNamedAnimationFrame(n,e),n},e.cancelVideoFrameCallback=function(e){this.queuedHanders_.has(e)?this.queuedHanders_.delete(e):this.cancelNamedAnimationFrame(e)},e.setPoster=function(){},e.playsinline=function(){},e.setPlaysinline=function(){},e.overrideNativeAudioTracks=function(){},e.overrideNativeVideoTracks=function(){},e.canPlayType=function(){return""},n.canPlayType=function(){return""},n.canPlaySource=function(e,t){return n.canPlayType(e.type)},n.isTech=function(e){return e.prototype instanceof n||e instanceof n||e===n},n.registerTech=function(e,t){if(n.techs_||(n.techs_={}),!n.isTech(t))throw new Error("Tech "+e+" must be a Tech");if(!n.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!n.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return e=lt(e),n.techs_[e]=t,n.techs_[at(e)]=t,"Tech"!==e&&n.defaultTechOrder_.push(e),t},n.getTech=function(e){if(e)return n.techs_&&n.techs_[e]?n.techs_[e]:(e=lt(e),window&&window.videojs&&window.videojs[e]?(d.warn("The "+e+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),window.videojs[e]):void 0)},n}(pt);ln.names.forEach(function(e){var t=ln[e];Vn.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),Vn.prototype.featuresVolumeControl=!0,Vn.prototype.featuresMuteControl=!0,Vn.prototype.featuresFullscreenResize=!1,Vn.prototype.featuresPlaybackRate=!1,Vn.prototype.featuresProgressEvents=!1,Vn.prototype.featuresSourceset=!1,Vn.prototype.featuresTimeupdateEvents=!1,Vn.prototype.featuresNativeTextTracks=!1,Vn.prototype.featuresVideoFrameCallback=!1,Vn.withSourceHandlers=function(r){r.registerSourceHandler=function(e,t){var n=(n=r.sourceHandlers)||(r.sourceHandlers=[]);void 0===t&&(t=n.length),n.splice(t,0,e)},r.canPlayType=function(e){for(var t,n=r.sourceHandlers||[],i=0;i<n.length;i++)if(t=n[i].canPlayType(e))return t;return""},r.selectSourceHandler=function(e,t){for(var n=r.sourceHandlers||[],i=0;i<n.length;i++)if(n[i].canHandleSource(e,t))return n[i];return null},r.canPlaySource=function(e,t){var n=r.selectSourceHandler(e,t);return n?n.canHandleSource(e,t):""};["seekable","seeking","duration"].forEach(function(e){var t=this[e];"function"==typeof t&&(this[e]=function(){return this.sourceHandler_&&this.sourceHandler_[e]?this.sourceHandler_[e].apply(this.sourceHandler_,arguments):t.apply(this,arguments)})},r.prototype),r.prototype.setSource=function(e){var t=r.selectSourceHandler(e,this.options_);t||(r.nativeSourceHandler?t=r.nativeSourceHandler:d.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler_),t!==r.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=t.handleSource(e,this,this.options_),this.one("dispose",this.disposeSourceHandler_)},r.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},pt.registerComponent("Tech",Vn),Vn.registerTech("Tech",Vn),Vn.defaultTechOrder_=[];var Un={},Kn={},Wn={};function zn(e,t,n){e.setTimeout(function(){return function n(i,e,r,s,o,a){void 0===i&&(i={});void 0===e&&(e=[]);void 0===o&&(o=[]);void 0===a&&(a=!1);var t=e,e=t[0],l=t.slice(1);if("string"==typeof e)n(i,Un[e],r,s,o,a);else if(e){var c=Qn(s,e);if(!c.setSource)return o.push(c),n(i,l,r,s,o,a);c.setSource(b({},i),function(e,t){return e?n(i,l,r,s,o,a):(o.push(c),void n(t,i.type===t.type?l:Un[t.type],r,s,o,a))})}else l.length?n(i,l,r,s,o,a):a?r(i,o):n(i,Un["*"],r,s,o,!0)}(t,Un[t.type],n,e)},1)}function qn(e,t,n,i){void 0===i&&(i=null);var r="call"+lt(n),r=e.reduce(Yn(r),i),i=r===Wn,r=i?null:t[n](r);return function(e,t,n,i){for(var r=e.length-1;0<=r;r--){var s=e[r];s[t]&&s[t](i,n)}}(e,n,r,i),r}var Xn={buffered:1,currentTime:1,duration:1,muted:1,played:1,paused:1,seekable:1,volume:1,ended:1},$n={setCurrentTime:1,setMuted:1,setVolume:1},Gn={play:1,pause:1};function Yn(n){return function(e,t){return e===Wn?Wn:t[n]?t[n](e):e}}function Qn(e,t){var n=Kn[e.id()],i=null;if(null==n)return i=t(e),Kn[e.id()]=[[t,i]],i;for(var r=0;r<n.length;r++){var s=n[r],o=s[0],s=s[1];o===t&&(i=s)}return null===i&&(i=t(e),n.push([t,i])),i}function Jn(e){return e=Dt(e=void 0===e?"":e),ei[e.toLowerCase()]||""}function Zn(e){var t;return e=Array.isArray(e)?(t=[],e.forEach(function(e){e=Zn(e),Array.isArray(e)?t=t.concat(e):T(e)&&t.push(e)}),t):"string"==typeof e&&e.trim()?[ti({src:e})]:T(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[ti(e)]:[]}var ei={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",m4a:"audio/mp4",mp3:"audio/mpeg",aac:"audio/aac",caf:"audio/x-caf",flac:"audio/flac",oga:"audio/ogg",wav:"audio/wav",m3u8:"application/x-mpegURL",mpd:"application/dash+xml",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",svg:"image/svg+xml",webp:"image/webp"};function ti(e){var t;return e.type||(t=Jn(e.src))&&(e.type=t),e}x=function(l){function e(e,t,n){var i=ct({createEl:!1},t),n=l.call(this,e,i,n)||this;if(t.playerOptions.sources&&0!==t.playerOptions.sources.length)e.src(t.playerOptions.sources);else for(var r=0,s=t.playerOptions.techOrder;r<s.length;r++){var o=lt(s[r]),a=Vn.getTech(o);if((a=!o?pt.getComponent(o):a)&&a.isSupported()){e.loadTech_(o);break}}return n}return vt(e,l),e}(pt);pt.registerComponent("MediaLoader",x);S=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.options_.controlText&&n.controlText(n.options_.controlText),n.handleMouseOver_=function(e){return n.handleMouseOver(e)},n.handleMouseOut_=function(e){return n.handleMouseOut(e)},n.handleClick_=function(e){return n.handleClick(e)},n.handleKeyDown_=function(e){return n.handleKeyDown(e)},n.emitTapEvents(),n.enable(),n}vt(e,i);var t=e.prototype;return t.createEl=function(e,t,n){void 0===e&&(e="div"),void 0===t&&(t={}),void 0===n&&(n={}),t=b({className:this.buildCSSClass(),tabIndex:0},t),"button"===e&&d.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),n=b({role:"button"},n),this.tabIndex_=t.tabIndex;n=Q(e,t,n);return n.appendChild(Q("span",{className:"vjs-icon-placeholder"},{"aria-hidden":!0})),this.createControlTextEl(n),n},t.dispose=function(){this.controlTextEl_=null,i.prototype.dispose.call(this)},t.createControlTextEl=function(e){return this.controlTextEl_=Q("span",{className:"vjs-control-text"},{"aria-live":"polite"}),e&&e.appendChild(this.controlTextEl_),this.controlText(this.controlText_,e),this.controlTextEl_},t.controlText=function(e,t){if(void 0===t&&(t=this.el()),void 0===e)return this.controlText_||"Need Text";var n=this.localize(e);this.controlText_=e,J(this.controlTextEl_,n),this.nonIconControl||this.player_.options_.noUITitleAttributes||t.setAttribute("title",n)},t.buildCSSClass=function(){return"vjs-control vjs-button "+i.prototype.buildCSSClass.call(this)},t.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),"undefined"!=typeof this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick_),this.on("keydown",this.handleKeyDown_))},t.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),"undefined"!=typeof this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off("mouseover",this.handleMouseOver_),this.off("mouseout",this.handleMouseOut_),this.off(["tap","click"],this.handleClick_),this.off("keydown",this.handleKeyDown_)},t.handleLanguagechange=function(){this.controlText(this.controlText_)},t.handleClick=function(e){this.options_.clickHandler&&this.options_.clickHandler.call(this,arguments)},t.handleKeyDown=function(e){dt.isEventKey(e,"Space")||dt.isEventKey(e,"Enter")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):i.prototype.handleKeyDown.call(this,e)},e}(pt);pt.registerComponent("ClickableComponent",S),pt.registerComponent("PosterImage",function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.update(),n.update_=function(e){return n.update(e)},e.on("posterchange",n.update_),n}vt(e,i);var t=e.prototype;return t.dispose=function(){this.player().off("posterchange",this.update_),i.prototype.dispose.call(this)},t.createEl=function(){return Q("div",{className:"vjs-poster",tabIndex:-1})},t.update=function(e){var t=this.player().poster();this.setSrc(t),t?this.show():this.hide()},t.setSrc=function(e){this.el_.style.backgroundImage=e?'url("'+e+'")':""},t.handleClick=function(e){var t;this.player_.controls()&&(t=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length,!this.player_.tech(!0)||(D||N)&&t||this.player_.tech(!0).focus(),this.player_.paused()?wt(this.player_.play()):this.player_.pause())},e}(S));var ni="#222",ii={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function ri(e,t){var n;if(4===e.length)n=e[1]+e[1]+e[2]+e[2]+e[3]+e[3];else{if(7!==e.length)throw new Error("Invalid color code provided, "+e+"; must be formatted as e.g. #f0e or #f604e2.");n=e.slice(1)}return"rgba("+parseInt(n.slice(0,2),16)+","+parseInt(n.slice(2,4),16)+","+parseInt(n.slice(4,6),16)+","+t+")"}function si(e,t,n){try{e.style[t]=n}catch(e){return}}pt.registerComponent("TextTrackDisplay",function(s){function e(n,e,t){function i(e){return r.updateDisplay(e)}var r=s.call(this,n,e,t)||this;return n.on("loadstart",function(e){return r.toggleDisplay(e)}),n.on("texttrackchange",i),n.on("loadedmetadata",function(e){return r.preselectTrack(e)}),n.ready(Ke(ft(r),function(){if(n.tech_&&n.tech_.featuresNativeTextTracks)this.hide();else{n.on("fullscreenchange",i),n.on("playerresize",i),window.addEventListener("orientationchange",i),n.on("dispose",function(){return window.removeEventListener("orientationchange",i)});for(var e=this.options_.playerOptions.tracks||[],t=0;t<e.length;t++)this.player_.addRemoteTextTrack(e[t],!0);this.preselectTrack()}})),r}vt(e,s);var t=e.prototype;return t.preselectTrack=function(){for(var e,t,n,i={captions:1,subtitles:1},r=this.player_.textTracks(),s=this.player_.cache_.selectedLanguage,o=0;o<r.length;o++){var a=r[o];s&&s.enabled&&s.language&&s.language===a.language&&a.kind in i?n=a.kind!==s.kind&&n||a:s&&!s.enabled?t=e=n=null:a.default&&("descriptions"!==a.kind||e?a.kind in i&&!t&&(t=a):e=a)}n?n.mode="showing":t?t.mode="showing":e&&(e.mode="showing")},t.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},t.createEl=function(){return s.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{translate:"yes","aria-live":"off","aria-atomic":"true"})},t.clearDisplay=function(){"function"==typeof window.WebVTT&&window.WebVTT.processCues(window,[],this.el_)},t.updateDisplay=function(){var e=this.player_.textTracks(),t=this.options_.allowMultipleShowingTracks;if(this.clearDisplay(),t){for(var n=[],i=0;i<e.length;++i){var r=e[i];"showing"===r.mode&&n.push(r)}this.updateForTrack(n)}else{for(var s=null,o=null,a=e.length;a--;){var l=e[a];"showing"===l.mode&&("descriptions"===l.kind?s=l:o=l)}o?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(o)):s&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(s))}},t.updateDisplayState=function(e){for(var t=this.player_.textTrackSettings.getValues(),n=e.activeCues,i=n.length;i--;){var r,s=n[i];s&&(r=s.displayState,t.color&&(r.firstChild.style.color=t.color),t.textOpacity&&si(r.firstChild,"color",ri(t.color||"#fff",t.textOpacity)),t.backgroundColor&&(r.firstChild.style.backgroundColor=t.backgroundColor),t.backgroundOpacity&&si(r.firstChild,"backgroundColor",ri(t.backgroundColor||"#000",t.backgroundOpacity)),t.windowColor&&(t.windowOpacity?si(r,"backgroundColor",ri(t.windowColor,t.windowOpacity)):r.style.backgroundColor=t.windowColor),t.edgeStyle&&("dropshadow"===t.edgeStyle?r.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px "+ni:"raised"===t.edgeStyle?r.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px "+ni:"depressed"===t.edgeStyle?r.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px "+ni:"uniform"===t.edgeStyle&&(r.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px "+ni)),t.fontPercent&&1!==t.fontPercent&&(s=window.parseFloat(r.style.fontSize),r.style.fontSize=s*t.fontPercent+"px",r.style.height="auto",r.style.top="auto"),t.fontFamily&&"default"!==t.fontFamily&&("small-caps"===t.fontFamily?r.firstChild.style.fontVariant="small-caps":r.firstChild.style.fontFamily=ii[t.fontFamily]))}},t.updateForTrack=function(e){if(Array.isArray(e)||(e=[e]),"function"==typeof window.WebVTT&&!e.every(function(e){return!e.activeCues})){for(var t=[],n=0;n<e.length;++n)for(var i=e[n],r=0;r<i.activeCues.length;++r)t.push(i.activeCues[r]);window.WebVTT.processCues(window,t,this.el_);for(var s=0;s<e.length;++s){for(var o=e[s],a=0;a<o.activeCues.length;++a){var l=o.activeCues[a].displayState;te(l,"vjs-text-track-cue"),te(l,"vjs-text-track-cue-"+(o.language||s)),o.language&&ae(l,"lang",o.language)}this.player_.textTrackSettings&&this.updateDisplayState(o)}}},e}(pt)),pt.registerComponent("LoadingSpinner",function(n){function e(){return n.apply(this,arguments)||this}return vt(e,n),e.prototype.createEl=function(){var e=this.player_.isAudio(),t=this.localize(e?"Audio Player":"Video Player"),e=Q("span",{className:"vjs-control-text",textContent:this.localize("{1} is loading.",[t])}),t=n.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return t.appendChild(e),t},e}(pt));var oi=function(t){function e(){return t.apply(this,arguments)||this}vt(e,t);var n=e.prototype;return n.createEl=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n={});n=Q("button",t=b({className:this.buildCSSClass()},t),n=b({type:"button"},n));return n.appendChild(Q("span",{className:"vjs-icon-placeholder"},{"aria-hidden":!0})),this.createControlTextEl(n),n},n.addChild=function(e,t){void 0===t&&(t={});var n=this.constructor.name;return d.warn("Adding an actionable (user controllable) child to a Button ("+n+") is not supported; use a ClickableComponent instead."),pt.prototype.addChild.call(this,e,t)},n.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},n.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},n.handleKeyDown=function(e){dt.isEventKey(e,"Space")||dt.isEventKey(e,"Enter")?e.stopPropagation():t.prototype.handleKeyDown.call(this,e)},e}(S);pt.registerComponent("Button",oi);Rt=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.mouseused_=!1,n.on("mousedown",function(e){return n.handleMouseDown(e)}),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-big-play-button"},t.handleClick=function(e){var t=this.player_.play();if(this.mouseused_&&e.clientX&&e.clientY){var n=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length;return wt(t),void(!this.player_.tech(!0)||(D||N)&&n||this.player_.tech(!0).focus())}var n=this.player_.getChild("controlBar"),i=n&&n.getChild("playToggle");i?(n=function(){return i.focus()},Ct(t)?t.then(n,function(){}):this.setTimeout(n,1)):this.player_.tech(!0).focus()},t.handleKeyDown=function(e){this.mouseused_=!1,i.prototype.handleKeyDown.call(this,e)},t.handleMouseDown=function(e){this.mouseused_=!0},e}(oi);Rt.prototype.controlText_="Play Video",pt.registerComponent("BigPlayButton",Rt),pt.registerComponent("CloseButton",function(n){function e(e,t){e=n.call(this,e,t)||this;return e.controlText(t&&t.controlText||e.localize("Close")),e}vt(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-close-button "+n.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.trigger({type:"close",bubbles:!1})},t.handleKeyDown=function(e){dt.isEventKey(e,"Esc")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):n.prototype.handleKeyDown.call(this,e)},e}(oi));Bt=function(i){function e(e,t){var n=i.call(this,e,t=void 0===t?{}:t)||this;return t.replay=void 0===t.replay||t.replay,n.on(e,"play",function(e){return n.handlePlay(e)}),n.on(e,"pause",function(e){return n.handlePause(e)}),t.replay&&n.on(e,"ended",function(e){return n.handleEnded(e)}),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-play-control "+i.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.player_.paused()?wt(this.player_.play()):this.player_.pause()},t.handleSeeked=function(e){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(e):this.handlePlay(e)},t.handlePlay=function(e){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},t.handlePause=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},t.handleEnded=function(e){var t=this;this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",function(e){return t.handleSeeked(e)})},e}(oi);Bt.prototype.controlText_="Play",pt.registerComponent("PlayToggle",Bt);function ai(e,t){e=e<0?0:e;var n=Math.floor(e%60),i=Math.floor(e/60%60),r=Math.floor(e/3600),s=Math.floor(t/60%60),t=Math.floor(t/3600);return(r=0<(r=isNaN(e)||e===1/0?i=n="-":r)||0<t?r+":":"")+(i=((r||10<=s)&&i<10?"0"+i:i)+":")+(n=n<10?"0"+n:n)}var li=ai;function ci(e,t){return li(e,t=void 0===t?e:t)}E=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on(e,["timeupdate","ended"],function(e){return n.updateContent(e)}),n.updateTextNode_(),n}vt(e,i);var t=e.prototype;return t.createEl=function(){var e=this.buildCSSClass(),t=i.prototype.createEl.call(this,"div",{className:e+" vjs-time-control vjs-control"}),n=Q("span",{className:"vjs-control-text",textContent:this.localize(this.labelText_)+" "},{role:"presentation"});return t.appendChild(n),this.contentEl_=Q("span",{className:e+"-display"},{"aria-live":"off",role:"presentation"}),t.appendChild(this.contentEl_),t},t.dispose=function(){this.contentEl_=null,this.textNode_=null,i.prototype.dispose.call(this)},t.updateTextNode_=function(e){var t=this;e=ci(e=void 0===e?0:e),this.formattedTime_!==e&&(this.formattedTime_=e,this.requestNamedAnimationFrame("TimeDisplay#updateTextNode_",function(){var e;t.contentEl_&&((e=t.textNode_)&&t.contentEl_.firstChild!==e&&(e=null,d.warn("TimeDisplay#updateTextnode_: Prevented replacement of text node element since it was no longer a child of this node. Appending a new node instead.")),t.textNode_=document.createTextNode(t.formattedTime_),t.textNode_&&(e?t.contentEl_.replaceChild(t.textNode_,e):t.contentEl_.appendChild(t.textNode_)))}))},t.updateContent=function(e){},e}(pt);E.prototype.labelText_="Time",E.prototype.controlText_="Time",pt.registerComponent("TimeDisplay",E);Ht=function(e){function t(){return e.apply(this,arguments)||this}vt(t,e);var n=t.prototype;return n.buildCSSClass=function(){return"vjs-current-time"},n.updateContent=function(e){var t=this.player_.ended()?this.player_.duration():this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateTextNode_(t)},t}(E);Ht.prototype.labelText_="Current Time",Ht.prototype.controlText_="Current Time",pt.registerComponent("CurrentTimeDisplay",Ht);H=function(i){function e(e,t){var n=i.call(this,e,t)||this,t=function(e){return n.updateContent(e)};return n.on(e,"durationchange",t),n.on(e,"loadstart",t),n.on(e,"loadedmetadata",t),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-duration"},t.updateContent=function(e){var t=this.player_.duration();this.updateTextNode_(t)},e}(E);H.prototype.labelText_="Duration",H.prototype.controlText_="Duration",pt.registerComponent("DurationDisplay",H),pt.registerComponent("TimeDivider",function(i){function e(){return i.apply(this,arguments)||this}return vt(e,i),e.prototype.createEl=function(){var e=i.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider"},{"aria-hidden":!0}),t=i.prototype.createEl.call(this,"div"),n=i.prototype.createEl.call(this,"span",{textContent:"/"});return t.appendChild(n),e.appendChild(t),e},e}(pt));f=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on(e,"durationchange",function(e){return n.updateContent(e)}),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-remaining-time"},t.createEl=function(){var e=i.prototype.createEl.call(this);return!1!==this.options_.displayNegative&&e.insertBefore(Q("span",{},{"aria-hidden":!0},"-"),this.contentEl_),e},t.updateContent=function(e){var t;"number"==typeof this.player_.duration()&&(t=this.player_.ended()?0:this.player_.remainingTimeDisplay?this.player_.remainingTimeDisplay():this.player_.remainingTime(),this.updateTextNode_(t))},e}(E);f.prototype.labelText_="Remaining Time",f.prototype.controlText_="Remaining Time",pt.registerComponent("RemainingTimeDisplay",f),pt.registerComponent("LiveDisplay",function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.updateShowing(),n.on(n.player(),"durationchange",function(e){return n.updateShowing(e)}),n}vt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=Q("div",{className:"vjs-live-display"},{"aria-live":"off"}),this.contentEl_.appendChild(Q("span",{className:"vjs-control-text",textContent:this.localize("Stream Type")+" "})),this.contentEl_.appendChild(document.createTextNode(this.localize("LIVE"))),e.appendChild(this.contentEl_),e},t.dispose=function(){this.contentEl_=null,i.prototype.dispose.call(this)},t.updateShowing=function(e){this.player().duration()===1/0?this.show():this.hide()},e}(pt));cn=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.updateLiveEdgeStatus(),n.player_.liveTracker&&(n.updateLiveEdgeStatusHandler_=function(e){return n.updateLiveEdgeStatus(e)},n.on(n.player_.liveTracker,"liveedgechange",n.updateLiveEdgeStatusHandler_)),n}vt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this,"button",{className:"vjs-seek-to-live-control vjs-control"});return this.textEl_=Q("span",{className:"vjs-seek-to-live-text",textContent:this.localize("LIVE")},{"aria-hidden":"true"}),e.appendChild(this.textEl_),e},t.updateLiveEdgeStatus=function(){!this.player_.liveTracker||this.player_.liveTracker.atLiveEdge()?(this.setAttribute("aria-disabled",!0),this.addClass("vjs-at-live-edge"),this.controlText("Seek to live, currently playing live")):(this.setAttribute("aria-disabled",!1),this.removeClass("vjs-at-live-edge"),this.controlText("Seek to live, currently behind live"))},t.handleClick=function(){this.player_.liveTracker.seekToLiveEdge()},t.dispose=function(){this.player_.liveTracker&&this.off(this.player_.liveTracker,"liveedgechange",this.updateLiveEdgeStatusHandler_),this.textEl_=null,i.prototype.dispose.call(this)},e}(oi);cn.prototype.controlText_="Seek to live, currently playing live",pt.registerComponent("SeekToLive",cn);function ui(e,t,n){return e=Number(e),Math.min(n,Math.max(t,isNaN(e)?t:e))}un=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.handleMouseDown_=function(e){return n.handleMouseDown(e)},n.handleMouseUp_=function(e){return n.handleMouseUp(e)},n.handleKeyDown_=function(e){return n.handleKeyDown(e)},n.handleClick_=function(e){return n.handleClick(e)},n.handleMouseMove_=function(e){return n.handleMouseMove(e)},n.update_=function(e){return n.update(e)},n.bar=n.getChild(n.options_.barName),n.vertical(!!n.options_.vertical),n.enable(),n}vt(e,i);var t=e.prototype;return t.enabled=function(){return this.enabled_},t.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown_),this.on("touchstart",this.handleMouseDown_),this.on("keydown",this.handleKeyDown_),this.on("click",this.handleClick_),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},t.disable=function(){var e;this.enabled()&&(e=this.bar.el_.ownerDocument,this.off("mousedown",this.handleMouseDown_),this.off("touchstart",this.handleMouseDown_),this.off("keydown",this.handleKeyDown_),this.off("click",this.handleClick_),this.off(this.player_,"controlsvisible",this.update_),this.off(e,"mousemove",this.handleMouseMove_),this.off(e,"mouseup",this.handleMouseUp_),this.off(e,"touchmove",this.handleMouseMove_),this.off(e,"touchend",this.handleMouseUp_),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1)},t.createEl=function(e,t,n){return void 0===n&&(n={}),(t=void 0===t?{}:t).className=t.className+" vjs-slider",t=b({tabIndex:0},t),n=b({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},n),i.prototype.createEl.call(this,e,t,n)},t.handleMouseDown=function(e){var t=this.bar.el_.ownerDocument;"mousedown"===e.type&&e.preventDefault(),"touchstart"!==e.type||O||e.preventDefault(),ce(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(t,"mousemove",this.handleMouseMove_),this.on(t,"mouseup",this.handleMouseUp_),this.on(t,"touchmove",this.handleMouseMove_),this.on(t,"touchend",this.handleMouseUp_),this.handleMouseMove(e,!0)},t.handleMouseMove=function(e){},t.handleMouseUp=function(){var e=this.bar.el_.ownerDocument;ue(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(e,"mousemove",this.handleMouseMove_),this.off(e,"mouseup",this.handleMouseUp_),this.off(e,"touchmove",this.handleMouseMove_),this.off(e,"touchend",this.handleMouseUp_),this.update()},t.update=function(){var t=this;if(this.el_&&this.bar){var n=this.getProgress();return n===this.progress_?n:(this.progress_=n,this.requestNamedAnimationFrame("Slider#update",function(){var e=t.vertical()?"height":"width";t.bar.el().style[e]=(100*n).toFixed(2)+"%"}),n)}},t.getProgress=function(){return Number(ui(this.getPercent(),0,1).toFixed(4))},t.calculateDistance=function(e){e=pe(this.el_,e);return this.vertical()?e.y:e.x},t.handleKeyDown=function(e){dt.isEventKey(e,"Left")||dt.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepBack()):dt.isEventKey(e,"Right")||dt.isEventKey(e,"Up")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):i.prototype.handleKeyDown.call(this,e)},t.handleClick=function(e){e.stopPropagation(),e.preventDefault()},t.vertical=function(e){if(void 0===e)return this.vertical_||!1;this.vertical_=!!e,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},e}(pt);pt.registerComponent("Slider",un);function hi(e,t){return ui(e/t*100,0,100).toFixed(2)+"%"}pt.registerComponent("LoadProgressBar",function(r){function e(e,t){var n=r.call(this,e,t)||this;return n.partEls_=[],n.on(e,"progress",function(e){return n.update(e)}),n}vt(e,r);var t=e.prototype;return t.createEl=function(){var e=r.prototype.createEl.call(this,"div",{className:"vjs-load-progress"}),t=Q("span",{className:"vjs-control-text"}),n=Q("span",{textContent:this.localize("Loaded")}),i=document.createTextNode(": ");return this.percentageEl_=Q("span",{className:"vjs-control-text-loaded-percentage",textContent:"0%"}),e.appendChild(t),t.appendChild(n),t.appendChild(i),t.appendChild(this.percentageEl_),e},t.dispose=function(){this.partEls_=null,this.percentageEl_=null,r.prototype.dispose.call(this)},t.update=function(e){var c=this;this.requestNamedAnimationFrame("LoadProgressBar#update",function(){var e=c.player_.liveTracker,t=c.player_.buffered(),e=e&&e.isLive()?e.seekableEnd():c.player_.duration(),n=c.player_.bufferedEnd(),i=c.partEls_,e=hi(n,e);c.percent_!==e&&(c.el_.style.width=e,J(c.percentageEl_,e),c.percent_=e);for(var r=0;r<t.length;r++){var s=t.start(r),o=t.end(r),a=i[r];a||(a=c.el_.appendChild(Q()),i[r]=a),a.dataset.start===s&&a.dataset.end===o||(a.dataset.start=s,a.dataset.end=o,a.style.left=hi(s,n),a.style.width=hi(o-s,n))}for(var l=i.length;l>t.length;l--)c.el_.removeChild(i[l-1]);i.length=t.length})},e}(pt)),pt.registerComponent("TimeTooltip",function(n){function e(e,t){t=n.call(this,e,t)||this;return t.update=We(Ke(ft(t),t.update),30),t}vt(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"},{"aria-hidden":"true"})},t.update=function(e,t,n){var i=de(this.el_),r=he(this.player_.el()),s=e.width*t;r&&i&&(t=e.left-r.left+s,r=e.width-s+(r.right-e.right),t<(e=i.width/2)?e+=e-t:r<e&&(e=r),e<0?e=0:e>i.width&&(e=i.width),e=Math.round(e),this.el_.style.right="-"+e+"px",this.write(n))},t.write=function(e){J(this.el_,e)},t.updateTime=function(i,r,s,o){var a=this;this.requestNamedAnimationFrame("TimeTooltip#updateTime",function(){var e,t,n=a.player_.duration();n=a.player_.liveTracker&&a.player_.liveTracker.isLive()?((t=(e=a.player_.liveTracker.liveWindow())-r*e)<1?"":"-")+ci(t,e):ci(s,n),a.update(i,r,n),o&&o()})},e}(pt));Xt=function(n){function e(e,t){t=n.call(this,e,t)||this;return t.update=We(Ke(ft(t),t.update),30),t}vt(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar"},{"aria-hidden":"true"})},t.update=function(e,t){var n,i=this.getChild("timeTooltip");i&&(n=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime(),i.updateTime(e,t,n))},e}(pt);Xt.prototype.options_={children:[]},U||j||Xt.prototype.options_.children.push("timeTooltip"),pt.registerComponent("PlayProgressBar",Xt);x=function(n){function e(e,t){t=n.call(this,e,t)||this;return t.update=We(Ke(ft(t),t.update),30),t}vt(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},t.update=function(e,t){var n=this,i=t*this.player_.duration();this.getChild("timeTooltip").updateTime(e,t,i,function(){n.el_.style.left=e.width*t+"px"})},e}(pt);x.prototype.options_={children:["timeTooltip"]},pt.registerComponent("MouseTimeDisplay",x);Rt=function(s){function e(e,t){t=s.call(this,e,t)||this;return t.setEventHandlers_(),t}vt(e,s);var t=e.prototype;return t.setEventHandlers_=function(){var t=this;this.update_=Ke(this,this.update),this.update=We(this.update_,30),this.on(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.updateInterval=null,this.enableIntervalHandler_=function(e){return t.enableInterval_(e)},this.disableIntervalHandler_=function(e){return t.disableInterval_(e)},this.on(this.player_,["playing"],this.enableIntervalHandler_),this.on(this.player_,["ended","pause","waiting"],this.disableIntervalHandler_),"hidden"in document&&"visibilityState"in document&&this.on(document,"visibilitychange",this.toggleVisibility_)},t.toggleVisibility_=function(e){"hidden"===document.visibilityState?(this.cancelNamedAnimationFrame("SeekBar#update"),this.cancelNamedAnimationFrame("Slider#update"),this.disableInterval_(e)):(this.player_.ended()||this.player_.paused()||this.enableInterval_(),this.update())},t.enableInterval_=function(){this.updateInterval||(this.updateInterval=this.setInterval(this.update,30))},t.disableInterval_=function(e){this.player_.liveTracker&&this.player_.liveTracker.isLive()&&e&&"ended"!==e.type||this.updateInterval&&(this.clearInterval(this.updateInterval),this.updateInterval=null)},t.createEl=function(){return s.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},t.update=function(e){var i=this;if("hidden"!==document.visibilityState){var r=s.prototype.update.call(this);return this.requestNamedAnimationFrame("SeekBar#update",function(){var e=i.player_.ended()?i.player_.duration():i.getCurrentTime_(),t=i.player_.liveTracker,n=i.player_.duration();t&&t.isLive()&&(n=i.player_.liveTracker.liveCurrentTime()),i.percent_!==r&&(i.el_.setAttribute("aria-valuenow",(100*r).toFixed(2)),i.percent_=r),i.currentTime_===e&&i.duration_===n||(i.el_.setAttribute("aria-valuetext",i.localize("progress bar timing: currentTime={1} duration={2}",[ci(e,n),ci(n,n)],"{1} of {2}")),i.currentTime_=e,i.duration_=n),i.bar&&i.bar.update(he(i.el()),i.getProgress())}),r}},t.userSeek_=function(e){this.player_.liveTracker&&this.player_.liveTracker.isLive()&&this.player_.liveTracker.nextSeekedFromUser(),this.player_.currentTime(e)},t.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},t.getPercent=function(){var e,t=this.getCurrentTime_(),n=this.player_.liveTracker;return n&&n.isLive()?(e=(t-n.seekableStart())/n.liveWindow(),n.atLiveEdge()&&(e=1)):e=t/this.player_.duration(),e},t.handleMouseDown=function(e){ye(e)&&(e.stopPropagation(),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),s.prototype.handleMouseDown.call(this,e))},t.handleMouseMove=function(e,t){if(void 0===t&&(t=!1),ye(e)){t||this.player_.scrubbing()||this.player_.scrubbing(!0);var n=this.calculateDistance(e),i=this.player_.liveTracker;if(i&&i.isLive()){if(.99<=n)return void i.seekToLiveEdge();var r,t=i.seekableStart(),e=i.liveCurrentTime();if((r=(r=e<=(r=t+n*i.liveWindow())?e:r)<=t?t+.1:r)===1/0)return}else(r=n*this.player_.duration())===this.player_.duration()&&(r-=.1);this.userSeek_(r)}},t.enable=function(){s.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},t.disable=function(){s.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},t.handleMouseUp=function(e){s.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying?wt(this.player_.play()):this.update_()},t.stepForward=function(){this.userSeek_(this.player_.currentTime()+5)},t.stepBack=function(){this.userSeek_(this.player_.currentTime()-5)},t.handleAction=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.handleKeyDown=function(e){var t,n=this.player_.liveTracker;dt.isEventKey(e,"Space")||dt.isEventKey(e,"Enter")?(e.preventDefault(),e.stopPropagation(),this.handleAction(e)):dt.isEventKey(e,"Home")?(e.preventDefault(),e.stopPropagation(),this.userSeek_(0)):dt.isEventKey(e,"End")?(e.preventDefault(),e.stopPropagation(),n&&n.isLive()?this.userSeek_(n.liveCurrentTime()):this.userSeek_(this.player_.duration())):/^[0-9]$/.test(dt(e))?(e.preventDefault(),e.stopPropagation(),t=10*(dt.codes[dt(e)]-dt.codes[0])/100,n&&n.isLive()?this.userSeek_(n.seekableStart()+n.liveWindow()*t):this.userSeek_(this.player_.duration()*t)):dt.isEventKey(e,"PgDn")?(e.preventDefault(),e.stopPropagation(),this.userSeek_(this.player_.currentTime()-60)):dt.isEventKey(e,"PgUp")?(e.preventDefault(),e.stopPropagation(),this.userSeek_(this.player_.currentTime()+60)):s.prototype.handleKeyDown.call(this,e)},t.dispose=function(){this.disableInterval_(),this.off(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.off(this.player_.liveTracker,"liveedgechange",this.update),this.off(this.player_,["playing"],this.enableIntervalHandler_),this.off(this.player_,["ended","pause","waiting"],this.disableIntervalHandler_),"hidden"in document&&"visibilityState"in document&&this.off(document,"visibilitychange",this.toggleVisibility_),s.prototype.dispose.call(this)},e}(un);Rt.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},U||j||Rt.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),pt.registerComponent("SeekBar",Rt);Bt=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.handleMouseMove=We(Ke(ft(n),n.handleMouseMove),30),n.throttledHandleMouseSeek=We(Ke(ft(n),n.handleMouseSeek),30),n.handleMouseUpHandler_=function(e){return n.handleMouseUp(e)},n.handleMouseDownHandler_=function(e){return n.handleMouseDown(e)},n.enable(),n}vt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},t.handleMouseMove=function(e){var t,n,i,r,s=this.getChild("seekBar");s&&(t=s.getChild("playProgressBar"),n=s.getChild("mouseTimeDisplay"),(t||n)&&(r=de(i=s.el()),e=pe(i,e).x,e=ui(e,0,1),n&&n.update(r,e),t&&t.update(r,s.getProgress())))},t.handleMouseSeek=function(e){var t=this.getChild("seekBar");t&&t.handleMouseMove(e)},t.enabled=function(){return this.enabled_},t.disable=function(){var e;this.children().forEach(function(e){return e.disable&&e.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDownHandler_),this.off(this.el_,"mousemove",this.handleMouseMove),this.removeListenersAddedOnMousedownAndTouchstart(),this.addClass("disabled"),this.enabled_=!1,this.player_.scrubbing()&&(e=this.getChild("seekBar"),this.player_.scrubbing(!1),e.videoWasPlaying&&wt(this.player_.play())))},t.enable=function(){this.children().forEach(function(e){return e.enable&&e.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDownHandler_),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},t.removeListenersAddedOnMousedownAndTouchstart=function(){var e=this.el_.ownerDocument;this.off(e,"mousemove",this.throttledHandleMouseSeek),this.off(e,"touchmove",this.throttledHandleMouseSeek),this.off(e,"mouseup",this.handleMouseUpHandler_),this.off(e,"touchend",this.handleMouseUpHandler_)},t.handleMouseDown=function(e){var t=this.el_.ownerDocument,n=this.getChild("seekBar");n&&n.handleMouseDown(e),this.on(t,"mousemove",this.throttledHandleMouseSeek),this.on(t,"touchmove",this.throttledHandleMouseSeek),this.on(t,"mouseup",this.handleMouseUpHandler_),this.on(t,"touchend",this.handleMouseUpHandler_)},t.handleMouseUp=function(e){var t=this.getChild("seekBar");t&&t.handleMouseUp(e),this.removeListenersAddedOnMousedownAndTouchstart()},e}(pt);Bt.prototype.options_={children:["seekBar"]},pt.registerComponent("ProgressControl",Bt);Ht=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on(e,["enterpictureinpicture","leavepictureinpicture"],function(e){return n.handlePictureInPictureChange(e)}),n.on(e,["disablepictureinpicturechanged","loadedmetadata"],function(e){return n.handlePictureInPictureEnabledChange(e)}),n.on(e,["loadedmetadata","audioonlymodechange","audiopostermodechange"],function(){"audio"===e.currentType().substring(0,5)||e.audioPosterMode()||e.audioOnlyMode()?(e.isInPictureInPicture()&&e.exitPictureInPicture(),n.hide()):n.show()}),n.disable(),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-picture-in-picture-control "+i.prototype.buildCSSClass.call(this)},t.handlePictureInPictureEnabledChange=function(){document.pictureInPictureEnabled&&!1===this.player_.disablePictureInPicture()?this.enable():this.disable()},t.handlePictureInPictureChange=function(e){this.player_.isInPictureInPicture()?this.controlText("Exit Picture-in-Picture"):this.controlText("Picture-in-Picture"),this.handlePictureInPictureEnabledChange()},t.handleClick=function(e){this.player_.isInPictureInPicture()?this.player_.exitPictureInPicture():this.player_.requestPictureInPicture()},e}(oi);Ht.prototype.controlText_="Picture-in-Picture",pt.registerComponent("PictureInPictureToggle",Ht);H=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on(e,"fullscreenchange",function(e){return n.handleFullscreenChange(e)}),!1===document[e.fsApi_.fullscreenEnabled]&&n.disable(),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-fullscreen-control "+i.prototype.buildCSSClass.call(this)},t.handleFullscreenChange=function(e){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},t.handleClick=function(e){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(oi);H.prototype.controlText_="Fullscreen",pt.registerComponent("FullscreenToggle",H);pt.registerComponent("VolumeLevel",function(t){function e(){return t.apply(this,arguments)||this}return vt(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,"div",{className:"vjs-volume-level"});return e.appendChild(t.prototype.createEl.call(this,"span",{className:"vjs-control-text"})),e},e}(pt)),pt.registerComponent("VolumeLevelTooltip",function(n){function e(e,t){t=n.call(this,e,t)||this;return t.update=We(Ke(ft(t),t.update),30),t}vt(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-volume-tooltip"},{"aria-hidden":"true"})},t.update=function(e,t,n,i){if(!n){var r=he(this.el_),s=he(this.player_.el()),n=e.width*t;if(!s||!r)return;t=e.left-s.left+n,s=e.width-n+(s.right-e.right),e=r.width/2;t<e?e+=e-t:s<e&&(e=s),e<0?e=0:e>r.width&&(e=r.width),this.el_.style.right="-"+e+"px"}this.write(i+"%")},t.write=function(e){J(this.el_,e)},t.updateVolume=function(e,t,n,i,r){var s=this;this.requestNamedAnimationFrame("VolumeLevelTooltip#updateVolume",function(){s.update(e,t,n,i.toFixed(0)),r&&r()})},e}(pt));E=function(n){function e(e,t){t=n.call(this,e,t)||this;return t.update=We(Ke(ft(t),t.update),30),t}vt(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},t.update=function(e,t,n){var i=this,r=100*t;this.getChild("volumeLevelTooltip").updateVolume(e,t,n,r,function(){n?i.el_.style.bottom=e.height*t+"px":i.el_.style.left=e.width*t+"px"})},e}(pt);E.prototype.options_={children:["volumeLevelTooltip"]},pt.registerComponent("MouseVolumeLevelDisplay",E);f=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on("slideractive",function(e){return n.updateLastVolume_(e)}),n.on(e,"volumechange",function(e){return n.updateARIAAttributes(e)}),e.ready(function(){return n.updateARIAAttributes()}),n}vt(e,i);var t=e.prototype;return t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},t.handleMouseDown=function(e){ye(e)&&i.prototype.handleMouseDown.call(this,e)},t.handleMouseMove=function(e){var t,n,i,r=this.getChild("mouseVolumeLevelDisplay");r&&(t=he(i=this.el()),n=this.vertical(),i=pe(i,e),i=n?i.y:i.x,i=ui(i,0,1),r.update(t,i,n)),ye(e)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(e)))},t.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},t.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},t.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},t.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},t.updateARIAAttributes=function(e){var t=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",t),this.el_.setAttribute("aria-valuetext",t+"%")},t.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},t.updateLastVolume_=function(){var e=this,t=this.player_.volume();this.one("sliderinactive",function(){0===e.player_.volume()&&e.player_.lastVolume_(t)})},e}(un);f.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},U||j||f.prototype.options_.children.splice(0,0,"mouseVolumeLevelDisplay"),f.prototype.playerEvent="volumechange",pt.registerComponent("VolumeBar",f);cn=function(s){function e(e,t){var n,i,r;return(t=void 0===t?{}:t).vertical=t.vertical||!1,"undefined"!=typeof t.volumeBar&&!k(t.volumeBar)||(t.volumeBar=t.volumeBar||{},t.volumeBar.vertical=t.vertical),n=s.call(this,e,t)||this,i=ft(n),(r=e).tech_&&!r.tech_.featuresVolumeControl&&i.addClass("vjs-hidden"),i.on(r,"loadstart",function(){r.tech_.featuresVolumeControl?i.removeClass("vjs-hidden"):i.addClass("vjs-hidden")}),n.throttledHandleMouseMove=We(Ke(ft(n),n.handleMouseMove),30),n.handleMouseUpHandler_=function(e){return n.handleMouseUp(e)},n.on("mousedown",function(e){return n.handleMouseDown(e)}),n.on("touchstart",function(e){return n.handleMouseDown(e)}),n.on("mousemove",function(e){return n.handleMouseMove(e)}),n.on(n.volumeBar,["focus","slideractive"],function(){n.volumeBar.addClass("vjs-slider-active"),n.addClass("vjs-slider-active"),n.trigger("slideractive")}),n.on(n.volumeBar,["blur","sliderinactive"],function(){n.volumeBar.removeClass("vjs-slider-active"),n.removeClass("vjs-slider-active"),n.trigger("sliderinactive")}),n}vt(e,s);var t=e.prototype;return t.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),s.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},t.handleMouseDown=function(e){var t=this.el_.ownerDocument;this.on(t,"mousemove",this.throttledHandleMouseMove),this.on(t,"touchmove",this.throttledHandleMouseMove),this.on(t,"mouseup",this.handleMouseUpHandler_),this.on(t,"touchend",this.handleMouseUpHandler_)},t.handleMouseUp=function(e){var t=this.el_.ownerDocument;this.off(t,"mousemove",this.throttledHandleMouseMove),this.off(t,"touchmove",this.throttledHandleMouseMove),this.off(t,"mouseup",this.handleMouseUpHandler_),this.off(t,"touchend",this.handleMouseUpHandler_)},t.handleMouseMove=function(e){this.volumeBar.handleMouseMove(e)},e}(pt);cn.prototype.options_={children:["volumeBar"]},pt.registerComponent("VolumeControl",cn);Xt=function(s){function e(e,t){var n,i,r=s.call(this,e,t)||this;return n=ft(r),(i=e).tech_&&!i.tech_.featuresMuteControl&&n.addClass("vjs-hidden"),n.on(i,"loadstart",function(){i.tech_.featuresMuteControl?n.removeClass("vjs-hidden"):n.addClass("vjs-hidden")}),r.on(e,["loadstart","volumechange"],function(e){return r.update(e)}),r}vt(e,s);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-mute-control "+s.prototype.buildCSSClass.call(this)},t.handleClick=function(e){var t=this.player_.volume(),n=this.player_.lastVolume_();0===t?(this.player_.volume(n<.1?.1:n),this.player_.muted(!1)):this.player_.muted(!this.player_.muted())},t.update=function(e){this.updateIcon_(),this.updateControlText_()},t.updateIcon_=function(){var e=this.player_.volume(),t=3;U&&this.player_.tech_&&this.player_.tech_.el_&&this.player_.muted(this.player_.tech_.el_.muted),0===e||this.player_.muted()?t=0:e<.33?t=1:e<.67&&(t=2);for(var n=0;n<4;n++)ne(this.el_,"vjs-vol-"+n);te(this.el_,"vjs-vol-"+t)},t.updateControlText_=function(){var e=this.player_.muted()||0===this.player_.volume()?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(oi);Xt.prototype.controlText_="Mute",pt.registerComponent("MuteToggle",Xt);x=function(i){function e(e,t){var n;return"undefined"!=typeof(t=void 0===t?{}:t).inline?t.inline=t.inline:t.inline=!0,"undefined"!=typeof t.volumeControl&&!k(t.volumeControl)||(t.volumeControl=t.volumeControl||{},t.volumeControl.vertical=!t.inline),(n=i.call(this,e,t)||this).handleKeyPressHandler_=function(e){return n.handleKeyPress(e)},n.on(e,["loadstart"],function(e){return n.volumePanelState_(e)}),n.on(n.muteToggle,"keyup",function(e){return n.handleKeyPress(e)}),n.on(n.volumeControl,"keyup",function(e){return n.handleVolumeControlKeyUp(e)}),n.on("keydown",function(e){return n.handleKeyPress(e)}),n.on("mouseover",function(e){return n.handleMouseOver(e)}),n.on("mouseout",function(e){return n.handleMouseOut(e)}),n.on(n.volumeControl,["slideractive"],n.sliderActive_),n.on(n.volumeControl,["sliderinactive"],n.sliderInactive_),n}vt(e,i);var t=e.prototype;return t.sliderActive_=function(){this.addClass("vjs-slider-active")},t.sliderInactive_=function(){this.removeClass("vjs-slider-active")},t.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},t.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),i.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},t.dispose=function(){this.handleMouseOut(),i.prototype.dispose.call(this)},t.handleVolumeControlKeyUp=function(e){dt.isEventKey(e,"Esc")&&this.muteToggle.focus()},t.handleMouseOver=function(e){this.addClass("vjs-hover"),Re(document,"keyup",this.handleKeyPressHandler_)},t.handleMouseOut=function(e){this.removeClass("vjs-hover"),Be(document,"keyup",this.handleKeyPressHandler_)},t.handleKeyPress=function(e){dt.isEventKey(e,"Esc")&&this.handleMouseOut()},e}(pt);x.prototype.options_={children:["muteToggle","volumeControl"]},pt.registerComponent("VolumePanel",x);var di=function(i){function e(e,t){var n=i.call(this,e,t)||this;return t&&(n.menuButton_=t.menuButton),n.focusedChild_=-1,n.on("keydown",function(e){return n.handleKeyDown(e)}),n.boundHandleBlur_=function(e){return n.handleBlur(e)},n.boundHandleTapClick_=function(e){return n.handleTapClick(e)},n}vt(e,i);var t=e.prototype;return t.addEventListenerForItem=function(e){e instanceof pt&&(this.on(e,"blur",this.boundHandleBlur_),this.on(e,["tap","click"],this.boundHandleTapClick_))},t.removeEventListenerForItem=function(e){e instanceof pt&&(this.off(e,"blur",this.boundHandleBlur_),this.off(e,["tap","click"],this.boundHandleTapClick_))},t.removeChild=function(e){"string"==typeof e&&(e=this.getChild(e)),this.removeEventListenerForItem(e),i.prototype.removeChild.call(this,e)},t.addItem=function(e){e=this.addChild(e);e&&this.addEventListenerForItem(e)},t.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=Q(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");e=i.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return e.appendChild(this.contentEl_),Re(e,"click",function(e){e.preventDefault(),e.stopImmediatePropagation()}),e},t.dispose=function(){this.contentEl_=null,this.boundHandleBlur_=null,this.boundHandleTapClick_=null,i.prototype.dispose.call(this)},t.handleBlur=function(e){var t=e.relatedTarget||document.activeElement;this.children().some(function(e){return e.el()===t})||(e=this.menuButton_)&&e.buttonPressed_&&t!==e.el().firstChild&&e.unpressButton()},t.handleTapClick=function(t){var e;this.menuButton_&&(this.menuButton_.unpressButton(),e=this.children(),!Array.isArray(e)||(e=e.filter(function(e){return e.el()===t.target})[0])&&"CaptionSettingsMenuItem"!==e.name()&&this.menuButton_.focus())},t.handleKeyDown=function(e){dt.isEventKey(e,"Left")||dt.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):(dt.isEventKey(e,"Right")||dt.isEventKey(e,"Up"))&&(e.preventDefault(),e.stopPropagation(),this.stepBack())},t.stepForward=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_+1),this.focus(e)},t.stepBack=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_-1),this.focus(e)},t.focus=function(e){void 0===e&&(e=0);var t=this.children().slice();t.length&&t[0].hasClass("vjs-menu-title")&&t.shift(),0<t.length&&(e<0?e=0:e>=t.length&&(e=t.length-1),t[this.focusedChild_=e].el_.focus())},e}(pt);pt.registerComponent("Menu",di);Rt=function(i){function e(e,t){var n;(n=i.call(this,e,t=void 0===t?{}:t)||this).menuButton_=new oi(e,t),n.menuButton_.controlText(n.controlText_),n.menuButton_.el_.setAttribute("aria-haspopup","true");t=oi.prototype.buildCSSClass();n.menuButton_.el_.className=n.buildCSSClass()+" "+t,n.menuButton_.removeClass("vjs-control"),n.addChild(n.menuButton_),n.update(),n.enabled_=!0;t=function(e){return n.handleClick(e)};return n.handleMenuKeyUp_=function(e){return n.handleMenuKeyUp(e)},n.on(n.menuButton_,"tap",t),n.on(n.menuButton_,"click",t),n.on(n.menuButton_,"keydown",function(e){return n.handleKeyDown(e)}),n.on(n.menuButton_,"mouseenter",function(){n.addClass("vjs-hover"),n.menu.show(),Re(document,"keyup",n.handleMenuKeyUp_)}),n.on("mouseleave",function(e){return n.handleMouseLeave(e)}),n.on("keydown",function(e){return n.handleSubmenuKeyDown(e)}),n}vt(e,i);var t=e.prototype;return t.update=function(){var e=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=e,this.addChild(e),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?(this.hide(),this.menu.contentEl_.removeAttribute("role")):(this.show(),this.menu.contentEl_.setAttribute("role","menu"))},t.createMenu=function(){var e,t=new di(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title&&(e=Q("li",{className:"vjs-menu-title",textContent:lt(this.options_.title),tabIndex:-1}),e=new pt(this.player_,{el:e}),t.addItem(e)),this.items=this.createItems(),this.items)for(var n=0;n<this.items.length;n++)t.addItem(this.items[n]);return t},t.createItems=function(){},t.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},t.buildWrapperCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+oi.prototype.buildCSSClass()+" "+i.prototype.buildCSSClass.call(this)},t.buildCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+i.prototype.buildCSSClass.call(this)},t.controlText=function(e,t){return void 0===t&&(t=this.menuButton_.el()),this.menuButton_.controlText(e,t)},t.dispose=function(){this.handleMouseLeave(),i.prototype.dispose.call(this)},t.handleClick=function(e){this.buttonPressed_?this.unpressButton():this.pressButton()},t.handleMouseLeave=function(e){this.removeClass("vjs-hover"),Be(document,"keyup",this.handleMenuKeyUp_)},t.focus=function(){this.menuButton_.focus()},t.blur=function(){this.menuButton_.blur()},t.handleKeyDown=function(e){dt.isEventKey(e,"Esc")||dt.isEventKey(e,"Tab")?(this.buttonPressed_&&this.unpressButton(),dt.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus())):(dt.isEventKey(e,"Up")||dt.isEventKey(e,"Down"))&&(this.buttonPressed_||(e.preventDefault(),this.pressButton()))},t.handleMenuKeyUp=function(e){(dt.isEventKey(e,"Esc")||dt.isEventKey(e,"Tab"))&&this.removeClass("vjs-hover")},t.handleSubmenuKeyPress=function(e){this.handleSubmenuKeyDown(e)},t.handleSubmenuKeyDown=function(e){(dt.isEventKey(e,"Esc")||dt.isEventKey(e,"Tab"))&&(this.buttonPressed_&&this.unpressButton(),dt.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus()))},t.pressButton=function(){this.enabled_&&(this.buttonPressed_=!0,this.menu.show(),this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),U&&G()||this.menu.focus())},t.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menu.hide(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},t.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},t.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(pt);pt.registerComponent("MenuButton",Rt);Bt=function(r){function e(e,t){var n=t.tracks,t=r.call(this,e,t)||this;if(t.items.length<=1&&t.hide(),!n)return ft(t);var i=Ke(ft(t),t.update);return n.addEventListener("removetrack",i),n.addEventListener("addtrack",i),n.addEventListener("labelchange",i),t.player_.on("ready",i),t.player_.on("dispose",function(){n.removeEventListener("removetrack",i),n.removeEventListener("addtrack",i),n.removeEventListener("labelchange",i)}),t}return vt(e,r),e}(Rt);pt.registerComponent("TrackButton",Bt);var pi=["Tab","Esc","Up","Down","Right","Left"],Ht=function(i){function e(e,t){e=i.call(this,e,t)||this;return e.selectable=t.selectable,e.isSelected_=t.selected||!1,e.multiSelectable=t.multiSelectable,e.selected(e.isSelected_),e.selectable?e.multiSelectable?e.el_.setAttribute("role","menuitemcheckbox"):e.el_.setAttribute("role","menuitemradio"):e.el_.setAttribute("role","menuitem"),e}vt(e,i);var t=e.prototype;return t.createEl=function(e,t,n){this.nonIconControl=!0;n=i.prototype.createEl.call(this,"li",b({className:"vjs-menu-item",tabIndex:-1},t),n);return n.replaceChild(Q("span",{className:"vjs-menu-item-text",textContent:this.localize(this.options_.label)}),n.querySelector(".vjs-icon-placeholder")),n},t.handleKeyDown=function(t){pi.some(function(e){return dt.isEventKey(t,e)})||i.prototype.handleKeyDown.call(this,t)},t.handleClick=function(e){this.selected(!0)},t.selected=function(e){this.selectable&&(e?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},e}(S);pt.registerComponent("MenuItem",Ht);var fi=function(l){function e(e,t){var i,n=t.track,r=e.textTracks();t.label=n.label||n.language||"Unknown",t.selected="showing"===n.mode,(i=l.call(this,e,t)||this).track=n,i.kinds=(t.kinds||[t.kind||i.track.kind]).filter(Boolean);function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleTracksChange.apply(ft(i),t)}function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleSelectedLanguageChange.apply(ft(i),t)}var a;return e.on(["loadstart","texttrackchange"],s),r.addEventListener("change",s),r.addEventListener("selectedlanguagechange",o),i.on("dispose",function(){e.off(["loadstart","texttrackchange"],s),r.removeEventListener("change",s),r.removeEventListener("selectedlanguagechange",o)}),void 0===r.onchange&&i.on(["tap","click"],function(){if("object"!=typeof window.Event)try{a=new window.Event("change")}catch(e){}a||(a=document.createEvent("Event")).initEvent("change",!0,!0),r.dispatchEvent(a)}),i.handleTracksChange(),i}vt(e,l);var t=e.prototype;return t.handleClick=function(e){var t=this.track,n=this.player_.textTracks();if(l.prototype.handleClick.call(this,e),n)for(var i=0;i<n.length;i++){var r=n[i];-1!==this.kinds.indexOf(r.kind)&&(r===t?"showing"!==r.mode&&(r.mode="showing"):"disabled"!==r.mode&&(r.mode="disabled"))}},t.handleTracksChange=function(e){var t="showing"===this.track.mode;t!==this.isSelected_&&this.selected(t)},t.handleSelectedLanguageChange=function(e){var t;"showing"===this.track.mode&&((t=this.player_.cache_.selectedLanguage)&&t.enabled&&t.language===this.track.language&&t.kind!==this.track.kind||(this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}))},t.dispose=function(){this.track=null,l.prototype.dispose.call(this)},e}(Ht);pt.registerComponent("TextTrackMenuItem",fi);var vi=function(n){function e(e,t){return t.track={player:e,kind:t.kind,kinds:t.kinds,default:!1,mode:"disabled"},t.kinds||(t.kinds=[t.kind]),t.label?t.track.label=t.label:t.track.label=t.kinds.join(" and ")+" off",t.selectable=!0,t.multiSelectable=!1,n.call(this,e,t)||this}vt(e,n);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),n=!0,i=0,r=t.length;i<r;i++){var s=t[i];if(-1<this.options_.kinds.indexOf(s.kind)&&"showing"===s.mode){n=!1;break}}n!==this.isSelected_&&this.selected(n)},t.handleSelectedLanguageChange=function(e){for(var t=this.player().textTracks(),n=!0,i=0,r=t.length;i<r;i++){var s=t[i];if(-1<["captions","descriptions","subtitles"].indexOf(s.kind)&&"showing"===s.mode){n=!1;break}}n&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(fi);pt.registerComponent("OffTextTrackMenuItem",vi);H=function(n){function e(e,t){return(t=void 0===t?{}:t).tracks=e.textTracks(),n.call(this,e,t)||this}return vt(e,n),e.prototype.createItems=function(e,t){var n;void 0===t&&(t=fi),this.label_&&(n=this.label_+" off"),(e=void 0===e?[]:e).push(new vi(this.player_,{kinds:this.kinds_,kind:this.kind_,label:n})),this.hideThreshold_+=1;var i=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var r=0;r<i.length;r++){var s,o=i[r];-1<this.kinds_.indexOf(o.kind)&&((s=new t(this.player_,{track:o,kinds:this.kinds_,kind:this.kind_,selectable:!0,multiSelectable:!1})).addClass("vjs-"+o.kind+"-menu-item"),e.push(s))}return e},e}(Bt);pt.registerComponent("TextTrackButton",H);var gi=function(s){function e(e,t){var n=t.track,i=t.cue,r=e.currentTime();return t.selectable=!0,t.multiSelectable=!1,t.label=i.text,t.selected=i.startTime<=r&&r<i.endTime,(t=s.call(this,e,t)||this).track=n,t.cue=i,t}return vt(e,s),e.prototype.handleClick=function(e){s.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime)},e}(Ht);pt.registerComponent("ChaptersTrackMenuItem",gi);E=function(r){function e(e,t,n){var i=r.call(this,e,t,n)||this;return i.selectCurrentItem_=function(){i.items.forEach(function(e){e.selected(i.track_.activeCues[0]===e.cue)})},i}vt(e,r);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-chapters-button "+r.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-chapters-button "+r.prototype.buildWrapperCSSClass.call(this)},t.update=function(e){e&&e.track&&"chapters"!==e.track.kind||((e=this.findChaptersTrack())!==this.track_?(this.setTrack(e),r.prototype.update.call(this)):(!this.items||e&&e.cues&&e.cues.length!==this.items.length)&&r.prototype.update.call(this))},t.setTrack=function(e){var t;this.track_!==e&&(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_&&((t=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_))&&t.removeEventListener("load",this.updateHandler_),this.track_.removeEventListener("cuechange",this.selectCurrentItem_),this.track_=null),this.track_=e,this.track_&&(this.track_.mode="hidden",(e=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_))&&e.addEventListener("load",this.updateHandler_),this.track_.addEventListener("cuechange",this.selectCurrentItem_)))},t.findChaptersTrack=function(){for(var e=this.player_.textTracks()||[],t=e.length-1;0<=t;t--){var n=e[t];if(n.kind===this.kind_)return n}},t.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(lt(this.kind_))},t.createMenu=function(){return this.options_.title=this.getMenuCaption(),r.prototype.createMenu.call(this)},t.createItems=function(){var e=[];if(!this.track_)return e;var t=this.track_.cues;if(!t)return e;for(var n=0,i=t.length;n<i;n++){var r=t[n],r=new gi(this.player_,{track:this.track_,cue:r});e.push(r)}return e},e}(H);E.prototype.kind_="chapters",E.prototype.controlText_="Chapters",pt.registerComponent("ChaptersButton",E);un=function(s){function e(e,t,n){var n=s.call(this,e,t,n)||this,i=e.textTracks(),r=Ke(ft(n),n.handleTracksChange);return i.addEventListener("change",r),n.on("dispose",function(){i.removeEventListener("change",r)}),n}vt(e,s);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),n=!1,i=0,r=t.length;i<r;i++){var s=t[i];if(s.kind!==this.kind_&&"showing"===s.mode){n=!0;break}}n?this.disable():this.enable()},t.buildCSSClass=function(){return"vjs-descriptions-button "+s.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+s.prototype.buildWrapperCSSClass.call(this)},e}(H);un.prototype.kind_="descriptions",un.prototype.controlText_="Descriptions",pt.registerComponent("DescriptionsButton",un);f=function(i){function e(e,t,n){return i.call(this,e,t,n)||this}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildWrapperCSSClass.call(this)},e}(H);f.prototype.kind_="subtitles",f.prototype.controlText_="Subtitles",pt.registerComponent("SubtitlesButton",f);var mi=function(n){function e(e,t){return t.track={player:e,kind:t.kind,label:t.kind+" settings",selectable:!1,default:!1,mode:"disabled"},t.selectable=!1,t.name="CaptionSettingsMenuItem",(e=n.call(this,e,t)||this).addClass("vjs-texttrack-settings"),e.controlText(", opens "+t.kind+" settings dialog"),e}return vt(e,n),e.prototype.handleClick=function(e){this.player().getChild("textTrackSettings").open()},e}(fi);pt.registerComponent("CaptionSettingsMenuItem",mi);cn=function(i){function e(e,t,n){return i.call(this,e,t,n)||this}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-captions-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-captions-button "+i.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new mi(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),i.prototype.createItems.call(this,e)},e}(H);cn.prototype.kind_="captions",cn.prototype.controlText_="Captions",pt.registerComponent("CaptionsButton",cn);var _i=function(i){function e(){return i.apply(this,arguments)||this}return vt(e,i),e.prototype.createEl=function(e,t,n){t=i.prototype.createEl.call(this,e,t,n),n=t.querySelector(".vjs-menu-item-text");return"captions"===this.options_.track.kind&&(n.appendChild(Q("span",{className:"vjs-icon-placeholder"},{"aria-hidden":!0})),n.appendChild(Q("span",{className:"vjs-control-text",textContent:" "+this.localize("Captions")}))),t},e}(fi);pt.registerComponent("SubsCapsMenuItem",_i);Xt=function(n){function e(e,t){return(t=n.call(this,e,t=void 0===t?{}:t)||this).label_="subtitles",-1<["en","en-us","en-ca","fr-ca"].indexOf(t.player_.language_)&&(t.label_="captions"),t.menuButton_.controlText(lt(t.label_)),t}vt(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subs-caps-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+n.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new mi(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=n.prototype.createItems.call(this,e,_i)},e}(H);Xt.prototype.kinds_=["captions","subtitles"],Xt.prototype.controlText_="Subtitles",pt.registerComponent("SubsCapsButton",Xt);var yi=function(o){function e(e,t){var i,n=t.track,r=e.audioTracks();t.label=n.label||n.language||"Unknown",t.selected=n.enabled,(i=o.call(this,e,t)||this).track=n,i.addClass("vjs-"+n.kind+"-menu-item");function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.handleTracksChange.apply(ft(i),t)}return r.addEventListener("change",s),i.on("dispose",function(){r.removeEventListener("change",s)}),i}vt(e,o);var t=e.prototype;return t.createEl=function(e,t,n){t=o.prototype.createEl.call(this,e,t,n),n=t.querySelector(".vjs-menu-item-text");return"main-desc"===this.options_.track.kind&&(n.appendChild(Q("span",{className:"vjs-icon-placeholder"},{"aria-hidden":!0})),n.appendChild(Q("span",{className:"vjs-control-text",textContent:" "+this.localize("Descriptions")}))),t},t.handleClick=function(e){if(o.prototype.handleClick.call(this,e),this.track.enabled=!0,this.player_.tech_.featuresNativeAudioTracks)for(var t=this.player_.audioTracks(),n=0;n<t.length;n++){var i=t[n];i!==this.track&&(i.enabled=i===this.track)}},t.handleTracksChange=function(e){this.selected(this.track.enabled)},e}(Ht);pt.registerComponent("AudioTrackMenuItem",yi);x=function(n){function e(e,t){return(t=void 0===t?{}:t).tracks=e.audioTracks(),n.call(this,e,t)||this}vt(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-audio-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-audio-button "+n.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(e){void 0===e&&(e=[]),this.hideThreshold_=1;for(var t=this.player_.audioTracks(),n=0;n<t.length;n++){var i=t[n];e.push(new yi(this.player_,{track:i,selectable:!0,multiSelectable:!1}))}return e},e}(Bt);x.prototype.controlText_="Audio Track",pt.registerComponent("AudioTrackButton",x);var bi=function(s){function e(e,t){var n,i=t.rate,r=parseFloat(i,10);return t.label=i,t.selected=r===e.playbackRate(),t.selectable=!0,t.multiSelectable=!1,(n=s.call(this,e,t)||this).label=i,n.rate=r,n.on(e,"ratechange",function(e){return n.update(e)}),n}vt(e,s);var t=e.prototype;return t.handleClick=function(e){s.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},t.update=function(e){this.selected(this.player().playbackRate()===this.rate)},e}(Ht);bi.prototype.contentElType="button",pt.registerComponent("PlaybackRateMenuItem",bi);S=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.menuButton_.el_.setAttribute("aria-describedby",n.labelElId_),n.updateVisibility(),n.updateLabel(),n.on(e,"loadstart",function(e){return n.updateVisibility(e)}),n.on(e,"ratechange",function(e){return n.updateLabel(e)}),n.on(e,"playbackrateschange",function(e){return n.handlePlaybackRateschange(e)}),n}vt(e,i);var t=e.prototype;return t.createEl=function(){var e=i.prototype.createEl.call(this);return this.labelElId_="vjs-playback-rate-value-label-"+this.id_,this.labelEl_=Q("div",{className:"vjs-playback-rate-value",id:this.labelElId_,textContent:"1x"}),e.appendChild(this.labelEl_),e},t.dispose=function(){this.labelEl_=null,i.prototype.dispose.call(this)},t.buildCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){for(var e=this.playbackRates(),t=[],n=e.length-1;0<=n;n--)t.push(new bi(this.player(),{rate:e[n]+"x"}));return t},t.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},t.handleClick=function(e){var t=this.player().playbackRate(),n=this.playbackRates(),t=(n.indexOf(t)+1)%n.length;this.player().playbackRate(n[t])},t.handlePlaybackRateschange=function(e){this.update()},t.playbackRates=function(){var e=this.player();return e.playbackRates&&e.playbackRates()||[]},t.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&0<this.playbackRates().length},t.updateVisibility=function(e){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},t.updateLabel=function(e){this.playbackRateSupported()&&(this.labelEl_.textContent=this.player().playbackRate()+"x")},e}(Rt);S.prototype.controlText_="Playback Rate",pt.registerComponent("PlaybackRateMenuButton",S);E=function(i){function e(){return i.apply(this,arguments)||this}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-spacer "+i.prototype.buildCSSClass.call(this)},t.createEl=function(e,t,n){return void 0===e&&(e="div"),void 0===n&&(n={}),(t=void 0===t?{}:t).className||(t.className=this.buildCSSClass()),i.prototype.createEl.call(this,e,t,n)},e}(pt);pt.registerComponent("Spacer",E),pt.registerComponent("CustomControlSpacer",function(e){function t(){return e.apply(this,arguments)||this}vt(t,e);var n=t.prototype;return n.buildCSSClass=function(){return"vjs-custom-control-spacer "+e.prototype.buildCSSClass.call(this)},n.createEl=function(){return e.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),textContent:" "})},t}(E));un=function(e){function t(){return e.apply(this,arguments)||this}return vt(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},t}(pt);un.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","seekToLive","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},"exitPictureInPicture"in document&&un.prototype.options_.children.splice(un.prototype.options_.children.length-1,0,"pictureInPictureToggle"),pt.registerComponent("ControlBar",un);f=function(i){function e(e,t){var n=i.call(this,e,t)||this;return n.on(e,"error",function(e){return n.open(e)}),n}vt(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-error-display "+i.prototype.buildCSSClass.call(this)},t.content=function(){var e=this.player().error();return e?this.localize(e.message):""},e}(jt);f.prototype.options_=g({},jt.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),pt.registerComponent("ErrorDisplay",f);var Ti="vjs-text-track-settings",cn=["#000","Black"],H=["#00F","Blue"],Xt=["#0FF","Cyan"],Bt=["#0F0","Green"],x=["#F0F","Magenta"],Ht=["#F00","Red"],Rt=["#FFF","White"],S=["#FF0","Yellow"],E=["1","Opaque"],un=["0.5","Semi-Transparent"],f=["0","Transparent"],ki={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[cn,Rt,Ht,Bt,H,S,x,Xt]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[E,un,f]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[Rt,cn,Ht,Bt,H,S,x,Xt]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(e){return"1.00"===e?null:Number(e)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[E,un]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[f,un,E]}};function Ci(e,t){if((e=t?t(e):e)&&"none"!==e)return e}ki.windowColor.options=ki.backgroundColor.options,pt.registerComponent("TextTrackSettings",function(i){function e(e,t){var n;return t.temporary=!1,(n=i.call(this,e,t)||this).updateDisplay=n.updateDisplay.bind(ft(n)),n.fill(),n.hasBeenOpened_=n.hasBeenFilled_=!0,n.endDialog=Q("p",{className:"vjs-control-text",textContent:n.localize("End of dialog window.")}),n.el().appendChild(n.endDialog),n.setDefaults(),void 0===t.persistTextTrackSettings&&(n.options_.persistTextTrackSettings=n.options_.playerOptions.persistTextTrackSettings),n.on(n.$(".vjs-done-button"),"click",function(){n.saveSettings(),n.close()}),n.on(n.$(".vjs-default-button"),"click",function(){n.setDefaults(),n.updateDisplay()}),y(ki,function(e){n.on(n.$(e.selector),"change",n.updateDisplay)}),n.options_.persistTextTrackSettings&&n.restoreSettings(),n}vt(e,i);var t=e.prototype;return t.dispose=function(){this.endDialog=null,i.prototype.dispose.call(this)},t.createElSelect_=function(e,t,n){var i=this;void 0===t&&(t=""),void 0===n&&(n="label");var e=ki[e],r=e.id.replace("%s",this.id_),s=[t,r].join(" ").trim();return["<"+n+' id="'+r+'" class="'+("label"===n?"vjs-label":"")+'">',this.localize(e.label),"</"+n+">",'<select aria-labelledby="'+s+'">'].concat(e.options.map(function(e){var t=r+"-"+e[1].replace(/\W+/g,"");return['<option id="'+t+'" value="'+e[0]+'" ','aria-labelledby="'+s+" "+t+'">',i.localize(e[1]),"</option>"].join("")})).concat("</select>").join("")},t.createElFgColor_=function(){var e="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",e),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",e),"</span>","</fieldset>"].join("")},t.createElBgColor_=function(){var e="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",e),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",e),"</span>","</fieldset>"].join("")},t.createElWinColor_=function(){var e="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",e),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",e),"</span>","</fieldset>"].join("")},t.createElColors_=function(){return Q("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},t.createElFont_=function(){return Q("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},t.createElControls_=function(){var e=this.localize("restore all settings to the default values");return Q("div",{className:"vjs-track-settings-controls",innerHTML:['<button type="button" class="vjs-default-button" title="'+e+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+e+"</span>","</button>",'<button type="button" class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},t.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},t.label=function(){return this.localize("Caption Settings Dialog")},t.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},t.buildCSSClass=function(){return i.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},t.getValues=function(){var n,i,e,r=this;return i=function(e,t,n){var i,t=(i=r.$(t.selector),t=t.parser,Ci(i.options[i.options.selectedIndex].value,t));return void 0!==t&&(e[n]=t),e},void 0===(e={})&&(e=0),_(n=ki).reduce(function(e,t){return i(e,n[t],t)},e)},t.setValues=function(n){var i=this;y(ki,function(e,t){!function(e,t,n){if(t)for(var i=0;i<e.options.length;i++)if(Ci(e.options[i].value,n)===t){e.selectedIndex=i;break}}(i.$(e.selector),n[t],e.parser)})},t.setDefaults=function(){var n=this;y(ki,function(e){var t=e.hasOwnProperty("default")?e.default:0;n.$(e.selector).selectedIndex=t})},t.restoreSettings=function(){var e;try{e=JSON.parse(window.localStorage.getItem(Ti))}catch(e){d.warn(e)}e&&this.setValues(e)},t.saveSettings=function(){if(this.options_.persistTextTrackSettings){var e=this.getValues();try{Object.keys(e).length?window.localStorage.setItem(Ti,JSON.stringify(e)):window.localStorage.removeItem(Ti)}catch(e){d.warn(e)}}},t.updateDisplay=function(){var e=this.player_.getChild("textTrackDisplay");e&&e.updateDisplay()},t.conditionalBlur_=function(){this.previouslyActiveEl_=null;var e=this.player_.controlBar,t=e&&e.subsCapsButton,e=e&&e.captionsButton;t?t.focus():e&&e.focus()},e}(jt)),pt.registerComponent("ResizeManager",function(s){function e(e,t){var n,i=t.ResizeObserver||window.ResizeObserver,r=ct({createEl:!(i=null===t.ResizeObserver?!1:i),reportTouchActivity:!1},t);return(n=s.call(this,e,r)||this).ResizeObserver=t.ResizeObserver||window.ResizeObserver,n.loadListener_=null,n.resizeObserver_=null,n.debouncedHandler_=ze(function(){n.resizeHandler()},100,!1,ft(n)),i?(n.resizeObserver_=new n.ResizeObserver(n.debouncedHandler_),n.resizeObserver_.observe(e.el())):(n.loadListener_=function(){var e,t;n.el_&&n.el_.contentWindow&&(e=n.debouncedHandler_,t=n.unloadListener_=function(){Be(this,"resize",e),Be(this,"unload",t),t=null},Re(n.el_.contentWindow,"unload",t),Re(n.el_.contentWindow,"resize",e))},n.one("load",n.loadListener_)),n}vt(e,s);var t=e.prototype;return t.createEl=function(){return s.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager",tabIndex:-1,title:this.localize("No content")},{"aria-hidden":"true"})},t.resizeHandler=function(){this.player_&&this.player_.trigger&&this.player_.trigger("playerresize")},t.dispose=function(){this.debouncedHandler_&&this.debouncedHandler_.cancel(),this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.loadListener_&&this.off("load",this.loadListener_),this.el_&&this.el_.contentWindow&&this.unloadListener_&&this.unloadListener_.call(this.el_.contentWindow),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null,s.prototype.dispose.call(this)},e}(pt));var wi={trackingThreshold:20,liveTolerance:15};pt.registerComponent("LiveTracker",function(i){function e(e,t){var t=ct(wi,t,{createEl:!1}),n=i.call(this,e,t)||this;return n.handleVisibilityChange_=function(e){return n.handleVisibilityChange(e)},n.trackLiveHandler_=function(){return n.trackLive_()},n.handlePlay_=function(e){return n.handlePlay(e)},n.handleFirstTimeupdate_=function(e){return n.handleFirstTimeupdate(e)},n.handleSeeked_=function(e){return n.handleSeeked(e)},n.seekToLiveEdge_=function(e){return n.seekToLiveEdge(e)},n.reset_(),n.on(n.player_,"durationchange",function(e){return n.handleDurationchange(e)}),n.on(n.player_,"canplay",function(){return n.toggleTracking()}),D&&"hidden"in document&&"visibilityState"in document&&n.on(document,"visibilitychange",n.handleVisibilityChange_),n}vt(e,i);var t=e.prototype;return t.handleVisibilityChange=function(){this.player_.duration()===1/0&&(document.hidden?this.stopTracking():this.startTracking())},t.trackLive_=function(){var e,t=this.player_.seekable();t&&t.length&&(e=Number(window.performance.now().toFixed(4)),t=-1===this.lastTime_?0:(e-this.lastTime_)/1e3,this.lastTime_=e,this.pastSeekEnd_=this.pastSeekEnd()+t,e=this.liveCurrentTime(),t=this.player_.currentTime(),t=this.player_.paused()||this.seekedBehindLive_||Math.abs(e-t)>this.options_.liveTolerance,(t=!this.timeupdateSeen_||e===1/0?!1:t)!==this.behindLiveEdge_&&(this.behindLiveEdge_=t,this.trigger("liveedgechange")))},t.handleDurationchange=function(){this.toggleTracking()},t.toggleTracking=function(){this.player_.duration()===1/0&&this.liveWindow()>=this.options_.trackingThreshold?(this.player_.options_.liveui&&this.player_.addClass("vjs-liveui"),this.startTracking()):(this.player_.removeClass("vjs-liveui"),this.stopTracking())},t.startTracking=function(){this.isTracking()||(this.timeupdateSeen_||(this.timeupdateSeen_=this.player_.hasStarted()),this.trackingInterval_=this.setInterval(this.trackLiveHandler_,30),this.trackLive_(),this.on(this.player_,["play","pause"],this.trackLiveHandler_),this.timeupdateSeen_?this.on(this.player_,"seeked",this.handleSeeked_):(this.one(this.player_,"play",this.handlePlay_),this.one(this.player_,"timeupdate",this.handleFirstTimeupdate_)))},t.handleFirstTimeupdate=function(){this.timeupdateSeen_=!0,this.on(this.player_,"seeked",this.handleSeeked_)},t.handleSeeked=function(){var e=Math.abs(this.liveCurrentTime()-this.player_.currentTime());this.seekedBehindLive_=this.nextSeekedFromUser_&&2<e,this.nextSeekedFromUser_=!1,this.trackLive_()},t.handlePlay=function(){this.one(this.player_,"timeupdate",this.seekToLiveEdge_)},t.reset_=function(){this.lastTime_=-1,this.pastSeekEnd_=0,this.lastSeekEnd_=-1,this.behindLiveEdge_=!0,this.timeupdateSeen_=!1,this.seekedBehindLive_=!1,this.nextSeekedFromUser_=!1,this.clearInterval(this.trackingInterval_),this.trackingInterval_=null,this.off(this.player_,["play","pause"],this.trackLiveHandler_),this.off(this.player_,"seeked",this.handleSeeked_),this.off(this.player_,"play",this.handlePlay_),this.off(this.player_,"timeupdate",this.handleFirstTimeupdate_),this.off(this.player_,"timeupdate",this.seekToLiveEdge_)},t.nextSeekedFromUser=function(){this.nextSeekedFromUser_=!0},t.stopTracking=function(){this.isTracking()&&(this.reset_(),this.trigger("liveedgechange"))},t.seekableEnd=function(){for(var e=this.player_.seekable(),t=[],n=e?e.length:0;n--;)t.push(e.end(n));return t.length?t.sort()[t.length-1]:1/0},t.seekableStart=function(){for(var e=this.player_.seekable(),t=[],n=e?e.length:0;n--;)t.push(e.start(n));return t.length?t.sort()[0]:0},t.liveWindow=function(){var e=this.liveCurrentTime();return e===1/0?0:e-this.seekableStart()},t.isLive=function(){return this.isTracking()},t.atLiveEdge=function(){return!this.behindLiveEdge()},t.liveCurrentTime=function(){return this.pastSeekEnd()+this.seekableEnd()},t.pastSeekEnd=function(){var e=this.seekableEnd();return-1!==this.lastSeekEnd_&&e!==this.lastSeekEnd_&&(this.pastSeekEnd_=0),this.lastSeekEnd_=e,this.pastSeekEnd_},t.behindLiveEdge=function(){return this.behindLiveEdge_},t.isTracking=function(){return"number"==typeof this.trackingInterval_},t.seekToLiveEdge=function(){this.seekedBehindLive_=!1,this.atLiveEdge()||(this.nextSeekedFromUser_=!1,this.player_.currentTime(this.liveCurrentTime()))},t.dispose=function(){this.off(document,"visibilitychange",this.handleVisibilityChange_),this.stopTracking(),i.prototype.dispose.call(this)},e}(pt));function Ei(e){if((i=e.el()).hasAttribute("src"))return e.triggerSourceset(i.src),1;var t=e.$$("source"),n=[],i="";if(t.length){for(var r=0;r<t.length;r++){var s=t[r].src;s&&-1===n.indexOf(s)&&n.push(s)}return!!n.length&&(1===n.length&&(i=n[0]),e.triggerSourceset(i),!0)}}function Si(e,t){for(var n={},i=0;i<e.length&&!((n=Object.getOwnPropertyDescriptor(e[i],t))&&n.set&&n.get);i++);return n.enumerable=!0,n.configurable=!0,n}function xi(s){var t,e,n,o=s.el();o.resetSourceWatch_||(t={},e=Si([s.el(),window.HTMLMediaElement.prototype,window.Element.prototype,Ii],"innerHTML"),n=function(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=r.apply(o,t);return Ei(s),i}},["append","appendChild","insertAdjacentHTML"].forEach(function(e){o[e]&&(t[e]=o[e],o[e]=n(t[e]))}),Object.defineProperty(o,"innerHTML",ct(e,{set:n(e.set)})),o.resetSourceWatch_=function(){o.resetSourceWatch_=null,Object.keys(t).forEach(function(e){o[e]=t[e]}),Object.defineProperty(o,"innerHTML",e)},s.one("sourceset",o.resetSourceWatch_))}function Pi(n){var i,t,r,s;n.featuresSourceset&&((i=n.el()).resetSourceset_||(t=Si([n.el(),window.HTMLMediaElement.prototype,Mi],"src"),r=i.setAttribute,s=i.load,Object.defineProperty(i,"src",ct(t,{set:function(e){e=t.set.call(i,e);return n.triggerSourceset(i.src),e}})),i.setAttribute=function(e,t){t=r.call(i,e,t);return/src/i.test(e)&&n.triggerSourceset(i.src),t},i.load=function(){var e=s.call(i);return Ei(n)||(n.triggerSourceset(""),xi(n)),e},i.currentSrc?n.triggerSourceset(i.currentSrc):Ei(n)||xi(n),i.resetSourceset_=function(){i.resetSourceset_=null,i.load=s,i.setAttribute=r,Object.defineProperty(i,"src",t),i.resetSourceWatch_&&i.resetSourceWatch_()}))}function ji(t,n,i,e){function r(e){return Object.defineProperty(t,n,{value:e,enumerable:!0,writable:!0})}var s={configurable:!0,enumerable:!0,get:function(){var e=i();return r(e),e}};return(e=void 0===e?!0:e)&&(s.set=r),Object.defineProperty(t,n,s)}var Ai,Ii=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(e){var t=document.createElement(this.nodeName.toLowerCase());t.innerHTML=e;for(var n=document.createDocumentFragment();t.childNodes.length;)n.appendChild(t.childNodes[0]);return this.innerText="",window.Element.prototype.appendChild.call(this,n),this.innerHTML}}),Mi=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?Lt(window.Element.prototype.getAttribute.call(this,"src")):""},set:function(e){return window.Element.prototype.setAttribute.call(this,"src",e),e}}),Ni=function(c){function o(e,t){var n=c.call(this,e,t)||this,t=e.source,i=!1;if(n.featuresVideoFrameCallback=n.featuresVideoFrameCallback&&"VIDEO"===n.el_.tagName,t&&(n.el_.currentSrc!==t.src||e.tag&&3===e.tag.initNetworkState_)?n.setSource(t):n.handleLateInit_(n.el_),e.enableSourceset&&n.setupSourcesetHandling_(),n.isScrubbing_=!1,n.el_.hasChildNodes()){for(var r=n.el_.childNodes,s=r.length,o=[];s--;){var a=r[s];"track"===a.nodeName.toLowerCase()&&(n.featuresNativeTextTracks?(n.remoteTextTrackEls().addTrackElement_(a),n.remoteTextTracks().addTrack(a.track),n.textTracks().addTrack(a.track),i||n.el_.hasAttribute("crossorigin")||!Ft(a.src)||(i=!0)):o.push(a))}for(var l=0;l<o.length;l++)n.el_.removeChild(o[l])}return n.proxyNativeTracks_(),n.featuresNativeTextTracks&&i&&d.warn("Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\nThis may prevent text tracks from loading."),n.restoreMetadataTracksInIOSNativePlayer_(),(B||V||I)&&!0===e.nativeControlsForTouch&&n.setControls(!0),n.proxyWebkitFullscreen_(),n.triggerReady(),n}vt(o,c);var e=o.prototype;return e.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),o.disposeMediaElement(this.el_),this.options_=null,c.prototype.dispose.call(this)},e.setupSourcesetHandling_=function(){Pi(this)},e.restoreMetadataTracksInIOSNativePlayer_=function(){function e(){n=[];for(var e=0;e<i.length;e++){var t=i[e];"metadata"===t.kind&&n.push({track:t,storedMode:t.mode})}}var n,i=this.textTracks();e(),i.addEventListener("change",e),this.on("dispose",function(){return i.removeEventListener("change",e)});function r(){for(var e=0;e<n.length;e++){var t=n[e];"disabled"===t.track.mode&&t.track.mode!==t.storedMode&&(t.track.mode=t.storedMode)}i.removeEventListener("change",r)}this.on("webkitbeginfullscreen",function(){i.removeEventListener("change",e),i.removeEventListener("change",r),i.addEventListener("change",r)}),this.on("webkitendfullscreen",function(){i.removeEventListener("change",e),i.addEventListener("change",e),i.removeEventListener("change",r)})},e.overrideNative_=function(e,t){var n,i=this;t===this["featuresNative"+e+"Tracks"]&&(this[(n=e.toLowerCase())+"TracksListeners_"]&&Object.keys(this[n+"TracksListeners_"]).forEach(function(e){i.el()[n+"Tracks"].removeEventListener(e,i[n+"TracksListeners_"][e])}),this["featuresNative"+e+"Tracks"]=!t,this[n+"TracksListeners_"]=null,this.proxyNativeTracksForType_(n))},e.overrideNativeAudioTracks=function(e){this.overrideNative_("Audio",e)},e.overrideNativeVideoTracks=function(e){this.overrideNative_("Video",e)},e.proxyNativeTracksForType_=function(n){var e,t,i=this,r=on[n],s=this.el()[r.getterName],o=this[r.getterName]();this["featuresNative"+r.capitalName+"Tracks"]&&s&&s.addEventListener&&(t=function(){for(var e=[],t=0;t<o.length;t++){for(var n=!1,i=0;i<s.length;i++)if(s[i]===o[t]){n=!0;break}n||e.push(o[t])}for(;e.length;)o.removeTrack(e.shift())},this[r.getterName+"Listeners_"]=e={change:function(e){var t={type:"change",target:o,currentTarget:o,srcElement:o};o.trigger(t),"text"===n&&i[an.remoteText.getterName]().trigger(t)},addtrack:function(e){o.addTrack(e.track)},removetrack:function(e){o.removeTrack(e.track)}},Object.keys(e).forEach(function(t){var n=e[t];s.addEventListener(t,n),i.on("dispose",function(e){return s.removeEventListener(t,n)})}),this.on("loadstart",t),this.on("dispose",function(e){return i.off("loadstart",t)}))},e.proxyNativeTracks_=function(){var t=this;on.names.forEach(function(e){t.proxyNativeTracksForType_(e)})},e.createEl=function(){var e,t=this.options_.tag;t&&(this.options_.playerElIngest||this.movingMediaElementInDOM)||(t?(e=t.cloneNode(!0),t.parentNode&&t.parentNode.insertBefore(e,t),o.disposeMediaElement(t),t=e):(t=document.createElement("video"),e=ct({},this.options_.tag&&se(this.options_.tag)),B&&!0===this.options_.nativeControlsForTouch||delete e.controls,re(t,b(e,{id:this.options_.techId,class:"vjs-tech"}))),t.playerId=this.options_.playerId),"undefined"!=typeof this.options_.preload&&ae(t,"preload",this.options_.preload),void 0!==this.options_.disablePictureInPicture&&(t.disablePictureInPicture=this.options_.disablePictureInPicture);for(var n=["loop","muted","playsinline","autoplay"],i=0;i<n.length;i++){var r=n[i],s=this.options_[r];"undefined"!=typeof s&&(s?ae(t,r,r):le(t,r),t[r]=s)}return t},e.handleLateInit_=function(e){if(0!==e.networkState&&3!==e.networkState){if(0===e.readyState){var t=!1,n=function(){t=!0};this.on("loadstart",n);var i=function(){t||this.trigger("loadstart")};return this.on("loadedmetadata",i),void this.ready(function(){this.off("loadstart",n),this.off("loadedmetadata",i),t||this.trigger("loadstart")})}var r=["loadstart"];r.push("loadedmetadata"),2<=e.readyState&&r.push("loadeddata"),3<=e.readyState&&r.push("canplay"),4<=e.readyState&&r.push("canplaythrough"),this.ready(function(){r.forEach(function(e){this.trigger(e)},this)})}},e.setScrubbing=function(e){this.isScrubbing_=e},e.scrubbing=function(){return this.isScrubbing_},e.setCurrentTime=function(e){try{this.isScrubbing_&&this.el_.fastSeek&&K?this.el_.fastSeek(e):this.el_.currentTime=e}catch(e){d(e,"Video is not ready. (Video.js)")}},e.duration=function(){var t=this;return this.el_.duration===1/0&&j&&O&&0===this.el_.currentTime?(this.on("timeupdate",function e(){0<t.el_.currentTime&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",e))}),NaN):this.el_.duration||NaN},e.width=function(){return this.el_.offsetWidth},e.height=function(){return this.el_.offsetHeight},e.proxyWebkitFullscreen_=function(){var e,t,n=this;"webkitDisplayingFullscreen"in this.el_&&(e=function(){this.trigger("fullscreenchange",{isFullscreen:!1}),this.el_.controls&&!this.options_.nativeControlsForTouch&&this.controls()&&(this.el_.controls=!1)},t=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",e),this.trigger("fullscreenchange",{isFullscreen:!0,nativeIOSFullscreen:!0}))},this.on("webkitbeginfullscreen",t),this.on("dispose",function(){n.off("webkitbeginfullscreen",t),n.off("webkitendfullscreen",e)}))},e.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var e=window.navigator&&window.navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1},e.enterFullScreen=function(){var e=this.el_;if(e.paused&&e.networkState<=e.HAVE_METADATA)wt(this.el_.play()),this.setTimeout(function(){e.pause();try{e.webkitEnterFullScreen()}catch(e){this.trigger("fullscreenerror",e)}},0);else try{e.webkitEnterFullScreen()}catch(e){this.trigger("fullscreenerror",e)}},e.exitFullScreen=function(){this.el_.webkitDisplayingFullscreen?this.el_.webkitExitFullScreen():this.trigger("fullscreenerror",new Error("The video is not fullscreen"))},e.requestPictureInPicture=function(){return this.el_.requestPictureInPicture()},e.requestVideoFrameCallback=function(e){return this.featuresVideoFrameCallback&&!this.el_.webkitKeys?this.el_.requestVideoFrameCallback(e):c.prototype.requestVideoFrameCallback.call(this,e)},e.cancelVideoFrameCallback=function(e){this.featuresVideoFrameCallback&&!this.el_.webkitKeys?this.el_.cancelVideoFrameCallback(e):c.prototype.cancelVideoFrameCallback.call(this,e)},e.src=function(e){if(void 0===e)return this.el_.src;this.setSrc(e)},e.reset=function(){o.resetMediaElement(this.el_)},e.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.setControls=function(e){this.el_.controls=!!e},e.addTextTrack=function(e,t,n){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,t,n):c.prototype.addTextTrack.call(this,e,t,n)},e.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return c.prototype.createRemoteTextTrack.call(this,e);var t=document.createElement("track");return e.kind&&(t.kind=e.kind),e.label&&(t.label=e.label),(e.language||e.srclang)&&(t.srclang=e.language||e.srclang),e.default&&(t.default=e.default),e.id&&(t.id=e.id),e.src&&(t.src=e.src),t},e.addRemoteTextTrack=function(e,t){t=c.prototype.addRemoteTextTrack.call(this,e,t);return this.featuresNativeTextTracks&&this.el().appendChild(t),t},e.removeRemoteTextTrack=function(e){if(c.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var t=this.$$("track"),n=t.length;n--;)e!==t[n]&&e!==t[n].track||this.el().removeChild(t[n])},e.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var e={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(e.droppedVideoFrames=this.el().webkitDroppedFrameCount,e.totalVideoFrames=this.el().webkitDecodedFrameCount),window.performance&&"function"==typeof window.performance.now?e.creationTime=window.performance.now():window.performance&&window.performance.timing&&"number"==typeof window.performance.timing.navigationStart&&(e.creationTime=window.Date.now()-window.performance.timing.navigationStart),e},o}(Vn);ji(Ni,"TEST_VID",function(){if(X()){var e=document.createElement("video"),t=document.createElement("track");return t.kind="captions",t.srclang="en",t.label="English",e.appendChild(t),e}}),Ni.isSupported=function(){try{Ni.TEST_VID.volume=.5}catch(e){return!1}return!(!Ni.TEST_VID||!Ni.TEST_VID.canPlayType)},Ni.canPlayType=function(e){return Ni.TEST_VID.canPlayType(e)},Ni.canPlaySource=function(e,t){return Ni.canPlayType(e.type)},Ni.canControlVolume=function(){try{var e=Ni.TEST_VID.volume;Ni.TEST_VID.volume=e/2+.1;var t=e!==Ni.TEST_VID.volume;return t&&U?(window.setTimeout(function(){Ni&&Ni.prototype&&(Ni.prototype.featuresVolumeControl=e!==Ni.TEST_VID.volume)}),!1):t}catch(e){return!1}},Ni.canMuteVolume=function(){try{var e=Ni.TEST_VID.muted;return Ni.TEST_VID.muted=!e,Ni.TEST_VID.muted?ae(Ni.TEST_VID,"muted","muted"):le(Ni.TEST_VID,"muted"),e!==Ni.TEST_VID.muted}catch(e){return!1}},Ni.canControlPlaybackRate=function(){if(j&&O&&L<58)return!1;try{var e=Ni.TEST_VID.playbackRate;return Ni.TEST_VID.playbackRate=e/2+.1,e!==Ni.TEST_VID.playbackRate}catch(e){return!1}},Ni.canOverrideAttributes=function(){try{var e=function(){};Object.defineProperty(document.createElement("video"),"src",{get:e,set:e}),Object.defineProperty(document.createElement("audio"),"src",{get:e,set:e}),Object.defineProperty(document.createElement("video"),"innerHTML",{get:e,set:e}),Object.defineProperty(document.createElement("audio"),"innerHTML",{get:e,set:e})}catch(e){return!1}return!0},Ni.supportsNativeTextTracks=function(){return K||U&&O},Ni.supportsNativeVideoTracks=function(){return!(!Ni.TEST_VID||!Ni.TEST_VID.videoTracks)},Ni.supportsNativeAudioTracks=function(){return!(!Ni.TEST_VID||!Ni.TEST_VID.audioTracks)},Ni.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],[["featuresMuteControl","canMuteVolume"],["featuresPlaybackRate","canControlPlaybackRate"],["featuresSourceset","canOverrideAttributes"],["featuresNativeTextTracks","supportsNativeTextTracks"],["featuresNativeVideoTracks","supportsNativeVideoTracks"],["featuresNativeAudioTracks","supportsNativeAudioTracks"]].forEach(function(e){var t=e[0],n=e[1];ji(Ni.prototype,t,function(){return Ni[n]()},!0)}),Ni.prototype.featuresVolumeControl=Ni.canControlVolume(),Ni.prototype.movingMediaElementInDOM=!U,Ni.prototype.featuresFullscreenResize=!0,Ni.prototype.featuresProgressEvents=!0,Ni.prototype.featuresTimeupdateEvents=!0,Ni.prototype.featuresVideoFrameCallback=!(!Ni.TEST_VID||!Ni.TEST_VID.requestVideoFrameCallback),Ni.patchCanPlayType=function(){4<=A&&!M&&!O&&(Ai=Ni.TEST_VID&&Ni.TEST_VID.constructor.prototype.canPlayType,Ni.TEST_VID.constructor.prototype.canPlayType=function(e){return e&&/^application\/(?:x-|vnd\.apple\.)mpegurl/i.test(e)?"maybe":Ai.call(this,e)})},Ni.unpatchCanPlayType=function(){var e=Ni.TEST_VID.constructor.prototype.canPlayType;return Ai&&(Ni.TEST_VID.constructor.prototype.canPlayType=Ai),e},Ni.patchCanPlayType(),Ni.disposeMediaElement=function(e){if(e){for(e.parentNode&&e.parentNode.removeChild(e);e.hasChildNodes();)e.removeChild(e.firstChild);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},Ni.resetMediaElement=function(e){if(e){for(var t=e.querySelectorAll("source"),n=t.length;n--;)e.removeChild(t[n]);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(e){Ni.prototype[e]=function(){return this.el_[e]||this.el_.hasAttribute(e)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){Ni.prototype["set"+lt(t)]=function(e){(this.el_[t]=e)?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","disablePictureInPicture","played","networkState","readyState","videoWidth","videoHeight","crossOrigin"].forEach(function(e){Ni.prototype[e]=function(){return this.el_[e]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate","disablePictureInPicture","crossOrigin"].forEach(function(t){Ni.prototype["set"+lt(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(e){Ni.prototype[e]=function(){return this.el_[e]()}}),Vn.withSourceHandlers(Ni),Ni.nativeSourceHandler={},Ni.nativeSourceHandler.canPlayType=function(e){try{return Ni.TEST_VID.canPlayType(e)}catch(e){return""}},Ni.nativeSourceHandler.canHandleSource=function(e,t){if(e.type)return Ni.nativeSourceHandler.canPlayType(e.type);if(e.src){e=Dt(e.src);return Ni.nativeSourceHandler.canPlayType("video/"+e)}return""},Ni.nativeSourceHandler.handleSource=function(e,t,n){t.setSrc(e.src)},Ni.nativeSourceHandler.dispose=function(){},Ni.registerSourceHandler(Ni.nativeSourceHandler),Vn.registerTech("Html5",Ni);var Oi=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],Li={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},Di=["tiny","xsmall","small","medium","large","xlarge","huge"],Fi={};Di.forEach(function(e){var t="x"===e.charAt(0)?"x-"+e.substring(1):e;Fi[e]="vjs-layout-"+t});var Ri={tiny:210,xsmall:320,small:425,medium:768,large:1440,xlarge:2560,huge:1/0},Bi=function(u){function a(e,t,n){var i,r;if(e.id=e.id||t.id||"vjs_video_"+Ae++,(t=b(a.getTagSettings(e),t)).initChildren=!1,t.createEl=!1,t.evented=!1,t.reportTouchActivity=!1,!t.language)if("function"==typeof e.closest){var s=e.closest("[lang]");s&&s.getAttribute&&(t.language=s.getAttribute("lang"))}else for(var o=e;o&&1===o.nodeType;){if(se(o).hasOwnProperty("lang")){t.language=o.getAttribute("lang");break}o=o.parentNode}if((i=u.call(this,null,t,n)||this).boundDocumentFullscreenChange_=function(e){return i.documentFullscreenChange_(e)},i.boundFullWindowOnEscKey_=function(e){return i.fullWindowOnEscKey(e)},i.boundUpdateStyleEl_=function(e){return i.updateStyleEl_(e)},i.boundApplyInitTime_=function(e){return i.applyInitTime_(e)},i.boundUpdateCurrentBreakpoint_=function(e){return i.updateCurrentBreakpoint_(e)},i.boundHandleTechClick_=function(e){return i.handleTechClick_(e)},i.boundHandleTechDoubleClick_=function(e){return i.handleTechDoubleClick_(e)},i.boundHandleTechTouchStart_=function(e){return i.handleTechTouchStart_(e)},i.boundHandleTechTouchMove_=function(e){return i.handleTechTouchMove_(e)},i.boundHandleTechTouchEnd_=function(e){return i.handleTechTouchEnd_(e)},i.boundHandleTechTap_=function(e){return i.handleTechTap_(e)},i.isFullscreen_=!1,i.log=p(i.id_),i.fsApi_=c,i.isPosterFromTech_=!1,i.queuedCallbacks_=[],i.isReady_=!1,i.hasStarted_=!1,i.userActive_=!1,i.debugEnabled_=!1,i.audioOnlyMode_=!1,i.audioPosterMode_=!1,i.audioOnlyCache_={playerHeight:null,hiddenChildren:[]},!i.options_||!i.options_.techOrder||!i.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");i.tag=e,i.tagAttributes=e&&se(e),i.language(i.options_.language),t.languages?(r={},Object.getOwnPropertyNames(t.languages).forEach(function(e){r[e.toLowerCase()]=t.languages[e]}),i.languages_=r):i.languages_=a.prototype.options_.languages,i.resetCache_(),i.poster_=t.poster||"",i.controls_=!!t.controls,e.controls=!1,e.removeAttribute("controls"),i.changingSrc_=!1,i.playCallbacks_=[],i.playTerminatedQueue_=[],e.hasAttribute("autoplay")?i.autoplay(!0):i.autoplay(i.options_.autoplay),t.plugins&&Object.keys(t.plugins).forEach(function(e){if("function"!=typeof i[e])throw new Error('plugin "'+e+'" does not exist')}),i.scrubbing_=!1,i.el_=i.createEl(),rt(ft(i),{eventBusKey:"el_"}),i.fsApi_.requestFullscreen&&(Re(document,i.fsApi_.fullscreenchange,i.boundDocumentFullscreenChange_),i.on(i.fsApi_.fullscreenchange,i.boundDocumentFullscreenChange_)),i.fluid_&&i.on(["playerreset","resize"],i.boundUpdateStyleEl_);n=ct(i.options_);t.plugins&&Object.keys(t.plugins).forEach(function(e){i[e](t.plugins[e])}),t.debug&&i.debug(!0),i.options_.playerOptions=n,i.middleware_=[],i.playbackRates(t.playbackRates),i.initChildren(),i.isAudio("audio"===e.nodeName.toLowerCase()),i.controls()?i.addClass("vjs-controls-enabled"):i.addClass("vjs-controls-disabled"),i.el_.setAttribute("role","region"),i.isAudio()?i.el_.setAttribute("aria-label",i.localize("Audio Player")):i.el_.setAttribute("aria-label",i.localize("Video Player")),i.isAudio()&&i.addClass("vjs-audio"),i.flexNotSupported_()&&i.addClass("vjs-no-flex"),B&&i.addClass("vjs-touch-enabled"),U||i.addClass("vjs-workinghover"),a.players[i.id_]=ft(i);e=l.split(".")[0];return i.addClass("vjs-v"+e),i.userActive(!0),i.reportUserActivity(),i.one("play",function(e){return i.listenForUserActivity_(e)}),i.on("stageclick",function(e){return i.handleStageClick_(e)}),i.on("keydown",function(e){return i.handleKeyDown(e)}),i.on("languagechange",function(e){return i.handleLanguagechange(e)}),i.breakpoints(i.options_.breakpoints),i.responsive(i.options_.responsive),i.on("ready",function(){i.audioPosterMode(i.options_.audioPosterMode),i.audioOnlyMode(i.options_.audioOnlyMode)}),i}vt(a,u);var e=a.prototype;return e.dispose=function(){var t=this;this.trigger("dispose"),this.off("dispose"),Be(document,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),Be(document,"keydown",this.boundFullWindowOnEscKey_),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),a.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),Kn[this.id()]=null,ln.names.forEach(function(e){e=ln[e],e=t[e.getterName]();e&&e.off&&e.off()}),u.prototype.dispose.call(this,{restoreEl:this.options_.restoreEl})},e.createEl=function(){var t,n=this.tag,e=this.playerElIngest_=n.parentNode&&n.parentNode.hasAttribute&&n.parentNode.hasAttribute("data-vjs-player"),i="video-js"===this.tag.tagName.toLowerCase();e?t=this.el_=n.parentNode:i||(t=this.el_=u.prototype.createEl.call(this,"div"));var r,s,o=se(n);if(i){for(t=this.el_=n,n=this.tag=document.createElement("video");t.children.length;)n.appendChild(t.firstChild);ee(t,"video-js")||te(t,"video-js"),t.appendChild(n),e=this.playerElIngest_=t,Object.keys(t).forEach(function(e){try{n[e]=t[e]}catch(e){}})}n.setAttribute("tabindex","-1"),o.tabindex="-1",(D||O&&R)&&(n.setAttribute("role","application"),o.role="application"),n.removeAttribute("width"),n.removeAttribute("height"),"width"in o&&delete o.width,"height"in o&&delete o.height,Object.getOwnPropertyNames(o).forEach(function(e){i&&"class"===e||t.setAttribute(e,o[e]),i&&n.setAttribute(e,o[e])}),n.playerId=n.id,n.id+="_html5_api",n.className="vjs-tech",(n.player=t.player=this).addClass("vjs-paused"),!0!==window.VIDEOJS_NO_DYNAMIC_STYLE&&(this.styleEl_=Pe("vjs-styles-dimensions"),r=Te(".vjs-styles-defaults"),(s=Te("head")).insertBefore(this.styleEl_,r?r.nextSibling:s.firstChild)),this.fill_=!1,this.fluid_=!1,this.width(this.options_.width),this.height(this.options_.height),this.fill(this.options_.fill),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio),this.crossOrigin(this.options_.crossOrigin||this.options_.crossorigin);for(var a=n.getElementsByTagName("a"),l=0;l<a.length;l++){var c=a.item(l);te(c,"vjs-hidden"),c.setAttribute("hidden","hidden")}return n.initNetworkState_=n.networkState,n.parentNode&&!e&&n.parentNode.insertBefore(t,n),Z(n,t),this.children_.unshift(n),this.el_.setAttribute("lang",this.language_),this.el_.setAttribute("translate","no"),this.el_=t},e.crossOrigin=function(e){if(!e)return this.techGet_("crossOrigin");"anonymous"===e||"use-credentials"===e?this.techCall_("setCrossOrigin",e):d.warn('crossOrigin must be "anonymous" or "use-credentials", given "'+e+'"')},e.width=function(e){return this.dimension("width",e)},e.height=function(e){return this.dimension("height",e)},e.dimension=function(e,t){var n=e+"_";if(void 0===t)return this[n]||0;if(""===t||"auto"===t)return this[n]=void 0,void this.updateStyleEl_();var i=parseFloat(t);isNaN(i)?d.error('Improper value "'+t+'" supplied for for '+e):(this[n]=i,this.updateStyleEl_())},e.fluid=function(e){var t,n=this;if(void 0===e)return!!this.fluid_;this.fluid_=!!e,nt(this)&&this.off(["playerreset","resize"],this.boundUpdateStyleEl_),e?(this.addClass("vjs-fluid"),this.fill(!1),t=function(){n.on(["playerreset","resize"],n.boundUpdateStyleEl_)},nt(e=this)?t():(e.eventedCallbacks||(e.eventedCallbacks=[]),e.eventedCallbacks.push(t))):this.removeClass("vjs-fluid"),this.updateStyleEl_()},e.fill=function(e){if(void 0===e)return!!this.fill_;this.fill_=!!e,e?(this.addClass("vjs-fill"),this.fluid(!1)):this.removeClass("vjs-fill")},e.aspectRatio=function(e){if(void 0===e)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},e.updateStyleEl_=function(){var e,t,n,i;!0!==window.VIDEOJS_NO_DYNAMIC_STYLE?(i=(n=(void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:0<this.videoWidth()?this.videoWidth()+":"+this.videoHeight():"16:9").split(":"))[1]/n[0],e=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/i:this.videoWidth()||300,t=void 0!==this.height_?this.height_:e*i,n=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(n),je(this.styleEl_,"\n      ."+n+" {\n        width: "+e+"px;\n        height: "+t+"px;\n      }\n\n      ."+n+".vjs-fluid:not(.vjs-audio-only-mode) {\n        padding-top: "+100*i+"%;\n      }\n    ")):(t="number"==typeof this.width_?this.width_:this.options_.width,n="number"==typeof this.height_?this.height_:this.options_.height,(i=this.tech_&&this.tech_.el())&&(0<=t&&(i.width=t),0<=n&&(i.height=n)))},e.loadTech_=function(e,t){var n=this;this.tech_&&this.unloadTech_();var i=lt(e),r=e.charAt(0).toLowerCase()+e.slice(1);"Html5"!==i&&this.tag&&(Vn.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=i,this.isReady_=!1;var s=this.autoplay(),o={source:t,autoplay:s="string"==typeof this.autoplay()||!0===this.autoplay()&&this.options_.normalizeAutoplay?!1:s,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,disablePictureInPicture:this.options_.disablePictureInPicture,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset,Promise:this.options_.Promise};ln.names.forEach(function(e){e=ln[e];o[e.getterName]=n[e.privateName]}),b(o,this.options_[i]),b(o,this.options_[r]),b(o,this.options_[e.toLowerCase()]),this.tag&&(o.tag=this.tag),t&&t.src===this.cache_.src&&0<this.cache_.currentTime&&(o.startTime=this.cache_.currentTime);e=Vn.getTech(e);if(!e)throw new Error("No Tech named '"+i+"' exists! '"+i+"' should be registered using videojs.registerTech()'");this.tech_=new e(o),this.tech_.ready(Ke(this,this.handleTechReady_),!0),xt(this.textTracksJson_||[],this.tech_),Oi.forEach(function(t){n.on(n.tech_,t,function(e){return n["handleTech"+lt(t)+"_"](e)})}),Object.keys(Li).forEach(function(t){n.on(n.tech_,t,function(e){0===n.tech_.playbackRate()&&n.tech_.seeking()?n.queuedCallbacks_.push({callback:n["handleTech"+Li[t]+"_"].bind(n),event:e}):n["handleTech"+Li[t]+"_"](e)})}),this.on(this.tech_,"loadstart",function(e){return n.handleTechLoadStart_(e)}),this.on(this.tech_,"sourceset",function(e){return n.handleTechSourceset_(e)}),this.on(this.tech_,"waiting",function(e){return n.handleTechWaiting_(e)}),this.on(this.tech_,"ended",function(e){return n.handleTechEnded_(e)}),this.on(this.tech_,"seeking",function(e){return n.handleTechSeeking_(e)}),this.on(this.tech_,"play",function(e){return n.handleTechPlay_(e)}),this.on(this.tech_,"firstplay",function(e){return n.handleTechFirstPlay_(e)}),this.on(this.tech_,"pause",function(e){return n.handleTechPause_(e)}),this.on(this.tech_,"durationchange",function(e){return n.handleTechDurationChange_(e)}),this.on(this.tech_,"fullscreenchange",function(e,t){return n.handleTechFullscreenChange_(e,t)}),this.on(this.tech_,"fullscreenerror",function(e,t){return n.handleTechFullscreenError_(e,t)}),this.on(this.tech_,"enterpictureinpicture",function(e){return n.handleTechEnterPictureInPicture_(e)}),this.on(this.tech_,"leavepictureinpicture",function(e){return n.handleTechLeavePictureInPicture_(e)}),this.on(this.tech_,"error",function(e){return n.handleTechError_(e)}),this.on(this.tech_,"posterchange",function(e){return n.handleTechPosterChange_(e)}),this.on(this.tech_,"textdata",function(e){return n.handleTechTextData_(e)}),this.on(this.tech_,"ratechange",function(e){return n.handleTechRateChange_(e)}),this.on(this.tech_,"loadedmetadata",this.boundUpdateStyleEl_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===i&&this.tag||Z(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.unloadTech_=function(){var t=this;ln.names.forEach(function(e){e=ln[e];t[e.privateName]=t[e.getterName]()}),this.textTracksJson_=St(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},e.tech=function(e){return void 0===e&&d.warn("Using the tech directly can be dangerous. I hope you know what you're doing.\nSee https://github.com/videojs/video.js/issues/2617 for more info.\n"),this.tech_},e.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"click",this.boundHandleTechClick_),this.on(this.tech_,"dblclick",this.boundHandleTechDoubleClick_),this.on(this.tech_,"touchstart",this.boundHandleTechTouchStart_),this.on(this.tech_,"touchmove",this.boundHandleTechTouchMove_),this.on(this.tech_,"touchend",this.boundHandleTechTouchEnd_),this.on(this.tech_,"tap",this.boundHandleTechTap_)},e.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.boundHandleTechTap_),this.off(this.tech_,"touchstart",this.boundHandleTechTouchStart_),this.off(this.tech_,"touchmove",this.boundHandleTechTouchMove_),this.off(this.tech_,"touchend",this.boundHandleTechTouchEnd_),this.off(this.tech_,"click",this.boundHandleTechClick_),this.off(this.tech_,"dblclick",this.boundHandleTechDoubleClick_)},e.handleTechReady_=function(){this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_()},e.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.handleTechDurationChange_(),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(!0===this.autoplay()&&this.options_.normalizeAutoplay?"play":this.autoplay())},e.manualAutoplay_=function(e){var i=this;if(this.tech_&&"string"==typeof e){var t,n=function(){var e=i.muted();i.muted(!0);function t(){i.muted(e)}i.playTerminatedQueue_.push(t);var n=i.play();if(Ct(n))return n.catch(function(e){throw t(),new Error("Rejection at manualAutoplay. Restoring muted value. "+(e||""))})};if("any"!==e||this.muted()?t="muted"!==e||this.muted()?this.play():n():Ct(t=this.play())&&(t=t.catch(n)),Ct(t))return t.then(function(){i.trigger({type:"autoplay-success",autoplay:e})}).catch(function(){i.trigger({type:"autoplay-failure",autoplay:e})})}},e.updateSourceCaches_=function(e){var t=e=void 0===e?"":e,n="";"string"!=typeof t&&(t=e.src,n=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],t&&!n&&(n=function(e,t){if(!t)return"";if(e.cache_.source.src===t&&e.cache_.source.type)return e.cache_.source.type;var n=e.cache_.sources.filter(function(e){return e.src===t});if(n.length)return n[0].type;for(var i=e.$$("source"),r=0;r<i.length;r++){var s=i[r];if(s.type&&s.src&&s.src===t)return s.type}return Jn(t)}(this,t)),this.cache_.source=ct({},e,{src:t,type:n});for(var n=this.cache_.sources.filter(function(e){return e.src&&e.src===t}),i=[],r=this.$$("source"),s=[],o=0;o<r.length;o++){var a=se(r[o]);i.push(a),a.src&&a.src===t&&s.push(a.src)}s.length&&!n.length?this.cache_.sources=i:n.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=t},e.handleTechSourceset_=function(e){var t,n,i,r=this;this.changingSrc_||(t=function(e){return r.updateSourceCaches_(e)},n=this.currentSource().src,i=e.src,n&&!/^blob:/.test(n)&&/^blob:/.test(i)&&(this.lastSource_&&(this.lastSource_.tech===i||this.lastSource_.player===n)||(t=function(){})),t(i),e.src||this.tech_.any(["sourceset","loadstart"],function(e){"sourceset"!==e.type&&(e=r.techGet("currentSrc"),r.lastSource_.tech=e,r.updateSourceCaches_(e))})),this.lastSource_={player:this.currentSource().src,tech:e.src},this.trigger({src:e.src,type:"sourceset"})},e.hasStarted=function(e){if(void 0===e)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},e.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.handleTechRateChange_=function(){0<this.tech_.playbackRate()&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},e.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting");var n=this.currentTime();this.on("timeupdate",function e(){n!==t.currentTime()&&(t.removeClass("vjs-waiting"),t.off("timeupdate",e))})},e.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.removeClass("vjs-ended"),this.trigger("seeked")},e.handleTechFirstPlay_=function(){this.options_.starttime&&(d.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.handleTechEnded_=function(){this.addClass("vjs-ended"),this.removeClass("vjs-waiting"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.handleTechClick_=function(e){this.controls_&&(void 0!==this.options_&&void 0!==this.options_.userActions&&void 0!==this.options_.userActions.click&&!1===this.options_.userActions.click||(void 0!==this.options_&&void 0!==this.options_.userActions&&"function"==typeof this.options_.userActions.click?this.options_.userActions.click.call(this,e):this.paused()?wt(this.play()):this.pause()))},e.handleTechDoubleClick_=function(t){this.controls_&&(Array.prototype.some.call(this.$$(".vjs-control-bar, .vjs-modal-dialog"),function(e){return e.contains(t.target)})||void 0!==this.options_&&void 0!==this.options_.userActions&&void 0!==this.options_.userActions.doubleClick&&!1===this.options_.userActions.doubleClick||(void 0!==this.options_&&void 0!==this.options_.userActions&&"function"==typeof this.options_.userActions.doubleClick?this.options_.userActions.doubleClick.call(this,t):this.isFullscreen()?this.exitFullscreen():this.requestFullscreen()))},e.handleTechTap_=function(){this.userActive(!this.userActive())},e.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.handleTechTouchEnd_=function(e){e.cancelable&&e.preventDefault()},e.handleStageClick_=function(){this.reportUserActivity()},e.toggleFullscreenClass_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},e.documentFullscreenChange_=function(e){var t=e.target.player;t&&t!==this||(e=this.el(),!(t=document[this.fsApi_.fullscreenElement]===e)&&e.matches?t=e.matches(":"+this.fsApi_.fullscreen):!t&&e.msMatchesSelector&&(t=e.msMatchesSelector(":"+this.fsApi_.fullscreen)),this.isFullscreen(t))},e.handleTechFullscreenChange_=function(e,t){var n=this;t&&(t.nativeIOSFullscreen&&(this.addClass("vjs-ios-native-fs"),this.tech_.one("webkitendfullscreen",function(){n.removeClass("vjs-ios-native-fs")})),this.isFullscreen(t.isFullscreen))},e.handleTechFullscreenError_=function(e,t){this.trigger("fullscreenerror",t)},e.togglePictureInPictureClass_=function(){this.isInPictureInPicture()?this.addClass("vjs-picture-in-picture"):this.removeClass("vjs-picture-in-picture")},e.handleTechEnterPictureInPicture_=function(e){this.isInPictureInPicture(!0)},e.handleTechLeavePictureInPicture_=function(e){this.isInPictureInPicture(!1)},e.handleTechError_=function(){var e=this.tech_.error();this.error(e)},e.handleTechTextData_=function(){this.trigger("textdata",1<arguments.length?arguments[1]:null)},e.getCache=function(){return this.cache_},e.resetCache_=function(){this.cache_={currentTime:0,initTime:0,inactivityTimeout:this.options_.inactivityTimeout,duration:NaN,lastVolume:1,lastPlaybackRate:this.defaultPlaybackRate(),media:null,src:"",source:{},sources:[],playbackRates:[],volume:1}},e.techCall_=function(i,r){this.ready(function(){if(i in $n)return e=this.middleware_,t=this.tech_,n=r,t[t=i](e.reduce(Yn(t),n));if(i in Gn)return qn(this.middleware_,this.tech_,i,r);var e,t,n;try{this.tech_&&this.tech_[i](r)}catch(e){throw d(e),e}},!0)},e.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in Xn)return e=this.middleware_,n=this.tech_,i=t,e.reduceRight(Yn(i),n[i]());if(t in Gn)return qn(this.middleware_,this.tech_,t);var e,n,i;try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw d("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw d("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw d(e),e}}},e.play=function(){var t=this,e=this.options_.Promise||window.Promise;return e?new e(function(e){t.play_(e)}):this.play_()},e.play_=function(e){var t=this;this.playCallbacks_.push(e=void 0===e?wt:e);e=Boolean(!this.changingSrc_&&(this.src()||this.currentSrc()));if(this.waitToPlay_&&(this.off(["ready","loadstart"],this.waitToPlay_),this.waitToPlay_=null),!this.isReady_||!e)return this.waitToPlay_=function(e){t.play_()},this.one(["ready","loadstart"],this.waitToPlay_),void(e||!K&&!U||this.load());e=this.techGet_("play");null===e?this.runPlayTerminatedQueue_():this.runPlayCallbacks_(e)},e.runPlayTerminatedQueue_=function(){var e=this.playTerminatedQueue_.slice(0);this.playTerminatedQueue_=[],e.forEach(function(e){e()})},e.runPlayCallbacks_=function(t){var e=this.playCallbacks_.slice(0);this.playCallbacks_=[],this.playTerminatedQueue_=[],e.forEach(function(e){e(t)})},e.pause=function(){this.techCall_("pause")},e.paused=function(){return!1!==this.techGet_("paused")},e.played=function(){return this.techGet_("played")||_t(0,0)},e.scrubbing=function(e){if("undefined"==typeof e)return this.scrubbing_;this.scrubbing_=!!e,this.techCall_("setScrubbing",this.scrubbing_),e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},e.currentTime=function(e){return"undefined"!=typeof e?(e<0&&(e=0),this.isReady_&&!this.changingSrc_&&this.tech_&&this.tech_.isReady_?(this.techCall_("setCurrentTime",e),void(this.cache_.initTime=0)):(this.cache_.initTime=e,this.off("canplay",this.boundApplyInitTime_),void this.one("canplay",this.boundApplyInitTime_))):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.applyInitTime_=function(){this.currentTime(this.cache_.initTime)},e.duration=function(e){if(void 0===e)return void 0!==this.cache_.duration?this.cache_.duration:NaN;(e=(e=parseFloat(e))<0?1/0:e)!==this.cache_.duration&&((this.cache_.duration=e)===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),isNaN(e)||this.trigger("durationchange"))},e.remainingTime=function(){return this.duration()-this.currentTime()},e.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.buffered=function(){var e;return e=!(e=this.techGet_("buffered"))||!e.length?_t(0,0):e},e.bufferedPercent=function(){return yt(this.buffered(),this.duration())},e.bufferedEnd=function(){var e=this.buffered(),t=this.duration(),e=e.end(e.length-1);return e=t<e?t:e},e.volume=function(e){var t;return void 0!==e?(t=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=t,this.techCall_("setVolume",t),void(0<t&&this.lastVolume_(t))):(t=parseFloat(this.techGet_("volume")),isNaN(t)?1:t)},e.muted=function(e){if(void 0===e)return this.techGet_("muted")||!1;this.techCall_("setMuted",e)},e.defaultMuted=function(e){return void 0!==e?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},e.lastVolume_=function(e){if(void 0===e||0===e)return this.cache_.lastVolume;this.cache_.lastVolume=e},e.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.isFullscreen=function(e){if(void 0===e)return this.isFullscreen_;var t=this.isFullscreen_;this.isFullscreen_=Boolean(e),this.isFullscreen_!==t&&this.fsApi_.prefixed&&this.trigger("fullscreenchange"),this.toggleFullscreenClass_()},e.requestFullscreen=function(o){var e=this.options_.Promise||window.Promise;if(e){var a=this;return new e(function(e,n){function i(){a.off("fullscreenerror",r),a.off("fullscreenchange",t)}function t(){i(),e()}function r(e,t){i(),n(t)}a.one("fullscreenchange",t),a.one("fullscreenerror",r);var s=a.requestFullscreenHelper_(o);s&&(s.then(i,i),s.then(e,n))})}return this.requestFullscreenHelper_()},e.requestFullscreenHelper_=function(e){var t=this;if(this.fsApi_.prefixed||(n=this.options_.fullscreen&&this.options_.fullscreen.options||{},void 0!==e&&(n=e)),this.fsApi_.requestFullscreen){var n=this.el_[this.fsApi_.requestFullscreen](n);return n&&n.then(function(){return t.isFullscreen(!0)},function(){return t.isFullscreen(!1)}),n}this.tech_.supportsFullScreen()&&!0==!this.options_.preferFullWindow?this.techCall_("enterFullScreen"):this.enterFullWindow()},e.exitFullscreen=function(){var e=this.options_.Promise||window.Promise;if(e){var o=this;return new e(function(e,n){function i(){o.off("fullscreenerror",r),o.off("fullscreenchange",t)}function t(){i(),e()}function r(e,t){i(),n(t)}o.one("fullscreenchange",t),o.one("fullscreenerror",r);var s=o.exitFullscreenHelper_();s&&(s.then(i,i),s.then(e,n))})}return this.exitFullscreenHelper_()},e.exitFullscreenHelper_=function(){var e=this;if(this.fsApi_.requestFullscreen){var t=document[this.fsApi_.exitFullscreen]();return t&&wt(t.then(function(){return e.isFullscreen(!1)})),t}this.tech_.supportsFullScreen()&&!0==!this.options_.preferFullWindow?this.techCall_("exitFullScreen"):this.exitFullWindow()},e.enterFullWindow=function(){this.isFullscreen(!0),this.isFullWindow=!0,this.docOrigOverflow=document.documentElement.style.overflow,Re(document,"keydown",this.boundFullWindowOnEscKey_),document.documentElement.style.overflow="hidden",te(document.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.fullWindowOnEscKey=function(e){dt.isEventKey(e,"Esc")&&!0===this.isFullscreen()&&(this.isFullWindow?this.exitFullWindow():this.exitFullscreen())},e.exitFullWindow=function(){this.isFullscreen(!1),this.isFullWindow=!1,Be(document,"keydown",this.boundFullWindowOnEscKey_),document.documentElement.style.overflow=this.docOrigOverflow,ne(document.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.disablePictureInPicture=function(e){if(void 0===e)return this.techGet_("disablePictureInPicture");this.techCall_("setDisablePictureInPicture",e),this.options_.disablePictureInPicture=e,this.trigger("disablepictureinpicturechanged")},e.isInPictureInPicture=function(e){return void 0!==e?(this.isInPictureInPicture_=!!e,void this.togglePictureInPictureClass_()):!!this.isInPictureInPicture_},e.requestPictureInPicture=function(){if("pictureInPictureEnabled"in document&&!1===this.disablePictureInPicture())return this.techGet_("requestPictureInPicture")},e.exitPictureInPicture=function(){if("pictureInPictureEnabled"in document)return document.exitPictureInPicture()},e.handleKeyDown=function(e){var t=this.options_.userActions;t&&t.hotkeys&&(function(e){var t=e.tagName.toLowerCase();if(e.isContentEditable)return!0;if("input"===t)return-1===["button","checkbox","hidden","radio","reset","submit"].indexOf(e.type);return-1!==["textarea"].indexOf(t)}(this.el_.ownerDocument.activeElement)||("function"==typeof t.hotkeys?t.hotkeys.call(this,e):this.handleHotkeys(e)))},e.handleHotkeys=function(e){var t=this.options_.userActions?this.options_.userActions.hotkeys:{},n=t.fullscreenKey,i=t.muteKey,i=void 0===i?function(e){return dt.isEventKey(e,"m")}:i,t=t.playPauseKey,t=void 0===t?function(e){return dt.isEventKey(e,"k")||dt.isEventKey(e,"Space")}:t;(void 0===n?function(e){return dt.isEventKey(e,"f")}:n).call(this,e)?(e.preventDefault(),e.stopPropagation(),n=pt.getComponent("FullscreenToggle"),!1!==document[this.fsApi_.fullscreenEnabled]&&n.prototype.handleClick.call(this,e)):i.call(this,e)?(e.preventDefault(),e.stopPropagation(),pt.getComponent("MuteToggle").prototype.handleClick.call(this,e)):t.call(this,e)&&(e.preventDefault(),e.stopPropagation(),pt.getComponent("PlayToggle").prototype.handleClick.call(this,e))},e.canPlayType=function(e){for(var t,n=0,i=this.options_.techOrder;n<i.length;n++){var r=i[n],s=Vn.getTech(r);if(s=s||pt.getComponent(r)){if(s.isSupported()&&(t=s.canPlayType(e)))return t}else d.error('The "'+r+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.selectSource=function(e){function t(e,n,i){var r;return e.some(function(t){return n.some(function(e){if(r=i(t,e))return!0})}),r}var n,i=this,r=this.options_.techOrder.map(function(e){return[e,Vn.getTech(e)]}).filter(function(e){var t=e[0],e=e[1];return e?e.isSupported():(d.error('The "'+t+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),s=function(e,t){var n=e[0];if(e[1].canPlaySource(t,i.options_[n.toLowerCase()]))return{source:t,tech:n}},s=this.options_.sourceOrder?t(e,r,(n=s,function(e,t){return n(t,e)})):t(r,e,s);return s||!1},e.handleSrc_=function(e,i){var r=this;if("undefined"==typeof e)return this.cache_.src||"";this.resetRetryOnError_&&this.resetRetryOnError_();var t,n,s=Zn(e);s.length?(this.changingSrc_=!0,i||(this.cache_.sources=s),this.updateSourceCaches_(s[0]),zn(this,s[0],function(e,t){var n;return r.middleware_=t,i||(r.cache_.sources=s),r.updateSourceCaches_(e),r.src_(e)?1<s.length?r.handleSrc_(s.slice(1)):(r.changingSrc_=!1,r.setTimeout(function(){this.error({code:4,message:this.options_.notSupportedMessage})},0),void r.triggerReady()):(t=t,n=r.tech_,void t.forEach(function(e){return e.setTech&&e.setTech(n)}))}),this.options_.retryOnError&&1<s.length&&(n=function(){r.off("error",t)},this.one("error",t=function(){r.error(null),r.handleSrc_(s.slice(1),!0)}),this.one("playing",n),this.resetRetryOnError_=function(){r.off("error",t),r.off("playing",n)})):this.setTimeout(function(){this.error({code:4,message:this.options_.notSupportedMessage})},0)},e.src=function(e){return this.handleSrc_(e,!1)},e.src_=function(e){var t,n,i=this,r=this.selectSource([e]);return!r||(t=r.tech,n=this.techName_,lt(t)!==lt(n)?(this.changingSrc_=!0,this.loadTech_(r.tech,r.source),this.tech_.ready(function(){i.changingSrc_=!1})):this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0),!1)},e.load=function(){this.techCall_("load")},e.reset=function(){var e=this,t=this.options_.Promise||window.Promise;this.paused()||!t?this.doReset_():wt(this.play().then(function(){return e.doReset_()}))},e.doReset_=function(){this.tech_&&this.tech_.clearTracks("text"),this.resetCache_(),this.poster(""),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset"),this.resetControlBarUI_(),nt(this)&&this.trigger("playerreset")},e.resetControlBarUI_=function(){this.resetProgressBar_(),this.resetPlaybackRate_(),this.resetVolumeBar_()},e.resetProgressBar_=function(){this.currentTime(0);var e=this.controlBar||{},t=e.durationDisplay,e=e.remainingTimeDisplay;t&&t.updateContent(),e&&e.updateContent()},e.resetPlaybackRate_=function(){this.playbackRate(this.defaultPlaybackRate()),this.handleTechRateChange_()},e.resetVolumeBar_=function(){this.volume(1),this.trigger("volumechange")},e.currentSources=function(){var e=this.currentSource(),t=[];return 0!==Object.keys(e).length&&t.push(e),this.cache_.sources||t},e.currentSource=function(){return this.cache_.source||{}},e.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.preload=function(e){return void 0!==e?(this.techCall_("setPreload",e),void(this.options_.preload=e)):this.techGet_("preload")},e.autoplay=function(e){if(void 0===e)return this.options_.autoplay||!1;var t;"string"==typeof e&&/(any|play|muted)/.test(e)||!0===e&&this.options_.normalizeAutoplay?(this.options_.autoplay=e,this.manualAutoplay_("string"==typeof e?e:"play"),t=!1):this.options_.autoplay=!!e,t="undefined"==typeof t?this.options_.autoplay:t,this.tech_&&this.techCall_("setAutoplay",t)},e.playsinline=function(e){return void 0!==e?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},e.loop=function(e){return void 0!==e?(this.techCall_("setLoop",e),void(this.options_.loop=e)):this.techGet_("loop")},e.poster=function(e){if(void 0===e)return this.poster_;(e=e||"")!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},e.handleTechPosterChange_=function(){var e;this.poster_&&!this.options_.techCanOverridePoster||!this.tech_||!this.tech_.poster||(e=this.tech_.poster()||"")!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))},e.controls=function(e){if(void 0===e)return!!this.controls_;this.controls_!==(e=!!e)&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},e.usingNativeControls=function(e){if(void 0===e)return!!this.usingNativeControls_;this.usingNativeControls_!==(e=!!e)&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},e.error=function(t){var n=this;if(void 0===t)return this.error_||null;if(s("beforeerror").forEach(function(e){e=e(n,t);T(e)&&!Array.isArray(e)||"string"==typeof e||"number"==typeof e||null===e?t=e:n.log.error("please return a value that MediaError expects in beforeerror hooks")}),this.options_.suppressNotSupportedError&&t&&4===t.code){var e=function(){this.error(t)};return this.options_.suppressNotSupportedError=!1,this.any(["click","touchstart"],e),void this.one("loadstart",function(){this.off(["click","touchstart"],e)})}if(null===t)return this.error_=t,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close());this.error_=new bt(t),this.addClass("vjs-error"),d.error("(CODE:"+this.error_.code+" "+bt.errorTypes[this.error_.code]+")",this.error_.message,this.error_),this.trigger("error"),s("error").forEach(function(e){return e(n,n.error_)})},e.reportUserActivity=function(e){this.userActivity_=!0},e.userActive=function(e){if(void 0===e)return this.userActive_;if((e=!!e)!==this.userActive_){if(this.userActive_=e,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(e){e.stopPropagation(),e.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.listenForUserActivity_=function(){var t,n,i,r=Ke(this,this.reportUserActivity),e=function(e){r(),this.clearInterval(t)};this.on("mousedown",function(){r(),this.clearInterval(t),t=this.setInterval(r,250)}),this.on("mousemove",function(e){e.screenX===n&&e.screenY===i||(n=e.screenX,i=e.screenY,r())}),this.on("mouseup",e),this.on("mouseleave",e);var s,e=this.getChild("controlBar");!e||U||j||(e.on("mouseenter",function(e){0!==this.player().options_.inactivityTimeout&&(this.player().cache_.inactivityTimeout=this.player().options_.inactivityTimeout),this.player().options_.inactivityTimeout=0}),e.on("mouseleave",function(e){this.player().options_.inactivityTimeout=this.player().cache_.inactivityTimeout})),this.on("keydown",r),this.on("keyup",r),this.setInterval(function(){var e;this.userActivity_&&(this.userActivity_=!1,this.userActive(!0),this.clearTimeout(s),(e=this.options_.inactivityTimeout)<=0||(s=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},e)))},250)},e.playbackRate=function(e){if(void 0===e)return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1;this.techCall_("setPlaybackRate",e)},e.defaultPlaybackRate=function(e){return void 0!==e?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.isAudio=function(e){if(void 0===e)return!!this.isAudio_;this.isAudio_=!!e},e.enableAudioOnlyUI_=function(){var t=this;this.addClass("vjs-audio-only-mode");var e=this.children(),n=this.getChild("ControlBar"),i=n&&n.currentHeight();e.forEach(function(e){e!==n&&e.el_&&!e.hasClass("vjs-hidden")&&(e.hide(),t.audioOnlyCache_.hiddenChildren.push(e))}),this.audioOnlyCache_.playerHeight=this.currentHeight(),this.height(i),this.trigger("audioonlymodechange")},e.disableAudioOnlyUI_=function(){this.removeClass("vjs-audio-only-mode"),this.audioOnlyCache_.hiddenChildren.forEach(function(e){return e.show()}),this.height(this.audioOnlyCache_.playerHeight),this.trigger("audioonlymodechange")},e.audioOnlyMode=function(e){var t=this;if("boolean"!=typeof e||e===this.audioOnlyMode_)return this.audioOnlyMode_;this.audioOnlyMode_=e;var n=this.options_.Promise||window.Promise;if(n){if(e){var i=[];return this.isInPictureInPicture()&&i.push(this.exitPictureInPicture()),this.isFullscreen()&&i.push(this.exitFullscreen()),this.audioPosterMode()&&i.push(this.audioPosterMode(!1)),n.all(i).then(function(){return t.enableAudioOnlyUI_()})}return n.resolve().then(function(){return t.disableAudioOnlyUI_()})}e?(this.isInPictureInPicture()&&this.exitPictureInPicture(),this.isFullscreen()&&this.exitFullscreen(),this.enableAudioOnlyUI_()):this.disableAudioOnlyUI_()},e.enablePosterModeUI_=function(){(this.tech_&&this.tech_).hide(),this.addClass("vjs-audio-poster-mode"),this.trigger("audiopostermodechange")},e.disablePosterModeUI_=function(){(this.tech_&&this.tech_).show(),this.removeClass("vjs-audio-poster-mode"),this.trigger("audiopostermodechange")},e.audioPosterMode=function(e){var t=this;if("boolean"!=typeof e||e===this.audioPosterMode_)return this.audioPosterMode_;this.audioPosterMode_=e;var n=this.options_.Promise||window.Promise;return n?e?(this.audioOnlyMode()?this.audioOnlyMode(!1):n.resolve()).then(function(){t.enablePosterModeUI_()}):n.resolve().then(function(){t.disablePosterModeUI_()}):e?(this.audioOnlyMode()&&this.audioOnlyMode(!1),void this.enablePosterModeUI_()):void this.disablePosterModeUI_()},e.addTextTrack=function(e,t,n){if(this.tech_)return this.tech_.addTextTrack(e,t,n)},e.addRemoteTextTrack=function(e,t){if(this.tech_)return this.tech_.addRemoteTextTrack(e,t)},e.removeRemoteTextTrack=function(e){var t=(t=(e=void 0===e?{}:e).track)||e;if(this.tech_)return this.tech_.removeRemoteTextTrack(t)},e.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},e.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.language=function(e){if(void 0===e)return this.language_;this.language_!==String(e).toLowerCase()&&(this.language_=String(e).toLowerCase(),nt(this)&&this.trigger("languagechange"))},e.languages=function(){return ct(a.prototype.options_.languages,this.languages_)},e.toJSON=function(){var e=ct(this.options_),t=e.tracks;e.tracks=[];for(var n=0;n<t.length;n++){var i=t[n];(i=ct(i)).player=void 0,e.tracks[n]=i}return e},e.createModal=function(e,t){var n=this;(t=t||{}).content=e||"";var i=new jt(this,t);return this.addChild(i),i.on("dispose",function(){n.removeChild(i)}),i.open(),i},e.updateCurrentBreakpoint_=function(){if(this.responsive())for(var e=this.currentBreakpoint(),t=this.currentWidth(),n=0;n<Di.length;n++){var i=Di[n];if(t<=this.breakpoints_[i]){if(e===i)return;e&&this.removeClass(Fi[e]),this.addClass(Fi[i]),this.breakpoint_=i;break}}},e.removeCurrentBreakpoint_=function(){var e=this.currentBreakpointClass();this.breakpoint_="",e&&this.removeClass(e)},e.breakpoints=function(e){return void 0===e||(this.breakpoint_="",this.breakpoints_=b({},Ri,e),this.updateCurrentBreakpoint_()),b(this.breakpoints_)},e.responsive=function(e){return void 0===e?this.responsive_:(e=Boolean(e))!==this.responsive_?((this.responsive_=e)?(this.on("playerresize",this.boundUpdateCurrentBreakpoint_),this.updateCurrentBreakpoint_()):(this.off("playerresize",this.boundUpdateCurrentBreakpoint_),this.removeCurrentBreakpoint_()),e):void 0},e.currentBreakpoint=function(){return this.breakpoint_},e.currentBreakpointClass=function(){return Fi[this.breakpoint_]||""},e.loadMedia=function(e,t){var n,i,r,s=this;e&&"object"==typeof e&&(this.reset(),this.cache_.media=ct(e),n=(r=this.cache_.media).artwork,i=r.poster,e=r.src,r=r.textTracks,!n&&i&&(this.cache_.media.artwork=[{src:i,type:Jn(i)}]),e&&this.src(e),i&&this.poster(i),Array.isArray(r)&&r.forEach(function(e){return s.addRemoteTextTrack(e,!1)}),this.ready(t))},e.getMedia=function(){if(this.cache_.media)return ct(this.cache_.media);var e=this.poster(),t={src:this.currentSources(),textTracks:Array.prototype.map.call(this.remoteTextTracks(),function(e){return{kind:e.kind,label:e.label,language:e.language,src:e.src}})};return e&&(t.poster=e,t.artwork=[{src:t.poster,type:Jn(t.poster)}]),t},a.getTagSettings=function(e){var t,n={sources:[],tracks:[]},i=se(e),r=i["data-setup"];if(ee(e,"vjs-fill")&&(i.fill=!0),ee(e,"vjs-fluid")&&(i.fluid=!0),null!==r&&(r=(t=kt(r||"{}"))[0],t=t[1],r&&d.error(r),b(i,t)),b(n,i),e.hasChildNodes())for(var s=e.childNodes,o=0,a=s.length;o<a;o++){var l=s[o],c=l.nodeName.toLowerCase();"source"===c?n.sources.push(se(l)):"track"===c&&n.tracks.push(se(l))}return n},e.flexNotSupported_=function(){var e=document.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},e.debug=function(e){if(void 0===e)return this.debugEnabled_;e?(this.trigger("debugon"),this.previousLogLevel_=this.log.level,this.log.level("debug"),this.debugEnabled_=!0):(this.trigger("debugoff"),this.log.level(this.previousLogLevel_),this.previousLogLevel_=void 0,this.debugEnabled_=!1)},e.playbackRates=function(e){if(void 0===e)return this.cache_.playbackRates;Array.isArray(e)&&e.every(function(e){return"number"==typeof e})&&(this.cache_.playbackRates=e,this.trigger("playbackrateschange"))},a}(pt);ln.names.forEach(function(e){var t=ln[e];Bi.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),Bi.prototype.crossorigin=Bi.prototype.crossOrigin,Bi.players={};un=window.navigator;Bi.prototype.options_={techOrder:Vn.defaultTechOrder_,html5:{},inactivityTimeout:2e3,playbackRates:[],liveui:!1,children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","liveTracker","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:un&&(un.languages&&un.languages[0]||un.userLanguage||un.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media.",normalizeAutoplay:!1,fullscreen:{options:{navigationUI:"hide"}},breakpoints:{},responsive:!1,audioOnlyMode:!1,audioPosterMode:!1},["ended","seeking","seekable","networkState","readyState"].forEach(function(e){Bi.prototype[e]=function(){return this.techGet_(e)}}),Oi.forEach(function(e){Bi.prototype["handleTech"+lt(e)+"_"]=function(){return this.trigger(e)}}),pt.registerComponent("Player",Bi);var Hi=v(function(n){function i(e,t){return n.exports=i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}n.exports=i});function Vi(e){return Yi.hasOwnProperty(e)}function Ui(e){return Vi(e)?Yi[e]:void 0}function Ki(e,t,n){n=(n?"before":"")+"pluginsetup",e.trigger(n,t),e.trigger(n+":"+t.name,t)}function Wi(t,n){function i(){Ki(this,{name:t,plugin:n,instance:null},!0);var e=n.apply(this,arguments);return Qi(this,t),Ki(this,{name:t,plugin:n,instance:e}),e}return Object.keys(n).forEach(function(e){i[e]=n[e]}),i}function zi(r,s){return s.prototype.name=r,function(){Ki(this,{name:r,plugin:s,instance:null},!0);for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=Xi(s,[this].concat(t));return this[r]=function(){return i},Ki(this,i.getEventHash()),i}}var qi=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}},Xi=v(function(i){function r(e,t,n){return qi()?i.exports=r=Reflect.construct:i.exports=r=function(e,t,n){var i=[null];i.push.apply(i,t);i=new(Function.bind.apply(e,i));return n&&Hi(i,n.prototype),i},r.apply(null,arguments)}i.exports=r}),$i="plugin",Gi="activePlugins_",Yi={},Qi=function(e,t){e[Gi]=e[Gi]||{},e[Gi][t]=!0},Ji=function(){function n(e){if(this.constructor===n)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,this.log||(this.log=this.player.log.createLogger(this.name)),rt(this),delete this.trigger,ot(this,this.constructor.defaultState),Qi(e,this.name),this.dispose=this.dispose.bind(this),e.on("dispose",this.dispose)}var e=n.prototype;return e.version=function(){return this.constructor.VERSION},e.getEventHash=function(e){return(e=void 0===e?{}:e).name=this.name,e.plugin=this.constructor,e.instance=this,e},e.trigger=function(e,t){return He(this.eventBusEl_,e,this.getEventHash(t=void 0===t?{}:t))},e.handleStateChanged=function(e){},e.dispose=function(){var e=this.name,t=this.player;this.trigger("dispose"),this.off(),t.off("dispose",this.dispose),t[Gi][e]=!1,this.player=this.state=null,t[e]=zi(e,Yi[e])},n.isBasic=function(e){e="string"==typeof e?Ui(e):e;return"function"==typeof e&&!n.prototype.isPrototypeOf(e.prototype)},n.registerPlugin=function(e,t){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+typeof e+".");if(Vi(e))d.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Bi.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof t)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+typeof t+".");return Yi[e]=t,e!==$i&&(n.isBasic(t)?Bi.prototype[e]=Wi(e,t):Bi.prototype[e]=zi(e,t)),t},n.deregisterPlugin=function(e){if(e===$i)throw new Error("Cannot de-register base plugin.");Vi(e)&&(delete Yi[e],delete Bi.prototype[e])},n.getPlugins=function(e){var n;return(e=void 0===e?Object.keys(Yi):e).forEach(function(e){var t=Ui(e);t&&((n=n||{})[e]=t)}),n},n.getPluginVersion=function(e){e=Ui(e);return e&&e.VERSION||""},n}();Ji.getPlugin=Ui,Ji.BASE_PLUGIN_NAME=$i,Ji.registerPlugin($i,Ji),Bi.prototype.usingPlugin=function(e){return!!this[Gi]&&!0===this[Gi][e]},Bi.prototype.hasPlugin=function(e){return!!Vi(e)};var Zi=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Hi(e,t)},er=function(e){return 0===e.indexOf("#")?e.slice(1):e};function tr(e,t,n){if(r=tr.getPlayer(e))return t&&d.warn('Player "'+e+'" is already initialised. Options will not be applied.'),n&&r.ready(n),r;var i="string"==typeof e?Te("#"+er(e)):e;if(!$(i))throw new TypeError("The element or ID supplied is not valid. (videojs)");i.ownerDocument.defaultView&&i.ownerDocument.body.contains(i)||d.warn("The element supplied is not included in the DOM"),!0===(t=t||{}).restoreEl&&(t.restoreEl=(i.parentNode&&i.parentNode.hasAttribute("data-vjs-player")?i.parentNode:i).cloneNode(!0)),s("beforesetup").forEach(function(e){e=e(i,ct(t));T(e)&&!Array.isArray(e)?t=ct(t,e):d.error("please return an object in beforesetup hooks")});var r=new(pt.getComponent("Player"))(i,t,n);return s("setup").forEach(function(e){return e(r)}),r}return tr.hooks_=n,tr.hooks=s,tr.hook=function(e,t){s(e,t)},tr.hookOnce=function(n,e){s(n,[].concat(e).map(function(t){return function e(){return i(n,e),t.apply(void 0,arguments)}}))},tr.removeHook=i,!0!==window.VIDEOJS_NO_DYNAMIC_STYLE&&X()&&((E=Te(".vjs-styles-defaults"))||(E=Pe("vjs-styles-defaults"),(un=Te("head"))&&un.insertBefore(E,un.firstChild),je(E,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid:not(.vjs-audio-only-mode) {\n        padding-top: 56.25%\n      }\n    "))),Se(1,tr),tr.VERSION=l,tr.options=Bi.prototype.options_,tr.getPlayers=function(){return Bi.players},tr.getPlayer=function(e){var t=Bi.players;if("string"==typeof e){var n=er(e),i=t[n];if(i)return i;n=Te("#"+n)}else n=e;if($(n)){e=n.player,n=n.playerId;if(e||t[n])return e||t[n]}},tr.getAllPlayers=function(){return Object.keys(Bi.players).map(function(e){return Bi.players[e]}).filter(Boolean)},tr.players=Bi.players,tr.getComponent=pt.getComponent,tr.registerComponent=function(e,t){Vn.isTech(t)&&d.warn("The "+e+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),pt.registerComponent.call(pt,e,t)},tr.getTech=Vn.getTech,tr.registerTech=Vn.registerTech,tr.use=function(e,t){Un[e]=Un[e]||[],Un[e].push(t)},Object.defineProperty(tr,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(tr.middleware,"TERMINATOR",{value:Wn,writeable:!1,enumerable:!0}),tr.browser=W,tr.TOUCH_ENABLED=B,tr.extend=function(e,t){var n,i=function(){e.apply(this,arguments)},r={};for(n in"object"==typeof(t=void 0===t?{}:t)?(t.constructor!==Object.prototype.constructor&&(i=t.constructor),r=t):"function"==typeof t&&(i=t),Zi(i,e),e&&(i.super_=e),r)r.hasOwnProperty(n)&&(i.prototype[n]=r[n]);return i},tr.mergeOptions=ct,tr.bind=Ke,tr.registerPlugin=Ji.registerPlugin,tr.deregisterPlugin=Ji.deregisterPlugin,tr.plugin=function(e,t){return d.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Ji.registerPlugin(e,t)},tr.getPlugins=Ji.getPlugins,tr.getPlugin=Ji.getPlugin,tr.getPluginVersion=Ji.getPluginVersion,tr.addLanguage=function(e,t){var n;return e=(""+e).toLowerCase(),tr.options.languages=ct(tr.options.languages,((n={})[e]=t,n)),tr.options.languages[e]},tr.log=d,tr.createLogger=p,tr.createTimeRange=tr.createTimeRanges=_t,tr.formatTime=ci,tr.setFormatTime=function(e){li=e},tr.resetFormatTime=function(){li=ai},tr.parseUrl=Ot,tr.isCrossOrigin=Ft,tr.EventTarget=qe,tr.on=Re,tr.one=Ve,tr.off=Be,tr.trigger=He,tr.xhr=Jt,tr.TextTrack=sn,tr.AudioTrack=P,tr.VideoTrack=F,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(e){tr[e]=function(){return d.warn("videojs."+e+"() is deprecated; use videojs.dom."+e+"() instead"),Ce[e].apply(null,arguments)}}),tr.computedStyle=C,tr.dom=Ce,tr.url=qt,tr.defineLazyProperty=ji,tr.addLanguage("en",{"Non-Fullscreen":"Exit Fullscreen"}),tr});