// 4.7.10 (2018-04-03)
!function(){"use strict";var e,t,n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v=function(e){return function(){return e}},V={noop:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},noarg:function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n()}},compose:function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,arguments))}},constant:v,identity:function(e){return e},tripleEquals:function(e,t){return e===t},curry:function(i){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];for(var a=new Array(arguments.length-1),n=1;n<arguments.length;n++)a[n-1]=arguments[n];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var o=a.concat(n);return i.apply(null,o)}},not:function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,arguments)}},die:function(e){return function(){throw new Error(e)}},apply:function(e){return e()},call:function(e){e()},never:v(!1),always:v(!0)},y=V.never,b=V.always,C=function(){return x},x=(r={fold:function(e,t){return e()},is:y,isSome:y,isNone:b,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},or:n,orThunk:t,map:C,ap:C,each:function(){},bind:C,flatten:C,exists:y,forall:b,filter:C,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:V.constant("none()")},Object.freeze&&Object.freeze(r),r),w=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:b,isNone:y,getOr:e,getOrThunk:e,getOrDie:e,or:t,orThunk:t,map:function(e){return w(e(n))},ap:function(e){return e.fold(C,function(e){return w(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:x},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(y,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},A={some:w,none:C,from:function(e){return null===e||e===undefined?x:w(e)}},N=(o=Array.prototype.indexOf)===undefined?function(e,t){return B(e,t)}:function(e,t){return o.call(e,t)},E=function(e,t){return-1<N(e,t)},S=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},k=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},T=function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)},R=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},_=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return A.some(n);return A.none()},B=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},D=Array.prototype.push,O=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);D.apply(t,e[n])}return t},P=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},L=Array.prototype.slice,H={map:S,each:k,eachr:T,partition:function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o,e)?n:r).push(a)}return{pass:n,fail:r}},filter:R,groupBy:function(e,t){if(0===e.length)return[];for(var n=t(e[0]),r=[],o=[],i=0,a=e.length;i<a;i++){var u=e[i],s=t(u);s!==n&&(r.push(o),o=[]),n=s,o.push(u)}return 0!==o.length&&r.push(o),r},indexOf:function(e,t){var n=N(e,t);return-1===n?A.none():A.some(n)},foldr:function(e,t,n){return T(e,function(e){n=t(n,e)}),n},foldl:function(e,t,n){return k(e,function(e){n=t(n,e)}),n},find:function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return A.some(o)}return A.none()},findIndex:_,flatten:O,bind:function(e,t){var n=S(e,t);return O(n)},forall:P,exists:function(e,t){return _(e,t).isSome()},contains:E,equal:function(e,n){return e.length===n.length&&P(e,function(e,t){return e===n[t]})},reverse:function(e){var t=L.call(e,0);return t.reverse(),t},chunk:function(e,t){for(var n=[],r=0;r<e.length;r+=t){var o=e.slice(r,r+t);n.push(o)}return n},difference:function(e,t){return R(e,function(e){return!E(t,e)})},mapToObject:function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n},pure:function(e){return[e]},sort:function(e,t){var n=L.call(e,0);return n.sort(t),n},range:function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n},head:function(e){return 0===e.length?A.none():A.some(e[0])},last:function(e){return 0===e.length?A.none():A.some(e[e.length-1])}},I="undefined"!=typeof window?window:Function("return this;")(),M=function(e,t){for(var n=t!==undefined&&null!==t?t:I,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n},F=function(e,t){var n=e.split(".");return M(n,t)},z={getOrDie:function(e,t){var n=F(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}},U=function(){return z.getOrDie("URL")},q={createObjectURL:function(e){return U().createObjectURL(e)},revokeObjectURL:function(e){U().revokeObjectURL(e)}},j=navigator,$=j.userAgent,W=function(e){return"matchMedia"in window&&matchMedia(e).matches};d=/Android/.test($),a=(a=!(i=/WebKit/.test($))&&/MSIE/gi.test($)&&/Explorer/gi.test(j.appName))&&/MSIE (\w+)\./.exec($)[1],u=-1!==$.indexOf("Trident/")&&(-1!==$.indexOf("rv:")||-1!==j.appName.indexOf("Netscape"))&&11,s=-1!==$.indexOf("Edge/")&&!a&&!u&&12,a=a||u||s,c=!i&&!u&&/Gecko/.test($),l=-1!==$.indexOf("Mac"),f=/(iPad|iPhone)/.test($),m="FormData"in window&&"FileReader"in window&&"URL"in window&&!!q.createObjectURL,p=W("only screen and (max-device-width: 480px)")&&(d||f),g=W("only screen and (min-width: 800px)")&&(d||f),h=-1!==$.indexOf("Windows Phone"),s&&(i=!1);var K,X,Y,G,J,Q,Z,ee,te,ne,re,oe,ie,ae,ue,se,ce,le,fe,de={opera:!1,webkit:i,ie:a,gecko:c,mac:l,iOS:f,android:d,contentEditable:!f||m||534<=parseInt($.match(/AppleWebKit\/(\d*)/)[1],10),transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:8!==a,range:window.getSelection&&"Range"in window,documentMode:a&&!s?document.documentMode||7:10,fileApi:m,ceFalse:!1===a||8<a,cacheSuffix:null,container:null,overrideViewPort:null,experimentalShadowDom:!1,canHaveCSP:!1===a||11<a,desktop:!p&&!g,windowsPhone:h},me=window.Promise?window.Promise:function(){function r(e,t){return function(){e.apply(t,arguments)}}var e=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=function(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],l(e,r(o,this),r(u,this))},t=i.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)};function a(r){var o=this;null!==this._state?t(function(){var e=o._state?r.onFulfilled:r.onRejected;if(null!==e){var t;try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void l(r(t,e),r(o,this),r(u,this))}this._state=!0,this._value=e,s.call(this)}catch(n){u.call(this,n)}}function u(e){this._state=!1,this._value=e,s.call(this)}function s(){for(var e=0,t=this._deferreds.length;e<t;e++)a.call(this,this._deferreds[e]);this._deferreds=null}function c(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function l(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(o){if(r)return;r=!0,n(o)}}return i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(n,r){var o=this;return new i(function(e,t){a.call(o,new c(n,r,e,t))})},i.all=function(){var s=Array.prototype.slice.call(1===arguments.length&&e(arguments[0])?arguments[0]:arguments);return new i(function(o,i){if(0===s.length)return o([]);var a=s.length;function u(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){u(t,e)},i)}s[t]=e,0==--a&&o(s)}catch(r){i(r)}}for(var e=0;e<s.length;e++)u(e,s[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i}(),pe=function(e,t){return"number"!=typeof t&&(t=0),setTimeout(e,t)},ge=function(e,t){return"number"!=typeof t&&(t=1),setInterval(e,t)},he=function(t,n){var r,e;return(e=function(){var e=arguments;clearTimeout(r),r=pe(function(){t.apply(this,e)},n)}).stop=function(){clearTimeout(r)},e},ve={requestAnimationFrame:function(e,t){K?K.then(e):K=new me(function(e){t||(t=document.body),function(e,t){var n,r=window.requestAnimationFrame,o=["ms","moz","webkit"];for(n=0;n<o.length&&!r;n++)r=window[o[n]+"RequestAnimationFrame"];r||(r=function(e){window.setTimeout(e,0)}),r(e,t)}(e,t)}).then(e)},setTimeout:pe,setInterval:ge,setEditorTimeout:function(e,t,n){return pe(function(){e.removed||t()},n)},setEditorInterval:function(e,t,n){var r;return r=ge(function(){e.removed?clearInterval(r):t()},n)},debounce:he,throttle:he,clearInterval:function(e){return clearInterval(e)},clearTimeout:function(e){return clearTimeout(e)}},ye=/^(?:mouse|contextmenu)|click/,be={keyLocation:1,layerX:1,layerY:1,returnValue:1,webkitMovementX:1,webkitMovementY:1,keyIdentifier:1},Ce=function(){return!1},xe=function(){return!0},we=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ne=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},Ee=function(e,t){var n,r,o,i,a,u,s=t||{};for(n in e)be[n]||(s[n]=e[n]);if(s.target||(s.target=s.srcElement||document),de.experimentalShadowDom&&(s.target=(r=e,o=s.target,a=o,(i=r.path)&&0<i.length&&(a=i[0]),r.composedPath&&(i=r.composedPath())&&0<i.length&&(a=i[0]),a)),e&&ye.test(e.type)&&e.pageX===undefined&&e.clientX!==undefined){var c=s.target.ownerDocument||document,l=c.documentElement,f=c.body;s.pageX=e.clientX+(l&&l.scrollLeft||f&&f.scrollLeft||0)-(l&&l.clientLeft||f&&f.clientLeft||0),s.pageY=e.clientY+(l&&l.scrollTop||f&&f.scrollTop||0)-(l&&l.clientTop||f&&f.clientTop||0)}return s.preventDefault=function(){s.isDefaultPrevented=xe,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},s.stopPropagation=function(){s.isPropagationStopped=xe,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},!(s.stopImmediatePropagation=function(){s.isImmediatePropagationStopped=xe,s.stopPropagation()})==((u=s).isDefaultPrevented===xe||u.isDefaultPrevented===Ce)&&(s.isDefaultPrevented=Ce,s.isPropagationStopped=Ce,s.isImmediatePropagationStopped=Ce),"undefined"==typeof s.metaKey&&(s.metaKey=!1),s},Se=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){n.domLoaded||(n.domLoaded=!0,t(o))},u=function(){i()&&(Ne(r,"readystatechange",u),a())},s=function(){try{r.documentElement.doScroll("left")}catch(e){return void ve.setTimeout(s)}a()};!r.addEventListener||de.ie&&de.ie<11?(we(r,"readystatechange",u),r.documentElement.doScroll&&e.self===e.top&&s()):i()?a():we(e,"DOMContentLoaded",a),we(e,"load",a)}},ke=function(){var m,p,g,h,v,y=this,b={};p="mce-data-"+(+new Date).toString(32),h="onmouseenter"in document.documentElement,g="onfocusin"in document.documentElement,v={mouseenter:"mouseover",mouseleave:"mouseout"},m=1,y.domLoaded=!1,y.events=b;var C=function(e,t){var n,r,o,i,a=b[t];if(n=a&&a[e.type])for(r=0,o=n.length;r<o;r++)if((i=n[r])&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return};y.bind=function(e,t,n,r){var o,i,a,u,s,c,l,f=window,d=function(e){C(Ee(e||f.event),o)};if(e&&3!==e.nodeType&&8!==e.nodeType){for(e[p]?o=e[p]:(o=m++,e[p]=o,b[o]={}),r=r||e,a=(t=t.split(" ")).length;a--;)c=d,s=l=!1,"DOMContentLoaded"===(u=t[a])&&(u="ready"),y.domLoaded&&"ready"===u&&"complete"===e.readyState?n.call(r,Ee({type:u})):(h||(s=v[u])&&(c=function(e){var t,n;if(t=e.currentTarget,(n=e.relatedTarget)&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=Ee(e||f.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,C(e,o))}),g||"focusin"!==u&&"focusout"!==u||(l=!0,s="focusin"===u?"focus":"blur",c=function(e){(e=Ee(e||f.event)).type="focus"===e.type?"focusin":"focusout",C(e,o)}),(i=b[o][u])?"ready"===u&&y.domLoaded?n({type:u}):i.push({func:n,scope:r}):(b[o][u]=i=[{func:n,scope:r}],i.fakeName=s,i.capture=l,i.nativeHandler=c,"ready"===u?Se(e,c,y):we(e,s||u,c,l)));return e=i=0,n}},y.unbind=function(e,t,n){var r,o,i,a,u,s;if(!e||3===e.nodeType||8===e.nodeType)return y;if(r=e[p]){if(s=b[r],t){for(i=(t=t.split(" ")).length;i--;)if(o=s[u=t[i]]){if(n)for(a=o.length;a--;)if(o[a].func===n){var c=o.nativeHandler,l=o.fakeName,f=o.capture;(o=o.slice(0,a).concat(o.slice(a+1))).nativeHandler=c,o.fakeName=l,o.capture=f,s[u]=o}n&&0!==o.length||(delete s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture))}}else{for(u in s)o=s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture);s={}}for(u in s)return y;delete b[r];try{delete e[p]}catch(d){e[p]=null}}return y},y.fire=function(e,t,n){var r;if(!e||3===e.nodeType||8===e.nodeType)return y;for((n=Ee(null,n)).type=t,n.target=e;(r=e[p])&&C(n,r),(e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow)&&!n.isPropagationStopped(););return y},y.clean=function(e){var t,n,r=y.unbind;if(!e||3===e.nodeType||8===e.nodeType)return y;if(e[p]&&r(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(r(e),t=(n=e.getElementsByTagName("*")).length;t--;)(e=n[t])[p]&&r(e);return y},y.destroy=function(){b={}},y.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}};ke.Event=new ke,ke.Event.bind(window,"ready",function(){});var Te="sizzle"+-new Date,Ae=window.document,Re=0,_e=0,Be=lt(),De=lt(),Oe=lt(),Pe=function(e,t){return e===t&&(oe=!0),0},Le=typeof undefined,Ie=1<<31,Me={}.hasOwnProperty,Fe=[],ze=Fe.pop,Ue=Fe.push,qe=Fe.push,Ve=Fe.slice,He=Fe.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},je="[\\x20\\t\\r\\n\\f]",$e="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",We="\\["+je+"*("+$e+")(?:"+je+"*([*^$|!~]?=)"+je+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$e+"))|)"+je+"*\\]",Ke=":("+$e+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+We+")*)|.*)\\)|)",Xe=new RegExp("^"+je+"+|((?:^|[^\\\\])(?:\\\\.)*)"+je+"+$","g"),Ye=new RegExp("^"+je+"*,"+je+"*"),Ge=new RegExp("^"+je+"*([>+~]|"+je+")"+je+"*"),Je=new RegExp("="+je+"*([^\\]'\"]*?)"+je+"*\\]","g"),Qe=new RegExp(Ke),Ze=new RegExp("^"+$e+"$"),et={ID:new RegExp("^#("+$e+")"),CLASS:new RegExp("^\\.("+$e+")"),TAG:new RegExp("^("+$e+"|[*])"),ATTR:new RegExp("^"+We),PSEUDO:new RegExp("^"+Ke),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+je+"*(even|odd|(([+-]|)(\\d*)n|)"+je+"*(?:([+-]|)"+je+"*(\\d+)|))"+je+"*\\)|)","i"),bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:new RegExp("^"+je+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+je+"*((?:-\\d)?\\d*)"+je+"*\\)|)(?=[^-]|$)","i")},tt=/^(?:input|select|textarea|button)$/i,nt=/^h\d$/i,rt=/^[^{]+\{\s*\[native \w/,ot=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,it=/[+~]/,at=/'|\\/g,ut=new RegExp("\\\\([\\da-f]{1,6}"+je+"?|("+je+")|.)","ig"),st=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{qe.apply(Fe=Ve.call(Ae.childNodes),Ae.childNodes),Fe[Ae.childNodes.length].nodeType}catch(Vx){qe={apply:Fe.length?function(e,t){Ue.apply(e,Ve.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}var ct=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m;if((t?t.ownerDocument||t:Ae)!==ae&&ie(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(u=(t=t||ae).nodeType)&&9!==u)return[];if(se&&!r){if(o=ot.exec(e))if(a=o[1]){if(9===u){if(!(i=t.getElementById(a))||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&fe(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return qe.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&Y.getElementsByClassName)return qe.apply(n,t.getElementsByClassName(a)),n}if(Y.qsa&&(!ce||!ce.test(e))){if(f=l=Te,d=t,m=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){for(c=Z(e),(l=t.getAttribute("id"))?f=l.replace(at,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",s=c.length;s--;)c[s]=f+yt(c[s]);d=it.test(e)&&ht(t.parentNode)||t,m=c.join(",")}if(m)try{return qe.apply(n,d.querySelectorAll(m)),n}catch(p){}finally{l||t.removeAttribute("id")}}}return te(e.replace(Xe,"$1"),t,n,r)};function lt(){var r=[];return function e(t,n){return r.push(t+" ")>G.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function ft(e){return e[Te]=!0,e}function dt(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||Ie)-(~e.sourceIndex||Ie);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function mt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function pt(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function gt(a){return ft(function(i){return i=+i,ft(function(e,t){for(var n,r=a([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function ht(e){return e&&typeof e.getElementsByTagName!==Le&&e}for(X in Y=ct.support={},Q=ct.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},ie=ct.setDocument=function(e){var t,s=e?e.ownerDocument||e:Ae,n=s.defaultView;return s!==ae&&9===s.nodeType&&s.documentElement?(ue=(ae=s).documentElement,se=!Q(s),n&&n!==function(e){try{return e.top}catch(t){}return null}(n)&&(n.addEventListener?n.addEventListener("unload",function(){ie()},!1):n.attachEvent&&n.attachEvent("onunload",function(){ie()})),Y.attributes=!0,Y.getElementsByTagName=!0,Y.getElementsByClassName=rt.test(s.getElementsByClassName),Y.getById=!0,G.find.ID=function(e,t){if(typeof t.getElementById!==Le&&se){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},G.filter.ID=function(e){var t=e.replace(ut,st);return function(e){return e.getAttribute("id")===t}},G.find.TAG=Y.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==Le)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"===e){for(;n=i[o++];)1===n.nodeType&&r.push(n);return r}return i},G.find.CLASS=Y.getElementsByClassName&&function(e,t){if(se)return t.getElementsByClassName(e)},le=[],ce=[],Y.disconnectedMatch=!0,ce=ce.length&&new RegExp(ce.join("|")),le=le.length&&new RegExp(le.join("|")),t=rt.test(ue.compareDocumentPosition),fe=t||rt.test(ue.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Pe=t?function(e,t){if(e===t)return oe=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!Y.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument===Ae&&fe(Ae,e)?-1:t===s||t.ownerDocument===Ae&&fe(Ae,t)?1:re?He.call(re,e)-He.call(re,t):0:4&n?-1:1)}:function(e,t){if(e===t)return oe=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,a=[e],u=[t];if(!o||!i)return e===s?-1:t===s?1:o?-1:i?1:re?He.call(re,e)-He.call(re,t):0;if(o===i)return dt(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;a[r]===u[r];)r++;return r?dt(a[r],u[r]):a[r]===Ae?-1:u[r]===Ae?1:0},s):ae},ct.matches=function(e,t){return ct(e,null,null,t)},ct.matchesSelector=function(e,t){if((e.ownerDocument||e)!==ae&&ie(e),t=t.replace(Je,"='$1']"),Y.matchesSelector&&se&&(!le||!le.test(t))&&(!ce||!ce.test(t)))try{var n=(void 0).call(e,t);if(n||Y.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(Vx){}return 0<ct(t,ae,null,[e]).length},ct.contains=function(e,t){return(e.ownerDocument||e)!==ae&&ie(e),fe(e,t)},ct.attr=function(e,t){(e.ownerDocument||e)!==ae&&ie(e);var n=G.attrHandle[t.toLowerCase()],r=n&&Me.call(G.attrHandle,t.toLowerCase())?n(e,t,!se):undefined;return r!==undefined?r:Y.attributes||!se?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},ct.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ct.uniqueSort=function(e){var t,n=[],r=0,o=0;if(oe=!Y.detectDuplicates,re=!Y.sortStable&&e.slice(0),e.sort(Pe),oe){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return re=null,e},J=ct.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=J(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=J(t);return n},(G=ct.selectors={cacheLength:50,createPseudo:ft,match:et,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ut,st),e[3]=(e[3]||e[4]||e[5]||"").replace(ut,st),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ct.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ct.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return et.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Qe.test(n)&&(t=Z(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ut,st).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=Be[e+" "];return t||(t=new RegExp("(^|"+je+")"+e+"("+je+"|$)"))&&Be(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==Le&&e.getAttribute("class")||"")})},ATTR:function(n,r,o){return function(e){var t=ct.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===o:"!="===r?t!==o:"^="===r?o&&0===t.indexOf(o):"*="===r?o&&-1<t.indexOf(o):"$="===r?o&&t.slice(-o.length)===o:"~="===r?-1<(" "+t+" ").indexOf(o):"|="===r&&(t===o||t.slice(0,o.length+1)===o+"-"))}},CHILD:function(m,e,t,p,g){var h="nth"!==m.slice(0,3),v="last"!==m.slice(-4),y="of-type"===e;return 1===p&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,a,u,s,c=h!==v?"nextSibling":"previousSibling",l=e.parentNode,f=y&&e.nodeName.toLowerCase(),d=!n&&!y;if(l){if(h){for(;c;){for(i=e;i=i[c];)if(y?i.nodeName.toLowerCase()===f:1===i.nodeType)return!1;s=c="only"===m&&!s&&"nextSibling"}return!0}if(s=[v?l.firstChild:l.lastChild],v&&d){for(u=(r=(o=l[Te]||(l[Te]={}))[m]||[])[0]===Re&&r[1],a=r[0]===Re&&r[2],i=u&&l.childNodes[u];i=++u&&i&&i[c]||(a=u=0)||s.pop();)if(1===i.nodeType&&++a&&i===e){o[m]=[Re,u,a];break}}else if(d&&(r=(e[Te]||(e[Te]={}))[m])&&r[0]===Re)a=r[1];else for(;(i=++u&&i&&i[c]||(a=u=0)||s.pop())&&((y?i.nodeName.toLowerCase()!==f:1!==i.nodeType)||!++a||(d&&((i[Te]||(i[Te]={}))[m]=[Re,a]),i!==e)););return(a-=g)===p||a%p==0&&0<=a/p}}},PSEUDO:function(e,i){var t,a=G.pseudos[e]||G.setFilters[e.toLowerCase()]||ct.error("unsupported pseudo: "+e);return a[Te]?a(i):1<a.length?(t=[e,e,"",i],G.setFilters.hasOwnProperty(e.toLowerCase())?ft(function(e,t){for(var n,r=a(e,i),o=r.length;o--;)e[n=He.call(e,r[o])]=!(t[n]=r[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:ft(function(e){var r=[],o=[],u=ee(e.replace(Xe,"$1"));return u[Te]?ft(function(e,t,n,r){for(var o,i=u(e,null,r,[]),a=e.length;a--;)(o=i[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return r[0]=e,u(r,null,n,o),!o.pop()}}),has:ft(function(t){return function(e){return 0<ct(t,e).length}}),contains:ft(function(t){return t=t.replace(ut,st),function(e){return-1<(e.textContent||e.innerText||J(e)).indexOf(t)}}),lang:ft(function(n){return Ze.test(n||"")||ct.error("unsupported lang: "+n),n=n.replace(ut,st).toLowerCase(),function(e){var t;do{if(t=se?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===ue},focus:function(e){return e===ae.activeElement&&(!ae.hasFocus||ae.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!G.pseudos.empty(e)},header:function(e){return nt.test(e.nodeName)},input:function(e){return tt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:gt(function(){return[0]}),last:gt(function(e,t){return[t-1]}),eq:gt(function(e,t,n){return[n<0?n+t:n]}),even:gt(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:gt(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:gt(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:gt(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=G.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})G.pseudos[X]=mt(X);for(X in{submit:!0,reset:!0})G.pseudos[X]=pt(X);function vt(){}function yt(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function bt(a,e,t){var u=e.dir,s=t&&"parentNode"===u,c=_e++;return e.first?function(e,t,n){for(;e=e[u];)if(1===e.nodeType||s)return a(e,t,n)}:function(e,t,n){var r,o,i=[Re,c];if(n){for(;e=e[u];)if((1===e.nodeType||s)&&a(e,t,n))return!0}else for(;e=e[u];)if(1===e.nodeType||s){if((r=(o=e[Te]||(e[Te]={}))[u])&&r[0]===Re&&r[1]===c)return i[2]=r[2];if((o[u]=i)[2]=a(e,t,n))return!0}}}function Ct(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function xt(e,t,n,r,o){for(var i,a=[],u=0,s=e.length,c=null!=t;u<s;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),c&&t.push(u)));return a}function wt(m,p,g,h,v,e){return h&&!h[Te]&&(h=wt(h)),v&&!v[Te]&&(v=wt(v,e)),ft(function(e,t,n,r){var o,i,a,u=[],s=[],c=t.length,l=e||function(e,t,n){for(var r=0,o=t.length;r<o;r++)ct(e,t[r],n);return n}(p||"*",n.nodeType?[n]:n,[]),f=!m||!e&&p?l:xt(l,u,m,n,r),d=g?v||(e?m:c||h)?[]:t:f;if(g&&g(f,d,n,r),h)for(o=xt(d,s),h(o,[],n,r),i=o.length;i--;)(a=o[i])&&(d[s[i]]=!(f[s[i]]=a));if(e){if(v||m){if(v){for(o=[],i=d.length;i--;)(a=d[i])&&o.push(f[i]=a);v(null,d=[],o,r)}for(i=d.length;i--;)(a=d[i])&&-1<(o=v?He.call(e,a):u[i])&&(e[o]=!(t[o]=a))}}else d=xt(d===t?d.splice(c,d.length):d),v?v(null,t,d,r):qe.apply(t,d)})}function Nt(e){for(var r,t,n,o=e.length,i=G.relative[e[0].type],a=i||G.relative[" "],u=i?1:0,s=bt(function(e){return e===r},a,!0),c=bt(function(e){return-1<He.call(r,e)},a,!0),l=[function(e,t,n){return!i&&(n||t!==ne)||((r=t).nodeType?s(e,t,n):c(e,t,n))}];u<o;u++)if(t=G.relative[e[u].type])l=[bt(Ct(l),t)];else{if((t=G.filter[e[u].type].apply(null,e[u].matches))[Te]){for(n=++u;n<o&&!G.relative[e[n].type];n++);return wt(1<u&&Ct(l),1<u&&yt(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(Xe,"$1"),t,u<n&&Nt(e.slice(u,n)),n<o&&Nt(e=e.slice(n)),n<o&&yt(e))}l.push(t)}return Ct(l)}vt.prototype=G.filters=G.pseudos,G.setFilters=new vt,Z=ct.tokenize=function(e,t){var n,r,o,i,a,u,s,c=De[e+" "];if(c)return t?0:c.slice(0);for(a=e,u=[],s=G.preFilter;a;){for(i in n&&!(r=Ye.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=Ge.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(Xe," ")}),a=a.slice(n.length)),G.filter)!(r=et[i].exec(a))||s[i]&&!(r=s[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?ct.error(e):De(e,u).slice(0)},ee=ct.compile=function(e,t){var n,h,v,y,b,r,o=[],i=[],a=Oe[e+" "];if(!a){for(t||(t=Z(e)),n=t.length;n--;)(a=Nt(t[n]))[Te]?o.push(a):i.push(a);(a=Oe(e,(h=i,y=0<(v=o).length,b=0<h.length,r=function(e,t,n,r,o){var i,a,u,s=0,c="0",l=e&&[],f=[],d=ne,m=e||b&&G.find.TAG("*",o),p=Re+=null==d?1:Math.random()||.1,g=m.length;for(o&&(ne=t!==ae&&t);c!==g&&null!=(i=m[c]);c++){if(b&&i){for(a=0;u=h[a++];)if(u(i,t,n)){r.push(i);break}o&&(Re=p)}y&&((i=!u&&i)&&s--,e&&l.push(i))}if(s+=c,y&&c!==s){for(a=0;u=v[a++];)u(l,f,t,n);if(e){if(0<s)for(;c--;)l[c]||f[c]||(f[c]=ze.call(r));f=xt(f)}qe.apply(r,f),o&&!e&&0<f.length&&1<s+v.length&&ct.uniqueSort(r)}return o&&(Re=p,ne=d),l},y?ft(r):r))).selector=e}return a},te=ct.select=function(e,t,n,r){var o,i,a,u,s,c="function"==typeof e&&e,l=!r&&Z(e=c.selector||e);if(n=n||[],1===l.length){if(2<(i=l[0]=l[0].slice(0)).length&&"ID"===(a=i[0]).type&&Y.getById&&9===t.nodeType&&se&&G.relative[i[1].type]){if(!(t=(G.find.ID(a.matches[0].replace(ut,st),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=et.needsContext.test(e)?0:i.length;o--&&(a=i[o],!G.relative[u=a.type]);)if((s=G.find[u])&&(r=s(a.matches[0].replace(ut,st),it.test(i[0].type)&&ht(t.parentNode)||t))){if(i.splice(o,1),!(e=r.length&&yt(i)))return qe.apply(n,r),n;break}}return(c||ee(e,l))(r,t,!se,n,it.test(e)&&ht(t.parentNode)||t),n},Y.sortStable=Te.split("").sort(Pe).join("")===Te,Y.detectDuplicates=!!oe,ie(),Y.sortDetached=!0;var Et=Array.isArray,St=function(e,t,n){var r,o;if(!e)return 0;if(n=n||e,e.length!==undefined){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return 0}else for(r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))return 0;return 1},kt=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},Tt={isArray:Et,toArray:function(e){var t,n,r=e;if(!Et(e))for(r=[],t=0,n=e.length;t<n;t++)r[t]=e[t];return r},each:St,map:function(n,r){var o=[];return St(n,function(e,t){o.push(r(e,t,n))}),o},filter:function(n,r){var o=[];return St(n,function(e,t){r&&!r(e,t,n)||o.push(e)}),o},indexOf:function(e,t){var n,r;if(e)for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},reduce:function(e,t,n,r){var o=0;for(arguments.length<3&&(n=e[0]);o<e.length;o++)n=t.call(r,n,e[o],o);return n},findIndex:kt,find:function(e,t,n){var r=kt(e,t,n);return-1!==r?e[r]:undefined},last:function(e){return e[e.length-1]}},At=/^\s*|\s*$/g,Rt=function(e){return null===e||e===undefined?"":(""+e).replace(At,"")},_t=function(e,t){return t?!("array"!==t||!Tt.isArray(e))||typeof e===t:e!==undefined},Bt=function(e,n,r,o){o=o||this,e&&(r&&(e=e[r]),Tt.each(e,function(e,t){if(!1===n.call(o,e,t,r))return!1;Bt(e,n,r,o)}))},Dt={trim:Rt,isArray:Tt.isArray,is:_t,toArray:Tt.toArray,makeMap:function(e,t,n){var r;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},r=e.length;r--;)n[e[r]]={};return n},each:Tt.each,map:Tt.map,grep:Tt.filter,inArray:Tt.indexOf,hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},extend:function(e,t){for(var n,r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var u,s=arguments;for(n=1,r=s.length;n<r;n++)for(o in t=s[n])t.hasOwnProperty(o)&&(u=t[o])!==undefined&&(e[o]=u);return e},create:function(e,t,n){var r,o,i,a,u,s=this,c=0;if(e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e),i=e[3].match(/(^|\.)(\w+)$/i)[2],!(o=s.createNS(e[3].replace(/\.\w+$/,""),n))[i]){if("static"===e[2])return o[i]=t,void(this.onCreate&&this.onCreate(e[2],e[3],o[i]));t[i]||(t[i]=function(){},c=1),o[i]=t[i],s.extend(o[i].prototype,t),e[5]&&(r=s.resolve(e[5]).prototype,a=e[5].match(/\.(\w+)$/i)[1],u=o[i],o[i]=c?function(){return r[a].apply(this,arguments)}:function(){return this.parent=r[a],u.apply(this,arguments)},o[i].prototype[i]=o[i],s.each(r,function(e,t){o[i].prototype[t]=r[t]}),s.each(t,function(e,t){r[t]?o[i].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==i&&(o[i].prototype[t]=e)})),s.each(t["static"],function(e,t){o[i][t]=e})}},walk:Bt,createNS:function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0;n<e.length;n++)t[r=e[n]]||(t[r]={}),t=t[r];return t},resolve:function(e,t){var n,r;for(t=t||window,n=0,r=(e=e.split(".")).length;n<r&&(t=t[e[n]]);n++);return t},explode:function(e,t){return!e||_t(e,"array")?e:Tt.map(e.split(t||","),Rt)},_addCacheSuffix:function(e){var t=de.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Ot=document,Pt=Array.prototype.push,Lt=Array.prototype.slice,It=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,Mt=ke.Event,Ft=Dt.makeMap("children,contents,next,prev"),zt=function(e){return void 0!==e},Ut=function(e){return"string"==typeof e},qt=function(e,t){var n,r,o;for(o=(t=t||Ot).createElement("div"),n=t.createDocumentFragment(),o.innerHTML=e;r=o.firstChild;)n.appendChild(r);return n},Vt=function(e,t,n,r){var o;if(Ut(t))t=qt(t,nn(e[0]));else if(t.length&&!t.nodeType){if(t=Jt.makeArray(t),r)for(o=t.length-1;0<=o;o--)Vt(e,t[o],n,r);else for(o=0;o<t.length;o++)Vt(e,t[o],n,r);return e}if(t.nodeType)for(o=e.length;o--;)n.call(e[o],t);return e},Ht=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},jt=function(e,t,n){var r,o;return t=Jt(t)[0],e.each(function(){var e=this;n&&r===e.parentNode||(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e)),o.appendChild(e)}),e},$t=Dt.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),Wt=Dt.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),Kt={"for":"htmlFor","class":"className",readonly:"readOnly"},Xt={"float":"cssFloat"},Yt={},Gt={},Jt=function(e,t){return new Jt.fn.init(e,t)},Qt=/^\s*|\s*$/g,Zt=function(e){return null===e||e===undefined?"":(""+e).replace(Qt,"")},en=function(e,t){var n,r,o,i;if(e)if((n=e.length)===undefined){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n&&(i=e[o],!1!==t.call(i,o,i));o++);return e},tn=function(e,n){var r=[];return en(e,function(e,t){n(t,e)&&r.push(t)}),r},nn=function(e){return e?9===e.nodeType?e:e.ownerDocument:Ot};Jt.fn=Jt.prototype={constructor:Jt,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return Jt(e).attr(t);o.context=t=document}if(Ut(e)){if(!(n="<"===(o.selector=e).charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:It.exec(e)))return Jt(t).find(e);if(n[1])for(r=qt(e,nn(t)).firstChild;r;)Pt.call(o,r),r=r.nextSibling;else{if(!(r=nn(t).getElementById(n[2])))return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return Dt.toArray(this)},add:function(e,t){var n,r,o=this;if(Ut(e))return o.add(Jt(e));if(!1!==t)for(n=Jt.unique(o.toArray().concat(Jt.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else Pt.apply(o,Jt.makeArray(e));return o},attr:function(t,n){var e,r=this;if("object"==typeof t)en(t,function(e,t){r.attr(e,t)});else{if(!zt(n)){if(r[0]&&1===r[0].nodeType){if((e=Yt[t])&&e.get)return e.get(r[0],t);if(Wt[t])return r.prop(t)?t:undefined;null===(n=r[0].getAttribute(t,2))&&(n=undefined)}return n}this.each(function(){var e;if(1===this.nodeType){if((e=Yt[t])&&e.set)return void e.set(this,n);null===n?this.removeAttribute(t,2):this.setAttribute(t,n,2)}})}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if("object"==typeof(e=Kt[e]||e))en(e,function(e,t){n.prop(e,t)});else{if(!zt(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1===this.nodeType&&(this[e]=t)})}return n},css:function(n,r){var e,o,i=this,t=function(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})},a=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})};if("object"==typeof n)en(n,function(e,t){i.css(e,t)});else if(zt(r))n=t(n),"number"!=typeof r||$t[n]||(r=r.toString()+"px"),i.each(function(){var e=this.style;if((o=Gt[n])&&o.set)o.set(this,r);else{try{this.style[Xt[n]||n]=r}catch(t){}null!==r&&""!==r||(e.removeProperty?e.removeProperty(a(n)):e.removeAttribute(n))}});else{if(e=i[0],(o=Gt[n])&&o.get)return o.get(e);if(!e.ownerDocument.defaultView)return e.currentStyle?e.currentStyle[t(n)]:"";try{return e.ownerDocument.defaultView.getComputedStyle(e,null).getPropertyValue(a(n))}catch(u){return undefined}}return i},remove:function(){for(var e,t=this.length;t--;)e=this[t],Mt.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){for(var e,t=this.length;t--;)for(e=this[t];e.firstChild;)e.removeChild(e.firstChild);return this},html:function(e){var t,n=this;if(zt(e)){t=n.length;try{for(;t--;)n[t].innerHTML=e}catch(r){Jt(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(zt(e)){for(t=n.length;t--;)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return Vt(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)})},prepend:function(){return Vt(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)},!0)},before:function(){return this[0]&&this[0].parentNode?Vt(this,arguments,function(e){this.parentNode.insertBefore(e,this)}):this},after:function(){return this[0]&&this[0].parentNode?Vt(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):this},appendTo:function(e){return Jt(e).append(this),this},prependTo:function(e){return Jt(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return jt(this,e)},wrapAll:function(e){return jt(this,e,!0)},wrapInner:function(e){return this.each(function(){Jt(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){Jt(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),Jt(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(o,i){var e=this;return"string"!=typeof o||(-1!==o.indexOf(" ")?en(o.split(" "),function(){e.toggleClass(this,i)}):e.each(function(e,t){var n,r;(r=Ht(t,o))!==i&&(n=t.className,r?t.className=Zt((" "+n+" ").replace(" "+o+" "," ")):t.className+=n?" "+o:o)})),e},hasClass:function(e){return Ht(this[0],e)},each:function(e){return en(this,e)},on:function(e,t){return this.each(function(){Mt.bind(this,e,t)})},off:function(e,t){return this.each(function(){Mt.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?Mt.fire(this,e.type,e):Mt.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new Jt(Lt.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)Jt.find(e,this[t],r);return Jt(r)},filter:function(n){return Jt("function"==typeof n?tn(this.toArray(),function(e,t){return n(t,e)}):Jt.filter(n,this.toArray()))},closest:function(n){var r=[];return n instanceof Jt&&(n=n[0]),this.each(function(e,t){for(;t;){if("string"==typeof n&&Jt(t).is(n)){r.push(t);break}if(t===n){r.push(t);break}t=t.parentNode}}),Jt(r)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):((t=this[0])&&(r=(n=t.ownerDocument).documentElement,t.getBoundingClientRect&&(i=(o=t.getBoundingClientRect()).left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:Pt,sort:[].sort,splice:[].splice},Dt.extend(Jt,{extend:Dt.extend,makeArray:function(e){return(t=e)&&t===t.window||e.nodeType?[e]:Dt.toArray(e);var t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Dt.isArray,each:en,trim:Zt,grep:tn,find:ct,expr:ct.selectors,unique:ct.uniqueSort,text:ct.getText,contains:ct.contains,filter:function(e,t,n){var r=t.length;for(n&&(e=":not("+e+")");r--;)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?Jt.find.matchesSelector(t[0],e)?[t[0]]:[]:Jt.find.matches(e,t)}});var rn=function(e,t,n){var r=[],o=e[t];for("string"!=typeof n&&n instanceof Jt&&(n=n[0]);o&&9!==o.nodeType;){if(n!==undefined){if(o===n)break;if("string"==typeof n&&Jt(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},on=function(e,t,n,r){var o=[];for(r instanceof Jt&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(r!==undefined){if(e===r)break;if("string"==typeof r&&Jt(e).is(r))break}o.push(e)}return o},an=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};en({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return rn(e,"parentNode")},next:function(e){return an(e,"nextSibling",1)},prev:function(e){return an(e,"previousSibling",1)},children:function(e){return on(e.firstChild,"nextSibling",1)},contents:function(e){return Dt.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,r){Jt.fn[e]=function(t){var n=[];return this.each(function(){var e=r.call(n,this,t,n);e&&(Jt.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(Ft[e]||(n=Jt.unique(n)),0===e.indexOf("parents")&&(n=n.reverse())),n=Jt(n),t?n.filter(t):n}}),en({parentsUntil:function(e,t){return rn(e,"parentNode",t)},nextUntil:function(e,t){return on(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return on(e,"previousSibling",1,t).slice(1)}},function(r,o){Jt.fn[r]=function(t,e){var n=[];return this.each(function(){var e=o.call(n,this,t,n);e&&(Jt.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(n=Jt.unique(n),0!==r.indexOf("parents")&&"prevUntil"!==r||(n=n.reverse())),n=Jt(n),e?n.filter(e):n}}),Jt.fn.is=function(e){return!!e&&0<this.filter(e).length},Jt.fn.init.prototype=Jt.fn,Jt.overrideDefaults=function(n){var r,o=function(e,t){return r=r||n(),0===arguments.length&&(e=r.element),t||(t=r.context),new o.fn.init(e,t)};return Jt.extend(o,this),o};var un=function(n,r,e){en(e,function(e,t){n[e]=n[e]||{},n[e][r]=t})};de.ie&&de.ie<8&&(un(Yt,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?undefined:t},size:function(e){var t=e.size;return 20===t?undefined:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?undefined:t}}),un(Yt,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),de.ie&&de.ie<9&&(Xt["float"]="styleFloat",un(Gt,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),Jt.attrHooks=Yt,Jt.cssHooks=Gt;var sn,cn=function(e){var t,n=!1;return function(){return n||(n=!0,t=e.apply(null,arguments)),t}},ln=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return dn(r(1),r(2))},fn=function(){return dn(0,0)},dn=function(e,t){return{major:e,minor:t}},mn={nu:dn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?fn():ln(e,n)},unknown:fn},pn="Firefox",gn=function(e,t){return function(){return t===e}},hn=function(e){var t=e.current;return{current:t,version:e.version,isEdge:gn("Edge",t),isChrome:gn("Chrome",t),isIE:gn("IE",t),isOpera:gn("Opera",t),isFirefox:gn(pn,t),isSafari:gn("Safari",t)}},vn={unknown:function(){return hn({current:undefined,version:mn.unknown()})},nu:hn,edge:V.constant("Edge"),chrome:V.constant("Chrome"),ie:V.constant("IE"),opera:V.constant("Opera"),firefox:V.constant(pn),safari:V.constant("Safari")},yn="Windows",bn="Android",Cn="Solaris",xn="FreeBSD",wn=function(e,t){return function(){return t===e}},Nn=function(e){var t=e.current;return{current:t,version:e.version,isWindows:wn(yn,t),isiOS:wn("iOS",t),isAndroid:wn(bn,t),isOSX:wn("OSX",t),isLinux:wn("Linux",t),isSolaris:wn(Cn,t),isFreeBSD:wn(xn,t)}},En={unknown:function(){return Nn({current:undefined,version:mn.unknown()})},nu:Nn,windows:V.constant(yn),ios:V.constant("iOS"),android:V.constant(bn),linux:V.constant("Linux"),osx:V.constant("OSX"),solaris:V.constant(Cn),freebsd:V.constant(xn)},Sn=function(e,t){var n=String(t).toLowerCase();return H.find(e,function(e){return e.search(n)})},kn=function(e,n){return Sn(e,n).map(function(e){var t=mn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Tn=function(e,n){return Sn(e,n).map(function(e){var t=mn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},An=function(e,t){return-1!==e.indexOf(t)},Rn=function(e){return e.replace(/^\s+|\s+$/g,"")},_n=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Bn=function(t){return function(e){return An(e,t)}},Dn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return An(e,"edge/")&&An(e,"chrome")&&An(e,"safari")&&An(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,_n],search:function(e){return An(e,"chrome")&&!An(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return An(e,"msie")||An(e,"trident")}},{name:"Opera",versionRegexes:[_n,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Bn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Bn("firefox")},{name:"Safari",versionRegexes:[_n,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(An(e,"safari")||An(e,"mobile/"))&&An(e,"applewebkit")}}],On=[{name:"Windows",search:Bn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return An(e,"iphone")||An(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Bn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Bn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Bn("linux"),versionRegexes:[]},{name:"Solaris",search:Bn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Bn("freebsd"),versionRegexes:[]}],Pn={browsers:V.constant(Dn),oses:V.constant(On)},Ln=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=Pn.browsers(),m=Pn.oses(),p=kn(d,e).fold(vn.unknown,vn.nu),g=Tn(m,e).fold(En.unknown,En.nu);return{browser:p,os:g,deviceType:(n=p,r=e,o=(t=g).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,s=o||a||u&&!0===/mobile/i.test(r),c=t.isiOS()||t.isAndroid(),l=c&&!s,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:V.constant(o),isiPhone:V.constant(i),isTablet:V.constant(s),isPhone:V.constant(l),isTouch:V.constant(c),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:V.constant(f)})}},In={detect:cn(function(){var e=navigator.userAgent;return Ln(e)})},Mn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:V.constant(e)}},Fn={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return Mn(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return Mn(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return Mn(n)},fromDom:Mn,fromPoint:function(e,t,n){return A.from(e.dom().elementFromPoint(t,n)).map(Mn)}},zn=8,Un=9,qn=1,Vn=3,Hn=function(e){return e.dom().nodeName.toLowerCase()},jn=function(e){return e.dom().nodeType},$n=function(t){return function(e){return jn(e)===t}},Wn=$n(qn),Kn=$n(Vn),Xn=$n(Un),Yn={name:Hn,type:jn,value:function(e){return e.dom().nodeValue},isElement:Wn,isText:Kn,isDocument:Xn,isComment:function(e){return jn(e)===zn||"#comment"===Hn(e)}},Gn=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},Jn={isString:Gn("string"),isObject:Gn("object"),isArray:Gn("array"),isNull:Gn("null"),isBoolean:Gn("boolean"),isUndefined:Gn("undefined"),isFunction:Gn("function"),isNumber:Gn("number")},Qn=(sn=Object.keys)===undefined?function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}:sn,Zn=function(e,t){for(var n=Qn(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},er=function(r,o){var i={};return Zn(r,function(e,t){var n=o(e,t,r);i[n.k]=n.v}),i},tr=function(e,n){var r=[];return Zn(e,function(e,t){r.push(n(e,t))}),r},nr=function(e){return tr(e,function(e){return e})},rr={bifilter:function(e,n){var r={},o={};return Zn(e,function(e,t){(n(e,t)?r:o)[t]=e}),{t:r,f:o}},each:Zn,map:function(e,r){return er(e,function(e,t,n){return{k:t,v:r(e,t,n)}})},mapToArray:tr,tupleMap:er,find:function(e,t){for(var n=Qn(e),r=0,o=n.length;r<o;r++){var i=n[r],a=e[i];if(t(a,i,e))return A.some(a)}return A.none()},keys:Qn,values:nr,size:function(e){return nr(e).length}},or=function(e,t,n){if(!(Jn.isString(n)||Jn.isBoolean(n)||Jn.isNumber(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},ir=function(e,t,n){or(e.dom(),t,n)},ar=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},ur=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},sr={clone:function(e){return H.foldl(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{})},set:ir,setAll:function(e,t){var n=e.dom();rr.each(t,function(e,t){or(n,t,e)})},get:ar,has:ur,remove:function(e,t){e.dom().removeAttribute(t)},hasNone:function(e){var t=e.dom().attributes;return t===undefined||null===t||0===t.length},transfer:function(o,i,e){Yn.isElement(o)&&Yn.isElement(i)&&H.each(e,function(e){var t,n,r;n=i,ur(t=o,r=e)&&!ur(n,r)&&ir(n,r,ar(t,r))})}},cr=cn(function(){return lr(Fn.fromDom(document))}),lr=function(e){var t=e.dom().body;if(null===t||t===undefined)throw"Body is not available yet";return Fn.fromDom(t)},fr={body:cr,getBody:lr,inBody:function(e){var t=Yn.isText(e)?e.dom().parentNode:e.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}},dr=function(e){return e.style!==undefined},mr=function(e,t,n){if(!Jn.isString(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);dr(e)&&e.style.setProperty(t,n)},pr=function(e,t){return dr(e)?e.style.getPropertyValue(t):""},gr=function(e,t){var n=e.dom();rr.each(t,function(e,t){mr(n,t,e)})},hr=function(e,t){var n=e.dom(),r=window.getComputedStyle(n).getPropertyValue(t),o=""!==r||fr.inBody(e)?r:pr(n,t);return null===o?undefined:o},vr=function(e){return e.slice(0).sort()},yr={sort:vr,reqMessage:function(e,t){throw new Error("All required keys ("+vr(e).join(", ")+") were not specified. Specified keys were: "+vr(t).join(", ")+".")},unsuppMessage:function(e){throw new Error("Unsupported keys for object: "+vr(e).join(", "))},validateStrArr:function(t,e){if(!Jn.isArray(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");H.each(e,function(e){if(!Jn.isString(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},invalidTypeMessage:function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+vr(e).join(", ")+") were not.")},checkDupes:function(e){var n=vr(e);H.find(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")})}},br={immutable:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return H.each(t,function(e,t){r[e]=V.constant(n[t])}),r}},immutableBag:function(o,i){var a=o.concat(i);if(0===a.length)throw new Error("You must specify at least one required or optional field.");return yr.validateStrArr("required",o),yr.validateStrArr("optional",i),yr.checkDupes(a),function(t){var n=rr.keys(t);H.forall(o,function(e){return H.contains(n,e)})||yr.reqMessage(o,n);var e=H.filter(n,function(e){return!H.contains(a,e)});0<e.length&&yr.unsuppMessage(e);var r={};return H.each(o,function(e){r[e]=V.constant(t[e])}),H.each(i,function(e){r[e]=V.constant(Object.prototype.hasOwnProperty.call(t,e)?A.some(t[e]):A.none())}),r}}},Cr=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},xr=function(){return z.getOrDie("Node")},wr=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Nr=function(e,t){return wr(e,t,xr().DOCUMENT_POSITION_CONTAINED_BY)},Er=qn,Sr=Un,kr=function(e){return e.nodeType!==Er&&e.nodeType!==Sr||0===e.childElementCount},Tr={all:function(e,t){var n=t===undefined?document:t.dom();return kr(n)?[]:H.map(n.querySelectorAll(e),Fn.fromDom)},is:function(e,t){var n=e.dom();if(n.nodeType!==Er)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},one:function(e,t){var n=t===undefined?document:t.dom();return kr(n)?A.none():A.from(n.querySelector(e)).map(Fn.fromDom)}},Ar=function(e,t){return e.dom()===t.dom()},Rr=In.detect().browser.isIE()?function(e,t){return Nr(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},_r={eq:Ar,isEqualNode:function(e,t){return e.dom().isEqualNode(t.dom())},member:function(e,t){return H.exists(t,V.curry(Ar,e))},contains:Rr,is:Tr.is},Br=function(e){return Fn.fromDom(e.dom().ownerDocument)},Dr=function(e){var t=e.dom();return A.from(t.parentNode).map(Fn.fromDom)},Or=function(e){var t=e.dom();return A.from(t.previousSibling).map(Fn.fromDom)},Pr=function(e){var t=e.dom();return A.from(t.nextSibling).map(Fn.fromDom)},Lr=function(e){var t=e.dom();return H.map(t.childNodes,Fn.fromDom)},Ir=function(e,t){var n=e.dom().childNodes;return A.from(n[t]).map(Fn.fromDom)},Mr=br.immutable("element","offset"),Fr={owner:Br,defaultView:function(e){var t=e.dom().ownerDocument.defaultView;return Fn.fromDom(t)},documentElement:function(e){var t=Br(e);return Fn.fromDom(t.dom().documentElement)},parent:Dr,findIndex:function(n){return Dr(n).bind(function(e){var t=Lr(e);return H.findIndex(t,function(e){return _r.eq(n,e)})})},parents:function(e,t){for(var n=Jn.isFunction(t)?t:V.constant(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,a=Fn.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o},siblings:function(t){return Dr(t).map(Lr).map(function(e){return H.filter(e,function(e){return!_r.eq(t,e)})}).getOr([])},prevSibling:Or,offsetParent:function(e){var t=e.dom();return A.from(t.offsetParent).map(Fn.fromDom)},prevSiblings:function(e){return H.reverse(Cr(e,Or))},nextSibling:Pr,nextSiblings:function(e){return Cr(e,Pr)},children:Lr,child:Ir,firstChild:function(e){return Ir(e,0)},lastChild:function(e){return Ir(e,e.dom().childNodes.length-1)},childNodesCount:function(e){return e.dom().childNodes.length},hasChildNodes:function(e){return e.dom().hasChildNodes()},leaf:function(e,t){var n=Lr(e);return 0<n.length&&t<n.length?Mr(n[t],0):Mr(e,t)}},zr=In.detect().browser,Ur=function(e){return H.find(e,Yn.isElement)},qr={getPos:function(e,t,n){var r,o,i,a=0,u=0,s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===hr(Fn.fromDom(e),"position"))return{x:a=(o=t.getBoundingClientRect()).left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,y:u=o.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop};for(r=t;r&&r!==n&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!==n&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;u+=(i=Fn.fromDom(t),zr.isFirefox()&&"table"===Yn.name(i)?Ur(Fr.children(i)).filter(function(e){return"caption"===Yn.name(e)}).bind(function(o){return Ur(Fr.nextSiblings(o)).map(function(e){var t=e.dom().offsetTop,n=o.dom().offsetTop,r=o.dom().offsetHeight;return t<=n?-r:0})}).getOr(0):0)}return{x:a,y:u}}},Vr=function(e){var n=A.none(),t=[],r=function(e){o()?a(e):t.push(e)},o=function(){return n.isSome()},i=function(e){H.each(e,a)},a=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){n=A.some(e),i(t),t=[]}),{get:r,map:function(n){return Vr(function(t){r(function(e){t(n(e))})})},isReady:o}},Hr={nu:Vr,pure:function(t){return Vr(function(e){e(t)})}},jr=function(n){return function(){var e=Array.prototype.slice.call(arguments),t=this;setTimeout(function(){n.apply(t,e)},0)}},$r=function(t){var e=function(e){t(jr(e))};return{map:function(r){return $r(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return $r(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return $r(function(t){e(function(e){n.get(t)})})},toLazy:function(){return Hr.nu(e)},get:e}},Wr={nu:$r,pure:function(t){return $r(function(e){e(t)})}},Kr=function(a,e){return e(function(r){var o=[],i=0;0===a.length?r([]):H.each(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})})},Xr=function(e){return Kr(e,Wr.nu)},Yr={par:Xr,mapM:function(e,t){var n=H.map(e,t);return Xr(n)},compose:function(t,n){return function(e){return n(e).bind(t)}}},Gr=function(n){return{is:function(e){return n===e},isValue:V.always,isError:V.never,getOr:V.constant(n),getOrThunk:V.constant(n),getOrDie:V.constant(n),or:function(e){return Gr(n)},orThunk:function(e){return Gr(n)},fold:function(e,t){return t(n)},map:function(e){return Gr(e(n))},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return A.some(n)}}},Jr=function(n){return{is:V.never,isValue:V.never,isError:V.always,getOr:V.identity,getOrThunk:function(e){return e()},getOrDie:function(){return V.die(String(n))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return Jr(n)},each:V.noop,bind:function(e){return Jr(n)},exists:V.never,forall:V.always,toOption:A.none}},Qr={value:Gr,error:Jr};function Zr(e,u){var t=e,n=function(e,t,n,r){var o,i;if(e){if(!r&&e[t])return e[t];if(e!==u){if(o=e[n])return o;for(i=e.parentNode;i&&i!==u;i=i.parentNode)if(o=i[n])return o}}};this.current=function(){return t},this.next=function(e){return t=n(t,"firstChild","nextSibling",e)},this.prev=function(e){return t=n(t,"lastChild","previousSibling",e)},this.prev2=function(e){return t=function(e,t,n,r){var o,i,a;if(e){if(o=e[n],u&&o===u)return;if(o){if(!r)for(a=o[t];a;a=a[t])if(!a[t])return a;return o}if((i=e.parentNode)&&i!==u)return i}}(t,"lastChild","previousSibling",e)}}var eo,to,no,ro=function(t){var n;return function(e){return(n=n||H.mapToObject(t,V.constant(!0))).hasOwnProperty(Yn.name(e))}},oo=ro(["h1","h2","h3","h4","h5","h6"]),io=ro(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),ao=function(e){return Yn.isElement(e)&&!io(e)},uo=function(e){return Yn.isElement(e)&&"br"===Yn.name(e)},so=ro(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),co=ro(["ul","ol","dl"]),lo=ro(["li","dd","dt"]),fo=ro(["area","base","basefont","br","col","frame","hr","img","input","isindex","link","meta","param","embed","source","wbr","track"]),mo=ro(["thead","tbody","tfoot"]),po=ro(["td","th"]),go=function(t){return function(e){return!!e&&e.nodeType===t}},ho=go(1),vo=function(e){var r=e.toLowerCase().split(" ");return function(e){var t,n;if(e&&e.nodeType)for(n=e.nodeName.toLowerCase(),t=0;t<r.length;t++)if(n===r[t])return!0;return!1}},yo=function(t){return function(e){if(ho(e)){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},bo=go(3),Co=go(8),xo=go(9),wo=vo("br"),No=yo("true"),Eo=yo("false"),So={isText:bo,isElement:ho,isComment:Co,isDocument:xo,isBr:wo,isContentEditableTrue:No,isContentEditableFalse:Eo,matchNodeNames:vo,hasPropValue:function(t,n){return function(e){return ho(e)&&e[t]===n}},hasAttribute:function(t,e){return function(e){return ho(e)&&e.hasAttribute(t)}},hasAttributeValue:function(t,n){return function(e){return ho(e)&&e.getAttribute(t)===n}},matchStyleValues:function(r,e){var o=e.toLowerCase().split(" ");return function(e){var t;if(ho(e))for(t=0;t<o.length;t++){var n=e.ownerDocument.defaultView.getComputedStyle(e,null);if((n?n.getPropertyValue(r):null)===o[t])return!0}return!1}},isBogus:function(e){return ho(e)&&e.hasAttribute("data-mce-bogus")},isBogusAll:function(e){return ho(e)&&"all"===e.getAttribute("data-mce-bogus")},isTable:function(e){return ho(e)&&"TABLE"===e.tagName}},ko=function(e){return e&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},To=function(e,t){var n,r=t.childNodes;if(!So.isElement(t)||!ko(t)){for(n=r.length-1;0<=n;n--)To(e,r[n]);if(!1===So.isDocument(t)){if(So.isText(t)&&0<t.nodeValue.length){var o=Dt.trim(t.nodeValue).length;if(e.isBlock(t.parentNode)||0<o)return;if(0===o&&(a=(i=t).previousSibling&&"SPAN"===i.previousSibling.nodeName,u=i.nextSibling&&"SPAN"===i.nextSibling.nodeName,a&&u))return}else if(So.isElement(t)&&(1===(r=t.childNodes).length&&ko(r[0])&&t.parentNode.insertBefore(r[0],t),r.length||fo(Fn.fromDom(t))))return;e.remove(t)}var i,a,u;return t}},Ao={trimNode:To},Ro=Dt.makeMap,_o=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Bo=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Do=/[<>&\"\']/g,Oo=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Po={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"};to={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},no={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"};var Lo=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),to[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}};eo=Lo("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32);var Io=function(e,t){return e.replace(t?_o:Bo,function(e){return to[e]||e})},Mo=function(e,t){return e.replace(t?_o:Bo,function(e){return 1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":to[e]||"&#"+e.charCodeAt(0)+";"})},Fo=function(e,t,n){return n=n||eo,e.replace(t?_o:Bo,function(e){return to[e]||n[e]||e})},zo={encodeRaw:Io,encodeAllRaw:function(e){return(""+e).replace(Do,function(e){return to[e]||e})},encodeNumeric:Mo,encodeNamed:Fo,getEncodeFunc:function(e,t){var n=Lo(t)||eo,r=Ro(e.replace(/\+/g,","));return r.named&&r.numeric?function(e,t){return e.replace(t?_o:Bo,function(e){return to[e]!==undefined?to[e]:n[e]!==undefined?n[e]:1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"})}:r.named?t?function(e,t){return Fo(e,t,n)}:Fo:r.numeric?Mo:Io},decode:function(e){return e.replace(Oo,function(e,t){return t?65535<(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Po[t]||String.fromCharCode(t):no[e]||eo[e]||(n=e,(r=Fn.fromTag("div").dom()).innerHTML=n,r.textContent||r.innerText||n);var n,r})}},Uo={},qo={},Vo=Dt.makeMap,Ho=Dt.each,jo=Dt.extend,$o=Dt.explode,Wo=Dt.inArray,Ko=function(e,t){return(e=Dt.trim(e))?e.split(t||" "):[]},Xo=function(e){var u,t,n,r,o,i,s={},a=function(e,t,n){var r,o,i,a=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};for(t=t||"","string"==typeof(n=n||[])&&(n=Ko(n)),r=(e=Ko(e)).length;r--;)i={attributes:a(o=Ko([u,t].join(" "))),attributesOrder:o,children:a(n,qo)},s[e[r]]=i},c=function(e,t){var n,r,o,i;for(n=(e=Ko(e)).length,t=Ko(t);n--;)for(r=s[e[n]],o=0,i=t.length;o<i;o++)r.attributes[t[o]]={},r.attributesOrder.push(t[o])};return Uo[e]?Uo[e]:(u="id accesskey class dir lang style tabindex title role",t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(u+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",t+=" article aside details dialog figure header footer hgroup section nav",n+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(u+=" xml:lang",n=[n,i="acronym applet basefont big font strike tt"].join(" "),Ho(Ko(i),function(e){a(e,"",n)}),t=[t,o="center dir isindex noframes"].join(" "),r=[t,n].join(" "),Ho(Ko(o),function(e){a(e,"",r)})),r=r||[t,n].join(" "),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",r),a("address dt dd div caption","",r),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",r),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",r),a("dl","","dt dd"),a("a","href target rel media hreflang type",n),a("q","cite",n),a("ins del","cite datetime",r),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",r),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[r,"param"].join(" ")),a("param","name value"),a("map","name",[r,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",r),a("th","colspan rowspan headers scope abbr",r),a("form","accept-charset action autocomplete enctype method name novalidate target",r),a("fieldset","disabled form name",[r,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?r:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[r,"li"].join(" ")),a("noscript","",r),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",r),a("mark rt rp summary bdi","",n),a("canvas","width height",r),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[r,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[r,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside header footer","",r),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[r,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",r),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[r,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(c("script","language xml:space"),c("style","xml:space"),c("object","declare classid code codebase codetype archive standby align border hspace vspace"),c("embed","align name hspace vspace"),c("param","valuetype type"),c("a","charset name rev shape coords"),c("br","clear"),c("applet","codebase archive code object alt name width height align hspace vspace"),c("img","name longdesc align border hspace vspace"),c("iframe","longdesc frameborder marginwidth marginheight scrolling align"),c("font basefont","size color face"),c("input","usemap align"),c("select","onchange"),c("textarea"),c("h1 h2 h3 h4 h5 h6 div p legend caption","align"),c("ul","type compact"),c("li","type"),c("ol dl menu dir","compact"),c("pre","width xml:space"),c("hr","align noshade size width"),c("isindex","prompt"),c("table","summary width frame rules cellspacing cellpadding align bgcolor"),c("col","width align char charoff valign"),c("colgroup","width align char charoff valign"),c("thead","align char charoff valign"),c("tr","align char charoff valign bgcolor"),c("th","axis align char charoff valign nowrap bgcolor width height"),c("form","accept"),c("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),c("tfoot","align char charoff valign"),c("tbody","align char charoff valign"),c("area","nohref"),c("body","background bgcolor text link vlink alink")),"html4"!==e&&(c("input button select textarea","autofocus"),c("input textarea","placeholder"),c("a","download"),c("link script img","crossorigin"),c("iframe","sandbox seamless allowfullscreen")),Ho(Ko("a form meter progress dfn"),function(e){s[e]&&delete s[e].children[e]}),delete s.caption.children.table,delete s.script,Uo[e]=s)},Yo=function(e,n){var r;return e&&(r={},"string"==typeof e&&(e={"*":e}),Ho(e,function(e,t){r[t]=r[t.toUpperCase()]="map"===n?Vo(e,/[, ]/):$o(e,/[, ]/)})),r};function Go(i){var e,t,n,r,o,a,u,s,c,l,f,d,m,N={},p={},E=[],g={},h={},v=function(e,t,n){var r=i[e];return r?r=Vo(r,/[, ]/,Vo(r.toUpperCase(),/[, ]/)):(r=Uo[e])||(r=Vo(t," ",Vo(t.toUpperCase()," ")),r=jo(r,n),Uo[e]=r),r};n=Xo((i=i||{}).schema),!1===i.verify_html&&(i.valid_elements="*[*]"),e=Yo(i.valid_styles),t=Yo(i.invalid_styles,"map"),s=Yo(i.valid_classes,"map"),r=v("whitespace_elements","pre script noscript style textarea video audio iframe object code"),o=v("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),a=v("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),u=v("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),l=v("non_empty_elements","td th iframe video audio object script pre code",a),f=v("move_caret_before_on_enter_elements","table",l),d=v("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside nav figure"),c=v("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption",d),m=v("text_inline_elements","span strong b em i font strike u var cite dfn code mark q sup sub samp"),Ho((i.special||"script noscript noframes noembed title style textarea xmp").split(" "),function(e){h[e]=new RegExp("</"+e+"[^>]*>","gi")});var S=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},y=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v,y,b,C=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)\])?$/,x=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,w=/[*?+]/;if(e)for(e=Ko(e,","),N["@"]&&(h=N["@"].attributes,v=N["@"].attributesOrder),t=0,n=e.length;t<n;t++)if(i=C.exec(e[t])){if(p=i[1],c=i[2],g=i[3],s=i[5],a={attributes:d={},attributesOrder:m=[]},"#"===p&&(a.paddEmpty=!0),"-"===p&&(a.removeEmpty=!0),"!"===i[4]&&(a.removeEmptyAttrs=!0),h){for(y in h)d[y]=h[y];m.push.apply(m,v)}if(s)for(r=0,o=(s=Ko(s,"|")).length;r<o;r++)if(i=x.exec(s[r])){if(u={},f=i[1],l=i[2].replace(/[\\:]:/g,":"),p=i[3],b=i[4],"!"===f&&(a.attributesRequired=a.attributesRequired||[],a.attributesRequired.push(l),u.required=!0),"-"===f){delete d[l],m.splice(Wo(m,l),1);continue}p&&("="===p&&(a.attributesDefault=a.attributesDefault||[],a.attributesDefault.push({name:l,value:b}),u.defaultValue=b),":"===p&&(a.attributesForced=a.attributesForced||[],a.attributesForced.push({name:l,value:b}),u.forcedValue=b),"<"===p&&(u.validValues=Vo(b,"?"))),w.test(l)?(a.attributePatterns=a.attributePatterns||[],u.pattern=S(l),a.attributePatterns.push(u)):(d[l]||m.push(l),d[l]=u)}h||"@"!==c||(h=d,v=m),g&&(a.outputName=c,N[g]=a),w.test(c)?(a.pattern=S(c),E.push(a)):N[c]=a}},b=function(e){N={},E=[],y(e),Ho(n,function(e,t){p[t]=e.children})},C=function(e){var a=/^(~)?(.+)$/;e&&(Uo.text_block_elements=Uo.block_elements=null,Ho(Ko(e,","),function(e){var t=a.exec(e),n="~"===t[1],r=n?"span":"div",o=t[2];if(p[o]=p[r],g[o]=r,n||(c[o.toUpperCase()]={},c[o]={}),!N[o]){var i=N[r];delete(i=jo({},i)).removeEmptyAttrs,delete i.removeEmpty,N[o]=i}Ho(p,function(e,t){e[r]&&(p[t]=e=jo({},p[t]),e[o]=e[r])})}))},x=function(e){var o=/^([+\-]?)(\w+)\[([^\]]+)\]$/;Uo[i.schema]=null,e&&Ho(Ko(e,","),function(e){var t,n,r=o.exec(e);r&&(n=r[1],t=n?p[r[2]]:p[r[2]]={"#comment":{}},t=p[r[2]],Ho(Ko(r[3],"|"),function(e){"-"===n?delete t[e]:t[e]={}}))})},w=function(e){var t,n=N[e];if(n)return n;for(t=E.length;t--;)if((n=E[t]).pattern.test(e))return n};return i.valid_elements?b(i.valid_elements):(Ho(n,function(e,t){N[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},p[t]=e.children}),"html5"!==i.schema&&Ho(Ko("strong/b em/i"),function(e){e=Ko(e,"/"),N[e[1]].outputName=e[0]}),Ho(Ko("ol ul sub sup blockquote span font a table tbody tr strong em b i"),function(e){N[e]&&(N[e].removeEmpty=!0)}),Ho(Ko("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),function(e){N[e].paddEmpty=!0}),Ho(Ko("span"),function(e){N[e].removeEmptyAttrs=!0})),C(i.custom_elements),x(i.valid_children),y(i.extended_valid_elements),x("+ol[ul|ol],+ul[ul|ol]"),Ho({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},function(e,t){N[t]&&(N[t].parentsRequired=Ko(e))}),i.invalid_elements&&Ho($o(i.invalid_elements),function(e){N[e]&&delete N[e]}),w("span")||y("span[!data-mce-type|*]"),{children:p,elements:N,getValidStyles:function(){return e},getValidClasses:function(){return s},getBlockElements:function(){return c},getInvalidStyles:function(){return t},getShortEndedElements:function(){return a},getTextBlockElements:function(){return d},getTextInlineElements:function(){return m},getBoolAttrs:function(){return u},getElementRule:w,getSelfClosingElements:function(){return o},getNonEmptyElements:function(){return l},getMoveCaretBeforeOnEnterElements:function(){return f},getWhiteSpaceElements:function(){return r},getSpecialElements:function(){return h},isValidChild:function(e,t){var n=p[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:function(e,t){var n,r,o=w(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns)for(r=n.length;r--;)if(n[r].pattern.test(e))return!0}return!1},getCustomElements:function(){return g},addValidElements:y,setValidElements:b,addCustomElements:C,addValidChildren:x}}var Jo=function(e,t,n,r){var o=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+o(t)+o(n)+o(r)};function Qo(b,e){var C,t,c,l,x=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,w=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,N=/\s*([^:]+):\s*([^;]+);?/g,E=/\s+$/,S={},k="\ufeff";for(b=b||{},e&&(c=e.getValidStyles(),l=e.getInvalidStyles()),t=("\\\" \\' \\; \\: ; : "+k).split(" "),C=0;C<t.length;C++)S[t[C]]=k+C,S[k+C]=t[C];return{toHex:function(e){return e.replace(x,Jo)},parse:function(e){var t,n,r,o,i,a,u,s,c={},l=b.url_converter,f=b.url_converter_scope||this,d=function(e,t,n){var r,o,i,a;if((r=c[e+"-top"+t])&&(o=c[e+"-right"+t])&&(i=c[e+"-bottom"+t])&&(a=c[e+"-left"+t])){var u=[r,o,i,a];for(C=u.length-1;C--&&u[C]===u[C+1];);-1<C&&n||(c[e+t]=-1===C?u[0]:u.join(" "),delete c[e+"-top"+t],delete c[e+"-right"+t],delete c[e+"-bottom"+t],delete c[e+"-left"+t])}},m=function(e){var t,n=c[e];if(n){for(t=(n=n.split(" ")).length;t--;)if(n[t]!==n[0])return!1;return c[e]=n[0],!0}},p=function(e){return o=!0,S[e]},g=function(e,t){return o&&(e=e.replace(/\uFEFF[0-9]/g,function(e){return S[e]})),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},h=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},v=function(e){return e.replace(/\\[0-9a-f]+/gi,h)},y=function(e,t,n,r,o,i){if(o=o||i)return"'"+(o=g(o)).replace(/\'/g,"\\'")+"'";if(t=g(t||n||r),!b.allow_script_urls){var a=t.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(a))return"";if(!b.allow_svg_data_urls&&/^data:image\/svg/i.test(a))return""}return l&&(t=l.call(f,t,"style")),"url('"+t.replace(/\'/g,"\\'")+"')"};if(e){for(e=(e=e.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,p).replace(/\"[^\"]+\"|\'[^\']+\'/g,function(e){return e.replace(/[;:]/g,p)});t=N.exec(e);)if(N.lastIndex=t.index+t[0].length,n=t[1].replace(E,"").toLowerCase(),r=t[2].replace(E,""),n&&r){if(n=v(n),r=v(r),-1!==n.indexOf(k)||-1!==n.indexOf('"'))continue;if(!b.allow_script_urls&&("behavior"===n||/expression\s*\(|\/\*|\*\//.test(r)))continue;"font-weight"===n&&"700"===r?r="bold":"color"!==n&&"background-color"!==n||(r=r.toLowerCase()),r=(r=r.replace(x,Jo)).replace(w,y),c[n]=o?g(r,!0):r}d("border","",!0),d("border","-width"),d("border","-color"),d("border","-style"),d("padding",""),d("margin",""),i="border",u="border-style",s="border-color",m(a="border-width")&&m(u)&&m(s)&&(c[i]=c[a]+" "+c[u]+" "+c[s],delete c[a],delete c[u],delete c[s]),"medium none"===c.border&&delete c.border,"none"===c["border-image"]&&delete c["border-image"]}return c},serialize:function(i,e){var t,n,r,o,a,u="",s=function(e){var t,n,r,o;if(t=c[e])for(n=0,r=t.length;n<r;n++)e=t[n],(o=i[e])&&(u+=(0<u.length?" ":"")+e+": "+o+";")};if(e&&c)s("*"),s(e);else for(t in i)!(n=i[t])||l&&(r=t,o=e,a=void 0,(a=l["*"])&&a[r]||(a=l[o])&&a[r])||(u+=(0<u.length?" ":"")+t+": "+n+";");return u}}}var Zo,ei=Dt.each,ti=Dt.grep,ni=de.ie,ri=/^([a-z0-9],?)+$/i,oi=/^[ \t\r\n]*$/,ii=function(n,r,o){var e={},i=r.keep_values,t={set:function(e,t,n){r.url_converter&&(t=r.url_converter.call(r.url_converter_scope||o(),t,n,e[0])),e.attr("data-mce-"+n,t).attr(n,t)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}};return e={style:{set:function(e,t){null===t||"object"!=typeof t?(i&&e.attr("data-mce-style",t),e.attr("style",t)):e.css(t)},get:function(e){var t=e.attr("data-mce-style")||e.attr("style");return t=n.serialize(n.parse(t),e[0].nodeName)}}},i&&(e.href=e.src=t),e},ai=function(e,t){var n=t.attr("style"),r=e.serialize(e.parse(n),t[0].nodeName);r||(r=null),t.attr("data-mce-style",r)},ui=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o};function si(a,u){var s,c=this;void 0===u&&(u={});var r={},i=window,o={},t=0,e=function(m,e){var p,g=0,h={};p=(e=e||{}).maxLoadTime||5e3;var v=function(e){m.getElementsByTagName("head")[0].appendChild(e)},n=function(e,t,n){var o,r,i,a,u=function(){for(var e=a.passed,t=e.length;t--;)e[t]();a.status=2,a.passed=[],a.failed=[]},s=function(){for(var e=a.failed,t=e.length;t--;)e[t]();a.status=3,a.passed=[],a.failed=[]},c=function(e,t){e()||((new Date).getTime()-i<p?ve.setTimeout(t):s())},l=function(){c(function(){for(var e,t,n=m.styleSheets,r=n.length;r--;)if((t=(e=n[r]).ownerNode?e.ownerNode:e.owningElement)&&t.id===o.id)return u(),!0},l)},f=function(){c(function(){try{var e=r.sheet.cssRules;return u(),!!e}catch(t){}},f)};if(e=Dt._addCacheSuffix(e),h[e]?a=h[e]:(a={passed:[],failed:[]},h[e]=a),t&&a.passed.push(t),n&&a.failed.push(n),1!==a.status)if(2!==a.status)if(3!==a.status){if(a.status=1,(o=m.createElement("link")).rel="stylesheet",o.type="text/css",o.id="u"+g++,o.async=!1,o.defer=!1,i=(new Date).getTime(),"onload"in o&&!((d=navigator.userAgent.match(/WebKit\/(\d*)/))&&parseInt(d[1],10)<536))o.onload=l,o.onerror=s;else{if(0<navigator.userAgent.indexOf("Firefox"))return(r=m.createElement("style")).textContent='@import "'+e+'"',f(),void v(r);l()}var d;v(o),o.href=e}else s();else u()},t=function(t){return Wr.nu(function(e){n(t,V.compose(e,V.constant(Qr.value(t))),V.compose(e,V.constant(Qr.error(t))))})},o=function(e){return e.fold(V.identity,V.identity)};return{load:n,loadAll:function(e,n,r){Yr.par(H.map(e,t)).get(function(e){var t=H.partition(e,function(e){return e.isValue()});0<t.fail.length?r(t.fail.map(o)):n(t.pass.map(o))})}}}(a),l=[],f=u.schema?u.schema:Go({}),d=Qo({url_converter:u.url_converter,url_converter_scope:u.url_converter_scope},u.schema),m=u.ownEvents?new ke(u.proxy):ke.Event,n=f.getBlockElements(),p=Jt.overrideDefaults(function(){return{context:a,element:q.getRoot()}}),g=function(e){if(e&&a&&"string"==typeof e){var t=a.getElementById(e);return t&&t.id!==e?a.getElementsByName(e)[1]:t}return e},h=function(e){return"string"==typeof e&&(e=g(e)),p(e)},v=function(e,t,n){var r,o,i=h(e);return i.length&&(o=(r=s[t])&&r.get?r.get(i,t):i.attr(t)),void 0===o&&(o=n||""),o},y=function(e){var t=g(e);return t?t.attributes:[]},b=function(e,t,n){var r,o;""===n&&(n=null);var i=h(e);r=i.attr(t),i.length&&((o=s[t])&&o.set?o.set(i,n,t):i.attr(t,n),r!==n&&u.onSetAttrib&&u.onSetAttrib({attrElm:i,attrName:t,attrValue:n}))},C=function(){return u.root_element||a.body},x=function(e,t){return qr.getPos(a.body,g(e),t)},w=function(e,t,n){var r=h(e);return n?r.css(t):("float"===(t=t.replace(/-(\D)/g,function(e,t){return t.toUpperCase()}))&&(t=de.ie&&de.ie<12?"styleFloat":"cssFloat"),r[0]&&r[0].style?r[0].style[t]:undefined)},N=function(e){var t,n;return e=g(e),t=w(e,"width"),n=w(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},E=function(e,t){var n;if(!e)return!1;if(!Array.isArray(e)){if("*"===t)return 1===e.nodeType;if(ri.test(t)){var r=t.toLowerCase().split(/,/),o=e.nodeName.toLowerCase();for(n=r.length-1;0<=n;n--)if(r[n]===o)return!0;return!1}if(e.nodeType&&1!==e.nodeType)return!1}var i=Array.isArray(e)?e:[e];return 0<ct(t,i[0].ownerDocument||i[0],null,i).length},S=function(e,t,n,r){var o,i=[],a=g(e);for(r=r===undefined,n=n||("BODY"!==C().nodeName?C().parentNode:null),Dt.is(t,"string")&&(t="*"===(o=t)?function(e){return 1===e.nodeType}:function(e){return E(e,o)});a&&a!==n&&a.nodeType&&9!==a.nodeType;){if(!t||"function"==typeof t&&t(a)){if(!r)return[a];i.push(a)}a=a.parentNode}return r?i:null},k=function(e,t,n){var r=t;if(e)for("string"==typeof t&&(r=function(e){return E(e,t)}),e=e[n];e;e=e[n])if("function"==typeof r&&r(e))return e;return null},T=function(e,n,r){var o,t="string"==typeof e?g(e):e;if(!t)return!1;if(Dt.isArray(t)&&(t.length||0===t.length))return o=[],ei(t,function(e,t){e&&("string"==typeof e&&(e=g(e)),o.push(n.call(r,e,t)))}),o;var i=r||c;return n.call(i,t)},A=function(e,t){h(e).each(function(e,n){ei(t,function(e,t){b(n,t,e)})})},R=function(e,r){var t=h(e);ni?t.each(function(e,t){if(!1!==t.canHaveHTML){for(;t.firstChild;)t.removeChild(t.firstChild);try{t.innerHTML="<br>"+r,t.removeChild(t.firstChild)}catch(n){Jt("<div></div>").html("<br>"+r).contents().slice(1).appendTo(t)}return r}}):t.html(r)},_=function(e,n,r,o,i){return T(e,function(e){var t="string"==typeof n?a.createElement(n):n;return A(t,r),o&&("string"!=typeof o&&o.nodeType?t.appendChild(o):"string"==typeof o&&R(t,o)),i?t:e.appendChild(t)})},B=function(e,t,n){return _(a.createElement(e),e,t,n,!0)},D=zo.decode,O=zo.encodeAllRaw,P=function(e,t){var n=h(e);return t?n.each(function(){for(var e;e=this.firstChild;)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)}).remove():n.remove(),1<n.length?n.toArray():n[0]},L=function(e,t,n){h(e).toggleClass(t,n).each(function(){""===this.className&&Jt(this).attr("class",null)})},I=function(t,e,n){return T(e,function(e){return Dt.is(e,"array")&&(t=t.cloneNode(!0)),n&&ei(ti(e.childNodes),function(e){t.appendChild(e)}),e.parentNode.replaceChild(t,e)})},M=function(){return a.createRange()},F=function(e,t,n,r){if(Dt.isArray(e)){for(var o=e.length;o--;)e[o]=F(e[o],t,n,r);return e}return!u.collect||e!==a&&e!==i||l.push([e,t,n,r]),m.bind(e,t,n,r||q)},z=function(e,t,n){var r;if(Dt.isArray(e)){for(r=e.length;r--;)e[r]=z(e[r],t,n);return e}if(l&&(e===a||e===i))for(r=l.length;r--;){var o=l[r];e!==o[0]||t&&t!==o[1]||n&&n!==o[2]||m.unbind(o[0],o[1],o[2])}return m.unbind(e,t,n)},U=function(e){if(e&&So.isElement(e)){var t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},q={doc:a,settings:u,win:i,files:o,stdMode:!0,boxModel:!0,styleSheetLoader:e,boundEvents:l,styles:d,schema:f,events:m,isBlock:function(e){if("string"==typeof e)return!!n[e];if(e){var t=e.nodeType;if(t)return!(1!==t||!n[e.nodeName])}return!1},$:p,$$:h,root:null,clone:function(t,e){if(!ni||1!==t.nodeType||e)return t.cloneNode(e);if(!e){var n=a.createElement(t.nodeName);return ei(y(t),function(e){b(n,e.nodeName,v(t,e.nodeName))}),n}return null},getRoot:C,getViewPort:function(e){var t=e||i,n=t.document,r=n.documentElement;return{x:t.pageXOffset||r.scrollLeft,y:t.pageYOffset||r.scrollTop,w:t.innerWidth||r.clientWidth,h:t.innerHeight||r.clientHeight}},getRect:function(e){var t,n;return e=g(e),t=x(e),n=N(e),{x:t.x,y:t.y,w:n.w,h:n.h}},getSize:N,getParent:function(e,t,n){var r=S(e,t,n,!1);return r&&0<r.length?r[0]:null},getParents:S,get:g,getNext:function(e,t){return k(e,t,"nextSibling")},getPrev:function(e,t){return k(e,t,"previousSibling")},select:function(e,t){return ct(e,g(t)||u.root_element||a,[])},is:E,add:_,create:B,createHTML:function(e,t,n){var r,o="";for(r in o+="<"+e,t)t.hasOwnProperty(r)&&null!==t[r]&&"undefined"!=typeof t[r]&&(o+=" "+r+'="'+O(t[r])+'"');return void 0!==n?o+">"+n+"</"+e+">":o+" />"},createFragment:function(e){var t,n=a.createElement("div"),r=a.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)r.appendChild(t);return r},remove:P,setStyle:function(e,t,n){var r=h(e).css(t,n);u.update_styles&&ai(d,r)},getStyle:w,setStyles:function(e,t){var n=h(e).css(t);u.update_styles&&ai(d,n)},removeAllAttribs:function(e){return T(e,function(e){var t,n=e.attributes;for(t=n.length-1;0<=t;t--)e.removeAttributeNode(n.item(t))})},setAttrib:b,setAttribs:A,getAttrib:v,getPos:x,parseStyle:function(e){return d.parse(e)},serializeStyle:function(e,t){return d.serialize(e,t)},addStyle:function(e){var t,n;if(q!==si.DOM&&a===document){if(r[e])return;r[e]=!0}(n=a.getElementById("mceDefaultStyles"))||((n=a.createElement("style")).id="mceDefaultStyles",n.type="text/css",(t=a.getElementsByTagName("head")[0]).firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(a.createTextNode(e))},loadCSS:function(e){var n;q===si.DOM||a!==document?(e||(e=""),n=a.getElementsByTagName("head")[0],ei(e.split(","),function(e){var t;e=Dt._addCacheSuffix(e),o[e]||(o[e]=!0,t=B("link",{rel:"stylesheet",href:e}),n.appendChild(t))})):si.DOM.loadCSS(e)},addClass:function(e,t){h(e).addClass(t)},removeClass:function(e,t){L(e,t,!1)},hasClass:function(e,t){return h(e).hasClass(t)},toggleClass:L,show:function(e){h(e).show()},hide:function(e){h(e).hide()},isHidden:function(e){return"none"===h(e).css("display")},uniqueId:function(e){return(e||"mce_")+t++},setHTML:R,getOuterHTML:function(e){var t="string"==typeof e?g(e):e;return So.isElement(t)?t.outerHTML:Jt("<div></div>").append(Jt(t).clone()).html()},setOuterHTML:function(e,t){h(e).each(function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}P(Jt(this).html(t),!0)})},decode:D,encode:O,insertAfter:function(e,t){var r=g(t);return T(e,function(e){var t,n;return t=r.parentNode,(n=r.nextSibling)?t.insertBefore(e,n):t.appendChild(e),e})},replace:I,rename:function(t,e){var n;return t.nodeName!==e.toUpperCase()&&(n=B(e),ei(y(t),function(e){b(n,e.nodeName,v(t,e.nodeName))}),I(n,t,!0)),n||t},findCommonAncestor:function(e,t){for(var n,r=e;r;){for(n=t;n&&r!==n;)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},toHex:function(e){return d.toHex(Dt.trim(e))},run:T,getAttribs:y,isEmpty:function(e,t){var n,r,o,i,a,u,s=0;if(e=e.firstChild){a=new Zr(e,e.parentNode),t=t||(f?f.getNonEmptyElements():null),i=f?f.getWhiteSpaceElements():{};do{if(o=e.nodeType,So.isElement(e)){var c=e.getAttribute("data-mce-bogus");if(c){e=a.next("all"===c);continue}if(u=e.nodeName.toLowerCase(),t&&t[u]){if("br"===u){s++,e=a.next();continue}return!1}for(n=(r=y(e)).length;n--;)if("name"===(u=r[n].nodeName)||"data-mce-bookmark"===u)return!1}if(8===o)return!1;if(3===o&&!oi.test(e.nodeValue))return!1;if(3===o&&e.parentNode&&i[e.parentNode.nodeName]&&oi.test(e.nodeValue))return!1;e=a.next()}while(e)}return s<=1},createRng:M,nodeIndex:ui,split:function(e,t,n){var r,o,i,a=M();if(e&&t)return a.setStart(e.parentNode,ui(e)),a.setEnd(t.parentNode,ui(t)),r=a.extractContents(),(a=M()).setStart(t.parentNode,ui(t)+1),a.setEnd(e.parentNode,ui(e)+1),o=a.extractContents(),(i=e.parentNode).insertBefore(Ao.trimNode(q,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(Ao.trimNode(q,o),e),P(e),n||t},bind:F,unbind:z,fire:function(e,t,n){return m.fire(e,t,n)},getContentEditable:U,getContentEditableParent:function(e){for(var t=C(),n=null;e&&e!==t&&null===(n=U(e));e=e.parentNode);return n},destroy:function(){if(l)for(var e=l.length;e--;){var t=l[e];m.unbind(t[0],t[1],t[2])}ct.setDocument&&ct.setDocument()},isChildOf:function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1},dumpRng:function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset}};return s=ii(d,u,function(){return q}),q}(Zo=si||(si={})).DOM=Zo(document),Zo.nodeIndex=ui;var ci=si,li=ci.DOM,fi=Dt.each,di=Dt.grep,mi=function(e){return"function"==typeof e},pi=function(){var f={},o=[],i={},a=[],d=0;this.isDone=function(e){return 2===f[e]},this.markDone=function(e){f[e]=2},this.add=this.load=function(e,t,n,r){f[e]===undefined&&(o.push(e),f[e]=0),t&&(i[e]||(i[e]=[]),i[e].push({success:t,failure:r,scope:n||this}))},this.remove=function(e){delete f[e],delete i[e]},this.loadQueue=function(e,t,n){this.loadScripts(o,e,t,n)},this.loadScripts=function(n,e,t,r){var s,c=[],l=function(t,e){fi(i[e],function(e){mi(e[t])&&e[t].call(e.scope)}),i[e]=undefined};a.push({success:e,failure:r,scope:t||this}),(s=function(){var e=di(n);if(n.length=0,fi(e,function(e){var t,n,r,o,i,a,u;2!==f[e]?3!==f[e]?1!==f[e]&&(f[e]=1,d++,t=e,n=function(){f[e]=2,d--,l("success",e),s()},r=function(){f[e]=3,d--,c.push(e),l("failure",e),s()},u=function(){a.remove(i),o&&(o.onreadystatechange=o.onload=o=null),n()},i=(a=li).uniqueId(),(o=document.createElement("script")).id=i,o.type="text/javascript",o.src=Dt._addCacheSuffix(t),"onreadystatechange"in o?o.onreadystatechange=function(){/loaded|complete/.test(o.readyState)&&u()}:o.onload=u,o.onerror=function(){mi(r)?r():"undefined"!=typeof console&&console.log&&console.log("Failed to load script: "+t)},(document.getElementsByTagName("head")[0]||document.body).appendChild(o)):l("failure",e):l("success",e)}),!d){var t=a.slice(0);a.length=0,fi(t,function(e){0===c.length?mi(e.success)&&e.success.call(e.scope):mi(e.failure)&&e.failure.call(e.scope,c)})}})()}};pi.ScriptLoader=new pi;var gi,hi=Dt.each;function vi(){var r=this,o=[],a={},u={},i=[],s=function(e){var t;return u[e]&&(t=u[e].dependencies),t||[]},c=function(e,t){return"object"==typeof t?t:"string"==typeof e?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}},l=function(n,e,t){var r=s(name);hi(r,function(e){var t=c(n,e);f(t.resource,t,undefined,undefined)}),e&&(t?e.call(t):e.call(pi))},f=function(e,t,n,r,o){if(!a[e]){var i="string"==typeof t?t:t.prefix+t.resource+t.suffix;0!==i.indexOf("/")&&-1===i.indexOf("://")&&(i=vi.baseURL+"/"+i),a[e]=i.substring(0,i.lastIndexOf("/")),u[e]?l(t,n,r):pi.ScriptLoader.add(i,function(){return l(t,n,r)},r,o)}};return{items:o,urls:a,lookup:u,_listeners:i,get:function(e){return u[e]?u[e].instance:undefined},dependencies:s,requireLangPack:function(e,t){var n=vi.language;if(n&&!1!==vi.languageLoad){if(t)if(-1!==(t=","+t+",").indexOf(","+n.substr(0,2)+","))n=n.substr(0,2);else if(-1===t.indexOf(","+n+","))return;pi.ScriptLoader.add(a[e]+"/langs/"+n+".js")}},add:function(t,e,n){o.push(e),u[t]={instance:e,dependencies:n};var r=H.partition(i,function(e){return e.name===t});return i=r.fail,hi(r.pass,function(e){e.callback()}),e},remove:function(e){delete a[e],delete u[e]},createUrl:c,addComponents:function(e,t){var n=r.urls[e];hi(t,function(e){pi.ScriptLoader.add(n+"/"+e)})},load:f,waitFor:function(e,t){u.hasOwnProperty(e)?t():i.push({name:e,callback:t})}}}(gi=vi||(vi={})).PluginManager=gi(),gi.ThemeManager=gi();var yi,bi="\ufeff",Ci=function(e){return e===bi},xi=bi,wi=function(e){return e.replace(new RegExp(bi,"g"),"")},Ni=So.isElement,Ei=So.isText,Si=function(e){return Ei(e)&&(e=e.parentNode),Ni(e)&&e.hasAttribute("data-mce-caret")},ki=function(e){return Ei(e)&&Ci(e.data)},Ti=function(e){return Si(e)||ki(e)},Ai=function(e){return e.firstChild!==e.lastChild||!So.isBr(e.firstChild)},Ri=function(e){var t=e.container();return e&&So.isText(t)&&t.data.charAt(e.offset())===xi},_i=function(e){var t=e.container();return e&&So.isText(t)&&t.data.charAt(e.offset()-1)===xi},Bi=function(e,t,n){var r,o,i;return(r=t.ownerDocument.createElement(e)).setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(((i=document.createElement("br")).setAttribute("data-mce-bogus","1"),i)),o=t.parentNode,n?o.insertBefore(r,t):t.nextSibling?o.insertBefore(r,t.nextSibling):o.appendChild(r),r},Di=function(e){return Ei(e)&&e.data[0]===xi},Oi=function(e){return Ei(e)&&e.data[e.data.length-1]===xi},Pi=function(e){return e&&e.hasAttribute("data-mce-caret")?(t=e.getElementsByTagName("br"),n=t[t.length-1],So.isBogus(n)&&n.parentNode.removeChild(n),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null;var t,n},Li=So.isContentEditableTrue,Ii=So.isContentEditableFalse,Mi=So.isBr,Fi=So.isText,zi=So.matchNodeNames("script style textarea"),Ui=So.matchNodeNames("img input textarea hr iframe video audio object"),qi=So.matchNodeNames("table"),Vi=Ti,Hi=function(e){return!Vi(e)&&(Fi(e)?!zi(e.parentNode):Ui(e)||Mi(e)||qi(e)||ji(e))},ji=function(e){return!1===(t=e,So.isElement(t)&&"true"===t.getAttribute("unselectable"))&&Ii(e);var t},$i=function(e,t){return Hi(e)&&function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(ji(e))return!1;if(Li(e))return!0}return!0}(e,t)},Wi=Math.round,Ki=function(e){return e?{left:Wi(e.left),top:Wi(e.top),bottom:Wi(e.bottom),right:Wi(e.right),width:Wi(e.width),height:Wi(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},Xi=function(e,t){return e=Ki(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e},Yi=function(e,t,n){return 0<=e&&e<=Math.min(t.height,n.height)/2},Gi=function(e,t){return e.bottom-e.height/2<t.top||!(e.top>t.bottom)&&Yi(t.top-e.bottom,e,t)},Ji=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&Yi(t.bottom-e.top,e,t)},Qi=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Zi=function(e,t){return 1===e.nodeType&&e.hasChildNodes()&&(t>=e.childNodes.length&&(t=e.childNodes.length-1),e=e.childNodes[t]),e},ea=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),ta=function(e){return"string"==typeof e&&768<=e.charCodeAt(0)&&ea.test(e)},na=[].slice,ra=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=na.call(arguments);return r.length-1>=e.length?e.apply(this,r.slice(1)):function(){var e=r.concat([].slice.call(arguments));return ra.apply(this,e)}},oa={constant:function(e){return function(){return e}},negate:function(t){return function(e){return!t(e)}},and:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=na.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(!n[t](e))return!1;return!0}},or:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=na.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(n[t](e))return!0;return!1}},curry:ra,compose:function(t,n){return function(e){return t(n(e))}},noop:function(){}},ia=function(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];if(!o.isSome())return A.none();n.push(o.getOrDie())}return A.some(t.apply(null,n))},aa=So.isElement,ua=Hi,sa=So.matchStyleValues("display","block table"),ca=So.matchStyleValues("float","left right"),la=oa.and(aa,ua,oa.negate(ca)),fa=oa.negate(So.matchStyleValues("white-space","pre pre-line pre-wrap")),da=So.isText,ma=So.isBr,pa=ci.nodeIndex,ga=Zi,ha=function(e){return"createRange"in e?e.createRange():ci.DOM.createRng()},va=function(e){return e&&/[\r\n\t ]/.test(e)},ya=function(e){return!!e.setStart&&!!e.setEnd},ba=function(e){var t,n=e.startContainer,r=e.startOffset;return!!(va(e.toString())&&fa(n.parentNode)&&So.isText(n)&&(t=n.data,va(t[r-1])||va(t[r+1])))},Ca=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},xa=function(e){var t,n,r,o,i,a,u,s;return t=0<(n=e.getClientRects()).length?Ki(n[0]):Ki(e.getBoundingClientRect()),!ya(e)&&ma(e)&&Ca(t)?(i=(r=e).ownerDocument,a=ha(i),u=i.createTextNode("\xa0"),(s=r.parentNode).insertBefore(u,r),a.setStart(u,0),a.setEnd(u,1),o=Ki(a.getBoundingClientRect()),s.removeChild(u),o):Ca(t)&&ya(e)?function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&So.isText(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),xa(i)}return null}(e):t},wa=function(e,t){var n=Xi(e,t);return n.width=1,n.right=n.left+1,n},Na=function(e){var t,n,r=[],o=function(e){var t,n;0!==e.height&&(0<r.length&&(t=e,n=r[r.length-1],t.left===n.left&&t.top===n.top&&t.bottom===n.bottom&&t.right===n.right)||r.push(e))},i=function(e,t){var n=ha(e.ownerDocument);if(t<e.data.length){if(ta(e.data[t]))return r;if(ta(e.data[t-1])&&(n.setStart(e,t),n.setEnd(e,t+1),!ba(n)))return o(wa(xa(n),!1)),r}0<t&&(n.setStart(e,t-1),n.setEnd(e,t),ba(n)||o(wa(xa(n),!1))),t<e.data.length&&(n.setStart(e,t),n.setEnd(e,t+1),ba(n)||o(wa(xa(n),!0)))};if(da(e.container()))return i(e.container(),e.offset()),r;if(aa(e.container()))if(e.isAtEnd())n=ga(e.container(),e.offset()),da(n)&&i(n,n.data.length),la(n)&&!ma(n)&&o(wa(xa(n),!1));else{if(n=ga(e.container(),e.offset()),da(n)&&i(n,0),la(n)&&e.isAtEnd())return o(wa(xa(n),!1)),r;t=ga(e.container(),e.offset()-1),la(t)&&!ma(t)&&(sa(t)||sa(n)||!la(n))&&o(wa(xa(t),!1)),la(n)&&o(wa(xa(n),!0))}return r};function Ea(t,n,e){var r=function(){return e||(e=Na(Ea(t,n))),e};return{container:oa.constant(t),offset:oa.constant(n),toRange:function(){var e;return(e=ha(t.ownerDocument)).setStart(t,n),e.setEnd(t,n),e},getClientRects:r,isVisible:function(){return 0<r().length},isAtStart:function(){return da(t),0===n},isAtEnd:function(){return da(t)?n>=t.data.length:n>=t.childNodes.length},isEqual:function(e){return e&&t===e.container()&&n===e.offset()},getNode:function(e){return ga(t,e?n-1:n)}}}(yi=Ea||(Ea={})).fromRangeStart=function(e){return yi(e.startContainer,e.startOffset)},yi.fromRangeEnd=function(e){return yi(e.endContainer,e.endOffset)},yi.after=function(e){return yi(e.parentNode,pa(e)+1)},yi.before=function(e){return yi(e.parentNode,pa(e))},yi.isAbove=function(e,t){return ia([H.head(t.getClientRects()),H.last(e.getClientRects())],Gi).getOr(!1)},yi.isBelow=function(e,t){return ia([H.last(t.getClientRects()),H.head(e.getClientRects())],Ji).getOr(!1)},yi.isAtStart=function(e){return!!e&&e.isAtStart()},yi.isAtEnd=function(e){return!!e&&e.isAtEnd()},yi.isTextPosition=function(e){return!!e&&So.isText(e.container())},yi.isElementPosition=function(e){return!1===yi.isTextPosition(e)};var Sa,ka,Ta=Ea,Aa=So.isElement,Ra=So.isText,_a=function(e){var t=e.parentNode;t&&t.removeChild(e)},Ba=function(e,t){0===t.length?_a(e):e.nodeValue=t},Da=function(e){var t=wi(e);return{count:e.length-t.length,text:t}},Oa=function(e,t){return Ia(e),t},Pa=function(e,t){return Ra(e)&&t.container()===e?(r=t,o=Da((n=e).data.substr(0,r.offset())),i=Da(n.data.substr(r.offset())),0<(a=o.text+i.text).length?(Ba(n,a),Ta(n,r.offset()-o.count)):r):Oa(e,t);var n,r,o,i,a},La=function(e,t){return t.container()===e.parentNode?(n=e,o=(r=t).container(),i=H.indexOf(o.childNodes,n).map(function(e){return e<r.offset()?Ta(o,r.offset()-1):r}).getOr(r),Ia(n),i):Oa(e,t);var n,r,o,i},Ia=function(e){if(Aa(e)&&Ti(e)&&(Ai(e)?e.removeAttribute("data-mce-caret"):_a(e)),Ra(e)){var t=wi(function(e){try{return e.nodeValue}catch(t){return""}}(e));Ba(e,t)}},Ma={removeAndReposition:function(e,t){return Ta.isTextPosition(t)?Pa(e,t):La(e,t)},remove:Ia},Fa=function(e){return Ta.isTextPosition(e)?0===e.offset():Hi(e.getNode())},za=function(e){if(Ta.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return Hi(e.getNode(!0))},Ua=function(e,t){return!Ta.isTextPosition(e)&&!Ta.isTextPosition(t)&&e.getNode()===t.getNode(!0)},qa=function(e,t,n){return e?!Ua(t,n)&&(r=t,!(!Ta.isTextPosition(r)&&So.isBr(r.getNode())))&&za(t)&&Fa(n):!Ua(n,t)&&Fa(t)&&za(n);var r},Va=function(e,t,n){var r=qs(t);return A.from(e?r.next(n):r.prev(n))},Ha=function(e,t){var n,r,o,i,a,u=e?t.firstChild:t.lastChild;return So.isText(u)?A.some(Ta(u,e?0:u.data.length)):u?Hi(u)?A.some(e?Ta.before(u):(a=u,So.isBr(a)?Ta.before(a):Ta.after(a))):(r=t,o=u,i=(n=e)?Ta.before(o):Ta.after(o),Va(n,r,i)):A.none()},ja={fromPosition:Va,nextPosition:V.curry(Va,!0),prevPosition:V.curry(Va,!1),navigate:function(t,n,r){return Va(t,n,r).bind(function(e){return fs(r,e,n)&&qa(t,r,e)?Va(t,n,e):A.some(e)})},positionIn:Ha,firstPositionIn:V.curry(Ha,!0),lastPositionIn:V.curry(Ha,!1)},$a=So.isContentEditableTrue,Wa=So.isContentEditableFalse,Ka=function(e,t,n,r,o){return t._selectionOverrides.showCaret(e,n,r,o)},Xa=function(e,t){var n,r;return e.fire("BeforeObjectSelected",{target:t}).isDefaultPrevented()?null:((r=(n=t).ownerDocument.createRange()).selectNode(n),r)},Ya=function(e,t,n){var r=vs(1,e.getBody(),t),o=Ta.fromRangeStart(r),i=o.getNode();if(Wa(i))return Ka(1,e,i,!o.isAtEnd(),!1);var a=o.getNode(!0);if(Wa(a))return Ka(1,e,a,!1,!1);var u=e.dom.getParent(o.getNode(),function(e){return Wa(e)||$a(e)});return Wa(u)?Ka(1,e,u,!1,n):null},Ga=function(e,t,n){if(!t||!t.collapsed)return t;var r=Ya(e,t,n);return r||t};(ka=Sa||(Sa={}))[ka.Br=0]="Br",ka[ka.Block=1]="Block",ka[ka.Wrap=2]="Wrap",ka[ka.Eol=3]="Eol";var Ja,Qa,Za=function(e,t){return e===Ja.Backwards?t.reverse():t},eu=function(e,t,n,r){for(var o,i,a,u,s,c,l=qs(n),f=r,d=[];f&&(s=l,c=f,o=t===Ja.Forwards?s.next(c):s.prev(c));){if(So.isBr(o.getNode(!1)))return t===Ja.Forwards?{positions:Za(t,d).concat([o]),breakType:Sa.Br,breakAt:A.some(o)}:{positions:Za(t,d),breakType:Sa.Br,breakAt:A.some(o)};if(o.isVisible()){if(e(f,o)){var m=(i=t,a=f,u=o,So.isBr(u.getNode(i===Ja.Forwards))?Sa.Br:!1===fs(a,u)?Sa.Block:Sa.Wrap);return{positions:Za(t,d),breakType:m,breakAt:A.some(o)}}d.push(o),f=o}else f=o}return{positions:Za(t,d),breakType:Sa.Eol,breakAt:A.none()}},tu=function(n,r,o,e){return r(o,e).breakAt.map(function(e){var t=r(o,e).positions;return n===Ja.Backwards?t.concat(e):[e].concat(t)}).getOr([])},nu=function(e,i){return H.foldl(e,function(e,o){return e.fold(function(){return A.some(o)},function(r){return ia([H.head(r.getClientRects()),H.head(o.getClientRects())],function(e,t){var n=Math.abs(i-e.left);return Math.abs(i-t.left)<=n?o:r}).or(e)})},A.none())},ru=function(t,e){return H.head(e.getClientRects()).bind(function(e){return nu(t,e.left)})},ou=V.curry(eu,Ea.isAbove,-1),iu=V.curry(eu,Ea.isBelow,1),au=V.curry(tu,-1,ou),uu=V.curry(tu,1,iu),su=function(e,t){return Tr.all(t,e)},cu=function(e,t,n,r,o){var i,a,u,s,c,l=su(Fn.fromDom(n),"td,th").map(function(e){return e.dom()}),f=H.filter((i=e,a=l,H.bind(a,function(e){var t,n,r=(t=e.getBoundingClientRect(),n=-1,{left:t.left-n,top:t.top-n,right:t.right+2*n,bottom:t.bottom+2*n,width:t.width+n,height:t.height+n});return[{x:r.left,y:i(r),cell:e},{x:r.right,y:i(r),cell:e}]})),function(e){return t(e,o)});return(u=f,s=r,c=o,H.foldl(u,function(e,r){return e.fold(function(){return A.some(r)},function(e){var t=Math.sqrt(Math.abs(e.x-s)+Math.abs(e.y-c)),n=Math.sqrt(Math.abs(r.x-s)+Math.abs(r.y-c));return A.some(n<t?r:e)})},A.none())).map(function(e){return e.cell})},lu=V.curry(cu,function(e){return e.bottom},function(e,t){return e.y<t}),fu=V.curry(cu,function(e){return e.top},function(e,t){return e.y>t}),du=function(t,n){return H.head(n.getClientRects()).bind(function(e){return lu(t,e.left,e.top)}).bind(function(e){return ru((t=e,ja.lastPositionIn(t).map(function(e){return ou(t,e).positions.concat(e)}).getOr([])),n);var t})},mu=function(t,n){return H.last(n.getClientRects()).bind(function(e){return fu(t,e.left,e.top)}).bind(function(e){return ru((t=e,ja.firstPositionIn(t).map(function(e){return[e].concat(iu(t,e).positions)}).getOr([])),n);var t})},pu=function(e){for(var t=0,n=0,r=e;r&&r.nodeType;)t+=r.offsetLeft||0,n+=r.offsetTop||0,r=r.offsetParent;return{x:t,y:n}},gu=function(e,t,n){var r,o,i,a,u,s=e.dom,c=s.getRoot(),l=0;if(u={elm:t,alignToTop:n},e.fire("scrollIntoView",u),!u.isDefaultPrevented()&&So.isElement(t)){if(!1===n&&(l=t.offsetHeight),"BODY"!==c.nodeName){var f=e.selection.getScrollContainer();if(f)return r=pu(t).y-pu(f).y+l,a=f.clientHeight,void((r<(i=f.scrollTop)||i+a<r+25)&&(f.scrollTop=r<i?r:r-a+25))}o=s.getViewPort(e.getWin()),r=s.getPos(t).y+l,i=o.y,a=o.h,(r<o.y||i+a<r+25)&&e.getWin().scrollTo(0,r<i?r:r-a+25)}},hu=function(d,e){H.head(Ea.fromRangeStart(e).getClientRects()).each(function(e){var t,n,r,o,i,a,u,s,c,l=function(e){if(e.inline)return e.getBody().getBoundingClientRect();var t=e.getWin();return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight,width:t.innerWidth,height:t.innerHeight}}(d),f={x:(i=t=l,a=n=e,a.left>i.left&&a.right<i.right?0:a.left<i.left?a.left-i.left:a.right-i.right),y:(r=t,o=n,o.top>r.top&&o.bottom<r.bottom?0:o.top<r.top?o.top-r.top:o.bottom-r.bottom)};s=0!==f.x?0<f.x?f.x+4:f.x-4:0,c=0!==f.y?0<f.y?f.y+4:f.y-4:0,(u=d).inline?(u.getBody().scrollLeft+=s,u.getBody().scrollTop+=c):u.getWin().scrollBy(s,c)})},vu=function(e,t,n){var r=e.getParam(t,n);if(-1!==r.indexOf("=")){var o=e.getParam(t,"","hash");return o.hasOwnProperty(e.id)?o[e.id]:n}return r},yu=function(e){return e.getParam("iframe_attrs",{})},bu=function(e){return e.getParam("doctype","<!DOCTYPE html>")},Cu=function(e){return e.getParam("document_base_url","")},xu=function(e){return vu(e,"body_id","tinymce")},wu=function(e){return vu(e,"body_class","")},Nu=function(e){return e.getParam("content_security_policy","")},Eu=function(e){return e.getParam("br_in_pre",!0)},Su=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":t},ku=function(e){return e.getParam("forced_root_block_attrs",{})},Tu=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},Au=function(e){return e.getParam("no_newline_selector","")},Ru=function(e){return e.getParam("keep_styles",!0)},_u=function(e){return e.getParam("end_container_on_empty_block",!1)},Bu=function(e){return Dt.explode(e.getParam("font_size_style_values",""))},Du=function(e){return Dt.explode(e.getParam("font_size_classes",""))},Ou=function(t,n){Fr.parent(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},Pu=function(e,t){e.dom().appendChild(t.dom())},Lu={before:Ou,after:function(e,t){Fr.nextSibling(e).fold(function(){Fr.parent(e).each(function(e){Pu(e,t)})},function(e){Ou(e,t)})},prepend:function(t,n){Fr.firstChild(t).fold(function(){Pu(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},append:Pu,appendAt:function(e,t,n){Fr.child(e,n).fold(function(){Pu(e,t)},function(e){Ou(e,t)})},wrap:function(e,t){Ou(e,t),Pu(t,e)}},Iu=In.detect().browser,Mu=function(){return Iu.isIE()||Iu.isEdge()||Iu.isFirefox()},Fu=function(e,t){e.selection.setRng(t),hu(e,t)},zu=function(t,n,e){var r=t(n,e);return r.breakType===Sa.Wrap&&0===r.positions.length?r.breakAt.map(function(e){return t(n,e).breakAt.isNone()}).getOr(!0):r.breakAt.isNone()},Uu=oa.curry(zu,ou),qu=oa.curry(zu,iu),Vu=function(e,t,n,r){var o,i,a,u,s=e.selection.getRng(),c=t?1:-1;if(Mu()&&(o=t,i=s,a=n,u=Ta.fromRangeStart(i),ja.positionIn(!o,a).map(function(e){return e.isEqual(u)}).getOr(!1))){var l=Ka(c,e,n,!t,!0);return Fu(e,l),!0}return!1},Hu=function(e,t){var n=t.getNode(e);return So.isElement(n)&&"TABLE"===n.nodeName?A.some(n):A.none()},ju=function(u,s,c){var e=Hu(!!s,c),t=!1===s;e.fold(function(){return Fu(u,c.toRange())},function(a){return ja.positionIn(t,u.getBody()).filter(function(e){return e.isEqual(c)}).fold(function(){return Fu(u,c.toRange())},function(e){return n=s,o=a,t=c,void((i=Su(r=u))?r.undoManager.transact(function(){var e=Fn.fromTag(i);sr.setAll(e,ku(r)),Lu.append(e,Fn.fromTag("br")),n?Lu.after(Fn.fromDom(o),e):Lu.before(Fn.fromDom(o),e);var t=r.dom.createRng();t.setStart(e.dom(),0),t.setEnd(e.dom(),0),Fu(r,t)}):Fu(r,t.toRange()));var n,r,o,t,i})})},$u=function(e,t,n,r){var o,i,a,u,s,c,l=e.selection.getRng(),f=Ta.fromRangeStart(l),d=e.getBody();if(!t&&Uu(r,f)){var m=(u=d,du(s=n,c=f).orThunk(function(){return H.head(c.getClientRects()).bind(function(e){return nu(au(u,Ta.before(s)),e.left)})}).getOr(Ta.before(s)));return ju(e,t,m),!0}return!(!t||!qu(r,f))&&(o=d,m=mu(i=n,a=f).orThunk(function(){return H.head(a.getClientRects()).bind(function(e){return nu(uu(o,Ta.after(i)),e.left)})}).getOr(Ta.after(i)),ju(e,t,m),!0)},Wu=function(t,n){return function(){return A.from(t.dom.getParent(t.selection.getNode(),"td,th")).bind(function(e){return A.from(t.dom.getParent(e,"table")).map(function(e){return Vu(t,n,e)})}).getOr(!1)}},Ku=function(n,r){return function(){return A.from(n.dom.getParent(n.selection.getNode(),"td,th")).bind(function(t){return A.from(n.dom.getParent(t,"table")).map(function(e){return $u(n,r,e,t)})}).getOr(!1)}},Xu=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return Xu(n())}}},Yu=So.isContentEditableFalse,Gu=function(e,t,n){var r,o,i,a,u,s=Xi(t.getBoundingClientRect(),n);return"BODY"===e.tagName?(r=e.ownerDocument.documentElement,o=e.scrollLeft||r.scrollLeft,i=e.scrollTop||r.scrollTop):(u=e.getBoundingClientRect(),o=e.scrollLeft-u.left,i=e.scrollTop-u.top),s.left+=o,s.right+=o,s.top+=i,s.bottom+=i,s.width=1,0<(a=t.offsetWidth-t.clientWidth)&&(n&&(a*=-1),s.left+=a,s.right+=a),s},Ju=function(a,u,e){var t,s,c=Xu(A.none()),l=function(){!function(e){var t,n,r,o,i;for(t=Jt("*[contentEditable=false]",e),o=0;o<t.length;o++)r=(n=t[o]).previousSibling,Oi(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(i.length-1,1)),r=n.nextSibling,Di(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(0,1))}(a),s&&(Ma.remove(s),s=null),c.get().each(function(e){Jt(e.caret).remove(),c.set(A.none())}),clearInterval(t)},f=function(){t=ve.setInterval(function(){e()?Jt("div.mce-visual-caret",a).toggleClass("mce-visual-caret-hidden"):Jt("div.mce-visual-caret",a).addClass("mce-visual-caret-hidden")},500)};return{show:function(t,e){var n,r,o;if(l(),o=e,So.isElement(o)&&/^(TD|TH)$/i.test(o.tagName))return null;if(!u(e))return s=function(e,t){var n,r,o;if(r=e.ownerDocument.createTextNode(xi),o=e.parentNode,t){if(n=e.previousSibling,Ei(n)){if(Ti(n))return n;if(Oi(n))return n.splitText(n.data.length-1)}o.insertBefore(r,e)}else{if(n=e.nextSibling,Ei(n)){if(Ti(n))return n;if(Di(n))return n.splitText(1),n}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r}(e,t),r=e.ownerDocument.createRange(),Yu(s.nextSibling)?(r.setStart(s,0),r.setEnd(s,0)):(r.setStart(s,1),r.setEnd(s,1)),r;s=Bi("p",e,t),n=Gu(a,e,t),Jt(s).css("top",n.top);var i=Jt('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(n).appendTo(a)[0];return c.set(A.some({caret:i,element:e,before:t})),c.get().each(function(e){t&&Jt(e.caret).addClass("mce-visual-caret-before")}),f(),(r=e.ownerDocument.createRange()).setStart(s,0),r.setEnd(s,0),r},hide:l,getCss:function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"},reposition:function(){c.get().each(function(e){var t=Gu(a,e.element,e.before);Jt(e.caret).css(t)})},destroy:function(){return ve.clearInterval(t)}}},Qu=function(e){return Yu(e)||So.isTable(e)&&Mu()},Zu=So.isContentEditableFalse,es=So.matchStyleValues("display","block table table-cell table-caption list-item"),ts=Ti,ns=Si,rs=oa.curry,os=So.isElement,is=Hi,as=function(e){return 0<e},us=function(e){return e<0},ss=function(e,t){for(var n;n=e(t);)if(!ns(n))return n;return null},cs=function(e,t,n,r,o){var i=new Zr(e,r);if(us(t)){if((Zu(e)||ns(e))&&n(e=ss(i.prev,!0)))return e;for(;e=ss(i.prev,o);)if(n(e))return e}if(as(t)){if((Zu(e)||ns(e))&&n(e=ss(i.next,!0)))return e;for(;e=ss(i.next,o);)if(n(e))return e}return null},ls=function(e,t){for(;e&&e!==t;){if(es(e))return e;e=e.parentNode}return null},fs=function(e,t,n){return ls(e.container(),n)===ls(t.container(),n)},ds=function(e,t){var n,r;return t?(n=t.container(),r=t.offset(),os(n)?n.childNodes[r+e]:null):null},ms=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},ps=function(e,t,n){var r,o,i,a;for(o=e?"previousSibling":"nextSibling";n&&n!==t;){if(r=n[o],ts(r)&&(r=r[o]),Zu(r)){if(a=n,ls(r,i=t)===ls(a,i))return r;break}if(is(r))break;n=n.parentNode}return null},gs=rs(ms,!0),hs=rs(ms,!1),vs=function(e,t,n){var r,o,i,a,u=rs(ps,!0,t),s=rs(ps,!1,t);if(o=n.startContainer,i=n.startOffset,Si(o)){if(os(o)||(o=o.parentNode),"before"===(a=o.getAttribute("data-mce-caret"))&&(r=o.nextSibling,Qu(r)))return gs(r);if("after"===a&&(r=o.previousSibling,Qu(r)))return hs(r)}if(!n.collapsed)return n;if(So.isText(o)){if(ts(o)){if(1===e){if(r=s(o))return gs(r);if(r=u(o))return hs(r)}if(-1===e){if(r=u(o))return hs(r);if(r=s(o))return gs(r)}return n}if(Oi(o)&&i>=o.data.length-1)return 1===e&&(r=s(o))?gs(r):n;if(Di(o)&&i<=1)return-1===e&&(r=u(o))?hs(r):n;if(i===o.data.length)return(r=s(o))?gs(r):n;if(0===i)return(r=u(o))?hs(r):n}return n},ys=function(e,t){var n=ds(e,t);return Zu(n)&&!So.isBogusAll(n)},bs=function(e,t){return So.isTable(ds(e,t))},Cs=function(e,t){return A.from(ds(e?0:-1,t)).filter(Zu)},xs=function(e,t,n){var r=vs(e,t,n);return-1===e?Ea.fromRangeStart(r):Ea.fromRangeEnd(r)},ws=rs(ys,0),Ns=rs(ys,-1),Es=rs(bs,0),Ss=rs(bs,-1);(Qa=Ja||(Ja={}))[Qa.Backwards=-1]="Backwards",Qa[Qa.Forwards=1]="Forwards";var ks,Ts,As,Rs,_s,Bs=So.isContentEditableFalse,Ds=So.isText,Os=So.isElement,Ps=So.isBr,Ls=Hi,Is=function(e){return Ui(e)||!!ji(t=e)&&!0!==Tt.reduce(t.getElementsByTagName("*"),function(e,t){return e||Li(t)},!1);var t},Ms=$i,Fs=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},zs=function(e,t){if(as(e)){if(Ls(t.previousSibling)&&!Ds(t.previousSibling))return Ta.before(t);if(Ds(t))return Ta(t,0)}if(us(e)){if(Ls(t.nextSibling)&&!Ds(t.nextSibling))return Ta.after(t);if(Ds(t))return Ta(t,t.data.length)}return us(e)?Ps(t)?Ta.before(t):Ta.after(t):Ta.before(t)},Us=function(e,t,n){var r,o,i,a,u;if(!Os(n)||!t)return null;if(t.isEqual(Ta.after(n))&&n.lastChild){if(u=Ta.after(n.lastChild),us(e)&&Ls(n.lastChild)&&Os(n.lastChild))return Ps(n.lastChild)?Ta.before(n.lastChild):u}else u=t;var s,c,l,f=u.container(),d=u.offset();if(Ds(f)){if(us(e)&&0<d)return Ta(f,--d);if(as(e)&&d<f.length)return Ta(f,++d);r=f}else{if(us(e)&&0<d&&(o=Fs(f,d-1),Ls(o)))return!Is(o)&&(i=cs(o,e,Ms,o))?Ds(i)?Ta(i,i.data.length):Ta.after(i):Ds(o)?Ta(o,o.data.length):Ta.before(o);if(as(e)&&d<f.childNodes.length&&(o=Fs(f,d),Ls(o)))return Ps(o)&&n.lastChild===o?null:(s=o,c=n,So.isBr(s)&&(l=Us(1,Ta.after(s),c))&&!fs(Ta.before(s),Ta.before(l),c)?Us(e,Ta.after(o),n):!Is(o)&&(i=cs(o,e,Ms,o))?Ds(i)?Ta(i,0):Ta.before(i):Ds(o)?Ta(o,0):Ta.after(o));r=o||u.getNode()}return(as(e)&&u.isAtEnd()||us(e)&&u.isAtStart())&&(r=cs(r,e,oa.constant(!0),n,!0),Ms(r,n))?zs(e,r):(o=cs(r,e,Ms,n),!(a=Tt.last(Tt.filter(function(e,t){for(var n=[];e&&e!==t;)n.push(e),e=e.parentNode;return n}(f,n),Bs)))||o&&a.contains(o)?o?zs(e,o):null:u=as(e)?Ta.after(a):Ta.before(a))},qs=function(t){return{next:function(e){return Us(Ja.Forwards,e,t)},prev:function(e){return Us(Ja.Backwards,e,t)}}},Vs=function(e){return Dt.grep(e.childNodes,function(e){return"LI"===e.nodeName})},Hs=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&("\xa0"===(t=e.firstChild).data||So.isBr(t));var t},js=function(e){return 0<e.length&&(!(t=e[e.length-1]).firstChild||Hs(t))?e.slice(0,-1):e;var t},$s=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Ws=function(e,t){var n=Ta.after(e),r=qs(t).prev(n);return r?r.toRange():null},Ks=function(t,e,n){var r,o,i,a,u=t.parentNode;return Dt.each(e,function(e){u.insertBefore(e,t)}),r=t,o=n,i=Ta.before(r),(a=qs(o).next(i))?a.toRange():null},Xs=function(e,t){var n,r,o,i,a,u,s=t.firstChild,c=t.lastChild;return s&&"meta"===s.name&&(s=s.next),c&&"mce_marker"===c.attr("id")&&(c=c.prev),r=c,u=(n=e).getNonEmptyElements(),r&&(r.isEmpty(u)||(o=r,n.getBlockElements()[o.name]&&(a=o).firstChild&&a.firstChild===a.lastChild&&("br"===(i=o.firstChild).name||"\xa0"===i.value)))&&(c=c.prev),!(!s||s!==c||"ul"!==s.name&&"ol"!==s.name)},Ys=function(e,o,i,t){var n,r,a,u,s,c,l,f,d,m,p,g,h,v,y,b,C,x,w,N=(n=o,r=t,c=e.serialize(r),l=n.createFragment(c),u=(a=l).firstChild,s=a.lastChild,u&&"META"===u.nodeName&&u.parentNode.removeChild(u),s&&"mce_marker"===s.id&&s.parentNode.removeChild(s),a),E=$s(o,i.startContainer),S=js(Vs(N.firstChild)),k=o.getRoot(),T=function(e){var t=Ta.fromRangeStart(i),n=qs(o.getRoot()),r=1===e?n.prev(t):n.next(t);return!r||$s(o,r.getNode())!==E};return T(1)?Ks(E,S,k):T(2)?(f=E,d=S,m=k,o.insertAfter(d.reverse(),f),Ws(d[0],m)):(g=S,h=k,v=p=E,b=(y=i).cloneRange(),C=y.cloneRange(),b.setStartBefore(v),C.setEndAfter(v),x=[b.cloneContents(),C.cloneContents()],(w=p.parentNode).insertBefore(x[0],p),Dt.each(g,function(e){w.insertBefore(e,p)}),w.insertBefore(x[1],p),w.removeChild(p),Ws(g[g.length-1],h))},Gs=function(e,t){return!!$s(e,t)},Js=So.isText,Qs=So.isBogus,Zs=ci.nodeIndex,ec=function(e){var t=e.parentNode;return Qs(t)?ec(t):t},tc=function(e){return e?Tt.reduce(e.childNodes,function(e,t){return Qs(t)&&"BR"!==t.nodeName?e=e.concat(tc(t)):e.push(t),e},[]):[]},nc=function(t){return function(e){return t===e}},rc=function(e){var t,r,n,o;return(Js(e)?"text()":e.nodeName.toLowerCase())+"["+(r=tc(ec(t=e)),n=Tt.findIndex(r,nc(t),t),r=r.slice(0,n+1),o=Tt.reduce(r,function(e,t,n){return Js(t)&&Js(r[n-1])&&e++,e},0),r=Tt.filter(r,So.matchNodeNames(t.nodeName)),(n=Tt.findIndex(r,nc(t),t))-o)+"]"},oc=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),Js(n)?o=function(e,t){for(;(e=e.previousSibling)&&Js(e);)t+=e.data.length;return t}(n,r):(r>=(i=n.childNodes).length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(rc(n)),a=function(e,t,n){var r=[];for(t=t.parentNode;!(t===e||n&&n(t));t=t.parentNode)r.push(t);return r}(e,n),a=Tt.filter(a,oa.negate(So.isBogus)),(u=u.concat(Tt.map(a,function(e){return rc(e)}))).reverse().join("/")+","+o},ic=function(e,t){var n,r,o;return t?(t=(n=t.split(","))[0].split("/"),o=1<n.length?n[1]:"before",(r=Tt.reduce(t,function(e,t){return(t=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t))?("text()"===t[1]&&(t[1]="#text"),n=e,r=t[1],o=parseInt(t[2],10),i=tc(n),i=Tt.filter(i,function(e,t){return!Js(e)||!Js(i[t-1])}),(i=Tt.filter(i,So.matchNodeNames(r)))[o]):null;var n,r,o,i},e))?Js(r)?function(e,t){for(var n,r=e,o=0;Js(r);){if(n=r.data.length,o<=t&&t<=o+n){e=r,t-=o;break}if(!Js(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return Js(e)&&t>e.data.length&&(t=e.data.length),Ta(e,t)}(r,parseInt(o,10)):(o="after"===o?Zs(r)+1:Zs(r),Ta(r.parentNode,o)):null):null},ac=So.isContentEditableFalse,uc=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],s=[],c=0,l=e.getRoot();for(So.isText(a)?s.push(n?function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&So.isText(r);r=r.previousSibling)o+=e(r.data).length;return o}(t,a,u):u):(u>=(i=a.childNodes).length&&i.length&&(c=1,u=Math.max(0,i.length-1)),s.push(e.nodeIndex(i[u],n)+c));a&&a!==l;a=a.parentNode)s.push(e.nodeIndex(a,n));return s},sc=function(e){So.isText(e)&&0===e.data.length&&e.parentNode.removeChild(e)},cc=function(e,t,n){var r=0;return Dt.each(e.select(t),function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++}),r},lc=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],So.isElement(n)&&"TR"===n.nodeName&&(n=(o=n.childNodes)[Math.min(t?r:r-1,o.length-1)])&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r))},fc=function(e){return lc(e,!0),lc(e,!1),e},dc=function(e,t){var n;if(So.isElement(e)&&(e=Zi(e,t),ac(e)))return e;if(Ti(e)){if(So.isText(e)&&Si(e)&&(e=e.parentNode),n=e.previousSibling,ac(n))return n;if(n=e.nextSibling,ac(n))return n}},mc=function(e,t,n){var r,o,i,a,u,s,c,l=n.getNode(),f=l?l.nodeName:null,d=n.getRng();return ac(l)||"IMG"===f?{name:f,index:cc(n.dom,f,l)}:(l=dc((r=d).startContainer,r.startOffset)||dc(r.endContainer,r.endOffset))?{name:f=l.tagName,index:cc(n.dom,f,l)}:(o=e,a=t,u=d,s=(i=n).dom,(c={}).start=uc(s,o,a,u,!0),i.isCollapsed()||(c.end=uc(s,o,a,u,!1)),c)},pc={getBookmark:function(e,t,n){return 2===t?mc(wi,n,e):3===t?(o=(r=e).getRng(),{start:oc(r.dom.getRoot(),Ta.fromRangeStart(o)),end:oc(r.dom.getRoot(),Ta.fromRangeEnd(o))}):t?{rng:e.getRng()}:function(e){var t=e.dom,n=e.getRng(),r=t.uniqueId(),o=e.isCollapsed(),i="overflow:hidden;line-height:0px",a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:cc(t,u,a)};var s=fc(n.cloneRange());if(!o){s.collapse(!1);var c=t.create("span",{"data-mce-type":"bookmark",id:r+"_end",style:i},"&#xFEFF;");s.insertNode(c),sc(c.nextSibling)}(n=fc(n)).collapse(!0);var l=t.create("span",{"data-mce-type":"bookmark",id:r+"_start",style:i},"&#xFEFF;");return n.insertNode(l),sc(l.previousSibling),e.moveToBookmark({id:r,keep:1}),{id:r}}(e);var r,o},getUndoBookmark:V.curry(mc,V.identity,!0)},gc="_mce_caret",hc=function(e){return So.isElement(e)&&e.id===gc},vc=function(e,t){for(;t&&t!==e;){if(t.id===gc)return t;t=t.parentNode}return null},yc=function(e,t){return!e.isBlock(t)||t.innerHTML||de.ie||(t.innerHTML='<br data-mce-bogus="1" />'),t},bc=function(e,t){return ja.lastPositionIn(e).fold(function(){return!1},function(e){return t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0})},Cc=function(e,t,n){return!(!1!==t.hasChildNodes()||!vc(e,t)||(o=n,i=(r=t).ownerDocument.createTextNode(xi),r.appendChild(i),o.setStart(i,0),o.setEnd(i,0),0));var r,o,i},xc=function(e,t,n,r){var o,i,a,u,s=n[t?"start":"end"],c=e.getRoot();if(s){for(a=s[0],i=c,o=s.length-1;1<=o;o--){if(u=i.childNodes,Cc(c,i,r))return!0;if(s[o]>u.length-1)return!!Cc(c,i,r)||bc(i,r);i=u[s[o]]}3===i.nodeType&&(a=Math.min(s[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(s[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},wc=function(e,t,n){var r,o,i,a,u,s,c=e.get(n.id+"_"+t),l=n.keep;if(c){if(r=c.parentNode,l?(r=c.firstChild,o=1):o=e.nodeIndex(c),u=r,s=o,!l){for(a=c.previousSibling,i=c.nextSibling,Dt.each(Dt.grep(c.childNodes),function(e){So.isText(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))});c=e.get(n.id+"_"+t);)e.remove(c,!0);a&&i&&a.nodeType===i.nodeType&&So.isText(a)&&!de.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,s=o)}return A.some(Ta(u,s))}return A.none()},Nc=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v=e.dom;if(t){if(Dt.isArray(t.start))return g=t,h=(p=v).createRng(),xc(p,!0,g,h)&&xc(p,!1,g,h)?A.some(h):A.none();if("string"==typeof t.start)return A.some((f=t,d=(l=v).createRng(),m=ic(l.getRoot(),f.start),d.setStart(m.container(),m.offset()),m=ic(l.getRoot(),f.end),d.setEnd(m.container(),m.offset()),d));if(t.id)return s=wc(o=v,"start",i=t),c=wc(o,"end",i),ia([s,(a=c,u=s,a.isSome()?a:u)],function(e,t){var n=o.createRng();return n.setStart(yc(o,e.container()),e.offset()),n.setEnd(yc(o,t.container()),t.offset()),n});if(t.name)return n=v,r=t,A.from(n.select(r.name)[r.index]).map(function(e){var t=n.createRng();return t.selectNode(e),t});if(t.rng)return A.some(t.rng)}return A.none()},Ec={getBookmark:function(e,t,n){return pc.getBookmark(e,t,n)},moveToBookmark:function(t,e){Nc(t,e).each(function(e){t.setRng(e)})},isBookmarkNode:function(e){return So.isElement(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")}},Sc=Dt.each,kc=function(o){this.compare=function(e,t){if(e.nodeName!==t.nodeName)return!1;var n=function(n){var r={};return Sc(o.getAttribs(n),function(e){var t=e.nodeName.toLowerCase();0!==t.indexOf("_")&&"style"!==t&&0!==t.indexOf("data-")&&(r[t]=o.getAttrib(n,t))}),r},r=function(e,t){var n,r;for(r in e)if(e.hasOwnProperty(r)){if(void 0===(n=t[r]))return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(t.hasOwnProperty(r))return!1;return!0};return!(!r(n(e),n(t))||!r(o.parseStyle(o.getAttrib(e,"style")),o.parseStyle(o.getAttrib(t,"style")))||Ec.isBookmarkNode(e)||Ec.isBookmarkNode(t))}},Tc=function(t,e){H.each(e,function(e){Lu.before(t,e)})},Ac=function(t,e){H.each(e,function(e){Lu.append(t,e)})},Rc=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},_c={empty:function(e){e.dom().textContent="",H.each(Fr.children(e),function(e){Rc(e)})},remove:Rc,unwrap:function(e){var t=Fr.children(e);0<t.length&&Tc(e,t),Rc(e)}},Bc=(ks=Yn.isText,Ts="text",As=function(e){return ks(e)?A.from(e.dom().nodeValue):A.none()},Rs=In.detect().browser,{get:function(e){if(!ks(e))throw new Error("Can only get "+Ts+" value of a "+Ts+" node");return _s(e).getOr("")},getOption:_s=Rs.isIE()&&10===Rs.version.major?function(e){try{return As(e)}catch(Vx){return A.none()}}:As,set:function(e,t){if(!ks(e))throw new Error("Can only set raw "+Ts+" value of a "+Ts+" node");e.dom().nodeValue=t}}),Dc=function(e){return Bc.get(e)},Oc=function(e){var t=su(e,"br"),n=H.filter(function(e){for(var t=[],n=e.dom();n;)t.push(Fn.fromDom(n)),n=n.lastChild;return t}(e).slice(-1),uo);t.length===n.length&&H.each(n,_c.remove)},Pc=function(e){_c.empty(e),Lu.append(e,Fn.fromHtml('<br data-mce-bogus="1">'))},Lc=function(n){Fr.lastChild(n).each(function(t){Fr.prevSibling(t).each(function(e){io(n)&&uo(t)&&io(e)&&_c.remove(t)})})},Ic=Dt.makeMap;function Mc(e){var u,s,c,l,f,d=[];return u=(e=e||{}).indent,s=Ic(e.indent_before||""),c=Ic(e.indent_after||""),l=zo.getEncodeFunc(e.entity_encoding||"raw",e.entities),f="html"===e.element_format,{start:function(e,t,n){var r,o,i,a;if(u&&s[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n"),d.push("<",e),t)for(r=0,o=t.length;r<o;r++)i=t[r],d.push(" ",i.name,'="',l(i.value,!0),'"');d[d.length]=!n||f?">":" />",n&&u&&c[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n")},end:function(e){var t;d.push("</",e,">"),u&&c[e]&&0<d.length&&0<(t=d[d.length-1]).length&&"\n"!==t&&d.push("\n")},text:function(e,t){0<e.length&&(d[d.length]=t?e:l(e))},cdata:function(e){d.push("<![CDATA[",e,"]]>")},comment:function(e){d.push("\x3c!--",e,"--\x3e")},pi:function(e,t){t?d.push("<?",e," ",l(t),"?>"):d.push("<?",e,"?>"),u&&d.push("\n")},doctype:function(e){d.push("<!DOCTYPE",e,">",u?"\n":"")},reset:function(){d.length=0},getContent:function(){return d.join("").replace(/\n$/,"")}}}function Fc(t,p){void 0===p&&(p=Go());var g=Mc(t);return(t=t||{}).validate=!("validate"in t)||t.validate,{serialize:function(e){var f,d;d=t.validate,f={3:function(e){g.text(e.value,e.raw)},8:function(e){g.comment(e.value)},7:function(e){g.pi(e.name,e.value)},10:function(e){g.doctype(e.value)},4:function(e){g.cdata(e.value)},11:function(e){if(e=e.firstChild)for(;m(e),e=e.next;);}},g.reset();var m=function(e){var t,n,r,o,i,a,u,s,c,l=f[e.type];if(l)l(e);else{if(t=e.name,n=e.shortEnded,r=e.attributes,d&&r&&1<r.length&&((a=[]).map={},c=p.getElementRule(e.name))){for(u=0,s=c.attributesOrder.length;u<s;u++)(o=c.attributesOrder[u])in r.map&&(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));for(u=0,s=r.length;u<s;u++)(o=r[u].name)in a.map||(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));r=a}if(g.start(e.name,r,n),!n){if(e=e.firstChild)for(;m(e),e=e.next;);g.end(t)}}};return 1!==e.type||t.inner?f[11](e):m(e),g.getContent()}}}var zc=function(a){var u=Ta.fromRangeStart(a),s=Ta.fromRangeEnd(a),c=a.commonAncestorContainer;return ja.fromPosition(!1,c,s).map(function(e){return!fs(u,s,c)&&fs(u,e,c)?(t=u.container(),n=u.offset(),r=e.container(),o=e.offset(),(i=document.createRange()).setStart(t,n),i.setEnd(r,o),i):a;var t,n,r,o,i}).getOr(a)},Uc=function(e){return e.collapsed?e:zc(e)},qc=So.matchNodeNames("td th"),Vc=function(o,e,t){var n,r,i,a,u,s,c,l,f,d,m,p,g=o.schema.getTextInlineElements(),h=o.selection,v=o.dom;if(/^ | $/.test(e)&&(e=function(e){var t,n,r;t=h.getRng(),n=t.startContainer,r=t.startOffset;var o=function(e){return n[e]&&3===n[e].nodeType};return 3===n.nodeType&&(0<r?e=e.replace(/^&nbsp;/," "):o("previousSibling")||(e=e.replace(/^ /,"&nbsp;")),r<n.length?e=e.replace(/&nbsp;(<br>|)$/," "):o("nextSibling")||(e=e.replace(/(&nbsp;| )(<br>|)$/,"&nbsp;"))),e}(e)),n=o.parser,p=t.merge,r=Fc({validate:o.settings.validate},o.schema),m='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;&#x200B;</span>',s={content:e,format:"html",selection:!0,paste:t.paste},(s=o.fire("BeforeSetContent",s)).isDefaultPrevented())o.fire("SetContent",{content:s.content,format:"html",selection:!0,paste:t.paste});else{-1===(e=s.content).indexOf("{$caret}")&&(e+="{$caret}"),e=e.replace(/\{\$caret\}/,m);var y,b,C,x,w=(l=h.getRng()).startContainer||(l.parentElement?l.parentElement():null),N=o.getBody();w===N&&h.isCollapsed()&&v.isBlock(N.firstChild)&&(y=N.firstChild)&&!o.schema.getShortEndedElements()[y.nodeName]&&v.isEmpty(N.firstChild)&&((l=v.createRng()).setStart(N.firstChild,0),l.setEnd(N.firstChild,0),h.setRng(l)),h.isCollapsed()||(o.selection.setRng(Uc(o.selection.getRng())),o.getDoc().execCommand("Delete",!1,null),C=(b=h.getRng()).startContainer,x=b.startOffset,3===C.nodeType&&b.collapsed&&("\xa0"===C.data[x]?(C.deleteData(x,1),/[\u00a0| ]$/.test(e)||(e+=" ")):"\xa0"===C.data[x-1]&&(C.deleteData(x-1,1),/[\u00a0| ]$/.test(e)||(e=" "+e))));var E,S,k,T={context:(i=h.getNode()).nodeName.toLowerCase(),data:t.data,insert:!0};if(u=n.parse(e,T),!0===t.paste&&Xs(o.schema,u)&&Gs(v,i))return l=Ys(r,v,o.selection.getRng(),u),o.selection.setRng(l),void o.fire("SetContent",s);if(function(e){for(var t=e;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")}(u),"mce_marker"===(f=u.lastChild).attr("id"))for(f=(c=f).prev;f;f=f.walk(!0))if(3===f.type||!v.isBlock(f.name)){o.schema.isValidChild(f.parent.name,"span")&&f.parent.insert(c,f,"br"===f.name);break}if(o._selectionOverrides.showBlockCaretContainer(i),T.invalid){for(h.setContent(m),i=h.getNode(),a=o.getBody(),9===i.nodeType?i=f=a:f=i;f!==a;)f=(i=f).parentNode;e=i===a?a.innerHTML:v.getOuterHTML(i),e=r.serialize(n.parse(e.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,function(){return r.serialize(u)}))),i===a?v.setHTML(a,e):v.setOuterHTML(i,e)}else e=r.serialize(u),function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t)}}(o,e,i);!function(){if(p){var n=o.getBody(),r=new kc(v);Dt.each(v.select("*[data-mce-fragment]"),function(e){for(var t=e.parentNode;t&&t!==n;t=t.parentNode)g[e.nodeName.toLowerCase()]&&r.compare(t,e)&&v.remove(e,!0)})}}(),function(e){var t,n,r;if(e){if(h.scrollIntoView(e),t=function(e){for(var t=o.getBody();e&&e!==t;e=e.parentNode)if("false"===o.dom.getContentEditable(e))return e;return null}(e))return v.remove(e),h.select(t);l=v.createRng(),(f=e.previousSibling)&&3===f.nodeType?(l.setStart(f,f.nodeValue.length),de.ie||(d=e.nextSibling)&&3===d.nodeType&&(f.appendData(d.data),d.parentNode.removeChild(d))):(l.setStartBefore(e),l.setEndBefore(e)),n=v.getParent(e,v.isBlock),v.remove(e),n&&v.isEmpty(n)&&(o.$(n).empty(),l.setStart(n,0),l.setEnd(n,0),qc(n)||n.getAttribute("data-mce-fragment")||!(r=function(e){var t=Ta.fromRangeStart(e);if(t=qs(o.getBody()).next(t))return t.toRange()}(l))?v.add(n,v.create("br",{"data-mce-bogus":"1"})):(l=r,v.remove(n))),h.setRng(l)}}(v.get("mce_marker")),E=o.getBody(),Dt.each(E.getElementsByTagName("*"),function(e){e.removeAttribute("data-mce-fragment")}),S=o.dom,k=o.selection.getStart(),A.from(S.getParent(k,"td,th")).map(Fn.fromDom).each(Lc),o.fire("SetContent",s),o.addVisual()}},Hc={insertAtCaret:function(e,t){var n,r,o="string"!=typeof(n=t)?(r=Dt.extend({paste:n.paste,data:{paste:n.paste}},n),{content:n.content,details:r}):{content:n,details:{}};Vc(e,o.content,o.details)}};function jc(e,t,n,r,o){return e(n,r)?A.some(n):Jn.isFunction(o)&&o(n)?A.none():t(n,r,o)}var $c=function(e,t,n){for(var r=e.dom(),o=Jn.isFunction(n)?n:V.constant(!1);r.parentNode;){r=r.parentNode;var i=Fn.fromDom(r);if(t(i))return A.some(i);if(o(i))break}return A.none()},Wc=function(e,t){return H.find(e.dom().childNodes,V.compose(t,Fn.fromDom)).map(Fn.fromDom)},Kc=function(e,r){var o=function(e){for(var t=0;t<e.childNodes.length;t++){if(r(Fn.fromDom(e.childNodes[t])))return A.some(Fn.fromDom(e.childNodes[t]));var n=o(e.childNodes[t]);if(n.isSome())return n}return A.none()};return o(e.dom())},Xc={first:function(e){return Kc(fr.body(),e)},ancestor:$c,closest:function(e,t,n){return jc(function(e){return t(e)},$c,e,t,n)},sibling:function(t,n){var e=t.dom();return e.parentNode?Wc(Fn.fromDom(e.parentNode),function(e){return!_r.eq(t,e)&&n(e)}):A.none()},child:Wc,descendant:Kc},Yc=br.immutable("sections","settings"),Gc=In.detect().deviceType.isTouch(),Jc=["lists","autolink","autosave"],Qc={theme:"mobile"},Zc=function(e){var t=Jn.isArray(e)?e.join(" "):e,n=H.map(Jn.isString(t)?t.split(" "):[],Rn);return H.filter(n,function(e){return 0<e.length})},el=function(e,t){return e.sections().hasOwnProperty(t)},tl=function(e,t,n,r){var o,i,a=Zc(n.forced_plugins),u=Zc(r.plugins),s=e&&el(t,"mobile")?(o=u,H.filter(o,V.curry(H.contains,Jc))):u,c=(i=s,[].concat(Zc(a)).concat(Zc(i)));return Dt.extend(r,{plugins:c.join(" ")})},nl=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,p,g,h,v=(o=["mobile"],i=r,a=rr.bifilter(i,function(e,t){return H.contains(o,t)}),Yc(a.t,a.f)),y=Dt.extend(t,n,v.settings(),(p=e,h=(g=v).settings().inline,p&&el(g,"mobile")&&!h?(l="mobile",f=Qc,d=v.sections(),m=d.hasOwnProperty(l)?d[l]:{},Dt.extend({},f,m)):{}),{validate:!0,content_editable:v.settings().inline,external_plugins:(u=n,s=v.settings(),c=s.external_plugins?s.external_plugins:{},u&&u.external_plugins?Dt.extend({},u.external_plugins,c):c)});return tl(e,v,n,y)},rl=function(e,t,n){return A.from(t.settings[n]).filter(e)},ol=V.curry(rl,Jn.isString),il=function(e,t,n,r){var o,i,a=t in e.settings?e.settings[t]:n;return"hash"===r?(i={},"string"==typeof(o=a)?H.each(0<o.indexOf("=")?o.split(/[;,](?![^=;,]*(?:[;,]|$))/):o.split(","),function(e){1<(e=e.split("=")).length?i[Dt.trim(e[0])]=Dt.trim(e[1]):i[Dt.trim(e[0])]=Dt.trim(e)}):i=o,i):"string"===r?rl(Jn.isString,e,t).getOr(n):"number"===r?rl(Jn.isNumber,e,t).getOr(n):"boolean"===r?rl(Jn.isBoolean,e,t).getOr(n):"object"===r?rl(Jn.isObject,e,t).getOr(n):"array"===r?rl(Jn.isArray,e,t).getOr(n):"function"===r?rl(Jn.isFunction,e,t).getOr(n):a},al=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,ul=function(e,t){var n=t.container(),r=t.offset();return e?ki(n)?So.isText(n.nextSibling)?Ta(n.nextSibling,0):Ta.after(n):Ri(t)?Ta(n,r+1):t:ki(n)?So.isText(n.previousSibling)?Ta(n.previousSibling,n.previousSibling.data.length):Ta.before(n):_i(t)?Ta(n,r-1):t},sl={isInlineTarget:function(e,t){var n=ol(e,"inline_boundaries_selector").getOr("a[href],code");return Tr.is(Fn.fromDom(t),n)},findRootInline:function(e,t,n){var r,o,i,a=(r=e,o=t,i=n,H.filter(ci.DOM.getParents(i.container(),"*",o),r));return A.from(a[a.length-1])},isRtl:function(e){return"rtl"===ci.DOM.getStyle(e,"direction",!0)||(t=e.textContent,al.test(t));var t},isAtZwsp:function(e){return Ri(e)||_i(e)},normalizePosition:ul,normalizeForwards:V.curry(ul,!0),normalizeBackwards:V.curry(ul,!1),hasSameParentBlock:function(e,t,n){var r=ls(t,e),o=ls(n,e);return r&&r===o}},cl=function(e,t){return _r.contains(e,t)?Xc.closest(t,function(e){return so(e)||lo(e)},(n=e,function(e){return _r.eq(n,Fn.fromDom(e.dom().parentNode))})):A.none();var n},ll=function(e){var t,n,r;e.dom.isEmpty(e.getBody())&&(e.setContent(""),n=(t=e).getBody(),r=n.firstChild&&t.dom.isBlock(n.firstChild)?n.firstChild:n,t.selection.setCursorLocation(r,0))},fl=function(i,a,u){return ia([ja.firstPositionIn(u),ja.lastPositionIn(u)],function(e,t){var n=sl.normalizePosition(!0,e),r=sl.normalizePosition(!1,t),o=sl.normalizePosition(!1,a);return i?ja.nextPosition(u,o).map(function(e){return e.isEqual(r)&&a.isEqual(n)}).getOr(!1):ja.prevPosition(u,o).map(function(e){return e.isEqual(n)&&a.isEqual(r)}).getOr(!1)}).getOr(!0)},dl=function(e,t,n){return Xc.ancestor(e,function(e){return Tr.is(e,t)},n)},ml=dl,pl=function(e,t){return Tr.one(t,e)},gl=function(e,t,n){return jc(Tr.is,dl,e,t,n)},hl=function(e,t,n){return ml(e,t,n).isSome()},vl=function(e,t){return So.isText(t)&&/^[ \t\r\n]*$/.test(t.data)&&!1===(n=e,r=t,o=Fn.fromDom(n),i=Fn.fromDom(r),hl(i,"pre,code",V.curry(_r.eq,o)));var n,r,o,i},yl=function(e,t){return Hi(t)&&!1===vl(e,t)||(n=t,So.isElement(n)&&"A"===n.nodeName&&n.hasAttribute("name"))||bl(t);var n},bl=So.hasAttribute("data-mce-bookmark"),Cl=So.hasAttribute("data-mce-bogus"),xl=So.hasAttributeValue("data-mce-bogus","all"),wl=function(e){return function(e){var t,n,r=0;if(yl(e,e))return!1;if(!(n=e.firstChild))return!0;t=new Zr(n,e);do{if(xl(n))n=t.next(!0);else if(Cl(n))n=t.next();else if(So.isBr(n))r++,n=t.next();else{if(yl(e,n))return!1;n=t.next()}}while(n);return r<=1}(e.dom())},Nl=br.immutable("block","position"),El=br.immutable("from","to"),Sl=function(e,t){var n=Fn.fromDom(e),r=Fn.fromDom(t.container());return cl(n,r).map(function(e){return Nl(e,t)})},kl=function(o,i,e){var t=Sl(o,Ta.fromRangeStart(e)),n=t.bind(function(e){return ja.fromPosition(i,o,e.position()).bind(function(e){return Sl(o,e).map(function(e){return t=o,n=i,r=e,So.isBr(r.position().getNode())&&!1===wl(r.block())?ja.positionIn(!1,r.block().dom()).bind(function(e){return e.isEqual(r.position())?ja.fromPosition(n,t,e).bind(function(e){return Sl(t,e)}):A.some(r)}).getOr(r):r;var t,n,r})})});return ia([t,n],El).filter(function(e){return r=e,!1===_r.eq(r.from().block(),r.to().block())&&(n=e,Fr.parent(n.from().block()).bind(function(t){return Fr.parent(n.to().block()).filter(function(e){return _r.eq(t,e)})}).isSome())&&(t=e,!1===So.isContentEditableFalse(t.from().block())&&!1===So.isContentEditableFalse(t.to().block()));var t,n,r})},Tl=function(e,t,n){return n.collapsed?kl(e,t,n):A.none()},Al=function(e,t,n){return _r.contains(t,e)?Fr.parents(e,function(e){return n(e)||_r.eq(e,t)}).slice(0,-1):[]},Rl=function(e,t){return Al(e,t,V.constant(!1))},_l=Rl,Bl=function(e,t){return[e].concat(Rl(e,t))},Dl=function(e){var t,n,r=(t=e,n=Fr.children(t),H.findIndex(n,io).fold(function(){return n},function(e){return n.slice(0,e)}));return H.each(r,function(e){_c.remove(e)}),r},Ol=function(e,t){ja.positionIn(e,t.dom()).each(function(e){var t=e.getNode();So.isBr(t)&&_c.remove(Fn.fromDom(t))})},Pl=function(e,t){var n=Bl(t,e);return H.find(n.reverse(),wl).each(_c.remove)},Ll=function(o,i){return _r.contains(i,o)?Fr.parent(o).bind(function(e){return _r.eq(e,i)?A.some(o):(t=i,n=o,r=Fr.parents(n,function(e){return _r.eq(e,t)}),A.from(r[r.length-2]));var t,n,r}):A.none()},Il=function(n,r,o){if(wl(o))return _c.remove(o),wl(r)&&Pc(r),ja.firstPositionIn(r.dom());Ol(!0,r),Ol(!1,o);var i=Dl(r);return Ll(r,o).fold(function(){Pl(n,r);var e=ja.lastPositionIn(o.dom());return H.each(i,function(e){Lu.append(o,e)}),e},function(t){var e=ja.prevPosition(o.dom(),Ta.before(t.dom()));return H.each(i,function(e){Lu.before(t,e)}),Pl(n,r),e})},Ml=function(e,t,n,r){return t?Il(e,r,n):Il(e,n,r)},Fl=function(t,n){var e,r=Fn.fromDom(t.getBody());return(e=Tl(r.dom(),n,t.selection.getRng()).bind(function(e){return Ml(r,n,e.from().block(),e.to().block())})).each(function(e){t.selection.setRng(e.toRange())}),e.isSome()},zl=function(e,t){var n=Fn.fromDom(t),r=V.curry(_r.eq,e);return Xc.ancestor(n,po,r).isSome()},Ul=function(e,t){var n,r,o=ja.prevPosition(e.dom(),Ta.fromRangeStart(t)).isNone(),i=ja.nextPosition(e.dom(),Ta.fromRangeEnd(t)).isNone();return!(zl(n=e,(r=t).startContainer)||zl(n,r.endContainer))&&o&&i},ql=function(e){var n,r,o,t,i=Fn.fromDom(e.getBody()),a=e.selection.getRng();return Ul(i,a)?((t=e).setContent(""),t.selection.setCursorLocation(),!0):(n=i,r=e.selection,o=r.getRng(),ia([cl(n,Fn.fromDom(o.startContainer)),cl(n,Fn.fromDom(o.endContainer))],function(e,t){return!1===_r.eq(e,t)&&(o.deleteContents(),Ml(n,!0,e,t).each(function(e){r.setRng(e.toRange())}),!0)}).getOr(!1))},Vl=function(e,t){return!e.selection.isCollapsed()&&ql(e)},Hl=function(a){if(!Jn.isArray(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return H.each(a,function(e,r){var t=rr.keys(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!Jn.isArray(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=rr.keys(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!H.forall(u,function(e){return H.contains(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n},jl=Hl([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),$l=function(e,t,n,r){var o=r.getNode(!1===t);return cl(Fn.fromDom(e),Fn.fromDom(n.getNode())).map(function(e){return wl(e)?jl.remove(e.dom()):jl.moveToElement(o)}).orThunk(function(){return A.some(jl.moveToElement(o))})},Wl=function(u,s,c){return ja.fromPosition(s,u,c).bind(function(e){return a=e.getNode(),po(Fn.fromDom(a))||lo(Fn.fromDom(a))?A.none():(t=u,o=e,i=function(e){return ao(Fn.fromDom(e))&&!fs(r,o,t)},Cs(!(n=s),r=c).fold(function(){return Cs(n,o).fold(V.constant(!1),i)},i)?A.none():s&&So.isContentEditableFalse(e.getNode())?$l(u,s,c,e):!1===s&&So.isContentEditableFalse(e.getNode(!0))?$l(u,s,c,e):s&&Ns(c)?A.some(jl.moveToPosition(e)):!1===s&&ws(c)?A.some(jl.moveToPosition(e)):A.none());var t,n,r,o,i,a})},Kl=function(r,e,o){return i=e,a=o.getNode(!1===i),u=i?"after":"before",So.isElement(a)&&a.getAttribute("data-mce-caret")===u?(t=e,n=o.getNode(!1===e),t&&So.isContentEditableFalse(n.nextSibling)?A.some(jl.moveToElement(n.nextSibling)):!1===t&&So.isContentEditableFalse(n.previousSibling)?A.some(jl.moveToElement(n.previousSibling)):A.none()).fold(function(){return Wl(r,e,o)},A.some):Wl(r,e,o).bind(function(e){return t=r,n=o,e.fold(function(e){return A.some(jl.remove(e))},function(e){return A.some(jl.moveToElement(e))},function(e){return fs(n,e,t)?A.none():A.some(jl.moveToPosition(e))});var t,n});var t,n,i,a,u},Xl=function(e,t){return r=e,o=(n=t).container(),i=n.offset(),!1===Ta.isTextPosition(n)&&o===r.parentNode&&i>Ta.before(r).offset()?Ta(t.container(),t.offset()-1):t;var n,r,o,i},Yl=function(e){return Hi(e.previousSibling)?A.some((t=e.previousSibling,So.isText(t)?Ta(t,t.data.length):Ta.after(t))):e.previousSibling?ja.lastPositionIn(e.previousSibling):A.none();var t},Gl=function(e){return Hi(e.nextSibling)?A.some((t=e.nextSibling,So.isText(t)?Ta(t,0):Ta.before(t))):e.nextSibling?ja.firstPositionIn(e.nextSibling):A.none();var t},Jl=function(r,o){return Yl(o).orThunk(function(){return Gl(o)}).orThunk(function(){return e=r,t=o,n=Ta.before(t.previousSibling?t.previousSibling:t.parentNode),ja.prevPosition(e,n).fold(function(){return ja.nextPosition(e,Ta.after(t))},A.some);var e,t,n})},Ql=function(n,r){return Gl(r).orThunk(function(){return Yl(r)}).orThunk(function(){return e=n,t=r,ja.nextPosition(e,Ta.after(t)).fold(function(){return ja.prevPosition(e,Ta.before(t))},A.some);var e,t})},Zl=function(e,t,n){return(r=e,o=t,i=n,r?Ql(o,i):Jl(o,i)).map(V.curry(Xl,n));var r,o,i},ef=function(t,n,e){e.fold(function(){t.focus()},function(e){t.selection.setRng(e.toRange(),n)})},tf=function(e,t){return t&&e.schema.getBlockElements().hasOwnProperty(Yn.name(t))},nf=function(e){if(wl(e)){var t=Fn.fromHtml('<br data-mce-bogus="1">');return _c.empty(e),Lu.append(e,t),A.some(Ta.before(t.dom()))}return A.none()},rf=function(t,n,e){var r,a,o,i=Zl(n,t.getBody(),e.dom()),u=Xc.ancestor(e,V.curry(tf,t),(r=t.getBody(),function(e){return e.dom()===r})),s=(a=e,o=i,ia([Fr.prevSibling(a),Fr.nextSibling(a),o],function(e,t,n){var r,o=e.dom(),i=t.dom();return So.isText(o)&&So.isText(i)?(r=o.data.length,o.appendData(i.data),_c.remove(t),_c.remove(a),n.container()===i?Ta(o,r):n):(_c.remove(a),n)}).orThunk(function(){return _c.remove(a),o}));t.dom.isEmpty(t.getBody())?(t.setContent(""),t.selection.setCursorLocation()):u.bind(nf).fold(function(){ef(t,n,s)},function(e){ef(t,n,A.some(e))})},of=function(a,u){var e,t,n,r,o;return(e=a.getBody(),t=u,n=a.selection.getRng(),r=vs(t?1:-1,e,n),o=Ta.fromRangeStart(r),!1===t&&Ns(o)?A.some(jl.remove(o.getNode(!0))):t&&ws(o)?A.some(jl.remove(o.getNode())):Kl(e,t,o)).map(function(e){return e.fold((o=a,i=u,function(e){return o._selectionOverrides.hideFakeCaret(),rf(o,i,Fn.fromDom(e)),!0}),(n=a,r=u,function(e){var t=r?Ta.before(e):Ta.after(e);return n.selection.setRng(t.toRange()),!0}),(t=a,function(e){return t.selection.setRng(e.toRange()),!0}));var t,n,r,o,i}).getOr(!1)},af=function(e,t){var n,r=e.selection.getNode();return!!So.isContentEditableFalse(r)&&(n=Fn.fromDom(e.getBody()),H.each(su(n,".mce-offscreen-selection"),_c.remove),rf(e,t,Fn.fromDom(e.selection.getNode())),ll(e),!0)},uf=function(e,t){return e.selection.isCollapsed()?of(e,t):af(e,t)},sf=function(e){var t,n=function(e,t){for(;t&&t!==e;){if(So.isContentEditableTrue(t)||So.isContentEditableFalse(t))return t;t=t.parentNode}return null}(e.getBody(),e.selection.getNode());return So.isContentEditableTrue(n)&&e.dom.isBlock(n)&&e.dom.isEmpty(n)&&(t=e.dom.create("br",{"data-mce-bogus":"1"}),e.dom.setHTML(n,""),n.appendChild(t),e.selection.setRng(Ta.before(t).toRange())),!0},cf=So.isText,lf=function(e){return cf(e)&&e.data[0]===xi},ff=function(e){return cf(e)&&e.data[e.data.length-1]===xi},df=function(e){return e.ownerDocument.createTextNode(xi)},mf=function(e,t){return e?function(e){if(cf(e.previousSibling))return ff(e.previousSibling)||e.previousSibling.appendData(xi),e.previousSibling;if(cf(e))return lf(e)||e.insertData(0,xi),e;var t=df(e);return e.parentNode.insertBefore(t,e),t}(t):function(e){if(cf(e.nextSibling))return lf(e.nextSibling)||e.nextSibling.insertData(0,xi),e.nextSibling;if(cf(e))return ff(e)||e.appendData(xi),e;var t=df(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}(t)},pf=V.curry(mf,!0),gf=V.curry(mf,!1),hf=function(e,t){return So.isText(e.container())?mf(t,e.container()):mf(t,e.getNode())},vf=function(e,t){var n=t.get();return n&&e.container()===n&&ki(n)},yf=function(n,e){return e.fold(function(e){Ma.remove(n.get());var t=pf(e);return n.set(t),A.some(Ta(t,t.length-1))},function(e){return ja.firstPositionIn(e).map(function(e){if(vf(e,n))return Ta(n.get(),1);Ma.remove(n.get());var t=hf(e,!0);return n.set(t),Ta(t,1)})},function(e){return ja.lastPositionIn(e).map(function(e){if(vf(e,n))return Ta(n.get(),n.get().length-1);Ma.remove(n.get());var t=hf(e,!1);return n.set(t),Ta(t,t.length-1)})},function(e){Ma.remove(n.get());var t=gf(e);return n.set(t),A.some(Ta(t,1))})},bf=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return A.none()},Cf=Hl([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),xf=function(e,t){var n=ls(t,e);return n||e},wf=function(e,t,n){var r=sl.normalizeForwards(n),o=xf(t,r.container());return sl.findRootInline(e,o,r).fold(function(){return ja.nextPosition(o,r).bind(V.curry(sl.findRootInline,e,o)).map(function(e){return Cf.before(e)})},A.none)},Nf=function(e,t){return null===vc(e,t)},Ef=function(e,t,n){return sl.findRootInline(e,t,n).filter(V.curry(Nf,t))},Sf=function(e,t,n){var r=sl.normalizeBackwards(n);return Ef(e,t,r).bind(function(e){return ja.prevPosition(e,r).isNone()?A.some(Cf.start(e)):A.none()})},kf=function(e,t,n){var r=sl.normalizeForwards(n);return Ef(e,t,r).bind(function(e){return ja.nextPosition(e,r).isNone()?A.some(Cf.end(e)):A.none()})},Tf=function(e,t,n){var r=sl.normalizeBackwards(n),o=xf(t,r.container());return sl.findRootInline(e,o,r).fold(function(){return ja.prevPosition(o,r).bind(V.curry(sl.findRootInline,e,o)).map(function(e){return Cf.after(e)})},A.none)},Af=function(e){return!1===sl.isRtl(_f(e))},Rf=function(e,t,n){return bf([wf,Sf,kf,Tf],[e,t,n]).filter(Af)},_f=function(e){return e.fold(V.identity,V.identity,V.identity,V.identity)},Bf=function(e){return e.fold(V.constant("before"),V.constant("start"),V.constant("end"),V.constant("after"))},Df=function(e){return e.fold(Cf.before,Cf.before,Cf.after,Cf.after)},Of=function(n,e,r,t,o,i){return ia([sl.findRootInline(e,r,t),sl.findRootInline(e,r,o)],function(e,t){return e!==t&&sl.hasSameParentBlock(r,e,t)?Cf.after(n?e:t):i}).getOr(i)},Pf=function(e,r){return e.fold(V.constant(!0),function(e){return n=r,!(Bf(t=e)===Bf(n)&&_f(t)===_f(n));var t,n})},Lf=function(e,t){return e?t.fold(V.compose(A.some,Cf.start),A.none,V.compose(A.some,Cf.after),A.none):t.fold(A.none,V.compose(A.some,Cf.before),A.none,V.compose(A.some,Cf.end))},If=function(a,u,s,c){var e=sl.normalizePosition(a,c),l=Rf(u,s,e);return Rf(u,s,e).bind(V.curry(Lf,a)).orThunk(function(){return t=a,n=u,r=s,o=l,e=c,i=sl.normalizePosition(t,e),ja.fromPosition(t,r,i).map(V.curry(sl.normalizePosition,t)).fold(function(){return o.map(Df)},function(e){return Rf(n,r,e).map(V.curry(Of,t,n,r,i,e)).filter(V.curry(Pf,o))}).filter(Af);var t,n,r,o,e,i})},Mf=Rf,Ff=If,zf=(V.curry(If,!1),V.curry(If,!0),Df),Uf=function(e){return e.fold(Cf.start,Cf.start,Cf.end,Cf.end)},qf=function(e){return Jn.isFunction(e.selection.getSel().modify)},Vf=function(e,t,n){var r=e?1:-1;return t.setRng(Ta(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},Hf=function(e,t){var n=t.selection.getRng(),r=e?Ta.fromRangeEnd(n):Ta.fromRangeStart(n);return!!qf(t)&&(e&&Ri(r)?Vf(!0,t.selection,r):!(e||!_i(r))&&Vf(!1,t.selection,r))},jf=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},$f=function(e){return!1!==e.settings.inline_boundaries},Wf=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Kf=function(t,e,n){return yf(e,n).map(function(e){return jf(t,e),n})},Xf=function(e,t,n){return function(){return!!$f(t)&&Hf(e,t)}},Yf={move:function(a,u,s){return function(){return!!$f(a)&&(t=a,n=u,e=s,r=t.getBody(),o=Ta.fromRangeStart(t.selection.getRng()),i=V.curry(sl.isInlineTarget,t),Ff(e,i,r,o).bind(function(e){return Kf(t,n,e)})).isSome();var t,n,e,r,o,i}},moveNextWord:V.curry(Xf,!0),movePrevWord:V.curry(Xf,!1),setupSelectedState:function(a){var u=Xu(null),s=V.curry(sl.isInlineTarget,a);return a.on("NodeChange",function(e){var t,n,r,o,i;$f(a)&&(t=s,n=a.dom,r=e.parents,o=H.filter(n.select('*[data-mce-selected="inline-boundary"]'),t),i=H.filter(r,t),H.each(H.difference(o,i),V.curry(Wf,!1)),H.each(H.difference(i,o),V.curry(Wf,!0)),function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=Ta.fromRangeStart(e.selection.getRng());Ta.isTextPosition(n)&&!1===sl.isAtZwsp(n)&&(jf(e,Ma.removeAndReposition(t.get(),n)),t.set(null))}}(a,u),function(n,r,o,e){if(r.selection.isCollapsed()){var t=H.filter(e,n);H.each(t,function(e){var t=Ta.fromRangeStart(r.selection.getRng());Mf(n,r.getBody(),t).bind(function(e){return Kf(r,o,e)})})}}(s,a,u,e.parents))}),u},setCaretPosition:jf},Gf=function(t,n){return function(e){return yf(n,e).map(function(e){return Yf.setCaretPosition(t,e),!0}).getOr(!1)}},Jf=function(r,o,i,a){var u=r.getBody(),s=V.curry(sl.isInlineTarget,r);r.undoManager.ignore(function(){var e,t,n;r.selection.setRng((e=i,t=a,(n=document.createRange()).setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n)),r.execCommand("Delete"),Mf(s,u,Ta.fromRangeStart(r.selection.getRng())).map(Uf).map(Gf(r,o))}),r.nodeChanged()},Qf=function(n,r,i,o){var e,t,a=(e=n.getBody(),t=o.container(),ls(t,e)||e),u=V.curry(sl.isInlineTarget,n),s=Mf(u,a,o);return s.bind(function(e){return i?e.fold(V.constant(A.some(Uf(e))),A.none,V.constant(A.some(zf(e))),A.none):e.fold(A.none,V.constant(A.some(zf(e))),A.none,V.constant(A.some(Uf(e))))}).map(Gf(n,r)).getOrThunk(function(){var t=ja.navigate(i,a,o),e=t.bind(function(e){return Mf(u,a,e)});return s.isSome()&&e.isSome()?sl.findRootInline(u,a,o).map(function(e){return o=e,!!ia([ja.firstPositionIn(o),ja.lastPositionIn(o)],function(e,t){var n=sl.normalizePosition(!0,e),r=sl.normalizePosition(!1,t);return ja.nextPosition(o,n).map(function(e){return e.isEqual(r)}).getOr(!0)}).getOr(!0)&&(rf(n,i,Fn.fromDom(e)),!0);var o}).getOr(!1):e.bind(function(e){return t.map(function(e){return i?Jf(n,r,o,e):Jf(n,r,e,o),!0})}).getOr(!1)})},Zf=function(e,t,n){if(e.selection.isCollapsed()&&!1!==e.settings.inline_boundaries){var r=Ta.fromRangeStart(e.selection.getRng());return Qf(e,t,n,r)}return!1},ed=br.immutable("start","end"),td=br.immutable("rng","table","cells"),nd=Hl([{removeTable:["element"]},{emptyCells:["cells"]}]),rd=function(e,t){return gl(Fn.fromDom(e),"td,th",t)},od=function(e,t){return ml(e,"table",t)},id=function(e){return!1===_r.eq(e.start(),e.end())},ad=function(e,n){return od(e.start(),n).bind(function(t){return od(e.end(),n).bind(function(e){return _r.eq(t,e)?A.some(t):A.none()})})},ud=function(e){return su(e,"td,th")},sd=function(r,e){var t=rd(e.startContainer,r),n=rd(e.endContainer,r);return e.collapsed?A.none():ia([t,n],ed).fold(function(){return t.fold(function(){return n.bind(function(t){return od(t,r).bind(function(e){return H.head(ud(e)).map(function(e){return ed(e,t)})})})},function(t){return od(t,r).bind(function(e){return H.last(ud(e)).map(function(e){return ed(t,e)})})})},function(e){return cd(r,e)?A.none():(n=r,od((t=e).start(),n).bind(function(e){return H.last(ud(e)).map(function(e){return ed(t.start(),e)})}));var t,n})},cd=function(e,t){return ad(t,e).isSome()},ld=function(e,t){var n,r,o,i,a,u=(n=e,V.curry(_r.eq,n));return(r=t,o=u,i=rd(r.startContainer,o),a=rd(r.endContainer,o),ia([i,a],ed).filter(id).filter(function(e){return cd(o,e)}).orThunk(function(){return sd(o,r)})).bind(function(e){return ad(t=e,u).map(function(e){return td(t,e,ud(e))});var t})},fd=function(e,t){return H.findIndex(e,function(e){return _r.eq(e,t)})},dd=function(n){return(r=n,ia([fd(r.cells(),r.rng().start()),fd(r.cells(),r.rng().end())],function(e,t){return r.cells().slice(e,t+1)})).map(function(e){var t=n.cells();return e.length===t.length?nd.removeTable(n.table()):nd.emptyCells(e)});var r},md=function(e,t){return ld(e,t).bind(dd)},pd=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},gd=pd,hd=function(e){return H.bind(e,function(e){var t=Qi(e);return t?[Fn.fromDom(t)]:[]})},vd=function(e){return 1<pd(e).length},yd=function(e){return H.filter(hd(e),po)},bd=function(e){return su(e,"td[data-mce-selected],th[data-mce-selected]")},Cd=function(e,t){var n=bd(t),r=yd(e);return 0<n.length?n:r},xd=Cd,wd=function(e){return Cd(gd(e.selection.getSel()),Fn.fromDom(e.getBody()))},Nd=function(e,t){return H.each(t,Pc),e.selection.setCursorLocation(t[0].dom(),0),!0},Ed=function(e,t){return rf(e,!1,t),!0},Sd=function(n,e,r,t){return Td(e,t).fold(function(){return t=n,md(e,r).map(function(e){return e.fold(V.curry(Ed,t),V.curry(Nd,t))});var t},function(e){return Ad(n,e)}).getOr(!1)},kd=function(e,t){return H.find(Bl(t,e),po)},Td=function(e,t){return H.find(Bl(t,e),function(e){return"caption"===Yn.name(e)})},Ad=function(e,t){return Pc(t),e.selection.setCursorLocation(t.dom(),0),A.some(!0)},Rd=function(u,s,c,l,f){return ja.navigate(c,u.getBody(),f).bind(function(e){return r=l,o=c,i=f,a=e,ja.firstPositionIn(r.dom()).bind(function(t){return ja.lastPositionIn(r.dom()).map(function(e){return o?i.isEqual(t)&&a.isEqual(e):i.isEqual(e)&&a.isEqual(t)})}).getOr(!0)?Ad(u,l):(t=l,n=e,Td(s,Fn.fromDom(n.getNode())).map(function(e){return!1===_r.eq(e,t)}));var t,n,r,o,i,a}).or(A.some(!0))},_d=function(a,u,s,e){var c=Ta.fromRangeStart(a.selection.getRng());return kd(s,e).bind(function(e){return wl(e)?Ad(a,e):(t=a,n=s,r=u,o=e,i=c,ja.navigate(r,t.getBody(),i).bind(function(e){return kd(n,Fn.fromDom(e.getNode())).map(function(e){return!1===_r.eq(e,o)})}));var t,n,r,o,i})},Bd=function(a,u,e){var s=Fn.fromDom(a.getBody());return Td(s,e).fold(function(){return _d(a,u,s,e)},function(e){return t=a,n=u,r=s,o=e,i=Ta.fromRangeStart(t.selection.getRng()),wl(o)?Ad(t,o):Rd(t,r,n,o,i);var t,n,r,o,i}).getOr(!1)},Dd=function(e,t){var n,r,o,i,a,u=Fn.fromDom(e.selection.getStart(!0)),s=wd(e);return e.selection.isCollapsed()&&0===s.length?Bd(e,t,u):(n=e,r=u,o=Fn.fromDom(n.getBody()),i=n.selection.getRng(),0!==(a=wd(n)).length?Nd(n,a):Sd(n,o,i,r))},Od=function(e,t){e.getDoc().execCommand(t,!1,null)},Pd={deleteCommand:function(e){uf(e,!1)||Zf(e,!1)||Fl(e,!1)||Dd(e)||Vl(e,!1)||(Od(e,"Delete"),ll(e))},forwardDeleteCommand:function(e){uf(e,!0)||Zf(e,!0)||Fl(e,!0)||Dd(e)||Vl(e,!0)||Od(e,"ForwardDelete")}},Ld=function(o){return function(r,e){return A.from(e).map(Fn.fromDom).filter(Yn.isElement).bind(function(e){return function(e,t,n){for(;n!==t;){if(n.style[e]){var r=n.style[e];return""!==r?A.some(r):A.none()}n=n.parentNode}return A.none()}(o,r,e.dom()).or((t=o,n=e.dom(),A.from(ci.DOM.getStyle(n,t,!0))));var t,n}).getOr("")}},Id={getFontSize:Ld("fontSize"),getFontFamily:V.compose(function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},Ld("fontFamily")),toPt:function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,r=t||0,o=Math.pow(10,r),Math.round(n*o)/o+"pt"):e;var n,r,o}},Md=function(e){return ja.firstPositionIn(e.getBody()).map(function(e){var t=e.container();return So.isText(t)?t.parentNode:t})},Fd=function(o){return A.from(o.selection.getRng()).bind(function(e){var t,n,r=o.getBody();return n=r,(t=e).startContainer===n&&0===t.startOffset?A.none():A.from(o.selection.getStart(!0))})},zd=function(e,t){var n=parseInt(t,10);if(1<=n&&n<=7){var r=Bu(e),o=Du(e);return o?o[n-1]||t:r[n-1]||t}return t},Ud=function(e,t){e.formatter.toggle("fontname",{value:zd(e,t)}),e.nodeChanged()},qd=function(t){return Fd(t).fold(function(){return Md(t).map(function(e){return Id.getFontFamily(t.getBody(),e)}).getOr("")},function(e){return Id.getFontFamily(t.getBody(),e)})},Vd=function(e,t){e.formatter.toggle("fontsize",{value:zd(e,t)}),e.nodeChanged()},Hd=function(t){return Fd(t).fold(function(){return Md(t).map(function(e){return Id.getFontSize(t.getBody(),e)}).getOr("")},function(e){return Id.getFontSize(t.getBody(),e)})},jd={isEq:function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset}},$d=function(e,t,n){return null!==function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(e,t,n)},Wd=function(e,t,n){return $d(e,t,function(e){return e.nodeName===n})},Kd=function(e){return e&&"TABLE"===e.nodeName},Xd=function(e,t,n){for(var r=new Zr(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());t=r[n?"prev":"next"]();)if(So.isBr(t))return!0},Yd=function(e,t,n,r,o){var i,a,u,s,c,l,f=e.getRoot(),d=e.schema.getNonEmptyElements();if(u=e.getParent(o.parentNode,e.isBlock)||f,r&&So.isBr(o)&&t&&e.isEmpty(u))return A.some(Ea(o.parentNode,e.nodeIndex(o)));for(i=new Zr(o,u);s=i[r?"prev":"next"]();){if("false"===e.getContentEditableParent(s)||(l=f,Ti(c=s)&&!1===$d(c,l,hc)))return A.none();if(So.isText(s)&&0<s.nodeValue.length)return!1===Wd(s,f,"A")?A.some(Ea(s,r?s.nodeValue.length:0)):A.none();if(e.isBlock(s)||d[s.nodeName.toLowerCase()])return A.none();a=s}return n&&a?A.some(Ea(a,0)):A.none()},Gd=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,p=e.getRoot(),g=!1;if(o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"],l=So.isElement(o)&&i===o.childNodes.length,s=e.schema.getNonEmptyElements(),c=n,Ti(o))return A.none();if(So.isElement(o)&&i>o.childNodes.length-1&&(c=!1),So.isDocument(o)&&(o=p,i=0),o===p){if(c&&(u=o.childNodes[0<i?i-1:0])){if(Ti(u))return A.none();if(s[u.nodeName]||Kd(u))return A.none()}if(o.hasChildNodes()){if(i=Math.min(!c&&0<i?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=So.isText(o)&&l?o.data.length:0,!t&&o===p.lastChild&&Kd(o))return A.none();if(function(e,t){for(;t&&t!==e;){if(So.isContentEditableFalse(t))return!0;t=t.parentNode}return!1}(p,o)||Ti(o))return A.none();if(o.hasChildNodes()&&!1===Kd(o)){a=new Zr(u=o,p);do{if(So.isContentEditableFalse(u)||Ti(u)){g=!1;break}if(So.isText(u)&&0<u.nodeValue.length){i=c?0:u.nodeValue.length,o=u,g=!0;break}if(s[u.nodeName.toLowerCase()]&&(!(f=u)||!/^(TD|TH|CAPTION)$/.test(f.nodeName))){i=e.nodeIndex(u),o=u.parentNode,c||i++,g=!0;break}}while(u=c?a.next():a.prev())}}}return t&&(So.isText(o)&&0===i&&Yd(e,l,t,!0,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),So.isElement(o)&&((u=o.childNodes[i])||(u=o.childNodes[i-1]),!u||!So.isBr(u)||(m="A",(d=u).previousSibling&&d.previousSibling.nodeName===m)||Xd(e,u,!1)||Xd(e,u,!0)||Yd(e,l,t,!0,u).each(function(e){o=e.container(),i=e.offset(),g=!0}))),c&&!t&&So.isText(o)&&i===o.nodeValue.length&&Yd(e,l,t,!1,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),g?A.some(Ea(o,i)):A.none()},Jd={normalize:function(e,t){var n=t.collapsed,r=t.cloneRange(),o=Ea.fromRangeStart(t);return Gd(e,n,!0,r).each(function(e){n&&Ea.isAbove(o,e)||r.setStart(e.container(),e.offset())}),n||Gd(e,n,!1,r).each(function(e){r.setEnd(e.container(),e.offset())}),n&&r.collapse(!0),jd.isEq(t,r)?A.none():A.some(r)}},Qd=function(e,t,n){var r=e.create("span",{},"&nbsp;");n.parentNode.insertBefore(r,n),t.scrollIntoView(r),e.remove(r)},Zd=function(e,t,n,r){var o=e.createRng();r?(o.setStartBefore(n),o.setEndBefore(n)):(o.setStartAfter(n),o.setEndAfter(n)),t.setRng(o)},em=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();Jd.normalize(i,a).each(function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)});var u=a.startOffset,s=a.startContainer;if(1===s.nodeType&&s.hasChildNodes()){var c=u>s.childNodes.length-1;s=s.childNodes[Math.min(u,s.childNodes.length-1)]||s,u=c&&3===s.nodeType?s.nodeValue.length:0}var l=i.getParent(s,i.isBlock),f=l?i.getParent(l.parentNode,i.isBlock):null,d=f?f.nodeName.toUpperCase():"",m=t&&t.ctrlKey;"LI"!==d||m||(l=f),s&&3===s.nodeType&&u>=s.nodeValue.length&&(function(e,t,n){for(var r,o=new Zr(t,n),i=e.getNonEmptyElements();r=o.next();)if(i[r.nodeName.toLowerCase()]||0<r.length)return!0}(e.schema,s,l)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),a.insertNode(n),Qd(i,o,n),Zd(i,o,n,r),e.undoManager.add()},tm=function(e,t){var n=Fn.fromTag("br");Lu.before(Fn.fromDom(t),n),e.undoManager.add()},nm=function(e,t){rm(e.getBody(),t)||Lu.after(Fn.fromDom(t),Fn.fromTag("br"));var n=Fn.fromTag("br");Lu.after(Fn.fromDom(t),n),Qd(e.dom,e.selection,n.dom()),Zd(e.dom,e.selection,n.dom(),!1),e.undoManager.add()},rm=function(e,t){return n=Ta.after(t),!!So.isBr(n.getNode())||ja.nextPosition(e,Ta.after(t)).map(function(e){return So.isBr(e.getNode())}).getOr(!1);var n},om=function(e){return e&&"A"===e.nodeName&&"href"in e},im=function(e){return e.fold(V.constant(!1),om,om,V.constant(!1))},am=function(e,t){t.fold(V.noop,V.curry(tm,e),V.curry(nm,e),V.noop)},um={insert:function(e,t){var n,r,o,i=(n=e,r=V.curry(sl.isInlineTarget,n),o=Ta.fromRangeStart(n.selection.getRng()),Mf(r,n.getBody(),o).filter(im));i.isSome()?i.each(V.curry(am,e)):em(e,t)}},sm=Hl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),cm=(sm.before,sm.on,sm.after,function(e){return e.fold(V.identity,V.identity,V.identity)}),lm=Hl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),fm=br.immutable("start","soffset","finish","foffset"),dm={domRange:lm.domRange,relative:lm.relative,exact:lm.exact,exactFromRange:function(e){return lm.exact(e.start(),e.soffset(),e.finish(),e.foffset())},range:fm,getWin:function(e){var t=e.match({domRange:function(e){return Fn.fromDom(e.startContainer)},relative:function(e,t){return cm(e)},exact:function(e,t,n,r){return e}});return Fr.defaultView(t)}},mm=In.detect().browser,pm=function(e,t){var n=Yn.isText(t)?Dc(t).length:Fr.children(t).length+1;return n<e?n:e<0?0:e},gm=function(e){return dm.range(e.start(),pm(e.soffset(),e.start()),e.finish(),pm(e.foffset(),e.finish()))},hm=function(e,t){return _r.contains(e,t)||_r.eq(e,t)},vm=function(t){return function(e){return hm(t,e.start())&&hm(t,e.finish())}},ym=function(e){return!0===e.inline||mm.isIE()},bm=function(e){return dm.range(Fn.fromDom(e.startContainer),e.startOffset,Fn.fromDom(e.endContainer),e.endOffset)},Cm=function(e){var t=e.getSelection();return(t&&0!==t.rangeCount?A.from(t.getRangeAt(0)):A.none()).map(bm)},xm=function(e){var t=Fr.defaultView(e);return Cm(t.dom()).filter(vm(e))},wm=function(e,t){return A.from(t).filter(vm(e)).map(gm)},Nm=function(e){var t=document.createRange();try{return t.setStart(e.start().dom(),e.soffset()),t.setEnd(e.finish().dom(),e.foffset()),A.some(t)}catch(n){return A.none()}},Em=function(e){return(e.bookmark?e.bookmark:A.none()).bind(V.curry(wm,Fn.fromDom(e.getBody()))).bind(Nm)},Sm={store:function(e){var t=ym(e)?xm(Fn.fromDom(e.getBody())):A.none();e.bookmark=t.isSome()?t:e.bookmark},storeNative:function(e,t){var n=Fn.fromDom(e.getBody()),r=(ym(e)?A.from(t):A.none()).map(bm).filter(vm(n));e.bookmark=r.isSome()?r:e.bookmark},readRange:Cm,restore:function(t){Em(t).each(function(e){t.selection.setRng(e)})},getRng:Em,getBookmark:xm,validate:wm},km=function(e,t){var n=e.settings,r=e.dom,o=e.selection,i=e.formatter,a=/[a-z%]+$/i.exec(n.indentation)[0],u=parseInt(n.indentation,10),s=e.getParam("indent_use_margin",!1);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||(n.forced_root_block||r.getParent(o.getNode(),r.isBlock)||i.apply("div"),H.each(o.getSelectedBlocks(),function(e){return function(e,t,n,r,o,i){if("false"!==e.getContentEditable(i)&&"LI"!==i.nodeName){var a=n?"margin":"padding";if(a="TABLE"===i.nodeName?"margin":a,a+="rtl"===e.getStyle(i,"direction",!0)?"Right":"Left","outdent"===t){var u=Math.max(0,parseInt(i.style[a]||0,10)-r);e.setStyle(i,a,u?u+o:"")}else u=parseInt(i.style[a]||0,10)+r+o,e.setStyle(i,a,u)}}(r,t,s,u,a,e)}))},Tm=Dt.each,Am=Dt.extend,Rm=Dt.map,_m=Dt.inArray;function Bm(s){var o,i,a,t,c={state:{},exec:{},value:{}},n=s.settings;s.on("PreInit",function(){o=s.dom,i=s.selection,n=s.settings,a=s.formatter});var r=function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.state[e])return t(e);try{return s.getDoc().queryCommandState(e)}catch(n){}return!1}},e=function(e,n){n=n||"exec",Tm(e,function(t,e){Tm(e.toLowerCase().split(","),function(e){c[n][e]=t})})},u=function(e,t,n){e=e.toLowerCase(),c.value[e]=function(){return t.call(n||s)}};Am(this,{execCommand:function(t,n,r,e){var o,i,a=!1;if(!s.removed){if(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(t)||e&&e.skip_focus?Sm.restore(s):s.focus(),(e=s.fire("BeforeExecCommand",{command:t,ui:n,value:r})).isDefaultPrevented())return!1;if(i=t.toLowerCase(),o=c.exec[i])return o(i,n,r),s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;if(Tm(s.plugins,function(e){if(e.execCommand&&e.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!(a=!0)}),a)return a;if(s.theme&&s.theme.execCommand&&s.theme.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;try{a=s.getDoc().execCommand(t,n,r)}catch(u){}return!!a&&(s.fire("ExecCommand",{command:t,ui:n,value:r}),!0)}},queryCommandState:r,queryCommandValue:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.value[e])return t(e);try{return s.getDoc().queryCommandValue(e)}catch(n){}}},queryCommandSupported:function(e){if(e=e.toLowerCase(),c.exec[e])return!0;try{return s.getDoc().queryCommandSupported(e)}catch(t){}return!1},addCommands:e,addCommand:function(e,o,i){e=e.toLowerCase(),c.exec[e]=function(e,t,n,r){return o.call(i||s,t,n,r)}},addQueryStateHandler:function(e,t,n){e=e.toLowerCase(),c.state[e]=function(){return t.call(n||s)}},addQueryValueHandler:u,hasCustomCommand:function(e){return e=e.toLowerCase(),!!c.exec[e]}});var l=function(e,t,n){return t===undefined&&(t=!1),n===undefined&&(n=null),s.getDoc().execCommand(e,t,n)},f=function(e){return a.match(e)},d=function(e,t){a.toggle(e,t?{value:t}:undefined),s.nodeChanged()},m=function(e){t=i.getBookmark(e)},p=function(){i.moveToBookmark(t)};e({"mceResetDesignMode,mceBeginUndoLevel":function(){},"mceEndUndoLevel,mceAddUndoLevel":function(){s.undoManager.add()},"Cut,Copy,Paste":function(e){var t,n=s.getDoc();try{l(e)}catch(o){t=!0}if("paste"!==e||n.queryCommandEnabled(e)||(t=!0),t||!n.queryCommandSupported(e)){var r=s.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");de.mac&&(r=r.replace(/Ctrl\+/g,"\u2318+")),s.notificationManager.open({text:r,type:"error"})}},unlink:function(){if(i.isCollapsed()){var e=s.dom.getParent(s.selection.getStart(),"a");e&&s.dom.remove(e,!0)}else a.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(e){var t=e.substring(7);"full"===t&&(t="justify"),Tm("left,center,right,justify".split(","),function(e){t!==e&&a.remove("align"+e)}),"none"!==t&&d("align"+t)},"InsertUnorderedList,InsertOrderedList":function(e){var t,n;l(e),(t=o.getParent(i.getNode(),"ol,ul"))&&(n=t.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(n.nodeName)&&(m(),o.split(n,t),p()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){d(e)},"ForeColor,HiliteColor":function(e,t,n){d(e,n)},FontName:function(e,t,n){Ud(s,n)},FontSize:function(e,t,n){Vd(s,n)},RemoveFormat:function(e){a.remove(e)},mceBlockQuote:function(){d("blockquote")},FormatBlock:function(e,t,n){return d(n||"p")},mceCleanup:function(){var e=i.getBookmark();s.setContent(s.getContent()),i.moveToBookmark(e)},mceRemoveNode:function(e,t,n){var r=n||i.getNode();r!==s.getBody()&&(m(),s.dom.remove(r,!0),p())},mceSelectNodeDepth:function(e,t,n){var r=0;o.getParent(i.getNode(),function(e){if(1===e.nodeType&&r++===n)return i.select(e),!1},s.getBody())},mceSelectNode:function(e,t,n){i.select(n)},mceInsertContent:function(e,t,n){Hc.insertAtCaret(s,n)},mceInsertRawHTML:function(e,t,n){var r=s.getContent();i.setContent("tiny_mce_marker"),s.setContent(r.replace(/tiny_mce_marker/g,function(){return n}))},mceToggleFormat:function(e,t,n){d(n)},mceSetContent:function(e,t,n){s.setContent(n)},"Indent,Outdent":function(e){km(s,e)},mceRepaint:function(){},InsertHorizontalRule:function(){s.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){s.hasVisual=!s.hasVisual,s.addVisual()},mceReplaceContent:function(e,t,n){s.execCommand("mceInsertContent",!1,n.replace(/\{\$selection\}/g,i.getContent({format:"text"})))},mceInsertLink:function(e,t,n){var r;"string"==typeof n&&(n={href:n}),r=o.getParent(i.getNode(),"a"),n.href=n.href.replace(" ","%20"),r&&n.href||a.remove("link"),n.href&&a.apply("link",n,r)},selectAll:function(){var e=o.getParent(i.getStart(),So.isContentEditableTrue);if(e){var t=o.createRng();t.selectNodeContents(e),i.setRng(t)}},"delete":function(){Pd.deleteCommand(s)},forwardDelete:function(){Pd.forwardDeleteCommand(s)},mceNewDocument:function(){s.setContent("")},InsertLineBreak:function(e,t,n){return um.insert(s,n),!0}}),e({"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull":function(e){var t="align"+e.substring(7),n=i.isCollapsed()?[o.getParent(i.getNode(),o.isBlock)]:i.getSelectedBlocks(),r=Rm(n,function(e){return!!a.matchNode(e,t)});return-1!==_m(r,!0)},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return f(e)},mceBlockQuote:function(){return f("blockquote")},Outdent:function(){var e;if(n.inline_styles){if((e=o.getParent(i.getStart(),o.isBlock))&&0<parseInt(e.style.paddingLeft,10))return!0;if((e=o.getParent(i.getEnd(),o.isBlock))&&0<parseInt(e.style.paddingLeft,10))return!0}return r("InsertUnorderedList")||r("InsertOrderedList")||!n.inline_styles&&!!o.getParent(i.getNode(),"BLOCKQUOTE")},"InsertUnorderedList,InsertOrderedList":function(e){var t=o.getParent(i.getNode(),"ul,ol");return t&&("insertunorderedlist"===e&&"UL"===t.tagName||"insertorderedlist"===e&&"OL"===t.tagName)}},"state"),e({Undo:function(){s.undoManager.undo()},Redo:function(){s.undoManager.redo()}}),u("FontName",function(){return qd(s)},this),u("FontSize",function(){return Hd(s)},this)}var Dm=Dt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend"," "),Om=function(a){var u,s,c=this,l={},f=function(){return!1},d=function(){return!0};u=(a=a||{}).scope||c,s=a.toggleEvent||f;var r=function(e,t,n,r){var o,i,a;if(!1===t&&(t=f),t)for(t={func:t},r&&Dt.extend(t,r),a=(i=e.toLowerCase().split(" ")).length;a--;)e=i[a],(o=l[e])||(o=l[e]=[],s(e,!0)),n?o.unshift(t):o.push(t);return c},m=function(e,t){var n,r,o,i,a;if(e)for(n=(i=e.toLowerCase().split(" ")).length;n--;){if(e=i[n],r=l[e],!e){for(o in l)s(o,!1),delete l[o];return c}if(r){if(t)for(a=r.length;a--;)r[a].func===t&&(r=r.slice(0,a).concat(r.slice(a+1)),l[e]=r);else r.length=0;r.length||(s(e,!1),delete l[e])}}else{for(e in l)s(e,!1);l={}}return c};c.fire=function(e,t){var n,r,o,i;if(e=e.toLowerCase(),(t=t||{}).type=e,t.target||(t.target=u),t.preventDefault||(t.preventDefault=function(){t.isDefaultPrevented=d},t.stopPropagation=function(){t.isPropagationStopped=d},t.stopImmediatePropagation=function(){t.isImmediatePropagationStopped=d},t.isDefaultPrevented=f,t.isPropagationStopped=f,t.isImmediatePropagationStopped=f),a.beforeFire&&a.beforeFire(t),n=l[e])for(r=0,o=n.length;r<o;r++){if((i=n[r]).once&&m(e,i.func),t.isImmediatePropagationStopped())return t.stopPropagation(),t;if(!1===i.func.call(u,t))return t.preventDefault(),t}return t},c.on=r,c.off=m,c.once=function(e,t,n){return r(e,t,n,{once:!0})},c.has=function(e){return e=e.toLowerCase(),!(!l[e]||0===l[e].length)}};Om.isNative=function(e){return!!Dm[e.toLowerCase()]};var Pm,Lm=function(n){return n._eventDispatcher||(n._eventDispatcher=new Om({scope:n,toggleEvent:function(e,t){Om.isNative(e)&&n.toggleNativeEvent&&n.toggleNativeEvent(e,t)}})),n._eventDispatcher},Im={fire:function(e,t,n){if(this.removed&&"remove"!==e)return t;if(t=Lm(this).fire(e,t,n),!1!==n&&this.parent)for(var r=this.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},on:function(e,t,n){return Lm(this).on(e,t,n)},off:function(e,t){return Lm(this).off(e,t)},once:function(e,t){return Lm(this).once(e,t)},hasEventListeners:function(e){return Lm(this).has(e)}},Mm=ci.DOM,Fm=function(e,t){return"selectionchange"===t?e.getDoc():!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t)?e.getDoc().documentElement:e.settings.event_root?(e.eventRoot||(e.eventRoot=Mm.select(e.settings.event_root)[0]),e.eventRoot):e.getBody()},zm=function(i,a){var e,t,u=function(e){return!e.hidden&&!e.readonly};if(i.delegates||(i.delegates={}),!i.delegates[a]&&!i.removed)if(e=Fm(i,a),i.settings.event_root){if(Pm||(Pm={},i.editorManager.on("removeEditor",function(){var e;if(!i.editorManager.activeEditor&&Pm){for(e in Pm)i.dom.unbind(Fm(i,e));Pm=null}})),Pm[a])return;t=function(e){for(var t=e.target,n=i.editorManager.get(),r=n.length;r--;){var o=n[r].getBody();(o===t||Mm.isChildOf(t,o))&&u(n[r])&&n[r].fire(a,e)}},Pm[a]=t,Mm.bind(e,a,t)}else t=function(e){u(i)&&i.fire(a,e)},Mm.bind(e,a,t),i.delegates[a]=t},Um={bindPendingEventDelegates:function(){var t=this;Dt.each(t._pendingNativeEvents,function(e){zm(t,e)})},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(t?n.initialized?zm(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(Fm(n,e),e,n.delegates[e]),delete n.delegates[e]))},unbindAllNativeEvents:function(){var e,t=this,n=t.getBody(),r=t.dom;if(t.delegates){for(e in t.delegates)t.dom.unbind(Fm(t,e),e,t.delegates[e]);delete t.delegates}!t.inline&&n&&r&&(n.onload=null,r.unbind(t.getWin()),r.unbind(t.getDoc())),r&&(r.unbind(n),r.unbind(t.getContainer()))}},qm=Um=Dt.extend({},Im,Um),Vm=function(e,t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},Hm=function(e,t){var n,r,o;e._clickBlocker&&(e._clickBlocker.unbind(),e._clickBlocker=null),t?(e._clickBlocker=(r=(n=e).getBody(),o=function(e){0<n.dom.getParents(e.target,"a").length&&e.preventDefault()},n.dom.bind(r,"click",o),{unbind:function(){n.dom.unbind(r,"click",o)}}),e.selection.controlSelection.hideResizeRect(),e.readonly=!0,e.getBody().contentEditable=!1):(e.readonly=!1,e.getBody().contentEditable=!0,Vm(e,"StyleWithCSS",!1),Vm(e,"enableInlineTableEditing",!1),Vm(e,"enableObjectResizing",!1),e.focus(),e.nodeChanged())},jm=function(e,t){var n=e.readonly?"readonly":"design";t!==n&&(e.initialized?Hm(e,"readonly"===t):e.on("init",function(){Hm(e,"readonly"===t)}),e.fire("SwitchMode",{mode:t}))},$m=Dt.each,Wm=Dt.explode,Km={f9:120,f10:121,f11:122},Xm=Dt.makeMap("alt,ctrl,shift,meta,access");function Ym(i){var a={},r=[],u=function(e){var t,n,r={};for(n in $m(Wm(e,"+"),function(e){e in Xm?r[e]=!0:/^[0-9]{2,}$/.test(e)?r.keyCode=parseInt(e,10):(r.charCode=e.charCodeAt(0),r.keyCode=Km[e]||e.toUpperCase().charCodeAt(0))}),t=[r.keyCode],Xm)r[n]?t.push(n):r[n]=!1;return r.id=t.join(","),r.access&&(r.alt=!0,de.mac?r.ctrl=!0:r.shift=!0),r.meta&&(de.mac?r.meta=!0:(r.ctrl=!0,r.meta=!1)),r},s=function(e,t,n,r){var o;return(o=Dt.map(Wm(e,">"),u))[o.length-1]=Dt.extend(o[o.length-1],{func:n,scope:r||i}),Dt.extend(o[0],{desc:i.translate(t),subpatterns:o.slice(1)})},o=function(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)},c=function(e){return e.func?e.func.call(e.scope):null};i.on("keyup keypress keydown",function(t){var e,n;((n=t).altKey||n.ctrlKey||n.metaKey||"keydown"===(e=t).type&&112<=e.keyCode&&e.keyCode<=123)&&!t.isDefaultPrevented()&&($m(a,function(e){if(o(t,e))return r=e.subpatterns.slice(0),"keydown"===t.type&&c(e),!0}),o(t,r[0])&&(1===r.length&&"keydown"===t.type&&c(r[0]),r.shift()))}),this.add=function(e,n,r,o){var t;return"string"==typeof(t=r)?r=function(){i.execCommand(t,!1,null)}:Dt.isArray(t)&&(r=function(){i.execCommand(t[0],t[1],t[2])}),$m(Wm(Dt.trim(e.toLowerCase())),function(e){var t=s(e,n,r,o);a[t.id]=t}),!0},this.remove=function(e){var t=s(e);return!!a[t.id]&&(delete a[t.id],!0)}}var Gm=function(e){var t=e!==undefined?e.dom():document;return A.from(t.activeElement).map(Fn.fromDom)},Jm=function(e){var t=Fr.owner(e).dom();return e.dom()===t.activeElement},Qm=function(t){return Gm(Fr.owner(t)).filter(function(e){return t.dom().contains(e.dom())})},Zm=function(t,e){return(n=e,n.collapsed?A.from(Zi(n.startContainer,n.startOffset)).map(Fn.fromDom):A.none()).bind(function(e){return mo(e)?A.some(e):!1===_r.contains(t,e)?A.some(t):A.none()});var n},ep=function(t,e){Zm(Fn.fromDom(t.getBody()),e).bind(function(e){return ja.firstPositionIn(e.dom())}).fold(function(){t.selection.normalize()},function(e){return t.selection.setRng(e.toRange())})},tp=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},np=function(e){var t,n=e.getBody();return n&&(t=Fn.fromDom(n),Jm(t)||Qm(t).isSome())},rp=function(e){return e.inline?np(e):(t=e).iframeElement&&Jm(Fn.fromDom(t.iframeElement));var t},op=function(e){return e.editorManager.setActive(e)},ip=function(e,t){e.removed||(t?op(e):function(t){var e=t.selection,n=t.settings.content_editable,r=t.getBody(),o=e.getRng();t.quirks.refreshContentEditable();var i,a,u=(i=t,a=e.getNode(),i.dom.getParent(a,function(e){return"true"===i.dom.getContentEditable(e)}));if(t.$.contains(r,u))return tp(u),ep(t,o),op(t);t.bookmark!==undefined&&!1===rp(t)&&Sm.getRng(t).each(function(e){t.selection.setRng(e),o=e}),n||(de.opera||tp(r),t.getWin().focus()),(de.gecko||n)&&(tp(r),ep(t,o)),op(t)}(e))},ap=rp,up=function(e,t){return t.dom()[e]},sp=function(e,t){return parseInt(hr(t,e),10)},cp=V.curry(up,"clientWidth"),lp=V.curry(up,"clientHeight"),fp=V.curry(sp,"margin-top"),dp=V.curry(sp,"margin-left"),mp={isXYInContentArea:function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m=Fn.fromDom(e.getBody()),p=e.inline?m:Fr.documentElement(m),g=(r=e.inline,i=t,a=n,u=(o=p).dom().getBoundingClientRect(),{x:i-(r?u.left+o.dom().clientLeft+dp(o):0),y:a-(r?u.top+o.dom().clientTop+fp(o):0)});return c=g.x,l=g.y,f=cp(s=p),d=lp(s),0<=c&&0<=l&&c<=f&&l<=d},isEditorAttachedToDom:function(e){var t,n=e.inline?e.getBody():e.getContentAreaContainer();return(t=n,A.from(t).map(Fn.fromDom)).map(function(e){return _r.contains(Fr.owner(e),e)}).getOr(!1)}};function pp(n){var t,o=[],i=function(){var e,t=n.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():{open:e=function(){throw new Error("Theme did not provide a NotificationManager implementation.")},close:e,reposition:e,getArgs:e}},a=function(){0<o.length&&i().reposition(o)},u=function(t){H.findIndex(o,function(e){return e===t}).each(function(e){o.splice(e,1)})},r=function(r){if(!n.removed&&mp.isEditorAttachedToDom(n))return H.find(o,function(e){return t=i().getArgs(e),n=r,!(t.type!==n.type||t.text!==n.text||t.progressBar||t.timeout||n.progressBar||n.timeout);var t,n}).getOrThunk(function(){n.editorManager.setActive(n);var e,t=i().open(r,function(){u(t),a()});return e=t,o.push(e),a(),t})};return(t=n).on("SkinLoaded",function(){var e=t.settings.service_message;e&&r({text:e,type:"warning",timeout:0,icon:""})}),t.on("ResizeEditor ResizeWindow",function(){ve.requestAnimationFrame(a)}),t.on("remove",function(){H.each(o,function(e){i().close(e)})}),{open:r,close:function(){A.from(o[0]).each(function(e){i().close(e),u(e),a()})},getNotifications:function(){return o}}}function gp(r){var o=[],i=function(){var e,t=r.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():{open:e=function(){throw new Error("Theme did not provide a WindowManager implementation.")},alert:e,confirm:e,close:e,getParams:e,setParams:e}},a=function(e,t){return function(){return t?t.apply(e,arguments):undefined}},u=function(e){var t;o.push(e),t=e,r.fire("OpenWindow",{win:t})},s=function(n){H.findIndex(o,function(e){return e===n}).each(function(e){var t;o.splice(e,1),t=n,r.fire("CloseWindow",{win:t}),0===o.length&&r.focus()})},e=function(){return A.from(o[o.length-1])};return r.on("remove",function(){H.each(o.slice(0),function(e){i().close(e)})}),{windows:o,open:function(e,t){r.editorManager.setActive(r),Sm.store(r);var n=i().open(e,t,s);return u(n),n},alert:function(e,t,n){var r=i().alert(e,a(n||this,t),s);u(r)},confirm:function(e,t,n){var r=i().confirm(e,a(n||this,t),s);u(r)},close:function(){e().each(function(e){i().close(e),s(e)})},getParams:function(){return e().map(i().getParams).getOr(null)},setParams:function(t){e().each(function(e){i().setParams(e,t)})},getWindows:function(){return o}}}var hp=vi.PluginManager,vp=function(e,t){var n=function(e,t){for(var n in hp.urls)if(hp.urls[n]+"/plugin"+t+".js"===e)return n;return null}(t,e.suffix);return n?"Failed to load plugin: "+n+" from url "+t:"Failed to load plugin url: "+t},yp=function(e,t){e.notificationManager.open({type:"error",text:t})},bp=function(e,t){e._skinLoaded?yp(e,t):e.on("SkinLoaded",function(){yp(e,t)})},Cp={pluginLoadError:function(e,t){bp(e,vp(e,t))},uploadError:function(e,t){bp(e,"Failed to upload image: "+t)},displayError:bp,initError:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=window.console;r&&(r.error?r.error.apply(r,arguments):r.log.apply(r,arguments))}},xp=vi.PluginManager,wp=vi.ThemeManager;function Np(){return new(z.getOrDie("XMLHttpRequest"))}function Ep(u,s){var r={},n=function(e,r,o,t){var i,n;(i=new Np).open("POST",s.url),i.withCredentials=s.credentials,i.upload.onprogress=function(e){t(e.loaded/e.total*100)},i.onerror=function(){o("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var e,t,n;i.status<200||300<=i.status?o("HTTP Error: "+i.status):(e=JSON.parse(i.responseText))&&"string"==typeof e.location?r((t=s.basePath,n=e.location,t?t.replace(/\/$/,"")+"/"+n.replace(/^\//,""):n)):o("Invalid JSON: "+i.responseText)},(n=new FormData).append("file",e.blob(),e.filename()),i.send(n)},c=function(e,t){return{url:t,blobInfo:e,status:!0}},l=function(e,t){return{url:"",blobInfo:e,status:!1,error:t}},f=function(e,t){Dt.each(r[e],function(e){e(t)}),delete r[e]},o=function(e,n){return e=Dt.grep(e,function(e){return!u.isUploaded(e.blobUri())}),me.all(Dt.map(e,function(e){return u.isPending(e.blobUri())?(t=e.blobUri(),new me(function(e){r[t]=r[t]||[],r[t].push(e)})):(o=e,i=s.handler,a=n,u.markPending(o.blobUri()),new me(function(t){var n;try{var r=function(){n&&n.close()};i(o,function(e){r(),u.markUploaded(o.blobUri(),e),f(o.blobUri(),c(o,e)),t(c(o,e))},function(e){r(),u.removeFailed(o.blobUri()),f(o.blobUri(),l(o,e)),t(l(o,e))},function(e){e<0||100<e||(n||(n=a()),n.progressBar.value(e))})}catch(e){t(l(o,e.message))}}));var o,i,a,t}))};return s=Dt.extend({credentials:!1,handler:n},s),{upload:function(e,t){return s.url||s.handler!==n?o(e,t):new me(function(e){e([])})}}}function Sp(e,t){return new(z.getOrDie("Blob"))(e,t)}function kp(){return new(z.getOrDie("FileReader"))}function Tp(e){return new(z.getOrDie("Uint8Array"))(e)}var Ap=function(e){return z.getOrDie("atob")(e)},Rp=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},_p=function(e){return 0===e.indexOf("blob:")?(i=e,new me(function(e,t){var n=function(){t("Cannot convert "+i+" to Blob. Resource might not exist or is inaccessible.")};try{var r=new Np;r.open("GET",i,!0),r.responseType="blob",r.onload=function(){200===this.status?e(this.response):n()},r.onerror=n,r.send()}catch(o){n()}})):0===e.indexOf("data:")?(o=e,new me(function(e){var t,n,r;o=Rp(o);try{t=Ap(o.data)}catch(Vx){return void e(new Sp([]))}for(n=new Tp(t.length),r=0;r<n.length;r++)n[r]=t.charCodeAt(r);e(new Sp([n],{type:o.type}))})):null;var i,o},Bp=function(n){return new me(function(e){var t=new kp;t.onloadend=function(){e(t.result)},t.readAsDataURL(n)})},Dp=Rp,Op=0,Pp=function(e){return(e||"blobid")+Op++},Lp=function(n,r,o,t){var i,a;0!==r.src.indexOf("blob:")?(i=Dp(r.src).data,(a=n.findFirst(function(e){return e.base64()===i}))?o({image:r,blobInfo:a}):_p(r.src).then(function(e){a=n.create(Pp(),e,i),n.add(a),o({image:r,blobInfo:a})},function(e){t(e)})):(a=n.getByUri(r.src))?o({image:r,blobInfo:a}):_p(r.src).then(function(t){Bp(t).then(function(e){i=Dp(e).data,a=n.create(Pp(),t,i),n.add(a),o({image:r,blobInfo:a})})},function(e){t(e)})},Ip=function(e){return e?e.getElementsByTagName("img"):[]},Mp=0,Fp={uuid:function(e){return e+Mp+++(t=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+(new Date).getTime().toString(36)+t()+t()+t());var t}};function zp(u){var n,o,i,t,e,a,r,s,c,l,f=(n=[],o=oa.constant,i=function(e){var t,n,r;if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");return t=e.id||Fp.uuid("blobid"),n=e.name||t,{id:o(t),name:o(n),filename:o(n+"."+(r=e.blob.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}[r.toLowerCase()]||"dat")),blob:o(e.blob),base64:o(e.base64),blobUri:o(e.blobUri||q.createObjectURL(e.blob)),uri:o(e.uri)}},{create:function(e,t,n,r){return i("object"==typeof e?e:{id:e,name:r,blob:t,base64:n})},add:function(e){t(e.id())||n.push(e)},get:t=function(t){return e(function(e){return e.id()===t})},getByUri:function(t){return e(function(e){return e.blobUri()===t})},findFirst:e=function(e){return Tt.filter(n,e)[0]},removeByUri:function(t){n=Tt.filter(n,function(e){return e.blobUri()!==t||(q.revokeObjectURL(e.blobUri()),!1)})},destroy:function(){Tt.each(n,function(e){q.revokeObjectURL(e.blobUri())}),n=[]}}),d=u.settings,m=(s={},c=function(e,t){return{status:e,resultUri:t}},{hasBlobUri:l=function(e){return e in s},getResultUri:function(e){var t=s[e];return t?t.resultUri:null},isPending:function(e){return!!l(e)&&1===s[e].status},isUploaded:function(e){return!!l(e)&&2===s[e].status},markPending:function(e){s[e]=c(1,null)},markUploaded:function(e,t){s[e]=c(2,t)},removeFailed:function(e){delete s[e]},destroy:function(){s={}}}),p=function(t){return function(e){return u.selection?t(e):[]}},g=function(e,t,n){for(var r=0;-1!==(r=e.indexOf(t,r))&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1),-1!==r;);return e},h=function(e,t,n){return e=g(e,'src="'+t+'"','src="'+n+'"'),e=g(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},v=function(t,n){Tt.each(u.undoManager.data,function(e){"fragmented"===e.type?e.fragments=Tt.map(e.fragments,function(e){return h(e,t,n)}):e.content=h(e.content,t,n)})},y=function(){return u.notificationManager.open({text:u.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})},b=function(e,t){f.removeByUri(e.src),v(e.src,t),u.$(e).attr({src:d.images_reuse_filename?t+"?"+(new Date).getTime():t,"data-mce-src":u.convertURL(t,"src")})},C=function(n){return a||(a=Ep(m,{url:d.images_upload_url,basePath:d.images_upload_base_path,credentials:d.images_upload_credentials,handler:d.images_upload_handler})),N().then(p(function(r){var e;return e=Tt.map(r,function(e){return e.blobInfo}),a.upload(e,y).then(p(function(e){var t=Tt.map(e,function(e,t){var n=r[t].image;return e.status&&!1!==u.settings.images_replace_blob_uris?b(n,e.url):e.error&&Cp.uploadError(u,e.error),{element:n,status:e.status}});return n&&n(t),t}))}))},x=function(e){if(!1!==d.automatic_uploads)return C(e)},w=function(e){return!d.images_dataimg_filter||d.images_dataimg_filter(e)},N=function(){var o,i,a;return r||(o=m,i=f,a={},r={findAll:function(e,n){var t;n||(n=oa.constant(!0)),t=Tt.filter(Ip(e),function(e){var t=e.src;return!!de.fileApi&&!e.hasAttribute("data-mce-bogus")&&!e.hasAttribute("data-mce-placeholder")&&!(!t||t===de.transparentSrc)&&(0===t.indexOf("blob:")?!o.isUploaded(t):0===t.indexOf("data:")&&n(e))});var r=Tt.map(t,function(n){if(a[n.src])return new me(function(t){a[n.src].then(function(e){if("string"==typeof e)return e;t({image:n,blobInfo:e.blobInfo})})});var e=new me(function(e,t){Lp(i,n,e,t)}).then(function(e){return delete a[e.image.src],e})["catch"](function(e){return delete a[n.src],e});return a[n.src]=e});return me.all(r)}}),r.findAll(u.getBody(),w).then(p(function(e){return e=Tt.filter(e,function(e){return"string"!=typeof e||(Cp.displayError(u,e),!1)}),Tt.each(e,function(e){v(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")}),e}))},E=function(e){return e.replace(/src="(blob:[^"]+)"/g,function(e,n){var t=m.getResultUri(n);if(t)return'src="'+t+'"';var r=f.getByUri(n);return r||(r=Tt.reduce(u.editorManager.get(),function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)},null)),r?'src="data:'+r.blob().type+";base64,"+r.base64()+'"':e})};return u.on("setContent",function(){!1!==u.settings.automatic_uploads?x():N()}),u.on("RawSaveContent",function(e){e.content=E(e.content)}),u.on("getContent",function(e){e.source_view||"raw"===e.format||(e.content=E(e.content))}),u.on("PostRender",function(){u.parser.addNodeFilter("img",function(e){Tt.each(e,function(e){var t=e.attr("src");if(!f.getByUri(t)){var n=m.getResultUri(t);n&&e.attr("src",n)}})})}),{blobCache:f,uploadImages:C,uploadImagesAuto:x,scanForImages:N,destroy:function(){f.destroy(),m.destroy(),r=a=null}}}var Up=function(e,t){return e.hasOwnProperty(t.nodeName)},qp=function(e,t){if(So.isText(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||Up(e,t.nextSibling)))return!0}return!1},Vp=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.dom,p=e.selection,g=e.schema,h=g.getBlockElements(),v=p.getStart(),y=e.getBody();if(f=d.forced_root_block,v&&So.isElement(v)&&f&&(l=y.nodeName.toLowerCase(),g.isValidChild(l,f.toLowerCase())&&(b=h,C=y,x=v,!H.exists(_l(Fn.fromDom(x),Fn.fromDom(C)),function(e){return Up(b,e.dom())})))){var b,C,x,w,N;for(n=(t=p.getRng()).startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,c=ap(e),v=y.firstChild;v;)if(w=h,N=v,So.isText(N)||So.isElement(N)&&!Up(w,N)&&!Ec.isBookmarkNode(N)){if(qp(h,v)){v=(u=v).nextSibling,m.remove(u);continue}a||(a=m.create(f,e.settings.forced_root_block_attrs),v.parentNode.insertBefore(a,v),s=!0),v=(u=v).nextSibling,a.appendChild(u)}else a=null,v=v.nextSibling;s&&c&&(t.setStart(n,r),t.setEnd(o,i),p.setRng(t),e.nodeChanged())}},Hp=function(e){e.settings.forced_root_block&&e.on("NodeChange",V.curry(Vp,e))};function jp(i){var r,o=[];"onselectionchange"in i.getDoc()||i.on("NodeChange Click MouseUp KeyUp Focus",function(e){var t,n;n={startContainer:(t=i.selection.getRng()).startContainer,startOffset:t.startOffset,endContainer:t.endContainer,endOffset:t.endOffset},"nodechange"!==e.type&&jd.isEq(n,r)||i.fire("SelectionChange"),r=n}),i.on("contextmenu",function(){i.fire("SelectionChange")}),i.on("SelectionChange",function(){var e=i.selection.getStart(!0);!e||!de.range&&i.selection.isCollapsed()||!function(e){var t,n;if((n=i.$(e).parentsUntil(i.getBody()).add(e)).length===o.length){for(t=n.length;0<=t&&n[t]===o[t];t--);if(-1===t)return o=n,!0}return o=n,!1}(e)&&i.dom.isChildOf(e,i.getBody())&&i.nodeChanged({selectionChange:!0})}),i.on("MouseUp",function(e){e.isDefaultPrevented()||("IMG"===i.selection.getNode().nodeName?ve.setEditorTimeout(i,function(){i.nodeChanged()}):i.nodeChanged())}),this.nodeChanged=function(e){var t,n,r,o=i.selection;i.initialized&&o&&!i.settings.disable_nodechange&&!i.readonly&&(r=i.getBody(),(t=o.getStart(!0)||r).ownerDocument===i.getDoc()&&i.dom.isChildOf(t,r)||(t=r),n=[],i.dom.getParent(t,function(e){if(e===r)return!0;n.push(e)}),(e=e||{}).element=t,e.parents=n,i.fire("NodeChange",e))}}var $p,Wp,Kp=function(e){var t,n,r,o;return o=e.getBoundingClientRect(),n=(t=e.ownerDocument).documentElement,r=t.defaultView,{top:o.top+r.pageYOffset-n.clientTop,left:o.left+r.pageXOffset-n.clientLeft}},Xp=function(e,t){return n=(u=e).inline?Kp(u.getBody()):{left:0,top:0},a=(i=e).getBody(),r=i.inline?{left:a.scrollLeft,top:a.scrollTop}:{left:0,top:0},{pageX:(o=function(e,t){if(t.target.ownerDocument!==e.getDoc()){var n=Kp(e.getContentAreaContainer()),r=(i=(o=e).getBody(),a=o.getDoc().documentElement,u={left:i.scrollLeft,top:i.scrollTop},s={left:i.scrollLeft||a.scrollLeft,top:i.scrollTop||a.scrollTop},o.inline?u:s);return{left:t.pageX-n.left+r.left,top:t.pageY-n.top+r.top}}var o,i,a,u,s;return{left:t.pageX,top:t.pageY}}(e,t)).left-n.left+r.left,pageY:o.top-n.top+r.top};var n,r,o,i,a,u},Yp=So.isContentEditableFalse,Gp=So.isContentEditableTrue,Jp=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},Qp=function(u,s){return function(e){if(0===e.button){var t=Tt.find(s.dom.getParents(e.target),oa.or(Yp,Gp));if(i=s.getBody(),Yp(a=t)&&a!==i){var n=s.dom.getPos(t),r=s.getBody(),o=s.getDoc().documentElement;u.element=t,u.screenX=e.screenX,u.screenY=e.screenY,u.maxX=(s.inline?r.scrollWidth:o.offsetWidth)-2,u.maxY=(s.inline?r.scrollHeight:o.offsetHeight)-2,u.relX=e.pageX-n.x,u.relY=e.pageY-n.y,u.width=t.offsetWidth,u.height=t.offsetHeight,u.ghost=function(e,t,n,r){var o=t.cloneNode(!0);e.dom.setStyles(o,{width:n,height:r}),e.dom.setAttrib(o,"data-mce-selected",null);var i=e.dom.create("div",{"class":"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return e.dom.setStyles(i,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),e.dom.setStyles(o,{margin:0,boxSizing:"border-box"}),i.appendChild(o),i}(s,t,u.width,u.height)}}var i,a}},Zp=function(l,f){return function(e){if(l.dragging&&(s=(i=f).selection,c=s.getSel().getRangeAt(0).startContainer,a=3===c.nodeType?c.parentNode:c,u=l.element,a!==u&&!i.dom.isChildOf(a,u)&&!Yp(a))){var t=(r=l.element,(o=r.cloneNode(!0)).removeAttribute("data-mce-selected"),o),n=f.fire("drop",{targetClone:t,clientX:e.clientX,clientY:e.clientY});n.isDefaultPrevented()||(t=n.targetClone,f.undoManager.transact(function(){Jp(l.element),f.insertContent(f.dom.getOuterHTML(t)),f._selectionOverrides.hideFakeCaret()}))}var r,o,i,a,u,s,c;eg(l)}},eg=function(e){e.dragging=!1,e.element=null,Jp(e.ghost)},tg=function(e){var t,n,r,o,i,a,g,h,v,u,s,c={};t=ci.DOM,a=document,n=Qp(c,e),g=c,h=e,v=ve.throttle(function(e,t){h._selectionOverrides.hideFakeCaret(),h.selection.placeCaretAt(e,t)},0),r=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m=Math.max(Math.abs(e.screenX-g.screenX),Math.abs(e.screenY-g.screenY));if(g.element&&!g.dragging&&10<m){if(h.fire("dragstart",{target:g.element}).isDefaultPrevented())return;g.dragging=!0,h.focus()}if(g.dragging){var p=(f=g,{pageX:(d=Xp(h,e)).pageX-f.relX,pageY:d.pageY+5});c=g.ghost,l=h.getBody(),c.parentNode!==l&&l.appendChild(c),t=g.ghost,n=p,r=g.width,o=g.height,i=g.maxX,a=g.maxY,s=u=0,t.style.left=n.pageX+"px",t.style.top=n.pageY+"px",n.pageX+r>i&&(u=n.pageX+r-i),n.pageY+o>a&&(s=n.pageY+o-a),t.style.width=r-u+"px",t.style.height=o-s+"px",v(e.clientX,e.clientY)}},o=Zp(c,e),u=c,i=function(){eg(u),u.dragging&&s.fire("dragend")},(s=e).on("mousedown",n),e.on("mousemove",r),e.on("mouseup",o),t.bind(a,"mousemove",r),t.bind(a,"mouseup",i),e.on("remove",function(){t.unbind(a,"mousemove",r),t.unbind(a,"mouseup",i)})},ng=function(e){var n;tg(e),(n=e).on("drop",function(e){var t="undefined"!=typeof e.clientX?n.getDoc().elementFromPoint(e.clientX,e.clientY):null;(Yp(t)||Yp(n.dom.getContentEditableParent(t)))&&e.preventDefault()})},rg=function(e){return Tt.reduce(e,function(e,t){return e.concat(function(t){var e=function(e){return Tt.map(e,function(e){return(e=Ki(e)).node=t,e})};if(So.isElement(t))return e(t.getClientRects());if(So.isText(t)){var n=t.ownerDocument.createRange();return n.setStart(t,0),n.setEnd(t,t.data.length),e(n.getClientRects())}}(t))},[])};(Wp=$p||($p={}))[Wp.Up=-1]="Up",Wp[Wp.Down=1]="Down";var og=function(o,i,a,e,u,t){var n,s,c=0,l=[],r=function(e){var t,n,r;for(r=rg([e]),-1===o&&(r=r.reverse()),t=0;t<r.length;t++)if(n=r[t],!a(n,s)){if(0<l.length&&i(n,Tt.last(l))&&c++,n.line=c,u(n))return!0;l.push(n)}};return(s=Tt.last(t.getClientRects()))&&(r(n=t.getNode()),function(e,t,n,r){for(;r=cs(r,e,$i,t);)if(n(r))return}(o,e,r,n)),l},ig=V.curry(og,$p.Up,Gi,Ji),ag=V.curry(og,$p.Down,Ji,Gi),ug=function(n){return function(e){return t=n,e.line>t;var t}},sg=function(n){return function(e){return t=n,e.line===t;var t}},cg=So.isContentEditableFalse,lg=cs,fg=function(e,t){return Math.abs(e.left-t)},dg=function(e,t){return Math.abs(e.right-t)},mg=function(e,t){return e>=t.left&&e<=t.right},pg=function(e,o){return Tt.reduce(e,function(e,t){var n,r;return n=Math.min(fg(e,o),dg(e,o)),r=Math.min(fg(t,o),dg(t,o)),mg(o,t)?t:mg(o,e)?e:r===n&&cg(t.node)?t:r<n?t:e})},gg=function(e,t,n,r){for(;r=lg(r,e,$i,t);)if(n(r))return},hg=function(e,t,n){var r,o,i,a,u,s,c,l,f=rg((o=e,Tt.filter(Tt.toArray(o.getElementsByTagName("*")),Qu))),d=Tt.filter(f,function(e){return n>=e.top&&n<=e.bottom});return(r=pg(d,t))&&(r=pg((u=e,l=function(t,e){var n;return n=Tt.filter(rg([e]),function(e){return!t(e,s)}),c=c.concat(n),0===n.length},(c=[]).push(s=r),gg($p.Up,u,V.curry(l,Gi),s.node),gg($p.Down,u,V.curry(l,Ji),s.node),c),t))&&Qu(r.node)?(a=t,{node:(i=r).node,before:fg(i,a)<dg(i,a)}):null},vg=function(i,a,e){return!e.collapsed&&H.foldl(e.getClientRects(),function(e,t){return e||(o=a,(r=i)>=(n=t).left&&r<=n.right&&o>=n.top&&o<=n.bottom);var n,r,o},!1)},yg=function(t,n){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},throttle:function(){var e=arguments;null===r&&(r=setTimeout(function(){t.apply(null,e),e=r=null},n))}}},bg=function(t){var e=yg(function(){if(!t.removed&&t.selection.getRng().collapsed){var e=Ga(t,t.selection.getRng(),!1);t.selection.setRng(e)}},0);t.on("focus",function(){e.throttle()}),t.on("blur",function(){e.cancel()})},Cg={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||this.metaKeyPressed(e)},metaKeyPressed:function(e){return de.mac?e.metaKey:e.ctrlKey&&!e.altKey}},xg=So.isContentEditableTrue,wg=So.isContentEditableFalse,Ng=Ns,Eg=ws,Sg=function(e,t){for(var n=e.getBody();t&&t!==n;){if(xg(t)||wg(t))return t;t=t.parentNode}return null},kg=function(p){var g,e,t,a=p.getBody(),o=Ju(p.getBody(),function(e){return p.dom.isBlock(e)},function(){return ap(p)}),h="sel-"+p.dom.uniqueId(),u=function(e){e&&p.selection.setRng(e)},s=function(){return p.selection.getRng()},v=function(e,t,n,r){return void 0===r&&(r=!0),p.fire("ShowCaret",{target:t,direction:e,before:n}).isDefaultPrevented()?null:(r&&p.selection.scrollIntoView(t,-1===e),o.show(n,t))},y=function(e,t){return t=vs(e,a,t),-1===e?Ta.fromRangeStart(t):Ta.fromRangeEnd(t)},n=function(e){return Ti(e)||Di(e)||Oi(e)},b=function(e){return n(e.startContainer)||n(e.endContainer)},c=function(e,t){var n,r,o,i,a,u,s,c,l,f,d=p.$,m=p.dom;if(!e)return null;if(e.collapsed){if(!b(e))if(!1===t){if(c=y(-1,e),Qu(c.getNode(!0)))return v(-1,c.getNode(!0),!1,!1);if(Qu(c.getNode()))return v(-1,c.getNode(),!c.isAtEnd(),!1)}else{if(c=y(1,e),Qu(c.getNode()))return v(1,c.getNode(),!c.isAtEnd(),!1);if(Qu(c.getNode(!0)))return v(1,c.getNode(!0),!1,!1)}return null}return i=e.startContainer,a=e.startOffset,u=e.endOffset,3===i.nodeType&&0===a&&wg(i.parentNode)&&(i=i.parentNode,a=m.nodeIndex(i),i=i.parentNode),1!==i.nodeType?null:(u===a+1&&(n=i.childNodes[a]),wg(n)?(l=f=n.cloneNode(!0),(s=p.fire("ObjectSelected",{target:n,targetClone:l})).isDefaultPrevented()?null:(r=pl(Fn.fromDom(p.getBody()),"#"+h).fold(function(){return d([])},function(e){return d([e.dom()])}),l=s.targetClone,0===r.length&&(r=d('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",h)).appendTo(p.getBody()),e=p.dom.createRng(),l===f&&de.ie?(r.empty().append('<p style="font-size: 0" data-mce-bogus="all">\xa0</p>').append(l),e.setStartAfter(r[0].firstChild.firstChild),e.setEndAfter(l)):(r.empty().append("\xa0").append(l).append("\xa0"),e.setStart(r[0].firstChild,1),e.setEnd(r[0].lastChild,0)),r.css({top:m.getPos(n,p.getBody()).y}),r[0].focus(),(o=p.selection.getSel()).removeAllRanges(),o.addRange(e),H.each(su(Fn.fromDom(p.getBody()),"*[data-mce-selected]"),function(e){sr.remove(e,"data-mce-selected")}),n.setAttribute("data-mce-selected","1"),g=n,C(),e)):null)},l=function(){g&&(g.removeAttribute("data-mce-selected"),pl(Fn.fromDom(p.getBody()),"#"+h).each(_c.remove),g=null)},C=function(){o.hide()};return de.ceFalse&&(function(){p.on("mouseup",function(e){var t=s();t.collapsed&&mp.isXYInContentArea(p,e.clientX,e.clientY)&&u(Ya(p,t,!1))}),p.on("click",function(e){var t;(t=Sg(p,e.target))&&(wg(t)&&(e.preventDefault(),p.focus()),xg(t)&&p.dom.isChildOf(t,p.selection.getNode())&&l())}),p.on("blur NewBlock",function(){l()}),p.on("ResizeWindow FullscreenStateChanged",function(){return o.reposition()});var n,r,i=function(e,t){var n,r,o=p.dom.getParent(e,p.dom.isBlock),i=p.dom.getParent(t,p.dom.isBlock);return!(!o||!p.dom.isChildOf(o,i)||!1!==wg(Sg(p,o)))||o&&(n=o,r=i,!(p.dom.getParent(n,p.dom.isBlock)===p.dom.getParent(r,p.dom.isBlock)))&&function(e){var t=qs(e);if(!e.firstChild)return!1;var n=Ta.before(e.firstChild),r=t.next(n);return r&&!Eg(r)&&!Ng(r)}(o)};r=!1,(n=p).on("touchstart",function(){r=!1}),n.on("touchmove",function(){r=!0}),n.on("touchend",function(e){var t=Sg(n,e.target);wg(t)&&(r||(e.preventDefault(),c(Xa(n,t))))}),p.on("mousedown",function(e){var t,n=e.target;if((n===a||"HTML"===n.nodeName||p.dom.isChildOf(n,a))&&!1!==mp.isXYInContentArea(p,e.clientX,e.clientY))if(t=Sg(p,n))wg(t)?(e.preventDefault(),c(Xa(p,t))):(l(),xg(t)&&e.shiftKey||vg(e.clientX,e.clientY,p.selection.getRng())||p.selection.placeCaretAt(e.clientX,e.clientY));else if(!1===Qu(n)){l(),C();var r=hg(a,e.clientX,e.clientY);if(r&&!i(e.target,r.node)){e.preventDefault();var o=v(1,r.node,r.before,!1);p.getBody().focus(),u(o)}}}),p.on("keypress",function(e){Cg.modifierPressed(e)||(e.keyCode,wg(p.selection.getNode())&&e.preventDefault())}),p.on("getSelectionRange",function(e){var t=e.range;if(g){if(!g.parentNode)return void(g=null);(t=t.cloneRange()).selectNode(g),e.range=t}}),p.on("setSelectionRange",function(e){var t;(t=c(e.range,e.forward))&&(e.range=t)}),p.on("AfterSetSelectionRange",function(e){var t,n=e.range;b(n)||C(),t=n.startContainer.parentNode,p.dom.hasClass(t,"mce-offscreen-selection")||l()}),p.on("copy",function(e){var t,n=e.clipboardData;if(!e.isDefaultPrevented()&&e.clipboardData&&!de.ie){var r=(t=p.dom.get(h))?t.getElementsByTagName("*")[0]:t;r&&(e.preventDefault(),n.clearData(),n.setData("text/html",r.outerHTML),n.setData("text/plain",r.outerText))}}),ng(p),bg(p)}(),e=p.contentStyles,t=".mce-content-body",e.push(o.getCss()),e.push(t+" .mce-offscreen-selection {position: absolute;left: -9999999999px;max-width: 1000000px;}"+t+" *[contentEditable=false] {cursor: default;}"+t+" *[contentEditable=true] {cursor: text;}")),{showCaret:v,showBlockCaretContainer:function(e){e.hasAttribute("data-mce-caret")&&(Pi(e),u(s()),p.selection.scrollIntoView(e[0]))},hideFakeCaret:C,destroy:function(){o.destroy(),g=null}}},Tg=Dt.each,Ag=function(e){return 0===e.indexOf("data-")||0===e.indexOf("aria-")},Rg=function(e){return e.replace(/<!--|-->/g,"")},_g=function(e,t,n){var r,o,i,a,u=1;for(a=e.getShortEndedElements(),(i=/<([!?\/])?([A-Za-z0-9\-_\:\.]+)((?:\s+[^"\'>]+(?:(?:"[^"]*")|(?:\'[^\']*\')|[^>]*))*|\/|\s+)>/g).lastIndex=r=n;o=i.exec(t);){if(r=i.lastIndex,"/"===o[1])u--;else if(!o[1]){if(o[2]in a)continue;u++}if(0===u)break}return r};function Bg(z,U){void 0===U&&(U=Go());var t=function(){};!1!==(z=z||{}).fix_self_closing&&(z.fix_self_closing=!0),Tg("comment cdata text start end pi doctype".split(" "),function(e){e&&(self[e]=z[e]||t)});var q=z.comment?z.comment:t,V=z.cdata?z.cdata:t,H=z.text?z.text:t,j=z.start?z.start:t,$=z.end?z.end:t,W=z.pi?z.pi:t,K=z.doctype?z.doctype:t;return{parse:function(e){var t,n,r,c,o,i,a,l,u,s,f,d,m,p,g,h,v,y,b,C,x,w,N,E,S,k,T,A,R,_=0,B=[],D=0,O=zo.decode,P=Dt.makeMap("src,href,data,background,formaction,poster"),L=/((java|vb)script|mhtml):/i,I=/^data:/i,M=function(e){var t,n;for(t=B.length;t--&&B[t].name!==e;);if(0<=t){for(n=B.length-1;t<=n;n--)(e=B[n]).valid&&$(e.name);B.length=t}},F=function(e,t,n,r,o){var i,a;if(n=(t=t.toLowerCase())in f?t:O(n||r||o||""),m&&!l&&!1===Ag(t)){if(!(i=y[t])&&b){for(a=b.length;a--&&!(i=b[a]).pattern.test(t););-1===a&&(i=null)}if(!i)return;if(i.validValues&&!(n in i.validValues))return}if(P[t]&&!z.allow_script_urls){var u=n.replace(/[\s\u0000-\u001F]+/g,"");try{u=decodeURIComponent(u)}catch(s){u=unescape(u)}if(L.test(u))return;if(!z.allow_html_data_urls&&I.test(u)&&!/^data:image\//i.test(u))return}l&&(t in P||0===t.indexOf("on"))||(c.map[t]=n,c.push({name:t,value:n}))};for(S=new RegExp("<(?:(?:!--([\\w\\W]*?)--\x3e)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:!DOCTYPE([\\w\\W]*?)>)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)((?:\\s+[^\"'>]+(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>]*))*|\\/|\\s+)>))","g"),k=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,s=U.getShortEndedElements(),E=z.self_closing_elements||U.getSelfClosingElements(),f=U.getBoolAttrs(),m=z.validate,u=z.remove_internals,R=z.fix_self_closing,T=U.getSpecialElements(),N=e+">";t=S.exec(N);){if(_<t.index&&H(O(e.substr(_,t.index-_))),n=t[6])":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),M(n);else if(n=t[7]){if(t.index+t[0].length>e.length){H(O(e.substr(t.index))),_=t.index+t[0].length;continue}if(":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),d=n in s,R&&E[n]&&0<B.length&&B[B.length-1].name===n&&M(n),!m||(p=U.getElementRule(n))){if(g=!0,m&&(y=p.attributes,b=p.attributePatterns),(v=t[8])?((l=-1!==v.indexOf("data-mce-type"))&&u&&(g=!1),(c=[]).map={},v.replace(k,F)):(c=[]).map={},m&&!l){if(C=p.attributesRequired,x=p.attributesDefault,w=p.attributesForced,p.removeEmptyAttrs&&!c.length&&(g=!1),w)for(o=w.length;o--;)a=(h=w[o]).name,"{$uid}"===(A=h.value)&&(A="mce_"+D++),c.map[a]=A,c.push({name:a,value:A});if(x)for(o=x.length;o--;)(a=(h=x[o]).name)in c.map||("{$uid}"===(A=h.value)&&(A="mce_"+D++),c.map[a]=A,c.push({name:a,value:A}));if(C){for(o=C.length;o--&&!(C[o]in c.map););-1===o&&(g=!1)}if(h=c.map["data-mce-bogus"]){if("all"===h){_=_g(U,e,S.lastIndex),S.lastIndex=_;continue}g=!1}}g&&j(n,c,d)}else g=!1;if(r=T[n]){r.lastIndex=_=t.index+t[0].length,(t=r.exec(e))?(g&&(i=e.substr(_,t.index-_)),_=t.index+t[0].length):(i=e.substr(_),_=e.length),g&&(0<i.length&&H(i,!0),$(n)),S.lastIndex=_;continue}d||(v&&v.indexOf("/")===v.length-1?g&&$(n):B.push({name:n,valid:g}))}else(n=t[1])?(">"===n.charAt(0)&&(n=" "+n),z.allow_conditional_comments||"[if"!==n.substr(0,3).toLowerCase()||(n=" "+n),q(n)):(n=t[2])?V(Rg(n)):(n=t[3])?K(n):(n=t[4])&&W(n,t[5]);_=t.index+t[0].length}for(_<e.length&&H(O(e.substr(_))),o=B.length-1;0<=o;o--)(n=B[o]).valid&&$(n.name)}}}(Bg||(Bg={})).findEndTag=_g;var Dg=Bg,Og=function(e,t){var n,r,o,i,a,u,s,c,l=t,f=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,d=e.schema;for(u=e.getTempAttrs(),s=l,c=new RegExp(["\\s?("+u.join("|")+')="[^"]+"'].join("|"),"gi"),l=s.replace(c,""),a=d.getShortEndedElements();i=f.exec(l);)r=f.lastIndex,o=i[0].length,n=a[i[1]]?r:Dg.findEndTag(d,l,r),l=l.substring(0,r-o)+l.substring(n),f.lastIndex=r-o;return wi(l)},Pg={trimExternal:Og,trimInternal:Og},Lg=0,Ig=2,Mg=1,Fg=function(p,g){var e=p.length+g.length+2,h=new Array(e),v=new Array(e),c=function(e,t,n,r,o){var i=l(e,t,n,r);if(null===i||i.start===t&&i.diag===t-r||i.end===e&&i.diag===e-n)for(var a=e,u=n;a<t||u<r;)a<t&&u<r&&p[a]===g[u]?(o.push([0,p[a]]),++a,++u):r-n<t-e?(o.push([2,p[a]]),++a):(o.push([1,g[u]]),++u);else{c(e,i.start,n,i.start-i.diag,o);for(var s=i.start;s<i.end;++s)o.push([0,p[s]]);c(i.end,t,i.end-i.diag,r,o)}},y=function(e,t,n,r){for(var o=e;o-t<r&&o<n&&p[o]===g[o-t];)++o;return{start:e,end:o,diag:t}},l=function(e,t,n,r){var o=t-e,i=r-n;if(0===o||0===i)return null;var a,u,s,c,l,f=o-i,d=i+o,m=(d%2==0?d:d+1)/2;for(h[1+m]=e,v[1+m]=t+1,a=0;a<=m;++a){for(u=-a;u<=a;u+=2){for(s=u+m,u===-a||u!==a&&h[s-1]<h[s+1]?h[s]=h[s+1]:h[s]=h[s-1]+1,l=(c=h[s])-e+n-u;c<t&&l<r&&p[c]===g[l];)h[s]=++c,++l;if(f%2!=0&&f-a<=u&&u<=f+a&&v[s-f]<=h[s])return y(v[s-f],u+e-n,t,r)}for(u=f-a;u<=f+a;u+=2){for(s=u+m-f,u===f-a||u!==f+a&&v[s+1]<=v[s-1]?v[s]=v[s+1]-1:v[s]=v[s-1],l=(c=v[s]-1)-e+n-u;e<=c&&n<=l&&p[c]===g[l];)v[s]=c--,l--;if(f%2==0&&-a<=u&&u<=a&&v[s]<=h[s+f])return y(v[s],u+e-n,t,r)}}},t=[];return c(0,p.length,0,g.length,t),t},zg=function(e){return 1===e.nodeType?e.outerHTML:3===e.nodeType?zo.encodeRaw(e.data,!1):8===e.nodeType?"\x3c!--"+e.data+"--\x3e":""},Ug=function(e,t,n){var r=function(e){var t,n,r;for(r=document.createElement("div"),t=document.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t}(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},qg=function(e){return Tt.filter(Tt.map(e.childNodes,zg),function(e){return 0<e.length})},Vg=function(e,t){var n,r,o,i=Tt.map(t.childNodes,zg);return n=Fg(i,e),r=t,o=0,Tt.each(n,function(e){e[0]===Lg?o++:e[0]===Mg?(Ug(r,e[1],o),o++):e[0]===Ig&&function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}}(r,o)}),t},Hg=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},jg=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},$g=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},Wg={createFragmentedLevel:Hg,createCompleteLevel:jg,createFromEditor:function(n){var e,t,r;return e=qg(n.getBody()),-1!==(t=(r=H.bind(e,function(e){var t=Pg.trimInternal(n.serializer,e);return 0<t.length?[t]:[]})).join("")).indexOf("</iframe>")?Hg(r):jg(t)},applyToEditor:function(e,t,n){"fragmented"===t.type?Vg(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw"}),e.selection.moveToBookmark(n?t.beforeBookmark:t.bookmark)},isEq:function(e,t){return!!e&&!!t&&$g(e)===$g(t)}};function Kg(u){var s,r,o=this,c=0,l=[],t=0,f=function(){return 0===t},i=function(e){f()&&(o.typing=e)},d=function(e){u.setDirty(e)},a=function(e){i(!1),o.add({},e)},n=function(){o.typing&&(i(!1),o.add())};return u.on("init",function(){o.add()}),u.on("BeforeExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&(n(),o.beforeChange())}),u.on("ExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&a(e)}),u.on("ObjectResizeStart Cut",function(){o.beforeChange()}),u.on("SaveContent ObjectResized blur",a),u.on("DragEnd",a),u.on("KeyUp",function(e){var t=e.keyCode;e.isDefaultPrevented()||((33<=t&&t<=36||37<=t&&t<=40||45===t||e.ctrlKey)&&(a(),u.nodeChanged()),46!==t&&8!==t||u.nodeChanged(),r&&o.typing&&!1===Wg.isEq(Wg.createFromEditor(u),l[0])&&(!1===u.isDirty()&&(d(!0),u.fire("change",{level:l[0],lastLevel:null})),u.fire("TypingUndo"),r=!1,u.nodeChanged()))}),u.on("KeyDown",function(e){var t=e.keyCode;if(!e.isDefaultPrevented())if(33<=t&&t<=36||37<=t&&t<=40||45===t)o.typing&&a(e);else{var n=e.ctrlKey&&!e.altKey||e.metaKey;!(t<16||20<t)||224===t||91===t||o.typing||n||(o.beforeChange(),i(!0),o.add({},e),r=!0)}}),u.on("MouseDown",function(e){o.typing&&a(e)}),u.on("input",function(e){var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data)&&a(e)}),u.addShortcut("meta+z","","Undo"),u.addShortcut("meta+y,meta+shift+z","","Redo"),u.on("AddUndo Undo Redo ClearUndos",function(e){e.isDefaultPrevented()||u.nodeChanged()}),o={data:l,typing:!1,beforeChange:function(){f()&&(s=pc.getUndoBookmark(u.selection))},add:function(e,t){var n,r,o,i=u.settings;if(o=Wg.createFromEditor(u),e=e||{},e=Dt.extend(e,o),!1===f()||u.removed)return null;if(r=l[c],u.fire("BeforeAddUndo",{level:e,lastLevel:r,originalEvent:t}).isDefaultPrevented())return null;if(r&&Wg.isEq(r,e))return null;if(l[c]&&(l[c].beforeBookmark=s),i.custom_undo_redo_levels&&l.length>i.custom_undo_redo_levels){for(n=0;n<l.length-1;n++)l[n]=l[n+1];l.length--,c=l.length}e.bookmark=pc.getUndoBookmark(u.selection),c<l.length-1&&(l.length=c+1),l.push(e),c=l.length-1;var a={level:e,lastLevel:r,originalEvent:t};return u.fire("AddUndo",a),0<c&&(d(!0),u.fire("change",a)),e},undo:function(){var e;return o.typing&&(o.add(),o.typing=!1,i(!1)),0<c&&(e=l[--c],Wg.applyToEditor(u,e,!0),d(!0),u.fire("undo",{level:e})),e},redo:function(){var e;return c<l.length-1&&(e=l[++c],Wg.applyToEditor(u,e,!1),d(!0),u.fire("redo",{level:e})),e},clear:function(){l=[],c=0,o.typing=!1,o.data=l,u.fire("ClearUndos")},hasUndo:function(){return 0<c||o.typing&&l[0]&&!Wg.isEq(Wg.createFromEditor(u),l[0])},hasRedo:function(){return c<l.length-1&&!o.typing},transact:function(e){return n(),o.beforeChange(),o.ignore(e),o.add()},ignore:function(e){try{t++,e()}finally{t--}},extra:function(e,t){var n,r;o.transact(e)&&(r=l[c].bookmark,n=l[c-1],Wg.applyToEditor(u,n,!0),o.transact(t)&&(l[c-1].beforeBookmark=r))}}}var Xg,Yg,Gg=function(e){return e&&/^(IMG)$/.test(e.nodeName)},Jg=function(e){return e&&3===e.nodeType&&/^([\t \r\n]+|)$/.test(e.nodeValue)},Qg=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},Zg={isInlineBlock:Gg,moveStart:function(e,t,n){var r,o,i,a=n.startContainer,u=n.startOffset;if((n.startContainer!==n.endContainer||!Gg(n.startContainer.childNodes[n.startOffset]))&&(3===a.nodeType&&u>=a.nodeValue.length&&(u=e.nodeIndex(a),a=a.parentNode),1===a.nodeType))for(u<(i=a.childNodes).length?r=new Zr(a=i[u],e.getParent(a,e.isBlock)):(r=new Zr(a=i[i.length-1],e.getParent(a,e.isBlock))).next(!0),o=r.current();o;o=r.next())if(3===o.nodeType&&!Jg(o))return n.setStart(o,0),void t.setRng(n)},getNonWhiteSpaceSibling:function(e,t,n){if(e)for(t=t?"nextSibling":"previousSibling",e=n?e:e[t];e;e=e[t])if(1===e.nodeType||!Jg(e))return e},isTextBlock:function(e,t){return t.nodeType&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},isValid:function(e,t,n){return e.schema.isValidChild(t,n)},isWhiteSpaceNode:Jg,replaceVars:function(e,n){return"string"!=typeof e?e=e(n):n&&(e=e.replace(/%(\w+)/g,function(e,t){return n[t]||e})),e},isEq:function(e,t){return t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},normalizeStyleValue:Qg,getStyle:function(e,t,n){return Qg(e,e.getStyle(t,n),n)},getTextDecoration:function(t,e){var n;return t.getParent(e,function(e){return(n=t.getStyle(e,"text-decoration"))&&"none"!==n}),n},getParents:function(e,t,n){return e.getParents(t,n,e.getRoot())}},eh=Ec.isBookmarkNode,th=Zg.getParents,nh=Zg.isWhiteSpaceNode,rh=Zg.isTextBlock,oh=function(e,t){for(void 0===t&&(t=3===e.nodeType?e.length:e.childNodes.length);e&&e.hasChildNodes();)(e=e.childNodes[t])&&(t=3===e.nodeType?e.length:e.childNodes.length);return{node:e,offset:t}},ih=function(e,t){for(var n=t;n;){if(1===n.nodeType&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},ah=function(e,t,n,r){var o,i,a=n.nodeValue;return void 0===r&&(r=e?a.length:0),e?(o=a.lastIndexOf(" ",r),-1===(o=(i=a.lastIndexOf("\xa0",r))<o?o:i)||t||o++):(o=a.indexOf(" ",r),i=a.indexOf("\xa0",r),o=-1!==o&&(-1===i||o<i)?o:i),o},uh=function(e,t,n,r,o,i){var a,u,s,c;if(3===n.nodeType){if(-1!==(s=ah(o,i,n,r)))return{container:n,offset:s};c=n}for(a=new Zr(n,e.getParent(n,e.isBlock)||t);u=a[o?"prev":"next"]();)if(3===u.nodeType){if(-1!==(s=ah(o,i,c=u)))return{container:u,offset:s}}else if(e.isBlock(u))break;if(c)return{container:c,offset:r=o?0:c.length}},sh=function(e,t,n,r,o){var i,a,u,s;for(3===r.nodeType&&0===r.nodeValue.length&&r[o]&&(r=r[o]),i=th(e,r),a=0;a<i.length;a++)for(u=0;u<t.length;u++)if(!("collapsed"in(s=t[u])&&s.collapsed!==n.collapsed)&&e.is(i[a],s.selector))return i[a];return r},ch=function(t,e,n,r){var o,i=t.dom,a=i.getRoot();if(e[0].wrapper||(o=i.getParent(n,e[0].block,a)),!o){var u=i.getParent(n,"LI,TD,TH");o=i.getParent(3===n.nodeType?n.parentNode:n,function(e){return e!==a&&rh(t,e)},u)}if(o&&e[0].wrapper&&(o=th(i,o,"ul,ol").reverse()[0]||o),!o)for(o=n;o[r]&&!i.isBlock(o[r])&&(o=o[r],!Zg.isEq(o,"br")););return o||n},lh=function(e,t,n,r,o,i,a){var u,s,c,l,f,d;if(u=s=a?n:o,l=a?"previousSibling":"nextSibling",f=e.getRoot(),3===u.nodeType&&!nh(u)&&(a?0<r:i<u.nodeValue.length))return u;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(c=s[l];c;c=c[l])if(!eh(c)&&!nh(c)&&("BR"!==(d=c).nodeName||!d.getAttribute("data-mce-bogus")||d.nextSibling))return s;if(s===f||s.parentNode===f){u=s;break}s=s.parentNode}return u},fh=function(e,t,n,r){var o,i=t.startContainer,a=t.startOffset,u=t.endContainer,s=t.endOffset,c=e.dom;return 1===i.nodeType&&i.hasChildNodes()&&3===(i=Zi(i,a)).nodeType&&(a=0),1===u.nodeType&&u.hasChildNodes()&&3===(u=Zi(u,t.collapsed?s:s-1)).nodeType&&(s=u.nodeValue.length),i=ih(c,i),u=ih(c,u),(eh(i.parentNode)||eh(i))&&3===(i=(i=eh(i)?i:i.parentNode).nextSibling||i).nodeType&&(a=0),(eh(u.parentNode)||eh(u))&&3===(u=(u=eh(u)?u:u.parentNode).previousSibling||u).nodeType&&(s=u.length),n[0].inline&&(t.collapsed&&((o=uh(c,e.getBody(),i,a,!0,r))&&(i=o.container,a=o.offset),(o=uh(c,e.getBody(),u,s,!1,r))&&(u=o.container,s=o.offset)),u=r?u:function(e,t){var n=oh(e,t);if(n.node){for(;n.node&&0===n.offset&&n.node.previousSibling;)n=oh(n.node.previousSibling);n.node&&0<n.offset&&3===n.node.nodeType&&" "===n.node.nodeValue.charAt(n.offset-1)&&1<n.offset&&(e=n.node).splitText(n.offset-1)}return e}(u,s)),(n[0].inline||n[0].block_expand)&&(n[0].inline&&3===i.nodeType&&0!==a||(i=lh(c,n,i,a,u,s,!0)),n[0].inline&&3===u.nodeType&&s!==u.nodeValue.length||(u=lh(c,n,i,a,u,s,!1))),n[0].selector&&!1!==n[0].expand&&!n[0].inline&&(i=sh(c,n,t,i,"previousSibling"),u=sh(c,n,t,u,"nextSibling")),(n[0].block||n[0].selector)&&(i=ch(e,n,i,"previousSibling"),u=ch(e,n,u,"nextSibling"),n[0].block&&(c.isBlock(i)||(i=lh(c,n,i,a,u,s,!0)),c.isBlock(u)||(u=lh(c,n,i,a,u,s,!1)))),1===i.nodeType&&(a=c.nodeIndex(i),i=i.parentNode),1===u.nodeType&&(s=c.nodeIndex(u)+1,u=u.parentNode),{startContainer:i,startOffset:a,endContainer:u,endOffset:s}},dh=Zg.isEq,mh=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++)if(!1===r[o].inherit&&e.dom.is(t,r[o].selector))return!0;return!1},ph=function(t,e,n,r){var o=t.dom.getRoot();return e!==o&&(e=t.dom.getParent(e,function(e){return!!mh(t,e,n)||e.parentNode===o||!!vh(t,e,n,r,!0)}),vh(t,e,n,r))},gh=function(e,t,n){return!!dh(t,n.inline)||!!dh(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0)},hh=function(e,t,n,r,o,i){var a,u,s,c=n[r];if(n.onmatch)return n.onmatch(t,n,r);if(c)if("undefined"==typeof c.length){for(a in c)if(c.hasOwnProperty(a)){if(u="attributes"===r?e.getAttrib(t,a):Zg.getStyle(e,t,a),o&&!u&&!n.exact)return;if((!o||n.exact)&&!dh(u,Zg.normalizeStyleValue(e,Zg.replaceVars(c[a],i),a)))return}}else for(s=0;s<c.length;s++)if("attributes"===r?e.getAttrib(t,c[s]):Zg.getStyle(e,t,c[s]))return n;return n},vh=function(e,t,n,r,o){var i,a,u,s,c=e.formatter.get(n),l=e.dom;if(c&&t)for(a=0;a<c.length;a++)if(i=c[a],gh(e.dom,t,i)&&hh(l,t,i,"attributes",o,r)&&hh(l,t,i,"styles",o,r)){if(s=i.classes)for(u=0;u<s.length;u++)if(!e.dom.hasClass(t,s[u]))return;return i}},yh={matchNode:vh,matchName:gh,match:function(e,t,n,r){var o;return r?ph(e,r,t,n):(r=e.selection.getNode(),!!ph(e,r,t,n)||!((o=e.selection.getStart())===r||!ph(e,o,t,n)))},matchAll:function(r,o,i){var e,a=[],u={};return e=r.selection.getStart(),r.dom.getParent(e,function(e){var t,n;for(t=0;t<o.length;t++)n=o[t],!u[n]&&vh(r,e,n,i)&&(u[n]=!0,a.push(n))},r.dom.getRoot()),a},canApply:function(e,t){var n,r,o,i,a,u=e.formatter.get(t),s=e.dom;if(u)for(n=e.selection.getStart(),r=Zg.getParents(s,n),i=u.length-1;0<=i;i--){if(!(a=u[i].selector)||u[i].defaultBlock)return!0;for(o=r.length-1;0<=o;o--)if(s.is(r[o],a))return!0}return!1},matchesUnInheritedFormatSelector:mh},bh=function(e,t){return e.splitText(t)},Ch={split:function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&So.isText(t)?0<n&&n<t.nodeValue.length&&(t=(r=bh(t,n)).previousSibling,n<o?(t=r=bh(r,o-=n).previousSibling,o=r.nodeValue.length,n=0):o=0):(So.isText(t)&&0<n&&n<t.nodeValue.length&&(t=bh(t,n),n=0),So.isText(r)&&0<o&&o<r.nodeValue.length&&(o=(r=bh(r,o).previousSibling).nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}}},xh=xi,wh="_mce_caret",Nh=function(e){return 0<function(e){for(var t=[];e;){if(3===e.nodeType&&e.nodeValue!==xh||1<e.childNodes.length)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t}(e).length},Eh=function(e){var t;if(e)for(e=(t=new Zr(e,e)).current();e;e=t.next())if(3===e.nodeType)return e;return null},Sh=function(e){var t=Fn.fromTag("span");return sr.setAll(t,{id:wh,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Lu.append(t,Fn.fromText(xh)),t},kh=function(e,t,n,r){var o,i,a,u;o=t.getRng(!0),i=e.getParent(n,e.isBlock),Nh(n)?(!1!==r&&(o.setStartBefore(n),o.setEndBefore(n)),e.remove(n)):((u=Eh(n))&&u.nodeValue.charAt(0)===xh&&u.deleteData(0,1),a=u,o.startContainer===a&&0<o.startOffset&&o.setStart(a,o.startOffset-1),o.endContainer===a&&0<o.endOffset&&o.setEnd(a,o.endOffset-1),e.remove(n,!0)),i&&e.isEmpty(i)&&Pc(Fn.fromDom(i)),t.setRng(o)},Th=function(e,t,n,r,o){if(r)kh(t,n,r,o);else if(!(r=vc(e,n.getStart())))for(;r=t.get(wh);)kh(t,n,r,!1)},Ah=function(e,t,n){var r=e.dom,o=r.getParent(n,oa.curry(Zg.isTextBlock,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(Oc(Fn.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},Rh=function(e,t){return e.appendChild(t),t},_h=function(e,t){var n=H.foldr(e,function(e,t){return Rh(e,t.cloneNode(!1))},t);return Rh(n,n.ownerDocument.createTextNode(xh))},Bh=function(e){var i=e.dom,a=e.selection,u=e.getBody();e.on("mouseup keydown",function(e){var t,n,r,o;t=u,n=i,r=a,o=e.keyCode,Th(t,n,r,null,!1),8===o&&r.isCollapsed()&&r.getStart().innerHTML===xh&&Th(t,n,r,vc(t,r.getStart())),37!==o&&39!==o||Th(t,n,r,vc(t,r.getStart()))})},Dh=function(e,t){return e.schema.getTextInlineElements().hasOwnProperty(Yn.name(t))&&!hc(t.dom())&&!So.isBogus(t.dom())},Oh={},Ph=Tt.filter,Lh=Tt.each;Yg=function(e){var t,n,r=e.selection.getRng();t=So.matchNodeNames("pre"),r.collapsed||(n=e.selection.getSelectedBlocks(),Lh(Ph(Ph(n,t),function(e){return t(e.previousSibling)&&-1!==Tt.indexOf(n,e.previousSibling)}),function(e){var t,n;t=e.previousSibling,Jt(n=e).remove(),Jt(t).append("<br><br>").append(n.childNodes)}))},Oh[Xg="pre"]||(Oh[Xg]=[]),Oh[Xg].push(Yg);var Ih=function(e,t){Lh(Oh[e],function(e){e(t)})},Mh=Dt.each,Fh={walk:function(e,t,o){var n,r,i,a,u,s,c,l=t.startContainer,f=t.startOffset,d=t.endContainer,m=t.endOffset;if(0<(c=e.select("td[data-mce-selected],th[data-mce-selected]")).length)Mh(c,function(e){o([e])});else{var p,g,h,v=function(e){var t;return 3===(t=e[0]).nodeType&&t===l&&f>=t.nodeValue.length&&e.splice(0,1),t=e[e.length-1],0===m&&0<e.length&&t===d&&3===t.nodeType&&e.splice(e.length-1,1),e},y=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},b=function(e,t){do{if(e.parentNode===t)return e;e=e.parentNode}while(e)},C=function(e,t,n){var r=n?"nextSibling":"previousSibling";for(u=(a=e).parentNode;a&&a!==t;a=u)u=a.parentNode,(s=y(a===e?a:a[r],r)).length&&(n||s.reverse(),o(v(s)))};if(1===l.nodeType&&l.hasChildNodes()&&(l=l.childNodes[f]),1===d.nodeType&&d.hasChildNodes()&&(g=m,h=(p=d).childNodes,--g>h.length-1?g=h.length-1:g<0&&(g=0),d=h[g]||p),l===d)return o(v([l]));for(n=e.findCommonAncestor(l,d),a=l;a;a=a.parentNode){if(a===d)return C(l,n,!0);if(a===n)break}for(a=d;a;a=a.parentNode){if(a===l)return C(d,n);if(a===n)break}r=b(l,n)||l,i=b(d,n)||d,C(l,r,!0),(s=y(r===l?r:r.nextSibling,"nextSibling",i===d?i.nextSibling:i)).length&&o(v(s)),C(d,i)}}},zh=/^(src|href|style)$/,Uh=Dt.each,qh=Zg.isEq,Vh=function(e){return/^(TH|TD)$/.test(e.nodeName)},Hh=function(e,t,n){var r,o,i;return r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"],So.isElement(r)&&(i=r.childNodes.length-1,!n&&o&&o--,r=r.childNodes[i<o?i:o]),So.isText(r)&&n&&o>=r.nodeValue.length&&(r=new Zr(r,e.getBody()).next()||r),So.isText(r)&&!n&&0===o&&(r=new Zr(r,e.getBody()).prev()||r),r},jh=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},$h=function(e,t,n,r){return!(t=Zg.getNonWhiteSpaceSibling(t,n,r))||"BR"===t.nodeName||e.isBlock(t)},Wh=function(e,n,r,o,i){var t,a,u,s,c,l,f,d,m,p,g,h,v,y,b=e.dom;if(c=b,!(qh(l=o,(f=n).inline)||qh(l,f.block)||(f.selector?So.isElement(l)&&c.is(l,f.selector):void 0)||(s=o,n.links&&"A"===s.tagName)))return!1;if("all"!==n.remove)for(Uh(n.styles,function(e,t){e=Zg.normalizeStyleValue(b,Zg.replaceVars(e,r),t),"number"==typeof t&&(t=e,i=0),(n.remove_similar||!i||qh(Zg.getStyle(b,i,t),e))&&b.setStyle(o,t,""),u=1}),u&&""===b.getAttrib(o,"style")&&(o.removeAttribute("style"),o.removeAttribute("data-mce-style")),Uh(n.attributes,function(e,t){var n;if(e=Zg.replaceVars(e,r),"number"==typeof t&&(t=e,i=0),!i||qh(b.getAttrib(i,t),e)){if("class"===t&&(e=b.getAttrib(o,t))&&(n="",Uh(e.split(/\s+/),function(e){/mce\-\w+/.test(e)&&(n+=(n?" ":"")+e)}),n))return void b.setAttrib(o,t,n);"class"===t&&o.removeAttribute("className"),zh.test(t)&&o.removeAttribute("data-mce-"+t),o.removeAttribute(t)}}),Uh(n.classes,function(e){e=Zg.replaceVars(e,r),i&&!b.hasClass(i,e)||b.removeClass(o,e)}),a=b.getAttribs(o),t=0;t<a.length;t++){var C=a[t].nodeName;if(0!==C.indexOf("_")&&0!==C.indexOf("data-"))return!1}return"none"!==n.remove?(d=e,p=n,h=(m=o).parentNode,v=d.dom,y=d.settings.forced_root_block,p.block&&(y?h===v.getRoot()&&(p.list_block&&qh(m,p.list_block)||Uh(Dt.grep(m.childNodes),function(e){Zg.isValid(d,y,e.nodeName.toLowerCase())?g?g.appendChild(e):(g=jh(v,e,y),v.setAttribs(g,d.settings.forced_root_block_attrs)):g=0})):v.isBlock(m)&&!v.isBlock(h)&&($h(v,m,!1)||$h(v,m.firstChild,!0,1)||m.insertBefore(v.create("br"),m.firstChild),$h(v,m,!0)||$h(v,m.lastChild,!1,1)||m.appendChild(v.create("br")))),p.selector&&p.inline&&!qh(p.inline,m)||v.remove(m,1),!0):void 0},Kh={removeFormat:Wh,remove:function(s,c,l,e,f){var t,n,d=s.formatter.get(c),m=d[0],a=!0,u=s.dom,r=s.selection,o=function(e){var n,t,r,o,i,a,u=(n=s,t=e,r=c,o=l,i=f,Uh(Zg.getParents(n.dom,t.parentNode).reverse(),function(e){var t;a||"_start"===e.id||"_end"===e.id||(t=yh.matchNode(n,e,r,o,i))&&!1!==t.split&&(a=e)}),a);return function(e,t,n,r,o,i,a,u){var s,c,l,f,d,m,p=e.dom;if(n){for(m=n.parentNode,s=r.parentNode;s&&s!==m;s=s.parentNode){for(c=p.clone(s,!1),d=0;d<t.length;d++)if(Wh(e,t[d],u,c,c)){c=0;break}c&&(l&&c.appendChild(l),f||(f=c),l=c)}!i||a.mixed&&p.isBlock(n)||(r=p.split(n,r)),l&&(o.parentNode.insertBefore(l,o),f.appendChild(o))}return r}(s,d,u,e,e,!0,m,l)},p=function(e){var t,n,r,o,i;if(So.isElement(e)&&u.getContentEditable(e)&&(o=a,a="true"===u.getContentEditable(e),i=!0),t=Dt.grep(e.childNodes),a&&!i)for(n=0,r=d.length;n<r&&!Wh(s,d[n],l,e,e);n++);if(m.deep&&t.length){for(n=0,r=t.length;n<r;n++)p(t[n]);i&&(a=o)}},i=function(e){var t=u.get(e?"_start":"_end"),n=t[e?"firstChild":"lastChild"];return Ec.isBookmarkNode(n)&&(n=n[e?"firstChild":"lastChild"]),So.isText(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),u.remove(t,!0),n},g=function(e){var t,n,r=e.commonAncestorContainer;if(e=fh(s,e,d,!0),m.split){if((t=Hh(s,e,!0))!==(n=Hh(s,e))){if(/^(TR|TH|TD)$/.test(t.nodeName)&&t.firstChild&&(t="TR"===t.nodeName?t.firstChild.firstChild||t:t.firstChild||t),r&&/^T(HEAD|BODY|FOOT|R)$/.test(r.nodeName)&&Vh(n)&&n.firstChild&&(n=n.firstChild||n),u.isChildOf(t,n)&&t!==n&&!u.isBlock(n)&&!Vh(t)&&!Vh(n))return t=jh(u,t,"span",{id:"_start","data-mce-type":"bookmark"}),o(t),void(t=i(!0));t=jh(u,t,"span",{id:"_start","data-mce-type":"bookmark"}),n=jh(u,n,"span",{id:"_end","data-mce-type":"bookmark"}),o(t),o(n),t=i(!0),n=i()}else t=n=o(t);e.startContainer=t.parentNode?t.parentNode:t,e.startOffset=u.nodeIndex(t),e.endContainer=n.parentNode?n.parentNode:n,e.endOffset=u.nodeIndex(n)+1}Fh.walk(u,e,function(e){Uh(e,function(e){p(e),So.isElement(e)&&"underline"===s.dom.getStyle(e,"text-decoration")&&e.parentNode&&"underline"===Zg.getTextDecoration(u,e.parentNode)&&Wh(s,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:"underline"}},null,e)})})};if(e)e.nodeType?((n=u.createRng()).setStartBefore(e),n.setEndAfter(e),g(n)):g(e);else if("false"!==u.getContentEditable(r.getNode()))r.isCollapsed()&&m.inline&&!u.select("td[data-mce-selected],th[data-mce-selected]").length?function(e,t,n,r){var o,i,a,u,s,c,l,f=e.dom,d=e.selection,m=[],p=d.getRng();for(o=p.startContainer,i=p.startOffset,3===(s=o).nodeType&&(i!==o.nodeValue.length&&(u=!0),s=s.parentNode);s;){if(yh.matchNode(e,s,t,n,r)){c=s;break}s.nextSibling&&(u=!0),m.push(s),s=s.parentNode}if(c)if(u){a=d.getBookmark(),p.collapse(!0);var g=fh(e,p,e.formatter.get(t),!0);g=Ch.split(g),e.formatter.remove(t,n,g),d.moveToBookmark(a)}else{l=vc(e.getBody(),c);var h=Sh(!1).dom(),v=_h(m,h);Ah(e,h,l||c),kh(f,d,l,!1),d.setCursorLocation(v,1),f.isEmpty(c)&&f.remove(c)}}(s,c,l,f):(t=r.getBookmark(),g(r.getRng()),r.moveToBookmark(t),m.inline&&yh.match(s,c,l,r.getStart())&&Zg.moveStart(u,r,r.getRng()),s.nodeChanged());else{e=r.getNode();for(var h=0,v=d.length;h<v&&(!d[h].ceFalseOverride||!Wh(s,d[h],l,e,e));h++);}}},Xh=Dt.each,Yh=function(e){return e&&1===e.nodeType&&!Ec.isBookmarkNode(e)&&!hc(e)&&!So.isBogus(e)},Gh=function(e,t){var n;for(n=e;n;n=n[t]){if(3===n.nodeType&&0!==n.nodeValue.length)return e;if(1===n.nodeType&&!Ec.isBookmarkNode(n))return n}return e},Jh=function(e,t,n){var r,o,i=new kc(e);if(t&&n&&(t=Gh(t,"previousSibling"),n=Gh(n,"nextSibling"),i.compare(t,n))){for(r=t.nextSibling;r&&r!==n;)r=(o=r).nextSibling,t.appendChild(o);return e.remove(n),Dt.each(Dt.grep(n.childNodes),function(e){t.appendChild(e)}),t}return n},Qh=function(e,t,n){Xh(e.childNodes,function(e){Yh(e)&&(t(e)&&n(e),e.hasChildNodes()&&Qh(e,t,n))})},Zh=function(n,e){return V.curry(function(e,t){return!(!t||!Zg.getStyle(n,t,e))},e)},ev=function(r,e,t){return V.curry(function(e,t,n){r.setStyle(n,e,t),""===n.getAttribute("style")&&n.removeAttribute("style"),tv(r,n)},e,t)},tv=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},nv=function(e,t){var n;1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType&&(n=Zg.getTextDecoration(e,t.parentNode),e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null))},rv=function(n,e,r,o){Xh(e,function(t){Xh(n.dom.select(t.inline,o),function(e){Yh(e)&&Kh.removeFormat(n,t,r,e,t.exact?e:null)}),function(r,e,t){if(e.clear_child_styles){var n=e.links?"*:not(a)":"*";Xh(r.select(n,t),function(n){Yh(n)&&Xh(e.styles,function(e,t){r.setStyle(n,t,"")})})}}(n.dom,t,o)})},ov=function(e,t,n,r){(t.styles.color||t.styles.textDecoration)&&(Dt.walk(r,V.curry(nv,e),"childNodes"),nv(e,r))},iv=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&Qh(r,Zh(e,"fontSize"),ev(e,"backgroundColor",Zg.replaceVars(t.styles.backgroundColor,n)))},av=function(e,t,n,r){"sub"!==t.inline&&"sup"!==t.inline||(Qh(r,Zh(e,"fontSize"),ev(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},uv=function(e,t,n,r){r&&!1!==t.merge_siblings&&(r=Jh(e,Zg.getNonWhiteSpaceSibling(r),r),r=Jh(e,r,Zg.getNonWhiteSpaceSibling(r,!0)))},sv=function(t,n,r,o,i){yh.matchNode(t,i.parentNode,r,o)&&Kh.removeFormat(t,n,o,i)||n.merge_with_parents&&t.dom.getParent(i.parentNode,function(e){if(yh.matchNode(t,e,r,o))return Kh.removeFormat(t,n,o,i),!0})},cv=Dt.each,lv=function(p,g,h,r){var e,t,v=p.formatter.get(g),y=v[0],o=!r&&p.selection.isCollapsed(),i=p.dom,n=p.selection,b=function(n,e){if(e=e||y,n){if(e.onformat&&e.onformat(n,e,h,r),cv(e.styles,function(e,t){i.setStyle(n,t,Zg.replaceVars(e,h))}),e.styles){var t=i.getAttrib(n,"style");t&&n.setAttribute("data-mce-style",t)}cv(e.attributes,function(e,t){i.setAttrib(n,t,Zg.replaceVars(e,h))}),cv(e.classes,function(e){e=Zg.replaceVars(e,h),i.hasClass(n,e)||i.addClass(n,e)})}},C=function(e,t){var n=!1;return!!y.selector&&(cv(e,function(e){if(!("collapsed"in e&&e.collapsed!==o))return i.is(t,e.selector)&&!hc(t)?(b(t,e),!(n=!0)):void 0}),n)},a=function(s,e,t,c){var l,f,d=[],m=!0;l=y.inline||y.block,f=s.create(l),b(f),Fh.walk(s,e,function(e){var a,u=function(e){var t,n,r,o;if(o=m,t=e.nodeName.toLowerCase(),n=e.parentNode.nodeName.toLowerCase(),1===e.nodeType&&s.getContentEditable(e)&&(o=m,m="true"===s.getContentEditable(e),r=!0),Zg.isEq(t,"br"))return a=0,void(y.block&&s.remove(e));if(y.wrapper&&yh.matchNode(p,e,g,h))a=0;else{if(m&&!r&&y.block&&!y.wrapper&&Zg.isTextBlock(p,t)&&Zg.isValid(p,n,l))return e=s.rename(e,l),b(e),d.push(e),void(a=0);if(y.selector){var i=C(v,e);if(!y.inline||i)return void(a=0)}!m||r||!Zg.isValid(p,l,t)||!Zg.isValid(p,n,l)||!c&&3===e.nodeType&&1===e.nodeValue.length&&65279===e.nodeValue.charCodeAt(0)||hc(e)||y.inline&&s.isBlock(e)?(a=0,cv(Dt.grep(e.childNodes),u),r&&(m=o),a=0):(a||(a=s.clone(f,!1),e.parentNode.insertBefore(a,e),d.push(a)),a.appendChild(e))}};cv(e,u)}),!0===y.links&&cv(d,function(e){var t=function(e){"A"===e.nodeName&&b(e,y),cv(Dt.grep(e.childNodes),t)};t(e)}),cv(d,function(e){var t,n,r,o,i,a=function(e){var n=!1;return cv(e.childNodes,function(e){if((t=e)&&1===t.nodeType&&!Ec.isBookmarkNode(t)&&!hc(t)&&!So.isBogus(t))return n=e,!1;var t}),n};n=0,cv(e.childNodes,function(e){Zg.isWhiteSpaceNode(e)||Ec.isBookmarkNode(e)||n++}),t=n,!(1<d.length)&&s.isBlock(e)||0!==t?(y.inline||y.wrapper)&&(y.exact||1!==t||((o=a(r=e))&&!Ec.isBookmarkNode(o)&&yh.matchName(s,o,y)&&(i=s.clone(o,!1),b(i),s.replace(i,r,!0),s.remove(o,1)),e=i||r),rv(p,v,h,e),sv(p,y,g,h,e),iv(s,y,h,e),av(s,y,h,e),uv(s,y,h,e)):s.remove(e,1)})};if("false"!==i.getContentEditable(n.getNode())){if(y){if(r)r.nodeType?C(v,r)||((t=i.createRng()).setStartBefore(r),t.setEndAfter(r),a(i,fh(p,t,v),0,!0)):a(i,r,0,!0);else if(o&&y.inline&&!i.select("td[data-mce-selected],th[data-mce-selected]").length)!function(e,t,n){var r,o,i,a,u,s,c=e.selection;a=(r=c.getRng(!0)).startOffset,s=r.startContainer.nodeValue,(o=vc(e.getBody(),c.getStart()))&&(i=Eh(o));var l,f,d=/[^\s\u00a0\u00ad\u200b\ufeff]/;s&&0<a&&a<s.length&&d.test(s.charAt(a))&&d.test(s.charAt(a-1))?(u=c.getBookmark(),r.collapse(!0),r=fh(e,r,e.formatter.get(t)),r=Ch.split(r),e.formatter.apply(t,n,r),c.moveToBookmark(u)):(o&&i.nodeValue===xh||(l=e.getDoc(),f=Sh(!0).dom(),i=(o=l.importNode(f,!0)).firstChild,r.insertNode(o),a=1),e.formatter.apply(t,n,o),c.setCursorLocation(i,a))}(p,g,h);else{var u=p.selection.getNode();p.settings.forced_root_block||!v[0].defaultBlock||i.getParent(u,i.isBlock)||lv(p,v[0].defaultBlock),p.selection.setRng(Uc(p.selection.getRng())),e=n.getBookmark(),a(i,fh(p,n.getRng(),v)),y.styles&&ov(i,y,h,u),n.moveToBookmark(e),Zg.moveStart(i,n,n.getRng()),p.nodeChanged()}Ih(g,p)}}else{r=n.getNode();for(var s=0,c=v.length;s<c;s++)if(v[s].ceFalseOverride&&i.is(r,v[s].selector))return void b(r,v[s])}},fv={applyFormat:lv},dv=Dt.each,mv={formatChanged:function(e,t,n,r,o){var i,a,u,s,c,l,f,d;null===t.get()&&(a=e,u={},(i=t).set({}),a.on("NodeChange",function(n){var r=Zg.getParents(a.dom,n.element),o={};r=Dt.grep(r,function(e){return 1===e.nodeType&&!e.getAttribute("data-mce-bogus")}),dv(i.get(),function(e,n){dv(r,function(t){return a.formatter.matchNode(t,n,{},e.similar)?(u[n]||(dv(e,function(e){e(!0,{node:t,format:n,parents:r})}),u[n]=e),o[n]=e,!1):!yh.matchesUnInheritedFormatSelector(a,t,n)&&void 0})}),dv(u,function(e,t){o[t]||(delete u[t],dv(e,function(e){e(!1,{node:n.element,format:t,parents:r})}))})})),c=n,l=r,f=o,d=(s=t).get(),dv(c.split(","),function(e){d[e]||(d[e]=[],d[e].similar=f),d[e].push(l)}),s.set(d)}},pv={get:function(r){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all"}],italic:[{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all"}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all"}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all"}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},fontsize_class:{inline:"span",attributes:{"class":"%value"}},blockquote:{block:"blockquote",wrapper:1,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(){return!0},onformat:function(n,e,t){Dt.each(t,function(e,t){r.setAttrib(n,t,e)})}},removeformat:[{selector:"b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Dt.each("p h1 h2 h3 h4 h5 h6 div address pre div dt dd samp".split(/\s/),function(e){t[e]={block:e,remove:"all"}}),t}},gv=Dt.each,hv=ci.DOM,vv=function(e,t){var n,o,r,m=t&&t.schema||Go({}),p=function(e){var t,n,r;return o="string"==typeof e?{name:e,classes:[],attrs:{}}:e,t=hv.create(o.name),n=t,(r=o).classes.length&&hv.addClass(n,r.classes.join(" ")),hv.setAttribs(n,r.attrs),t},g=function(n,e,t){var r,o,i,a,u,s,c,l,f=0<e.length&&e[0],d=f&&f.name;if(u=d,s="string"!=typeof(a=n)?a.nodeName.toLowerCase():a,c=m.getElementRule(s),i=!(!(l=c&&c.parentsRequired)||!l.length)&&(u&&-1!==Dt.inArray(l,u)?u:l[0]))d===i?(o=e[0],e=e.slice(1)):o=i;else if(f)o=e[0],e=e.slice(1);else if(!t)return n;return o&&(r=p(o)).appendChild(n),t&&(r||(r=hv.create("div")).appendChild(n),Dt.each(t,function(e){var t=p(e);r.insertBefore(t,n)})),g(r,e,o&&o.siblings)};return e&&e.length?(o=e[0],n=p(o),(r=hv.create("div")).appendChild(g(n,e.slice(1),o.siblings)),r):""},yv=function(e){var t,a={classes:[],attrs:{}};return"*"!==(e=a.selector=Dt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,function(e,t,n,r,o){switch(t){case"#":a.attrs.id=n;break;case".":a.classes.push(n);break;case":":-1!==Dt.inArray("checked disabled enabled read-only required".split(" "),n)&&(a.attrs[n]=n)}if("["===r){var i=o.match(/([\w\-]+)(?:\=\"([^\"]+))?/);i&&(a.attrs[i[1]]=i[2])}return""})),a.name=t||"div",a},bv=function(e){return e&&"string"==typeof e?(e=(e=e.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Dt.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),function(e){var t=Dt.map(e.split(/(?:~\+|~|\+)/),yv),n=t.pop();return t.length&&(n.siblings=t),n}).reverse()):[]},Cv={getCssText:function(n,e){var t,r,o,i,a,u,s="";if(!1===(u=n.settings.preview_styles))return"";"string"!=typeof u&&(u="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow");var c=function(e){return e.replace(/%(\w+)/g,"")};if("string"==typeof e){if(!(e=n.formatter.get(e)))return;e=e[0]}return"preview"in e&&!1===(u=e.preview)?"":(t=e.block||e.inline||"span",(i=bv(e.selector)).length?(i[0].name||(i[0].name=t),t=e.selector,r=vv(i,n)):r=vv([t],n),o=hv.select(t,r)[0]||r.firstChild,gv(e.styles,function(e,t){(e=c(e))&&hv.setStyle(o,t,e)}),gv(e.attributes,function(e,t){(e=c(e))&&hv.setAttrib(o,t,e)}),gv(e.classes,function(e){e=c(e),hv.hasClass(o,e)||hv.addClass(o,e)}),n.fire("PreviewFormats"),hv.setStyles(r,{position:"absolute",left:-65535}),n.getBody().appendChild(r),a=hv.getStyle(n.getBody(),"fontSize",!0),a=/px$/.test(a)?parseInt(a,10):0,gv(u.split(" "),function(e){var t=hv.getStyle(o,e,!0);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=hv.getStyle(n.getBody(),e,!0),"#ffffff"===hv.toHex(t).toLowerCase())||"color"===e&&"#000000"===hv.toHex(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===a)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*a+"px"}"border"===e&&t&&(s+="padding:0 2px;"),s+=e+":"+t+";"}}),n.fire("AfterPreviewFormats"),hv.remove(r),s)},parseSelector:bv,selectorToHtml:function(e,t){return vv(bv(e),t)}},xv={toggle:function(e,t,n,r,o){var i=t.get(n);!yh.match(e,n,r,o)||"toggle"in i[0]&&!i[0].toggle?fv.applyFormat(e,n,r,o):Kh.remove(e,n,r,o)}},wv={setup:function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])}};function Nv(e){var t,n,r,o=(t=e,n={},(r=function(e,t){e&&("string"!=typeof e?Dt.each(e,function(e,t){r(t,e)}):(t=t.length?t:[t],Dt.each(t,function(e){"undefined"==typeof e.deep&&(e.deep=!e.selector),"undefined"==typeof e.split&&(e.split=!e.selector||e.inline),"undefined"==typeof e.remove&&e.selector&&!e.inline&&(e.remove="none"),e.selector&&e.inline&&(e.mixed=!0,e.block_expand=!0),"string"==typeof e.classes&&(e.classes=e.classes.split(/\s+/))}),n[e]=t))})(pv.get(t.dom)),r(t.settings.formats),{get:function(e){return e?n[e]:n},register:r,unregister:function(e){return e&&n[e]&&delete n[e],n}}),i=Xu(null);return wv.setup(e),Bh(e),{get:o.get,register:o.register,unregister:o.unregister,apply:V.curry(fv.applyFormat,e),remove:V.curry(Kh.remove,e),toggle:V.curry(xv.toggle,e,o),match:V.curry(yh.match,e),matchAll:V.curry(yh.matchAll,e),matchNode:V.curry(yh.matchNode,e),canApply:V.curry(yh.canApply,e),formatChanged:V.curry(mv.formatChanged,e,i),getCssText:V.curry(Cv.getCssText,e)}}var Ev=function(a){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)o.hasOwnProperty(i)&&(n[i]=a(n[i],o[i]))}return n}},Sv=Ev(function(e,t){return Jn.isObject(e)&&Jn.isObject(t)?Sv(e,t):t}),kv=Ev(function(e,t){return t}),Tv={deepMerge:Sv,merge:kv},Av=function(e,t){return e.fire("PreProcess",t)},Rv=function(e,t){return e.fire("PostProcess",t)},_v=function(e){return e.fire("remove")},Bv={register:function(t,s,c){t.addAttributeFilter("data-mce-tabindex",function(e,t){for(var n,r=e.length;r--;)(n=e[r]).attr("tabindex",n.attributes.map["data-mce-tabindex"]),n.attr(t,null)}),t.addAttributeFilter("src,href,style",function(e,t){for(var n,r,o=e.length,i="data-mce-"+t,a=s.url_converter,u=s.url_converter_scope;o--;)(r=(n=e[o]).attributes.map[i])!==undefined?(n.attr(t,0<r.length?r:null),n.attr(i,null)):(r=n.attributes.map[t],"style"===t?r=c.serializeStyle(c.parseStyle(r),n.name):a&&(r=a.call(u,r,t,n.name)),n.attr(t,0<r.length?r:null))}),t.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)(n=(t=e[r]).attr("class"))&&(n=t.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),t.attr("class",0<n.length?n:null))}),t.addAttributeFilter("data-mce-type",function(e,t,n){for(var r,o=e.length;o--;)"bookmark"!==(r=e[o]).attributes.map["data-mce-type"]||n.cleanup||r.remove()}),t.addNodeFilter("noscript",function(e){for(var t,n=e.length;n--;)(t=e[n].firstChild)&&(t.value=zo.decode(t.value))}),t.addNodeFilter("script,style",function(e,t){for(var n,r,o,i=e.length,a=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")};i--;)r=(n=e[i]).firstChild?n.firstChild.value:"","script"===t?((o=n.attr("type"))&&n.attr("type","mce-no/type"===o?null:o.replace(/^mce\-/,"")),"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="// <![CDATA[\n"+a(r)+"\n// ]]>")):"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="\x3c!--\n"+a(r)+"\n--\x3e")}),t.addNodeFilter("#comment",function(e){for(var t,n=e.length;n--;)0===(t=e[n]).value.indexOf("[CDATA[")?(t.name="#cdata",t.type=4,t.value=t.value.replace(/^\[CDATA\[|\]\]$/g,"")):0===t.value.indexOf("mce:protected ")&&(t.name="#text",t.type=3,t.raw=!0,t.value=unescape(t.value).substr(14))}),t.addNodeFilter("xml:namespace,input",function(e,t){for(var n,r=e.length;r--;)7===(n=e[r]).type?n.remove():1===n.type&&("input"!==t||"type"in n.attributes.map||n.attr("type","text"))}),t.addAttributeFilter("data-mce-type",function(e){H.each(e,function(e){"format-caret"===e.attr("data-mce-type")&&(e.isEmpty(t.schema.getNonEmptyElements())?e.remove():e.unwrap())})}),t.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})},trimTrailingBr:function(e){var t,n,r=function(e){return e&&"br"===e.name};r(t=e.lastChild)&&r(n=t.prev)&&(t.remove(),n.remove())}},Dv={process:function(e,t,n){return f=n,(l=e)&&l.hasEventListeners("PreProcess")&&!f.no_events?(o=t,i=n,c=(r=e).dom,o=o.cloneNode(!0),(a=document.implementation).createHTMLDocument&&(u=a.createHTMLDocument(""),Dt.each("BODY"===o.nodeName?o.childNodes:[o],function(e){u.body.appendChild(u.importNode(e,!0))}),o="BODY"!==o.nodeName?u.body.firstChild:u.body,s=c.doc,c.doc=u),Av(r,Tv.merge(i,{node:o})),s&&(c.doc=s),o):t;var r,o,i,a,u,s,c,l,f}},Ov=function(e,u,s){e.addNodeFilter("font",function(e){H.each(e,function(e){var t,n,r=u.parse(e.attr("style")),o=e.attr("color"),i=e.attr("face"),a=e.attr("size");o&&(r.color=o),i&&(r["font-family"]=i),a&&(r["font-size"]=s[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",u.serialize(r)),t=e,n=["color","face","size"],H.each(n,function(e){t.attr(e,null)})})})},Pv=function(e,t){var n,r=Qo();t.convert_fonts_to_spans&&Ov(e,r,Dt.explode(t.font_size_legacy_values)),n=r,e.addNodeFilter("strike",function(e){H.each(e,function(e){var t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))})})},Lv={register:function(e,t){t.inline_styles&&Pv(e,t)}},Iv=/^[ \t\r\n]*$/,Mv={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Fv=function(e,t,n){var r,o,i=n?"lastChild":"firstChild",a=n?"prev":"next";if(e[i])return e[i];if(e!==t){if(r=e[a])return r;for(o=e.parent;o&&o!==t;o=o.parent)if(r=o[a])return r}},zv=function(){function a(e,t){this.name=e,1===(this.type=t)&&(this.attributes=[],this.attributes.map={})}return a.create=function(e,t){var n,r;if(n=new a(e,Mv[e]||1),t)for(r in t)n.attr(r,t[r]);return n},a.prototype.replace=function(e){return e.parent&&e.remove(),this.insert(e,this),this.remove(),this},a.prototype.attr=function(e,t){var n,r;if("string"!=typeof e){for(r in e)this.attr(r,e[r]);return this}if(n=this.attributes){if(t!==undefined){if(null===t){if(e in n.map)for(delete n.map[e],r=n.length;r--;)if(n[r].name===e)return n=n.splice(r,1),this;return this}if(e in n.map){for(r=n.length;r--;)if(n[r].name===e){n[r].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,this}return n.map[e]}},a.prototype.clone=function(){var e,t,n,r,o,i=new a(this.name,this.type);if(n=this.attributes){for((o=[]).map={},e=0,t=n.length;e<t;e++)"id"!==(r=n[e]).name&&(o[o.length]={name:r.name,value:r.value},o.map[r.name]=r.value);i.attributes=o}return i.value=this.value,i.shortEnded=this.shortEnded,i},a.prototype.wrap=function(e){return this.parent.insert(e,this),e.append(this),this},a.prototype.unwrap=function(){var e,t;for(e=this.firstChild;e;)t=e.next,this.insert(e,this,!0),e=t;this.remove()},a.prototype.remove=function(){var e=this.parent,t=this.next,n=this.prev;return e&&(e.firstChild===this?(e.firstChild=t)&&(t.prev=null):n.next=t,e.lastChild===this?(e.lastChild=n)&&(n.next=null):t.prev=n,this.parent=this.next=this.prev=null),this},a.prototype.append=function(e){var t;return e.parent&&e.remove(),(t=this.lastChild)?((t.next=e).prev=t,this.lastChild=e):this.lastChild=this.firstChild=e,e.parent=this,e},a.prototype.insert=function(e,t,n){var r;return e.parent&&e.remove(),r=t.parent||this,n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,(e.next=t).prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,(e.prev=t).next=e),e.parent=r,e},a.prototype.getAll=function(e){var t,n=[];for(t=this.firstChild;t;t=Fv(t,this))t.name===e&&n.push(t);return n},a.prototype.empty=function(){var e,t,n;if(this.firstChild){for(e=[],n=this.firstChild;n;n=Fv(n,this))e.push(n);for(t=e.length;t--;)(n=e[t]).parent=n.firstChild=n.lastChild=n.next=n.prev=null}return this.firstChild=this.lastChild=null,this},a.prototype.isEmpty=function(e,t,n){var r,o,i=this.firstChild;if(t=t||{},i)do{if(1===i.type){if(i.attributes.map["data-mce-bogus"])continue;if(e[i.name])return!1;for(r=i.attributes.length;r--;)if("name"===(o=i.attributes[r].name)||0===o.indexOf("data-mce-bookmark"))return!1}if(8===i.type)return!1;if(3===i.type&&!Iv.test(i.value))return!1;if(3===i.type&&i.parent&&t[i.parent.name]&&Iv.test(i.value))return!1;if(n&&n(i))return!1}while(i=Fv(i,this));return!0},a.prototype.walk=function(e){return Fv(this,null,e)},a}(),Uv=function(e,t,n,r){(e.padd_empty_with_br||t.insert)&&n[r.name]?r.empty().append(new zv("br",1)).shortEnded=!0:r.empty().append(new zv("#text",3)).value="\xa0"},qv=function(e){return Vv(e,"#text")&&"\xa0"===e.firstChild.value},Vv=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},Hv=function(r,e,t,n){return n.isEmpty(e,t,function(e){return t=e,(n=r.getElementRule(t.name))&&n.paddEmpty;var t,n})},jv=function(e,t){return e&&(t[e.name]||"br"===e.name)},$v=function(e,g){var h=e.schema;g.remove_trailing_brs&&e.addNodeFilter("br",function(e,t,n){var r,o,i,a,u,s,c,l,f=e.length,d=Dt.extend({},h.getBlockElements()),m=h.getNonEmptyElements(),p=h.getNonEmptyElements();for(d.body=1,r=0;r<f;r++)if(i=(o=e[r]).parent,d[o.parent.name]&&o===i.lastChild){for(u=o.prev;u;){if("span"!==(s=u.name)||"bookmark"!==u.attr("data-mce-type")){if("br"!==s)break;if("br"===s){o=null;break}}u=u.prev}o&&(o.remove(),Hv(h,m,p,i)&&(c=h.getElementRule(i.name))&&(c.removeEmpty?i.remove():c.paddEmpty&&Uv(g,n,d,i)))}else{for(a=o;i&&i.firstChild===a&&i.lastChild===a&&!d[(a=i).name];)i=i.parent;a===i&&!0!==g.padd_empty_with_br&&((l=new zv("#text",3)).value="\xa0",o.replace(l))}}),e.addAttributeFilter("href",function(e){var t,n,r,o=e.length;if(!g.allow_unsafe_link_target)for(;o--;)"a"===(t=e[o]).name&&"_blank"===t.attr("target")&&t.attr("rel",(n=t.attr("rel"),r=n?Dt.trim(n):"",/\b(noopener)\b/g.test(r)?r:r.split(" ").filter(function(e){return 0<e.length}).concat(["noopener"]).sort().join(" ")))}),g.allow_html_in_named_anchor||e.addAttributeFilter("id,name",function(e){for(var t,n,r,o,i=e.length;i--;)if("a"===(o=e[i]).name&&o.firstChild&&!o.attr("href"))for(r=o.parent,t=o.lastChild;n=t.prev,r.insert(t,o),t=n;);}),g.fix_list_elements&&e.addNodeFilter("ul,ol",function(e){for(var t,n,r=e.length;r--;)if("ul"===(n=(t=e[r]).parent).name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new zv("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}}),g.validate&&h.getValidClasses()&&e.addAttributeFilter("class",function(e){for(var t,n,r,o,i,a,u,s=e.length,c=h.getValidClasses();s--;){for(n=(t=e[s]).attr("class").split(" "),i="",r=0;r<n.length;r++)o=n[r],u=!1,(a=c["*"])&&a[o]&&(u=!0),a=c[t.name],!u&&a&&a[o]&&(u=!0),u&&(i&&(i+=" "),i+=o);i.length||(i=null),t.attr("class",i)}})},Wv=Dt.makeMap,Kv=Dt.each,Xv=Dt.explode,Yv=Dt.extend;function Gv(k,T){void 0===T&&(T=Go());var A={},R=[],_={},B={};(k=k||{}).validate=!("validate"in k)||k.validate,k.root_name=k.root_name||"body";var D=function(e){var t,n,r;n in A&&((r=_[n])?r.push(e):_[n]=[e]),t=R.length;for(;t--;)(n=R[t].name)in e.attributes.map&&((r=B[n])?r.push(e):B[n]=[e]);return e},e={schema:T,addAttributeFilter:function(e,n){Kv(Xv(e),function(e){var t;for(t=0;t<R.length;t++)if(R[t].name===e)return void R[t].callbacks.push(n);R.push({name:e,callbacks:[n]})})},getAttributeFilters:function(){return[].concat(R)},addNodeFilter:function(e,n){Kv(Xv(e),function(e){var t=A[e];t||(A[e]=t=[]),t.push(n)})},getNodeFilters:function(){var e=[];for(var t in A)A.hasOwnProperty(t)&&e.push({name:t,callbacks:A[t]});return e},filterNode:D,parse:function(e,a){var t,n,r,o,i,u,s,c,l,f,d,m=[];a=a||{},_={},B={},l=Yv(Wv("script,style,head,html,body,title,meta,param"),T.getBlockElements());var p=T.getNonEmptyElements(),g=T.children,h=k.validate,v="forced_root_block"in a?a.forced_root_block:k.forced_root_block,y=T.getWhiteSpaceElements(),b=/^[ \t\r\n]+/,C=/[ \t\r\n]+$/,x=/[ \t\r\n]+/g,w=/^[ \t\r\n]+$/,N=function(e,t){var n,r=new zv(e,t);return e in A&&((n=_[e])?n.push(r):_[e]=[r]),r},E=function(e){var t,n,r,o,i=T.getBlockElements();for(t=e.prev;t&&3===t.type;){if(0<(r=t.value.replace(C,"")).length)return void(t.value=r);if(n=t.next){if(3===n.type&&n.value.length){t=t.prev;continue}if(!i[n.name]&&"script"!==n.name&&"style"!==n.name){t=t.prev;continue}}o=t.prev,t.remove(),t=o}};t=Dg({validate:h,allow_script_urls:k.allow_script_urls,allow_conditional_comments:k.allow_conditional_comments,self_closing_elements:function(e){var t,n={};for(t in e)"li"!==t&&"p"!==t&&(n[t]=e[t]);return n}(T.getSelfClosingElements()),cdata:function(e){d.append(N("#cdata",4)).value=e},text:function(e,t){var n;f||(e=e.replace(x," "),jv(d.lastChild,l)&&(e=e.replace(b,""))),0!==e.length&&((n=N("#text",3)).raw=!!t,d.append(n).value=e)},comment:function(e){d.append(N("#comment",8)).value=e},pi:function(e,t){d.append(N(e,7)).value=t,E(d)},doctype:function(e){d.append(N("#doctype",10)).value=e,E(d)},start:function(e,t,n){var r,o,i,a,u;if(i=h?T.getElementRule(e):{}){for((r=N(i.outputName||e,1)).attributes=t,r.shortEnded=n,d.append(r),(u=g[d.name])&&g[r.name]&&!u[r.name]&&m.push(r),o=R.length;o--;)(a=R[o].name)in t.map&&((s=B[a])?s.push(r):B[a]=[r]);l[e]&&E(r),n||(d=r),!f&&y[e]&&(f=!0)}},end:function(e){var t,n,r,o,i;if(n=h?T.getElementRule(e):{}){if(l[e]&&!f){if((t=d.firstChild)&&3===t.type)if(0<(r=t.value.replace(b,"")).length)t.value=r,t=t.next;else for(o=t.next,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.next,(0===r.length||w.test(r))&&(t.remove(),t=o),t=o;if((t=d.lastChild)&&3===t.type)if(0<(r=t.value.replace(C,"")).length)t.value=r,t=t.prev;else for(o=t.prev,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.prev,(0===r.length||w.test(r))&&(t.remove(),t=o),t=o}if(f&&y[e]&&(f=!1),n.removeEmpty&&Hv(T,p,y,d)&&!d.attributes.map.name&&!d.attr("id"))return i=d.parent,l[d.name]?d.empty().remove():d.unwrap(),void(d=i);n.paddEmpty&&(qv(d)||Hv(T,p,y,d))&&Uv(k,a,l,d),d=d.parent}}},T);var S=d=new zv(a.context||k.root_name,11);if(t.parse(e),h&&m.length&&(a.context?a.invalid=!0:function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,p,g,h;for(d=Wv("tr,td,th,tbody,thead,tfoot,table"),l=T.getNonEmptyElements(),f=T.getWhiteSpaceElements(),m=T.getTextBlockElements(),p=T.getSpecialElements(),t=0;t<e.length;t++)if((n=e[t]).parent&&!n.fixed)if(m[n.name]&&"li"===n.parent.name){for(g=n.next;g&&m[g.name];)g.name="li",g.fixed=!0,n.parent.insert(g,n.parent),g=g.next;n.unwrap(n)}else{for(o=[n],r=n.parent;r&&!T.isValidChild(r.name,n.name)&&!d[r.name];r=r.parent)o.push(r);if(r&&1<o.length){for(o.reverse(),i=a=D(o[0].clone()),c=0;c<o.length-1;c++){for(T.isValidChild(a.name,o[c].name)?(u=D(o[c].clone()),a.append(u)):u=a,s=o[c].firstChild;s&&s!==o[c+1];)h=s.next,u.append(s),s=h;a=u}Hv(T,l,f,i)?r.insert(n,o[0],!0):(r.insert(i,o[0],!0),r.insert(n,i)),r=o[0],(Hv(T,l,f,r)||Vv(r,"br"))&&r.empty().remove()}else if(n.parent){if("li"===n.name){if((g=n.prev)&&("ul"===g.name||"ul"===g.name)){g.append(n);continue}if((g=n.next)&&("ul"===g.name||"ul"===g.name)){g.insert(n,g.firstChild,!0);continue}n.wrap(D(new zv("ul",1)));continue}T.isValidChild(n.parent.name,"div")&&T.isValidChild("div",n.name)?n.wrap(D(new zv("div",1))):p[n.name]?n.empty().remove():n.unwrap()}}}(m)),v&&("body"===S.name||a.isRootContent)&&function(){var e,t,n=S.firstChild,r=function(e){e&&((n=e.firstChild)&&3===n.type&&(n.value=n.value.replace(b,"")),(n=e.lastChild)&&3===n.type&&(n.value=n.value.replace(C,"")))};if(T.isValidChild(S.name,v.toLowerCase())){for(;n;)e=n.next,3===n.type||1===n.type&&"p"!==n.name&&!l[n.name]&&!n.attr("data-mce-type")?(t||((t=N(v,1)).attr(k.forced_root_block_attrs),S.insert(t,n)),t.append(n)):(r(t),t=null),n=e;r(t)}}(),!a.invalid){for(c in _){for(s=A[c],i=(n=_[c]).length;i--;)n[i].parent||n.splice(i,1);for(r=0,o=s.length;r<o;r++)s[r](n,c,a)}for(r=0,o=R.length;r<o;r++)if((s=R[r]).name in B){for(i=(n=B[s.name]).length;i--;)n[i].parent||n.splice(i,1);for(i=0,u=s.callbacks.length;i<u;i++)s.callbacks[i](n,s.name,a)}}return S}};return $v(e,k),Lv.register(e,k),e}var Jv=function(e,t,n){-1===Dt.inArray(t,n)&&(e.addAttributeFilter(n,function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)}),t.push(n))},Qv=function(e,t,n){var r=wi(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection?r:Dt.trim(r)},Zv=function(e,t,n,r){var o=r.selection?Tv.merge({forced_root_block:!1},r):r,i=e.parse(n,o);return Bv.trimTrailingBr(i),i},ey=function(e,t,n,r,o){var i,a,u,s,c=(i=r,Fc(t,n).serialize(i));return a=e,s=c,!(u=o).no_events&&a?Rv(a,Tv.merge(u,{content:s})).content:s};function ty(e,t){var a,u,s,c,l,n,r=(a=e,n=["data-mce-selected"],s=(u=t)&&u.dom?u.dom:ci.DOM,c=u&&u.schema?u.schema:Go(a),a.entity_encoding=a.entity_encoding||"named",a.remove_trailing_brs=!("remove_trailing_brs"in a)||a.remove_trailing_brs,l=Gv(a,c),Bv.register(l,a,s),{schema:c,addNodeFilter:l.addNodeFilter,addAttributeFilter:l.addAttributeFilter,serialize:function(e,t){var n=Tv.merge({format:"html"},t||{}),r=Dv.process(u,e,n),o=Qv(s,r,n),i=Zv(l,s,o,n);return"tree"===n.format?i:ey(u,a,c,i,n)},addRules:function(e){c.addValidElements(e)},setRules:function(e){c.setValidElements(e)},addTempAttr:V.curry(Jv,l,n),getTempAttrs:function(){return n}});return{schema:r.schema,addNodeFilter:r.addNodeFilter,addAttributeFilter:r.addAttributeFilter,serialize:r.serialize,addRules:r.addRules,setRules:r.setRules,addTempAttr:r.addTempAttr,getTempAttrs:r.getTempAttrs}}function ny(e){return{getBookmark:V.curry(Ec.getBookmark,e),moveToBookmark:V.curry(Ec.moveToBookmark,e)}}(ny||(ny={})).isBookmarkNode=Ec.isBookmarkNode;var ry=ny,oy=So.isContentEditableFalse,iy=So.isContentEditableTrue,ay=function(r,a){var u,s,c,l,f,d,m,p,g,h,v,y,i,b,C,x,w,N=a.dom,E=Dt.each,S=a.getDoc(),k=document,T=Math.abs,A=Math.round,R=a.getBody();l={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]};var e=".mce-content-body";a.contentStyles.push(e+" div.mce-resizehandle {position: absolute;border: 1px solid black;box-sizing: content-box;background: #FFF;width: 7px;height: 7px;z-index: 10000}"+e+" .mce-resizehandle:hover {background: #000}"+e+" img[data-mce-selected],"+e+" hr[data-mce-selected] {outline: 1px solid black;resize: none}"+e+" .mce-clonedresizable {position: absolute;"+(de.gecko?"":"outline: 1px dashed black;")+"opacity: .5;filter: alpha(opacity=50);z-index: 10000}"+e+" .mce-resize-helper {background: #555;background: rgba(0,0,0,0.75);border-radius: 3px;border: 1px;color: white;display: none;font-family: sans-serif;font-size: 12px;white-space: nowrap;line-height: 14px;margin: 5px 10px;padding: 5px;position: absolute;z-index: 10001}");var _=function(e){return e&&("IMG"===e.nodeName||a.dom.is(e,"figure.image"))},n=function(e){var t,n,r=e.target;t=e,n=a.selection.getRng(),!_(t.target)||vg(t.clientX,t.clientY,n)||e.isDefaultPrevented()||(e.preventDefault(),a.selection.select(r))},B=function(e){return a.dom.is(e,"figure.image")?e.querySelector("img"):e},D=function(e){var t=a.settings.object_resizing;return!1!==t&&!de.iOS&&("string"!=typeof t&&(t="table,img,figure.image,div"),"false"!==e.getAttribute("data-mce-resize")&&e!==a.getBody()&&Tr.is(Fn.fromDom(e),t))},O=function(e){var t,n,r,o;t=e.screenX-d,n=e.screenY-m,b=t*f[2]+h,C=n*f[3]+v,b=b<5?5:b,C=C<5?5:C,(_(u)&&!1!==a.settings.resize_img_proportional?!Cg.modifierPressed(e):Cg.modifierPressed(e)||_(u)&&f[2]*f[3]!=0)&&(T(t)>T(n)?(C=A(b*y),b=A(C/y)):(b=A(C/y),C=A(b*y))),N.setStyles(B(s),{width:b,height:C}),r=0<(r=f.startPos.x+t)?r:0,o=0<(o=f.startPos.y+n)?o:0,N.setStyles(c,{left:r,top:o,display:"block"}),c.innerHTML=b+" &times; "+C,f[2]<0&&s.clientWidth<=b&&N.setStyle(s,"left",p+(h-b)),f[3]<0&&s.clientHeight<=C&&N.setStyle(s,"top",g+(v-C)),(t=R.scrollWidth-x)+(n=R.scrollHeight-w)!=0&&N.setStyles(c,{left:r-t,top:o-n}),i||(a.fire("ObjectResizeStart",{target:u,width:h,height:v}),i=!0)},P=function(){i=!1;var e=function(e,t){t&&(u.style[e]||!a.schema.isValid(u.nodeName.toLowerCase(),e)?N.setStyle(B(u),e,t):N.setAttrib(B(u),e,t))};e("width",b),e("height",C),N.unbind(S,"mousemove",O),N.unbind(S,"mouseup",P),k!==S&&(N.unbind(k,"mousemove",O),N.unbind(k,"mouseup",P)),N.remove(s),N.remove(c),o(u),a.fire("ObjectResized",{target:u,width:b,height:C}),N.setAttrib(u,"style",N.getAttrib(u,"style")),a.nodeChanged()},o=function(e){var t,r,o,n,i;L(),F(),t=N.getPos(e,R),p=t.x,g=t.y,i=e.getBoundingClientRect(),r=i.width||i.right-i.left,o=i.height||i.bottom-i.top,u!==e&&(u=e,b=C=0),n=a.fire("ObjectSelected",{target:e}),D(e)&&!n.isDefaultPrevented()?E(l,function(n,e){var t;(t=N.get("mceResizeHandle"+e))&&N.remove(t),t=N.add(R,"div",{id:"mceResizeHandle"+e,"data-mce-bogus":"all","class":"mce-resizehandle",unselectable:!0,style:"cursor:"+e+"-resize; margin:0; padding:0"}),de.ie&&(t.contentEditable=!1),N.bind(t,"mousedown",function(e){var t;e.stopImmediatePropagation(),e.preventDefault(),d=(t=e).screenX,m=t.screenY,h=B(u).clientWidth,v=B(u).clientHeight,y=v/h,(f=n).startPos={x:r*n[0]+p,y:o*n[1]+g},x=R.scrollWidth,w=R.scrollHeight,s=u.cloneNode(!0),N.addClass(s,"mce-clonedresizable"),N.setAttrib(s,"data-mce-bogus","all"),s.contentEditable=!1,s.unSelectabe=!0,N.setStyles(s,{left:p,top:g,margin:0}),s.removeAttribute("data-mce-selected"),R.appendChild(s),N.bind(S,"mousemove",O),N.bind(S,"mouseup",P),k!==S&&(N.bind(k,"mousemove",O),N.bind(k,"mouseup",P)),c=N.add(R,"div",{"class":"mce-resize-helper","data-mce-bogus":"all"},h+" &times; "+v)}),n.elm=t,N.setStyles(t,{left:r*n[0]+p-t.offsetWidth/2,top:o*n[1]+g-t.offsetHeight/2})}):L(),u.setAttribute("data-mce-selected","1")},L=function(){var e,t;for(e in F(),u&&u.removeAttribute("data-mce-selected"),l)(t=N.get("mceResizeHandle"+e))&&(N.unbind(t),N.remove(t))},I=function(e){var t,n=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};i||a.removed||(E(N.select("img[data-mce-selected],hr[data-mce-selected]"),function(e){e.removeAttribute("data-mce-selected")}),t="mousedown"===e.type?e.target:r.getNode(),n(t=N.$(t).closest("table,img,figure.image,hr")[0],R)&&(z(),n(r.getStart(!0),t)&&n(r.getEnd(!0),t))?o(t):L())},M=function(e){return oy(function(e,t){for(;t&&t!==e;){if(iy(t)||oy(t))return t;t=t.parentNode}return null}(a.getBody(),e))},F=function(){for(var e in l){var t=l[e];t.elm&&(N.unbind(t.elm),delete t.elm)}},z=function(){try{a.getDoc().execCommand("enableObjectResizing",!1,!1)}catch(e){}};return a.on("init",function(){z(),de.ie&&11<=de.ie&&(a.on("mousedown click",function(e){var t=e.target,n=t.nodeName;i||!/^(TABLE|IMG|HR)$/.test(n)||M(t)||(2!==e.button&&a.selection.select(t,"TABLE"===n),"mousedown"===e.type&&a.nodeChanged())}),a.dom.bind(R,"mscontrolselect",function(e){var t=function(e){ve.setEditorTimeout(a,function(){a.selection.select(e)})};if(M(e.target))return e.preventDefault(),void t(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&t(e.target))}));var t=ve.throttle(function(e){a.composing||I(e)});a.on("nodechange ResizeEditor ResizeWindow drop FullscreenStateChanged",t),a.on("keyup compositionend",function(e){u&&"TABLE"===u.nodeName&&t(e)}),a.on("hide blur",L),a.on("contextmenu",n)}),a.on("remove",F),{isResizable:D,showResizeRect:o,hideResizeRect:L,updateResizeRect:I,destroy:function(){u=s=null}}},uy=function(e){return So.isContentEditableTrue(e)||So.isContentEditableFalse(e)},sy={fromPoint:function(e,t,n){var r,o,i,a,u,s=n;if(s.caretPositionFromPoint)(o=s.caretPositionFromPoint(e,t))&&((r=n.createRange()).setStart(o.offsetNode,o.offset),r.collapse(!0));else if(n.caretRangeFromPoint)r=n.caretRangeFromPoint(e,t);else if(s.body.createTextRange){r=s.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(c){r=function(e,n,t){var r,o,i;if(r=t.elementFromPoint(e,n),o=t.body.createTextRange(),r&&"HTML"!==r.tagName||(r=t.body),o.moveToElementText(r),0<(i=(i=Dt.toArray(o.getClientRects())).sort(function(e,t){return(e=Math.abs(Math.max(e.top-n,e.bottom-n)))-(t=Math.abs(Math.max(t.top-n,t.bottom-n)))})).length){n=(i[0].bottom+i[0].top)/2;try{return o.moveToPoint(e,n),o.collapse(!0),o}catch(a){}}return null}(e,t,n)}return i=r,a=n.body,u=i&&i.parentElement?i.parentElement():null,So.isContentEditableFalse(function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(u,a,uy))?null:i}return r}},cy=function(n,e){return H.map(e,function(e){var t=n.fire("GetSelectionRange",{range:e});return t.range!==e?t.range:e})},ly=function(e,t){return Fn.fromDom(e.dom().cloneNode(t))},fy=function(e){return ly(e,!0)},dy=function(e){return ly(e,!1)},my=fy,py=function(e,t){var n=(t||document).createDocumentFragment();return H.each(e,function(e){n.appendChild(e.dom())}),Fn.fromDom(n)},gy=function(t){return Fr.firstChild(t).fold(V.constant([t]),function(e){return[t].concat(gy(e))})},hy=function(t){return Fr.lastChild(t).fold(V.constant([t]),function(e){return"br"===Yn.name(e)?Fr.prevSibling(e).map(function(e){return[t].concat(hy(e))}).getOr([]):[t].concat(hy(e))})},vy=function(o,e){return ia([(i=e,a=i.startContainer,u=i.startOffset,So.isText(a)?0===u?A.some(Fn.fromDom(a)):A.none():A.from(a.childNodes[u]).map(Fn.fromDom)),(t=e,n=t.endContainer,r=t.endOffset,So.isText(n)?r===n.data.length?A.some(Fn.fromDom(n)):A.none():A.from(n.childNodes[r-1]).map(Fn.fromDom))],function(e,t){var n=H.find(gy(o),V.curry(_r.eq,e)),r=H.find(hy(o),V.curry(_r.eq,t));return n.isSome()&&r.isSome()}).getOr(!1);var t,n,r,i,a,u},yy=function(e,t,n,r){var o=n,i=new Zr(n,o),a=e.schema.getNonEmptyElements();do{if(3===n.nodeType&&0!==Dt.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName]&&!/^(TD|TH)$/.test(n.nodeName))return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n));if(de.ie&&de.ie<11&&e.isBlock(n)&&e.isEmpty(n))return void(r?t.setStart(n,0):t.setEnd(n,0))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},by=br.immutable("element","width","rows"),Cy=br.immutable("element","cells"),xy=br.immutable("x","y"),wy=function(e,t){var n=parseInt(sr.get(e,t),10);return isNaN(n)?1:n},Ny=function(e){return H.foldl(e,function(e,t){return t.cells().length>e?t.cells().length:e},0)},Ey=function(e,t){for(var n=e.rows(),r=0;r<n.length;r++)for(var o=n[r].cells(),i=0;i<o.length;i++)if(_r.eq(o[i],t))return A.some(xy(i,r));return A.none()},Sy=function(e,t,n,r,o){for(var i=[],a=e.rows(),u=n;u<=o;u++){var s=a[u].cells(),c=t<r?s.slice(t,r+1):s.slice(r,t+1);i.push(Cy(a[u].element(),c))}return i},ky=function(e){var o=by(dy(e),0,[]);return H.each(su(e,"tr"),function(n,r){H.each(su(n,"td,th"),function(e,t){!function(e,t,n,r,o){for(var i=wy(o,"rowspan"),a=wy(o,"colspan"),u=e.rows(),s=n;s<n+i;s++){u[s]||(u[s]=Cy(my(r),[]));for(var c=t;c<t+a;c++)u[s].cells()[c]=s===n&&c===t?o:dy(o)}}(o,function(e,t,n){for(;r=t,o=n,i=void 0,((i=e.rows())[o]?i[o].cells():[])[r];)t++;var r,o,i;return t}(o,t,r),r,n,e)})}),by(o.element(),Ny(o.rows()),o.rows())},Ty=function(e){return i=t=e,n=H.map(i.rows(),function(e){var t=H.map(e.cells(),function(e){var t=my(e);return sr.remove(t,"colspan"),sr.remove(t,"rowspan"),t}),n=dy(e.element());return Ac(n,t),n}),r=dy(t.element()),o=Fn.fromTag("tbody"),Ac(o,n),Lu.append(r,o),r;var t,n,r,o,i},Ay=function(l,e,t){return Ey(l,e).bind(function(c){return Ey(l,t).map(function(e){return t=l,r=e,o=(n=c).x(),i=n.y(),a=r.x(),u=r.y(),s=i<u?Sy(t,o,i,a,u):Sy(t,o,u,a,i),by(t.element(),Ny(s),s);var t,n,r,o,i,a,u,s})})},Ry=function(n,t){return H.find(n,function(e){return"li"===Yn.name(e)&&vy(e,t)}).fold(V.constant([]),function(e){return(t=n,H.find(t,function(e){return"ul"===Yn.name(e)||"ol"===Yn.name(e)})).map(function(e){return[Fn.fromTag("li"),Fn.fromTag(Yn.name(e))]}).getOr([]);var t})},_y=function(e,t){var n,r=Fn.fromDom(t.commonAncestorContainer),o=Bl(r,e),i=H.filter(o,function(e){return ao(e)||oo(e)}),a=Ry(o,t),u=i.concat(a.length?a:lo(n=r)?Fr.parent(n).filter(co).fold(V.constant([]),function(e){return[n,e]}):co(n)?[n]:[]);return H.map(u,dy)},By=function(){return py([])},Dy=function(e,t){return n=Fn.fromDom(t.cloneContents()),r=_y(e,t),o=H.foldl(r,function(e,t){return Lu.append(t,e),t},n),0<r.length?py([o]):o;var n,r,o},Oy=function(e,o){return(t=e,n=o[0],ml(n,"table",V.curry(_r.eq,t))).bind(function(e){var t=o[0],n=o[o.length-1],r=ky(e);return Ay(r,t,n).map(function(e){return py([Ty(e)])})}).getOrThunk(By);var t,n},Py=function(e,t){var n,r,o=xd(t,e);return 0<o.length?Oy(e,o):(n=e,0<(r=t).length&&r[0].collapsed?By():Dy(n,r[0]))},Ly=function(e,t){var n,r=e.selection.getRng(),o=e.dom.create("body"),i=e.selection.getSel(),a=cy(e,gd(i));if((t=t||{}).get=!0,t.format=t.format||"html",t.selection=!0,(t=e.fire("BeforeGetContent",t)).isDefaultPrevented())return e.fire("GetContent",t),t.content;if("text"===t.format)return e.selection.isCollapsed()?"":wi(r.text||(i.toString?i.toString():""));r.cloneContents?(n=t.contextual?Py(Fn.fromDom(e.getBody()),a).dom():r.cloneContents())&&o.appendChild(n):r.item!==undefined||r.htmlText!==undefined?(o.innerHTML="<br>"+(r.item?r.item(0).outerHTML:r.htmlText),o.removeChild(o.firstChild)):o.innerHTML=r.toString(),t.getInner=!0;var u=e.selection.serializer.serialize(o,t);return"tree"===t.format?u:(t.content=e.selection.isCollapsed()?"":u,e.fire("GetContent",t),t.content)},Iy=function(e,t,n){var r,o,i,a=e.selection.getRng(),u=e.getDoc();if((n=n||{format:"html"}).set=!0,n.selection=!0,n.content=t,n.no_events||!(n=e.fire("BeforeSetContent",n)).isDefaultPrevented()){if(t=n.content,a.insertNode){t+='<span id="__caret">_</span>',a.startContainer===u&&a.endContainer===u?u.body.innerHTML=t:(a.deleteContents(),0===u.body.childNodes.length?u.body.innerHTML=t:a.createContextualFragment?a.insertNode(a.createContextualFragment(t)):(o=u.createDocumentFragment(),i=u.createElement("div"),o.appendChild(i),i.outerHTML=t,a.insertNode(o))),r=e.dom.get("__caret"),(a=u.createRange()).setStartBefore(r),a.setEndBefore(r),e.selection.setRng(a),e.dom.remove("__caret");try{e.selection.setRng(a)}catch(s){}}else a.item&&(u.execCommand("Delete",!1,null),a=e.getRng()),/^\s+/.test(t)?(a.pasteHTML('<span id="__mce_tmp">_</span>'+t),e.dom.remove("__mce_tmp")):a.pasteHTML(t);n.no_events||e.fire("SetContent",n)}else e.fire("SetContent",n)},My=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return A.from(i).map(Fn.fromDom).map(function(e){return r&&t.collapsed?e:Fr.child(e,o(e,a)).getOr(e)}).bind(function(e){return Yn.isElement(e)?A.some(e):Fr.parent(e)}).map(function(e){return e.dom()}).getOr(e)},Fy=function(e,t,n){return My(e,t,!0,n,function(e,t){return Math.min(Fr.childNodesCount(e),t)})},zy=function(e,t,n){return My(e,t,!1,n,function(e,t){return 0<t?t-1:t})},Uy=function(e,t){for(var n=e;e&&So.isText(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},qy=Dt.each,Vy=function(e){return!!e.select},Hy=function(e){return!(!e||!e.ownerDocument)&&_r.contains(Fn.fromDom(e.ownerDocument),Fn.fromDom(e))},jy=function(u,s,e,c){var n,t,l,f,a,r=function(e,t){return Iy(c,e,t)},o=function(e){var t=m();t.collapse(!!e),i(t)},d=function(){return s.getSelection?s.getSelection():s.document.selection},m=function(){var e,t,n,r,o=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}};if(!s)return null;if(null==(r=s.document))return null;if(c.bookmark!==undefined&&!1===ap(c)){var i=Sm.getRng(c);if(i.isSome())return i.map(function(e){return cy(c,[e])[0]}).getOr(r.createRange())}try{(e=d())&&(t=0<e.rangeCount?e.getRangeAt(0):e.createRange?e.createRange():r.createRange())}catch(a){}return(t=cy(c,[t])[0])||(t=r.createRange?r.createRange():r.body.createTextRange()),t.setStart&&9===t.startContainer.nodeType&&t.collapsed&&(n=u.getRoot(),t.setStart(n,0),t.setEnd(n,0)),l&&f&&(0===o(t.START_TO_START,t,l)&&0===o(t.END_TO_END,t,l)?t=f:f=l=null),t},i=function(e,t){var n,r;if((o=e)&&(Vy(o)||Hy(o.startContainer)&&Hy(o.endContainer))){var o,i=Vy(e)?e:null;if(i){f=null;try{i.select()}catch(a){}}else{if(n=d(),e=c.fire("SetSelectionRange",{range:e,forward:t}).range,n){f=e;try{n.removeAllRanges(),n.addRange(e)}catch(a){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),l=0<n.rangeCount?n.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!n.setBaseAndExtent||de.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(r=e.startContainer.childNodes[e.startOffset])&&"IMG"===r.tagName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(r,0,r,1)),c.fire("AfterSetSelectionRange",{range:e,forward:t})}}},p=function(){var e,t,n=d();return!(n&&n.anchorNode&&n.focusNode)||((e=u.createRng()).setStart(n.anchorNode,n.anchorOffset),e.collapse(!0),(t=u.createRng()).setStart(n.focusNode,n.focusOffset),t.collapse(!0),e.compareBoundaryPoints(e.START_TO_START,t)<=0)},g={bookmarkManager:null,controlSelection:null,dom:u,win:s,serializer:e,editor:c,collapse:o,setCursorLocation:function(e,t){var n=u.createRng();e?(n.setStart(e,t),n.setEnd(e,t),i(n),o(!1)):(yy(u,n,c.getBody(),!0),i(n))},getContent:function(e){return Ly(c,e)},setContent:r,getBookmark:function(e,t){return n.getBookmark(e,t)},moveToBookmark:function(e){return n.moveToBookmark(e)},select:function(e,t){var r,n,o;return(r=u,n=e,o=t,A.from(n).map(function(e){var t=r.nodeIndex(e),n=r.createRng();return n.setStart(e.parentNode,t),n.setEnd(e.parentNode,t+1),o&&(yy(r,n,e,!0),yy(r,n,e,!1)),n})).each(i),e},isCollapsed:function(){var e=m(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:p,setNode:function(e){return r(u.getOuterHTML(e)),e},getNode:function(){return e=c.getBody(),(t=m())?(r=t.startContainer,o=t.endContainer,i=t.startOffset,a=t.endOffset,n=t.commonAncestorContainer,!t.collapsed&&(r===o&&a-i<2&&r.hasChildNodes()&&(n=r.childNodes[i]),3===r.nodeType&&3===o.nodeType&&(r=r.length===i?Uy(r.nextSibling,!0):r.parentNode,o=0===a?Uy(o.previousSibling,!1):o.parentNode,r&&r===o))?r:n&&3===n.nodeType?n.parentNode:n):e;var e,t,n,r,o,i,a},getSel:d,setRng:i,getRng:m,getStart:function(e){return Fy(c.getBody(),m(),e)},getEnd:function(e){return zy(c.getBody(),m(),e)},getSelectedBlocks:function(e,t){return function(e,t,n,r){var o,i,a=[];if(i=e.getRoot(),n=e.getParent(n||Fy(i,t,!1),e.isBlock),r=e.getParent(r||zy(i,t,!1),e.isBlock),n&&n!==i&&a.push(n),n&&r&&n!==r)for(var u=new Zr(o=n,i);(o=u.next())&&o!==r;)e.isBlock(o)&&a.push(o);return r&&n!==r&&r!==i&&a.push(r),a}(u,m(),e,t)},normalize:function(){var e=m();if(!vd(d())){var t=Jd.normalize(u,e);return t.each(function(e){i(e,p())}),t.getOr(e)}return e},selectorChanged:function(e,t){var i;return a||(a={},i={},c.on("NodeChange",function(e){var n=e.element,r=u.getParents(n,null,u.getRoot()),o={};qy(a,function(e,n){qy(r,function(t){if(u.is(t,n))return i[n]||(qy(e,function(e){e(!0,{node:t,selector:n,parents:r})}),i[n]=e),o[n]=e,!1})}),qy(i,function(e,t){o[t]||(delete i[t],qy(e,function(e){e(!1,{node:n,selector:t,parents:r})}))})})),a[e]||(a[e]=[]),a[e].push(t),g},getScrollContainer:function(){for(var e,t=u.getRoot();t&&"BODY"!==t.nodeName;){if(t.scrollHeight>t.clientHeight){e=t;break}t=t.parentNode}return e},scrollIntoView:function(e,t){return gu(c,e,t)},placeCaretAt:function(e,t){return i(sy.fromPoint(e,t,c.getDoc()))},getBoundingClientRect:function(){var e=m();return e.collapsed?Ta.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:function(){s=l=f=null,t.destroy()}};return n=ry(g),t=ay(g,c),g.bookmarkManager=n,g.controlSelection=t,g},$y=So.isContentEditableFalse,Wy=Qi,Ky=Ns,Xy=ws,Yy=function(e,t){for(;t=e(t);)if(t.isVisible())return t;return t},Gy=function(e,t,n,r){var o,i,a,u,s,c,l=e===Ja.Forwards,f=l?Xy:Ky;return!r.collapsed&&(o=Wy(r),$y(o))?Ka(e,t,o,e===Ja.Backwards,!0):(u=Si(r.startContainer),f(i=xs(e,t.getBody(),r))?Xa(t,i.getNode(!l)):(i=n(i))?f(i)?Ka(e,t,i.getNode(!l),l,!0):f(a=n(i))&&(!(c=fs(s=i,a))&&So.isBr(s.getNode())||c)?Ka(e,t,a.getNode(!l),l,!0):u?Ga(t,i.toRange(),!0):null:u?r:null)},Jy=function(e,t,n,r){var o,i,a,u,s,c,l,f,d;if(d=Wy(r),o=xs(e,t.getBody(),r),i=n(t.getBody(),ug(1),o),a=Tt.filter(i,sg(1)),s=Tt.last(o.getClientRects()),(Xy(o)||Es(o))&&(d=o.getNode()),(Ky(o)||Ss(o))&&(d=o.getNode(!0)),!s)return null;if(c=s.left,(u=pg(a,c))&&$y(u.node))return l=Math.abs(c-u.left),f=Math.abs(c-u.right),Ka(e,t,u.node,l<f,!0);if(d){var m=function(e,t,n,r){var o,i,a,u,s,c,l=qs(t),f=[],d=0,m=function(e){return Tt.last(e.getClientRects())};1===e?(o=l.next,i=Ji,a=Gi,u=Ta.after(r)):(o=l.prev,i=Gi,a=Ji,u=Ta.before(r)),c=m(u);do{if(u.isVisible()&&!a(s=m(u),c)){if(0<f.length&&i(s,Tt.last(f))&&d++,(s=Ki(s)).position=u,s.line=d,n(s))return f;f.push(s)}}while(u=o(u));return f}(e,t.getBody(),ug(1),d);if(u=pg(Tt.filter(m,sg(1)),c))return Ga(t,u.position.toRange(),!0);if(u=Tt.last(Tt.filter(m,sg(0))))return Ga(t,u.position.toRange(),!0)}},Qy=function(e,t,n){var r,o,i,a,u=qs(e.getBody()),s=oa.curry(Yy,u.next),c=oa.curry(Yy,u.prev);if(n.collapsed&&e.settings.forced_root_block){if(!(r=e.dom.getParent(n.startContainer,"PRE")))return;(1===t?s(Ta.fromRangeStart(n)):c(Ta.fromRangeStart(n)))||(a=(i=e).dom.create(i.settings.forced_root_block),(!de.ie||11<=de.ie)&&(a.innerHTML='<br data-mce-bogus="1">'),o=a,1===t?e.$(r).after(o):e.$(r).before(o),e.selection.select(o,!0),e.selection.collapse())}},Zy=function(l,f){return function(){var e,t,n,r,o,i,a,u,s,c=(t=f,r=qs((e=l).getBody()),o=oa.curry(Yy,r.next),i=oa.curry(Yy,r.prev),a=t?Ja.Forwards:Ja.Backwards,u=t?o:i,s=e.selection.getRng(),(n=Gy(a,e,u,s))?n:(n=Qy(e,a,s))||null);return!!c&&(l.selection.setRng(c),!0)}},eb=function(u,s){return function(){var e,t,n,r,o,i,a=(r=(t=s)?1:-1,o=t?ag:ig,i=(e=u).selection.getRng(),(n=Jy(r,e,o,i))?n:(n=Qy(e,r,i))||null);return!!a&&(u.selection.setRng(a),!0)}},tb=function(e,r){return H.bind((t=e,H.map(t,function(e){return Tv.merge({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:V.noop},e)})),function(e){return t=e,(n=r).keyCode===t.keyCode&&n.shiftKey===t.shiftKey&&n.altKey===t.altKey&&n.ctrlKey===t.ctrlKey&&n.metaKey===t.metaKey?[e]:[];var t,n});var t},nb=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}},rb=function(e,t){return H.find(tb(e,t),function(e){return e.action()})},ob=function(i,a){i.on("keydown",function(e){var t,n,r,o;!1===e.isDefaultPrevented()&&(t=i,n=a,r=e,o=In.detect().os,rb([{keyCode:Cg.RIGHT,action:Zy(t,!0)},{keyCode:Cg.LEFT,action:Zy(t,!1)},{keyCode:Cg.UP,action:eb(t,!1)},{keyCode:Cg.DOWN,action:eb(t,!0)},{keyCode:Cg.RIGHT,action:Wu(t,!0)},{keyCode:Cg.LEFT,action:Wu(t,!1)},{keyCode:Cg.UP,action:Ku(t,!1)},{keyCode:Cg.DOWN,action:Ku(t,!0)},{keyCode:Cg.RIGHT,action:Yf.move(t,n,!0)},{keyCode:Cg.LEFT,action:Yf.move(t,n,!1)},{keyCode:Cg.RIGHT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:Yf.moveNextWord(t,n)},{keyCode:Cg.LEFT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:Yf.movePrevWord(t,n)}],r).each(function(e){r.preventDefault()}))})},ib=function(e){return 1===Fr.children(e).length},ab=function(e,t,n,r){var o,i,a,u,s=V.curry(Dh,t),c=H.map(H.filter(r,s),function(e){return e.dom()});if(0===c.length)rf(t,e,n);else{var l=(o=n.dom(),i=c,a=Sh(!1),u=_h(i,a.dom()),Lu.before(Fn.fromDom(o),a),_c.remove(Fn.fromDom(o)),Ta(u,0));t.selection.setRng(l.toRange())}},ub=function(n,r){var t,e=Fn.fromDom(n.getBody()),o=Fn.fromDom(n.selection.getStart()),i=H.filter((t=Bl(o,e),H.findIndex(t,io).fold(V.constant(t),function(e){return t.slice(0,e)})),ib);return H.last(i).map(function(e){var t=Ta.fromRangeStart(n.selection.getRng());return!!fl(r,t,e.dom())&&(ab(r,n,e,i),!0)}).getOr(!1)},sb=function(e,t){return!!e.selection.isCollapsed()&&ub(e,t)},cb=function(o,i){o.on("keydown",function(e){var t,n,r;!1===e.isDefaultPrevented()&&(t=o,n=i,r=e,rb([{keyCode:Cg.BACKSPACE,action:nb(uf,t,!1)},{keyCode:Cg.DELETE,action:nb(uf,t,!0)},{keyCode:Cg.BACKSPACE,action:nb(Zf,t,n,!1)},{keyCode:Cg.DELETE,action:nb(Zf,t,n,!0)},{keyCode:Cg.BACKSPACE,action:nb(Vl,t,!1)},{keyCode:Cg.DELETE,action:nb(Vl,t,!0)},{keyCode:Cg.BACKSPACE,action:nb(Fl,t,!1)},{keyCode:Cg.DELETE,action:nb(Fl,t,!0)},{keyCode:Cg.BACKSPACE,action:nb(Dd,t,!1)},{keyCode:Cg.DELETE,action:nb(Dd,t,!0)},{keyCode:Cg.BACKSPACE,action:nb(sb,t,!1)},{keyCode:Cg.DELETE,action:nb(sb,t,!0)}],r).each(function(e){r.preventDefault()}))}),o.on("keyup",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=o,n=e,rb([{keyCode:Cg.BACKSPACE,action:nb(sf,t)},{keyCode:Cg.DELETE,action:nb(sf,t)}],n))})},lb=function(e){return A.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},fb=function(e,t){var n,r,o,i=t,a=e.dom,u=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var s=function(e){for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}}(t.firstChild);s&&/^(UL|OL|DL)$/.test(s.nodeName)&&t.insertBefore(a.doc.createTextNode("\xa0"),t.firstChild)}if(o=a.createRng(),t.normalize(),t.hasChildNodes()){for(n=new Zr(t,t);r=n.current();){if(So.isText(r)){o.setStart(r,0),o.setEnd(r,0);break}if(u[r.nodeName.toLowerCase()]){o.setStartBefore(r),o.setEndBefore(r);break}i=r,r=n.next()}r||(o.setStart(i,0),o.setEnd(i,0))}else So.isBr(t)?t.nextSibling&&a.isBlock(t.nextSibling)?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)):(o.setStart(t,0),o.setEnd(t,0));e.selection.setRng(o),a.remove(void 0),e.selection.scrollIntoView(t)}},db=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},mb=lb,pb=function(e){return lb(e).fold(V.constant(""),function(e){return e.nodeName.toUpperCase()})},gb=function(e){return lb(e).filter(function(e){return lo(Fn.fromDom(e))}).isSome()},hb=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},vb=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},yb=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},bb=function(e,t,n){for(var r=e[n?"firstChild":"lastChild"];r&&!So.isElement(r);)r=r[n?"nextSibling":"previousSibling"];return r===t},Cb=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){var u;vb(u=n)&&vb(u.parentNode)&&(o="LI");var s,c,l=o?t(o):i.create("BR");if(bb(n,r,!0)&&bb(n,r,!1))hb(n,"LI")?i.insertAfter(l,yb(n)):i.replace(l,n);else if(bb(n,r,!0))hb(n,"LI")?(i.insertAfter(l,yb(n)),l.appendChild(i.doc.createTextNode(" ")),l.appendChild(n)):n.parentNode.insertBefore(l,n);else if(bb(n,r,!1))i.insertAfter(l,yb(n));else{n=yb(n);var f=a.cloneRange();f.setStartAfter(r),f.setEndAfter(n);var d=f.extractContents();"LI"===o&&(c="LI",(s=d).firstChild&&s.firstChild.nodeName===c)?(l=d.firstChild,i.insertAfter(d,n)):(i.insertAfter(d,n),i.insertAfter(l,n))}i.remove(r),fb(e,l)}},xb=function(e){e.innerHTML='<br data-mce-bogus="1">'},wb=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},Nb=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},Eb=function(e,t,n){return!1===So.isText(t)?n:e?1===n&&t.data.charAt(n-1)===xi?0:n:n===t.data.length-1&&t.data.charAt(n)===xi?t.data.length:n},Sb=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},kb=function(e,t){var n=Su(e);n&&n.toLowerCase()===t.tagName.toLowerCase()&&e.dom.setAttribs(t,ku(e))},Tb=function(a,e){var t,u,s,i,c,n,r,o,l,f,d,m,p,g,h,v,y,b,C=a.dom,x=a.schema,w=x.getNonEmptyElements(),N=a.selection.getRng(),E=function(e){var t,n,r,o=s,i=x.getTextInlineElements();if(e||"TABLE"===f||"HR"===f?(t=C.create(e||m),kb(a,t)):t=c.cloneNode(!1),r=t,!1===Ru(a))C.setAttrib(t,"style",null),C.setAttrib(t,"class",null);else do{if(i[o.nodeName]){if(hc(o))continue;n=o.cloneNode(!1),C.setAttrib(n,"id",""),t.hasChildNodes()?n.appendChild(t.firstChild):r=n,t.appendChild(n)}}while((o=o.parentNode)&&o!==u);return xb(r),t},S=function(e){var t,n,r,o;if(o=Eb(e,s,i),So.isText(s)&&(e?0<o:o<s.nodeValue.length))return!1;if(s.parentNode===c&&p&&!e)return!0;if(e&&So.isElement(s)&&s===c.firstChild)return!0;if(wb(s,"TABLE")||wb(s,"HR"))return p&&!e||!p&&e;for(t=new Zr(s,c),So.isText(s)&&(e&&0===o?t.prev():e||o!==s.nodeValue.length||t.next());n=t.current();){if(So.isElement(n)){if(!n.getAttribute("data-mce-bogus")&&(r=n.nodeName.toLowerCase(),w[r]&&"br"!==r))return!1}else if(So.isText(n)&&!/^[ \t\r\n]*$/.test(n.nodeValue))return!1;e?t.prev():t.next()}return!0},k=function(){r=/^(H[1-6]|PRE|FIGURE)$/.test(f)&&"HGROUP"!==d?E(m):E(),_u(a)&&Nb(C,l)&&C.isEmpty(c)?r=C.split(l,c):C.insertAfter(r,c),fb(a,r)};Jd.normalize(C,N).each(function(e){N.setStart(e.startContainer,e.startOffset),N.setEnd(e.endContainer,e.endOffset)}),s=N.startContainer,i=N.startOffset,m=Su(a),n=e.shiftKey,So.isElement(s)&&s.hasChildNodes()&&(p=i>s.childNodes.length-1,s=s.childNodes[Math.min(i,s.childNodes.length-1)]||s,i=p&&So.isText(s)?s.nodeValue.length:0),(u=Sb(C,s))&&((m&&!n||!m&&n)&&(s=function(e,t,n,r,o){var i,a,u,s,c,l,f,d=t||"P",m=e.dom,p=Sb(m,r);if(!(a=m.getParent(r,m.isBlock))||!Nb(m,a)){if(l=(a=a||p)===e.getBody()||(f=a)&&/^(TD|TH|CAPTION)$/.test(f.nodeName)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=m.create(d),kb(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;for(s=r;s.parentNode!==a;)s=s.parentNode;for(;s&&!m.isBlock(s);)s=(u=s).previousSibling;if(u&&e.schema.isValidChild(l,d.toLowerCase())){for(i=m.create(d),kb(e,i),u.parentNode.insertBefore(i,u),s=u;s&&!m.isBlock(s);)c=s.nextSibling,i.appendChild(s),s=c;n.setStart(r,o),n.setEnd(r,o)}}return r}(a,m,N,s,i)),c=C.getParent(s,C.isBlock),l=c?C.getParent(c.parentNode,C.isBlock):null,f=c?c.nodeName.toUpperCase():"","LI"!==(d=l?l.nodeName.toUpperCase():"")||e.ctrlKey||(l=(c=l).parentNode,f=d),/^(LI|DT|DD)$/.test(f)&&C.isEmpty(c)?Cb(a,E,l,c,m):m&&c===a.getBody()||(m=m||"P",Si(c)?(r=Pi(c),C.isEmpty(c)&&xb(c),fb(a,r)):S()?k():S(!0)?(r=c.parentNode.insertBefore(E(),c),fb(a,wb(c,"HR")?r:c)):((t=(y=N,b=y.cloneRange(),b.setStart(y.startContainer,Eb(!0,y.startContainer,y.startOffset)),b.setEnd(y.endContainer,Eb(!1,y.endContainer,y.endOffset)),b).cloneRange()).setEndAfter(c),function(e){for(;So.isText(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild;);}(o=t.extractContents()),r=o.firstChild,C.insertAfter(o,c),function(e,t,n){var r,o=n,i=[];if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;So.isElement(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}for(r=i.length;r--;)!(o=i[r]).hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue?e.remove(o):(a=o)&&"A"===a.nodeName&&0===Dt.trim(wi(a.innerText||a.textContent)).length&&e.remove(o);var a}}(C,w,r),g=C,(h=c).normalize(),(v=h.lastChild)&&!/^(left|right)$/gi.test(g.getStyle(v,"float",!0))||g.add(h,"br"),C.isEmpty(c)&&xb(c),r.normalize(),C.isEmpty(r)?(C.remove(r),k()):fb(a,r)),C.setAttrib(r,"id",""),a.fire("NewBlock",{newBlock:r})))},Ab=function(e,t){return mb(e).filter(function(e){return 0<t.length&&Tr.is(Fn.fromDom(e),t)}).isSome()},Rb=function(e){return Ab(e,Tu(e))},_b=function(e){return Ab(e,Au(e))},Bb=Hl([{br:[]},{block:[]},{none:[]}]),Db=function(e,t){return _b(e)},Ob=function(n){return function(e,t){return""===Su(e)===n}},Pb=function(n){return function(e,t){return gb(e)===n}},Lb=function(n){return function(e,t){return"PRE"===pb(e)===n}},Ib=function(n){return function(e,t){return Eu(e)===n}},Mb=function(e,t){return Rb(e)},Fb=function(e,t){return t},zb=function(e){var t=Su(e),n=db(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},Ub=function(e,t){return function(n,r){return H.foldl(e,function(e,t){return e&&t(n,r)},!0)?A.some(t):A.none()}},qb=function(e,t){return bf([Ub([Db],Bb.none()),Ub([Lb(!0),Ib(!1),Fb],Bb.br()),Ub([Lb(!0),Ib(!1)],Bb.block()),Ub([Lb(!0),Ib(!0),Fb],Bb.block()),Ub([Lb(!0),Ib(!0)],Bb.br()),Ub([Pb(!0),Fb],Bb.br()),Ub([Pb(!0)],Bb.block()),Ub([Ob(!0),Fb,zb],Bb.block()),Ub([Ob(!0)],Bb.br()),Ub([Mb],Bb.br()),Ub([Ob(!1),Fb],Bb.br()),Ub([zb],Bb.block())],[e,t.shiftKey]).getOr(Bb.none())},Vb=function(e,t){qb(e,t).fold(function(){um.insert(e,t)},function(){Tb(e,t)},V.noop)},Hb=function(o){o.on("keydown",function(e){var t,n,r;e.keyCode===Cg.ENTER&&(t=o,(n=e).isDefaultPrevented()||(n.preventDefault(),(r=t.undoManager).typing&&(r.typing=!1,r.add()),t.undoManager.transact(function(){!1===t.selection.isCollapsed()&&t.execCommand("Delete"),Vb(t,n)})))})},jb=function(e,t,n){return u=t,!(!$b(n)||!So.isText(u.container())||(r=e,i=(o=t).container(),a=o.offset(),i.insertData(a,"\xa0"),r.selection.setCursorLocation(i,a+1),0));var r,o,i,a,u},$b=function(e){return e.fold(V.constant(!1),V.constant(!0),V.constant(!0),V.constant(!1))},Wb=function(e){return!!e.selection.isCollapsed()&&(t=e,n=V.curry(sl.isInlineTarget,t),r=Ta.fromRangeStart(t.selection.getRng()),Mf(n,t.getBody(),r).map(V.curry(jb,t,r)).getOr(!1));var t,n,r},Kb=function(r){r.on("keydown",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=r,n=e,rb([{keyCode:Cg.SPACEBAR,action:nb(Wb,t)}],n).each(function(e){n.preventDefault()}))})},Xb=function(e,t){var n;t.hasAttribute("data-mce-caret")&&(Pi(t),(n=e).selection.setRng(n.selection.getRng()),e.selection.scrollIntoView(t))},Yb=function(e,t){var n,r=(n=e,pl(Fn.fromDom(n.getBody()),"*[data-mce-caret]").fold(V.constant(null),function(e){return e.dom()}));if(r)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void Xb(e,r)):void(Ai(r)&&(Xb(e,r),e.undoManager.add()))},Gb=function(e){e.on("keyup compositionstart",V.curry(Yb,e))},Jb=function(e){var t=Yf.setupSelectedState(e);Gb(e),ob(e,t),cb(e,t),Hb(e),Kb(e)};function Qb(u){var s,n,r,o=Dt.each,c=Cg.BACKSPACE,l=Cg.DELETE,f=u.dom,d=u.selection,e=u.settings,t=u.parser,i=de.gecko,a=de.ie,m=de.webkit,p="data:text/mce-internal,",g=a?"Text":"URL",h=function(e,t){try{u.getDoc().execCommand(e,!1,t)}catch(n){}},v=function(e){return e.isDefaultPrevented()},y=function(){u.shortcuts.add("meta+a",null,"SelectAll")},b=function(){u.on("keydown",function(e){if(!v(e)&&e.keyCode===c&&d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}})},C=function(){u.inline||(u.contentStyles.push("body {min-height: 150px}"),u.on("click",function(e){var t;if("HTML"===e.target.nodeName){if(11<de.ie)return void u.getBody().focus();t=u.selection.getRng(),u.getBody().focus(),u.selection.setRng(t),u.selection.normalize(),u.nodeChanged()}}))};return u.on("keydown",function(e){var t,n,r,o,i;if(!v(e)&&e.keyCode===Cg.BACKSPACE&&(n=(t=d.getRng()).startContainer,r=t.startOffset,o=f.getRoot(),i=n,t.collapsed&&0===r)){for(;i&&i.parentNode&&i.parentNode.firstChild===i&&i.parentNode!==o;)i=i.parentNode;"BLOCKQUOTE"===i.tagName&&(u.formatter.toggle("blockquote",null,i),(t=f.createRng()).setStart(n,0),t.setEnd(n,0),d.setRng(t))}}),s=function(e){var t=f.create("body"),n=e.cloneContents();return t.appendChild(n),d.serializer.serialize(t,{format:"html"})},u.on("keydown",function(e){var t,n,r,o,i,a=e.keyCode;if(!v(e)&&(a===l||a===c)){if(t=u.selection.isCollapsed(),n=u.getBody(),t&&!f.isEmpty(n))return;if(!t&&(r=u.selection.getRng(),o=s(r),(i=f.createRng()).selectNode(u.getBody()),o!==s(i)))return;e.preventDefault(),u.setContent(""),n.firstChild&&f.isBlock(n.firstChild)?u.selection.setCursorLocation(n.firstChild,0):u.selection.setCursorLocation(n,0),u.nodeChanged()}}),de.windowsPhone||u.on("keyup focusin mouseup",function(e){Cg.modifierPressed(e)||d.normalize()},!0),m&&(u.settings.content_editable||f.bind(u.getDoc(),"mousedown mouseup",function(e){var t;if(e.target===u.getDoc().documentElement)if(t=d.getRng(),u.getBody().focus(),"mousedown"===e.type){if(Ti(t.startContainer))return;d.placeCaretAt(e.clientX,e.clientY)}else d.setRng(t)}),u.on("click",function(e){var t=e.target;/^(IMG|HR)$/.test(t.nodeName)&&"false"!==f.getContentEditableParent(t)&&(e.preventDefault(),u.selection.select(t),u.nodeChanged()),"A"===t.nodeName&&f.hasClass(t,"mce-item-anchor")&&(e.preventDefault(),d.select(t))}),e.forced_root_block&&u.on("init",function(){h("DefaultParagraphSeparator",e.forced_root_block)}),u.on("init",function(){u.dom.bind(u.getBody(),"submit",function(e){e.preventDefault()})}),b(),t.addNodeFilter("br",function(e){for(var t=e.length;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}),de.iOS?(u.inline||u.on("keydown",function(){document.activeElement===document.body&&u.getWin().focus()}),C(),u.on("click",function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)}),u.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")):y()),11<=de.ie&&(C(),b()),de.ie&&(y(),h("AutoUrlDetect",!1),u.on("dragstart",function(e){var t,n,r;(t=e).dataTransfer&&(u.selection.isCollapsed()&&"IMG"===t.target.tagName&&d.select(t.target),0<(n=u.selection.getContent()).length&&(r=p+escape(u.id)+","+escape(n),t.dataTransfer.setData(g,r)))}),u.on("drop",function(e){if(!v(e)){var t=(i=e).dataTransfer&&(a=i.dataTransfer.getData(g))&&0<=a.indexOf(p)?(a=a.substr(p.length).split(","),{id:unescape(a[0]),html:unescape(a[1])}):null;if(t&&t.id!==u.id){e.preventDefault();var n=sy.fromPoint(e.x,e.y,u.getDoc());d.setRng(n),r=t.html,o=!0,u.queryCommandSupported("mceInsertClipboardContent")?u.execCommand("mceInsertClipboardContent",!1,{content:r,internal:o}):u.execCommand("mceInsertContent",!1,r)}}var r,o,i,a})),i&&(u.on("keydown",function(e){if(!v(e)&&e.keyCode===c){if(!u.getBody().getElementsByTagName("hr").length)return;if(d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode(),n=t.previousSibling;if("HR"===t.nodeName)return f.remove(t),void e.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(f.remove(n),e.preventDefault())}}}),Range.prototype.getClientRects||u.on("mousedown",function(e){if(!v(e)&&"HTML"===e.target.nodeName){var t=u.getBody();t.blur(),ve.setEditorTimeout(u,function(){t.focus()})}}),n=function(){var e=f.getAttribs(d.getStart().cloneNode(!1));return function(){var t=d.getStart();t!==u.getBody()&&(f.setAttrib(t,"style",null),o(e,function(e){t.setAttributeNode(e.cloneNode(!0))}))}},r=function(){return!d.isCollapsed()&&f.getParent(d.getStart(),f.isBlock)!==f.getParent(d.getEnd(),f.isBlock)},u.on("keypress",function(e){var t;if(!v(e)&&(8===e.keyCode||46===e.keyCode)&&r())return t=n(),u.getDoc().execCommand("delete",!1,null),t(),e.preventDefault(),!1}),f.bind(u.getDoc(),"cut",function(e){var t;!v(e)&&r()&&(t=n(),ve.setEditorTimeout(u,function(){t()}))}),e.readonly||u.on("BeforeExecCommand MouseDown",function(){h("StyleWithCSS",!1),h("enableInlineTableEditing",!1),e.object_resizing||h("enableObjectResizing",!1)}),u.on("SetContent ExecCommand",function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||o(f.select("a"),function(e){var t=e.parentNode,n=f.getRoot();if(t.lastChild===e){for(;t&&!f.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}f.add(t,"br",{"data-mce-bogus":1})}})}),u.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"),de.mac&&u.on("keydown",function(e){!Cg.metaKeyPressed(e)||e.shiftKey||37!==e.keyCode&&39!==e.keyCode||(e.preventDefault(),u.selection.getSel().modify("move",37===e.keyCode?"backward":"forward","lineboundary"))}),b()),{refreshContentEditable:function(){},isHidden:function(){var e;return!i||u.removed?0:!(e=u.selection.getSel())||!e.rangeCount||0===e.rangeCount}}}var Zb=function(e){return So.isElement(e)&&so(Fn.fromDom(e))},eC=function(t){t.on("click",function(e){3===e.detail&&function(e){var t=e.selection.getRng(),n=Ea.fromRangeStart(t),r=Ea.fromRangeEnd(t);if(Ea.isElementPosition(n)){var o=n.container();Zb(o)&&ja.firstPositionIn(o).each(function(e){return t.setStart(e.container(),e.offset())})}Ea.isElementPosition(r)&&(o=n.container(),Zb(o)&&ja.lastPositionIn(o).each(function(e){return t.setEnd(e.container(),e.offset())})),e.selection.setRng(Uc(t))}(t)})},tC=ci.DOM,nC=function(e){var t;e.bindPendingEventDelegates(),e.initialized=!0,e.fire("init"),e.focus(!0),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),(t=e).settings.auto_focus&&ve.setEditorTimeout(t,function(){var e;(e=!0===t.settings.auto_focus?t:t.editorManager.get(t.settings.auto_focus)).destroyed||e.focus()},100)},rC=function(t,e){var n,r,u,o,i,a,s,c,l,f=t.settings,d=t.getElement(),m=t.getDoc();f.inline||(t.getElement().style.visibility=t.orgVisibility),e||f.content_editable||(m.open(),m.write(t.iframeHTML),m.close()),f.content_editable&&(t.on("remove",function(){var e=this.getBody();tC.removeClass(e,"mce-content-body"),tC.removeClass(e,"mce-edit-focus"),tC.setAttrib(e,"contentEditable",null)}),tC.addClass(d,"mce-content-body"),t.contentDocument=m=f.content_document||document,t.contentWindow=f.content_window||window,t.bodyElement=d,f.content_document=f.content_window=null,f.root_name=d.nodeName.toLowerCase()),(n=t.getBody()).disabled=!0,t.readonly=f.readonly,t.readonly||(t.inline&&"static"===tC.getStyle(n,"position",!0)&&(n.style.position="relative"),n.contentEditable=t.getParam("content_editable_state",!0)),n.disabled=!1,t.editorUpload=zp(t),t.schema=Go(f),t.dom=ci(m,{keep_values:!0,url_converter:t.convertURL,url_converter_scope:t,hex_colors:f.force_hex_style_colors,class_filter:f.class_filter,update_styles:!0,root_element:t.inline?t.getBody():null,collect:f.content_editable,schema:t.schema,onSetAttrib:function(e){t.fire("SetAttrib",e)}}),t.parser=((o=Gv((u=t).settings,u.schema)).addAttributeFilter("src,href,style,tabindex",function(e,t){for(var n,r,o,i=e.length,a=u.dom;i--;)if(r=(n=e[i]).attr(t),o="data-mce-"+t,!n.attributes.map[o]){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===t?((r=a.serializeStyle(a.parseStyle(r),n.name)).length||(r=null),n.attr(o,r),n.attr(t,r)):"tabindex"===t?(n.attr(o,r),n.attr(t,null)):n.attr(o,u.convertURL(r,t,n.name))}}),o.addNodeFilter("script",function(e){for(var t,n,r=e.length;r--;)0!==(n=(t=e[r]).attr("type")||"no/type").indexOf("mce-")&&t.attr("type","mce-"+n)}),o.addNodeFilter("#cdata",function(e){for(var t,n=e.length;n--;)(t=e[n]).type=8,t.name="#comment",t.value="[CDATA["+t.value+"]]"}),o.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",function(e){for(var t,n=e.length,r=u.schema.getNonEmptyElements();n--;)(t=e[n]).isEmpty(r)&&0===t.getAll("br").length&&(t.append(new zv("br",1)).shortEnded=!0)}),o),t.serializer=ty(f,t),t.selection=jy(t.dom,t.getWin(),t.serializer,t),t.formatter=Nv(t),t.undoManager=Kg(t),t._nodeChangeDispatcher=new jp(t),t._selectionOverrides=kg(t),eC(t),Jb(t),Hp(t),t.fire("PreInit"),f.browser_spellcheck||f.gecko_spellcheck||(m.body.spellcheck=!1,tC.setAttrib(n,"spellcheck","false")),t.quirks=Qb(t),t.fire("PostRender"),f.directionality&&(n.dir=f.directionality),f.nowrap&&(n.style.whiteSpace="nowrap"),f.protect&&t.on("BeforeSetContent",function(t){Dt.each(f.protect,function(e){t.content=t.content.replace(e,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})})}),t.on("SetContent",function(){t.addVisual(t.getBody())}),f.padd_empty_editor&&t.on("PostProcess",function(e){e.content=e.content.replace(/^(<p[^>]*>(&nbsp;|&#160;|\s|\u00a0|<br \/>|)<\/p>[\r\n]*|<br \/>[\r\n]*)$/,"")}),t.load({initial:!0,format:"html"}),t.startContent=t.getContent({format:"raw"}),t.on("compositionstart compositionend",function(e){t.composing="compositionstart"===e.type}),0<t.contentStyles.length&&(r="",Dt.each(t.contentStyles,function(e){r+=e+"\r\n"}),t.dom.addStyle(r)),(i=t,i.inline?tC.styleSheetLoader:i.dom.styleSheetLoader).loadAll(t.contentCSS,function(e){nC(t)},function(e){nC(t)}),f.content_style&&(a=t,s=f.content_style,c=Fn.fromDom(a.getDoc().head),l=Fn.fromTag("style"),sr.set(l,"type","text/css"),Lu.append(l,Fn.fromText(s)),Lu.append(c,l))},oC=ci.DOM,iC=function(e,t){var n,r,o,i,a,u,s,c=e.editorManager.translate("Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help"),l=(n=e.id,r=c,o=t.height,i=yu(e),s=Fn.fromTag("iframe"),sr.setAll(s,i),sr.setAll(s,{id:n+"_ifr",frameBorder:"0",allowTransparency:"true",title:r}),gr(s,{width:"100%",height:(a=o,u="number"==typeof a?a+"px":a,u||""),display:"block"}),s).dom();l.onload=function(){l.onload=null,e.fire("load")};var f,d,m,p,g=function(e,t){if(document.domain!==window.location.hostname&&de.ie&&de.ie<12){var n=Fp.uuid("mce");e[n]=function(){rC(e)};var r='javascript:(function(){document.open();document.domain="'+document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return oC.setAttrib(t,"src",r),!0}return!1}(e,l);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=l,e.iframeHTML=(p=bu(f=e)+"<html><head>",Cu(f)!==f.documentBaseUrl&&(p+='<base href="'+f.documentBaseURI.getURI()+'" />'),p+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />',d=xu(f),m=wu(f),Nu(f)&&(p+='<meta http-equiv="Content-Security-Policy" content="'+Nu(f)+'" />'),p+='</head><body id="'+d+'" class="mce-content-body '+m+'" data-id="'+f.id+'"><br></body></html>'),oC.add(t.iframeContainer,l),g},aC=function(e,t){var n=iC(e,t);t.editorContainer&&(oC.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=oC.isHidden(t.editorContainer)),e.getElement().style.display="none",oC.setAttrib(e.id,"aria-hidden","true"),n||rC(e)},uC=ci.DOM,sC=function(t,n,e){var r,o,i=xp.get(e);if(r=xp.urls[e]||t.documentBaseUrl.replace(/\/$/,""),e=Dt.trim(e),i&&-1===Dt.inArray(n,e)){if(Dt.each(xp.dependencies(e),function(e){sC(t,n,e)}),t.plugins[e])return;o=new i(t,r,t.$),(t.plugins[e]=o).init&&(o.init(t,r),n.push(e))}},cC=function(e){return e.replace(/^\-/,"")},lC=function(e){return{editorContainer:e,iframeContainer:e}},fC=function(e){var t,n,r=e.getElement();return e.inline?lC(null):(t=r,n=uC.create("div"),uC.insertAfter(n,t),lC(n))},dC=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.getElement();return e.orgDisplay=m.style.display,Jn.isString(d.theme)?(l=(o=e).settings,f=o.getElement(),i=l.width||uC.getStyle(f,"width")||"100%",a=l.height||uC.getStyle(f,"height")||f.offsetHeight,u=l.min_height||100,(s=/^[0-9\.]+(|px)$/i).test(""+i)&&(i=Math.max(parseInt(i,10),100)),s.test(""+a)&&(a=Math.max(parseInt(a,10),u)),c=o.theme.renderUI({targetNode:f,width:i,height:a,deltaWidth:l.delta_width,deltaHeight:l.delta_height}),l.content_editable||(a=(c.iframeHeight||a)+("number"==typeof a?c.deltaHeight||0:""))<u&&(a=u),c.height=a,c):Jn.isFunction(d.theme)?(r=(t=e).getElement(),(n=t.settings.theme(t,r)).editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||t.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||t.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:r.offsetHeight,n):fC(e)},mC=function(t){var e,n,r,o,i,a,u=t.settings,s=t.getElement();return t.rtl=u.rtl_ui||t.editorManager.i18n.rtl,t.editorManager.i18n.setCode(u.language),u.aria_label=u.aria_label||uC.getAttrib(s,"aria-label",t.getLang("aria.rich_text_area")),t.fire("ScriptsLoaded"),o=(n=t).settings.theme,Jn.isString(o)?(n.settings.theme=cC(o),r=wp.get(o),n.theme=new r(n,wp.urls[o]),n.theme.init&&n.theme.init(n,wp.urls[o]||n.documentBaseUrl.replace(/\/$/,""),n.$)):n.theme={},i=t,a=[],Dt.each(i.settings.plugins.split(/[ ,]/),function(e){sC(i,a,cC(e))}),e=dC(t),t.editorContainer=e.editorContainer?e.editorContainer:null,u.content_css&&Dt.each(Dt.explode(u.content_css),function(e){t.contentCSS.push(t.documentBaseURI.toAbsolute(e))}),u.content_editable?rC(t):aC(t,e)},pC=ci.DOM,gC=function(e){return"-"===e.charAt(0)},hC=function(i,a){var u=pi.ScriptLoader;!function(e,t,n,r){var o=t.settings,i=o.theme;if(Jn.isString(i)){if(!gC(i)&&!wp.urls.hasOwnProperty(i)){var a=o.theme_url;a?wp.load(i,t.documentBaseURI.toAbsolute(a)):wp.load(i,"themes/"+i+"/theme"+n+".js")}e.loadQueue(function(){wp.waitFor(i,r)})}else r()}(u,i,a,function(){var e,t,n,r,o;e=u,(n=(t=i).settings).language&&"en"!==n.language&&!n.language_url&&(n.language_url=t.editorManager.baseURL+"/langs/"+n.language+".js"),n.language_url&&!t.editorManager.i18n.data[n.language]&&e.add(n.language_url),r=i.settings,o=a,Dt.isArray(r.plugins)&&(r.plugins=r.plugins.join(" ")),Dt.each(r.external_plugins,function(e,t){xp.load(t,e),r.plugins+=" "+t}),Dt.each(r.plugins.split(/[ ,]/),function(e){if((e=Dt.trim(e))&&!xp.urls[e])if(gC(e)){e=e.substr(1,e.length);var t=xp.dependencies(e);Dt.each(t,function(e){var t={prefix:"plugins/",resource:e,suffix:"/plugin"+o+".js"};e=xp.createUrl(t,e),xp.load(e.resource,e)})}else xp.load(e,{prefix:"plugins/",resource:e,suffix:"/plugin"+o+".js"})}),u.loadQueue(function(){i.removed||mC(i)},i,function(e){Cp.pluginLoadError(i,e[0]),i.removed||mC(i)})})},vC=function(t){var e=t.settings,n=t.id,r=function(){pC.unbind(window,"ready",r),t.render()};if(ke.Event.domLoaded){if(t.getElement()&&de.contentEditable){e.inline?t.inline=!0:(t.orgVisibility=t.getElement().style.visibility,t.getElement().style.visibility="hidden");var o=t.getElement().form||pC.getParent(n,"form");o&&(t.formElement=o,e.hidden_input&&!/TEXTAREA|INPUT/i.test(t.getElement().nodeName)&&(pC.insertAfter(pC.create("input",{type:"hidden",name:n}),n),t.hasHiddenInput=!0),t.formEventDelegate=function(e){t.fire(e.type,e)},pC.bind(o,"submit reset",t.formEventDelegate),t.on("reset",function(){t.setContent(t.startContent,{format:"raw"})}),!e.submit_patch||o.submit.nodeType||o.submit.length||o._mceOldSubmit||(o._mceOldSubmit=o.submit,o.submit=function(){return t.editorManager.triggerSave(),t.setDirty(!1),o._mceOldSubmit(o)})),t.windowManager=gp(t),t.notificationManager=pp(t),"xml"===e.encoding&&t.on("GetContent",function(e){e.save&&(e.content=pC.encode(e.content))}),e.add_form_submit_trigger&&t.on("submit",function(){t.initialized&&t.save()}),e.add_unload_trigger&&(t._beforeUnload=function(){!t.initialized||t.destroyed||t.isHidden()||t.save({format:"raw",no_events:!0,set_dirty:!1})},t.editorManager.on("BeforeUnload",t._beforeUnload)),t.editorManager.add(t),hC(t,t.suffix)}}else pC.bind(window,"ready",r)},yC=function(e,t,n){var r=e.sidebars?e.sidebars:[];r.push({name:t,settings:n}),e.sidebars=r},bC=Dt.each,CC=Dt.trim,xC="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),wC={ftp:21,http:80,https:443,mailto:25},NC=function(r,e){var t,n,o=this;if(r=CC(r),t=(e=o.settings=e||{}).base_uri,/^([\w\-]+):([^\/]{2})/i.test(r)||/^\s*#/.test(r))o.source=r;else{var i=0===r.indexOf("//");0!==r.indexOf("/")||i||(r=(t&&t.protocol||"http")+"://mce_host"+r),/^[\w\-]*:?\/\//.test(r)||(n=e.base_uri?e.base_uri.path:new NC(document.location.href).directory,""==e.base_uri.protocol?r="//mce_host"+o.toAbsPath(n,r):(r=/([^#?]*)([#?]?.*)/.exec(r),r=(t&&t.protocol||"http")+"://mce_host"+o.toAbsPath(n,r[1])+r[2])),r=r.replace(/@@/g,"(mce_at)"),r=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(r),bC(xC,function(e,t){var n=r[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n}),t&&(o.protocol||(o.protocol=t.protocol),o.userInfo||(o.userInfo=t.userInfo),o.port||"mce_host"!==o.host||(o.port=t.port),o.host&&"mce_host"!==o.host||(o.host=t.host),o.source=""),i&&(o.protocol="")}};NC.prototype={setPath:function(e){e=/^(.*?)\/?(\w+)?$/.exec(e),this.path=e[0],this.directory=e[1],this.file=e[2],this.source="",this.getURI()},toRelative:function(e){var t;if("./"===e)return e;if("mce_host"!==(e=new NC(e,{base_uri:this})).host&&this.host!==e.host&&e.host||this.port!==e.port||this.protocol!==e.protocol&&""!==e.protocol)return e.getURI();var n=this.getURI(),r=e.getURI();return n===r||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===r?n:(t=this.toRelPath(this.path,e.path),e.query&&(t+="?"+e.query),e.anchor&&(t+="#"+e.anchor),t)},toAbsolute:function(e,t){return(e=new NC(e,{base_uri:this})).getURI(t&&this.isSameOrigin(e))},isSameOrigin:function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=wC[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},toRelPath:function(e,t){var n,r,o,i=0,a="";if(e=(e=e.substring(0,e.lastIndexOf("/"))).split("/"),n=t.split("/"),e.length>=n.length)for(r=0,o=e.length;r<o;r++)if(r>=n.length||e[r]!==n[r]){i=r+1;break}if(e.length<n.length)for(r=0,o=n.length;r<o;r++)if(r>=e.length||e[r]!==n[r]){i=r+1;break}if(1===i)return t;for(r=0,o=e.length-(i-1);r<o;r++)a+="../";for(r=i-1,o=n.length;r<o;r++)a+=r!==i-1?"/"+n[r]:n[r];return a},toAbsPath:function(e,t){var n,r,o,i=0,a=[];for(r=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),bC(e,function(e){e&&a.push(e)}),e=a,n=t.length-1,a=[];0<=n;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?0<i?i--:a.push(t[n]):i++);return 0!==(o=(n=e.length-i)<=0?a.reverse().join("/"):e.slice(0,n).join("/")+"/"+a.reverse().join("/")).indexOf("/")&&(o="/"+o),r&&o.lastIndexOf("/")!==o.length-1&&(o+=r),o},getURI:function(e){var t,n=this;return n.source&&!e||(t="",e||(n.protocol?t+=n.protocol+"://":t+="//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},NC.parseDataUri=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},NC.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t};var EC=function(e,t){t(e),e.firstChild&&EC(e.firstChild,t),e.next&&EC(e.next,t)},SC=function(e,t,n){var r=function(e,n,t){var r={},o={},i=[];for(var a in t.firstChild&&EC(t.firstChild,function(t){H.each(e,function(e){e.name===t.name&&(r[e.name]?r[e.name].nodes.push(t):r[e.name]={filter:e,nodes:[t]})}),H.each(n,function(e){"string"==typeof t.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(t):o[e.name]={filter:e,nodes:[t]})})}),r)r.hasOwnProperty(a)&&i.push(r[a]);for(var u in o)o.hasOwnProperty(u)&&i.push(o[u]);return i}(e,t,n);H.each(r,function(t){H.each(t.filter.callbacks,function(e){e(t.nodes,t.filter.name,{})})})},kC=function(e){return e instanceof zv},TC=function(u,s,c){return void 0===c&&(c={}),c.format=c.format?c.format:"html",c.set=!0,c.content=kC(s)?"":s,kC(s)||c.no_events||(u.fire("BeforeSetContent",c),s=c.content),A.from(u.getBody()).fold(V.constant(s),function(e){return kC(s)?function(e,t,n,r){SC(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);var o=Fc({validate:e.validate},e.schema).serialize(n);return r.content=Dt.trim(o),e.dom.setHTML(t,r.content),r.no_events||e.fire("SetContent",r),n}(u,e,s,c):(t=u,n=e,o=c,0===(r=s).length||/^\s+$/.test(r)?(a='<br data-mce-bogus="1">',"TABLE"===n.nodeName?r="<tr><td>"+a+"</td></tr>":/^(UL|OL)$/.test(n.nodeName)&&(r="<li>"+a+"</li>"),(i=t.settings.forced_root_block)&&t.schema.isValidChild(n.nodeName.toLowerCase(),i.toLowerCase())?(r=a,r=t.dom.createHTML(i,t.settings.forced_root_block_attrs,r)):r||(r='<br data-mce-bogus="1">'),t.dom.setHTML(n,r),t.fire("SetContent",o)):("raw"!==o.format&&(r=Fc({validate:t.validate},t.schema).serialize(t.parser.parse(r,{isRootContent:!0,insert:!0}))),o.content=Dt.trim(r),t.dom.setHTML(n,o.content),o.no_events||t.fire("SetContent",o)),o.content);var t,n,r,o,i,a})},AC=function(t,n){return void 0===n&&(n={}),A.from(t.getBody()).fold(V.constant("tree"===n.format?new zv("body",11):""),function(e){return function(e,t,n){var r;if(t.format=t.format?t.format:"html",t.get=!0,t.getInner=!0,t.no_events||e.fire("BeforeGetContent",t),"raw"===t.format)r=Dt.trim(Pg.trimExternal(e.serializer,n.innerHTML));else if("text"===t.format)r=wi(n.innerText||n.textContent);else{if("tree"===t.format)return e.serializer.serialize(n,t);r=e.serializer.serialize(n,t)}return"text"!==t.format?t.content=Dt.trim(r):t.content=r,t.no_events||e.fire("GetContent",t),t.content}(t,n,e)})},RC=ci.DOM,_C=function(e){return A.from(e).each(function(e){return e.destroy()})},BC=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save(),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&RC.remove(o.nextSibling),!e.inline&&r&&(i=e,RC.setStyle(i.id,"display",i.orgDisplay)),_v(e),e.editorManager.remove(e),RC.remove(e.getContainer()),_C(t),_C(n),e.destroy()}var i},DC=function(e,t){var n,r,o,i=e.selection,a=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),_C(i),_C(a)),(r=(n=e).formElement)&&(r._mceOldSubmit&&(r.submit=r._mceOldSubmit,r._mceOldSubmit=null),RC.unbind(r,"submit reset",n.formEventDelegate)),(o=e).contentAreaContainer=o.formElement=o.container=o.editorContainer=null,o.bodyElement=o.contentDocument=o.contentWindow=null,o.iframeElement=o.targetElm=null,o.selection&&(o.selection=o.selection.win=o.selection.dom=o.selection.dom.doc=null),e.destroyed=!0):e.remove())},OC=ci.DOM,PC=Dt.extend,LC=Dt.each,IC=Dt.resolve,MC=de.ie,FC=function(e,t,n){var r,o,i,a,u,s,c,l=this,f=l.documentBaseUrl=n.documentBaseURL,d=n.baseURI;r=l,o=e,i=f,a=n.defaultSettings,u=t,c={id:o,theme:"modern",delta_width:0,delta_height:0,popup_css:"",plugins:"",document_base_url:i,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_style_values:"xx-small,x-small,small,medium,large,x-large,xx-large",font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,padd_empty_editor:!0,render_ui:!0,indentation:"30px",inline_styles:!0,convert_fonts_to_spans:!0,indent:"simple",indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:(s=r).convertURL,url_converter_scope:s,ie7_compat:!0},t=nl(Gc,c,a,u),l.settings=t,vi.language=t.language||"en",vi.languageLoad=t.language_load,vi.baseURL=n.baseURL,l.id=e,l.setDirty(!1),l.plugins={},l.documentBaseURI=new NC(t.document_base_url,{base_uri:d}),l.baseURI=d,l.contentCSS=[],l.contentStyles=[],l.shortcuts=new Ym(l),l.loadedCSS={},l.editorCommands=new Bm(l),l.suffix=n.suffix,l.editorManager=n,l.inline=t.inline,l.buttons={},l.menuItems={},t.cache_suffix&&(de.cacheSuffix=t.cache_suffix.replace(/^[\?\&]+/,"")),!1===t.override_viewport&&(de.overrideViewPort=!1),n.fire("SetupEditor",{editor:l}),l.execCallback("setup",l),l.$=Jt.overrideDefaults(function(){return{context:l.inline?l.getBody():l.getDoc(),element:l.getBody()}})};PC(FC.prototype={render:function(){vC(this)},focus:function(e){ip(this,e)},hasFocus:function(){return ap(this)},execCallback:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,o=this.settings[e];if(o)return this.callbackLookup&&(r=this.callbackLookup[e])&&(o=r.func,r=r.scope),"string"==typeof o&&(r=(r=o.replace(/\.\w+$/,""))?IC(r):0,o=IC(o),this.callbackLookup=this.callbackLookup||{},this.callbackLookup[e]={func:o,scope:r}),o.apply(r||this,Array.prototype.slice.call(arguments,1))},translate:function(e){if(e&&Dt.is(e,"string")){var n=this.settings.language||"en",r=this.editorManager.i18n;e=r.data[n+"."+e]||e.replace(/\{\#([^\}]+)\}/g,function(e,t){return r.data[n+"."+t]||"{#"+t+"}"})}return this.editorManager.translate(e)},getLang:function(e,t){return this.editorManager.i18n.data[(this.settings.language||"en")+"."+e]||(t!==undefined?t:"{#"+e+"}")},getParam:function(e,t,n){return il(this,e,t,n)},nodeChanged:function(e){this._nodeChangeDispatcher.nodeChanged(e)},addButton:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),t.stateSelector&&"undefined"==typeof t.active&&(t.active=!1),t.text||t.icon||(t.icon=e),n.buttons=n.buttons,t.tooltip=t.tooltip||t.title,n.buttons[e]=t},addSidebar:function(e,t){return yC(this,e,t)},addMenuItem:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),n.menuItems=n.menuItems,n.menuItems[e]=t},addContextToolbar:function(e,t){var n,r=this;r.contextToolbars=r.contextToolbars||[],"string"==typeof e&&(n=e,e=function(e){return r.dom.is(e,n)}),r.contextToolbars.push({id:Fp.uuid("mcet"),predicate:e,items:t})},addCommand:function(e,t,n){this.editorCommands.addCommand(e,t,n)},addQueryStateHandler:function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},addQueryValueHandler:function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},addShortcut:function(e,t,n,r){this.shortcuts.add(e,t,n,r)},execCommand:function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},queryCommandState:function(e){return this.editorCommands.queryCommandState(e)},queryCommandValue:function(e){return this.editorCommands.queryCommandValue(e)},queryCommandSupported:function(e){return this.editorCommands.queryCommandSupported(e)},show:function(){this.hidden&&(this.hidden=!1,this.inline?this.getBody().contentEditable=!0:(OC.show(this.getContainer()),OC.hide(this.id)),this.load(),this.fire("show"))},hide:function(){var e=this,t=e.getDoc();e.hidden||(MC&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable=!1,e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(OC.hide(e.getContainer()),OC.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},isHidden:function(){return!!this.hidden},setProgressState:function(e,t){this.fire("ProgressState",{state:e,time:t})},load:function(e){var t,n=this.getElement();return this.removed?"":n?((e=e||{}).load=!0,t=this.setContent(n.value!==undefined?n.value:n.innerHTML,e),e.element=n,e.no_events||this.fire("LoadContent",e),e.element=n=null,t):void 0},save:function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return(e=e||{}).save=!0,e.element=o,e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,/TEXTAREA|INPUT/i.test(o.nodeName)?o.value=t:(r.inline||(o.innerHTML=t),(n=OC.getParent(r.id,"form"))&&LC(n.elements,function(e){if(e.name===r.id)return e.value=t,!1})),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},setContent:function(e,t){return TC(this,e,t)},getContent:function(e){return AC(this,e)},insertContent:function(e,t){t&&(e=PC({content:e},t)),this.execCommand("mceInsertContent",!1,e)},isDirty:function(){return!this.isNotDirty},setDirty:function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},setMode:function(e){jm(this,e)},getContainer:function(){return this.container||(this.container=OC.get(this.editorContainer||this.id+"_parent")),this.container},getContentAreaContainer:function(){return this.contentAreaContainer},getElement:function(){return this.targetElm||(this.targetElm=OC.get(this.id)),this.targetElm},getWin:function(){var e;return this.contentWindow||(e=this.iframeElement)&&(this.contentWindow=e.contentWindow),this.contentWindow},getDoc:function(){var e;return this.contentDocument||(e=this.getWin())&&(this.contentDocument=e.document),this.contentDocument},getBody:function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},convertURL:function(e,t,n){var r=this.settings;return r.urlconverter_callback?this.execCallback("urlconverter_callback",e,n,!0,t):!r.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r.relative_urls?this.documentBaseURI.toRelative(e):e=this.documentBaseURI.toAbsolute(e,r.remove_script_host)},addVisual:function(e){var n,r=this,o=r.settings,i=r.dom;e=e||r.getBody(),r.hasVisual===undefined&&(r.hasVisual=o.visual),LC(i.select("table,a",e),function(e){var t;switch(e.nodeName){case"TABLE":return n=o.visual_table_class||"mce-item-table",void((t=i.getAttrib(e,"border"))&&"0"!==t||!r.hasVisual?i.removeClass(e,n):i.addClass(e,n));case"A":return void(i.getAttrib(e,"href")||(t=i.getAttrib(e,"name")||e.id,n=o.visual_anchor_class||"mce-item-anchor",t&&r.hasVisual?i.addClass(e,n):i.removeClass(e,n)))}}),r.fire("VisualAid",{element:e,hasVisual:r.hasVisual})},remove:function(){BC(this)},destroy:function(e){DC(this,e)},uploadImages:function(e){return this.editorUpload.uploadImages(e)},_scanForImages:function(){return this.editorUpload.scanForImages()}},qm);var zC,UC,qC,VC={isEditorUIElement:function(e){return-1!==e.className.toString().indexOf("mce-")}},HC=function(n,e){var t,r,o=In.detect().browser;o.isIE()||o.isEdge()?(r=n).on("focusout",function(){Sm.store(r)}):(t=e,n.on("mouseup touchend",function(e){t.throttle()})),n.on("keyup nodechange",function(e){var t;"nodechange"===(t=e).type&&t.selectionChange||Sm.store(n)})},jC=function(e){var t,n,r,o=yg(function(){Sm.store(e)},0);e.inline&&(t=e,n=o,r=function(){n.throttle()},ci.DOM.bind(document,"mouseup",r),t.on("remove",function(){ci.DOM.unbind(document,"mouseup",r)})),e.on("init",function(){HC(e,o)}),e.on("remove",function(){o.cancel()})},$C=ci.DOM,WC=function(e){return VC.isEditorUIElement(e)},KC=function(t,e){var n=t?t.settings.custom_ui_selector:"";return null!==$C.getParent(e,function(e){return WC(e)||!!n&&t.dom.is(e,n)})},XC=function(r,e){var t=e.editor;jC(t),t.on("focusin",function(){var e=r.focusedEditor;e!==this&&(e&&e.fire("blur",{focusedEditor:this}),r.setActive(this),(r.focusedEditor=this).fire("focus",{blurredEditor:e}),this.focus(!0))}),t.on("focusout",function(){var t=this;ve.setEditorTimeout(t,function(){var e=r.focusedEditor;KC(t,function(){try{return document.activeElement}catch(e){return document.body}}())||e!==t||(t.fire("blur",{focusedEditor:null}),r.focusedEditor=null)})}),zC||(zC=function(e){var t,n=r.activeEditor;t=e.target,n&&t.ownerDocument===document&&(t===document.body||KC(n,t)||r.focusedEditor!==n||(n.fire("blur",{focusedEditor:null}),r.focusedEditor=null))},$C.bind(document,"focusin",zC))},YC=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||($C.unbind(document,"focusin",zC),zC=null)},GC=function(e){e.on("AddEditor",V.curry(XC,e)),e.on("RemoveEditor",V.curry(YC,e))},JC={},QC="en",ZC={setCode:function(e){e&&(QC=e,this.rtl=!!this.data[e]&&"rtl"===this.data[e]._dir)},getCode:function(){return QC},rtl:!1,add:function(e,t){var n=JC[e];for(var r in n||(JC[e]=n={}),t)n[r]=t[r];this.setCode(e)},translate:function(e){var t=JC[QC]||{},n=function(e){return Dt.is(e,"function")?Object.prototype.toString.call(e):r(e)?"":""+e},r=function(e){return""===e||null===e||Dt.is(e,"undefined")},o=function(e){return e=n(e),Dt.hasOwn(t,e)?n(t[e]):e};if(r(e))return"";if(Dt.is(e,"object")&&Dt.hasOwn(e,"raw"))return n(e.raw);if(Dt.is(e,"array")){var i=e.slice(1);e=o(e[0]).replace(/\{([0-9]+)\}/g,function(e,t){return Dt.hasOwn(i,t)?n(i[t]):e})}return o(e).replace(/{context:\w+}$/,"")},data:JC},ex=ci.DOM,tx=Dt.explode,nx=Dt.each,rx=Dt.extend,ox=0,ix=!1,ax=[],ux=[],sx=function(t){nx(qC.get(),function(e){"scroll"===t.type?e.fire("ScrollWindow",t):e.fire("ResizeWindow",t)})},cx=function(e){e!==ix&&(e?Jt(window).on("resize scroll",sx):Jt(window).off("resize scroll",sx),ix=e)},lx=function(t){var e=ux;delete ax[t.id];for(var n=0;n<ax.length;n++)if(ax[n]===t){ax.splice(n,1);break}return ux=H.filter(ux,function(e){return t!==e}),qC.activeEditor===t&&(qC.activeEditor=0<ux.length?ux[0]:null),qC.focusedEditor===t&&(qC.focusedEditor=null),e.length!==ux.length};rx(qC={defaultSettings:{},$:Jt,majorVersion:"4",minorVersion:"7.10",releaseDate:"2018-04-03",editors:ax,i18n:ZC,activeEditor:null,settings:{},setup:function(){var e,t,n,r,o="";if(t=NC.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),n=window.tinymce||window.tinyMCEPreInit)e=n.base||n.baseURL,o=n.suffix;else{for(var i=document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=(r=i[a].src).substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==u.indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/"));break}}!e&&document.currentScript&&(-1!==(r=document.currentScript.src).indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/")))}this.baseURL=new NC(t).toAbsolute(e),this.documentBaseURL=t,this.baseURI=new NC(this.baseURL),this.suffix=o,GC(this)},overrideDefaults:function(e){var t,n;(t=e.base_url)&&(this.baseURL=new NC(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new NC(this.baseURL)),n=e.suffix,e.suffix&&(this.suffix=n);var r=(this.defaultSettings=e).plugin_base_urls;for(var o in r)vi.PluginManager.urls[o]=r[o]},init:function(r){var n,u,s=this;u=Dt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option tbody tfoot thead tr script noscript style textarea video audio iframe object menu"," ");var c=function(e){var t=e.id;return t||(t=(t=e.name)&&!ex.get(t)?e.name:ex.uniqueId(),e.setAttribute("id",t)),t},l=function(e,t){return t.constructor===RegExp?t.test(e.className):ex.hasClass(e,t)},f=function(e){n=e},e=function(){var o,i=0,a=[],n=function(e,t,n){var r=new FC(e,t,s);a.push(r),r.on("init",function(){++i===o.length&&f(a)}),r.targetElm=r.targetElm||n,r.render()};ex.unbind(window,"ready",e),function(e){var t=r[e];t&&t.apply(s,Array.prototype.slice.call(arguments,2))}("onpageload"),o=Jt.unique(function(t){var e,n=[];if(de.ie&&de.ie<11)return Cp.initError("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if(t.types)return nx(t.types,function(e){n=n.concat(ex.select(e.selector))}),n;if(t.selector)return ex.select(t.selector);if(t.target)return[t.target];switch(t.mode){case"exact":0<(e=t.elements||"").length&&nx(tx(e),function(t){var e;(e=ex.get(t))?n.push(e):nx(document.forms,function(e){nx(e.elements,function(e){e.name===t&&(t="mce_editor_"+ox++,ex.setAttrib(e,"id",t),n.push(e))})})});break;case"textareas":case"specific_textareas":nx(ex.select("textarea"),function(e){t.editor_deselector&&l(e,t.editor_deselector)||t.editor_selector&&!l(e,t.editor_selector)||n.push(e)})}return n}(r)),r.types?nx(r.types,function(t){Dt.each(o,function(e){return!ex.is(e,t.selector)||(n(c(e),rx({},r,t),e),!1)})}):(Dt.each(o,function(e){var t;(t=s.get(e.id))&&t.initialized&&!(t.getContainer()||t.getBody()).parentNode&&(lx(t),t.unbindAllNativeEvents(),t.destroy(!0),t.removed=!0,t=null)}),0===(o=Dt.grep(o,function(e){return!s.get(e.id)})).length?f([]):nx(o,function(e){var t;t=e,r.inline&&t.tagName.toLowerCase()in u?Cp.initError("Could not initialize inline editor on invalid inline target element",e):n(c(e),r,e)}))};return s.settings=r,ex.bind(window,"ready",e),new me(function(t){n?t(n):f=function(e){t(e)}})},get:function(t){return 0===arguments.length?ux.slice(0):Jn.isString(t)?H.find(ux,function(e){return e.id===t}).getOr(null):Jn.isNumber(t)&&ux[t]?ux[t]:null},add:function(e){var t=this;return ax[e.id]===e||(null===t.get(e.id)&&("length"!==e.id&&(ax[e.id]=e),ax.push(e),ux.push(e)),cx(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),UC||(UC=function(){t.fire("BeforeUnload")},ex.bind(window,"beforeunload",UC))),e},createEditor:function(e,t){return this.add(new FC(e,t,this))},remove:function(e){var t,n,r=this;if(e)return Jn.isString(e)?(e=e.selector||e,void nx(ex.select(e),function(e){(n=r.get(e.id))&&r.remove(n)})):(n=e,Jn.isNull(r.get(n.id))?null:(lx(n)&&r.fire("RemoveEditor",{editor:n}),0===ux.length&&ex.unbind(window,"beforeunload",UC),n.remove(),cx(0<ux.length),n));for(t=ux.length-1;0<=t;t--)r.remove(ux[t])},execCommand:function(e,t,n){var r=this.get(n);switch(e){case"mceAddEditor":return this.get(n)||new FC(n,this.settings,this).render(),!0;case"mceRemoveEditor":return r&&r.remove(),!0;case"mceToggleEditor":return r?r.isHidden()?r.show():r.hide():this.execCommand("mceAddEditor",0,n),!0}return!!this.activeEditor&&this.activeEditor.execCommand(e,t,n)},triggerSave:function(){nx(ux,function(e){e.save()})},addI18n:function(e,t){ZC.add(e,t)},translate:function(e){return ZC.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e}},Im),qC.setup();var fx,dx=qC;function mx(n){return{walk:function(e,t){return Fh.walk(n,e,t)},split:Ch.split,normalize:function(t){return Jd.normalize(n,t).fold(V.constant(!1),function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0})}}}(fx=mx||(mx={})).compareRanges=jd.isEq,fx.getCaretRangeFromPoint=sy.fromPoint,fx.getSelectedNode=Qi,fx.getNode=Zi;var px,gx,hx=mx,vx=Math.min,yx=Math.max,bx=Math.round,Cx=function(e,t,n){var r,o,i,a,u,s;return r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,s=t.h,"b"===(n=(n||"").split(""))[0]&&(o+=s),"r"===n[1]&&(r+=u),"c"===n[0]&&(o+=bx(s/2)),"c"===n[1]&&(r+=bx(u/2)),"b"===n[3]&&(o-=a),"r"===n[4]&&(r-=i),"c"===n[3]&&(o-=bx(a/2)),"c"===n[4]&&(r-=bx(i/2)),xx(r,o,i,a)},xx=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},wx={inflate:function(e,t,n){return xx(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},relativePosition:Cx,findBestRelativePosition:function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if((o=Cx(e,t,r[i])).x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},intersect:function(e,t){var n,r,o,i;return n=yx(e.x,t.x),r=yx(e.y,t.y),o=vx(e.x+e.w,t.x+t.w),i=vx(e.y+e.h,t.y+t.h),o-n<0||i-r<0?null:xx(n,r,o-n,i-r)},clamp:function(e,t,n){var r,o,i,a,u,s,c,l,f,d;return u=e.x,s=e.y,c=e.x+e.w,l=e.y+e.h,f=t.x+t.w,d=t.y+t.h,r=yx(0,t.x-u),o=yx(0,t.y-s),i=yx(0,c-f),a=yx(0,l-d),u+=r,s+=o,n&&(c+=r,l+=o,u-=i,s-=a),xx(u,s,(c-=i)-u,(l-=a)-s)},create:xx,fromClientRect:function(e){return xx(e.left,e.top,e.width,e.height)}},Nx={},Ex={add:function(e,t){Nx[e.toLowerCase()]=t},has:function(e){return!!Nx[e.toLowerCase()]},get:function(e){var t=e.toLowerCase(),n=Nx.hasOwnProperty(t)?Nx[t]:null;if(null===n)throw new Error("Could not find module for type: "+e);return n},create:function(e,t){var n;if("string"==typeof e?(t=t||{}).type=e:e=(t=e).type,e=e.toLowerCase(),!(n=Nx[e]))throw new Error("Could not find control by type: "+e);return(n=new n(t)).type=e,n}},Sx=Dt.each,kx=Dt.extend,Tx=function(){};Tx.extend=px=function(n){var e,t,r,o=this.prototype,i=function(){var e,t,n;if(!gx&&(this.init&&this.init.apply(this,arguments),t=this.Mixins))for(e=t.length;e--;)(n=t[e]).init&&n.init.apply(this,arguments)},a=function(){return this},u=function(n,r){return function(){var e,t=this._super;return this._super=o[n],e=r.apply(this,arguments),this._super=t,e}};for(t in gx=!0,e=new this,gx=!1,n.Mixins&&(Sx(n.Mixins,function(e){for(var t in e)"init"!==t&&(n[t]=e[t])}),o.Mixins&&(n.Mixins=o.Mixins.concat(n.Mixins))),n.Methods&&Sx(n.Methods.split(","),function(e){n[e]=a}),n.Properties&&Sx(n.Properties.split(","),function(e){var t="_"+e;n[e]=function(e){return e!==undefined?(this[t]=e,this):this[t]}}),n.Statics&&Sx(n.Statics,function(e,t){i[t]=e}),n.Defaults&&o.Defaults&&(n.Defaults=kx({},o.Defaults,n.Defaults)),n)"function"==typeof(r=n[t])&&o[t]?e[t]=u(t,r):e[t]=r;return i.prototype=e,(i.constructor=i).extend=px,i};var Ax=Math.min,Rx=Math.max,_x=Math.round,Bx=function(e,n){var r,o,t,i;if(n=n||'"',null===e)return"null";if("string"==(t=typeof e))return o="\bb\tt\nn\ff\rr\"\"''\\\\",n+e.replace(/([\u0080-\uFFFF\x00-\x1f\"\'\\])/g,function(e,t){return'"'===n&&"'"===e?e:(r=o.indexOf(t))+1?"\\"+o.charAt(r+1):(e=t.charCodeAt().toString(16),"\\u"+"0000".substring(e.length)+e)})+n;if("object"===t){if(e.hasOwnProperty&&"[object Array]"===Object.prototype.toString.call(e)){for(r=0,o="[";r<e.length;r++)o+=(0<r?",":"")+Bx(e[r],n);return o+"]"}for(i in o="{",e)e.hasOwnProperty(i)&&(o+="function"!=typeof e[i]?(1<o.length?","+n:n)+i+n+":"+Bx(e[i],n):"");return o+"}"}return""+e},Dx={serialize:Bx,parse:function(e){try{return JSON.parse(e)}catch(t){}}},Ox={callbacks:{},count:0,send:function(t){var n=this,r=ci.DOM,o=t.count!==undefined?t.count:n.count,i="tinymce_jsonp_"+o;n.callbacks[o]=function(e){r.remove(i),delete n.callbacks[o],t.callback(e)},r.add(r.doc.body,"script",{id:i,src:t.url,type:"text/javascript"}),n.count++}},Px={send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||1e4<n++?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,1e4<n?"TIMED_OUT":"GENERAL",t,e),t=null):setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",Px.fire("beforeInitialize",{settings:e}),t=new Np){if(t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&Dt.each(e.requestheaders,function(e){t.setRequestHeader(e.key,e.value)}),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),(t=Px.fire("beforeSend",{xhr:t,settings:e}).xhr).send(e.data),!e.async)return r();setTimeout(r,10)}}};Dt.extend(Px,Im);var Lx=Dt.extend,Ix=function(e){this.settings=Lx({},e),this.count=0};Ix.sendRPC=function(e){return(new Ix).send(e)},Ix.prototype={send:function(n){var r=n.error,o=n.success;(n=Lx(this.settings,n)).success=function(e,t){void 0===(e=Dx.parse(e))&&(e={error:"JSON Parse error."}),e.error?r.call(n.error_scope||n.scope,e.error,t):o.call(n.success_scope||n.scope,e.result)},n.error=function(e,t){r&&r.call(n.error_scope||n.scope,e,t)},n.data=Dx.serialize({id:n.id||"c"+this.count++,method:n.method,params:n.params}),n.content_type="application/json",Px.send(n)}};var Mx,Fx=window.localStorage,zx=dx,Ux={geom:{Rect:wx},util:{Promise:me,Delay:ve,Tools:Dt,VK:Cg,URI:NC,Class:Tx,EventDispatcher:Om,Observable:Im,I18n:ZC,XHR:Px,JSON:Dx,JSONRequest:Ix,JSONP:Ox,LocalStorage:Fx,Color:function(e){var n={},u=0,s=0,c=0,t=function(e){var t;return"object"==typeof e?"r"in e?(u=e.r,s=e.g,c=e.b):"v"in e&&function(e,t,n){var r,o,i,a;if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,n=parseInt(n,10)/100,t=Rx(0,Ax(t,1)),n=Rx(0,Ax(n,1)),0!==t){switch(r=e/60,i=(o=n*t)*(1-Math.abs(r%2-1)),a=n-o,Math.floor(r)){case 0:u=o,s=i,c=0;break;case 1:u=i,s=o,c=0;break;case 2:u=0,s=o,c=i;break;case 3:u=0,s=i,c=o;break;case 4:u=i,s=0,c=o;break;case 5:u=o,s=0,c=i;break;default:u=s=c=0}u=_x(255*(u+a)),s=_x(255*(s+a)),c=_x(255*(c+a))}else u=s=c=_x(255*n)}(e.h,e.s,e.v):(t=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(u=parseInt(t[1],10),s=parseInt(t[2],10),c=parseInt(t[3],10)):(t=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(u=parseInt(t[1],16),s=parseInt(t[2],16),c=parseInt(t[3],16)):(t=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(u=parseInt(t[1]+t[1],16),s=parseInt(t[2]+t[2],16),c=parseInt(t[3]+t[3],16)),u=u<0?0:255<u?255:u,s=s<0?0:255<s?255:s,c=c<0?0:255<c?255:c,n};return e&&t(e),n.toRgb=function(){return{r:u,g:s,b:c}},n.toHsv=function(){return e=u,t=s,n=c,o=0,(i=Ax(e/=255,Ax(t/=255,n/=255)))===(a=Rx(e,Rx(t,n)))?{h:0,s:0,v:100*(o=i)}:(r=(a-i)/a,{h:_x(60*((e===i?3:n===i?1:5)-(e===i?t-n:n===i?e-t:n-e)/((o=a)-i))),s:_x(100*r),v:_x(100*o)});var e,t,n,r,o,i,a},n.toHex=function(){var e=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+e(u)+e(s)+e(c)},n.parse=t,n}},dom:{EventUtils:ke,Sizzle:ct,DomQuery:Jt,TreeWalker:Zr,DOMUtils:ci,ScriptLoader:pi,RangeUtils:hx,Serializer:ty,ControlSelection:ay,BookmarkManager:ry,Selection:jy,Event:ke.Event},html:{Styles:Qo,Entities:zo,Node:zv,Schema:Go,SaxParser:Dg,DomParser:Gv,Writer:Mc,Serializer:Fc},ui:{Factory:Ex},Env:de,AddOnManager:vi,Formatter:Nv,UndoManager:Kg,EditorCommands:Bm,WindowManager:gp,NotificationManager:pp,EditorObservable:qm,Shortcuts:Ym,Editor:FC,FocusManager:VC,EditorManager:dx,DOM:ci.DOM,ScriptLoader:pi.ScriptLoader,PluginManager:vi.PluginManager,ThemeManager:vi.ThemeManager,trim:Dt.trim,isArray:Dt.isArray,is:Dt.is,toArray:Dt.toArray,makeMap:Dt.makeMap,each:Dt.each,map:Dt.map,grep:Dt.grep,inArray:Dt.inArray,extend:Dt.extend,create:Dt.create,walk:Dt.walk,createNS:Dt.createNS,resolve:Dt.resolve,explode:Dt.explode,_addCacheSuffix:Dt._addCacheSuffix,isOpera:de.opera,isWebKit:de.webkit,isIE:de.ie,isGecko:de.gecko,isMac:de.mac},qx=zx=Dt.extend(zx,Ux);Mx=qx,window.tinymce=Mx,window.tinyMCE=Mx,function(e){if("object"==typeof module)try{module.exports=e}catch(t){}}(qx)}();