// 电子卡册页面JavaScript
const { createApp } = Vue;

// 获取URL参数
var coupon_book_guid = getQueryString("guid");
var bid = getQueryString("bid");
document.title = "电子卡册";

// 工具函数
function getFirstImgSrc() {
  return document.querySelector("body").innerHTML.match(/<img.*?src=(["'])(.*?)(\1).*?>/)?.[2] || null;
}

function bind_flow() {
  layui.use("flow", function () {
    var flow = layui.flow;
    flow.lazyimg();
  });
}

// 页面状态管理
let pageState = {
  isLoaded: false,
  cachedData: null,
  lastLoadTime: 0,
  isFromCache: false,
};

// iOS返回刷新问题检测和处理
function handlePageLifecycle() {
  // 检测页面可见性变化
  document.addEventListener("visibilitychange", function () {
    if (document.visibilityState === "visible") {
      console.log("页面变为可见状态");
      // 可以在这里添加调试alert: alert('页面变为可见');
    } else {
      console.log("页面变为隐藏状态");
      // 可以在这里添加调试alert: alert('页面变为隐藏');
    }
  });

  // 监听页面显示事件 (iOS Safari重要)
  window.addEventListener("pageshow", function (event) {
    console.log("pageshow事件触发", event.persisted);
    if (event.persisted) {
      // 这是最重要的调试信息，保留这个alert
      //alert('iOS返回检测: 从缓存恢复页面');
    }
  });

  // 监听页面隐藏事件
  window.addEventListener("pagehide", function (event) {
    console.log("pagehide事件触发", event.persisted);
    // 可以在这里添加调试alert: alert('页面即将隐藏');
  });

  // 监听beforeunload事件
  window.addEventListener("beforeunload", function (event) {
    console.log("beforeunload事件触发");
    // 可以在这里添加调试alert: alert('页面即将卸载');
  });

  // 监听unload事件
  window.addEventListener("unload", function (event) {
    console.log("unload事件触发");
  });

  // 监听focus事件
  window.addEventListener("focus", function () {
    console.log("window focus事件触发");
    // 可以在这里添加调试alert: alert('窗口获得焦点');
  });

  // 监听blur事件
  window.addEventListener("blur", function () {
    console.log("window blur事件触发");
    // 可以在这里添加调试alert: alert('窗口失去焦点');
  });
}

// 立即调用页面生命周期处理
handlePageLifecycle();

// Vue应用
const app = createApp({
  data() {
    return {
      // 页面状态
      loading: true,
      keyword: "",

      // 数据
      data: {},
      goods_list: [],
      config: {},
      share_info: {},

      // 分类相关
      whichIndex: 0, // 当前选中的分类索引
    };
  },

  computed: {
    // 当前选中的分类
    currentCategory() {
      return this.data.code_list?.[this.whichIndex] || {};
    },
  },

  mounted() {
    this.initPage();
    this.setupPageRestoreHandling();
  },

  methods: {
    // 设置页面恢复处理
    setupPageRestoreHandling() {
      const self = this;

      // 监听pageshow事件，这是iOS Safari最重要的事件
      window.addEventListener("pageshow", function (event) {
        console.log("pageshow事件:", event.persisted);

        if (event.persisted) {
          // 页面从缓存恢复，尝试从localStorage恢复数据
          console.log("检测到从缓存恢复，尝试恢复页面状态");
          self.restorePageState();
        }
      });

      // 在页面隐藏前保存状态
      window.addEventListener("pagehide", function (event) {
        console.log("pagehide事件，保存页面状态");
        self.savePageState();
      });

      // 页面可见性变化时的处理
      document.addEventListener("visibilitychange", function () {
        if (document.visibilityState === "visible") {
          // 页面重新可见时，检查是否需要恢复数据
          const now = Date.now();
          const lastSave = localStorage.getItem("coupon_book_last_save");

          if (lastSave && now - parseInt(lastSave) < 300000) {
            // 5分钟内
            console.log("页面重新可见，尝试恢复数据");
            self.restorePageState();
          }
        }
      });
    },

    // 保存页面状态到localStorage
    savePageState() {
      try {
        const state = {
          data: this.data,
          goods_list: this.goods_list,
          config: this.config,
          share_info: this.share_info,
          whichIndex: this.whichIndex,
          keyword: this.keyword,
          timestamp: Date.now(),
        };

        localStorage.setItem("coupon_book_state", JSON.stringify(state));
        localStorage.setItem("coupon_book_last_save", Date.now().toString());
        console.log("页面状态已保存到localStorage");
      } catch (e) {
        console.error("保存页面状态失败:", e);
      }
    },

    // 从localStorage恢复页面状态
    restorePageState() {
      try {
        const savedState = localStorage.getItem("coupon_book_state");
        if (savedState) {
          const state = JSON.parse(savedState);
          const now = Date.now();

          // 检查数据是否在5分钟内保存的
          if (state.timestamp && now - state.timestamp < 300000) {
            this.data = state.data || {};
            this.goods_list = state.goods_list || [];
            this.config = state.config || {};
            this.share_info = state.share_info || {};
            this.whichIndex = state.whichIndex || 0;
            this.keyword = state.keyword || "";
            this.loading = false;

            // 恢复页面标题
            if (state.data && state.data.info && state.data.info.name) {
              document.title = state.data.info.name;
            }

            this.$nextTick(() => {
              this.initSwiper();
              this.initWechatShare();
              bind_flow();
            });

            console.log("页面状态已从localStorage恢复");
            // 可以在这里添加成功提示: alert('页面状态已成功恢复，避免了重新加载');
            return true;
          }
        }
      } catch (e) {
        console.error("恢复页面状态失败:", e);
      }
      return false;
    },

    // 初始化页面
    initPage() {
      // 首先尝试从缓存恢复
      if (!this.restorePageState()) {
        // 如果恢复失败，则正常加载数据
        this.loadData();
      }
    },

    // 搜索功能
    search() {
      this.loadData();
    },

    // 加载数据
    loadData() {
      this.loading = true;

      post_layui_member_api_v1(
        "/coupon_book/index",
        {
          guid: coupon_book_guid,
          share_guid: getQueryString("share_guid"),
          keyword: this.keyword,
        },
        (result) => {
          this.data = result.data;
          this.config = result.data.config;
          this.share_info = result.data.share_info;
          document.title = result.data.info.name;
          this.loading = false;

          this.$nextTick(() => {
            this.initSwiper();
            this.initWechatShare();
            bind_flow();

            // 默认加载第一个分类的商品
            if (result.data.code_list?.length) {
              this.getGoodsList(result.data.code_list[0].guid);
            }

            // 数据加载完成后保存状态
            this.savePageState();
          });
        },
        () => {
          this.loading = false;
        }
      );
    },

    // 初始化轮播图
    initSwiper() {
      if (!this.data.banner_list || this.data.banner_list.length === 0) {
        return;
      }

      const swiper = new Swiper(".swiper", {
        direction: "horizontal",
        loop: true,
        autoHeight: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        speed: 1000,
        pagination: {
          el: ".swiper-pagination",
        },
      });

      // 监听轮播图变化，调整布局
      swiper.on("update", () => {
        this.adjustLayout();
      });

      swiper.on("slideChange", () => {
        this.adjustLayout();
      });
    },

    // 调整布局高度
    adjustLayout() {
      this.$nextTick(() => {
        const swiperHeight = document.querySelector(".swiper")?.clientHeight || 0;
        const clientHeight = document.body.clientHeight;
        const bottomHeight = clientHeight - swiperHeight - 60; // 60px为其他元素高度

        const bottomElement = document.querySelector(".bottom");
        if (bottomElement) {
          bottomElement.style.height = `${bottomHeight}px`;
        }
      });
    },

    // 分类点击事件
    selectCategory(index) {
      if (index === this.whichIndex) return; // 如果点击的是当前分类，不做处理

      this.whichIndex = index;
      this.getGoodsList(this.data.code_list[index].guid);
    },

    // 获取商品列表
    getGoodsList(coupon_guid) {
      post_layui_member_api_v1(
        "/coupon_book/goods_list",
        { coupon_guid },
        (result) => {
          this.goods_list = result.data;
          // 商品列表更新后保存状态
          this.savePageState();
        },
        () => {
          this.goods_list = [];
        }
      );
    },

    // 商品详情跳转
    gotoDetail(goods) {
      redirect(`/member/goods/detail.html?bid=${bid}&from=coupon_book&hide_button=1&show_back=1&guid=${goods.guid}`);
    },

    // 生成分享链接
    gotoShare() {
      const layer = layui.layer;

      const content = `
        <form class="layui-form" action="" style="padding: 10px">
          <div class="layui-form-item">
            <label class="layui-form-label">公司名</label>
            <div class="layui-input-block">
              <input type="text" name="company" required lay-verify="required" placeholder="请输入公司名" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-block">
              <input type="text" name="true_name" required lay-verify="required" placeholder="请输入姓名" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
              <input type="tel" name="mobile" required lay-verify="required|phone" placeholder="请输入手机号" autocomplete="off" class="layui-input">
            </div>
          </div>
          <span>保存完成信息以后，请点击右上角…分享!</span>
          <div class="layui-form-item">
            <div class="layui-input-block">
              <button class="layui-btn" lay-submit lay-filter="formDemo">提交信息</button>
            </div>
          </div>
        </form>
      `;

      layer.open({
        type: 1,
        area: ["95%", "400px"],
        title: "生成我的链接",
        content: content,
        success: (_, index) => {
          layui.use("form", function () {
            const form = layui.form;

            form.on("submit(formDemo)", function (data) {
              data.field.coupon_book_guid = coupon_book_guid;

              post_layui_member_api_v1("/coupon_book/add_share_note", data.field, (result) => {
                const time = 1000;
                layer.msg(
                  result.msg,
                  {
                    icon: 1,
                    time: time,
                  },
                  () => {
                    layer.close(index);
                    setTimeout(() => {
                      window.location.href = result.data.url;
                    }, time);
                  }
                );
              });

              return false;
            });
          });
        },
      });
    },

    // 初始化微信分享
    initWechatShare() {
      if (!is_weixin()) {
        return;
      }

      wx.ready(() => {
        // 分享给朋友
        wx.updateAppMessageShareData({
          title: this.data.info.name,
          desc: "点击查看详情",
          link: window.location.href,
          imgUrl: getFirstImgSrc(),
          success: () => {
            wx.showMenuItems({
              menuList: share_menu_list,
            });
          },
        });

        // 分享到朋友圈
        wx.updateTimelineShareData({
          title: this.data.info.name,
          link: window.location.href,
          imgUrl: getFirstImgSrc(),
          success: () => {
            wx.showMenuItems({
              menuList: share_menu_list,
            });
          },
        });
      });
    },

    // 获取分类样式
    getCategoryStyle(index) {
      const isSelected = index === this.whichIndex;
      return {
        backgroundColor: isSelected ? this.config.coupon_book_item_bg_color_selected : this.config.coupon_book_item_bg_color,
      };
    },

    // 获取商品图片样式
    getGoodsImageStyle() {
      return {
        border: `1px solid ${this.config.coupon_book_item_bg_color_selected}`,
        outline: `1px solid ${this.config.coupon_book_item_bg_color_selected}`,
        outlineOffset: "2px",
        borderRadius: "2px",
      };
    },
  },
});

// 挂载应用
app.mount("#app");
