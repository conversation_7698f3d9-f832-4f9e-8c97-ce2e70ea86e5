<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use app\middleware\CheckTxtFile;
use think\facade\Route;
use think\swoole\response\File;


Route::any('gateway/wechat_event/[:appid]', 'gateway/wechat_event/index');//微信推送事件
Route::any('gateway/his/donghua', 'gateway/notify/donghua_old');//兼容之前给东华的老的推送链接,后续推动其统一

Route::get('u/:file_name', ':file_name.txt')->ext('txt')->middleware(CheckTxtFile::class);
Route::get(':file_name', ':file_name.txt')->ext('txt')->middleware(CheckTxtFile::class);

Route::any('u/:code', '\app\controller\index\Plugins@redirect_to_long_url')->ext();
//Route::any('assigned', '\app\controller\index\Plugins@upgrade_push');
if (php_sapi_name() == 'cli') {
//    Route::get('static/:path', function (string $path) {
//        $filename = public_path('static') . $path;
//        return new File($filename);
//    })->pattern(['path' => '.*\.\w+$']);

//    Route::get(':prefix/:path', function (string $prefix, string $path) {
//        $filename = public_path($prefix) . '/' . $path;
//        return new File($filename);
//    })->pattern(['prefix' => 'static|statics|file', 'path' => '.*\.\w+$']);
    //下面已经包含上面的逻辑了 无需重复定义
    $static_path_list = ['static', 'statics', 'file'];
    foreach ($static_path_list as $static_path) {
        Route::get($static_path . '/:path', function (string $path) use ($static_path) {
            $filename = public_path($static_path) . $path;
            return new File($filename);
        })->pattern(['path' => '.*\.\w+$']);
    }
}
//定义类似 admin_api/v1/index/index 这种路径
Route::auto('[:__module__]/v:__version__/[:__controller__]/[:__action__]', ':__module__/v:__version__/:__controller__/:__action__', true)->pattern(['__version__' => '\d+']);
//定义类似 admin/index/index 这种路径
Route::auto('[:__module__]/[:__controller__]/[:__action__]', ':__module__/:__controller__/:__action__', true);
//// 动态注册应用中间件
//$parts                 = explode('/', rtrim(Request::baseUrl(), '/'));
//$first_level_directory = $parts[1] ?? Config::get('app.default_app');//获取应用名称
//if (!empty(Config::get('middleware.alias.' . $first_level_directory))) {
//    //仅当有定义别名为应用名的中间件组才动态注册
//    Route::middleware($first_level_directory);
//}