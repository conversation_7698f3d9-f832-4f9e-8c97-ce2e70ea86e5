<?php

namespace app\controller\member_api\v1;


use app\common\tools\Visitor;
use app\model\CustomerMessage;
use app\model\CustomerQueue;
use app\model\CustomerSession;

class Kefu extends BasicMemberApi
{
    /**
     * 获取常见问题列表
     * @return \think\response\Json
     */
    public function question_list()
    {
        result([]);
    }

    /**
     * 机器人问答接口
     */
    public function robot_service()
    {
        result([]);
    }

    /**
     * 消息列表接口
     */
    public function message_list()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $session_guid = $params['session_guid'] ?? '';
        if (!$session_guid) {
            error('缺少会话ID');
        }

        // 1. 先查询基本消息数据
        $field = [
            'guid',
            'bid',
            'session_guid',
            'from_type',
            'from_guid',
            'to_guid',
            'msg_type',
            'content',
            'is_read',
            'send_time',
            'extra'
        ];

        $map = [
            ['bid', '=', $bid],
            ['session_guid', '=', $session_guid],
            ['delete_time', 'null', null],
            ['is_recall', '=', 0]
        ];

        $db          = new CustomerMessage();
        $this->model = $db->field($field)->where($map)->order(['send_time' => 'ASC']);
        $list        = $this->_list();

        if (!empty($list['data'])) {
            // 2. 收集需要查询的用户GUID和会员GUID
            $user_guids   = [];
            $member_guids = [];

            foreach ($list['data'] as $msg) {
                if ($msg['from_type'] == 2) {
                    $user_guids[] = $msg['from_guid'];
                } else {
                    $member_guids[] = $msg['from_guid'];
                }
            }

            // 3. 去重并批量查询用户信息
            $user_info_map = [];
            if (!empty($user_guids)) {
                $user_guids = array_unique($user_guids);
                $users      = \app\model\User::where([
                    ['bid', '=', $bid],
                    ['guid', 'in', $user_guids]
                ])->field(['guid', 'name', 'head'])->select()->toArray();

                foreach ($users as $user) {
                    $user_info_map[$user['guid']] = [
                        'name'   => $user['name'] ?: \app\model\User::DEFAULT_NAME,
                        'avatar' => $user['head'] ?: \app\model\User::DEFAULT_AVATAR
                    ];
                }
            }

            // 4. 去重并批量查询会员信息
            $member_info_map = [];
            if (!empty($member_guids)) {
                $member_guids = array_unique($member_guids);
                $members      = \app\model\Member::where([
                    ['bid', '=', $bid],
                    ['guid', 'in', $member_guids]
                ])->field(['guid', 'name', 'head_img'])->select()->toArray();

                foreach ($members as $member) {
                    $member_info_map[$member['guid']] = [
                        'name'   => $member['name'] ?: \app\model\Member::DEFAULT_NAME,
                        'avatar' => $member['head_img'] ?: \app\model\Member::DEFAULT_AVATAR
                    ];
                }
            }

            // 5. 为每条消息补充头像昵称信息
            foreach ($list['data'] as &$msg) {
                if ($msg['from_type'] == 2) {
                    // 客服消息
                    $user_info          = $user_info_map[$msg['from_guid']] ?? [
                        'name'   => \app\model\User::DEFAULT_NAME,
                        'avatar' => \app\model\User::DEFAULT_AVATAR
                    ];
                    $msg['from_name']   = $user_info['name'];
                    $msg['from_avatar'] = $user_info['avatar'];
                } else {
                    // 会员消息
                    $member_info        = $member_info_map[$msg['from_guid']] ?? [
                        'name'   => \app\model\Member::DEFAULT_NAME,
                        'avatar' => \app\model\Member::DEFAULT_AVATAR
                    ];
                    $msg['from_name']   = $member_info['name'];
                    $msg['from_avatar'] = $member_info['avatar'];
                }
            }
        }

        result($list);
    }

    /**
     * 会话列表接口
     * @return \think\response\Json
     */
    public function session_list()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $user_guid   = $this->get_user_guid();
        $member_guid = $this->get_member_guid();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null],
        ];
        if ($user_guid) {
            $map[] = ['user_guid', '=', $user_guid];
        }
        if ($member_guid) {
            $map[] = ['member_guid', '=', $member_guid];
        }
        $field       = [
            'guid',
            'bid',
            'member_guid',
            'user_guid',
            'status',
            'start_time',
            'end_time',
            'last_msg_time',
            'close_type',
            'remark'
        ];
        $db          = new CustomerSession();
        $this->model = $db->field($field)->where($map)->order(['last_msg_time' => 'desc']);
        $list        = $this->_list();
        result($list);
    }

    /**
     * 访客请求会话/排队分配客服
     * @return \think\response\Json
     */
    public function request_session()
    {
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        // 1. 检查未结束会话
        $session = CustomerSession::where([
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['status', '=', 0], // 进行中
        ])->find();
        if ($session) {
            result($session);
        }
        // 2. 检查是否已在队列
        $queue = CustomerQueue::where([
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['status', '=', 0], // 排队中
        ])->find();
        if (!$queue) {
            CustomerQueue::create([
                'guid'        => create_guid(),
                'member_guid' => $member_guid,
                'bid'         => $bid,
                'queue_time'  => date('Y-m-d H:i:s.u'),
                'status'      => 0,
            ]);
        }
        // 3. 查询在线客服列表
        $onlineKefuList = \app\model\User::where([
            ['bid', '=', $bid],
            ['service_status', '=', 1],
        ])->select();

        if (empty($onlineKefuList) || count($onlineKefuList) == 0) {
            // 没有客服在线
            result(['code' => 201, 'msg' => '暂无客服在线，请稍后再来']);
        }

        // 4. 查找有空闲容量的客服
        $availableKefu = null;
        foreach ($onlineKefuList as $kefu) {
            // 查询该客服当前接待的会话数
            $currentSessionCount = CustomerSession::where([
                ['user_guid', '=', $kefu['guid']],
                ['status', '=', 1] // 进行中的会话
            ])->count();

            // 检查是否有空闲容量
            $maxServiceNum = $kefu['max_service_num'] ?? 10; // 默认10个
            if ($currentSessionCount < $maxServiceNum) {
                $availableKefu = $kefu;
                break;
            }
        }

        if ($availableKefu) {
            // 5. 分配客服，生成会话
            $session = CustomerSession::create([
                'guid'        => create_guid(),
                'bid'         => $bid,
                'member_guid' => $member_guid,
                'user_guid'   => $availableKefu['guid'],
                'status'      => 1, // 修复：会员端分配客服时直接设为进行中状态
                'start_time'  => date('Y-m-d H:i:s.u'),
            ]);
            // 6. 更新队列状态
            CustomerQueue::where([
                ['bid', '=', $bid],
                ['member_guid', '=', $member_guid],
                ['status', '=', 0],
            ])->update(['status' => 1]);
            result($session);
        } else {
            // 有客服在线但都忙，继续排队
            result(['code' => 202, 'msg' => '客服全忙，排队中请等待', 'queue' => true]);
        }
    }

    /**
     * 获取客服前端初始化配置
     */
    public function config()
    {
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $db_member   = new \app\model\Member();
        $member_info = $db_member->get_member_info(['bid' => $bid, 'guid' => $member_guid]);
        if (!$member_info) {
            error('会员信息不存在');
        }
        $config = get_config_by_bid($bid);

        $data = [
            'enabled'      => (bool)$config['online_service'],
            'style'        => ['box_color' => '#13a5e7'],
            'robot_open'   => 1,
            'robot_title'  => '智能客服',
            'customerId'   => $member_info['guid'],
            'customerName' => ($member_info['name'] ?? '') ?: \app\model\Member::DEFAULT_NAME,
            'avatar'       => ($member_info['head_img'] ?? '') ?: \app\model\Member::DEFAULT_AVATAR,
            'seller'       => $bid,
            'tk'           => '',
            't'            => '',
            'type'         => 2,
            'os'           => Visitor::is_mobile_browser() ? 'm' : 'p',
        ];
        result($data);
    }
}
