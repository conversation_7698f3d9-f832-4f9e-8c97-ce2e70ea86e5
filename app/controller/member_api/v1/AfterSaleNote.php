<?php

namespace app\controller\member_api\v1;

use app\common\service\WeixinService;
use app\model\AfterSaleNote as AfterSaleNoteModel;
use Exception;

class AfterSaleNote extends BasicMemberApi
{

    /**
     * 获取订单的售后记录列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $bid        = $this->get_bid();
        $params     = $this->params;
        $order_guid = $params['order_guid'];

        $db_after_sale_note = new AfterSaleNoteModel();
        $map                = [
            ['bid', '=', $bid],
            ['order_guid', '=', $order_guid],
        ];

        $list = $db_after_sale_note->where($map)
            ->order('create_time', 'desc')
            ->select();

        // 格式化创建者类型
        foreach ($list as &$item) {
            $item['create_type_text'] = $item['create_type'] == 1 ? '客户' : '商家';
        }
        // 获取订单售后状态
        $db_order = new \app\model\GoodsOrder();
        $map      = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];

        $order             = $db_order->field(['after_sale_status'])->where($map)->find();
        $after_sale_status = $order['after_sale_status'];

        $subscribe_url = WeixinService::get_subscribe_url($bid, $this->get_appid());

        result([
            'list'              => $list,
            'subscribe_url'     => $subscribe_url,
            'after_sale_status' => $after_sale_status
        ]);
    }

    /**
     * 提交售后记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $params             = $this->params;
        $data               = [
            'order_guid'  => $params['order_guid'],
            'images_list' => $params['images_list'] ?? [],
            'content'     => "类型：{$params['type']}，原因：{$params['reason']}",
            'step'        => 1, // 创建工单
        ];
        $db_after_sale_note = new AfterSaleNoteModel();
        $db_after_sale_note->add($data);
        success('创建成功');
    }

    /**
     * 回复售后记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reply()
    {
        $params             = $this->params;
        $data               = [
            'order_guid'  => $params['order_guid'],
            'content'     => $params['content'],
            'images_list' => $params['images_list'] ?? [],
            'step'        => 2, // 回复工单
        ];
        $db_after_sale_note = new AfterSaleNoteModel();
        $db_after_sale_note->add($data);
        success('回复成功');
    }
}
