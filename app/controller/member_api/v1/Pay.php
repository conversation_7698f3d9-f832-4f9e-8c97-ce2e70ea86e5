<?php

namespace app\controller\member_api\v1;

use app\model\CouponActiveOrder;
use app\model\CouponSellOrder;
use app\model\GoodsOrder;
use app\model\PayOrder;
use app\common\tools\Visitor;
use Exception;

class Pay extends BasicMemberApi
{
    /**
     *付款码支付
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function micropay()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $attach       = [];
        $out_trade_no = $params['out_trade_no'];
        $auth_code    = $params['auth_code'];
        $driver       = '';
        if (preg_match("/^(10|11|12|13|14|15)\d{16}$/", $auth_code)) {
            $driver = 'wechat'; //10~15开头18位 微信付款码
        } elseif (preg_match("/^28\d{15,16}$/", $auth_code)) {
            $driver = 'alipay'; //17位是分期码 28开头 18位支付宝付款码
        } else {
            error('付款码格式有误,请核实是否微信或支付宝付款码!');
        }
        $this->check_out_trade_no($out_trade_no);
        $device_id = isset($params['device_id']) ? $params['device_id'] : '';
        $way       = $params['way'] ?? 1; //默认收款业务
        switch ($way) {
            case 1:
                $total_fee = (int)$params['total_fee'];
                break;
            case 3:
                $wechat_group_guid = $params['wechat_group_guid'];
                $db_wechat_group   = new \app\model\WechatGroup();
                $map               = [
                    ['bid', '=', $bid],
                    ['guid', '=', $wechat_group_guid]
                ];
                $wechat_group      = $db_wechat_group->where($map)->findOrFail();
                $total_fee         = tools()::nc_price_yuan2fen($wechat_group['price']);
                break;
            default:
                throw new Exception('不支持的业务类型');
        }
        $order_guid = create_guid();
        $ip         = tools()::get_client_ip();
        $options    = [
            'bid'              => $bid,
            'guid'             => $order_guid,
            'user_guid'        => '',
            'body'             => '付款码支付',
            'auth_code'        => $auth_code,
            'device_id'        => $device_id,
            'spbill_create_ip' => $ip,
            'total_fee'        => $total_fee,
            'attach'           => $attach,
            'way'              => $way,
            'out_trade_no'     => $out_trade_no,
        ];
        $result     = pay($bid)->driver($driver)->scene('pos')->apply($options);
        wr_log($result);
        if ($this->isSuccess($result)) {
            $db = new PayOrder();
            //需要考虑支付中 ORDER_PAYING :支付中; SUCCESS:支付成功 ;PAYERROR :支付失败;REVOKED:已撤销 ;REFUND :转入退款
            $map         = [
                ['bid', '=', $bid],
                ['guid', '=', $order_guid],
                ['bill_number', '=', $out_trade_no],
            ];
            $update_data = [
                'status'         => 1,
                'buyer_id'       => isset($result['openid']) ? $result['openid'] : '', //付款码支付 更新 openid
                'third_trade_no' => $result['transaction_id'],
                'trade_time'     => tools()::format_time($result['time_end']),
                'notify_data'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
            $db::update($update_data, $map);
            $return = [
                'trade_state'     => 'SUCCESS',
                'trade_state_des' => '支付成功',
                'total_fee'       => $result['total_fee'],
                'transaction_id'  => $result['transaction_id'],
                'out_trade_no'    => $result['out_trade_no'],
                'time_end'        => tools()::format_time($result['time_end']),
            ];
            result($return, '收款成功:' . tools()::nc_price_fen2yuan($total_fee) . '元');
        } else {
            $return = [
                'out_trade_no'     => $out_trade_no,
                'trade_state'      => $result['trade_state'],
                'trade_state_desc' => $result['err_code_des'],
            ];
        }
        result($return);
    }

    protected function check_out_trade_no($out_trade_no)
    {
        if (date('Y') !== substr($out_trade_no, 0, 4)) {
            error('out_trade_no格式有误,请传入以' . date('Y') . '开头20位数字');
        }
        $time  = substr($out_trade_no, 0, 14);
        $times = tools()::format_time($time);
        $abs   = abs(time() - strtotime($times));
        if ($abs > 600) {
            error('out_trade_no 已过期:与服务器时差超过10分钟!');
        }
    }

    /**
     * 判断结果是否成功
     * @param $result
     * @return bool
     */
    protected function isSuccess($result)
    {
        if (!is_array($result)) {
            return false;
        }
        return isset($result['return_code']) && ($result['return_code'] === 'SUCCESS') && isset($result['result_code']) && ($result['result_code'] === 'SUCCESS');
    }

    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if ($params['bid'] != $bid) {
            error('商家标识校验不通过');
        }
        $type         = $params['type'];
        $order_guid   = $params['order_guid'];
        $driver       = $params['driver'] ?? '';
        $scene        = $params['scene'] ?? '';
        $out_trade_no = '';
        $total_money  = 0;
        $options      = [];
        if ($type == 'code') {
            $type = 'goods';
        }
        switch ($type) {
            case 'goods':
                $map            = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid],
                ];
                $db_goods_order = new GoodsOrder();
                $order_info     = $db_goods_order->where($map)->findOrFail();
                $total_money    = $order_info['paid_wechat'];
                $out_trade_no   = $order_info['third_order_number'];
                $pay_type       = $order_info['pay_type'];
                //创建完成订单之后,需要pay下单
                switch ($pay_type) {
                    case 1:
                        //微信公众号支付,
                        $int_total_fee = tools()::nc_price_yuan2fen($total_money);
                        $options       = [
                            'bid'          => $bid,
                            'body'         => '[卡券兑换]',
                            'way'          => 4,
                            'out_trade_no' => $out_trade_no,
                            'total_fee'    => $int_total_fee,
                            'openid'       => $this->get_openid(),
                            'appid'        => $this->get_appid(),
                            'job_attach'   => [
                                'class' => 'GoodsOrder@after_pay_success',
                                'data'  => [
                                    'bid'         => $bid,
                                    'order_guid'  => $order_guid,
                                    'bill_number' => $out_trade_no,
                                ]]
                        ];

                        $map         = [
                            ['bid', '=', $bid],
                            ['guid', '=', $order_guid]
                        ];
                        $update_data = ['pay_type' => 1];
                        $db_goods_order::update($update_data, $map);
                        break;
                    default:
                        throw new Exception('暂时不支持的支付方式');
                }
                break;
            case 'recharge_business_sms':
                $recharge_info = cache($order_guid);
                if (empty($recharge_info)) {
                    error('请退出后重试');
                }
                $out_trade_no = $recharge_info['out_trade_no']; //商户号
                $recharge_bid = $recharge_info['recharge_bid']; //充值商家标识
                $total_fee    = $recharge_info['total_fee']; //金额
                $total_money  = tools()::nc_price_fen2yuan($total_fee);
                $amount       = $recharge_info['amount']; //充值短信数量
                $options      = [
                    'bid'          => $bid,
                    'body'         => '[短信充值]',
                    'way'          => 6,
                    'out_trade_no' => $out_trade_no,
                    'total_fee'    => $total_fee,
                    'openid'       => $this->get_openid(),
                    'appid'        => $this->get_appid(),
                    'job_attach'   => [
                        'class' => 'Sms@recharge_business_sms',
                        'data'  => [
                            'bid'        => $recharge_bid,
                            'amount'     => $amount,
                            'socket_uid' => $out_trade_no,
                        ]
                    ]
                ];
                break;
            case 'recharge_business_money':
                $recharge_info = cache($order_guid);
                if (empty($recharge_info)) {
                    error('请退出后重试');
                }
                $out_trade_no = $recharge_info['out_trade_no']; //商户号
                $recharge_bid = $recharge_info['recharge_bid']; //充值商家标识
                $total_fee    = $recharge_info['total_fee']; //金额
                $total_money  = tools()::nc_price_fen2yuan($total_fee);
                $money        = $recharge_info['money']; //充值金额
                $note_guid    = $recharge_info['note_guid']; //记录唯一id,将作为user_money_note表的guid
                $options      = [
                    'bid'          => $bid,
                    'body'         => '[商家余额充值]',
                    'way'          => 8,
                    'out_trade_no' => $out_trade_no,
                    'total_fee'    => $total_fee,
                    'openid'       => $this->get_openid(),
                    'appid'        => $this->get_appid(),
                    'job_attach'   => [
                        'class' => 'Business@recharge_business_money',
                        'data'  => [
                            'bid'        => $recharge_bid,
                            'amount'     => $money,
                            'socket_uid' => $out_trade_no,
                            'note_guid'  => $note_guid,
                        ]
                    ]
                ];
                break;
            case 'recharge_user_money':
                $recharge_info = cache($order_guid);
                if (empty($recharge_info)) {
                    error('请退出后重试');
                }
                $out_trade_no       = $recharge_info['out_trade_no']; //商户号
                $recharge_bid       = $recharge_info['recharge_bid']; //充值商家标识
                $recharge_user_guid = $recharge_info['recharge_user_guid']; //充值商家标识
                $total_fee          = $recharge_info['total_fee']; //金额
                $total_money        = tools()::nc_price_fen2yuan($total_fee);
                $money              = $recharge_info['money']; //充值金额
                $note_guid          = $recharge_info['note_guid']; //记录唯一id,将作为user_money_note表的guid
                $options            = [
                    'bid'          => $bid,
                    'body'         => '[商家余额充值]',
                    'way'          => 8,
                    'out_trade_no' => $out_trade_no,
                    'total_fee'    => $total_fee,
                    'openid'       => $this->get_openid(),
                    'appid'        => $this->get_appid(),
                    'job_attach'   => [
                        'class' => 'Business@recharge_user_money',
                        'data'  => [
                            'bid'        => $recharge_bid,
                            'user_guid'  => $recharge_user_guid,
                            'amount'     => $money,
                            'socket_uid' => $out_trade_no,
                            'note_guid'  => $note_guid,
                        ]
                    ]
                ];
                break;
            case 'code_buy':
                $map                  = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid],
                ];
                $db_coupon_sell_order = new CouponSellOrder();
                $order_info           = $db_coupon_sell_order->where($map)->findOrFail();
                $total_money          = $order_info['total_money'];
                $out_trade_no         = $order_info['third_order_number'];
                $pay_type             = $order_info['pay_type'];
                //创建完成订单之后,需要pay下单
                switch ($pay_type) {
                    case 1:
                        //微信公众号支付,
                        $int_total_fee = tools()::nc_price_yuan2fen($total_money);
                        $options       = [
                            'bid'          => $bid,
                            'body'         => '[卡券商城]',
                            'way'          => 5, //卡券购买
                            'out_trade_no' => $out_trade_no,
                            'total_fee'    => $int_total_fee,
                            'openid'       => $this->get_openid(),
                            'appid'        => $this->get_appid(),
                            'job_attach'   => [
                                'class' => 'Code@code_buy_pay_success_callback',
                                'data'  => [
                                    'bid'         => $bid,
                                    'order_guid'  => $order_guid,
                                    'bill_number' => $out_trade_no,
                                ]]
                        ];
                        $map           = [
                            ['bid', '=', $bid],
                            ['guid', '=', $order_guid]
                        ];
                        $update_data   = ['pay_type' => 1];
                        $db_coupon_sell_order::update($update_data, $map);
                        break;
                    default:
                        throw new Exception('暂时不支持的支付方式');
                }
                break;
            case 'code_active':
                $map                    = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid],
                ];
                $db_coupon_active_order = new CouponActiveOrder();
                $order_info             = $db_coupon_active_order->where($map)->findOrFail();
                $total_money            = $order_info['total_money'];
                $out_trade_no           = $order_info['third_order_number'];
                $pay_type               = $order_info['pay_type'];
                $coupon_code            = $order_info['coupon_code'];
                //创建完成订单之后,需要pay下单
                switch ($pay_type) {
                    case 1:
                        //微信公众号支付,
                        $int_total_fee = tools()::nc_price_yuan2fen($total_money);
                        $options       = [
                            'bid'          => $bid,
                            'body'         => '[卡券激活]',
                            'way'          => 7, //卡券激活
                            'out_trade_no' => $out_trade_no,
                            'total_fee'    => $int_total_fee,
                            'openid'       => $this->get_openid(),
                            'appid'        => $this->get_appid(),
                            'job_attach'   => [
                                'class' => 'Code@code_active_pay_success_callback',
                                'data'  => [
                                    'bid'         => $bid,
                                    'order_guid'  => $order_guid,
                                    'bill_number' => $out_trade_no,
                                    'coupon_code' => $coupon_code,
                                ]]
                        ];
                        $map           = [
                            ['bid', '=', $bid],
                            ['guid', '=', $order_guid]
                        ];
                        $update_data   = ['pay_type' => 1];
                        $db_coupon_active_order::update($update_data, $map);
                        break;
                    default:
                        throw new Exception('暂时不支持的支付方式');
                }
                break;
            default:
                throw new Exception('暂不支持的业务类型' . $type);
        }

        if (empty($driver) || empty($scene)) {
            if (Visitor::is_wechat_browser()) {
                $driver = 'wechat';
                $scene  = 'mp';
            } elseif (Visitor::is_alipay_browser()) {
                $driver = 'alipay';
                $scene  = 'wap';
            } else {
                error('请在微信或者支付宝中打开');
            }
        }

        $options = pay($bid)->driver($driver)->scene($scene)->apply($options);
        //下单成功后更新业务
        switch ($type) {
            case 'code':
                $db_goods_order = new GoodsOrder();
                $map            = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid]
                ];
                $update_data    = ['pay_type' => 1];
                $db_goods_order::update($update_data, $map);
                break;
            case 'recharge_business_sms':
                //                wr_log('recharge_business_sms 下单成功', 1);
                break;
        }
        $data = [
            'pay_options'           => $options,
            'third_pay_bill_number' => $out_trade_no,
            'total_money'           => $total_money,
            'server'                => Visitor::get_user_agent(),
            'scene'                 => $scene,
        ];
        result($data);
    }

    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function create_order()
    {
        $params       = $this->params;
        $out_trade_no = tools()::get_bill_number();
        $attach       = null;
        $job_attach   = null;
        $bid          = $this->get_bid();
        $way          = $params['way'] ?? 1; //默认收款业务
        $total_fee    = tools()::nc_price_yuan2fen($params['total_fee'] ?? 0);
        switch ($way) {
            case 1:
                $total_fee  = tools()::nc_price_yuan2fen($params['total_fee']);
                $job_attach = [
                    'class' => 'Weixin@recharge',
                    'data'  => [
                        'bid'         => $bid,
                        'bill_number' => $out_trade_no,
                        'socket_uid'  => $out_trade_no,
                    ]
                ];
                break;
            case 3:
                $wechat_group_guid = $params['wechat_group_guid'];
                $db_wechat_group   = new \app\model\WechatGroup();
                $map               = [
                    ['bid', '=', $bid],
                    ['guid', '=', $wechat_group_guid]
                ];
                $wechat_group      = $db_wechat_group->where($map)->findOrEmpty();
                if ($wechat_group->isEmpty()) {
                    error('微信群标识不存在');
                }
                $total_fee = tools()::nc_price_yuan2fen($wechat_group['price']);
                $attach    = ['wechat_group_guid' => $wechat_group_guid];
                break;
            default:
                throw new Exception('不支持的业务类型');
        }
        $options = [
            'bid'          => $bid,
            'body'         => '微信买单',
            'out_trade_no' => $out_trade_no,
            'total_fee'    => $total_fee,
            'openid'       => $this->get_openid(),
            'appid'        => $this->get_appid(),
            'attach'       => $attach,
            'way'          => $way,
            'job_attach'   => $job_attach
        ];
        if (Visitor::is_wechat_browser()) {
            $options = pay($bid)->driver('wechat')->scene('mp')->apply($options);
            $options = json_encode($options, JSON_UNESCAPED_UNICODE);
            $data    = [
                'options' => $options,
                'bill_no' => $out_trade_no
            ];
            result($data);
        } elseif (Visitor::is_alipay_browser()) {
            $data = pay($bid)->driver('alipay')->scene('wap')->apply($options);
            result($data);
        } else {
            error('请在微信或者支付宝中打开');
        }
    }

    /**
     *查单
     * @access public
     * @return void
     * @throws Exception
     */
    public function query()
    {
        $bill_number = $this->params['bill_no'];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['bill_number', '=', $bill_number],
        ];
        $db          = new PayOrder();
        $order       = $db->where($map)->order(['id' => 'DESC'])->findOrEmpty();
        if ($order->isEmpty()) {
            error('订单号不存在');
        }
        $redirect_url  = '';
        $redirect_path = '';
        $button_text   = '查看订单详情';
        $way           = $order['way'];
        switch ($way) {
            case 4: //卡券兑换
                $bid             = $order['bid'];
                $job_attach_data = $order['job_attach']['data'];
                $order_guid      = $job_attach_data['order_guid'];
                $redirect_url    = (string)url('member/goods_order/detail', ['bid' => $bid, 'order_guid' => $order_guid], false, true);
                $redirect_path   = (string)url('pages/order/detail', ['bid' => $bid, 'order_guid' => $order_guid], false);
                break;
            case 5: //卡券购买
                $bid             = $order['bid'];
                $job_attach_data = $order['job_attach']['data'];
                $order_guid      = $job_attach_data['order_guid'];
                $redirect_url    = (string)url('member/code/code_list', ['bid' => $bid], false, true);
                $redirect_path   = (string)url('pages/code_list/index', ['bid' => $bid, 'order_guid' => $order_guid], false);
                break;
            case 6: //短信充值
                break;
            case 7: //卡券激活
                //                $button_text   = '继续激活';
                //                $redirect_path = '/pages/code/active';
                $job_attach_data = $order['job_attach']['data'];
                $coupon_code     = $job_attach_data['coupon_code'];
                $button_text     = '去提货';
                $redirect_path   = '/pages/code/index?code=' . $coupon_code;
                break;
            default:
                break;
        }
        $data = [
            'order_data'    => $order,
            'redirect_url'  => $redirect_url,
            'redirect_path' => $redirect_path,
            'button_text'   => $button_text
        ];
        result($data);
    }

    /**
     *查单(点进计划成功页面)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_result()
    {
        $params       = $this->params;
        $out_trade_no = $params['out_trade_no'];
        $sub_mch_id   = $params['sub_mch_id'];
        $check_code   = $params['check_code'];
        $begin_time   = date('Y-m-d H:i:s', strtotime('-1 days'));
        $map          = [
            ['bill_number', '=', $out_trade_no],
            ['create_time', '>', $begin_time],
        ];
        $db           = new PayOrder();
        $order        = $db->where($map)->find();
        if (empty($order)) {
            error('订单号不存在');
        }
        $data = [
            'content'      => '谢谢惠顾',
            'button_text'  => '完成',
            'jump_out_url' => null,
        ];
        if ($order['status'] === 1) {
            $page_path = $params['page_path'];
            $sign      = md5($page_path . '?sub_mch_id=' . $sub_mch_id . '&out_trade_no=' . $out_trade_no . '&transaction_id=' . $order['third_trade_no']);
            if ($sign !== $check_code) {
                // error('验签失败');
            }
            //处理业务
            $way = $order['way'];
            switch ($way) {
                case 1: //收款业务
                    $data = [
                        'content'      => '谢谢惠顾',
                        'button_text'  => '完成',
                        'jump_out_url' => null,
                    ];
                    break;
                case 3: //付费进群
                    $attach            = $order['attach'];
                    $wechat_group_guid = $attach['wechat_group_guid'];
                    $db_wechat_group   = new \app\model\WechatGroup();
                    $map               = [
                        ['bid', '=', $order['bid']],
                        ['guid', '=', $wechat_group_guid]
                    ];
                    $wechat_group      = $db_wechat_group->where($map)->findOrEmpty();
                    $data              = [
                        'content'      => '请点击下方按钮进群',
                        'button_text'  => '点击进群',
                        'jump_out_url' => $wechat_group['qrcode'],
                    ];
                    break;
                case 4: //卡券兑换
                    $bid             = $order['bid'];
                    $job_attach_data = $order['job_attach']['data'];
                    $order_guid      = $job_attach_data['order_guid'];
                    $jump_out_url    = (string)url('member/goods_order/detail', ['bid' => $bid, 'order_guid' => $order_guid], false, true);
                    $data            = [
                        'content'      => '请点击下方按钮查看',
                        'button_text'  => '订单详情',
                        'jump_out_url' => $jump_out_url,
                    ];
                    break;
                case 7: //付费激活卡券
                    $jump_out_url = (string)url('admin/coupon_send_note/active', [], false, true);
                    $data         = [
                        'content'      => '激活成功',
                        'button_text'  => '继续激活',
                        'jump_out_url' => $jump_out_url,
                    ];
                    break;
                default:
            }
        }
        result([
            'data'  => $data,
            'order' => $order
        ]);
    }
}