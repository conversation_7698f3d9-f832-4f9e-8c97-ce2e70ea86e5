<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use Exception;

class Wechat extends BasicMemberApi
{
    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function active_card()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $card_id         = $params['card_id'];
        $encrypt_code    = $params['encrypt_code'];
        $activate_ticket = $params['activate_ticket'];
        $member_guid     = $this->get_member_guid();
        //       $wechat_config = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        //       $appid         = $wechat_config['app_id'];

        $config            = get_config_by_bid($bid);
        $appid             = $config['appid'];
        $wechat            = weixin($appid)::WeChatCard();
        $decrypt_code      = $wechat->decrypt($encrypt_code);
        $decrypt_code      = $decrypt_code['code'];
        $user_info         = $wechat->getCardMemberCard($card_id, $decrypt_code);
        $form_info         = $wechat->getActivateMemberCardTempinfo($activate_ticket);
        $data['user_info'] = $user_info;
        $data['form_info'] = $form_info;
        wr_log($data);
        $name   = $user_info['nickname'];
        $mobile = $form_info['info']['common_field_list'][0]['value'] ?? '';
        if (empty($mobile)) {
            error('卡券激活繁忙,手机号为空');
        }
        //todo 更新会员信息
        $map = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
        ];

        //激活会员卡
        $activate_data = [
            'membership_number' => $mobile, //用手机号做卡号
            'code'              => $decrypt_code,
        ];
        $activate_info = $wechat->activateMemberCard($activate_data);
        wr_log($data);
        $db_member = new \app\model\Member();
        $db_member->update_member_info_and_update_coupon_send_note_member_guid($this->get_bid(), $this->get_member_guid(), ['mobile' => $mobile, 'name' => $name]);
        result($data);
    }
}