<?php

namespace app\controller\member_api\v1;


use app\model\AuctionActivity;
use app\model\AuctionGoods;
use app\model\AuctionGoodsOrder;
use app\model\Member as MemberModel;
use Exception;

class Auction extends BasicMemberApi
{
    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_list()
    {
        $params        = $this->params;
        $activity_guid = $params['activity_guid'];
        $db_goods      = new AuctionGoods();
        $key_name      = $params['key'];
        $field         = [$key_name];
        $map           = [
            ['bid', '=', $this->get_bid()],
            ['activity_guid', '=', $activity_guid],
        ];
        if (!empty($params['keyword'])) {
            $map[] = [$key_name, 'LIKE', '%' . $params['keyword'] . '%'];
        }
        $goods = $db_goods->where($map)->group($field)->field($field)->select();
        $data  = [];
        foreach ($goods as $key => $val) {
            $data[] = [
                'name'  => $val[$key_name],
                'value' => $val[$key_name],
            ];
        }
        result($data);
    }

    /**
     *PC端获取商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_pc()
    {
        $params   = $this->params;
        $db_goods = new AuctionGoods();
        $join     = [
            ['auction_activity aa', 'aa.guid=g.activity_guid AND aa.bid=g.bid'],
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
            ['auction_goods_order ago', "ago.goods_guid=g.guid AND ago.bid=g.bid  AND ago.status in(0,1) AND ago.member_guid='$this->get_member_guid()'", $params['inner']],
        ];
        $field    = [
            'g.*',
            'ago.offer_price' => 'offer_price',
        ];
        $map      = [
            ['g.bid', '=', $this->get_bid()],
        ];
        if (!empty($params['activity_guid'])) {
            $map[] = ['aa.guid', '=', $params['activity_guid']];
        }
        if (!empty($params['category_guid'])) {
            $map[] = ['gc.guid', '=', $params['category_guid']];
        }
        if (!empty($params['carrier'])) {
            $map[] = ['g.carrier', 'IN', explode(',', $params['carrier'])];
        }
        if (!empty($params['name'])) {
            $map[] = ['g.name', 'IN', explode(',', $params['name'])];
            // $map[] = ['g.name', 'LIKE', '%' . $params['name'] . '%'];
        }
        if (isset($params['status']) && $params['status'] !== '') {
            $map[] = ['ago.status', '=', $params['status']];
        }
        $order = [
            'aa.guid' => 'ASC',
            'g.no'    => 'ASC',
            'g.name'  => 'ASC',
        ];
        $goods = $db_goods->alias('g')->where($map)->order($order)->join($join)->field($field)->paginate($this->get_paginate_config())->toArray();
        result($goods);
    }

    /**
     *获取商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods()
    {
        $params = $this->params;
        //$category_guid           = $params['category_guid'];
        $activity_guid       = $params['activity_guid'];
        $number              = $params['number'];
        $db_goods            = new AuctionGoods();
        $join                = [
            ['auction_activity aa', 'aa.guid=g.activity_guid AND aa.bid=g.bid'],
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
            ['auction_goods_order ago', "ago.goods_guid=g.guid AND ago.bid=g.bid AND ago.member_guid='$this->get_member_guid()' AND ago.status in(0,1)", 'LEFT'],
        ];
        $field               = [
            'g.guid',
            'g.no'            => 'no',
            'g.name'          => 'title',
            'g.amount'        => 'amount',
            'ago.offer_price' => 'offer_price',
        ];
        $map                 = [
            ['g.bid', '=', $this->get_bid()],
            // ['aa.goods_category_guid', '=', $category_guid],
            ['aa.number', '=', $number],
            //           ['aa.begin_time', '<=', $now_time],
            //           ['aa.end_time', '>', $now_time],
            ['aa.status', '=', '1'],
            ['aa.guid', '=', $activity_guid]
        ];
        $goods               = $db_goods->alias('g')->where($map)->order('g.no,g.name', 'ASC')->join($join)->field($field)->paginate($this->get_paginate_config())->toArray();
        $db_auction_activity = new AuctionActivity();
        $map                 = [
            ['bid', '=', $this->get_bid()],
            //           ['goods_category_guid', '=', $category_guid],
            ['guid', '=', $activity_guid],
            ['number', '=', $number],
        ];
        $filed               = [
            'end_time',
            'pic',
        ];
        $activity_info       = $db_auction_activity->where($map)->field($filed)->findOrEmpty();
        $data                = ['goods_list' => $goods, 'activity_info' => $activity_info];
        result($data);
    }

    /**
     *获取类别
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function cat()
    {
        $db_auction_activity = new AuctionActivity();
        $now_time            = format_timestamp();
        $join                = [
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
        ];
        $field               = [
            'aa.guid'                   => 'activity_guid',
            'aa.pic',
            'aa.end_time',
            'gc.name'                   => 'category_name',
            'gc.guid'                   => 'category_guid',
            'aa.number'                 => 'number',
            'CONCAT(gc.name,aa.number)' => 'goods_category_with_date',
        ];
        $group               = [
            'activity_guid',
            'category_guid',
            'number',
            'aa.pic',
            'aa.end_time',
            'gc.name',
            'CONCAT(gc.name,aa.number)',
        ];
        $map                 = [
            ['aa.bid', '=', $this->get_bid()],
            ['aa.begin_time', '<=', $now_time],
            ['aa.end_time', '>', $now_time],
            ['aa.status', '=', '1']
        ];
        $data                = $db_auction_activity->alias('aa')->where($map)->group($group)->join($join)->field($field)->select();
        result($data);
    }

    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db_goods  = new AuctionGoods();
        $tabItems  = [];
        $goodsList = [];
        $now_time  = format_timestamp();
        $join      = [
            ['auction_activity aa', 'aa.guid=g.activity_guid AND aa.bid=g.bid'],
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
            ['auction_goods_order ago', "ago.goods_guid=g.guid AND ago.bid=g.bid AND ago.member_guid='$this->get_member_guid()' AND ago.status in(0,1)", 'LEFT'],
        ];
        $field     = [
            'aa.pic',
            'aa.end_time',
            'g.guid',
            'g.no'                      => 'no',
            'g.name'                    => 'title',
            "left(g.name,8)"            => 'short_title',
            'g.starting_price'          => 'price',
            'g.amount'                  => 'amount',
            'gc.name'                   => 'category_name',
            'CONCAT(gc.name,aa.number)' => 'goods_category_with_date',
            'ago.offer_price'           => 'offer_price',
        ];
        $map       = [
            ['g.bid', '=', $this->get_bid()],
            ['aa.begin_time', '<=', $now_time],
            ['aa.end_time', '>', $now_time],
            ['aa.status', '=', '1']
        ];
        $goods     = $db_goods->alias('g')->where($map)->order('g.no,g.name', 'ASC')->join($join)->field($field)->select();
        $cat       = array_column(tools()::object2array($goods), 'goods_category_with_date');
        $cat       = array_unique($cat);
        $id        = 0;
        foreach ($cat as $key => $val) {
            $tabItems[]     = [
                'name' => $val
            ];
            $goodsList[$id] = [
                'name' => $val,
                'id'   => 'id' . $id++,
            ];
            foreach ($goods as $k => $v) {
                if ($v['goods_category_with_date'] === $val) {
                    $goodsList[$id]['items'][]  = $v;
                    $goodsList[$id]['pic']      = $v['pic'];
                    $goodsList[$id]['end_time'] = $v['end_time'];
                }
            }
        }
        $data = ['goodsList' => $goodsList, 'tabItems' => $tabItems];
        result($data);
    }

    /**
     *通过商品guid获取出价详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_offer_info()
    {
        $params     = $this->params;
        $goods_guid = $params['goods_guid'];
        $map        = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            ['goods_guid', '=', $goods_guid],
            ['status', '=', 0]
        ];
        $db         = new AuctionGoodsOrder();
        $note       = $db->where($map)->find();
        result($note ?: []);
    }

    /**
     *出价
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function offer()
    {
        $params                = $this->params;
        $db_action_goods_order = new AuctionGoodsOrder();
        $goods_guid            = $params['goods_guid'];
        $offer_price           = $params['offer_price'];
        if (!$offer_price) {
            $this->cancel_offer();
        }
        $rate              = 0.03;
        $map               = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            ['status', '=', 0],
            ['goods_guid', '<>', $goods_guid],
        ];
        $total_offer_price = $db_action_goods_order->where($map)->sum('offer_price');
        $db_member         = new MemberModel();
        $map               = [
            'bid'  => $this->get_bid(),
            'guid' => $this->get_member_guid(),
        ];
        $member            = $db_member->get_member_info($map);
        $max_offer_price   = (int)($member['money'] / $rate);
        if ($max_offer_price < ($total_offer_price + $offer_price)) {
            error('可用额度不足,您可出价金额:' . $max_offer_price . ',当前已出价:' . $total_offer_price);
        }
        $map             = [
            ['ag.bid', '=', $this->get_bid()],
            ['ag.guid', '=', $goods_guid],
        ];
        $join            = [
            ['auction_activity aa', 'ag.bid = aa.bid AND ag.activity_guid=aa.guid']
        ];
        $db_action_goods = new AuctionGoods();
        $activity_info   = $db_action_goods->alias('ag')->field(['aa.guid', 'aa.begin_time', 'aa.end_time', 'aa.status'])->join($join)->where($map)->find();
        if (empty($activity_info)) {
            error('活动不存在');
        }
        if ($activity_info['status'] != 1) {
            error('活动已下线');
        }
        if (strtotime($activity_info['begin_time']) > time()) {
            error('活动尚未开始');
        }
        if (strtotime($activity_info['end_time']) < time()) {
            error('活动已经结束');
        }
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            ['goods_guid', '=', $goods_guid],
            ['status', '=', 0]
        ];
        $guid = $db_action_goods_order->where($map)->value('guid');
        if ($guid) {
            $update_data = [
                'offer_price' => $offer_price
            ];
            $offer       = $db_action_goods_order::update($update_data, $map);
        } else {
            $insert_data        = [
                'bid'         => $this->get_bid(),
                'member_guid' => $this->get_member_guid(),
                'guid'        => create_guid(),
                'bill_number' => tools()::get_bill_number(),
                'offer_price' => $offer_price,
                'goods_guid'  => $goods_guid,
            ];
            $offer              = $db_action_goods_order->save($insert_data);
            $db_action_activity = new AuctionActivity();
            $map                = [
                ['bid', '=', $this->get_bid()],
                ['guid', '=', $activity_info['guid']],
            ];
            $db_action_activity->where($map)->setInc('offer_count');
        }
        success('出价成功');
    }

    /**
     *取消出价
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function cancel_offer()
    {
        $params      = $this->params;
        $db          = new AuctionGoodsOrder();
        $goods_guid  = $params['goods_guid'];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            ['goods_guid', '=', $goods_guid]
        ];
        $update_data = [
            'status' => -1,
            'result' => '【主动取消】'
        ];
        $db::update($update_data, $map);
        success('取消成功');
    }

    /**
     *订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function offer_note()
    {
        $db = new AuctionGoodsOrder();
        if (isset($this->params['status'])) {
            $this->params['ago.status'] = $this->params['status'];
            unset($this->params['status']);
        }
        $join        = [
            ['auction_goods ag', "ago.goods_guid = ag.guid AND ago.bid = ag.bid"],
            ['auction_activity aa', "ag.activity_guid = aa.guid AND ago.bid = aa.bid"],
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
        ];
        $map         = [
            ['ago.bid', '=', $this->get_bid()],
            ['ago.member_guid', '=', $this->get_member_guid()],
        ];
        $field       = [
            'gc.name' => 'category_name',
            'ago.offer_price',
            'ago.update_time',
            'ag.name',
            'ago.result',
            'ago.express',
            'ago.express_no',
        ];
        $this->model = $db->alias('ago')->join($join)->where($map)->field($field)->order(['ago.update_time' => 'DESC']);
        result($this->_list());
    }
}