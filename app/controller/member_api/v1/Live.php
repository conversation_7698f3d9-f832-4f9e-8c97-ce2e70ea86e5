<?php

namespace app\controller\member_api\v1;


use Exception;
use Throwable;

class Live extends BasicMemberApi
{
    /**
     * 直播列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $mini = weixin($this->get_appid())::WeMiniLive();
        try {
            $result = $mini->getLiveList();
        } catch (Throwable|Exception $e) {
            switch ($e->getCode()) {
                case '48001':
                    error('当前小程序未开通直播权限');
                    break;
                default:
                    error($e->getMessage());
                    break;
            }
        }
        if (!empty($result['room_info'])) {
            foreach ($result['room_info'] as $key => $val) {
                $result['room_info'][$key]['start_time'] = date('Y-m-d H:i:s', $result['room_info'][$key]['start_time']);
                $result['room_info'][$key]['end_time']   = date('Y-m-d H:i:s', $result['room_info'][$key]['end_time']);
            }
        }
        result($result);
    }

    /**
     * 回放列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function replay()
    {
        $mini   = weixin($this->get_appid())::WeMiniLive();
        $result = $mini->getLiveInfo();
        result($result);
    }
}