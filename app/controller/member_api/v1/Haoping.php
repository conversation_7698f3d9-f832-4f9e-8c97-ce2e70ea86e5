<?php

namespace app\controller\member_api\v1;

use app\model\HaopingOrder;
use Exception;

class Haoping extends BasicMemberApi
{
    /**
     * 提交好评返现
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function hao_ping()
    {
        $param    = $this->params;
        $openid   = $param['openid'];
        $order_no = $param['order_sn'];
        $db       = new HaopingOrder();
        $map      = [
            ['bid', '=', $this->get_bid()],
            ['order_no', '=', $order_no],
            ['order_no_status', '=', 1]
        ];
        $note     = $db->where($map)->find();
        if (!$note) {
            error('订单号:' . $order_no . '不存在或者订单未同步,请稍后再提交');
        }
        //已存在的逻辑
        //2 审核通过并提现成功
        if ($note['status'] == 1) {
            error('该订单已领取,请勿重复领取哦');
        }
        //正在审核中
        if ($note['status'] == 0) {
            error('您提交的订单号正在审核中,请耐心等待');
        }
        //审核被拒绝
        if ($note['status'] == -1) {
            error('您提交的订单号审核不通过~');
        }
        $param['evaluate_images'] = $this->request->param('evaluate_images', '', null);
        $pic                      = implode(',', json_decode($param['evaluate_images'], true));
        //插入新记录
        $data = [
            'openid' => $openid,
            'pic'    => $pic,
            'status' => 0//订单状态变成审核中
        ];
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $note['guid']],
        ];
        $db::update($data, $map); //更新订单
        result($param, '订单号已提交，审核成功后返现将以红包形式发送，请注意在微信查收，并于24小时内领取。');
    }
}