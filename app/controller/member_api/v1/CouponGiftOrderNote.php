<?php

namespace app\controller\member_api\v1;

use app\model\Coupon;
use app\model\CouponGiftOrderNote as CouponGiftOrderNoteModel;
use app\model\GoodsOrder;
use Exception;

/**
 * 卡券送礼订单控制器
 *
 * 主要功能：
 * 1. 通过卡号密码查看送礼信息和商品列表
 * 2. 处理收礼确认操作
 * 3. 管理送礼券的状态和订单详情
 *
 * @package app\controller\member_api\v1
 * <AUTHOR>
 * @version 1.0
 */
class CouponGiftOrderNote extends BasicMemberApi
{
    /**
     * 通过卡号密码查看送礼信息
     *
     * 功能说明：
     * 1. 验证卡号和密码的有效性
     * 2. 检查是否为送礼券类型（type=4）
     * 3. 获取可选择的商品列表
     * 4. 查询已有的送礼记录和订单详情
     * 5. 返回完整的送礼信息供前端展示
     *
     * @access public
     * @throws Exception 当卡券验证失败或非送礼券时抛出异常
     */
    public function info()
    {
        // 获取请求参数
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $token                 = $params['token'];
        $info                  = cache($token);
        $coupon_send_note_guid = $info['coupon_send_note_guid'];
        $coupon_guid           = $info['coupon_guid'];
        // 验证卡券有效性

        /*
         * $coupon_send_note 卡券发送记录数据结构示例：
         * [
         *     "id" => "6008727",                                    // 记录ID
         *     "guid" => "4014f45f-3ca1-895e-12f0-2b7a912a6c88",   // 记录GUID
         *     "bid" => "f37eb109-70ac-9e6e-a089-ecefebd3d396",    // 商家ID
         *     "coupon_guid" => "b0317859-88ee-6816-1168-ff5ebf533aff", // 卡券模板GUID
         *     "code" => "100590",                                  // 卡号
         *     "password" => "123013",                              // 卡密
         *     "status" => 0,                                       // 状态：0-已激活未使用，1-已使用
         *     "availability_time" => "2025-07-17",                // 生效时间
         *     "expire_time" => "2099-12-31",                      // 过期时间
         *     "create_time" => "2025-07-17 16:04:24",            // 创建时间
         *     // ... 其他字段
         * ]
         */

        // 查询卡券基础信息（模板信息）
        $db_coupon   = new Coupon();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid]
        ];
        $coupon_info = $db_coupon->where($map)->findOrFail();

        // 验证卡券类型：必须是送礼券（type=4）
        $is_gift_coupon = ($coupon_info['type'] == 4);
        if (!$is_gift_coupon) {
            error('非送礼券');
        }

        // 查询该卡券的送礼记录（按创建时间倒序，获取最新记录）
        $db_gift_order_note = new CouponGiftOrderNoteModel();
        $map                = [
            ['bid', '=', $bid],
            ['status', 'NOT IN', [-1]],
            ['coupon_send_note_guid', '=', $coupon_send_note_guid]
        ];
        $gift_records       = $db_gift_order_note->where($map)->order(['create_time' => 'desc'])->findOrEmpty();

        // 获取订单详情（如果存在送礼记录且有订单GUID）
        $order_detail = [];
        if (!$gift_records->isEmpty() && $gift_records['order_guid']) {
            // 查询关联的商品订单详情
            $db_goods_order = new GoodsOrder();
            $order_detail   = $db_goods_order->get_order_detail($bid, $gift_records['order_guid']);
        }

        $gift_records['need_gift_password'] = !empty($gift_records['gift_password']);

        // 构建返回数据结构
        $result = [
            'coupon_info'      => [
                'guid' => $coupon_info['guid'],    // 卡券模板GUID
                'name' => $coupon_info['name'],    // 卡券名称
            ],
            'coupon_send_note' => [
                'guid' => $coupon_send_note_guid, // 卡券发送记录GUID
            ],
            'gift_records'     => $gift_records,     // 送礼记录（包含状态、收礼口令等）
            'order_detail'     => $order_detail,     // 订单详情（商品列表、收货地址等）
        ];
        result($result);
    }


    /**
     * 确认收礼接口
     *
     * 功能说明：
     * 1. 验证用户身份和权限
     * 2. 收集收礼所需的参数（送礼记录GUID、收货地址GUID等）
     * 3. 调用模型层处理收礼业务逻辑
     * 4. 返回处理结果
     *
     * 业务流程：
     * - 验证送礼记录是否存在且状态为待领取
     * - 验证收货地址是否有效
     * - 创建商品订单
     * - 更新送礼记录状态为已领取
     * - 发送配送通知
     *
     * @access public
     * @return void
     * @throws Exception 当参数验证失败或业务处理异常时抛出
     */
    public function claim_gift()
    {
        // 获取请求参数和用户信息
        $params      = $this->params;
        $bid         = $this->get_bid();         // 商家ID
        $member_guid = $this->get_member_guid(); // 会员GUID
        $openid      = $this->get_openid();      // 微信OpenID

        // 组装业务处理所需的数据
        $data = [
            'bid'              => $bid,                                    // 商家ID
            'member_guid'      => $member_guid,                           // 收礼人会员GUID
            'openid'           => $openid,                                // 收礼人微信OpenID
            'gift_record_guid' => $params['gift_record_guid'] ?? '',      // 送礼记录GUID
            'address_guid'     => $params['address_guid'] ?? '',          // 收货地址GUID
            'gift_password'    => $params['gift_password'] ?? '',         // 收礼口令（可选）
        ];

        // 调用模型层处理收礼业务逻辑
        $model  = new CouponGiftOrderNoteModel();
        $result = $model->claim_gift_by_data($data);

        // 根据处理结果返回相应信息
        if ($result === true) {
            success('收礼成功，礼品将尽快为您配送');
        } else {
            error($model->getError() ?: '收礼失败，请稍后重试');
        }
    }
}
