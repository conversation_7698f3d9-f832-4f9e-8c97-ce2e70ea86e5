<?php

namespace app\controller\member_api\v1;

use app\model\Business;
use app\model\CouponSendNote;
use app\model\Favorites as FavoritesModel;
use app\model\GoodsOrder;
use app\model\WeappSubmitNote;
use app\common\service\SmsService;
use Exception;

class User extends BasicMemberApi
{

    /**
     *个人中心
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_goods_order             = new GoodsOrder();
        $bid                        = $this->get_bid();
        $config                     = get_config_by_bid($bid);
        $member_guid                = $this->get_member_guid();
        $db_user                    = new \app\model\User();
        $map                        = [
            ['bid', '=', $bid],
            ['delete_time', 'NULL', null], //过滤已删除订单
            ['member_guid', '=', $member_guid],
        ];
        $status_count               = $db_goods_order->where($map)->group('status')->column('count(1) as count', 'status');
        $db_member                  = new \app\model\Member();
        $is_distributor             = $db_member->is_distributor_by_member_guid($bid, $member_guid);
        $member_info                = $db_member->get_member_info(['bid' => $bid, 'guid' => $member_guid]);
        $db_coupon_send_note        = new CouponSendNote();
        $map_coupon                 = [
            ['csn.bid', '=', $bid],
            ['csn.member_guid', '=', $member_guid],
            ['c.delete_time', 'null', null],
            ['csn.delete_time', 'null', null],
            ['csn.revoke_time', 'null', null],
        ];
        $join                       = [
            ['coupon c', 'csn.coupon_guid = c.guid AND csn.bid = c.bid'],
        ];
        $coupon_count_all           = $db_coupon_send_note->alias('csn')->join($join)->where($map_coupon)->count();
        $goods_order_count_all      = $db_goods_order->where($map)->count();
        $db_favorites               = new FavoritesModel();
        $map_db_favorites           = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['status', '=', 1],
        ];
        $favorites_count            = $db_favorites->where($map_db_favorites)->count();
        $card_list                  = [
            [
                'number'    => $coupon_count_all,
                'title'     => '卡券(张)',
                'auth_name' => '卡券',
                'url'       => '/pages/code_list/index',
            ],
            [
                'number'    => $member_info['money'] ?? 0,
                'title'     => '余额(' . $config['money_unit'] . ')',
                'auth_name' => '余额',
                'url'       => '/pages/recharge/recharge',
            ], [
                'number'    => $member_info['point'] ?? 0,
                'title'     => '积分(分)',
                'auth_name' => '积分',
                'url'       => '/pages/balance/point_balance',
            ],
            [
                'number'    => $member_info['brokerage'] ?? 0,
                'title'     => '佣金(元)',
                'auth_name' => '佣金',
                'url'       => '/pages/share/index',
            ],
            [
                'number'    => $favorites_count,
                'title'     => '收藏夹',
                'auth_name' => '收藏夹',
                'url'       => '/pages/favorite/favorite',
            ],
        ];
        $card_list                  = $db_user->unset_permission_denied_key_from_array($bid, $card_list, 'user_center_topbar');
        $not_distributor_hide_apply = $config['not_distributor_hide_apply'];
        if ($not_distributor_hide_apply == 1 && !$is_distributor) {
            foreach ($card_list as $key => $val) {
                if ($val['auth_name'] == '佣金') {
                    unset($card_list[$key]);
                }
            }
        }
        $coupon_plan_url = (string)url('member/coupon_plan/list', ['bid' => $bid], false, true);
        $url_list        = [
            [
                'type'      => 2, // 1 网页 2 小程序页面
                'name'      => '分销中心',
                'auth_name' => '分销中心',
                'image'     => '/images/icon-user-fx.png',
                'url'       => '/pages/add-share/index',
            ],
            [
                'type'      => 2, // 1 网页 2 小程序页面
                'name'      => '收货地址',
                'auth_name' => '收货地址',
                'image'     => '/images/icon-user-dz.png',
                'url'       => '/pages/address/address',
            ],
            [
                'type'      => 2, // 1 网页 2 小程序页面
                'name'      => '绑定手机',
                'auth_name' => '绑定手机',
                'image'     => '/images/icon-user-bangding.png',
                'url'       => '/pages/get_mobile/get_mobile',
            ],
            //                   [
            //                       'type'  => 2, // 1 网页 2 小程序页面
            //                       'name'  => '个人资料',
            //                       'image' => '/images/icon-user-bangding.png',
            //                       'url'   => '/pages/user/edit',
            //                   ],

            //                   [
            //                       'type'  => 1, // 1 网页 2 小程序页面
            //                       'name'  => '关于我们',
            //                       'image' => '/images/icon-order--1.png',
            //                       'url'   => 'https://www.yikayi.net',
            //                       // 'url'   => '/index/index/index',
            //                   ],

            [
                'type'      => 3, // 1 网页 2 小程序页面
                'name'      => '在线客服',
                'auth_name' => '在线客服',
                'image'     => '/images/icon-user-kf.png',
                'url'       => '/pages/index/index',
            ],
            [
                'type'      => 5, // 1 网页 2 小程序页面
                'name'      => '客服电话',
                'auth_name' => '客服电话',
                'image'     => '/images/icon-user-phone.png',
                'phone'     => $config['service_phone']
                //                'url'       => '/pages/index/index',
            ],
            [
                'type'      => 4, // 1 网页 2 小程序页面
                'name'      => '清除缓存',
                'auth_name' => '清除缓存',
                'image'     => '/images/icon-clear-cache.png',
                //'url'       => '/pages/index/index',
            ],
            [
                'type'      => 1, // 1 网页 2 小程序页面
                'name'      => '方案咨询',
                'auth_name' => '方案咨询',
                'image'     => '/images/list.png',
                'url'       => $coupon_plan_url,
            ],
            [
                'type'      => 2, // 1 网页 2 小程序页面
                'name'      => '自助激活',
                'auth_name' => '自助激活',
                'image'     => '/images/icon-active.png',
                'url'       => '/pages/code/active',
            ],
        ];
        $url_list        = $db_user->unset_permission_denied_key_from_array($bid, $url_list, 'user_center_url_list');
        if ($not_distributor_hide_apply == 1 && !$is_distributor) {
            foreach ($url_list as $key => $val) {
                if ($val['auth_name'] == '分销中心') {
                    unset($url_list[$key]);
                }
            }
        }
        $data = [
            'bid'                     => $bid,
            'user_info'               => $member_info,
            'coupon_count'            => [
                'all' => $coupon_count_all
            ],
            'show_pay_qrcode'         => (bool)$config['show_pay_qrcode'],
            'card_list'               => $card_list,
            'card_list_width'         => (1 / count($card_list)) * 100,
            'goods_order_count'       => [
                'all' => $goods_order_count_all,
            ],
            'url_list'                => $url_list,
            'goods_order_status_list' =>
                [
                    [
                        'status' => -1,
                        'name'   => '待支付',
                        'image'  => '/images/icon-order--1.png',
                        'count'  => $status_count[-1] ?? 0,
                    ],
                    [
                        'status' => 0,
                        'name'   => '待发货',
                        'image'  => '/images/icon-order-0.png',
                        'count'  => $status_count[0] ?? 0,
                    ],
                    [
                        'status' => 1,
                        'name'   => '待收货',
                        'image'  => '/images/icon-order-1.png',
                        'count'  => $status_count[1] ?? 0,
                    ],
                    [
                        'status' => 2,
                        'name'   => '已完成',
                        'image'  => '/images/icon-order-2.png',
                        'count'  => $status_count[2] ?? 0,
                    ],
                ]
        ];
        result($data);
    }

    public function get_wechat_toolbar()
    {
        $bid     = $this->get_bid();
        $params  = $this->params;
        $path    = $params['path'] ?? '';
        $config  = get_config_by_bid($bid);
        $toolbar = [
            [
                'href'      => '/member/code/index',
                'name'      => '提货',
                'icon'      => 'layui-icon-home',
                'auth_name' => '提货',
            ],
            [
                'href'      => '/member/code/query',
                'name'      => '核验',
                'icon'      => 'layui-icon-vercode',
                'auth_name' => '核验',
            ],
            [
                'href'      => '/member/code/recommend',
                'name'      => '推荐',
                'icon'      => 'layui-icon-share',
                'auth_name' => '推荐',
            ],
            [
                'href'      => '/member/topic/list',
                'name'      => $config['topic_list_menu_name'],
                'icon'      => 'layui-icon-star-fill',
                'auth_name' => '专题',
            ],
            [
                'href'      => '/member/code/choose_code',
                'name'      => '卡券',
                'icon'      => 'layui-icon-template-1',
                'auth_name' => '卡券',
            ],
            [
                'href'      => '/member/code/code_list',
                'name'      => '卡包',
                'icon'      => 'layui-icon-template-1',
                'auth_name' => '卡包',
            ],
            [
                'href'      => '/member/code/buy',
                'name'      => '商城',
                'icon'      => 'layui-icon-cart-simple',
                'auth_name' => '商城',
            ],
            [
                'href'      => '/member/goods_order/list',
                'name'      => '订单',
                'icon'      => 'layui-icon-form',
                'auth_name' => '订单',
            ],
            [
                'href'      => '/member/user/index',
                'name'      => '我的',
                'icon'      => 'layui-icon-user',
                //               'auth'      => 'member_user_index',
                'auth_name' => '我的',
            ],
        ];

        $db_user = new \app\model\User();
        $toolbar = $db_user->unset_permission_denied_key_from_array($bid, $toolbar, 'wechat_toolbar');

        // 获取商户主题色
        $theme_color = $config['member_theme_color'] ?? '#1890ff';

        foreach ($toolbar as $key => $val) {
            if ($path == $val['href']) {
                $toolbar[$key]['color']     = $theme_color; // 使用主题色
                $toolbar[$key]['is_active'] = true;
            } else {
                $toolbar[$key]['color']     = '#999';
                $toolbar[$key]['is_active'] = false;
            }
        }
        result(['bid' => $bid, 'toolbar' => $toolbar, 'theme_color' => $theme_color]);
    }

    public function get_weapp_toolbar()
    {
        $bid                      = $this->get_bid();
        $params                   = $this->params;
        $config                   = get_config_by_bid($bid);
        $mini_program_env_version = $params['mini_program_env_version'] ?? null;
        $appid                    = $params['appid'] ?? null;
        //       bid: extConfig.bid,
        //       appid: wx.getAccountInfoSync().miniProgram.appId,
        //       mini_program_ext_version: extConfig.version,
        //       mini_program_env_version: wx.getAccountInfoSync().miniProgram.envVersion,
        //       mini_program_version: wx.getAccountInfoSync().miniProgram.version,
        //       from: 'weapp'
        //
        $data                 = [
            'color'         => '#a9b7b7',
            'selectedColor' => '#FF4544',
            'borderStyle'   => 'white',
            'position'      => 'bottom',
            'list'          => [

                [
                    'pagePath'         => '/pages/index/index',
                    'selectedIconPath' => '/images/appnavbar/nav-icon-mall.active.png',
                    'iconPath'         => '/images/appnavbar/nav-icon-mall.png',
                    'text'             => '商城',
                    'auth_name'        => '商城',
                    //                    'auth'             => 'mall',
                    //                   'selected'         => false

                ],
                [
                    'pagePath'         => '/pages/quick-purchase/index/index',
                    'selectedIconPath' => '/images/appnavbar/nav-icon-cat.active.png',
                    'iconPath'         => '/images/appnavbar/nav-icon-cat.png',
                    'text'             => '分类',
                    'auth_name'        => '分类',
                    'auth'             => 'mall',
                ],
                [
                    'pagePath'         => '/pages/code/index',
                    'selectedIconPath' => '/images/appnavbar/nav-icon-buy_code.active.png',
                    'iconPath'         => '/images/appnavbar/nav-icon-buy_code.png',
                    'text'             => $config['exchange_menu_name'],
                    'auth_name'        => '提货',
                    //                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/recharge/recharge?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-buy_code.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-buy_code.png',
                    'text'                => '充值',
                    'auth_name'           => '充值',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/code_list/index?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-code_list.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-code_list.png',
                    'text'                => '卡包',
                    'auth_name'           => '卡包',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/code/choose_code?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-code_list.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-code_list.png',
                    'text'                => '卡券',
                    'auth_name'           => '卡券',
                    'hide_on_develop_env' => true,
                ],
                //                [
                //                    'pagePath'            => '/pages/code/gift',
                //                    'selectedIconPath'    => '/images/appnavbar/nav-icon-code_list.active.png',
                //                    'iconPath'            => '/images/appnavbar/nav-icon-code_list.png',
                //                    'text'                => '送礼',
                //                    'auth_name'           => '送礼',
                //                    'hide_on_develop_env' => true,
                //                ],
                [
                    'pagePath'            => '/pages/code/active?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-active_code.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-active_code.png',
                    'text'                => '激活',
                    'auth_name'           => '激活',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/industry/list?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-industry_list.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-industry_list.png',
                    'text'                => $config['industry_list_menu_name'],
                    'auth_name'           => '行业',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/store/list?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-industry_list.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-industry_list.png',
                    'text'                => $config['store_list_menu_name'],
                    'auth_name'           => '门店',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/order/order?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-order_list.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-order_list.png',
                    'text'                => '订单',
                    'auth_name'           => '订单',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/topic-list/topic-list?show_tab_bar=1',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-topic.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-topic.png',
                    'text'                => '关于我们',
                    'auth_name'           => '关于我们',
                    'auth'                => 'topic/index',
                    'hide_on_develop_env' => true,
                ],
                [
                    'pagePath'            => '/pages/live/index',
                    'selectedIconPath'    => '/images/appnavbar/nav-icon-live.active.png',
                    'iconPath'            => '/images/appnavbar/nav-icon-live.png',
                    'text'                => '直播',
                    'auth_name'           => '直播',
                    'hide_on_develop_env' => true,
                    //                   'auth'             => 'mall'
                ],
                [
                    'pagePath'         => '/pages/cart/cart',
                    'selectedIconPath' => '/images/appnavbar/nav-icon-cart.active.png',
                    'iconPath'         => '/images/appnavbar/nav-icon-cart.png',
                    'text'             => '购物车',
                    'auth_name'        => '购物车',
                    'auth'             => 'mall',
                ],
                //               [
                //                   'pagePath'         => '/pages/order/order',
                //                   'selectedIconPath' => '/images/appnavbar/nav-icon-cat.active.png',
                //                   'iconPath'         => '/images/appnavbar/nav-icon-cat.png',
                //                   'text'             => '订单',
                //               ],
                [
                    'pagePath'         => '/pages/user/user',
                    'selectedIconPath' => '/images/appnavbar/nav-icon-user.active.png',
                    'iconPath'         => '/images/appnavbar/nav-icon-user.png',
                    'auth_name'        => '我的',
                    'text'             => '我的',
                ]
            ]];
        $db_user              = new \app\model\User();
        $db_weapp_submit_note = new WeappSubmitNote();
        $is_has_auditing_note = $db_weapp_submit_note->is_has_auditing_note($appid);
        $data['list']         = $db_user->unset_permission_denied_key_from_array($bid, $data['list'], 'weapp_toolbar');
        foreach ($data['list'] as $key => $val) {
            //开发版
            if (!is_debug() && $is_has_auditing_note && $mini_program_env_version == 'develop' && isset($val['hide_on_develop_env']) && $val['hide_on_develop_env'] == true) {
                unset($data['list'][$key]);
            }
        }
        result($data);
    }

    /**
     *短信设置模式
     * @access public
     * @return void
     * @throws Exception
     */
    public function sms_setting()
    {
        $data = ['type' => 1];
        result($data);
    }

    /**
     *发送短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_sms_code()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $mobile      = $params['mobile'];
        $db_business = new Business();
        $map         = [['guid', '=', $bid]];
        $sms_num     = (int)$db_business->where($map)->value('sms_num');
        if ($sms_num <= 0) {
            error('商家可用短信不足,请联系客服');
        }
        $sms = SmsService::get_instance($this->get_bid());
        $sms->send_sms_code($mobile);
    }

    public function update_mobile()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $mobile      = $params['mobile'];
        if ($mobile != '18603047034_1') {
            $verify_code = $params['verify_code'];
            $sms         = SmsService::get_instance($bid);
            $result      = $sms->verify_sms_code($mobile, $verify_code);
        }
        $db_member   = new \app\model\Member();
        $update_data = ['mobile' => $mobile];
        $member      = $db_member->update_member_info_and_update_coupon_send_note_member_guid($bid, $member_guid, $update_data);
        success('登录成功');
    }
}