<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;

use app\model\AddressParserNote;
use app\model\Area;
use app\model\AreaRule;
use app\model\MemberAddress;
use Exception;

class Address extends BasicMemberApi
{

    public function auto_parse()
    {
        $address_text = $this->params['address'];
        if (empty($address_text)) {
            error('请复制地址!');
        }
        $db = new AddressParserNote();
        $db->auto_parse($address_text);
    }


    public function list()
    {
        $db          = new MemberAddress();
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $map         = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['delete_time', 'null', null],
        ];
        $field       = [
            'guid',
            'province_id',
            'a1.name' => 'province_name',
            'city_id',
            'a2.name' => 'city_name',
            'area_id',
            'a3.name' => 'area_name',
            'ma.address',
            'ma.true_name',
            'ma.mobile',
            'is_default'
        ];
        $join        = [
            ['area a1', 'a1.id=ma.province_id'],
            ['area a2', 'a2.id=ma.city_id'],
            ['area a3', 'a3.id=ma.area_id'],
        ];
        $list        = $db->alias('ma')->where($map)->field($field)->join($join)->order(['is_default' => 'DESC'])->select();
        result($list);
    }

    /**
     *获取地址
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db          = new MemberAddress();
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $guid        = $this->params['guid'];
        $info        = $db->get_address_detail($bid, $guid);
        result($info);
    }

    public function add()
    {
        $params           = $this->params;
        $db               = new MemberAddress();
        $params['mobile'] = tools()::find_string_number($params['mobile']);
        if (!tools()::is_mobile($params['mobile'])) {
            error('手机号格式不正确,请输入1开头11位手机号');
        }
        $result = $db->add($params);
        success('添加成功');
    }

    public function edit()
    {
        $params           = $this->params;
        $db               = new MemberAddress();
        $params['mobile'] = tools()::find_string_number($params['mobile']);
        if (!tools()::is_mobile($params['mobile'])) {
            error('手机号格式不正确,请输入1开头11位手机号');
        }
        $result = $db->edit($params);
        success('修改成功');
    }

    public function set_default()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $db          = new MemberAddress();
        $guid        = $this->params['guid'];
        $update_data = ['is_default' => 0];
        $map         = [
            ['bid', '=', $bid],
            ['guid', '<>', $guid],
        ];
        $db::update($update_data, $map);
        $update_data = ['is_default' => 1];
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $db::update($update_data, $map);
        success('设置成功');
    }

    /**
     *添加微信地址
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $db          = new MemberAddress();
        $params      = $this->params;
        $guid        = $params['guid'];
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $guid]
        ];
        $update_data = ['delete_time' => format_timestamp()];
        $db::update($update_data, $map);
        success('删除成功');
    }


    public function get_area_id_from_wechat()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $province_name = $params['province_name'];
        $city_name     = $params['city_name'];
        $area_name     = $params['area_name'];
        $db_area       = new Area();
        $area_info     = $db_area->get_area_id_from_wechat($province_name, $city_name, $area_name);
        $db_area_rule  = new AreaRule();
        $check_area    = $db_area_rule->check_area($bid, $area_info);
        if ($check_area === false) {
            error($db_area_rule->getError());
        }
        result($area_info);
    }

    /**
     *添加微信地址
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add_wechat_address()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $province_name = $params['province_name'];
        $city_name     = $params['city_name'];
        $area_name     = $params['area_name'];
        $db_area       = new Area();
        $area_info     = $db_area->get_area_id_from_wechat($province_name, $city_name, $area_name);
        if (empty($area_info['area_id'])) {
            error('省市区自动匹配失败,请手动添加收获地址');
        }
        $db_area_rule = new AreaRule();
        $check_area   = $db_area_rule->check_area($bid, $area_info);
        if ($check_area === false) {
            error($db_area_rule->getError());
        }
        $params['mobile'] = tools()::find_string_number($params['mobile']);
        if (!tools()::is_mobile($params['mobile'])) {
            error('手机号格式不正确,请输入1开头11位手机号');
        }
        $data   = [
            'guid'        => create_guid(),
            'address'     => $params['address'],
            'true_name'   => $params['true_name'],
            'mobile'      => $params['mobile'],
            'province_id' => $area_info['province_id'],
            'city_id'     => $area_info['city_id'],
            'area_id'     => $area_info['area_id'],
        ];
        $db     = new MemberAddress();
        $result = $db->add($data);
        result($data);
    }


}