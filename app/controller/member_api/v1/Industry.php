<?php

namespace app\controller\member_api\v1;

use app\model\Industry as IndustryModel;
use Exception;
use think\facade\Db;

class Industry extends BasicMemberApi
{
    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db     = new IndustryModel();
        $params = $this->params;
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1]
        ];
        $result = $db->where($map)->order(['sort' => 'ASC'])->select();
        result($result);
    }


    /**
     *门店详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db        = new StoreModel();
        $params    = $this->params;
        $longitude = $params['lon'] ?? 0;
        $latitude  = $params['lat'] ?? 0;
        $bid       = $this->get_bid();
        $guid      = $params['guid'];
        $map       = [
            ['guid', '=', $guid],
            ['bid', '=', $bid],
        ];
        $field     = [
            'a1.name' => 'province_name',
            'a2.name' => 'city_name',
            'a3.name' => 'area_name',
            's.*',
            Db::raw("ROUND(6378.138*2*ASIN(SQRT(POW(SIN(($latitude*PI()/180-latitude*PI()/180)/2),2)+COS($latitude*PI()/180)*COS(latitude*PI()/180)*POW(SIN(($longitude*PI()/180-longitude*PI()/180)/2),2)))*1000) as distance")
        ];
        $join      = [
            ['area a1', "s.province_id = a1.id"],
            ['area a2', "s.city_id = a2.id AND a2.pid=a1.id"],
            ['area a3', "s.area_id = a3.id AND a3.pid=a2.id"],
        ];
        unset($this->params['lon']);
        unset($this->params['lat']);
        $info = $db->alias('s')->join($join)->field($field)->where($map)->find();
        result($info);
    }
}