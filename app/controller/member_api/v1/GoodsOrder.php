<?php

namespace app\controller\member_api\v1;


use app\model\GoodsOrder as GoodsOrderModel;
use app\common\service\UrlService;
use Exception;


class GoodsOrder extends BasicMemberApi
{
    /**
     *取消订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function close()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $order_guid  = $params['order_guid'];
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
            ['status', '=', -1],
        ];
        $update_data = [
            'status'      => -2,
            'cancel_time' => format_timestamp()
        ];
        $db          = new GoodsOrderModel();
        $db::update($update_data, $map);
        success('取消成功');
    }

    /**
     *确认收货
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function confirm()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $order_guid = $params['order_guid'];
        $db         = new GoodsOrderModel();
        $db->confirm_order($bid, $order_guid);
        success('确认收货成功');
    }

    /**
     *提交订单(统一方法)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $db_goods_order = new GoodsOrderModel();
        $order_data     = $db_goods_order->submit($this->params);
        result($order_data, '兑换成功');
    }

    /**
     *订单预览
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_preview()
    {
        $db_goods_order      = new GoodsOrderModel();
        $submit_preview_data = $db_goods_order->submit_preview($this->params);
        result($submit_preview_data);
    }

    /**
     *详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params                  = $this->params;
        $bid                     = $this->get_bid();
        $order_guid              = $params['guid'];
        $db_goods_order          = new GoodsOrderModel();
        $order_detail            = $db_goods_order->get_order_detail($bid, $order_guid);
        $config                  = get_config_by_bid($bid);
        $choose_goods_show_price = $config['choose_goods_show_price'];
        if ($choose_goods_show_price == 0 && isset($order_detail['goods_info'])) {
            $goods_info = $order_detail['goods_info'];
            foreach ($goods_info as $key => $val) {
                $goods_info[$key]['price'] = 0;
            }
            $order_detail['goods_info'] = $goods_info;
        }
        result($order_detail);
    }

    public function apply_refund()
    {
        $params                           = $this->params;
        $bid                              = $this->get_bid();
        $order_guid                       = $params['order_guid'];
        $db_goods_order                   = new GoodsOrderModel();
        $map                              = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $order_info                       = $db_goods_order->where($map)->findOrFail();
        $order_status                     = $order_info['status'];
        $is_enable_refund_by_order_status = $db_goods_order->is_enable_refund_by_order_status($bid, $order_status);
        if (!$is_enable_refund_by_order_status) {
            error('当前订单不允许退款!');
        }
        if (in_array($order_status, [1, 2])) {
            error('当前订单已发货,如需退单请联系客服!');
        }
        if ($order_status == 0) {
            //待发货状态
            $db_goods_order->refund($params);
            success('退单成功!');
        } else {
            error('暂不支持的订单状态!');
        }
    }

    public function get_pickup_qrcode_url()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $member_guid  = $this->get_member_guid();
        $guid         = $params['guid'];
        $pick_up_code = $params['pick_up_code'] ?? '';
        if (empty($pick_up_code)) {
            error('获取核销码失败');
        }
        $map            = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
        ];
        $db_goods_order = new GoodsOrderModel();
        $qrcode_url     = UrlService::text_to_qrcode_url($pick_up_code);
        $data           = ['qrcode_url' => $qrcode_url];
        result($data);
    }

    public function all()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $member_guid    = $this->get_member_guid();
        $db_goods_order = new GoodsOrderModel();
        $join           = [
            ['coupon c', 'go.bid = c.bid AND go.coupon_guid= c.guid', 'LEFT'],
            ['coupon_send_note csn', 'go.bid = csn.bid AND go.coupon_send_note_guid= csn.guid', 'LEFT'],
            ['area a1', 'go.province_id = a1.id', 'LEFT'],
            ['area a2', 'go.city_id = a2.id', 'LEFT'],
            ['area a3', 'go.area_id = a3.id', 'LEFT'],
        ];
        $map            = [
            ['go.bid', '=', $bid],
            ['go.status', '<>', -1], //不展示待支付订单
            ['go.member_guid', '=', $member_guid],
            ['go.delete_time', 'null', null],
        ];
        if (isset($params['status']) && in_array($params['status'], [0, 1])) {
            $map[] = ['go.status', '=', $params['status']];
        }
        $field      = [
            'c.name'                                     => 'coupon_name',
            'go.*',
            'csn.code',
            "CONCAT(a1.name,a2.name,a3.name,go.address)" => 'address_info',
        ];
        $order      = ['go.create_time' => 'DESC', 'id' => 'DESC'];
        $order_list = $db_goods_order->alias('go')->join($join)->field($field)->where($map)->order($order)->select();
        result($order_list);
    }

    public function list()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $member_guid    = $this->get_member_guid();
        $map            = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null],
            ['member_guid', '=', $member_guid],
        ];
        $db_goods_order = new GoodsOrderModel();
        //查询订单基本信息
        $field       = [
            'bid',
            'guid',
            'type',
            'mobile',
            'bill_number',
            'total_amount',
            'total_money',
            'status',
            'pick_up_code',
            'goods_info',
            'express_code',
            'express_no',
            'create_time',
            'address'
        ];
        $this->model = $db_goods_order->field($field)->where($map)->order(['create_time' => 'DESC']);
        if (!isset($params['append_order_detail'])) {
            $this->model = $this->model->append(['order_detail']);
        } else {
            unset($this->params['append_order_detail']);
        }
        $list = $this->_list();
        foreach ($list['data'] as $key => $val) {
            $list['data'][$key]['query_route_url'] = $db_goods_order->get_express_query_url($val);
            $list['data'][$key]['goods_info']      = $db_goods_order->build_goods_info_pic($list['data'][$key]['goods_info']);
        }
        //  app.to_web('https://' + extConfig.domain + '/member/tools/kuaidi?bid=' + extConfig.bid + '&coname=www&nu=' + express_no);
        result($list);
    }

    /**
     *支付下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function pay()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $db_goods_order = new GoodsOrderModel();
        $order_data     = $db_goods_order->pay($this->params);
        result($order_data);
    }
}