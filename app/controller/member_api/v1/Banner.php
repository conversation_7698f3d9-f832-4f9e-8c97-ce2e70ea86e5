<?php


namespace app\controller\member_api\v1;


class Banner extends BasicMemberApi
{
    public function list()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $db_banner   = new \app\model\Banner();
        $type        = $params['type'] ?? 1;
        $map         = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['type', '=', $type]
        ];
        $field       = [
            'img_url',
            'path',
            'title'
        ];
        $banner_list = $db_banner->field($field)->order(['sort' => 'ASC'])->where($map)->select();
        result(['banner_list' => $banner_list]);
    }

}