<?php

declare(strict_types=1);

namespace app\controller\member_api\v1;

use app\common\service\UrlService;
use app\model\ShareActivityReachRewardItem;
use app\model\ShareActivityRewardNote;
use app\model\ShareActivityShareNote;
use app\model\YkyMember;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;
use Endroid\QrCode\Writer\PngWriter;
use Intervention\Image\AbstractFont;
use Intervention\Image\ImageManagerStatic as Image;
use think\facade\Db;

class ShareActivity extends BasicMemberApi
{
    protected function get_yky_openid()
    {
        $bid        = $this->get_bid();
        $key        = $bid . '_yky_openid_vale';
        $yky_openid = cookies($key);
        if (empty($yky_openid)) {
            if (is_host()) {
                $yky_openid = $this->get_openid();
            } else {
                error('openid获取失败!');
            }
        }
        return $yky_openid;
    }

    public function get_config()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        result($config);
    }

    public function share_activity_detail()
    {
        $bid               = $this->get_bid();
        $config            = get_config_by_bid($bid);
        $params            = $this->params;
        $guid              = $params['guid'];
        $share_openid      = $params['share_openid'] ?? '';
        $openid            = $this->get_yky_openid();
        $db_share_activity = new \app\model\ShareActivity();
        $map               = [
            ['bid', '=', $bid],
            ['guid', '=', $guid]
        ];
        $activity_detail   = $db_share_activity->where($map)->findOrEmpty();
        if ($activity_detail->isEmpty()) {
            error('活动不存在' . json_encode($this->params, JSON_UNESCAPED_UNICODE));
        }
        $reach_reward_require_num = $activity_detail['reach_reward_require_num'];

        $db_yky_member                = new YkyMember();
        $register_url                 = $db_yky_member->get_member_card_url($bid, $activity_detail['we_com_live_code_url'], $activity_detail['yky_chain_store_guid'], $share_openid);
        $db_share_activity_share_note = new ShareActivityShareNote();
        $map                          = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $guid],
            ['share_openid', '=', $openid]
        ];
        $field                        = [
            Db::raw("IFNULL(CONCAT(LEFT(openid, 3), '****', RIGHT(openid, 4)), '') as openid"),
            'share_time'
        ];
        $share_note                   = $db_share_activity_share_note->field($field)->where($map)->order(['create_time' => 'desc'])->select()->toArray();
        $share_num                    = count($share_note);

        $map              = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $guid],
        ];
        $field            = [
            Db::raw("IFNULL(CONCAT(LEFT(share_openid, 3), '****', RIGHT(share_openid, 4)), '') as share_openid"),
            'COUNT(1) as share_num',
        ];
        $rank_list        = $db_share_activity_share_note->field($field)->where($map)->group(['share_openid'])->order(['share_num' => 'desc'])->select()->toArray();
        $processed_openid = $openid ? substr($openid, 0, 3) . '****' . substr($openid, -4) : '';
        $rank             = array_search($processed_openid, array_column($rank_list, 'share_openid'));
        $rank             = $rank !== false ? $rank + 1 : 0;

        $db_share_activity_reward_note = new ShareActivityRewardNote();
        $map                           = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $guid],
            ['openid', '=', $openid],
            ['type', '=', 1],
        ];
        $join_reward_status            = $db_share_activity_reward_note->where($map)->count();
        $map                           = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $guid],
            ['openid', '=', $openid],
            ['type', '=', 2],
        ];
        $reach_reward_status           = $db_share_activity_reward_note->where($map)->count();


        $yky_member = \xieyongfa\yky\Yky::Member($config);

        $member_info   = $yky_member->Get_MemberInfo($openid);
        $is_yky_member = !empty($member_info['data']);

        $member_info = $is_yky_member ? current($member_info['data']) : [];
        if (isset($member_info['CardId'])) {
            $member_info['CardId'] = tools()::replace_to_star($member_info['CardId']);
        }
        //https://6669.m.yunhuiyuan.cn/Member/Card/?bid=0b1dd780-592f-410c-bc81-7e98b16591e1
        $member_card_url = 'https://' . $config['yunhuiyuan_username'] . '.m.yunhuiyuan.cn/Member/Card/?bid=' . $config['yunhuiyuan_bid'];

        $db_share_activity_reach_reward_item = new ShareActivityReachRewardItem();
        $map                                 = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $guid],
        ];
        $reach_reward_item                   = $db_share_activity_reach_reward_item->where($map)->order(['level' => 'ASC'])->select()->toArray();
        // 遍历达标奖励项，添加奖励状态
        foreach ($reach_reward_item as $key => $val) {
            $map                                      = [
                ['bid', '=', $bid],
                ['share_activity_guid', '=', $guid],
                ['openid', '=', $openid],
                ['type', '=', 2], // 2表示达标奖励
                ['level', '=', $val['level']],
                ['rule_guid', '=', $val['guid']]
            ];
            $reach_reward_item[$key]['reward_status'] = $db_share_activity_reward_note->where($map)->count() > 0;
        }

        result([
            'percent'             => !$reach_reward_require_num ? 0 : ceil($share_num / $reach_reward_require_num * 100),
            'share_num'           => $share_num,
            'share_note'          => $share_note,
            'rank_list'           => $rank_list,
            'activity_detail'     => $activity_detail,
            'join_reward_status'  => $join_reward_status,
            'reach_reward_status' => $reach_reward_status,
            'register_url'        => $register_url,
            'share_openid'        => $openid,
            'member_info'         => $member_info,
            'is_member'           => $is_yky_member,
            'rank'                => $rank,
            'member_card_url'     => $member_card_url,
            'logo'                => $config['logo'],
            'reach_reward_item'   => $reach_reward_item,
        ]);
    }

    public function add_share_note()
    {
        $bid                          = $this->get_bid();
        $params                       = $this->params;
        $db_share_activity_share_note = new ShareActivityShareNote();
        $share_activity_guid          = $params['share_activity_guid'];
        $share_openid                 = $params['share_openid'] ?? '';
        $share_member_guid            = $params['share_member_guid'] ?? null;
        $share_yky_member_guid        = $params['share_yky_member_guid'] ?? null;
        $yky_member_guid              = $params['yky_member_guid'] ?? null;
        $openid                       = $params['openid'] ?? null;
        if (empty($openid)) {
            error('openid不能为空');
        }
        if ($openid != $this->get_yky_openid()) {
            error('openid校验不通过:openid=' . $openid . ';get_openid=' . $this->get_openid());
        }
        $now_time                      = format_timestamp();
        $data                          = [
            'guid'                  => create_guid(), // 假设存在 create_guid 函数生成唯一标识
            'bid'                   => $bid,
            'share_activity_guid'   => $share_activity_guid,
            'share_yky_member_guid' => $share_yky_member_guid,
            'share_member_guid'     => $share_member_guid,
            'share_openid'          => $share_openid,
            'last_visit_time'       => $now_time,
            'share_time'            => $now_time,
            'yky_member_guid'       => $yky_member_guid,
            'member_guid'           => $this->get_member_guid(),
            'openid'                => $openid,
        ];
        $map_share_activity_share_note = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $share_activity_guid],
            ['share_openid', '=', $share_openid],
            ['openid', '=', $openid],
        ];
        $count                         = $db_share_activity_share_note->where($map_share_activity_share_note)->count();

        if ($count > 0) {
            $update_data = ['last_visit_time' => $now_time];
            $result      = $db_share_activity_share_note::update($update_data, $map_share_activity_share_note);
        } else {
            $result = $db_share_activity_share_note->save($data);
        }
        $map_share_activity_share_note = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $share_activity_guid],
            ['share_openid', '=', $share_openid],
        ];
        $count                         = $db_share_activity_share_note->where($map_share_activity_share_note)->count();
        wr_log('$count=' . $count . '----$map_share_activity_share_note=' . json_encode($map_share_activity_share_note, JSON_UNESCAPED_UNICODE));
        //      JS代码  redirect('/member/share_activity/index?bid=' + bid + '&guid=' + share_activity_guid + '&share_openid=' + share_openid + '&source=1');
        $url = $this->generate_share_activity_url($bid, $share_activity_guid, $share_openid, 3, false);

        $db_share_activity  = new \app\model\ShareActivity();
        $map_share_activity = [
            ['bid', '=', $bid],
            ['guid', '=', $share_activity_guid],
        ];
        $activity_detail    = $db_share_activity->where($map_share_activity)->findOrEmpty();
        if ($activity_detail->isEmpty()) {
            error('当前活动不存在:' . json_encode($this->params, JSON_UNESCAPED_UNICODE));
        }
        $reach_reward_require_num = $activity_detail['reach_reward_require_num']; //参与奖励

        $db_yky_member                 = new YkyMember();
        $get_member_card_url           = $db_yky_member->get_member_card_url($bid, $activity_detail['we_com_live_code_url'], $activity_detail['yky_chain_store_guid'], $share_openid);
        $db_share_activity_reward_note = new ShareActivityRewardNote();

        $join_reward_yky_coupon_guid = $activity_detail['join_reward_yky_coupon_guid']; //参与奖励
        $join_reward_yky_point       = $activity_detail['join_reward_yky_point']; //参与奖励
        $join_reward_yky_value       = $activity_detail['join_reward_yky_value']; //参与奖励储值
        //需要发放奖励, 先判断是否是一卡易会员
        $config          = get_config_by_bid($bid);
        $yky_member      = \xieyongfa\yky\Yky::Member($config);
        $member_info     = $yky_member->Get_MemberInfo($openid);
        $is_yky_member   = !empty($member_info['data']);
        $yky_member_info = $is_yky_member ? current($member_info['data']) : [];

        if ($join_reward_yky_coupon_guid || $join_reward_yky_point > 0 || $join_reward_yky_value > 0) {
            if (!$is_yky_member) {
                $url = $get_member_card_url;
            } else {
                $member_info                   = current($member_info['data']);
                $card_id                       = $member_info['CardId'];
                $db_share_activity_reward_note = new ShareActivityRewardNote();
                $reward_data                   = [
                    'share_activity_guid' => $share_activity_guid,
                    'bid'                 => $bid,
                    'openid'              => $openid,
                    'card_id'             => $card_id,
                    'type'                => 1,
                ];
                $db_share_activity_reward_note->reward($reward_data);
                $url = $this->generate_share_activity_url($bid, $share_activity_guid, $share_openid, 4, false);
            }
        }
//        //查看是否达标
//        if ($count + 1 >= $reach_reward_require_num) {
//            //发送达标奖励
//            if (!$is_yky_member) {
//                $url = $get_member_card_url;
//            } else {
//                if (empty($member_info['data'])) {
//                    wr_log('$member_info为空', 1);
//                    error('会员信息不存在');
//                }
//                $member_info = current($member_info['data']);
//                $card_id     = $member_info['CardId'];
//                $reward_data = [
//                    'share_activity_guid' => $share_activity_guid,
//                    'bid'                 => $bid,
//                    'openid'              => $openid,
//                    'card_id'             => $card_id,
//                    'type'                => 2,
//                ];
//                $db_share_activity_reward_note->reward($reward_data);
//            }
//        }
        // 检查是否有达标奖励明细，如果有则进行奖励发放
        $db_share_activity_reach_reward_item = new ShareActivityReachRewardItem();
        $map                                 = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $share_activity_guid],
            ['delete_time', '=', null],
        ];
        $reach_reward_item                   = $db_share_activity_reach_reward_item->where($map)->select()->toArray();
        if (!empty($reach_reward_item)) {
            foreach ($reach_reward_item as $item) {
                // 检查是否已经发放过该等级的奖励
                $existing_reward = $db_share_activity_reward_note->where([
                    ['share_activity_guid', '=', $share_activity_guid],
                    ['bid', '=', $bid],
                    ['openid', '=', $share_openid],
                    ['type', '=', 2], // 达标奖励明细类型
                    ['level', '=', $item['level']], // 对应等级
                ])->count();

                if (!$existing_reward) {
                    // 检查是否达到该等级要求
                    if ($count >= $item['require_num']) {
                        wr_log('$count=' . $count . json_encode($item, JSON_UNESCAPED_UNICODE));
                        // 检查库存是否充足
                        if ($item['award_stock'] > 0) {
                            if (!$is_yky_member) {
                                $url = $get_member_card_url;
                            } else {
                                $card_id     = $yky_member_info['CardId'];
                                $reward_data = [
                                    'share_activity_guid' => $share_activity_guid,
                                    'bid'                 => $bid,
                                    'openid'              => $share_openid,
                                    'card_id'             => $card_id,
                                    'type'                => 2, // 达标奖励明细类型
                                    'level'               => $item['level'], // 等级
                                    'rule_guid'           => $item['guid'], // 奖励GUID
                                ];
                                $db_share_activity_reach_reward_item->reward($reward_data);
                            }
                        } else {
                            wr_log('达标了但是库存不足' . $share_openid, 1);
                        }
                    }
                }
            }
        }
        result(['url' => $url], '分享成功');
    }

    /**
     * 生成分享活动URL
     * @param string $bid 商家ID
     * @param string $share_activity_guid 活动GUID
     * @param string|null $share_openid 分享者OPENID
     * @param int $source 来源 2 海报 3 上报
     * @param bool $to_short 是否转短链
     * @return string
     */
    private function generate_share_activity_url($bid, $share_activity_guid, $share_openid = null, $source = 1, $to_short = true)
    {
        $url = (string)url('/member/share_activity/index', [
            'bid'          => $bid,
            'guid'         => $share_activity_guid,
            'share_openid' => $share_openid,
            'source'       => $source
        ], false, true);
        return $to_short ? UrlService::get_full_url(UrlService::long_to_short($url)) : $url;
    }

    public function get_share_image()
    {
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $share_activity_guid = $params['share_activity_guid'];
        $openid              = $this->get_yky_openid();
        $db                  = new \app\model\ShareActivity();
        $map                 = [
            ['bid', '=', $bid],
            ['guid', '=', $share_activity_guid]
        ];
        $activity_detail     = $db->where($map)->findOrEmpty();
        if ($activity_detail->isEmpty()) {
            error('您访问的活动不存在:' . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        $background_image_url = $activity_detail['background_image_url'];
        if (empty($background_image_url)) {
            error('未设置背景图片');
        }

        // 获取会员信息
        $config      = get_config_by_bid($bid);
        $yky_member  = \xieyongfa\yky\Yky::Member($config);
        $member_info = $yky_member->Get_MemberInfo($openid);
        $member_name = '';
        if (!empty($member_info['data'])) {
            $member_info = current($member_info['data']);
            $member_name = $member_info['TrueName'] ?? '';
        }

        $share_qrcode_position_info = $activity_detail['share_qrcode_position_info'];
        //{"x":313,"y":668,"width":449,"height":449,"rotate":0}
        $x     = 313;
        $y     = 668;
        $width = 450;
        if ($share_qrcode_position_info) {
            $share_qrcode_position_info = json_decode($share_qrcode_position_info, true);
            $x                          = (int)$share_qrcode_position_info['x'];
            $y                          = (int)$share_qrcode_position_info['y'];
            $width                      = (int)$share_qrcode_position_info['width'];
        }

        $url           = $this->generate_share_activity_url($bid, $share_activity_guid, $openid, 2);
        $ext           = 'jpg';
        $v             = 4;
        $file_path     = '/temp/images/' . md5($v . $url . $background_image_url . $activity_detail['share_qrcode_position_info'] . $member_name) . '.' . $ext;
        $absolute_path = tools()::get_absolute_path($file_path);
        $from          = 'cache';
        if (!file_exists($absolute_path)) {
            $from = 'api';
            // 生成二维码
            $writer = new PngWriter();
            $qrCode = QrCode::create($url)
                ->setEncoding(new Encoding('UTF-8'))
                ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh())
                ->setSize($width)
                ->setMargin(10)
                ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
                ->setForegroundColor(new Color(0, 0, 0))
                ->setBackgroundColor(new Color(255, 255, 255));

            $result = $writer->write($qrCode);

            // 配置图像驱动，优先使用imagick
            $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
            Image::configure(['driver' => $driver_name]);

            // 打开背景图片
            //$background_image_local = tools()::web_url_to_local_absolute_path($background_image_url);
            $image = Image::make($background_image_url);

            // 直接从内存创建二维码图片
            $qrCodeImage = Image::make($result->getString());

            // 将二维码合成到背景图上
            $image->insert($qrCodeImage, 'top-left', $x, $y);

            // 如果有会员姓名，添加到左上角
            if (!empty($member_name)) {
                $font = tools()::get_absolute_path('/static/css/fonts/MSYH.TTC');
                $image->text($member_name, 10, 10, function (AbstractFont $font_draw) use ($font) {
                    $font_draw->file($font);
                    $font_draw->size(24);
                    $font_draw->color('#FFFFFF');
                    $font_draw->align('left');
                    $font_draw->valign('top');
                    $font_draw->angle(0);
                });
            }
            $image->save($absolute_path);
        }
        result([
            'from'            => $from,
            'share_image_url' => tools()::path_to_web($file_path)
        ]);
    }
}
