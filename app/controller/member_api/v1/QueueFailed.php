<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/26
 * Time: 20:31
 */

namespace app\controller\member_api\v1;

use app\model\JobsFailed;
use Exception;

class QueueFailed extends BasicMemberApi
{
    protected array $middleware = [
        //\app\middleware\CropWeixinAuth::class => ['except' => ['initialize']],
        //'check'                               => ['only' => ['hello']],
    ];

    /**
     *队列列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db    = new JobsFailed();
        $field = [
            'connection',
            "JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job'))"                                      => 'job_name',
            "substring_index(JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job')),'controller\\\\',-1)" => 'simple_job_name',
            "count(1)"                                                                         => 'count',
        ];
        $list  = $db->field($field)->group('connection,job_name')->order(['queue' => 'ASC', 'count' => 'DESC'])->select();
        result($list);
    }

    /**
     *更改队列状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_status()
    {
        $db              = new JobsFailed();
        $params          = $this->params;
        $simple_job_name = $params['simple_job_name'];
        $queue           = $params['queue'];
        $action          = $params['action_event'];
        $action_name     = $params['action_name'];
        $result          = $db->$action($simple_job_name);
        if ($result) {
            success('成功' . $action_name . '了' . $result . '条任务');
        } else {
            error('没有要' . $action_name . '的条任务');
        }
    }

    /**
     *批量重启或删除队列任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function batch_change_status()
    {
        $db     = new JobsFailed();
        $params = $this->params;
        $queue  = $params['queue'];
        $action = $params['action'];
        foreach ($queue as $job) {
            $simple_job_name = $job['simple_job_name'];
            $db->$action($simple_job_name);
        }
        success('操作成功');
    }
}