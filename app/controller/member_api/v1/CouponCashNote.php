<?php


namespace app\controller\member_api\v1;


use app\common\exceptions\NotNotifyException;
use app\model\Coupon;
use app\model\CouponSendNote;
use app\common\service\NotifyService;
use Exception;

class CouponCashNote extends BasicMemberApi
{
    public function submit_preview()

    {
        $bid        = $this->get_bid();
        $params     = $this->params;
        $token      = $params['token'];
        $token_info = cache($token);
        if (empty($token_info)) {
            throw new NotNotifyException('token校验失败');
        }
        $coupon_send_note_guid = $token_info['coupon_send_note_guid'];
        $coupon_guid           = $token_info['coupon_guid'];
        $token_info_bid        = $token_info['bid'];
        $db_coupon             = new Coupon();
        $map_coupon            = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info           = $db_coupon->where($map_coupon)->findOrFail();
        $cash_money            = $coupon_info['cash_money'];
        if ($cash_money <= 0) {
            error('当前卡券不支持提现');
        }
        result($coupon_info);
    }

    public function submit()
    {
        $bid               = $this->get_bid();
        $member_guid       = (string)$this->get_member_guid();
        $openid            = (string)$this->get_openid();
        $appid             = (string)$this->get_appid();
        $params            = $this->params;
        $remark_image_list = $params['remark_image_list'] ?? null;
        $alipay_true_name  = $params['alipay_true_name'];
        $alipay_account    = $params['alipay_account'];
        if (!tools()::is_mobile($alipay_account) && !tools()::is_email($alipay_account)) {
            error('支付宝账号需为邮箱或者手机号');
        }
        $token      = $params['token'];
        $token_info = cache($token);
        if (empty($token_info)) {
            throw new NotNotifyException('token校验失败');
        }
        $coupon_send_note_guid = $token_info['coupon_send_note_guid'];
        $coupon_guid           = $token_info['coupon_guid'];
        $token_info_bid        = $token_info['bid'];
        $db_coupon_send_note   = new CouponSendNote();
        $coupon_send_note      = $db_coupon_send_note->verify($bid, ['coupon_send_note_guid' => $coupon_send_note_guid]);
        if ($coupon_send_note === false) {
            error($db_coupon_send_note->getError());
        }
        //开始检测
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info = $db_coupon->where($map_coupon)->findOrFail();
        $cash_money  = $coupon_info['cash_money'];
        if ($cash_money <= 0) {
            error('当前卡券不支持提现');
        }
        $coupon_cash_note_guid = create_guid();
        $bill_number           = tools()::get_bill_number();
        //开始核销卡券
        $data        = [
            'bid'                   => $bid,
            'coupon_send_note_guid' => $coupon_send_note_guid,
            'member_guid'           => $member_guid,
            'relation_guid'         => $coupon_cash_note_guid,
            'used_num'              => 1,
            'used_value'            => 0,
            'way'                   => 5,//提现
        ];
        $used_result = $db_coupon_send_note->use_coupon($data);
        if (!$used_result) {
            throw new Exception($db_coupon_send_note->getError());
        }
        //核销成功开始写提现记录
        $db_coupon_cash_note = new \app\model\CouponCashNote();
        $order_data          = [
            'guid'              => $coupon_cash_note_guid,
            'bid'               => $bid,
            'member_guid'       => $member_guid,
            'appid'             => $appid,
            'openid'            => $openid,
            'coupon_send_guid'  => $coupon_send_note_guid,
            'ip'                => tools()::get_client_ip(),
            'bill_number'       => $bill_number,
            'cash_money'        => $cash_money,
            'alipay_true_name'  => $alipay_true_name,
            'alipay_account'    => $alipay_account,
            'remark_image_list' => $remark_image_list,
            'status'            => 0,//待审核
            'pay_type'          => 'alipay',
        ];
        $db_coupon_cash_note->save($order_data);

        //发送通知
        $data = [
            'url'         => '',
            'title'       => '',
            'name'        => '【新卡券提现申请】', //流程名称
            'create_time' => format_timestamp(),  //操作时间
            'user'        => $alipay_account . '(' . $alipay_true_name . ')', //操作人员
            'detail'      => '提现金额:' . $cash_money, //流程摘要
            'remark'      => '',
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_business()->set_data($data)->set_bid($bid)->send();
        result(['order_guid' => $coupon_cash_note_guid, 'bill_number' => $bill_number]);
    }

    public function order_list()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $member_guid         = $this->get_member_guid();
        $map                 = [
            ['ccn.bid', '=', $bid],
            ['ccn.member_guid', '=', $member_guid],
        ];
        $join                = [
            ['coupon_send_note csn', 'ccn.coupon_send_guid = csn.guid AND ccn.bid = csn.bid'],
        ];
        $field               = [
            'ccn.*',
            'csn.code',
        ];
        $db_coupon_cash_note = new \app\model\CouponCashNote();
        $this->model         = $db_coupon_cash_note->alias('ccn')->join($join)->order(['ccn.create_time' => 'DESC'])->field($field)->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}