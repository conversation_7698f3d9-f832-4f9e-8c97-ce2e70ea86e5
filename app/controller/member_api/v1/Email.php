<?php

namespace app\controller\member_api\v1;


use app\common\tools\Excel;
use DateInterval;
use DateTimeImmutable;
use Ddeboer\Imap\Search\Date\Before;
use Ddeboer\Imap\Search\Date\Since;
use Ddeboer\Imap\SearchExpression;
use Ddeboer\Imap\Server;
use Exception;

class Email extends BasicMemberApi
{
    /**
     * 下载附件
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function download()
    {
        ignore_user_abort(); // run script in background
        set_time_limit(0); // run script forever
        $params   = $this->params;
        $username = $params['username'];
        $password = $params['password'];
        wr_log('正在收取邮件:' . $username . '-' . $password, 1);
        $server = new Server($params['server_name']);
        try {
            $connection = $server->authenticate($username, $password);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        $cache_key = $username . ':' . __FUNCTION__;
        if (cache($cache_key)) {
            error('10分钟内只允许操作1次,请稍后重试');
        }
        $mailbox = $connection->getMailbox('INBOX');
        $search  = new SearchExpression();
        $today   = new DateTimeImmutable();
        $day     = (int)$params['day'];
        $date    = date("Y-m-d", strtotime("-$day day"));
        $search->addCondition(new Since($today->sub(new DateInterval('P' . $day . 'D'))));
        if ($day > 0) {
            $search->addCondition(new Before($today->sub(new DateInterval('P' . ($day - 1) . 'D'))));
        }
        $messages           = $mailbox->getMessages($search);
        $path               = '/temp/' . date('Ymd') . '/';
        $absolute_path      = tools()::get_absolute_path($path);
        $all_array          = [];
        $email_count        = 0;
        $row_count          = 0;
        $email_title_prefix = '[邮件汇总小工具]';
        $allowField         = explode(',', $params['column_name']);
        foreach ($messages as $message) {
            $subject = $message->getSubject();
            //不包含关键词或者是系统邮件 则忽略本邮件
            if (false === strpos($subject, $params['keyword']) || false !== strpos($subject, $email_title_prefix)) {
                continue;
            }
            $email_count++;
            $message_id = $message->getNumber();
            if ($message->hasAttachments()) {
                $attachments = $message->getAttachments();
                foreach ($attachments as $att) {
                    $file_name = 'email_' . $message_id . '_' . $att->getFilename();
                    if (false !== strpos($att->getFilename(), $params['ext'])) {
                        $content = $att->getDecodedContent();
                        tools()::download($absolute_path, $file_name, $content);
                        $file  = new Excel();
                        $array = $file->load($path . $file_name)->excelToArray($allowField, (int)$params['sheet_index']);
                        foreach ($array as $key => $val) {
                            $push_data = [];
                            $is_number = true;
                            foreach ($allowField as $k => $v) {
                                if (!is_numeric($val[$k])) {
                                    //暂时只支持提取纯数字的列
                                    $is_number = false;
                                }
                                $push_data[$k] = $val[$k];
                            }
                            if ($is_number) {
                                $row_count++;
                                $all_array[] = $push_data;
                            }
                        }
                    }
                }
            }
            wr_log($subject . '|邮件提取完毕');
        }
        wr_log('收取邮件完毕:' . $username . '-' . $password, 1);
        if ($email_count > 0) {
            if ($row_count > 0) {
                $excel = new Excel();
                $path  = tools()::get_absolute_path('/temp/' . date('Ymd') . '/');
                !is_dir($path) && mkdir($path, 0755, true);
                $save_name   = $path . create_guid() . '.xlsx';
                $file_name   = $excel->arrayToExcel($allowField, $all_array, $save_name, null, true);
                $email_data  = [
                    'account' => 'xyf',
                    'send_to' => $username,
                    'bcc'     => get_system_config('bcc_email'),
                    'title'   => $email_title_prefix . $date . '-' . $params['keyword'] . '-' . $params['ext'],
                    'subject' => '成功收取' . $username . '的' . $email_count . '封邮件,提取' . $row_count . '条有效数据,具体请查看附件',
                    'attach'  => $file_name . '|' . $email_title_prefix . $params['keyword'] . '-' . $date . '.xlsx',
                ];
                $send_result = send_email($email_data);
            }
            cache($cache_key, format_timestamp(), 600);
            success('成功收取' . $email_count . '封邮件,提取' . $row_count . '条有效数据,数据已发送至您的邮箱!');
        } else {
            error($date . '没有要收取的邮件');
        }
    }
}