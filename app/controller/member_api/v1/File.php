<?php

namespace app\controller\member_api\v1;

use Exception;
use Storage\Storage;
use think\Response;
use think\response\Json;
use Throwable;

class File extends BasicMemberApi
{
    /**
     *base64上传
     * @access public
     * @return Response
     * @throws Exception
     */
    public function upload_base64()
    {
        $params = $this->params;
        $this->request->__set('_base64', trim($params['editor']));
        $upload      = Storage::local();
        $file        = $upload->upload();
        $file['url'] = tools()::replace_readonly_to_www($file['url']);
        return response('<img src="' . $file['url'] . '" alt="" />')->contentType('text/html');
    }

    /**
     *上传
     * @access public
     * @return void
     * @throws Exception
     */
    public function upload()
    {
        //$storage_type = !empty($this->params['storage']) ? $this->params['storage'] : $config['storage_type'];
        $data = [];
        try {
            $upload = Storage::driver($this->get_storage_type());
            $file   = $upload->upload();
            $data   = ['filePath' => tools()::replace_readonly_to_www($file['url'])];
        } catch (Exception|Throwable $e) {
            error($e->getMessage());
        }
        result($data);
    }

    /**
     *获取驱动
     * @access protected
     * @return string
     * @throws Exception
     */
    protected function get_storage_type()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        return $config['storage_type'];
    }

    /**
     *移动端上传
     * @access public
     * @return Json
     * @throws Exception
     */
    public function uploadifive()
    {
        try {
            $upload      = Storage::driver($this->get_storage_type());
            $file        = $upload->upload();
            $file['url'] = tools()::replace_readonly_to_www($file['url']);
            $msg         = [
                'error'   => 0,
                'message' => 'success',
                'url'     => $file['url'],
            ];
        } catch (Exception|Throwable $e) {
            $msg = [
                'error'   => -1,
                'message' => '上传失败:' . $e->getMessage()
            ];
        }
        return json($msg);
    }

    public function upload_wechat()
    {
        $bid                  = $this->get_bid();
        $params               = $this->params;
        $media_id             = $params['media_id'];
        $config               = get_config_by_bid($bid);
        $appid                = $config['appid'];
        $media_instance       = weixin($appid)::WeChatMedia();
        $base64_encode_result = $media_instance->get($media_id);
        $this->request->__set('_stream', $base64_encode_result);
        return $this->upload();
    }
}