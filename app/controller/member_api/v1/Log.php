<?php

namespace app\controller\member_api\v1;

use Exception;

class Log extends BasicMemberApi
{
    /**
     *日志列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', tools()::get_empty_guid()]
        ];
        $this->model = $this->model->where($map)->order(['id' => 'DESC']);
        result($this->_list());
    }

    protected function initialize()
    {
        $this->model = new \app\model\Log();
        parent::initialize();
    }
}