<?php

namespace app\controller\member_api\v1;

use app\model\Business as BusinessModel;
use app\model\YkyUser;
use Exception;

class Upay extends BasicMemberApi
{

    public function get_config()
    {
        $bid           = $this->get_bid();
        $config        = get_config_by_bid($bid);
        $yky           = new \OpenApi\Yky($config);
        $result        = $yky->get_member_info_by_openid($this->get_openid());
        $db_business   = new  BusinessModel();
        $map           = [['guid', '=', $bid]];
        $business_info = $db_business->where($map)->find();
        unset($business_info['id']);
        unset($business_info['appid']);
        unset($business_info['secret']);
        if ($result === false) {
            error($yky->message);
        }
        $params      = $this->params;
        $store_guid  = $params['store_guid'];
        $db_yky_user = new YkyUser();
        $map         = [
            ['bid', '=', $bid],
            ['chain_store_guid', '=', $store_guid]
        ];
        $store_name  = $db_yky_user->where($map)->value('store_name');
        $data        = [
            'member_info'   => $result,
            'business_info' => $business_info,
            'store_info'    => [
                'store_name' => $store_name
            ],
            'parameter'     => [
                'service_charge_rate' => 0.1,
            ]
        ];
        result($data);
    }

    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function create_order()
    {
        $params       = $this->params;
        $out_trade_no = tools()::get_bill_number();
        $attach       = null;
        $job_attach   = null;
        $bid          = $this->get_bid();
        $total_fee    = $params['total_fee'] ?? 0; //储值支付金额
        if ($total_fee < 0.1) {
            error('最低支付金额0.1元起');
        }
        $value_balance = $params['value_balance'];
        if (empty($value_balance)) {
            error('仅允许会员买单');
        }
        if ($total_fee > $value_balance) {
            error('会员可用余额不足,最多可支付:' . $value_balance . '元!');
        }
        //$service_charge = $params['service_charge'] ?? 0;//手续费金额
        $service_charge = tools()::nc_price_calculate($total_fee, '*', 0.1, 2);//手续费金额为支付金额的10%
        $total_paid     = tools()::nc_price_calculate($total_fee, '+', $service_charge, 2);//手续费金额为支付金额的10%
        if ($service_charge <= 0) {
            error('手续费金额有误!');
        }
        $store_guid          = $params['chain_store_guid'] ?? '';
        $user_account        = $params['user_account'] ?? '';
        $memo                = $params['memo'] ?? '';
        $config              = get_config_by_bid($bid);
        $yunhuiyuan_username = $config['yunhuiyuan_username'];
        $yunhuiyuan_bid      = $config['yunhuiyuan_bid'];
        $url                 = 'https://' . $yunhuiyuan_username . '.m.yunhuiyuan.cn/UPay/Submit?bid=' . $yunhuiyuan_bid;
        $data                = [
            'totalCash'              => $total_paid,//支付总额
            //           'money'                  => $total_fee + $service_charge,//支付总额
            'subductionCash'         => 0,
            'storeGuid'              => $store_guid,
            'activityGuid'           => '********-0000-0000-0000-************',
            'uPayDiscount'           => 0,//活动优惠
            'couponDiscount'         => 0,//优惠券抵扣
            'pointDiscount'          => 0,//积分抵扣
            'coupons'                => '',
            'payCash'                => $service_charge,//移动支付
            'payType'                => 'WechatPay',
            'meno'                   => urldecode($memo),
            'AuthorCode'             => '',//授权码
            'PaidCash'               => 0,//现金支付
            'PaidCard'               => 0,//银行卡支付
            'signature'              => '',//签名
            'Thirdorderno'           => '',
            'MemberValue'            => $total_fee,//储值支付
            'MemberDiscountUpayCash' => 0,//会员折扣优惠
            'uPaySource'             => 0,
            'StoreRelationId'        => $store_guid,
            'UserAccount'            => $user_account,//操作工号
            'PrintUserAccount'       => '',//打印播报工号
            'AddValueRuleGuid'       => null,//充值规则guid
            'AddValue'               => 0,//充值金额
            'TotalPaid'              => 0,
            'AddValuePay'            => 0,
            'addValueGiftType'       => 'undefined',
            'PayTypeForValueAdd'     => null,
            //           'payUrl'                 => 'https%3A%2F%2Fbkchina.m.yunhuiyuan.cn%2FPay%2FUPay%3Fbid%3De02cbb7b-a9e7-e311-a603-90b11c47e695%26guid%3D3e576bdd-6896-e511-a18c-90b11c47b4db%26money%3D0.01%26userAccount%3D5%26everyonePaySn%3Di5jNv',
            'originalTotalMoney'     => 0,
            'staffGuid'              => '********-0000-0000-0000-************',//员工ID
            'isChildCard'            => false,
            'submitTime'             => null,//订单提交时间
            'preferentialMoney'      => 0,
            'sourceId'               => null,
            'everyonePaySn'          => '',
        ];
        $cookie              = 'bid=' . $yunhuiyuan_bid . ';' . $this->get_appid() . '_openid=' . $this->get_openid();
        $user_agent          = $this->request->header('user_agent');
        //$header              = ['User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.3(0x18000328) NetType/WIFI Language/zh_CN'];
        $header = ['User-Agent' => $user_agent]; //需要包含MicroMessenger
        $result = curl()->set_header($header)->form_params()->set_cookies($cookie)->post($url, $data)->get_body();
        result($result);
    }

}