<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2019/1/6
 * Time: 20:26
 */

namespace app\controller\member_api\v1;

use app\model\PayOrder;
use app\model\YkyCouponQrcode;
use app\model\YkyCouponQrcodeNote;
use app\model\YkyPointValueExchangeOrder;
use app\model\YkyUserMap;
use app\common\tools\Visitor;
use Exception;
use OpenApi\BaiWang;
use OpenApi\LianBao;

class Yky extends BasicMemberApi
{

    /**
     *订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function order()
    {
        $bid          = $this->get_bid();
        $db           = new YkyUserMap();
        $user_info    = $db->get_user_info($this->get_appid(), $this->get_openid(), $bid);
        $config       = $db->get_config_by_appid_openid($user_info['user_name']);
        $yky          = new \OpenApi\Yky($config);
        $default_data = [
            'userAccount' => $user_info['user_account'],
            'where'       => '',
            'pageIndex'   => 0,
            'pageSize'    => 20,
            'orderBy'     => " OperateTime DESC",
        ];
        $list         = $yky->Get_ConsumeNotePagedV2($default_data);
        result($list);
    }

    /**
     *会员信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_info()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $result = $yky->get_member_info_by_openid($this->get_openid());
        if ($result === false) {
            error($yky->message);
        }
        result($result);
    }

    /**
     *积分/储值兑换记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function point_or_value_exchange_note()
    {
        $bid                               = $this->get_bid();
        $openid                            = $this->get_openid();
        $db_yky_point_value_exchange_order = new YkyPointValueExchangeOrder();
        $map                               = [
            ['bid', '=', $bid],
            ['openid', '=', $openid]
        ];
        $list                              = $db_yky_point_value_exchange_order->where($map)->order(['update_time' => 'DESC'])->limit(50)->select();
        result($list);
    }

    /**
     *积分/储值兑换
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function point_or_value_exchange()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $type           = $params['type'];  //1积分 2储值兑换
        $exchange_type  = $params['exchange_type'];  //1兑换到微信零钱包
        $exchange_value = $params['exchange_value']; //兑换额度,即消耗多少积分或者储值
        $openid         = $this->get_openid();
        $config         = get_config_by_bid($bid);
        //获取会员信息
        $yky         = new \OpenApi\Yky($config);
        $member_info = $yky->get_member_info_by_openid($openid);
        if ($member_info === false) {
            error($yky->message);
        }
        //需要手机号格式合法才可兑换,确保安全
        if (!tools()::is_mobile($member_info['Mobile'])) {
            error('请先到会员系统中完善手机号再来兑换哦~');
        }
        //校验兑换的额度是否 大于会员的可用积分或可用储值
        switch ($type) {
            case 1: //积分兑换
                $member_enable_point = $member_info['EnablePoint'];
                if ($exchange_value > $member_enable_point) {
                    error('您最多可使用' . $member_enable_point . '积分');
                }
                break;
            case 2: //储值兑换
                $member_enable_value = $member_info['EnableValue'];
                if ($exchange_value > $member_enable_value) {
                    error('您最多可使用' . $member_enable_value . '储值');
                }
                break;
            default:
                throw new Exception('系统繁忙~');
                break;
        }
        switch ($exchange_type) {
            case 1: //兑换到微信零钱包
                //TODO 通过查询商家 rate 参数,等来计算实际exchange_value
                if ($exchange_value < 0.3) {
                    error('最低提现金额0.3元~');
                }
                if ($exchange_value > 1) {
                    error('当前测试阶段最大提现金额1元');
                }
                break;
            default:
                throw new Exception('目前仅支持兑换到微信零钱包');
                break;
        }
        //校验通过之后 创建订单
        $db_yky_point_value_exchange_order = new YkyPointValueExchangeOrder();
        $order_guid                        = create_guid();
        $bill_number                       = tools()::get_bill_number();
        $order_data                        = [
            'guid'           => $order_guid,
            'bid'            => $bid,
            'member_guid'    => $member_info['MemberGuid'],
            'openid'         => $openid,
            'cardid'         => $member_info['CardId'],
            'mobile'         => $member_info['Mobile'] ?: '',
            'billno'         => $bill_number,
            'type'           => $type, //积分或者储值
            'value'          => $exchange_value, //花费多少积分或者储值
            'rate'           => 1, //默认1:1
            'exchange_type'  => $exchange_type, //1 兑换到微信零钱包
            'exchange_value' => $exchange_value,//给予多少金额,比如兑换到微信零钱包,则是到帐多少金额
        ];
        $db_yky_point_value_exchange_order->save($order_data);
        if ($config['point_or_value_exchange_auto_audit'] == 1) {
            //如果是自动审核模式 则添加异步任务处理该订单
            $job_data = [
                'order_guid' => $order_guid,
                'bid'        => $bid
            ];
            job()->set_job_name('Member@point_or_value_exchange')->push_job($job_data);
        }
        success('提交成功!');
    }

    /**
     *充值第三方账户
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function recharge_third_point()
    {
        $reward_card_id = '13928873139';
        $params         = $this->params;
        if (!tools()::is_mobile($params['mobile'])) {
            error('手机号格式不正确');
        }
        if (!in_array($params['channel'], [4, 5])) {
            error('选择的兑换途径不正确');
        }
        $bid          = $this->get_bid();
        $config       = get_config_by_bid($bid);
        $amount       = (int)$params['amount'];
        $enable_point = (float)$params['enable_point'];
        if ($enable_point < $amount) {
            error('您最多可兑换' . $enable_point . '分!');
        }
        if ($amount < 1000) {
            error('最低1000积分起兑换!');
        }
        $yky         = new \OpenApi\Yky($config);
        $member_info = $yky->get_member_info_by_openid($this->get_openid());
        if ($member_info === false) {
            error($yky->message);
        }
        $enable_point = $member_info['EnablePoint'];
        if ($enable_point < $amount) {
            error('您最多可兑换' . $enable_point . '分');
        }
        $db_yky_point_value_exchange_order = new YkyPointValueExchangeOrder();
        $order_guid                        = create_guid();
        $bill_number                       = tools()::get_bill_number();
        $order_data                        = [
            'guid'           => $order_guid,
            'bid'            => $bid,
            'member_guid'    => $member_info['MemberGuid'],
            'openid'         => $this->get_openid(),
            'cardid'         => $member_info['CardId'],
            'mobile'         => $params['mobile'],
            'billno'         => $bill_number,
            'type'           => 1, //积分
            'value'          => $amount,
            'rate'           => 1,
            'exchange_type'  => $params['channel'],
            'exchange_value' => $amount,
        ];
        $map                               = [
            ['guid', '=', $order_guid],
            ['bid', '=', $bid],
        ];
        $db_yky_point_value_exchange_order->save($order_data);
        //开始返还第三方平台积分,并扣除一卡易平台积分
        if ($params['channel'] == 4) {
            $lianbao                  = new LianBao();
            $lianbao->recommend_phone = '13046265117';
            $money_amount             = tools()::nc_price_calculate($amount, '/', 110, 4);
            $money_amount             = tools()::nc_price_calculate(ceil($money_amount * 100), '/', 100);
            if ($money_amount < 0.01) {
                error('您兑换的积分太低了,请加大试试');
            }
            $result = $lianbao->score_order($params['mobile'], $bill_number, $money_amount, '一卡易积分兑换');
            if ($result === false) {
                $update_data = [
                    'status' => -1,
                    'result' => $lianbao->errmsg,
                ];
                $db_yky_point_value_exchange_order::update($update_data, $map);
                error($lianbao->errmsg);
            }
            $update_data = [
                'third_billno' => $result['order_number']
            ];
            $db_yky_point_value_exchange_order::update($update_data, $map);
        }
        if ($params['channel'] == 5) {
            $bw     = BaiWang::getInstance();
            $data   = [
                'seller_account' => $reward_card_id,
                'buyer_account'  => $params['mobile'],
                'order_no'       => $bill_number,
                'pay_cash'       => $amount,
                'goods'          => '【商品】',
                'memo'           => '订单号:' . $bill_number . '来自一卡易积分兑换',
            ];
            $result = $bw->createOrder($data);
            if ($result === false) {
                $update_data = [
                    'status' => -1,
                    'result' => $bw->errmsg,
                ];
                $db_yky_point_value_exchange_order::update($update_data, $map);
                error($bw->errmsg);
            }
            $update_data = ['third_billno' => $result['order_no']];
            $db_yky_point_value_exchange_order::update($update_data, $map);
        }
        //扣除一卡易积分
        $post_data   = [
            'userAccount' => '10000',
            'cardId'      => $member_info['CardId'],
            'point'       => -abs($amount),
            'meno'        => date('Y-m-d') . '_转换第三方平台积分.单号:' . $bill_number
        ];
        $result      = $yky->Update_MemberPoint($post_data);
        $update_data = [
            'status'     => 1,
            'yky_billno' => $result['message']
        ];
        $db_yky_point_value_exchange_order::update($update_data, $map);
        success('兑换成功!');
    }

    /**
     *充值积分受限
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function recharge()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $params = $this->params;
        $amount = (int)$params['amount'];
        if ($amount <= 0) {
            error('充值数量需大于0!');
        }
        $db_order    = new PayOrder();
        $bill_number = tools()::get_bill_number();
        // 接口成功的处理,插入数据库 第三方支付表
        $job_attach = [
            'job'  => 'Yikayi@recharge_limit',
            'data' => [
                'bid'         => $bid,
                'amount'      => $amount,
                'openid'      => $this->get_openid(),
                'limit_type'  => 1,//积分受限
                'bill_number' => $bill_number,
            ],
        ];
        $total_fee  = $amount;
        $job_attach = json_encode($job_attach, JSON_UNESCAPED_UNICODE);
        $data       = [
            'guid'        => create_guid(),
            'bid'         => $bid,
            'total_fee'   => $total_fee,
            'bill_number' => $bill_number,
            'buyer_id'    => $this->get_openid(),
            'way'         => 2,  //在线充值
            'job_attach'  => $job_attach,
            'body'        => '在线充值积分受限'
        ];
        $result     = $db_order->save($data);
        if (!$result) {
            error('订单创建失败');
        }
        $data = [
            "billNumber"   => $bill_number, // 订单号(同一个商家下唯一)
            "totalFee"     => $total_fee, // 总金额(以分为单位，1元= 100 分)
            "notifyUrl"    => (string)url('gateway/yky/pay_notify', ['bid' => $bid], false, true),// 回调地址
            //"redirectUrl" => 'http://www.baidu.com?guid=bfdadd88-f692-069a-4edb-993c7e5a711e',// 回调地址,暂时不写
            "uPayDiscount" => $total_fee,// 不优惠金额
            //'attach'     => $this->get_bid(),
            "meno"         => '【在线充值积分受限】'// 备注信息
        ];
        if (Visitor::is_wechat_browser() && Visitor::get_referer()) {
            $data['redirectUrl'] = Visitor::get_referer();
            // $data['redirectUrl'] = 'http://www.yikayi.net/admin';
        }
        $result = $yky->UPayOrder($data);
        if ($result === false) {
            error($yky->message);
        }
        $data = ['url' => urlencode($result['upayUrl'])];
        result($data);
    }

    public function get_yky_coupon_qrcode_info()
    {
        $bid          = $this->get_bid();
        $db           = new YkyUserMap();
        $user_info    = $db->get_user_info($this->get_appid(), $this->get_openid(), $bid);
        $params       = $this->params;
        $qrcode_guid  = $params['qrcode_guid'];
        $page_guid    = $params['page_guid'];
        $user_account = $user_info['user_account'];

        $db_yky_coupon_qrcode      = new YkyCouponQrcode();
        $map                       = [
            ['bid', '=', $bid],
            ['guid', '=', $qrcode_guid],
        ];
        $qrcode_info               = $db_yky_coupon_qrcode->where($map)->findOrFail();
        $yky_coupon_guid           = $qrcode_info['yky_coupon_guid'];
        $qrcode_title              = $qrcode_info['name'];
        $note_guid                 = create_guid();
        $insert_data               = [
            'guid'             => $note_guid,
            'bid'              => $bid,
            'yky_user_account' => $user_account,
            'yky_coupon_guid'  => $yky_coupon_guid,
            'page_guid'        => $page_guid,
            'qrcode_guid'      => $qrcode_guid,
            'status'           => 0, //待领取
        ];
        $db_yky_coupon_qrcode_note = new YkyCouponQrcodeNote();
        $db_yky_coupon_qrcode_note->save($insert_data);

        $receive_coupon_url = (string)url('member/yikayi/receive_coupon', ['bid' => $bid, 'note_guid' => $note_guid], false, true);
        $qrcode_url         = (string)url('index/plugins/create_qrcode', ['size' => 300, 'data' => $receive_coupon_url], false, true);
        result([
            'page_guid'    => $page_guid,
            'qrcode_title' => $qrcode_title,
            'qrcode_url'   => $qrcode_url,
            'user_account' => $user_account,
        ]);
    }
}