<?php

namespace app\controller\member_api\v1;

use app\model\Favorites as FavoritesModel;
use app\model\Goods as GoodsModel;
use app\model\GoodsAttr;
use app\model\GoodsAttrGroup;
use app\model\GoodsCategory;
use app\model\GoodsSku;
use app\model\GoodsSkuItem;
use app\model\Member;
use Exception;
use Intervention\Image\AbstractFont;
use Intervention\Image\Constraint;
use Intervention\Image\ImageManagerStatic as Image;
use think\facade\Db;

class Goods extends BasicMemberApi
{

    public function goods_attr_info()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $goods_guid = $params['goods_guid'];
        $attr_list  = json_decode($params['attr_list'], true);
        $db_goods   = new GoodsModel();
        $data       = $db_goods->get_goods_sku_info($bid, $attr_list, $goods_guid);
        result($data);
    }

    /**
     *商品详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        //   $db_member             = new Member();
        //   $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
        $goods_guid = $params['guid'];
        $map_goods  = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $filed      = [
            'guid',
            'category_guid',
            'name',
            'original_price',
            'price',
            //  Db::raw("ROUND(price*$member_discount_ratio,2) as price"),
            'pic',
            'pic1',
            'pic2',
            'description',
            'create_time',
            'update_time'
        ];
        $db_goods   = new GoodsModel();
        $goods_info = $db_goods->where($map_goods)->findOrEmpty();
        if ($goods_info->isEmpty()) {
            error('商品可能已经被删除');
        }
        //增加浏览量
        $db_goods->where($map_goods)->setInc('view_times');

        //拼接商品公共详情模块
        $config                      = get_config_by_bid($bid);
        $goods_detail_common_content = $config['goods_detail_common_content'];
        if ($goods_detail_common_content) {
            $goods_info['description'] = $goods_detail_common_content . $goods_info['description'];
        }
        $goods_info['description'] = tools()::remove_taobao_jd_class($goods_info['description']);
        $goods_info['description'] = tools()::add_rich_img_class($goods_info['description']);
        $goods_info                = $db_goods->buildPic($goods_info);
        $db_favorites              = new FavoritesModel();
        $map                       = [
            ['bid', '=', $bid],
            ['type', '=', 1],
            ['relation_guid', '=', $goods_guid],
            ['member_guid', '=', $member_guid],
        ];
        $is_favorite               = $db_favorites->where($map)->value('status');
        $goods_info['is_favorite'] = $is_favorite ? 1 : 0;
        $goods_info['price']       = $goods_info['show_price'] >= 0 ? $goods_info['show_price'] : $goods_info['price'];

        $db_member             = new Member();
        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
        $goods_info['price']   = tools()::nc_price_calculate($goods_info['price'], '*', $member_discount_ratio, 2);
        $goods_info['sales']   = $goods_info['sales'] + $goods_info['virtual_sales'];
        //       $goods_info['service_list'] = ['7天退货', '正品保障'];
        $is_attribute                  = $goods_info['is_attribute'];
        $goods_info['attr_group_list'] = [];
        $goods_info['use_attr']        = $is_attribute;
        if ($is_attribute == 1) {
            // 使用公共方法获取属性分组列表
            $attr_group_list = $db_goods->get_goods_attr_group_list($bid, $goods_guid, true);
            //        $attr_group_array = $db_goods_attr_group->where($map)->field(['guid', 'name'])->select();
            //
            //        foreach ($attr_group_array as $attr_group) {
            //            $attr_group_guid = $attr_group['guid'];
            //            $map             = [
            //                ['bid', '=', $bid],
            //                ['goods_attr_group_guid', '=', $attr_group_guid],
            //            ];
            //            $attr_array      = $db_goods_attr->where($map)->select();
            //
            //
            //        }

            //            $goods_info['attr_group_list']   = [
            //                [
            //                    'attr_group_name' => '颜色',
            //                    'attr_group_id'   => 1,
            //                    'attr_list'       =>
            //                        [
            //                            [
            //
            //                                'attr_name' => '白色',
            //                                'attr_id'   => 11,
            //                            ],
            //                            [
            //                                'attr_id'   => 22,
            //                                'attr_name' => '黑色',
            //                            ],
            //                        ]
            //                ],
            //                [
            //                    'attr_group_name' => '尺码',
            //                    'attr_group_id'   => 2,
            //                    'attr_list'       =>
            //                        [
            //                            [
            //                                'attr_id'   => 33,
            //                                'attr_name' => 'XL',
            //                            ],
            //                            [
            //                                'attr_id'   => 44,
            //                                'attr_name' => 'XXL',
            //                            ],
            //                        ]
            //                ],
            //            ];
            $goods_info['attr_group_list']   = $attr_group_list;
            $goods_info['checked_attr_list'] = [];
        }
        //        $goods_info['show_exchange_button'] = 1;
        $goods_info['show_stock'] = (int)$config['choose_goods_show_stock'];
        result($goods_info);
    }

    /**
     *快速购买
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function quick()
    {
        $bid                 = $this->get_bid();
        $cache_key           = __FUNCTION__ . ':' . $bid;
        $goods_category_list = cache($cache_key);
        if (!$goods_category_list) {
            //        $db_member             = new Member();
            //        $member_guid           = $this->get_member_guid();
            //        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
            $db_goods_category    = new GoodsCategory();
            $map_goods_category   = [
                ['bid', '=', $bid],
                ['status', '=', 1],
                ['parent_guid', '=', tools()::get_empty_guid()],
            ];
            $filed_goods_category = [
                'bid',
                'guid',
                'parent_guid',
                'name',
                'mini_pic'
            ];
            $order_goods_category = [
                'sort'        => 'ASC',
                'create_time' => 'DESC'
            ];

            $goods_category_list = $db_goods_category->where($map_goods_category)->field($filed_goods_category)->order($order_goods_category)->append(['all_goods', 'sub_category'])->order(['sort' => 'ASC'])->select();
            $goods_category_list = tools()::object2array($goods_category_list);
            foreach ($goods_category_list as $key => $val) {
                $goods_category_list[$key]['goods'] = $val['all_goods'];
                unset($goods_category_list[$key]['all_goods']);
            }
            cache($cache_key, $goods_category_list, 30);
        }
        result(['list' => $goods_category_list]);

        $db_goods            = new GoodsModel();
        $db_goods_category   = new GoodsCategory();
        $map                 = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
        ];
        $filed               = [
            'guid',
            'category_guid',
            'name',
            'pic'   => 'cover_pic',
            'price',
            'show_price',
            'original_price',
            'count' => 'num',
        ];
        $goods_list          = $db_goods->field($filed)->where($map)->select();
        $filed               = [
            'guid',
            'name',
            'parent_guid',
        ];
        $goods_category_list = $db_goods_category->field($filed)->where($map)->select();
        $goods_list          = tools()::object2array($goods_list);
        $goods_category_list = tools()::object2array($goods_category_list);
        //补充图片前缀
        foreach ($goods_list as $k => $v) {
            $goods_list[$k] = $db_goods->buildPic($v);
        }
        foreach ($goods_category_list as $key => $val) {
            $goods_category_list[$key]['goods'] = [];
            foreach ($goods_list as $k => $v) {
                if ($v['category_guid'] === $val['guid']) {
                    $v['use_attr']                        = 0;
                    $v['price']                           = $v['show_price'] >= 0 ? $v['show_price'] : $v['price'];
                    $v['original_price']                  = number_format($v['original_price'], 2);
                    $attr_array                           = [
                        [
                            'attr_list' => [
                                'attr_id'   => 1,
                                'attr_name' => '默认',
                            ],
                            'num'       => 1,
                            'price'     => (float)$v['price'],
                            'no'        => '',
                            'pic'       => ''
                        ]
                    ];
                    $v['attr']                            = json_encode($attr_array, JSON_UNESCAPED_UNICODE);
                    $goods_category_list[$key]['goods'][] = $v;
                }
            }
        }
        result(['list' => $goods_category_list]);
    }

    public function goods_qrcode()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $member_guid   = $this->get_member_guid();
        $appid         = $this->get_appid();
        $goods_guid    = $params['goods_guid'];
        $db_goods      = new GoodsModel();
        $map           = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $goods_info    = $db_goods->where($map)->find();
        $goods_pic     = $goods_info['pic'];
        $goods_name    = $goods_info['name'];
        $goods_price   = $goods_info['price'];
        $db_business   = new \app\model\Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $business_name = $business_info['business_name'];
        $path          = '/pages/index/goods_detail?guid=' . $goods_guid . '&share_member_guid=' . $member_guid;
        $version       = '**********';
        $file_path     = '/temp/images/' . md5($version . $appid . $path . $goods_pic . $goods_name . $goods_price . $business_name) . '.png';
        $from          = 'cache';
        $absolute_path = tools()::get_absolute_path($file_path);
        if (!file_exists($absolute_path)) {
            $from         = 'api';
            $mini         = weixin($appid)::WeMiniQrcode();
            $qrcodeBinary = $mini->createMiniPath($path, 300, false, ['r' => '0', 'g' => '0', 'b' => '0'], false);

            // 使用白底+商品图合成方式
            $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
            Image::configure(['driver' => $driver_name]);

            // 1. 创建白色画布
            $image = Image::canvas(750, 1334, '#fff');

            // 2. 商品图片
            $goods_pic_local_path = tools()::web_url_to_local_absolute_path($goods_pic);
            $goods_image          = Image::make($goods_pic_local_path)->resize(750, null, function (Constraint $constraint) {
                $constraint->aspectRatio();
            });
            $image->insert($goods_image, 'top-left', 0, 0);

            // 3. 计算商品图高度
            $goods_image_height = $goods_image->height();

            // 4. 其余元素插入到商品图下方
            // 二维码（直接用二进制内容生成Image对象，无需file_put_contents）
            $qrcode_image = Image::make($qrcodeBinary);
            $image->insert($qrcode_image, 'bottom-right', 30, 30);

            // 字体路径
            $font = tools()::get_absolute_path('/static/css/fonts/MSYH.TTC');

            // 商品名
            $image->text($goods_name, 10, 780, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(36);
                $font_draw->color('#000000');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 价格
            $image->text('￥' . $goods_price, 10, 920, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(48);
                $font_draw->color('#ff4544');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 商家名
            $image->text($business_name, 10, 1130, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(36);
                $font_draw->color('#575757');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 长按识别
            $image->text('长按识别小程序访问', 10, 1200, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(30);
                $font_draw->color('#575757');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            $image->save($absolute_path);
        }
        $data = [
            'from'    => $from,
            'pic_url' => tools()::path_to_web($file_path),
        ];
        result($data);
    }

    /**
     *获取商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_list()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $db_member             = new Member();
        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);

        $map = [
            ['g.bid', '=', $this->get_bid()],
            ['g.status', '=', 1],
        ];
        if (!empty($params['keyword'])) {
            $map[] = ['g.name', 'like', '%' . $params['keyword'] . '%'];
        }
        $where_or_map = [];
        if (!empty($params['cat_guid'])) {
            $cat_guid            = $params['cat_guid'];
            $db_goods_category   = new GoodsCategory();
            $map_goods_category  = [
                ['bid', '=', $bid],
                ['guid|parent_guid', '=', $cat_guid],
            ];
            $category_guid_array = $db_goods_category->where($map_goods_category)->column('guid');
            $map[]               = ['g.category_guid', 'IN', $category_guid_array];
            $where_or_map        = [
                ['g.category_guid', '=', $cat_guid],
                ['g.status', '=', 1],
                ['gc.guid', '=', $cat_guid],
            ];
        }
        if (!empty($params['tag_guid'])) {
            $tag_guid      = $params['tag_guid'];
            $tag_guid_json = json_encode([$tag_guid]);
            $map[]         = Db::raw("JSON_CONTAINS(g.tag_guid,'$tag_guid_json')>0");
            //            $map[]    = ['g.tag_guid', '=', $tag_guid];
        }

        $db_goods  = new GoodsModel();
        $join      = [
            ['goods_category gc', "g.category_guid = gc.guid AND g.bid = gc.bid"],
        ];
        $filed     = [
            'g.guid',
            'g.category_guid',
            'g.name',
            //            'g.price',
            Db::raw("ROUND(g.price*$member_discount_ratio,2) as price"),
            'g.show_price',
            'g.original_price',
            'g.pic',
            'g.description',
            Db::raw("g.sales + g.virtual_sales as sales"),
        ];
        $sort_type = (isset($params['sort_type']) && $params['sort_type'] == 0) ? 'ASC' : 'DESC';
        $sort      = $params['sort'] ?? false;
        switch ($sort) {
            case 1:
                $sort = 'g.create_time';
                break;
            case 2:
                $sort = 'g.price';
                break;
            default:
                $sort      = 'g.sort';
                $sort_type = 'ASC';
                break;
        }
        unset($this->params['cat_guid']);
        unset($this->params['tag_guid']);
        unset($this->params['sort_type']);
        unset($this->params['sort']);
        unset($this->params['keyword']);

        $this->model = $db_goods->alias('g')->join($join)->field($filed)->where($map)->order($sort, $sort_type);
        $goods_list  = $this->_list();
        foreach ($goods_list['data'] as $k => $v) {
            $goods_list['data'][$k]['price']       = $v['show_price'] >= 0 ? $v['show_price'] : $v['price'];
            $goods_list['data'][$k]['pic']         = tools()::add_thumbnail_small($goods_list['data'][$k]['pic']);
            $goods_list['data'][$k]['description'] = tools()::add_rich_img_class($goods_list['data'][$k]['description']);
        }
        $goods_list['list']     = $goods_list['data']; //兼容分页
        $goods_list['cat_list'] = [
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => 'aaaa'],
            ['name' => '3333'],
        ]; //兼容分页

        //       $data = [
        //           'data'       => $goods_list['data'], //兼容分页
        //           'list'       => $goods_list['data'],
        //           'page_count' => $goods_list['last_page'],
        //           'row_count'  => $goods_list['total'],
        //       ];
        result($goods_list);
    }

    /**
     *获取商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get()
    {
        $params                = $this->params;
        $db_member             = new Member();
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
        $map                   = [
            ['g.bid', '=', $bid],
            ['g.status', '=', 1],
        ];
        if (!empty($params['keyword'])) {
            $map[] = ['g.name', 'like', '%' . $params['keyword'] . '%'];
        }
        $where_or_map = [];
        if (!empty($params['cat_guid'])) {
            $cat_guid     = $params['cat_guid'];
            $map[]        = ['g.category_guid', '=', $cat_guid];
            $where_or_map = [
                ['g.category_guid', '=', $cat_guid],
                ['g.status', '=', 1],
                ['gc.guid', '=', $cat_guid],
            ];
        }
        $db_goods  = new GoodsModel();
        $join      = [
            ['goods_category gc', " g.category_guid = gc.guid AND g.bid = gc.bid"],
        ];
        $filed     = [
            'g.guid',
            'g.category_guid',
            'g.name',
            //            'g.price',
            Db::raw("ROUND(g.price*$member_discount_ratio,2) as price"),
            'g.show_price',
            'g.original_price',
            'g.pic',
            'g.description',
        ];
        $sort_type = (isset($params['sort_type']) && $params['sort_type'] == 1) ? 'DESC' : 'ASC';
        $sort      = $params['sort'] ?? false;
        switch ($sort) {
            case 2:
                $sort = 'price';
                break;
            default:
                $sort = 'g.sort';
                break;
        }
        $goods_list = $db_goods->alias('g')->join($join)->field($filed)->where($map)->order($sort, $sort_type)->paginate($this->get_paginate_config(6))->toArray();

        foreach ($goods_list['data'] as $k => $v) {
            $goods_list['data'][$k]['price'] = $v['show_price'] >= 0 ? $v['show_price'] : $v['price'];
        }
        $data = [
            'data'       => $goods_list['data'], //兼容分页
            'list'       => $goods_list['data'],
            'page_count' => $goods_list['last_page'],
            'row_count'  => $goods_list['total'],
        ];
        result($data);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db_member             = new Member();
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
        $db_goods              = new GoodsModel();
        $db_goods_category     = new GoodsCategory();
        $map                   = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
        ];
        $filed                 = [
            'guid',
            'category_guid',
            'name',
            'pic',
            //            'price',
            Db::raw("ROUND(price*$member_discount_ratio,2) as price"),
            'original_price',
            'count',
            'description',
        ];
        $order                 = [
            'sort'        => 'DESC',
            'create_time' => 'DESC',
        ];
        $goods_list            = $db_goods->field($filed)->order($order)->where($map)->select();
        $filed                 = [
            'guid',
            'name',
        ];
        $goods_category_list   = $db_goods_category->field($filed)->where($map)->select();
        $goods_list            = tools()::object2array($goods_list);
        $goods_category_list   = tools()::object2array($goods_category_list);
        //补充图片前缀
        foreach ($goods_list as $k => $v) {
            $goods_list[$k]          = $db_goods->buildPic($v);
            $goods_list[$k]['price'] = $v['show_price'] >= 0 ? $v['show_price'] : $v['price'];
        }
        foreach ($goods_category_list as $key => $val) {
            $goods_category_list[$key]['goods_list'] = [];
            foreach ($goods_list as $k => $v) {
                if ($v['category_guid'] === $val['guid']) {
                    $goods_category_list[$key]['goods_list'][] = $v;
                }
            }
        }
        result($goods_category_list);
    }
}
