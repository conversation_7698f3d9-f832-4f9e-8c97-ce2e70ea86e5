<?php

namespace app\controller\member_api\v1;

use app\model\ConsumeQueueNote;
use app\model\MemberMoneyNote;
use app\model\YkyDailyRewardOrder;
use Exception;


class Reward extends BasicMemberApi
{
    /**
     *消费返现队列
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function consume_queue_note()
    {
        $bid    = $this->get_bid();
        $openid = $this->get_openid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $member = $yky->get_member_info_by_openid($openid);
        if ($member === false) {
            error($yky->message);
        }
        $member_chain_store_guid = $member['ChainStoreGuid'];
        $db_consume_queue_note   = new ConsumeQueueNote();
        $map                     = [
            ['bid', '=', $bid],
            // ['chain_store_guid', '=', $member_chain_store_guid],
            ['status', '=', 1]
        ];
        $filed                   = [
            'card_id',
            'true_name',
            'head_img',
            'reward_time',
            'reward_money',
            'message',
        ];
        $is_reward_list          = $db_consume_queue_note->where($map)->order(['reward_time' => 'DESC'])->field($filed)->limit(50)->select()->toArray();
        $map                     = [
            ['bid', '=', $bid],
            // ['chain_store_guid', '=', $member_chain_store_guid],
            ['status', '=', 0]
        ];
        $wait_reward_list        = $db_consume_queue_note->where($map)->order(['id' => 'ASC'])->field($filed)->limit(50)->select()->toArray();
        $map                     = [
            ['bid', '=', $bid],
            ['openid', '=', $openid],
            // ['chain_store_guid', '=', $member_chain_store_guid],
        ];
        $note                    = $db_consume_queue_note->where($map)->order(['status' => 'DESC', 'reward_time' => 'DESC'])->findOrEmpty();
        //$db_business             = new \app\model\Business();
        //$business_info         = $db_business->get_business_info_by_account_or_guid($bid);
        $data = [
            'is_reward_list'     => $is_reward_list,
            'wait_reward_list'   => $wait_reward_list,
            'member'             => $member,
            'note'               => $note,
            'title'              => '【' . $member['StoreName'] . '】返现列表',
            // 'title'            => $business_info['business_name'] . '【' . $member['StoreName'] . '】返现列表',
            'empty_reward_url'   => $config['empty_reward_url'],
            'reward_help_url'    => $config['reward_help_url'],
            'advance_reward_url' => $config['advance_reward_url'],
            'buy_multiple'       => $config['buy_multiple']
        ];
        result($data);
    }

    public function member_info()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $member = $yky->get_member_info_by_openid($this->get_openid());
        if ($member === false) {
            error($yky->message);
        }
        $member_guid = $member['MemberGuid'];
        $db_member   = new \app\model\Member();
        $member_map  = ['bid' => $bid, 'yky_member_guid' => $member_guid];
        $member_info = $db_member->get_member_info($member_map);
        if (!$member_info) {
            throw new Exception('会员注册失败');
        }
        $member['money'] = $member_info['money'] ?? 0;
        $data            = [
            'member' => $member,
            'config' => $config
        ];
        result($data);
    }

    public function money()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $member = $yky->get_member_info_by_openid($this->get_openid());
        if ($member === false) {
            error($yky->message);
        }
        $yky_member_guid          = $member['MemberGuid'];
        $db_yky_member_money_note = new MemberMoneyNote();
        $map                      = [
            ['bid', '=', $bid],
            ['yky_member_guid', '=', $yky_member_guid],
        ];
        $list                     = $db_yky_member_money_note->where($map)->limit(50)->order(['id' => 'DESC'])->select();
        result($list);
    }

    /**
     *奖励记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function note()
    {
        $bid        = $this->get_bid();
        $config     = get_config_by_bid($bid);
        $yky        = new \OpenApi\Yky($config);
        $member     = $yky->get_member_info_by_openid($this->get_openid());
        $card_id    = $member['CardId'];
        $map        = [
            ['ydro.bid', '=', $this->get_bid()],
            ['ydro.reward_card_id', '=', $card_id],
            ['ydro.status', '=', 1],
            ['ydro.reward_value', '>', 0],
        ];
        $join       = [
            ['yky_chain_store ycs', 'ydro.chain_store_guid=ycs.guid AND ydro.bid=ycs.bid'],
        ];
        $db         = new YkyDailyRewardOrder();
        $filed      = [
            'ycs.guid',
            'ycs.store_name',
            'ydro.trade_date',
            'ydro.reward_value',
            'ydro.level',
            'ydro.status',
            'ydro.message',
            'ydro.create_time',
            'ydro.update_time',
        ];
        $order_list = $db->alias('ydro')->join($join)->field($filed)->where($map)->limit(100)->order('create_time', 'DESC')->select();
        result($order_list);
    }
}
