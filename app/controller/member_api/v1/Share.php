<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use app\common\service\NotifyService;
use app\model\GoodsOrder as GoodsOrderModel;
use app\model\Member;
use app\model\MemberBrokerageCashNote;
use app\model\MemberBrokerageNote;
use app\model\MemberDistributorApplyNote;
use Exception;
use Intervention\Image\AbstractFont;
use Intervention\Image\ImageManagerStatic as Image;

class Share extends BasicMemberApi
{
    /**
     *
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function check()
    {
        $params                           = $this->params;
        $bid                              = $this->get_bid();
        $member_guid                      = $this->get_member_guid();
        $db_member                        = new Member();
        $is_distributor                   = $db_member->is_distributor_by_member_guid($bid, $member_guid);
        $db_member_distributor_apply_note = new MemberDistributorApplyNote();
        $map                              = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
        ];
        $note                             = $db_member_distributor_apply_note->where($map)->findOrEmpty();
        $apply_status                     = $note->isEmpty() ? -2 : $note['status']; //默认未申请
        result(['is_distributor' => (int)$is_distributor, 'apply_status' => $apply_status]);
    }

    public function join()
    {
        $params                           = $this->params;
        $bid                              = $this->get_bid();
        $member_guid                      = $this->get_member_guid();
        $name                             = $params['name'];
        $mobile                           = $params['mobile'];
        $db_member_distributor_apply_note = new MemberDistributorApplyNote();
        $guid                             = create_guid();
        $insert_data                      = [
            'guid'        => $guid,
            'bid'         => $bid,
            'member_guid' => $member_guid,
            'mobile'      => $mobile,
            'name'        => $name,
            'status'      => 0, // 待审核
        ];
        $db_member_distributor_apply_note->save($insert_data);

        //把手机号和姓名,更新到会员姓名
        $db_member   = new Member();
        $update_data = [
            'bid'    => $bid,
            'guid'   => $member_guid,
            'mobile' => $mobile,
            'name'   => $name,
        ];
        $member      = $db_member->update_member_info($update_data);

        $config                            = get_config_by_bid($bid);
        $is_auto_examine_distributor_apply = $config['is_auto_examine_distributor_apply'];
        if ($is_auto_examine_distributor_apply == 1) {
            $db_member_distributor_apply_note->examine($bid, $guid);
        }
        $update_data['create_time'] = format_timestamp();
        notify()->set_key_name(NotifyService::DistributorApply)->set_member_mobile($mobile)->set_data($update_data)->set_bid($bid)->send();
        success('申请成功');
    }

    public function get_order()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $member_guid    = $this->get_member_guid();
        $map            = [
            ['bid', '=', $bid],
            ['share_member_guid', '=', $member_guid],
        ];
        $db_goods_order = new GoodsOrderModel();
        //查询订单基本信息
        $field       = [
            'bid',
            'guid',
            'bill_number',
            'total_amount',
            'total_money',
            'status',
            'create_time',
        ];
        $this->model = $db_goods_order->field($field)->where($map)->append(['order_detail'])->order(['update_time' => 'DESC']);
        $list        = $this->_list();
        result($list);


        result(['list' => [
            [
                'order_type'   => 'order_type',
                'order_no'     => '12000',
                'nickname'     => 'nickname',
                'avatar_url'   => 'avatar_url',
                'is_price'     => 'is_price',
                'status'       => '待付款',
                'share_status' => 'share_status',
                'share_money'  => 10,
                'create_time'  => format_timestamp(),
            ]
        ]]);
    }

    public function get_cash_note()
    {
        $params                        = $this->params;
        $bid                           = $this->get_bid();
        $member_guid                   = $this->get_member_guid();
        $map                           = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
        ];
        $db_member_brokerage_cash_note = new MemberBrokerageCashNote();
        //查询订单基本信息
        $field       = [
            'bid',
            'guid',
            'pay_type',
            'cash_money',
            'status',
            'create_time',
        ];
        $field       = ['*'];
        $this->model = $db_member_brokerage_cash_note->field($field)->where($map)->append(['status_text'])->order(['update_time' => 'DESC'])->where($map);
        $list        = $this->_list();
        result($list);
    }

    public function get_qrcode()
    {
        $params                 = $this->params;
        $bid                    = $this->get_bid();
        $member_guid            = $this->get_member_guid();
        $appid                  = $this->get_appid();
        $config                 = get_config_by_bid($bid);
        $share_background_image = $config['share_background_image'];
        if (empty($share_background_image)) {
            error('未设置分享背景图');
        }
        $share_background_config = $config['share_background_config'];
        $path                    = '/pages/index/index?share_member_guid=' . $member_guid;
        $file_path               = '/temp/images/' . md5($appid . $path . $share_background_image . $share_background_config) . '.png';
        $from                    = 'cache';
        $absolute_path           = tools()::get_absolute_path($file_path);
        if (!file_exists($absolute_path)) {
            $from                    = 'api';
            $mini                    = weixin($appid)::WeMiniQrcode();
            $share_background_config = json_decode($share_background_config, true);
            $result                  = $mini->createMiniPath($path, $share_background_config['width'], false, ['r' => '0', 'g' => '0', 'b' => '0'], false);
            file_put_contents($absolute_path, $result);
            if ($share_background_image) {
                // 配置图像驱动，优先使用imagick
                $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
                Image::configure(['driver' => $driver_name]);

                // 打开背景图片
                $background_image_local = tools()::web_url_to_local_absolute_path($share_background_image);
                $image                  = Image::make($background_image_local);

                // 从文件创建二维码图片
                $qrCodeImage = Image::make($absolute_path);

                // 将二维码合成到背景图上
                $image->insert($qrCodeImage, 'top-left', (int)$share_background_config['x'], (int)$share_background_config['y']);

                // 如果有会员ID，添加到左上角
                if ($this->get_member_id() > 0) {
                    $font = tools()::get_absolute_path('/static/css/fonts/MSYH.TTC');
                    $image->text($this->get_member_id(), 10, 10, function (AbstractFont $font_draw) use ($font) {
                        $font_draw->file($font);
                        $font_draw->size(18);
                        $font_draw->color('#000000', 10); // 10% 透明度
                        $font_draw->align('left');
                        $font_draw->valign('top');
                        $font_draw->angle(0);
                    });
                }

                // 保存最终图片
                $image->save($absolute_path);
            }
        }
        $data = [
            'from'   => $from,
            'qrcode' => tools()::path_to_web($file_path),
        ];
        result($data);
    }

    public function get_team()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $member_guid    = $this->get_member_guid();
        $db_member      = new Member();
        $map            = [
            ['bid', '=', $bid],
            ['share_member_guid', '=', $member_guid]
        ];
        $field          = [
            'id',
            'guid',
            'name',
            'head_img',
            'share_member_guid',
            'create_time',
        ];
        $list           = $db_member->field($field)->limit(30)->where($map)->order(['create_time' => 'DESC'])->select();
        $data['list']   = $list;
        $data['first']  = 2;
        $data['second'] = 2;
        $data['third']  = 2;
        result($data);

    }

    //申请提现
    public function apply()
    {
        $params                        = $this->params;
        $bid                           = $this->get_bid();
        $member_guid                   = $this->get_member_guid();
        $db_member_brokerage_cash_note = new MemberBrokerageCashNote();
        $pay_type                      = $params['pay_type'];
        $cash_money                    = $params['cash_money'];
        $db_member_brokerage_note      = new MemberBrokerageNote();

        $member_brokerage_info          = $db_member_brokerage_note->get_member_brokerage_info($bid, $member_guid);
        $member_brokerage               = $member_brokerage_info['member_brokerage'];
        $available_cash_brokerage_money = $member_brokerage_info['available_cash_brokerage_money'];

        if ($cash_money > $member_brokerage) {
            error('提现金额不能超过您的剩余佣金总额:' . $member_brokerage);
        }
        if ($cash_money > $available_cash_brokerage_money) {
            error('提现金额不能超过您的可提现佣金总额:' . $available_cash_brokerage_money);
        }

        $config                        = get_config_by_bid($bid);
        $member_brokerage_min_can_cash = $config['member_brokerage_min_can_cash'];
        if ($cash_money < $member_brokerage_min_can_cash) {
            error('佣金需满' . $member_brokerage_min_can_cash . '元才可提现');
        }

        //todo,一定要做好对cash_money的校验
        $bill_number = tools()::get_bill_number();
        $insert_data = [
            'guid'             => create_guid(),
            'bid'              => $bid,
            'member_guid'      => $member_guid,
            'bill_number'      => $bill_number,
            'cash_money'       => $cash_money,
            'pay_type'         => $pay_type,
            'status'           => 0, //等待审核
            'wechat_true_name' => $params['wechat_true_name'] ?? '',
            'wechat_account'   => $params['wechat_account'] ?? '',
            'alipay_true_name' => $params['alipay_true_name'] ?? '',
            'alipay_account'   => $params['alipay_account'] ?? '',
            'bank_true_name'   => $params['bank_true_name'] ?? '',
            'bank_name'        => $params['bank_name'] ?? '',
            'bank_account'     => $params['bank_account'] ?? '',
        ];

        $db_member_brokerage_cash_note->save($insert_data);
        $insert_data['create_time'] = format_timestamp();
        notify()->set_key_name(NotifyService::MemberBrokerageCashApply)->set_data($insert_data)->set_bid($bid)->send();

        success('提交成功,请耐心等待审核');
    }

    public function get_share_setting()
    {
        $bid                         = $this->get_bid();
        $config                      = get_config_by_bid($bid);
        $distributor_apply_agreement = $config['distributor_apply_agreement'];
        $share_setting               = [
            'bank'              => 1,
            'min_money'         => 0,
            'level'             => 1,
            'content'           => '',
            'agree'             => $distributor_apply_agreement,
            'business_name'     => '分享合伙人',
            'add_share_pic_url' => ''
        ];
        result($share_setting);
    }

    public function get_cash_info()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $config      = get_config_by_bid($bid);

        //统计佣金数据
        $db_member_brokerage_note = new MemberBrokerageNote();
        //     'total_success_cash_brokerage_money'        => $total_success_cash_brokerage_money,  //累计成功提现佣金
        //           'total_auditing_cash_brokerage_money'       => $total_auditing_cash_brokerage_money, //累计审核中佣金
        //           'total_waiting_for_payment_brokerage_money' => $total_waiting_for_payment_brokerage_money, //累计审核通过待打款金额
        //           'available_cash_brokerage_money'            => $available_cash_brokerage_money, //可提现金额
        //           'total_brokerage_money'                     => $total_brokerage_money,//分销佣金
        //           'total_brokerage_order_money'               => $total_brokerage_order_money,//分销订单总额
        $member_brokerage_info      = $db_member_brokerage_note->get_member_brokerage_info($bid, $member_guid);
        $member_brokerage_cash_type = $config['member_brokerage_cash_type'];
        $member_brokerage_cash_type = explode(',', $member_brokerage_cash_type);
        if (empty(array_filter($member_brokerage_cash_type))) {
            error('该功能尚未启用');
        }
        $list       = [
            '微信'   => 'wechat',
            '支付宝' => 'alipay',
            '银行卡' => 'bank',
        ];
        $type_array = [];
        foreach ($member_brokerage_cash_type as $type) {
            $type_array[] = $list[$type];
        }
        $default_last_cash_note = [
            'pay_type'         => reset($type_array),
            'wechat_true_name' => '',
            'wechat_account'   => '',

            'alipay_true_name' => '',
            'alipay_account'   => '',
            'bank_true_name'   => '',
            'bank_name'        => '',
            'bank_account'     => '',
        ];

        $db             = new MemberBrokerageCashNote();
        $map            = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
        ];
        $last_cash_note = $db->where($map)->findOrEmpty();
        $pay_type_list  = [];
        foreach ($list as $key => $val) {
            $pay_type_list[$val] = in_array($key, $member_brokerage_cash_type);
        }
        $data = [
            //           'member_brokerage'                    => $member_brokerage, //账户可用余额
            //           'total_auditing_cash_brokerage_money' => $total_auditing_cash_brokerage_money, //审核中提现金额
            //           'available_cash_brokerage_money'      => $available_cash_brokerage_money, //可提现金额
            'cash_max_day'   => -1, //每天最大可提现金额
            'bank'           => '',
            'pay_type_list'  => $pay_type_list,
            //           'pay_type_list'  => [
            //               'wechat' => true,
            //               'alipay' => false,
            //               'bank'   => false,
            //           ],
            'last_cash_note' => !$last_cash_note->isEmpty() ? $last_cash_note : $default_last_cash_note,
        ];

        result(array_merge($data, $member_brokerage_info));
    }


    /**
     *
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_info()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        //统计佣金数据
        $db_member_brokerage_note = new MemberBrokerageNote();
        //    'total_success_cash_brokerage_money'        => $total_success_cash_brokerage_money,  //累计成功提现佣金
        //           'total_auditing_cash_brokerage_money'       => $total_auditing_cash_brokerage_money, //累计审核中佣金
        //           'total_waiting_for_payment_brokerage_money' => $total_waiting_for_payment_brokerage_money, //累计审核通过待打款金额
        //           'available_cash_brokerage_money'            => $available_cash_brokerage_money, //可提现金额
        //           'total_brokerage_money'                     => $total_brokerage_money,//分销佣金
        //           'total_brokerage_order_money'               => $total_brokerage_order_money,//分销订单总额
        $member_brokerage_info = $db_member_brokerage_note->get_member_brokerage_info($bid, $member_guid);
        //统计分销数量
        $db_member                                         = new Member();
        $map_member                                        = [['bid', '=', $bid], ['share_member_guid', '=', $member_guid]];
        $total_share_member_count                          = $db_member->where($map_member)->count();
        $member_brokerage_info['total_share_member_count'] = $total_share_member_count; //我的团队
        //       $member_brokerage_info                                      = [
        //           'total_success_cash_brokerage_money'        => $total_success_cash_brokerage_money,  //累计成功提现佣金
        //           'total_auditing_cash_brokerage_money'       => $total_auditing_cash_brokerage_money, //累计审核中佣金
        //           'total_waiting_for_payment_brokerage_money' => $total_waiting_for_payment_brokerage_money, //累计审核通过待打款金额
        //           'available_cash_brokerage_money'            => $available_cash_brokerage_money, //可提现金额
        //           'total_brokerage_money'                     => $total_brokerage_money,//分销佣金
        //           'total_brokerage_order_money'               => $total_brokerage_order_money,//分销订单总额
        //           'total_share_member_count'                  => $total_share_member_count, //我的团队
        //       ];
        result($member_brokerage_info);
    }


}