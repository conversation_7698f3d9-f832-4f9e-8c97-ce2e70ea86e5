<?php

namespace app\controller\member_api\v1;

use app\model\MemberMoneyNote as MemberMoneyNoteModel;
use Exception;

class MemberMoneyNote extends BasicMemberApi
{
    /**
     *充值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $member_guid          = $this->get_member_guid();
        $bid                  = $this->get_bid();
        $params               = $this->params;
        $guid                 = $params['guid'];
        $db_member_money_note = new MemberMoneyNoteModel();
        $map                  = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['guid', '=', $guid],
        ];
        $info                 = $db_member_money_note->where($map)->findOrFail();
        $operator_user_guid   = $info['operator_user_guid'];
        $map_user             = [
            ['bid', '=', $bid],
            ['guid', '=', $operator_user_guid],
        ];
        $db_user              = new \app\model\User();
        $user_info            = $db_user->where($map_user)->find();
        $info['user_account'] = $user_info['account'];
        $map_store            = [
            ['bid', '=', $bid],
            ['guid', '=', $user_info['store_guid']],
        ];
        $db_store             = new \app\model\Store();
        $store_info           = $db_store->where($map_store)->find();
        $info['store_name']   = $store_info['store_name'] ?? '';
        result($info);
    }

    /**
     *充值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $member_guid          = $this->get_member_guid();
        $bid                  = $this->get_bid();
        $map                  = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
        ];
        $db_member            = new \app\model\Member();
        $money                = $db_member->where($map)->value('money');
        $db_member_money_note = new MemberMoneyNoteModel();
        $map                  = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
        ];
        $this->model          = $db_member_money_note->where($map)->order(['create_time' => 'DESC']);
        $data                 = [
            'balance' => [
                'money' => number_format($money, 2, '.', ''),
            ],
            'list'    => $this->_list(),
        ];
        result($this->_list());
    }
}