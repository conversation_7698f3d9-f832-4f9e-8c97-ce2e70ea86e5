<?php

namespace app\controller\member_api\v1;

use app\model\Banner;
use app\model\Goods as GoodsModel;
use app\model\GoodsTraceCode;
use app\model\GoodsTraceCodeQueryNote;

class GoodsTrace extends BasicMemberApi
{
    public function verify_code()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $code                 = $params['code'];
        $db_goods_trace_code  = new GoodsTraceCode();
        $map_goods_trace_code = [
            ['bid', '=', $bid],
            ['code', '=', $code],
        ];
        $trace_info           = $db_goods_trace_code->where($map_goods_trace_code)->findOrEmpty();
        if ($trace_info->isEmpty()) {
            error('防伪码' . $code . '不存在');
        }
        $db_goods_trace_code->add_query_times($bid, $code);
        $db_goods_trace_code_query_note  = new GoodsTraceCodeQueryNote();
        $map_goods_trace_code_query_note = [
            ['bid', '=', $bid],
            ['code', '=', $code],
        ];
        $goods_trace_code_query_note     = $db_goods_trace_code_query_note->where($map_goods_trace_code_query_note)->order(['query_time' => 'ASC'])->findOrFail();
        $total_query_times               = $db_goods_trace_code_query_note->where($map_goods_trace_code_query_note)->count();
        $first_query_time                = $goods_trace_code_query_note['query_time'];
        $message                         = '当前防伪密码已被查询' . $total_query_times . '次，第一次查询时间为:' . $first_query_time . ';如果您不是第一次查询，请谨防假冒!';
        if ($total_query_times == 1) {
            $config                       = get_config_by_bid($bid);
            $first_goods_trace_query_tips = $config['first_goods_trace_query_tips'];
            if ($first_goods_trace_query_tips) {
                $message = $first_goods_trace_query_tips;
            }
//            $goods_guid = $trace_info['goods_guid'];
//            $db_goods   = new GoodsModel();
//            $map_goods  = [
//                ['bid', '=', $bid],
//                ['guid', '=', $goods_guid],
//            ];
//            $goods_info = $db_goods->where($map_goods)->field(['name'])->findOrFail();
        }
        $data = [
            'first_query_time'  => $first_query_time,
            'total_query_times' => $total_query_times,
            'message'           => $message
        ];
        result($data);
    }

    public function get_config()
    {
        $params = $this->params;
        $q      = $params['q'] ?? null;
        if ($q) {
            $q      = urldecode($q);
            $params = tools()::parse_url_params($q);
        }
        $code          = $params['code'] ?? null;
        $bid           = $this->get_bid();
        $db_banner     = new Banner();
        $db_business   = new \app\model\Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $business_name = $business_info ['business_name'];
        $config        = get_config_by_bid($bid);
        $banner_list   = $db_banner->get_banner_list($bid, 10);
        if (empty(count($banner_list))) {
            $img_url     = get_system_config('default_goods_trace_banner_image_url');
            $img_url     = tools()::add_thumbnail($img_url);
            $banner_list = [
                [
                    "type"      => 10,
                    "title"     => "",
                    "img_url"   => $img_url,
                    "path"      => "",
                    "url"       => "",
                    "sort"      => 1,
                    "status"    => 1,
                    "video_url" => "",
                    "width"     => "100%",
                ]
            ];
        }
        $config['goods_trace_notice'] = tools()::add_rich_img_class($config['goods_trace_notice']);
        $data                         = [
            'title'              => $business_name,
            'goods_trace_notice' => $config['goods_trace_notice'],
            'pick_up_notice_pop' => $config['pick_up_notice_pop'],
            'show_scan_button'   => (bool)$config['show_scan_button'],
            'banner_list'        => $banner_list,
        ];
        result($data);
    }


    public function detail()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $params      = !empty($params['c']) ? $params : tools()::parse_url_params(urldecode(($params['q'] ?? ''))); //小程序扫码短链
        $code        = $params['c'] ?? '';
        if (empty($code)) {
            error('请求路径不正确');
        }
        $db_goods_trace_code  = new GoodsTraceCode();
        $map_goods_trace_code = [
            ['bid', '=', $bid],
            ['code', '=', $code],
        ];
        $trace_info           = $db_goods_trace_code->where($map_goods_trace_code)->findOrEmpty();
        if ($trace_info->isEmpty()) {
            error('溯源码' . $code . '不存在');
        }
        $db_goods_trace_code->add_query_times($bid, $code);

        $goods_guid = $trace_info['goods_guid'];
        $map        = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $db_goods   = new GoodsModel();
        $goods_info = $db_goods->where($map)->findOrEmpty();
        if ($goods_info->isEmpty()) {
            error('商品可能已经被删除');
        }
        $goods_info['description'] = tools()::remove_taobao_jd_class($goods_info['description']);
        $goods_info['description'] = tools()::add_rich_img_class($goods_info['description']);
        $goods_info                = $db_goods->buildPic($goods_info);
        $business_config           = get_config_by_bid($bid);
        $config['weapp_username']  = $business_config['weapp_username'];
        result(['goods_info' => $goods_info, 'config' => $config]);
    }
}