<?php

namespace app\controller\member_api\v1;

use app\model\Member as MemberModel;
use app\common\service\SmsService;
use app\common\service\TokenService;
use app\common\service\UrlService;
use Exception;

class Member extends BasicMemberApi
{

    /**
     *会员详情
     * @access public
     * @return void
     * @throws Exception
     */
    public function pay_qrcode()
    {
        $db          = new MemberModel();
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $map         = ['bid' => $bid, 'guid' => $member_guid];
        $member_info = $db->get_member_info($map);
        $exp         = 300;
        $jti         = create_guid();
        $token       = [
            'jti'         => $jti,
            'exp'         => $exp,
            'member_guid' => $member_guid,
            'bid'         => $bid,
        ];
        $jwt         = TokenService::encode($token);
        $long_url    = (string)url('admin/member/deduct_money', ['bid' => $bid, 'token' => $jwt], false, true);
        $short_url   = UrlService::long_to_short($long_url, $exp);
        $full_url    = UrlService::get_full_url($short_url);
        $qrcode_url  = UrlService::text_to_qrcode_url($full_url);
        $config      = get_config_by_bid($bid);
        $money_unit  = $config['money_unit'];
        //        $qrcode_url  = 'https://www.yikayi.net/index/plugins/create_qrcode?size=300&data=http%3A%2F%2Fwww.yikayi.net%2Fu%2F18CaLO';
        $data = ['qrcode_url' => $qrcode_url, 'member_info' => $member_info, 'time' => format_timestamp(), 'money_unit' => $money_unit, 'logo' => $config['logo']];
        result($data);
    }

    /**
     *会员详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function info()
    {
        $db                             = new MemberModel();
        $map                            = ['bid' => $this->get_bid(), 'guid' => $this->get_member_guid()];
        $member_info                    = $db->get_member_info($map);
        $member_info['brokerage_ratio'] = $db->get_member_group_brokerage_ratio($this->get_bid(), $member_info['member_group_guid']);
        result($member_info);
    }

    /**
     *编辑会员
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db            = new MemberModel();
        $data          = $this->params;
        $bid           = $this->get_bid();
        $member_guid   = $this->get_member_guid();
        $data['bid']   = $bid;
        $data['guid']  = $member_guid;
        $map           = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid],
        ];
        $before_mobile = $db->where($map)->value('mobile');
        if (isset($data['mobile']) && $data['mobile'] && $data['mobile'] != $before_mobile) {
            if (empty($data['code'])) {
                error('修改过手机号请输入验证码');
            }
            $sms = SmsService::get_instance($bid);
            $sms->verify_sms_code($data['mobile'], $data['code']);
        }
        $member = $db->update_member_info($data, true);
        result($member);
    }
}