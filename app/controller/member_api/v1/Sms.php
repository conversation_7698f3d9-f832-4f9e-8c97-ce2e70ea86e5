<?php

namespace app\controller\member_api\v1;

use app\common\service\SmsService;
use Exception;

class Sms extends BasicMemberApi
{
    /**
     *发送短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_sms_code()
    {
        $params = $this->params;
        $mobile = $params['mobile'];
        $sms    = SmsService::get_instance($this->get_bid());
        $sms->send_sms_code($mobile);
    }
}