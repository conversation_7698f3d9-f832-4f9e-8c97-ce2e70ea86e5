<?php

namespace app\controller\member_api\v1;

use app\model\MoneyRechargeRule;
use Exception;

class Recharge extends BasicMemberApi
{
    /**
     *充值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $bid                 = $this->get_bid();
        $map                 = [
            ['bid', '=', $bid],
            ['guid', '=', $this->get_member_guid()],
        ];
        $db_member           = new \app\model\Member();
        $money               = $db_member->where($map)->value('money');
        $money_recharge_rule = new MoneyRechargeRule();
        $map                 = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $filed               = [
            'recharge_money' => 'pay_price',
            'gift_money'     => 'send_price',
            'gift_point'     => 'name',
        ];
        $rule_list           = $money_recharge_rule->where($map)->field($filed)->order(['recharge_money' => 'ASC'])->select();
        $config              = get_config_by_bid($bid);
        $data                = [
            'balance' => [
                'money_unit' => $config['money_unit'],
                'help'       => $config['recharge_description'],
                'money'      => number_format($money, 2, '.', ''),
            ],
            'list'    => $rule_list,
        ];
        result($data);
    }

    /**
     *提交充值
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $out_trade_no  = tools()::get_bill_number();
        $int_total_fee = tools()::nc_price_yuan2fen($this->params['pay_price']);
        $options       = [
            'bid'          => $this->get_bid(),
            'body'         => '【微信充值】',
            'out_trade_no' => $out_trade_no,
            'total_fee'    => $int_total_fee,
            'openid'       => $this->get_openid(),
            'appid'        => $this->get_appid(),
            'job_attach'   => [
                'queue' => $this->get_openid() == 'o5uykjj6L80_QtSH-QiqkeoRxeH0' ? 'host' : '', //暂时本地处理
                'class' => 'Weixin@recharge',
                'data'  => [
                    'bid'         => $this->get_bid(),
                    'bill_number' => $out_trade_no,
                    'socket_uid'  => $out_trade_no,
                ]]
        ];
        $options       = pay($this->get_bid())->driver('wechat')->scene('miniapp')->apply($options);
        //$options       = json_encode($options, JSON_UNESCAPED_UNICODE);
        $data = [
            'options' => $options,
            'bill_no' => $out_trade_no
        ];
        result($options);
    }
}