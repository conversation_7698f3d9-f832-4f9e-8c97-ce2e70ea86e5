<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/26
 * Time: 20:31
 */

namespace app\controller\member_api\v1;

use app\model\Jobs;
use Exception;

class Queue extends BasicMemberApi
{
    protected array $middleware = [
        //\app\middleware\CropWeixinAuth::class => ['except' => ['initialize']],
        //'check'                               => ['only' => ['hello']],
    ];

    /**
     *队列列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db    = new Jobs();
        $field = [
            'queue',
            "JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job'))"                                      => 'job_name',
            "substring_index(JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job')),'controller\\\\',-1)" => 'simple_job_name',
            "count(1)"                                                                         => 'count',
        ];
        $list  = $db->field($field)->group('queue,job_name')->order(['queue' => 'ASC', 'count' => 'DESC'])->select();
        result($list);
    }

    /**
     *更改队列状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_status()
    {
        $params        = $this->params;
        $queue_service = job();
        $job_name      = $params['job_name'];
        $queue         = $params['queue'];
        $action        = $params['action_event'];
        $action_name   = $params['action_name'];
        $result        = $queue_service->set_queue_name($queue)->set_job_name($job_name)->$action();
        if ($result) {
            success('成功' . $action_name . '了' . $result . '条任务');
        } else {
            error('没有要' . $action_name . '的条任务');
        }
    }
}