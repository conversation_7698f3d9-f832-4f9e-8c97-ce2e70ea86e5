<?php


namespace app\controller\member_api\v1;

use app\model\YkyGoodsType;
use app\model\YkyMemberPublishGoods;
use Exception;
use xieyongfa\yky\Yky;

class Barter extends BasicMemberApi
{
    /**
     *获取会员信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function get_member_info()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $yky    = new \OpenApi\Yky($config);
        $result = $yky->get_member_info_by_openid($this->get_openid());
        if ($result === false) {
            error($yky->message);
        }
        return $result;
    }

    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_type_list()
    {
        $bid             = $this->get_bid();
        $params          = $this->params;
        $db_goods_type   = new YkyGoodsType();
        $goods_type_list = $db_goods_type->get_goods_type($bid);
        $data            = ['goods_type_list' => $goods_type_list];
        result($data);
    }

    /**
     *删除商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function delete_goods()
    {
        $bid            = $this->get_bid();
        $params         = $this->params;
        $goods_guid     = $params['guid'];
        $db             = new YkyMemberPublishGoods();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $member_info    = $this->get_member_info();
        $member_card_id = $member_info['CardId'];
        if (!$this->is_super_member($member_card_id)) {
            $member_guid = $member_info['MemberGuid'];
            $map[]       = ['member_guid', '=', $member_guid];
        }
        $goods_info = $db->where($map)->findOrFail();
        $config     = get_config_by_bid($bid);
        $yky        = Yky::Goods($config);
        $data       = ['guid' => $goods_info['goods_item_guid']];
        $result     = $yky->Delete_GoodsItem($data);
        if ($result === false) {
            error($yky->message);
        }
        $goods_data = ['delete_time' => format_timestamp()];
        $db::update($goods_data, $map);
        success('删除成功');
    }

    /**
     *商品详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_detail()
    {
        $bid             = $this->get_bid();
        $params          = $this->params;
        $goods_guid      = $params['guid'];
        $db              = new YkyMemberPublishGoods();
        $map             = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $member_info     = $this->get_member_info();
        $member_card_id  = $member_info['CardId'];
        $is_super_member = $this->is_super_member($member_card_id);
        if (!$is_super_member) {
            $member_guid = $member_info['MemberGuid'];
            $map[]       = ['member_guid', '=', $member_guid];
        }
        $goods_info      = $db->where($map)->findOrFail();
        $db_goods_type   = new YkyGoodsType();
        $goods_type_list = $db_goods_type->get_goods_type($bid);
        $data            = [
            'goods_detail'    => $goods_info,
            'is_super_member' => $is_super_member,
            'goods_type_list' => $goods_type_list
        ];
        result($data);
    }

    /**
     *保存商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit_goods()
    {
        $bid            = $this->get_bid();
        $params         = $this->params;
        $guid           = $params['guid'];
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $member_info    = $this->get_member_info();
        $member_guid    = $member_info['MemberGuid'];
        $member_card_id = $member_info['CardId'];
        if (!$this->is_super_member($member_card_id)) {
            $map[] = ['member_guid', '=', $member_guid];
        }
        $db         = new YkyMemberPublishGoods();
        $goods_info = $db->where($map)->findOrFail();
        $config     = get_config_by_bid($bid);
        $yky        = Yky::Goods($config);
        $data       = [
            'guid'              => $goods_info['goods_item_guid'],
            'name'              => $params['goods_name'],
            'price'             => $params['goods_price'],
            'referencePrice'    => $params['goods_price'],
            'marketPrice'       => $params['goods_price'],
            'mallPrice'         => $params['goods_price'],
            'goodsItemTypeGuid' => $params['goods_item_type_guid'],
            'description'       => $params['description'],
            'barCode'           => time(),
            'isPublishToMall'   => (bool)$params['is_publish_to_mall'],
            'imagePath'         => '',
            'full_image_path'   => ''
        ];
        $file_list  = $params['file_list'];
        if ($file_list && !is_debug()) {
            $domain = 'https://files.1card1.cn';
            if (strpos($file_list, $domain) === false) {
                $yky_upload    = Yky::Upload($config);
                $upload_result = $yky_upload->UploadImage($file_list);
                if ($upload_result === false) {
                    error($yky_upload->message);
                }
                $data['imagePath']       = $upload_result['imagePath'];
                $data['full_image_path'] = $upload_result['imageFullPath'];
            } else {
                //https://files.1card1.cn/OpenApi/159168/20210418/d1483748b4984ca48a08049029b90059.jpg
                $data['imagePath']       = str_replace($domain, '', $file_list);
                $data['full_image_path'] = $file_list;
            }
        }
        $result = $yky->Edit_GoodsItem($data);
        if ($result === false) {
            error($yky->message);
        }
        $goods_data = [
            'goods_name'           => $params['goods_name'],
            'goods_price'          => $params['goods_price'],
            'goods_item_type_guid' => $params['goods_item_type_guid'],
            'description'          => $params['description'],
            'is_publish_to_mall'   => $params['is_publish_to_mall'],
            'image_path'           => $data['imagePath'],
            'full_image_path'      => $data['full_image_path'],
        ];
        $db::update($goods_data, $map);
        result($result, '修改成功');
    }

    /**
     *发布商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function publish_goods()
    {
        $bid       = $this->get_bid();
        $params    = $this->params;
        $config    = get_config_by_bid($bid);
        $yky       = Yky::Goods($config);
        $data      = [
            'name'              => $params['goods_name'],
            'price'             => $params['goods_price'],
            'referencePrice'    => $params['goods_price'],
            'marketPrice'       => $params['goods_price'],
            'mallPrice'         => $params['goods_price'],
            'goodsItemTypeGuid' => $params['goods_item_type_guid'],
            'description'       => $params['description'],
            'barCode'           => time(),
            'isPublishToMall'   => true,
            'imagePath'         => '',
            'full_image_path'   => ''
        ];
        $file_list = $params['file_list'];
        if ($file_list && !is_debug()) {
            $domain = 'https://files.1card1.cn';
            if (strpos($file_list, $domain) === false) {
                $yky_upload    = Yky::Upload($config);
                $upload_result = $yky_upload->UploadImage($file_list);
                if ($upload_result === false) {
                    error($yky_upload->message);
                }
                $data['imagePath']       = $upload_result['imagePath'];
                $data['full_image_path'] = $upload_result['imageFullPath'];
            } else {
                //https://files.1card1.cn/OpenApi/159168/20210418/d1483748b4984ca48a08049029b90059.jpg
                $data['imagePath']       = str_replace($domain, '', $file_list);
                $data['full_image_path'] = $file_list;
            }
        }
        $result = $yky->Add_GoodsItem($data);
        if ($result === false) {
            error($yky->message);
        }
        $member_info    = $this->get_member_info();
        $member_guid    = $member_info['MemberGuid'];
        $member_card_id = $member_info['CardId'];
        $member_mobile  = $member_info['Mobile'];
        $goods_data     = [
            'guid'                 => create_guid(),
            'bid'                  => $bid,
            'member_guid'          => $member_guid,
            'goods_name'           => $params['goods_name'],
            'mobile'               => $member_mobile,
            'goods_price'          => $params['goods_price'],
            'goods_item_type_guid' => $params['goods_item_type_guid'],
            'description'          => $params['description'],
            'is_publish_to_mall'   => 1,
            'goods_item_guid'      => $result['goodsItemGuid'],
            'image_path'           => $data['imagePath'],
            'full_image_path'      => $data['full_image_path']
        ];
        $db             = new YkyMemberPublishGoods();
        $db->save($goods_data);
        result($result, '发布成功');
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_list()
    {
        $bid            = $this->get_bid();
        $params         = $this->params;
        $member_info    = $this->get_member_info();
        $member_card_id = $member_info['CardId'];
        $db             = new YkyMemberPublishGoods();
        $map            = [
            ['bid', '=', $bid],
        ];
        if (!$this->is_super_member($member_card_id)) {
            $member_guid = $member_info['MemberGuid'];
            $map[]       = ['member_guid', '=', $member_guid];
        }
        $list = $db->where($map)->order(['create_time' => 'DESC'])->select();
        result($list);
    }

    /**
     *是否管理员卡号
     * @access public
     * @param string $member_card_id 会员卡号
     * @return bool
     * @throws Exception
     */
    protected function is_super_member($member_card_id)
    {
        $bid                            = $this->get_bid();
        $config                         = get_config_by_bid($bid);
        $super_yky_member_card_id_array = $config['super_yky_member_card_id_array'];
        $super_yky_member_card_id_array = explode(',', $super_yky_member_card_id_array);
        return in_array($member_card_id, $super_yky_member_card_id_array);
    }
}