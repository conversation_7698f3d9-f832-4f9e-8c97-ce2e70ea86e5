<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;

use app\model\Member as MemberModel;
use app\model\StudyImageCompositingNote;
use Imagick;
use ImagickDraw;
use Intervention\Image\AbstractFont;
use Intervention\Image\AbstractShape;
use Intervention\Image\ImageManagerStatic as Image;
use Intervention\Image\Imagick\Driver;

class Study extends BasicMemberApi
{
    /**
     * 字体配置数组
     * 包含所有字体相关的配置信息
     * @var array
     */
    private $fonts_config = [
        [
            'font_name' => '雅黑',
            'css_name'  => 'Microsoft YaHei',
            'file_name' => 'MSYH.TTC'
        ],
        [
            'font_name' => '宋体',
            'css_name'  => 'SimSun',
            'file_name' => 'SONGTI.TTF'
        ],
        [
            'font_name' => '行书',
            'css_name'  => 'STXingkai',
            'file_name' => 'XINGSHU.TTF'
        ],
        [
            'font_name' => '楷体',
            'css_name'  => 'KaiTi',
            'file_name' => 'KAISHU.ttf'
        ],
        [
            'font_name' => '隶书',
            'css_name'  => 'STLiti',
            'file_name' => 'LISHU.ttf'
        ]
    ];

    /**
     * 处理文本自动换行（优化版）
     *
     * @param string $text 原始文本
     * @param int $image_width 图片宽度
     * @param int $font_size 字体大小
     * @param int $x 起始X坐标
     * @param string $font 字体路径
     * @param int $max_width 最大宽度（默认为图片宽度减去左右边距）
     * @return array 处理后的文本行数组
     */
    private function format_text($text, $image_width, $font_size, $x, $font, $max_width = null)
    {
        // 如果未指定最大宽度，则使用图片宽度减去左边距和固定右边距
        if ($max_width === null) {
            $max_width = $image_width - $x - 20; // 固定右边距20px
        }

        // 如果文本为空，直接返回空数组
        if (empty($text)) {
            return [];
        }

        // 首先按换行符分割原始文本，保留原始的换行
        $original_lines = explode("\n", $text);
        $final_lines    = [];

        // 处理每一个原始行
        foreach ($original_lines as $original_line) {
            // 如果原始行为空，添加一个空行并继续
            if (empty(trim($original_line))) {
                $final_lines[] = '';
                continue;
            }

            // 预处理：将英文单词、数字和标点符号作为整体处理
            $pattern = '/([a-zA-Z0-9]+|[^\p{L}\p{N}\s]|[\p{Han}])/u';
            preg_match_all($pattern, $original_line, $matches);
            $words = $matches[0]; // 获取所有匹配项

            $current_line  = '';
            $current_width = 0;

            foreach ($words as $word) {
                // 计算当前单词的宽度
                $word_dimensions = $this->get_text_dimensions($word, $font, $font_size);
                $word_width      = $word_dimensions['width'];

                // 计算添加单词后的总宽度
                $temp_line       = $current_line . $word;
                $temp_dimensions = $this->get_text_dimensions($temp_line, $font, $font_size);
                $temp_width      = $temp_dimensions['width'];

                // 如果添加单词后超过最大宽度，则换行
                // 但如果当前行为空，则强制添加该单词（避免单词太长导致死循环）
                if ($temp_width > $max_width && !empty($current_line)) {
                    $final_lines[] = $current_line;
                    $current_line  = $word;
                    $current_width = $word_width;
                } else {
                    $current_line  = $temp_line;
                    $current_width = $temp_width;
                }

                // 特殊处理：如果是中文字符且当前行已经接近最大宽度，考虑提前换行
                if (preg_match('/\p{Han}/u', $word) && ($max_width - $current_width) < $font_size) {
                    $final_lines[] = $current_line;
                    $current_line  = '';
                    $current_width = 0;
                }
            }

            // 添加当前原始行的最后一行
            if (!empty($current_line)) {
                $final_lines[] = $current_line;
            }
        }

        return $final_lines;
    }

    public function image_compositing()
    {
        // 配置图像驱动，优先使用imagick
        $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
        Image::configure(['driver' => $driver_name]);
        $params               = $this->params;
        $background_image_url = $params['background_image'] ?? 'http://www.yikayi.net/static/img/member/background/0.jpg';
        $text                 = $params['text'] ?? format_timestamp() . '默认文字';

        $text_position = $params['text_position'] ?? [
            'x' => 0.02, // 增加默认左边距
            'y' => 0.02// 增加默认上边距
        ]; // 文字位置（默认左上角）

        $x = $text_position['x'];
        $y = $text_position['y'];
        // 生成唯一的文件名
        $file_name     = md5($background_image_url . $text . time()) . '.png';
        $file_path     = '/temp/images/' . $file_name;
        $absolute_path = tools()::get_absolute_path($file_path);

        // 确保目录存在
        $dir = dirname($absolute_path);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        // 将背景图片URL转换为本地绝对路径
//        $background_image_local = tools()::web_url_to_local_absolute_path($background_image_url);

        // 打开背景图片
        $image = Image::make($background_image_url);

        // 获取图片尺寸
        $image_width  = $image->width();
        $image_height = $image->height();
        // 定义最大图片宽度变量
        $max_image_width = 1000;

        // 如果背景图宽度超过最大宽度，进行裁剪
        if ($image_width > $max_image_width) {
            $new_height = intval(($image_height / $image_width) * $max_image_width);
            $image->resize($max_image_width, $new_height);
            $image_width  = $max_image_width;
            $image_height = $new_height;
        }

        $x = $x * $image_width;
        $y = $y * $image_height;
        // 获取字体选择，支持font或family参数，默认使用雅黑
        $selected_font = $params['text_style']['font'] ?? $params['text_style']['family'] ?? '雅黑';

        // 从字体配置中获取字体文件
        $font_file = 'MSYH.TTC'; // 默认雅黑字体
        foreach ($this->fonts_config as $font_config) {
            if ($font_config['font_name'] === $selected_font || $font_config['css_name'] === $selected_font) {
                $font_file = $font_config['file_name'];
                break;
            }
        }

        // 设置字体路径
        $font = tools()::get_absolute_path('/static/css/fonts/' . $font_file);

        // 主文字颜色和大小设置
        $font_size  = isset($params['text_style']['size']) ? intval($params['text_style']['size']) : 20;
        $font_size  = (int)($font_size * ($image_width / 600));
        $font_color = $params['text_style']['color'] ?? '#FFFFFF';

        // 处理文本自动换行，明确指定最大宽度
        $max_width  = $image_width - $x - 10; // 左边距为x，右边距为60px
        $text_lines = $this->format_text($text, $image_width, $font_size, $x, $font, $max_width);

        // 逐行添加文本
        $line_height = $font_size * 1.2; // 增加行高，提高可读性
        $current_y   = $y;

        foreach ($text_lines as $line) {
            // 绘制主文字，添加类型提示
            $image->text($line, $x, $current_y, function (AbstractFont $font_draw) use ($font_size, $font_color, $font) {
                $font_draw->file($font);
                $font_draw->size($font_size);
                $font_draw->color($font_color);
                $font_draw->align('left');
                $font_draw->valign('top');
                $font_draw->angle(0);
            });
            $current_y += $line_height;
        }

        $db_study_image_compositing_note = new StudyImageCompositingNote();
        $map                             = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
        ];
        $count                           = $db_study_image_compositing_note->where($map)->count();
        $count                           += 1;
        // 获取当前日期
        $current_date = date('Y年m月d日');
        $punch_text   = "第{$count}次打卡";

        // 获取会员信息
        $db            = new MemberModel();
        $map           = ['bid' => $this->get_bid(), 'guid' => $this->get_member_guid()];
        $member_info   = $db->get_member_info($map);
        $member_name   = $member_info['name'] ?? ''; // 若会员姓名不存在，显示未知会员
        $member_avatar = $member_info['head_img'] ?? ''; // 假设会员信息中有 avatar 字段存储头像 URL

        // 处理会员头像
        if ($member_avatar && strpos($member_avatar, 'head_img.png') === false) {
            // 转换为本地路径
            $avatar_local = tools()::web_url_to_local_absolute_path($member_avatar);

            // 检查头像文件是否存在并处理
            if (file_exists($avatar_local)) {
                // 创建头像图像实例
                $avatar = Image::make($avatar_local);
                // 固定头像大小为100像素
                $diameter = 100;
                // 调整头像大小
                $avatar->resize($diameter, $diameter);
                // 创建一个圆形蒙版，添加类型提示
                $mask = Image::canvas($diameter, $diameter);
                $mask->circle($diameter, $diameter / 2, $diameter / 2, function (AbstractShape $draw) {
                    $draw->background('#ffffff');
                });

                // 应用蒙版使头像变为圆形
                $avatar->mask($mask, false);

                // 将头像插入到主图像中
                $pos_x = 20;  // 固定在左侧20像素处
                $pos_y = 20;  // 固定在顶部20像素处

                $image->insert($avatar, 'top-left', $pos_x, $pos_y);
            }
        }
        // 计算会员姓名文字的位置
        $name_x         = 50;
        $name_y         = 160; // 会员姓名在打卡文字上方
        $name_font_size = $font_size * 0.6;

        // 文字颜色
        $mark_color = '#000000'; // 使用黑色文字，因为背景是白色的

        // 半透明背景颜色
        $bg_opacity = 0.4; // 40%不透明度

        // 计算所有文本的背景位置

        // 绘制会员姓名（带背景）
        if ($member_name) {
            $name_bg_info = $this->composite_text_with_background($image, $member_name, $name_font_size, $font, $mark_color, $name_x, $name_y, '#FFFFFF', $bg_opacity, false);
        }

        // 计算打卡文字的位置
        $punch_x         = $image_width - 20; // 稍微向左移动一点，给右边留出空间
        $punch_y         = $image_height - 150; // 调整垂直位置，确保不会太靠下
        $punch_font_size = $font_size * 0.9;

        // 绘制打卡文字（带背景）
        $punch_bg_info = $this->composite_text_with_background($image, $punch_text, $punch_font_size, $font, $mark_color, $punch_x, $punch_y, '#FFFFFF', $bg_opacity, true);

        // 计算日期文字的位置
        $date_x         = $image_width - 20; // 与打卡文字保持相同的水平对齐
        $date_y         = $image_height - 80; // 调整垂直位置，确保与打卡文字有适当间距
        $date_font_size = $font_size * 0.9;

        // 绘制日期文字（带背景）
        $date_bg_info = $this->composite_text_with_background($image, $current_date, $date_font_size, $font, $mark_color, $date_x, $date_y, '#FFFFFF', $bg_opacity, true);

        // 保存合成后的图片
        $image->save($absolute_path);

        // 返回合成后的图片URL
        $image_url = tools()::path_to_web($file_path);
        $params    = $this->params;
        unset($params['access_token']);
        $save_data = [
            'bid'         => $this->get_bid(),
            'member_guid' => $this->get_member_guid(),
            'image_url'   => $image_url,
            'content'     => $text,
            'data'        => $params
        ];
        $db_study_image_compositing_note->save($save_data);

        if ($this->request->isGet()) {
            return "<img style='height: 100%' src='{$image_url}' />";
        } else {
            result(['image_url' => $image_url, 'font_size' => $font_size]);
        }
    }

    /**
     * 获取样式配置，包含背景图片列表、字体颜色列表和字体列表
     */
    public function get_style_config()
    {
        // 获取背景图片列表
        $background_dir = root_path() . 'public/';
        $path           = 'static/img/member/background';
        $image_list     = [];
        $file_list      = tools()::scan_dir($background_dir . $path);
        foreach ($file_list as $file) {
            $file_name    = basename($file);
            $image_url    = request()->domain() . '/' . $path . '/' . $file_name;
            $image_list[] = [
                'name' => $file_name,
                'url'  => $image_url
            ];
        }


        // 定义字体颜色列表
        $text_colors = [
            [
                'name'  => '白色',
                'value' => '#ffffff'
            ],
            [
                'name'  => '橙色',
                'value' => '#ff9900'
            ],
            [
                'name'  => '红色',
                'value' => '#ff0000'
            ],
            [
                'name'  => '蓝色',
                'value' => '#0000ff'
            ],
            [
                'name'  => '绿色',
                'value' => '#00ff00'
            ],
            [
                'name'  => '黄色',
                'value' => '#ffff00'
            ],
            [
                'name'  => '紫色',
                'value' => '#800080'
            ],
            [
                'name'  => '黑色',
                'value' => '#000000'
            ]
        ];
        // 返回合并后的数据
        result([
            'background_images'        => $image_list,
            'text_colors'              => $text_colors,
            'fonts'                    => $this->fonts_config,
            'preview_font_size'        => 16,
            'preview_font_line_height' => 1.2,
            'base_font_size'           => 24,
        ]);
    }


    /**
     * 获取文本尺寸（使用Imagick）
     *
     * @param string $text 要测量的文本
     * @param string $font 字体文件路径
     * @param int $font_size 字体大小
     * @return array 包含宽度和高度的数组 ['width' => float, 'height' => float]
     */
    private function get_text_dimensions($text, $font, $font_size)
    {
        try {
            $draw = new ImagickDraw();
            $draw->setFont($font);
            $draw->setFontSize($font_size);

            $images  = new Imagick();
            $metrics = $images->queryFontMetrics($draw, $text);

            return [
                'width'  => $metrics['textWidth'],
                'height' => $metrics['textHeight']
            ];
        } catch (Exception $e) {
            // 如果Imagick不可用，回退到imagettfbbox
            $box = imagettfbbox($font_size, 0, $font, $text);
            return [
                'width'  => $box[2] - $box[0],
                'height' => $box[3] - $box[7]
            ];
        }
    }

    /**
     * 合成文字并添加背景到图片上
     *
     * 该方法使用 Intervention/Image 库在指定图片上绘制一个背景矩形，
     * 然后将指定的文字显示在背景上。
     *
     * @param \Intervention\Image\Image $image Intervention\Image\Image 图像对象
     * @param string $text 要合成到图片上的文字内容
     * @param int $font_size 文字的字体大小
     * @param string $font 字体文件的绝对路径，用于指定文字的字体
     * @param string $text_color 文字的颜色（十六进制颜色代码）
     * @param int $x 文字起始 X 坐标
     * @param int $y 文字起始 Y 坐标
     * @param string $bg_color 背景颜色（十六进制颜色代码）
     * @param float $bg_opacity 背景透明度（0-1之间的值，1为不透明）
     * @param bool $align_right 是否右对齐文字
     * @return array 返回背景信息数组，可用于后续绘制
     */
    private function composite_text_with_background(\Intervention\Image\Image $image, $text, $font_size, $font, $text_color, $x, $y, $bg_color = '#FFFFFF', $bg_opacity = 0.4, $align_right = false)
    {
        $x = (int)$x;
        $y = (int)$y;

        // 获取文本尺寸
        $dimensions  = $this->get_text_dimensions($text, $font, $font_size);
        $text_width  = $dimensions['width'];
        $text_height = $dimensions['height'];

        // 设置内边距
        $base_padding   = 10;
        $base_font_size = 16;
        $rate           = ($font_size / $base_font_size);
        $padding        = $base_padding * $rate;
        // 计算背景条的宽度和高度（包含内边距）
        $bg_width  = (int)($text_width + 2 * $padding); // 左右内边距各padding
        $bg_height = (int)($text_height + 2 * $padding); // 上下内边距各padding

        // 背景条左上角坐标
        $bg_x = (int)($x - $padding);
        $bg_y = (int)($y - $padding);

        // 如果需要右对齐，调整位置
        if ($align_right) {
            $x    = $x - $bg_width;
            $y    = $y - $bg_height;
            $bg_x = $bg_x - $bg_width;
            $bg_y = $bg_y - $bg_height;
        }

        // 创建背景信息数组
        $bg_info = [
            'bg_x'        => $bg_x,
            'bg_y'        => $bg_y,
            'bg_width'    => $bg_width,
            'bg_height'   => $bg_height,
            'text_width'  => $text_width,
            'text_height' => $text_height,
            'text_x'      => $x,
            'text_y'      => $y,
            'padding'     => $padding,
            'font_size'   => $font_size,
        ];
//          error(json_encode($bg_info));
        // 创建半透明背景颜色
        $bg_color_with_opacity = $bg_color;
        if ($bg_opacity < 1) {
            // 将十六进制颜色转换为RGBA
            $r                     = hexdec(substr($bg_color, 1, 2));
            $g                     = hexdec(substr($bg_color, 3, 2));
            $b                     = hexdec(substr($bg_color, 5, 2));
            $bg_color_with_opacity = "rgba($r, $g, $b, $bg_opacity)";
        }


        // 使用 Imagick 绘制半圆
        if ($image->getDriver() instanceof Driver) {
            $imagick = $image->getCore();
            $draw    = new \ImagickDraw();
            $draw->setFillColor($bg_color_with_opacity);

            // 左半圆
            $draw->arc(
                ($bg_x - ($bg_height / 2)) - 1, // 左半圆的起始 x 坐标
                $bg_y,// 左半圆的起始 y 坐标（位于矩形中心）
                ($bg_x + ($bg_height / 2)) - 1,// 左半圆的结束 x 坐标（位于矩形中心）
                $bg_y + $bg_height,// 左半圆的结束 y 坐标
                90,
                270
            );
            // 右半圆
            $draw->arc(
                ($bg_x + $bg_width - ($bg_height / 2)) + 1, // 左半圆的起始 x 坐标
                $bg_y,// 左半圆的起始 y 坐标（位于矩形中心）
                ($bg_x + $bg_width + ($bg_height / 2)) + 1,// 左半圆的结束 x 坐标（位于矩形中心）
                $bg_y + $bg_height,// 左半圆的结束 y 坐标
                270,
                90
            );
            $imagick->drawImage($draw);
        }

        // 绘制背景矩形
        $image->rectangle(
            $bg_x,
            $bg_y,
            $bg_x + $bg_width,
            $bg_y + $bg_height,
            function (AbstractShape $rect_draw) use ($bg_color_with_opacity) {
                $rect_draw->background($bg_color_with_opacity);
                $rect_draw->border(0);
            }
        );
        // 绘制文本 - 使用原始坐标，不再需要计算居中位置，添加类型提示
        $image->text($text, $x, $y + $text_height / 2, function (AbstractFont $font_draw) use ($font, $font_size, $text_color) {
            $font_draw->file($font);
            $font_draw->size($font_size);
            $font_draw->color($text_color);
            $font_draw->align('left'); // 默认就是左对齐，可以省略
            $font_draw->valign('center'); // 设置顶部对齐才能让文字左上角 就是XY坐标
        });
        return $bg_info;
    }
}

