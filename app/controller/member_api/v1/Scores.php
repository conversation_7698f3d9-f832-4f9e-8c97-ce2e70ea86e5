<?php

namespace app\controller\member_api\v1;


use app\model\Banner;
use app\model\MemberGroup;
use app\model\MemberGroupArticle;
use app\model\MemberScoreNote;
use Exception;

class Scores extends BasicMemberApi
{
    /**
     * 评分详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db                      = new MemberScoreNote();
        $params                  = $this->params;
        $bid                     = $this->get_bid();
        $guid                    = $params['guid'];
        $map                     = [
            ['sn.bid', '=', $bid],
            ['sn.guid', '=', $guid]
        ];
        $join                    = [
            ['member m', 'sn.member_guid = m.guid AND sn.bid = m.bid'],
            ['member m2', 'sn.operator_member_guid = m2.guid AND sn.bid = m2.bid', 'LEFT'],
        ];
        $field                   = [
            'sn.*',
            'm.name'  => 'member_name',
            'm2.name' => 'operator_member_name',
        ];
        $score_note              = $db->alias('sn')->where($map)->join($join)->field($field)->find();
        $score_note['score_add'] = array_sum($score_note['score_detail']);
        result($score_note);
    }

    /**
     * 提交评分
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $db                   = new MemberScoreNote();
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $operator_member_guid = $this->get_member_guid();
        $db_member            = new \app\model\Member();
        $map                  = [
            ['bid', '=', $bid],
            ['guid', '=', $operator_member_guid]
        ];
        $operator_member_info = $db_member->where($map)->find();
        if ($operator_member_info['user_type'] != 1) {
            error('您没有计分权限');
        }
        $score = count($params['score_detail']) * 20;
        if ($score == 0) {
            error('请选择打分项目');
        }
        $insert_data = [
            'guid'                 => create_guid(),
            'bid'                  => $bid,
            'member_guid'          => $params['member_guid'],
            'images'               => $params['images'],
            'comment'              => $params['comment'] ?? '',
            'operator_member_guid' => $operator_member_guid,
            'score_detail'         => $params['score_detail'],
            'score'                => $score,
            'type'                 => 1,
        ];
        $db->save($insert_data);
        success('打分成功');
    }

    /**
     * 会员二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_qrcode()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $appid         = $this->get_appid();
        $member_guid   = $params['guid'];
        $path          = '/pages/score/submit?member_guid=' . $member_guid;
        $file_path     = '/temp/images/' . md5($appid . $path) . '.png';
        $from          = 'cache';
        $absolute_path = tools()::get_absolute_path($file_path);
        if (!file_exists($absolute_path)) {
            $from   = 'api';
            $mini   = weixin($appid)::WeMiniQrcode();
            $result = $mini->createMiniPath($path, 430, false, ['r' => '0', 'g' => '0', 'b' => '0'], false);
            file_put_contents($absolute_path, $result);
        }
        $data = [
            'from' => $from,
            'src'  => tools()::path_to_web($file_path),
        ];
        result($data);
    }

    /**
     * 获取首页数据
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_home_data()
    {
        $bid           = $this->get_bid();
        $link1         = [
            'html' => 'content',
            'url'  => 'article/detail',
            'data' => ['key' => 'banner1']
        ];
        $link1         = urlencode(json_encode($link1));
        $link2         = [
            'html' => 'content',
            'url'  => 'article/detail',
            'data' => ['key' => 'banner2']
        ];
        $link2         = urlencode(json_encode($link2));
        $banner        = [
            'imgUrls'        => [
                [
                    'link' => '/pages/article/article_detail?data=' . $link1,
                    'url'  => 'http://' . config('app.app_host_domain') . '/file/uploads/201910/19/e320107c-01b1-0bc0-4def-f41456e1ebf3.png',
                ],
                [
                    'link' => '/pages/article/article_detail?data=' . $link2,
                    'url'  => 'http://' . config('app.app_host_domain') . '/file/uploads/201910/20/6fa5936b-637f-1ffd-601a-c764f1a0f040.png',
                ],
            ],
            'indicatorDots'  => true,//小点
            'indicatorColor' => 'white',//指示点颜色
            'activeColor'    => 'coral',//当前选中的指示点颜色
            'autoplay'       => true,//是否自动轮播
            'interval'       => 3000,//间隔时间
            'duration'       => 500,//滑动时间
        ];
        $db_banner     = new Banner();
        $banner_list   = $db_banner->get_banner_list($bid, 6);
        $db_business   = new \app\model\Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $title         = $business_info['business_name'];
        result([
            'title'       => $title,
            'banner'      => $banner,
            'banner_list' => $banner_list
        ]);
    }

    /**
     * 会员详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_detail()
    {
        $params      = $this->params;
        $db_member   = new \app\model\Member();
        $bid         = $this->get_bid();
        $member_guid = $params['guid'];
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $member_guid]
        ];
        //会员信息
        $info                  = $db_member->where($map)->find();
        $db                    = new MemberScoreNote();
        $map                   = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid]
        ];
        $share_member_guid     = $info['share_member_guid'];
        $map2                  = [
            ['bid', '=', $bid],
            ['guid', '=', $share_member_guid]
        ];
        $recommend_member_name = '';
        if ($share_member_guid) {
            $recommend_member_name = $db_member->where($map2)->value('name');
        }
        //$total_score           = $db->where($map)->sum('score');
        $last_month_avg_score  = $db->where($map)->whereMonth('create_time', 'last month')->avg('score');
        $this_month_avg_score  = $db->where($map)->whereMonth('create_time')->avg('score');
        $last_month_score_desc = $this->get_star_info($last_month_avg_score);
        $this_month_score_desc = $this->get_star_info($this_month_avg_score);
        $last_score            = $db->where($map)->order(['create_time' => 'DESC'])->value('score');
        $join                  = [
            ['member m', " sn.operator_member_guid = m.guid AND sn.bid = m.bid", 'LEFT'],
        ];
        $field                 = [
            'sn.*',
            'm.name' => 'operator_member_name',
        ];
        $map                   = [
            ['sn.bid', '=', $bid],
            ['sn.member_guid', '=', $member_guid],
            ['sn.create_time', '>=', date('Y-m-01 00:00:00')],
        ];
        $score_note            = $db->alias('sn')->where($map)->join($join)->field($field)->order(['create_time' => 'DESC'])->select()->toArray();
        foreach ($score_note as $key => $val) {
            $score_note[$key]['create_time'] = date('m月d日', strtotime($val['create_time']));
        }
        $data = [
            //'total_score'          => (int)$total_score,
            'recommend_member_name' => $recommend_member_name,
            'last_score'            => (int)$last_score,
            'last_month_avg_score'  => (int)$last_month_avg_score,
            'this_month_avg_score'  => (int)$this_month_avg_score,
            'last_month_star'       => $last_month_score_desc['star'],
            'last_month_star_desc'  => $last_month_score_desc['star_desc'],
            'this_month_star'       => $this_month_score_desc['star'],
            'this_month_star_desc'  => $this_month_score_desc['star_desc'],
            'score_note'            => $score_note,
            'member_info'           => $info
        ];
        result($data);
    }

    /**
     * 提交评分
     * @access protected
     * @param int $score 分数
     * @return mixed
     * @throws Exception
     */
    protected function get_star_info($score)
    {
        $score = ((int)$score) + 1;
        $data  = [];
        switch ($score) {
            case $score > 90:
                $data['star']      = 5;
                $data['star_desc'] = '五星';
                break;
            case $score > 80:
                $data['star']      = 4;
                $data['star_desc'] = '四星';
                break;
            case $score > 70:
                $data['star']      = 3;
                $data['star_desc'] = '三星';
                break;
            case $score > 1:
                $data['star']      = 2;
                $data['star_desc'] = '不合格';
                break;
            case $score = 1:
                $data['star']      = 1;
                $data['star_desc'] = '未评分';
                break;
            default:
                $data['star']      = 0;
                $data['star_desc'] = '未知';
                break;
        }
        return $data;
    }

    /**
     * 负责人列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function recommend_member_list()
    {
        $params = $this->params;
        $db     = new \app\model\Member();
        $bid    = $this->get_bid();
        $map    = [
            ['m.bid', '=', $bid],
            ['m.share_member_guid', 'not null', null],
            ['m.delete_time', 'null', null],
            ['m2.delete_time', 'null', null],
        ];
        if (!empty($params['member_group_guid'])) {
            //存在member_group_guid 则只取对应级别下的责任人
            $map[] = ['m.member_group_guid', '=', $params['member_group_guid']];
        }
        $list = $db->alias('m')->field(['m2.guid', 'm2.name'])->where($map)->join('member m2', 'm.bid=m2.bid AND m.share_member_guid=m2.guid')->order(['m2.name' => 'ASC'])->group(['m.share_member_guid', 'm2.name'])->select()->toArray();
        array_unshift($list, [
            'name' => '全部',
            'guid' => '',
        ]);
        result($list);
    }

    /**
     * 村子列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_group()
    {
        $db   = new MemberGroup();
        $bid  = $this->get_bid();
        $map  = [
            ['bid', '=', $bid],
            ['status', '=', 1]
        ];
        $list = $db->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC'])->select();
        result($list);
    }

    /**
     * 村子详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_group_detail()
    {
        $params              = $this->params;
        $db                  = new MemberGroup();
        $bid                 = $this->get_bid();
        $map                 = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']]
        ];
        $info                = $db->where($map)->find();
        $info['description'] = tools()::add_rich_img_class($info['description']);
        result($info);
    }

    /**
     * 工作记录详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_group_article_detail()
    {
        $db_member_group_article = new MemberGroupArticle();
        $params                  = $this->params;
        $bid                     = $this->get_bid();
        $guid                    = $params['guid'];
        $map                     = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $result                  = $db_member_group_article->where($map)->find();
        result($result);
    }

    /**
     * 工作记录列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_group_article_list()
    {
        $db_member_group_article = new MemberGroupArticle();
        $params                  = $this->params;
        $bid                     = $this->get_bid();
        $member_group_guid       = $params['guid'];
        $begin_month             = $params['month'] ?? date('m');
        $end_month               = $begin_month + 1;
        $map                     = [
            ['bid', '=', $bid],
            ['member_group_guid', '=', $member_group_guid],
            ['create_time', '>=', date("Y-$begin_month-01 00:00:00")],
            ['create_time', '<', date("Y-$end_month-01 00:00:00")],
        ];
        $order                   = ['create_time' => 'DESC'];
        //分页返回
        $result = $db_member_group_article->where($map)->order($order)->paginate($this->get_paginate_config())->toArray();
        result($result);
    }

    /**
     * 评分排行榜
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function member_group_score_sort()
    {
        $db_member_score_note  = new MemberScoreNote();
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $member_group_guid     = $params['guid'];
        $share_member_guid     = $params['share_member_guid'] ?? null;
        $recommend_member_guid = $params['recommend_member_guid'] ?? null;
        $share_member_guid     = $share_member_guid ?: $recommend_member_guid;
        $search_value          = $params['search_value'] ?? null;
        $begin_month           = $params['month'] ?? date('m');
        $end_month             = $begin_month + 1;
        $map                   = [
            ['m.bid', '=', $bid],
            ['m.name', '<>', ''],
            ['m.delete_time', 'null', NULL],
            ['m.member_group_guid', '=', $member_group_guid],
        ];
        if ($share_member_guid) {
            $map[] = ['m.share_member_guid', '=', $share_member_guid];
        }
        if ($search_value) {
            $map[] = ['m.name|m.room_number', 'LIKE', '%' . $search_value . '%'];
        }
        $sub_map   = [
            ['bid', '=', $bid],
            ['create_time', '>=', date("Y-$begin_month-01 00:00:00")],
            ['create_time', '<', date("Y-$end_month-01 00:00:00")],
        ];
        $sub_field = [
            'bid',
            'member_guid',
            'IFNULL(SUM(score),0)'          => 'total_score',
            'count(*)'                      => 'count',
            'IFNULL(round(AVG(score),2),0)' => 'avg_score',
        ];
        $sub_group = [
            'bid',
            'member_guid',
        ];
        $sub_sql   = $db_member_score_note
            ->where($sub_map)
            ->field($sub_field)
            ->group($sub_group)
            ->buildSql();
        $field     = [
            'm.bid',
            'm.guid',
            'm.head_img',
            "IFNULL(m.name,'匿名')"  => 'name',
            "IFNULL(m2.name,'匿名')" => 'recommend_member_name',
            'IFNULL(total_score,0)'  => 'total_score',
            'IFNULL(count,0)'        => 'count',
            'IFNULL(avg_score,0)'    => 'avg_score',
        ];
        $db_member = new \app\model\Member();
        $order     = ['avg_score' => 'DESC', 'm.id' => 'DESC'];
        //分页返回
        $result           = $db_member->alias('m')->where($map)->join('member m2', 'm.bid = m2.bid AND m.share_member_guid=m2.guid', 'LEFT')->join([$sub_sql => 'msn'], 'm.bid=msn.bid AND m.guid=msn.member_guid', 'LEFT')->field($field)->order($order)->paginate($this->get_paginate_config())->toArray();
        $list             = $result['data'];
        $default_head_img = $db_member->get_default_head_img();
        foreach ($list as $key => $val) {
            $list[$key]['head_img']  = !empty($val['head_img']) ? $val['head_img'] : $default_head_img;
            $avg_score               = (int)$list[$key]['avg_score'];
            $star_info               = $this->get_star_info($avg_score);
            $list[$key]['avg_score'] = $avg_score;
            $list[$key]['star']      = $star_info['star'];
            $list[$key]['star_desc'] = $star_info['star_desc'];
        }
        $result['data'] = $list;
        result($result);

    }
}