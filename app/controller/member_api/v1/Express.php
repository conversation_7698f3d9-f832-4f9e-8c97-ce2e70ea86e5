<?php

namespace app\controller\member_api\v1;


use Exception;

class Express extends BasicMemberApi
{
    /**
     *数据统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function query()
    {
        $params        = $this->params;
        $db_express    = new \app\model\Express();
        $params['way'] = 1;
        $result        = $db_express->query_route($params);
        result($result);
    }
}