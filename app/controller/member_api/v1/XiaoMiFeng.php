<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use app\model\MemberMoneyNote;
use app\model\XmfAccessToken;
use app\model\XmfOrder;
use Exception;
use OpenApi\XiaoMiFengApi;

class XiaoMiFeng extends BasicMemberApi
{
    /**获取token
     * @param string $code code
     * @param string $yky_member_guid 一卡易会员guid
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function get_token($code, $yky_member_guid = '')
    {
        $bid = $this->get_bid();
        if (empty($yky_member_guid)) {
            $config          = get_config_by_bid($bid);
            $yky             = new \OpenApi\Yky($config);
            $member_info     = $yky->get_member_info_by_openid($this->get_openid());
            $yky_member_guid = $member_info['MemberGuid'];
        }
        $xiao_mi_feng = new \OpenApi\XiaoMiFeng();
        $result       = $xiao_mi_feng->get_token($code);
        if ($result === false) {
            error($xiao_mi_feng->errmsg);
        }
        $openid        = $result['openId'];
        $token         = $result['token'];
        $refresh_token = $result['refreshToken'];
        $expired_time  = format_timestamp($result['expireIn']);
        $db            = new XmfAccessToken();
        $map           = [
            ['bid', '=', $bid],
            ['openid', '=', $openid],
        ];
        $count         = $db->where($map)->count();
        if ($count > 0) {
            //存在更新
            $update_data = [
                'refresh_token'     => $refresh_token,
                'token'             => $token,
                'expired_time'      => $expired_time,
                'last_refresh_time' => format_timestamp()
            ];
            $db::update($update_data, $map);
        } else {
            $data = [
                'guid'              => create_guid(),
                'bid'               => $bid,
                'yky_member_guid'   => $yky_member_guid,
                'refresh_token'     => $refresh_token,
                'expired_time'      => $expired_time,
                'token'             => $token,
                'openid'            => $openid,
                'last_refresh_time' => format_timestamp(),
            ];
            $db->save($data);
        }
        return $result;
    }

    public function check_openid()
    {
        $bid             = $this->get_bid();
        $config          = get_config_by_bid($bid);
        $yky             = new \OpenApi\Yky($config);
        $member_info     = $yky->get_member_info_by_openid($this->get_openid());
        $yky_member_guid = $member_info['MemberGuid'];
        $db              = new XmfAccessToken();
        $map             = [
            ['bid', '=', $bid],
            ['yky_member_guid', '=', $yky_member_guid],
        ];
        $count           = $db->where($map)->count();
        if ($count) {
            success('是会员');
        } else {
            error('不是会员');
        }
    }

    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function exchange()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $config          = get_config_by_bid($bid);
        $yky             = new \OpenApi\Yky($config);
        $member_info     = $yky->get_member_info_by_openid($this->get_openid());
        $yky_member_guid = $member_info['MemberGuid'];
        $db              = new XmfAccessToken();
        $map             = [
            ['bid', '=', $bid],
            ['yky_member_guid', '=', $yky_member_guid],
        ];
        $info            = $db->where($map)->findOrEmpty();
        if (!$info->isEmpty()) {
            $openid = $info['openid'];
        } else {
            //通过code换取到openId
            $code   = $params['code'];
            $result = $this->get_token($code, $yky_member_guid);
            $openid = $result['openId'];
        }
        $amount = $params['exchange_money'];
        if ($amount <= 0) {
            error('兑换金额必须大于0');
        }
        //       $max_amount = 5;
        //       if ($amount > $max_amount) {
        //           error('测试阶段每次仅能兑换' . $max_amount . '以内!');
        //       }
        $asset_type = 201;
        //       $pay_coin_symbol = $params['pay_coin_symbol'];
        $pay_coin_symbol = 'XMF1349';
        $lock            = get_distributed_instance();
        $key             = 'XMF:' . __FUNCTION__ . $bid . $openid;
        $locked          = $lock->get_lock($key);
        //先预下单
        $db_xmf_order = new XmfOrder();
        $order_id     = tools()::get_bill_number();
        $order_guid   = create_guid();
        $rate         = $config['xiao_mi_feng_rate'];
        //以下请求暂时冗余
        $url           = 'https://webapi.huilv.cc/api/trend/yaho';
        $data          = ['pinzhong' => 'USDCNY', 'longs' => 'd1'];
        $result        = curl()->form_params()->post($url, $data)->get_body();
        $huilv         = end($result['obj'])['huilv']; //6.X
        $api           = new XiaoMiFengApi();
        $result        = $api->ticker($pay_coin_symbol);
        $bili          = $result['last']; //0.026
        $exchange_rate = tools()::nc_price_calculate($huilv, '*', $bili, 8); //0.17 左右
        //实际用到$exchange_rate_zh_cn即可
        $url    = 'https://www.jcthy.com/fe-ex-api/common/public_info_market';
        $data   = ['securityInfo' => json_encode(['log_BSDeviceFingerprint' => 0, 'log_original' => 0, 'log_CHFIT_DEVICEID' => 0,]), 'uaTime' => format_timestamp()];
        $result = curl()->ignore_log(true)->post($url, $data)->get_body();
        if ($result['code'] !== '0') {
            error('查询汇率出错 ' . ($result['msg'] ?? ''));
        }
        $exchange_rate_zh_cn = $result['data']['market']['rate']['zh_CN'][$pay_coin_symbol];
        $after_amount        = tools()::nc_price_calculate($amount, '*', (1 - $rate), 4); //先扣除手续费
        //       $order_amount = tools()::ncPriceCalculate($after_amount, '/', $exchange_rate, 2);// todo后续按接口
        $after_exchange_rate_zh_cn = tools()::format_number($exchange_rate_zh_cn, 2); // 只取两位小数 无需四舍五入
        $order_amount              = tools()::nc_price_calculate($after_amount, '/', $after_exchange_rate_zh_cn, 2);// todo后续按接口
        $save_data                 = [
            'guid'                => $order_guid,
            'bid'                 => $bid,
            'yky_member_guid'     => $yky_member_guid,
            'openid'              => $openid,
            'after_amount'        => $after_amount,
            'order_amount'        => $order_amount,
            'dec_money'           => $amount,
            'huilv'               => $huilv,
            'bili'                => $bili,
            'exchange_rate'       => $exchange_rate,
            'exchange_rate_zh_cn' => $exchange_rate_zh_cn,
            'rate'                => $rate,
            'asset_type'          => $asset_type,
            'pay_coin_symbol'     => $pay_coin_symbol,
            'order_id'            => $order_id
        ];
        $db_xmf_order->save($save_data);
        //先扣除会员的money
        $inc_money            = -$amount; //todo 通过 $amount 转换
        $db_member            = new \app\model\Member();
        $db_member_money_note = new MemberMoneyNote();
        $member_map           = ['bid' => $bid, 'yky_member_guid' => $yky_member_guid];
        $member_info          = $db_member->get_member_info($member_map);
        if (!$member_info) {
            throw new Exception('会员注册失败');
        }
        $member_guid           = $member_info['guid'];
        $inc_money_bill_number = tools()::get_bill_number();
        $money_note_guid       = create_guid();
        $order                 = [
            'bid'                  => $bid,
            'guid'                 => $money_note_guid,
            'member_guid'          => $member_guid,
            'yky_member_guid'      => $yky_member_guid,
            'bill_number'          => $inc_money_bill_number,
            'relation_bill_number' => $order_id,
            'type'                 => -1, // 1 充值 -1 扣除
            'way'                  => 7, // 兑换币扣除
            'money'                => $inc_money,
            'memo'                 => '兑换币'
        ];
        $db_member_money_note->recharge_money($order);
        //再请求三方赠币
        $xiao_mi_feng  = new \OpenApi\XiaoMiFeng();
        $post_data     = [
            'assetType'     => $asset_type,
            'appOrderId'    => $order_id,
            'openId'        => $openid,
            'orderAmount'   => $order_amount,
            'payCoinSymbol' => $pay_coin_symbol,
        ];
        $result        = $xiao_mi_feng->refundOrder($post_data);
        $xmf_order_map = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        if ($result === false) {
            // 回退会员的money 并更新订单状态为失败
            $db_member_money_note->undo($bid, $money_note_guid);
            $msg         = $xiao_mi_feng->errmsg ?? '';
            $update_data = [
                'status' => -1,
                'result' => $msg,
            ];
            $db_xmf_order::update($update_data, $xmf_order_map);
            $lock->unlock($locked);
            error('兑换失败:' . $result);
        } else {
            //更新订单状态为成功
            $msg         = '兑换成功';
            $update_data = [
                'status'    => 1,
                'order_num' => $result['orderNum'],
                'result'    => $msg,
            ];
            $db_xmf_order::update($update_data, $xmf_order_map);
            $lock->unlock($locked);
            success('兑换成功');
        }
    }
}