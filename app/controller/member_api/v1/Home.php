<?php

namespace app\controller\member_api\v1;

use app\model\CouponCategory;
use app\model\Goods as GoodsModel;
use app\model\GoodsCategory;
use app\model\Tag;
use Exception;

class Home extends BasicMemberApi
{


    /**
     *首页
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $data_type       = $params['type'] ?? 1; // 1 原有的一维数组, 2 是新的二维数组
        $config          = get_config_by_bid($bid);
        $close_home_page = $config['close_home_page'];
        if ($close_home_page == 1) {
            $header = ['LocationPath' => '/pages/code/index'];
            error('正在跳转到首页', -1, null, '', $header);
        }
        $db_business    = new \app\model\Business();
        $business_info  = $db_business->get_business_info_by_account_or_guid($bid);
        $index_layout   = explode(',', $config['index_layout']);
        $layout_mapping = [
            [
                'name'   => '公告',
                'type'   => 'notice',
                'action' => 'get_notice',
            ], [
                'name'   => '搜索',
                'type'   => 'search',
                'action' => 'get_search',
            ], [
                'name'   => '轮播图',
                'type'   => 'banner_list',
                'action' => 'get_banner_list',
            ], [
                'name'   => '视频',
                'type'   => 'video_list',
                'action' => 'get_video_list',
            ], [
                'name'   => '标签',
                'type'   => 'nav',
                'action' => 'get_tag_list',
            ], [
                'name'   => '分类',
                'type'   => 'nav',
                'action' => 'get_goods_category_list',
            ], [
                'name'   => '专题',
                'type'   => 'topic_list',
                'action' => 'get_topic_list',
            ], [
                'name'   => '富文本',
                'type'   => 'rich_text',
                'action' => 'get_rich_text',
            ], [
                'name'   => '瀑布图',
                'type'   => 'image_url_list',
                'action' => 'get_image_url_list',
            ], [
                'name'   => '标签列表',
                'type'   => 'cat_list',
                'action' => 'get_tag_list_with_goods_list',
                'attr'   => ['key' => 'tag_guid']
            ], [
                'name'   => '商品列表',
                'type'   => 'cat_list',
                'action' => 'get_cat_list_with_goods_list',
                'attr'   => ['key' => 'cat_guid']
            ], [
                'name'   => '卡券列表',
                'type'   => 'coupon_cat_list',
                'action' => 'get_coupon_category_list_with_coupon_list',
            ],
        ];
        $layout_list    = [];
        //搜索,轮播图,标签,分类,专题,富文本,瀑布图,标签列表,商品列表,卡券列表
        foreach ($index_layout as $layout_name) {
            foreach ($layout_mapping as $key => $val) {
                if ($layout_name == $val['name']) {
                    $layout_data = call_user_func_array([$this, $val['action']], []);
                    switch ($data_type) {
                        case 1:
                            $layout_list[$val['type']] = $layout_data;
                            break;
                        case 2:
                            $layout_list[] = ['type' => $val['type'], 'data' => $layout_data, 'attr' => ($val['attr'] ?? [])];
                            break;
                        default:
                            throw new Exception('暂时不支持该数据类型');
                    }
                    //                    $layout_list = array_push($layout_list, [$val['type'] => $layout_data]);
                }
            }
        }
        $layout = [
            'layout'         => $layout_list,
            'cat_goods_cols' => 2,
            'cache_time'     => 60,
            'title'          => $business_info['business_name']
        ];
        result($layout);
    }

    protected function get_notice()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        return [
            'title'   => $config['mall_notice_title'],
            'content' => $config['mall_notice_content'],
            'path'    => '/pages/index/index',
        ];
    }

    protected function get_search()
    {
        return true;
    }

    protected function get_video_list()
    {
        $bid       = $this->get_bid();
        $db_banner = new \app\model\Banner();
        return $db_banner->get_banner_list($bid, 4);
    }

    protected function get_banner_list()
    {
        $bid       = $this->get_bid();
        $db_banner = new \app\model\Banner();
        return $db_banner->get_banner_list($bid, 2);
    }

    protected function get_tag_list()
    {
        $db_tag    = new Tag();
        $bid       = $this->get_bid();
        $map_tag   = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $field_tag = [
            'bid',
            'guid',
            'name',
            'icon'
        ];
        $order_tag = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];

        $tag_list = $db_tag->where($map_tag)->field($field_tag)->order($order_tag)->order(['sort' => 'ASC'])->select();
        $nav_tag  = [];
        foreach ($tag_list as $key => $val) {
            $nav_tag[] = [
                'open_type' => 'navigate',
                'name'      => $val['name'],
                'url'       => '/pages/list/list?tag_guid=' . $val['guid'],
                'mini_pic'  => $val['icon']
            ];
        }
        return array_chunk($nav_tag, 10);
    }

    protected function get_goods_category_list()
    {
        $bid                  = $this->get_bid();
        $db_goods_category    = new GoodsCategory();
        $map_goods_category   = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['parent_guid', 'IN', [tools()::get_empty_guid(), '']],
        ];
        $field_goods_category = [
            'bid',
            'guid',
            'parent_guid',
            'name',
            'pic',
            'mini_pic'
        ];
        $order_goods_category = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];

        $goods_category_list = $db_goods_category->where($map_goods_category)->field($field_goods_category)->order($order_goods_category)->order(['sort' => 'ASC'])->select();
        if (count($goods_category_list) == 1) {
            //如果只有一个分类则不展示分类模块
            return [];
        }
        $nav_goods_category = [];
        foreach ($goods_category_list as $key => $val) {
            $nav_goods_category[] = [
                'open_type' => 'navigate',
                'name'      => $val['name'],
                'url'       => '/pages/list/list?cat_guid=' . $val['guid'],
                'mini_pic'  => $val['mini_pic']
            ];
        }
        $config        = get_config_by_bid($bid);
        $category_rows = (int)$config['category_rows']; //分类行数 默认2行
        return array_chunk($nav_goods_category, $category_rows * 5);
    }

    protected function get_topic_list()
    {
        $bid       = $this->get_bid();
        $map_topic = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $db_topic  = new \app\model\Topic();
        return $db_topic->where($map_topic)->order(['sort' => 'ASC'])->limit(5)->select();
    }

    protected function get_rich_text()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        return tools()::add_rich_img_class($config['mall_home_page_rich_text']);
    }

    protected function get_image_url_list()
    {
        $bid       = $this->get_bid();
        $db_banner = new \app\model\Banner();
        return $db_banner->get_banner_list($bid, 3);
    }

    protected function get_tag_list_with_goods_list()
    {
        $db_tag    = new Tag();
        $bid       = $this->get_bid();
        $map_tag   = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $field_tag = [
            'bid',
            'guid',
            'name',
            'icon',
            'pic'
        ];
        $order_tag = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        return $db_tag->where($map_tag)->field($field_tag)->order($order_tag)->append(['goods_list'])->order(['sort' => 'ASC'])->select();
    }

    protected function get_cat_list_with_goods_list()
    {
        $bid               = $this->get_bid();
        $db_goods_category = new GoodsCategory();
        return $db_goods_category->get_cat_list_with_goods_list($bid);
    }

    protected function get_coupon_category_list_with_coupon_list()
    {
        $bid                   = $this->get_bid();
        $db_coupon_category    = new CouponCategory();
        $map_coupon_category   = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $field_coupon_category = [
            'bid',
            'guid',
            'parent_guid',
            'name',
            'pic',
            'mini_pic'
        ];
        $order_coupon_category = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        return $db_coupon_category->where($map_coupon_category)->field($field_coupon_category)->order($order_coupon_category)->append(['coupon_list'])->select();
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_list()
    {
        $db_goods                = new GoodsModel();
        $db_goods_category       = new GoodsCategory();
        $bid                     = $this->get_bid();
        $goods_limit             = 6; //区域商品个数
        $goods_category_limit    = 5; //每行商品类别个数
        $map                     = [
            ['bid', '=', $this->get_bid()],
            ['parent_guid', 'IN', [tools()::get_empty_guid(), '']],
            //           ['parent_guid', '=', tools()::get_empty_guid()]
        ];
        $field                   = [
            'guid',
            'parent_guid',
            'name',
            'pic',
            'mini_pic'
        ];
        $order                   = [
            'sort'        => 'DESC',
            'create_time' => 'DESC'
        ];
        $all_goods_category_list = $db_goods_category->field($field)->order($order)->where($map)->select()->toArray();
        $field                   = [
            'guid',
            'category_guid',
            'name',
            'pic',
            'price',
            'show_price',
            'original_price',
            'count',
            //'description',
        ];
        $goods_list_arr          = [];
        $goods_category_list     = array_slice($all_goods_category_list, 0, $goods_category_limit);
        foreach ($goods_category_list as $key => $val) {
            $map        = [
                ['bid', '=', $bid],
                ['status', '=', 1],
                ['category_guid', 'IN', [$val['guid'], $val['parent_guid']]],
            ];
            $goods_list = $db_goods->field($field)->order($order)->where($map)->limit($goods_limit)->select()->toArray();

            foreach ($goods_list as $k => $v) {
                $goods_list[$k]['price'] = $v['show_price'] >= 0 ? $v['show_price'] : $v['price'];
            }
            $goods_list_arr = array_merge($goods_list_arr, $goods_list);
        }

        //补充图片前缀
        foreach ($goods_list_arr as $k => $v) {
            $goods_list_arr[$k] = $db_goods->buildPic($v);
        }

        foreach ($goods_category_list as $key => $val) {
            $goods_category_list[$key]['goods_list'] = [];
            foreach ($goods_list_arr as $k => $v) {
                if (in_array($v['category_guid'], [$val['guid'], $val['parent_guid']])) {
                    $goods_category_list[$key]['goods_list'][] = $v;
                }
            }
        }
        return ['goods_list' => $goods_category_list, 'all_goods_category_list' => $all_goods_category_list];
    }

    /**
     *兼容发布了SKU但是又访问了旧版的小程序,保证不报错
     * @access public
     * @return array
     * @throws Exception
     */
    public function goods_attr_info()
    {
        result([]);
    }

    /**
     *首页
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index_old()
    {
        $params            = $this->params;
        $data_type         = $params['type'] ?? 1; // 1 原有的一维数组, 2 是新的二维数组
        $bid               = $this->get_bid();
        $config            = get_config_by_bid($bid);
        $db_business       = new \app\model\Business();
        $business_info     = $db_business->get_business_info_by_account_or_guid($bid);
        $index_layout      = explode(',', $config['index_layout']);
        $index_layout_list = [
            'search'          => '搜索',
            'banner_list'     => '轮播图',
            'nav'             => '分类',
            'cat_list'        => '商品列表',
            'coupon_cat_list' => '卡券列表',
        ];
        $layout            = [
            'layout'         => [
                'notice'          => $this->get_notice(),
                'search'          => $this->get_search(),
                'banner_list'     => $this->get_banner_list(),
                'nav'             => $this->get_goods_category_list(),
                'rich_text'       => $this->get_rich_text(),
                'topic_list'      => $this->get_topic_list(),
                'image_url_list'  => $this->get_image_url_list(),
                'cat_list'        => $this->get_cat_list_with_goods_list(),
                'coupon_cat_list' => $this->get_coupon_category_list_with_coupon_list()
            ],
            'cat_goods_cols' => 2,
            'title'          => $business_info['business_name']
        ];
        foreach ($index_layout_list as $key => $val) {
            if (!in_array($val, $index_layout)) {
                unset($layout['layout'][$key]);
            }
        }
        //        if (count($goods_category_list) == 1) {
        //            //如果只有一个分类则不展示分类模块
        //            unset($layout['layout']['nav']);
        //        }
        result($layout);
        //
        //        $db_banner      = new \app\model\Banner();
        //        $banner_list    = $db_banner->get_banner_list($bid, 2);
        //        $image_url_list = $db_banner->get_banner_list($bid, 3);
        //
        //        //       $banner_list = [
        //        //           [
        //        //               'src'   => 'http://img02.tooopen.com/images/20150928/tooopen_sy_143912755726.jpg',
        //        //               'url'   => '/pages/user/user?id=' . time(),
        //        //               'title' => 'title 1'
        //        //           ], [
        //        //               'src'   => 'http://img02.tooopen.com/images/20150928/tooopen_sy_143912755726.jpg',
        //        //               'url'   => '/pages/user/user?id=' . time(),
        //        //               'title' => 'title 2'
        //        //           ],
        //        //       ];
        //        $topic_list = [
        //            [
        //                'id'    => 1,
        //                'guid'  => 'guid1',
        //                'tag'   => '热门',
        //                'title' => '标题',
        //            ], [
        //                'id'    => 2,
        //                'guid'  => 'guid2',
        //                'tag'   => '教程',
        //                'title' => '标题2',
        //            ],
        //        ];
        //
        //        $map_topic = [
        //            ['bid', '=', $bid],
        //            ['status', '=', 1],
        //        ];
        //
        //        $db_topic   = new \app\model\Topic();
        //        $topic_list = $db_topic->where($map_topic)->order(['sort' => 'ASC'])->limit(5)->select();
        //        $cat_list   =
        //            [
        //                [
        //                    'id'          => 1,
        //                    'store_id'    => 1,
        //                    'parent_id'   => 1,
        //                    'name'        => '特价',
        //                    'pic_url'     => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                    'sort'        => '100',
        //                    'addtime'     => '1522676264',
        //                    'is_delete'   => 0,
        //                    'big_pic_url' => '',
        //                    'advert_pic'  => '',
        //                    'advert_url'  => '',
        //                    'is_show'     => 1,
        //                    'page_url'    => "\/pages\/list\/list?cat_id=7",
        //                    'open_type'   => "navigate",
        //                    'cat_pic'     => "",
        //                    'goods_list'  => [
        //                        [
        //                            'id'            => 2,
        //                            'name'          => '特价',
        //                            'price'         => '1.00',
        //                            'pic_url'       => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                            'num'           => null,
        //                            'unit'          => '件',
        //                            'sales'         => '0件',
        //                            'virtual_sales' => 100,
        //                        ]
        //                    ]
        //                ],
        //                [
        //                    'id'          => 2,
        //                    'store_id'    => 1,
        //                    'parent_id'   => 1,
        //                    'name'        => '夏天',
        //                    'pic_url'     => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                    'sort'        => '100',
        //                    'addtime'     => '1522676264',
        //                    'is_delete'   => 0,
        //                    'big_pic_url' => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                    'advert_pic'  => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                    'advert_url'  => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                    'is_show'     => 1,
        //                    'page_url'    => "\/pages\/list\/list?cat_id=7",
        //                    'open_type'   => "navigate",
        //                    'cat_pic'     => "",
        //                    'goods_list'  => [
        //                        [
        //                            'id'            => 21,
        //                            'name'          => '特价',
        //                            'price'         => '1.00',
        //                            'pic_url'       => 'https:\/\/static.ushop.xany6.com\/uploads\/image\/ee\/ee92784cdc6bc0471e65637306fee554.png',
        //                            'num'           => null,
        //                            'unit'          => '件',
        //                            'sales'         => '0件',
        //                            'virtual_sales' => 100,
        //                        ]
        //                    ]
        //                ]
        //            ];
        //        //$cat_list   = $this->goods_list();
        //
        //        $db_goods_category    = new GoodsCategory();
        //        $db_coupon_category   = new CouponCategory();
        //        $map_goods_category   = [
        //            ['bid', '=', $bid],
        //            ['status', '=', 1],
        //        ];
        //        $field_goods_category = [
        //            'bid',
        //            'guid',
        //            'parent_guid',
        //            'name',
        //            'mini_pic'
        //        ];
        //        $order_goods_category = [
        //            'sort'        => 'ASC',
        //            'create_time' => 'DESC'
        //        ];
        //
        //        $goods_category_list = $db_goods_category->where($map_goods_category)->field($field_goods_category)->order($order_goods_category)->order(['sort' => 'ASC'])->select();
        //        // $topic_list = [];
        //        $nav_goods_category = [];
        //        foreach ($goods_category_list as $key => $val) {
        //            $nav_goods_category[] = [
        //                'open_type' => 'navigate',
        //                'name'      => $val['name'],
        //                'url'       => '/pages/list/list?cat_guid=' . $val['guid'],
        //                'mini_pic'  => $val['mini_pic']
        //            ];
        //        }
        //        $nav_goods_category = array_chunk($nav_goods_category, 10);
        //
        //        $goods_category_list_with_goods_list   = $db_goods_category->where($map_goods_category)->field($field_goods_category)->order($order_goods_category)->append(['goods_list'])->select();
        //        $map_coupon_category                   = [
        //            ['bid', '=', $bid],
        //            ['status', '=', 1],
        //        ];
        //        $field_coupon_category                 = [
        //            'bid',
        //            'guid',
        //            'parent_guid',
        //            'name',
        //            'mini_pic'
        //        ];
        //        $order_coupon_category                 = [
        //            'sort'        => 'ASC',
        //            'create_time' => 'DESC'
        //        ];
        //        $coupon_category_list_with_coupon_list = $db_coupon_category->where($map_coupon_category)->field($field_coupon_category)->order($order_coupon_category)->append(['coupon_list'])->select();
        //
        //        //$banner_list = [];
        //        //       $image_url_list = [
        //        //           [
        //        //               'img_url' => 'https://www.yikayi.net/file/uploads/202203/08/0ad1255d-2e5f-fa3b-4c7d-3c77ba24c95f.jpg',
        //        //               'path'    => '/pages/index/index',
        //        //           ],
        //        //           [
        //        //               'img_url' => 'https://www.yikayi.net/file/uploads/202203/08/0ad1255d-2e5f-fa3b-4c7d-3c77ba24c95f.jpg',
        //        //               'path'    => '/pages/index/index',
        //        //           ]
        //        //       ];
        //        $mall_home_page_rich_text = str_replace('<img', '<img class="rich_img"', $config['mall_home_page_rich_text']);

    }

}
