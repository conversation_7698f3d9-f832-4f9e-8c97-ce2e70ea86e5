<?php

namespace app\controller\member_api\v1;

use app\model\GiftPackage as GiftPackageModel;
use app\model\YkyCouponExchangeOrder;
use app\model\YkyCouponTransferNote;
use app\common\tools\AES_256_CBC;
use Exception;

class GiftPackage extends BasicMemberApi
{
    /**
     *魔术包提现
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function cash()
    {
        $param         = $this->params;
        $bid           = $this->get_bid();
        $config        = get_config_by_bid($bid);
        $exchange_type = 2; //暂时固定写死微信
        //此方法校验数据安全性,返回前端数据是否提交成功,然后把数据放入提现队列
        $yky    = new \OpenApi\Yky($config);
        $member = $yky->get_member_info_by_openid($param['openid']);
        if ($member === false) {
            error($yky->message);
        }
        $limit                     = 10;//每天每人最多提现10次
        $type                      = $param['type']; //开启老魔术包还是新魔术包
        $yky_coupon_exchange_order = new YkyCouponExchangeOrder();
        $db_gift_package           = new GiftPackageModel();
        //先查下符合条件的券guid
        $map     = [
            ['bid', '=', $bid],
            ['value', 'notnull', null],
            ['wechat_value', 'notnull', null],
            ['yikayi_value', 'notnull', null],
        ];
        $coupons = $db_gift_package->where($map)->column('coupon_guid');
        //选择名下已有魔术包
        if ($type == 1) {
            if (empty($param['coupon_send_guid'])) {
                error('请选择要开启的魔术包');
            }
            //类型1 说明是提现自己的魔术包
            //为了确保安全 校验是否一致
            $result = $yky->get_coupon_send_note_by_cardid($member['CardId'], $coupons);
            if ($result === false) {
                error('您没有获得相关卡密');
            }
            $coupon_send_guid     = $param['coupon_send_guid'];
            $coupon_send_guid     = explode(',', $coupon_send_guid);
            $coupon_send_guid_arr = [];
            foreach ($coupon_send_guid as $key => $val) {
                $val                    = AES_256_CBC::decrypt($val);
                $coupon_send_info       = explode('|', $val);
                $coupon_send_guid_arr[] = [
                    'coupon_guid'      => $coupon_send_info[0],
                    'coupon_send_guid' => $coupon_send_info[1],
                ];
            }
            //先校验本次最多可提现张数
            $will_cash = count($coupon_send_guid_arr);
            if ($will_cash > $limit) {
                error('请选择10张以内魔术包!');
            }
            $map     = [
                ['bid', '=', $bid],
                ['exchange_type', '=', 2],
                ['openid', '=', $member['ThirdOpenId']],
            ];
            $is_cash = $yky_coupon_exchange_order->where($map)->whereDay('update_time')->count();
            if (($is_cash + $will_cash) > $limit) {
                error('您当前最多可选择' . ($limit - $is_cash) . '张');
            }
            $member['TrueName'] = tools()::emoji_encode($member['TrueName']);
            $success            = 0;
            foreach ($coupon_send_guid_arr as $key => $val) {
                $order_guid    = create_guid();
                $exchange_data = [
                    'guid'             => $order_guid,
                    'bid'              => $bid,
                    'member_guid'      => $member['MemberGuid'],
                    'coupon_guid'      => $val['coupon_guid'],
                    'coupon_send_guid' => $val['coupon_send_guid'],
                    'openid'           => $member['ThirdOpenId'],
                    'cardid'           => $member['CardId'],
                    'exchange_type'    => $exchange_type,
                    'result'           => '等待提现中'
                ];
                $map           = [
                    ['bid', '=', $bid],
                    ['coupon_send_guid', '=', $val['coupon_send_guid']],
                ];
                $count         = $yky_coupon_exchange_order->where($map)->count();
                if ($count) {
                    continue;
                }
                //查询提现金额
                $map    = [
                    ['bid', '=', $bid],
                    ['coupon_guid', '=', $val['coupon_guid']],
                ];
                $coupon = $db_gift_package->field(['wechat_value', 'yikayi_value'])->where($map)->find();
                //提现至储值
                if ($exchange_type == 1) {
                    $exchange_data['exchange_value'] = $coupon['yikayi_value'];
                }
                //提现至微信
                if ($exchange_type == 2) {
                    $exchange_data['exchange_value'] = $coupon['wechat_value'];
                }
                $yky_coupon_exchange_order->save($exchange_data);
                $data = [
                    'bid'        => $bid,
                    'order_guid' => $order_guid,
                ];
                if (job()->set_job_name('Member@coupon_exchange')->push_job($data)) {
                    ++$success;
                    continue;
                } else {
                    wr_log('push_job_api失败', 1);
                    error('任务提交失败');
                }
            }
            if ($success > 0) {
                success('提交成功' . $success . '条');
            } else {
                error('没有提交成功的数据~');
            }
        }
        if ($type == 2) {
            // 定义表单验证规则
            $rules = [
                'openid'        => 'require',
                'card_id|卡号'  => 'require',
                'password|密码' => 'require',
            ];
            // 验证表单数据
            $this->validate($param, $rules);
            $from_card_id        = $param['card_id'];
            $key                 = __FUNCTION__ . $from_card_id;
            $lock_instance       = get_distributed_instance();
            $lock                = $lock_instance->get_lock($key);
            $post_data['cardId'] = $from_card_id;
            if (tools()::verify_throttle_check($bid . $from_card_id)) {
                $lock_instance->unlock();
                error('由于连续3次密码输入错误,该卡号已经被锁定,半小时后再试~');
            }
            //通过openi 查找会员卡号
            $post_data['password'] = $param['password'];
            $result                = $yky->MemberLogin($post_data);
            if ($result === false) {
                $lock_instance->unlock();
                tools()::verify_throttle_inc($bid . $from_card_id);
                error('验证失败:' . $yky->message . ',连续输入三次将锁定半小时~');
            }
            $result = $yky->get_coupon_send_note_by_cardid($from_card_id, $coupons);
            if ($result === false) {
                $lock_instance->unlock();
                error('该卡不含魔术包');
            }
            $map     = [
                ['bid', '=', $bid],
                ['exchange_type', '=', 2],
                ['openid', '=', $member['ThirdOpenId']],
            ];
            $is_cash = $yky_coupon_exchange_order->where($map)->whereDay('update_time')->count();
            if ($is_cash > $limit) {
                error('每人每天最多提现10张哦~');
            }
            $CouponSendGuid = $result[0]['Guid'];
            $CouponGuid     = $result[0]['CouponGuid'];
            $order_guid     = create_guid();
            $exchange_data  = [
                'guid'             => $order_guid,
                'bid'              => $bid,
                'member_guid'      => $member['MemberGuid'],
                'coupon_guid'      => $CouponGuid,
                'coupon_send_guid' => $CouponSendGuid,
                'openid'           => $member['ThirdOpenId'],
                'cardid'           => $member['CardId'],
                'exchange_type'    => $exchange_type,
                'result'           => '等待提现中'
            ];
            $map            = [
                ['bid', '=', $bid],
                ['coupon_send_guid', '=', $CouponSendGuid],
            ];
            $count          = $yky_coupon_exchange_order->where($map)->count();
            if ($count) {
                error('当前魔术包已经在处理中,请耐心等待');
            }
            //查询提现金额
            $map    = [
                ['bid', '=', $bid],
                ['coupon_guid', '=', $CouponGuid],
            ];
            $coupon = $db_gift_package->field(['wechat_value', 'yikayi_value'])->where($map)->find();
            //提现至储值
            if ($exchange_type == 1) {
                $exchange_data['exchange_value'] = $coupon['yikayi_value'];
            }
            //提现至微信
            if ($exchange_type == 2) {
                $exchange_data['exchange_value'] = $coupon['wechat_value'];
            }
            $yky_coupon_exchange_order->save($exchange_data);
            $data             = [
                'guid'          => create_guid(),
                'bid'           => $bid,
                'old_send_guid' => $CouponSendGuid,
                'from_card_id'  => $from_card_id,
                'to_card_id'    => $member['CardId'],
                'coupon_guid'   => $CouponGuid,
                'send_guid'     => '',
                'create_time'   => format_timestamp(),
            ];
            $db_transfer_note = new YkyCouponTransferNote();
            $db_transfer_note->save($data);
            $data = [
                'bid'        => $bid,
                'order_guid' => $order_guid,
            ];
            $lock_instance->unlock();
            if (job()->set_job_name('Member@coupon_exchange')->push_job($data)) {
                success('开启成功~');
            } else {
                wr_log('push_job_api失败', 1);
                error('push_job提交任务失败~');
            }
        }

    }
}