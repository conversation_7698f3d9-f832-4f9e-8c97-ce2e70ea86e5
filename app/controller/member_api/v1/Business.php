<?php

namespace app\controller\member_api\v1;


use Exception;

class Business extends BasicMemberApi
{
    /**
     *商家信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function info()
    {
        $db   = new \app\model\Business();
        $map  = [['guid', '=', $this->get_bid()]];
        $info = $db->where($map)->find();
        unset($info['id']);
        unset($info['appid']);
        unset($info['secret']);
        result($info);
    }
}