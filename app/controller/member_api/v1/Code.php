<?php

namespace app\controller\member_api\v1;

use app\common\exceptions\NotNotifyException;
use app\common\service\NotifyService;
use app\common\service\WeixinService;
use app\model\Area;
use app\model\AreaRule;
use app\model\Banner;
use app\model\Coupon;
use app\model\CouponActiveOrder;
use app\model\CouponCategory;
use app\model\CouponGoodsItem;
use app\model\CouponRecommendNote;
use app\model\CouponSellOrder;
use app\model\CouponSendNote;
use app\model\CouponShareNote;
use app\model\ExtendField;
use app\model\Favorites as FavoritesModel;
use app\model\GoodsOrder;
use app\model\Member;
use app\model\ShortUrl;
use app\model\WechatConfig;
use app\common\service\SmsService;
use app\common\service\UrlService;
use app\common\tools\Visitor;
use app\model\WechatSubscribeTemplateList;
use Exception;
use Grafika\Color;
use Grafika\Grafika;
use think\facade\Db;
use Intervention\Image\ImageManagerStatic as Image;
use Intervention\Image\Constraint;
use Intervention\Image\AbstractFont;


class Code extends BasicMemberApi
{
    public function active()
    {
        $params                 = $this->params;
        $coupon_code            = trim($params['code']);
        $db_coupon_active_order = new CouponActiveOrder();
        $db_coupon_active_order->active($coupon_code);
    }

    public function parse_qrcode()
    {
        $params = $this->params;
        $text   = $params['text'];
        $data   = [];
        if (tools()::is_url($text)) {
            $url_params = tools()::parse_url_params($text);
            if (!empty($url_params['c'])) {
                $data['code'] = $url_params['c'];
            }
            if (!empty($url_params['p'])) {
                $data['password'] = $url_params['p'];
            }
        } elseif (is_string($text)) {
            $data['code'] = $text;
        }
        if (empty($data)) {
            //error('不是有效的二维码');
        }
        result($data);
    }

    public function bind_code_by_mobile()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $phone       = $params['phone'];
        $verify_code = $params['verify_code'];
        $sms         = SmsService::get_instance($bid);
        if ($phone != '18603047034_1') {
            //临时调试
            $result = $sms->verify_sms_code($phone, $verify_code);
        }
        //验证通过
        //更新用户手机号
        $update_data = ['mobile' => $phone];
        $db_member   = new Member();
        $member      = $db_member->update_member_info_and_update_coupon_send_note_member_guid($bid, $member_guid, $update_data);
        //        if (!empty($data['coupon_guid'])) {
        //            $map[] = ['coupon_guid', '=', $data['coupon_guid']];
        //        }
        $db_coupon_send_note    = new CouponSendNote();
        $map                    = [['bid', '=', $bid]];
        $db_coupon              = new Coupon();
        $map_coupon             = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null],
        ];
        $not_delete_coupon_guid = $db_coupon->where($map_coupon)->column('guid');
        $map[]                  = ['coupon_guid', 'IN', $not_delete_coupon_guid];
        $map[]                  = ['mobile', '=', $phone];
        $map[]                  = ['status', '=', 0];
        $map[]                  = ['member_guid', '=', $member_guid];
        $total_count            = $db_coupon_send_note->where($map)->count();
        if ($total_count == 0) {
            error('该手机号下没有卡券信息');
        }
        result(['total' => $total_count], '绑定成功' . $total_count . '张');
    }

    public function bind_code()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $member_guid         = $this->get_member_guid();
        $code                = $params['code'];
        $password            = $params['password'];
        $db_coupon_send_note = new CouponSendNote();
        $info                = $db_coupon_send_note->verify($bid, ['code' => $code, 'password' => $password]);
        if (!$info) {
            error($db_coupon_send_note->getError());
        }

        if (!empty($info['member_guid'])) {
            if ($info['member_guid'] == $member_guid) {
                error('您已经绑定过该卡券了,无需重复绑定');
            } else {
                error('该卡券已经被其他人绑定过');
            }
        }

        $db_coupon   = new Coupon();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $info['coupon_guid']]
        ];
        $coupon_info = $db_coupon->where($map)->find();
        $coupon_type = $coupon_info['type'];
        if ($coupon_type == 3) {
            //金额卡直接充入账户余额
            $db_coupon_send_note = new CouponSendNote();
            $available_value     = $db_coupon_send_note->code_to_member_money(['code' => $code, 'password' => $password]);
            $config              = get_config_by_bid($bid);
            success('充值成功' . $available_value . $config['money_unit']);
        }

        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $info['guid']],
        ];
        $update_data = ['member_guid' => $member_guid];
        $db_coupon_send_note::update($update_data, $map);
        success('绑定成功');
    }

    /**
     * 验证卡号密码,返回token(存储在redis中)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function verify_coupon_send_note_guid()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $db_coupon_send_note   = new CouponSendNote();
        $config                = get_config_by_bid($bid);
        $allow_submit_order    = $config['allow_submit_order'];
        if ($allow_submit_order == 0) {
            error($config['forbid_submit_order_tips']);
        }
        $result = $db_coupon_send_note->code_or_send_note_guid_to_token($bid, ['coupon_send_note_guid' => $coupon_send_note_guid]);
        if ($result === false) {
            error($db_coupon_send_note->getError());
        } else {
            result($result);
        }
    }

    /**
     * 验证卡号密码,返回token(存储在redis中)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function verify_code()
    {
        $params             = $this->params;
        $bid                = $this->get_bid();
        $config             = get_config_by_bid($bid);
        $allow_submit_order = $config['allow_submit_order'];
        if ($allow_submit_order == 0) {
            error($config['forbid_submit_order_tips']);
        }
        $use_code_alias = $config['use_code_alias'];
        $data           = [];
        $goods_guid     = $params['goods_guid'] ?? null;
        $pick_up_type   = $params['pick_up_type'] ?? 1; // 1 卡号+密码 提货  2 验证码提货

        if ($pick_up_type == 1) {
            if (!isset($params['code'])) {
                error('请输入卡号');
            }
            $code = $params['code'];
            if (!isset($params['password'])) {
                error('请输入密码');
            }
            $password = $params['password'];
            $data     = [
                'use_code_alias' => $use_code_alias,
                'code'           => $code,
                'password'       => $password
            ];
        } elseif ($pick_up_type == 2) {
            $phone       = $params['phone'];
            $verify_code = $params['verify_code'];
            $data        = ['phone' => $phone, 'verify_code' => $verify_code];
            if (!empty($params['coupon_guid'])) {
                $data['coupon_guid'] = $params['coupon_guid'];
            }
        }
        if ($goods_guid) {
            $data['goods_guid'] = $goods_guid;
        }
        $db_coupon_send_note = new CouponSendNote();
        $result              = $db_coupon_send_note->code_or_send_note_guid_to_token($bid, $data);
        if ($result === false && empty($db_coupon_send_note->getReturnData())) {
            error($db_coupon_send_note->getError());
        } elseif (!empty($db_coupon_send_note->getReturnData())) {
            $return_data = $db_coupon_send_note->getReturnData();
            $status      = $return_data['status'];
            $info        = $return_data['info'];
            switch ($status) {
                case 'CODE_USED':
                    $coupon_send_note_guid = $info['guid'];
                    //已使用的跳转到订单详情
                    $db_goods_order = new GoodsOrder();
                    $map            = [
                        ['bid', '=', $bid],
                        ['coupon_send_note_guid', '=', $coupon_send_note_guid],
                    ];
                    $order_info     = $db_goods_order->where($map)->order(['id' => 'desc'])->findOrEmpty();
                    if ($order_info['delete_time']) {
                        error('当前卡密已被使用!'); //这种情况跳转到订单详情也是看不到订单
                    }
                    if ($order_info['member_guid'] == $this->get_member_guid() || $config['allow_query_other_member_order'] == 1) {
                        if (Visitor::is_mini_app_browser()) {
                            //todo 暂时小程序有问题无法自动跳转到小程序的详情页
                            error('当前卡密已使用,请去订单列表中查看订单详情!');
                        } else {
                            $url = (string)url('member/goods_order/detail', ['bid' => $bid, 'order_guid' => $order_info['guid'], 'from' => 'used_code'], false, true);
                            result([
                                'url'  => $url,
                                'path' => '/pages/order/detail?bid=' . $bid . '&order_guid=' . $order_info['guid'],
                            ]);
                        }
                    } else {
                        error('当前卡密已经被其他人使用');
                    }
                    break;
                case 'CHOOSE_CODE':
                    $url = (string)url('member/code/choose_code', ['bid' => $bid, 'from' => 'verify_code'], false, true);
                    result([
                        'url'  => $url,
                        'path' => '/pages/code/choose_code?bid=' . $bid . '&from=verify_code',
                    ]);
                    break;
                default:
                    error($db_coupon_send_note->getError());
            }
        } else {
            result($result);
        }
    }

    protected function get_disable_date_list_by_coupon_guid($bid, $coupon_guid, $coupon_send_note_guid = null, $choose_goods_guid_list = null)
    {
        $db_coupon = new Coupon();
        return $db_coupon->get_disable_date_list_by_coupon_guid($bid, $coupon_guid, $coupon_send_note_guid, $choose_goods_guid_list);
    }

    public function get_disable_date_list()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $token                 = $params['token'] ?? null;
        $coupon_guid           = $params['coupon_guid'] ?? null;
        $coupon_send_note_guid = $params['coupon_send_note_guid'] ?? null;
        if (empty($coupon_guid)) {
            $info = cache($token);
            if (empty($info)) {
                throw new NotNotifyException('请退出后重试');
            }
            if ($info['bid'] != $bid) {
                throw new NotNotifyException('请退出后重试!');
            }
            $coupon_guid           = $info['coupon_guid'];
            $coupon_send_note_guid = $info['coupon_send_note_guid'];
        }
        $disable_date_array = $this->get_disable_date_list_by_coupon_guid($bid, $coupon_guid, $coupon_send_note_guid);
        result(['disable_date_list' => $disable_date_array]);
    }

    public function get_goods_list_by_coupon_guid()
    {
        $params           = $this->params;
        $bid              = $this->get_bid();
        $coupon_guid      = $params['guid'] ?? '';
        $code             = $params['code'] ?? '';
        $full_return      = $params['full_return'] ?? true;
        $coupon_send_note = [];
        if (empty($coupon_guid) && $code) {
            $db_coupon_send_note  = new CouponSendNote();
            $map_coupon_send_note = [
                ['bid', '=', $bid],
                ['code', '=', $code],
            ];
            $coupon_guid          = $db_coupon_send_note->where($map_coupon_send_note)->value('coupon_guid');
            if (empty($coupon_guid)) {
                throw new NotNotifyException('请退出后重试');
            }
        }
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info = $db_coupon->where($map_coupon)->findOrEmpty();

        $db_coupon_goods_item = new CouponGoodsItem();
        $goods_list           = $db_coupon_goods_item->get_goods_list($bid, $coupon_guid, $full_return);
        foreach ($goods_list as $key => $val) {
            $goods_list[$key]['pic'] = tools()::add_thumbnail_small($val['pic']);
        }
        $coupon_info['description']        = tools()::remove_taobao_jd_class($coupon_info['description']);
        $coupon_info['description']        = tools()::add_rich_img_class($coupon_info['description']);
        $coupon_info['simple_description'] = tools()::add_rich_img_class($coupon_info['simple_description']);

        $exchange_type      = 1; //1  一选一  2  多选一  3 多选多
        $exchange_goods_num = $coupon_info['exchange_goods_num'];
        $goods_list_num     = count($goods_list);
        if ($exchange_goods_num == 1 && $goods_list_num > 1) {
            $exchange_type = 2;  // 2  多选一
        } elseif ($exchange_goods_num > 1 && $goods_list_num > 1) {
            $exchange_type = 3; //  3 多选多
        }
        if ($exchange_type == 1) {
            // 一选一的时候动态设置 第一个商品的选择商品默认1件
            $goods_list[0]['min_choose_num'] = $goods_list[0]['min_choose_num'] == 0 ? 1 : $goods_list[0]['min_choose_num'];

        }
        if (empty($coupon_send_note) && $code) {
            $db_coupon_send_note  = new CouponSendNote();
            $map_coupon_send_note = [
                ['bid', '=', $bid],
                ['coupon_guid', '=', $coupon_guid],
                ['code', '=', $code],
            ];

            $field            = [
                Db::raw("date_format(expire_time,'%Y-%m-%d') as expire_time"),
                Db::raw("date_format(availability_time,'%Y-%m-%d') as availability_time"),
                'status',
                Db::raw('send_num - used_num as available_use_num'),
                Db::raw('send_value - used_value as available_use_value'),
            ];
            $coupon_send_note = $db_coupon_send_note->field($field)->where($map_coupon_send_note)->findOrFail();
        }
        $config              = get_config_by_bid($bid);
        $goods_category_list = $db_coupon_goods_item->get_goods_list_with_cat($bid, $goods_list);
        result([
            'coupon_info'         => $coupon_info,
            'coupon_send_note'    => $coupon_send_note,
            'goods_list'          => $goods_list,
            'goods_category_list' => $goods_category_list,
            'goods_list_num'      => $goods_list_num,
            'exchange_type'       => $exchange_type,
            'config'              => [
                'choose_goods_show_price' => (int)$config['choose_goods_show_price'],
                'template_type'           => (int)$config['multiple_choose_single_template_type'],
                'choose_goods_notice'     => $config['choose_goods_notice'],
            ]
        ]);
    }

    public function get_goods_list_by_token()
    {
        $params = $this->params;
        $bid    = $this->get_bid();

        // 支持三种模式的参数
        $token       = $params['token'] ?? null;
        $code        = $params['code'] ?? null;
        $coupon_guid = $params['guid'] ?? null;

        // 验证参数
        if (!$token && !$code && !$coupon_guid) {
            error('缺少必要参数：需要token、code或guid');
        }

        $db_coupon_send_note = new CouponSendNote();
        $data                = [];
        if ($token) {
            // Token模式：已验证用户，返回完整信息
            $data = $db_coupon_send_note->get_code_info_by_token($bid, $token);
        } elseif ($code) {
            // Code模式：未验证用户，返回安全信息
            $data = $db_coupon_send_note->get_code_info_by_code($bid, $code, $coupon_guid);
        } elseif ($coupon_guid) {
            // GUID模式：仅券卡GUID，返回基础信息
            $data = $db_coupon_send_note->get_coupon_info_by_coupon_guid($bid, $coupon_guid);
        }

        result($data);
    }

    /**
     *订单预览/用于计算商品是否需要支付等
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_preview()
    {
        $db_goods_order = new GoodsOrder();
        $order_data     = $db_goods_order->submit_preview($this->params);
        result($order_data);
    }

    public function submit_buy_code_order()
    {
        $params            = $this->params;
        $bid               = $this->get_bid();
        $member_guid       = $this->get_member_guid();
        $num               = $params['amount'] ?? 1;
        $coupon_guid       = $params['coupon_guid'];
        $share_member_guid = $params['share_member_guid'] ?? null;
        $share_user_guid   = $params['share_user_guid'] ?? null;
        $db_coupon         = new Coupon();
        $map_sell_code     = [
            'guid' => $coupon_guid
        ];
        $coupon_info       = $db_coupon->get_sell_code($map_sell_code, true);
        if ($coupon_info['discount_selling_price'] == 0) {
            error('售价为0不允许销售');
        }
        $total_money = tools()::nc_price_calculate($num, '*', $coupon_info['discount_selling_price'], 2);
        if ($total_money <= 0) {
            error('金额计算有误');
        }
        $openid               = $this->get_openid();
        $appid                = $this->get_appid();
        $db_coupon_sell_order = new CouponSellOrder();
        $order_guid           = create_guid();
        $bill_number          = tools()::get_bill_number();
        $third_order_number   = tools()::get_bill_number();
        $order_data           = [
            'bid'                => $bid,
            'guid'               => $order_guid,
            'coupon_guid'        => $coupon_guid,
            'num'                => $num,
            'bill_number'        => $bill_number,
            'total_money'        => $total_money,
            'openid'             => $openid,
            'appid'              => $appid,
            'member_guid'        => $member_guid,
            'third_order_number' => $third_order_number,
            'share_member_guid'  => $share_member_guid,
            'share_user_guid'    => $share_user_guid,
            'status'             => -1, //待支付
            'pay_type'           => 1, //暂时仅支持微信支付
        ];
        $db_coupon_sell_order->save($order_data);
        result(['order_guid' => $order_guid, 'bill_number' => $order_data['bill_number'], 'status' => -1], '下单成功');
    }

    public function submit_order()
    {
        $db_goods_order = new GoodsOrder();
        $order_data     = $db_goods_order->submit($this->params);
        result($order_data, '提货成功');
    }

    public function modify_order()
    {
        $params                       = $this->params;
        $bid                          = $this->get_bid();
        $order_guid                   = $params['order_guid'];
        $request_send_or_pick_up_time = $params['request_send_or_pick_up_time'];
        $db_goods_order               = new GoodsOrder();
        $map                          = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
            ['status', '=', 0],
        ];
        $update_data                  = ['request_send_or_pick_up_time' => $request_send_or_pick_up_time];
        $db_goods_order::update($update_data, $map);
        success('修改成功');
    }

    public function get_verify_qrcode()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $db_coupon_send_note   = new CouponSendNote();
        $map_coupon_send_note  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_note_guid]
        ];
        $coupon_send_note      = $db_coupon_send_note->where($map_coupon_send_note)->find();
        $coupon_guid           = $coupon_send_note['coupon_guid'];
        $code                  = $coupon_send_note['code'];
        $password              = $coupon_send_note['password'];
        $map_coupon            = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid]
        ];
        $db_coupon             = new Coupon();
        $coupon_info           = $db_coupon->where($map_coupon)->find();
        $exp                   = 300;
        $long_url              = '';
        $token                 = create_guid();
        if (in_array($coupon_info['type'], [1, 2])) {
            // 数量卡 生成进入提货选商品页面的二维码
            $cache_data = [
                'bid'                   => $bid,
                'coupon_guid'           => $coupon_guid,
                'coupon_send_note_guid' => $coupon_send_note_guid,
            ];
            cache($token, $cache_data, $exp);
            //转换成短链接
            $long_url = (string)url('admin/code/choose_goods', ['bid' => $bid, 'token' => $token, 'from' => 'mobile'], false, true);
            //        $image_url = (string)url('index/plugins/create_qrcode', ['size' => 300, 'data' => $url], false, true);

        } else if ($coupon_info['type'] == 3) {
            //礼品充值卡 进入扣费页面
            //转换成短链接
            $long_url = (string)url('admin/coupon_send_note/deduct_money', ['bid' => $bid, 'c' => $code, 'p' => $password, 'token' => $token], false, true);
            //        $image_url = (string)url('index/plugins/create_qrcode', ['size' => 300, 'data' => $url], false, true);
            //http://www.yikayi.net/admin/coupon_send_note/deduct_money?bid=dd943a84-f26e-1dff-1f5b-c957b9b9fde3&c=RS100002&p=356534
        }
        $short_url  = UrlService::long_to_short($long_url, $exp);
        $full_url   = UrlService::get_full_url($short_url);
        $qrcode_url = UrlService::text_to_qrcode_url($full_url);
        result(['image_url' => $qrcode_url]);
    }

    public function online_free_receive_code()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $coupon_guid = $params['coupon_guid'];

        //判断金额卡,核销对应的卡券金额
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $value       = 0;
        $coupon_info = $db_coupon->where($map_coupon)->findOrFail();
        if ($coupon_info['type'] == 2) {
            $value = $coupon_info['value'];
        }
        //发放
        $data = [
            'bid'         => $bid,
            'coupon_guid' => $coupon_guid,
            'member_guid' => $member_guid,
            'num'         => 1,
            'value'       => $value,
            'way'         => 5, // 在线领取
        ];
        $send = $db_coupon->send_coupon($data);
        if ($send === false) {
            error($db_coupon->getError());
        }
        success('领取成功');
    }

    public function receive_share_code()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $member_guid          = $this->get_member_guid();
        $share_note_guid      = $params['share_note_guid'];
        $db_coupon_share_note = new CouponShareNote();
        $map                  = [
            ['bid', '=', $bid],
            ['guid', '=', $share_note_guid],
        ];
        $share_note           = $db_coupon_share_note->where($map)->findOrEmpty();
        if ($share_note->isEmpty()) {
            error('分享记录不存在');
        }
        if ($share_note['status'] !== 0) {
            error('当前卡券已经被领取过了!');
        }
        $coupon_guid          = $share_note['coupon_guid'];
        $share_member_guid    = $share_note['share_member_guid'];
        $share_send_note_guid = $share_note['share_send_note_guid'];
        $share_num            = $share_note['share_num'];
        $share_value          = $share_note['share_value'];
        $share_time           = $share_note['share_time'];  //后续校验 时间过长不允许领取
        $used_value           = 0;
        if ($member_guid == $share_member_guid) {
            error('不能领取自己分享的卡券');
        }
        //判断金额卡+充值卡,核销对应的卡券金额
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info = $db_coupon->where($map_coupon)->findOrEmpty();
        if ($coupon_info->isEmpty()) {
            error('卡券已经被删除');
        }
        $coupon_type = $coupon_info['type'];
        if (in_array($coupon_type, [2, 3])) {
            $used_value = $share_num * $coupon_info['value'];
            if (floatval($used_value) !== floatval($share_value)) {
                $msg = '优惠券领取失败,请稍候再试!';
                wr_log('$used_value 校验 $share_value 不通过,$used_value=' . $used_value . ',$share_value=' . $share_value . '$params=' . json_encode($params, JSON_UNESCAPED_UNICODE), 1);
                error($msg);
            }
        }
        if ($coupon_info['share_status'] == 0) {
            error('当前卡券不支持转赠');
        }
        //先核销
        $db_coupon_send_note = new CouponSendNote();
        $data                = [
            'bid'                   => $bid,
            'coupon_send_note_guid' => $share_send_note_guid,
            'member_guid'           => $share_member_guid,
            'relation_guid'         => $share_note_guid,
            'way'                   => 2, //卡券转赠
            'used_num'              => $share_num,
            'used_value'            => $used_value,
        ];
        $used                = $db_coupon_send_note->use_coupon($data);
        if ($used === false) {
            $msg = '优惠券领取失败,请稍候再试!';
            wr_log($msg . $db_coupon->getError(), 1);
            error($msg);
        }
        $db_coupon_send_note  = new CouponSendNote();
        $map_coupon_send_note = [
            ['bid', '=', $bid],
            ['guid', '=', $share_send_note_guid],
        ];
        $coupon_send_note     = $db_coupon_send_note->field(['availability_time', 'expire_time'])->where($map_coupon_send_note)->findOrFail();
        //再发放
        $data = [
            'bid'               => $bid,
            'coupon_guid'       => $coupon_guid,
            'member_guid'       => $member_guid,
            'num'               => $share_num,
            'value'             => $used_value,
            'availability_time' => $coupon_send_note['availability_time'],
            'expire_time'       => $coupon_send_note['expire_time'],
            'way'               => 4, // 转赠
        ];
        $send = $db_coupon->send_coupon($data);
        if ($send === false) {
            error($db_coupon->getError());
        }
        $update_data = [
            'status'                 => 1,
            'receive_member_guid'    => $member_guid,
            'share_used_note_guid'   => $used['guid'],
            'receive_send_note_guid' => $send['guid'],
            'receive_time'           => microsecond(),
        ];
        $share_note::update($update_data, $map);
        success('卡券领取成功');
    }

    public function get_share_note_info()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        // $member_guid          = $this->get_member_guid();
        $share_note_guid      = $params['share_note_guid'];
        $db_coupon_share_note = new CouponShareNote();
        $map                  = [
            ['bid', '=', $bid],
            ['guid', '=', $share_note_guid],
        ];
        $share_note           = $db_coupon_share_note->where($map)->findOrEmpty();
        if ($share_note->isEmpty()) {
            error('分享记录不存在');
        }
        result($share_note);
    }

    public function get_share_note_guid()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $share_num             = $params['share_num'] ?? 1;
        $db_coupon_send_note   = new CouponSendNote();
        $send_note_info        = $db_coupon_send_note->get_code_info($bid, $coupon_send_note_guid);
        $coupon_info           = $send_note_info['coupon_info'];
        if ($coupon_info['share_status'] == 0) {
            error('当前卡券不支持转赠哦');
        }
        $share_value = 0;
        $coupon_type = $coupon_info['type']; //1数量卡 2金额卡 3充值卡
        //        $send_num    = $send_note_info['send_num'];
        //        $used_num    = $send_note_info['used_num'];
        //        $send_value  = $send_note_info['send_value'];
        //        $used_value  = $send_note_info['used_value'];

        if (in_array($coupon_type, [2, 3])) {
            $coupon_value    = $coupon_info['value']; //卡券面额
            $available_value = $send_note_info['available_value'];
            $share_value     = tools()::nc_price_calculate($coupon_value, '*', $share_num, 2);
            $share_value     = min($share_value, $available_value);
            if ($share_value <= 0) {
                throw new Exception('卡券可用余额不足');
            }
        }
        $available_num        = $send_note_info['available_num'];
        $share_num            = min($share_num, $available_num); //转赠张数和可用张数取最小值,避免漏洞
        $db_coupon_share_note = new CouponShareNote();
        $share_note_guid      = create_guid();
        $insert_data          = [
            'guid'                 => $share_note_guid,
            'bid'                  => $bid,
            'share_member_guid'    => $member_guid,
            'coupon_guid'          => $send_note_info['coupon_guid'],
            'share_send_note_guid' => $coupon_send_note_guid,
            'share_num'            => $share_num,
            'share_value'          => $share_value,
            'share_time'           => microsecond(),
        ];
        $db_coupon_share_note->save($insert_data);
        result(['share_note_guid' => $share_note_guid, 'share_member_guid' => $member_guid]);
    }

    public function code_note_detail()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $db_coupon_send_note   = new CouponSendNote();
        $data                  = $db_coupon_send_note->get_code_info($bid, $coupon_send_note_guid, false);
        result($data);
    }

    public function get_code_info()
    {
        $params = $this->params;
        $q      = $params['q'] ?? null;
        if ($q) {
            $q      = urldecode($q);
            $params = tools()::parse_url_params($q);
        }
        if (empty($params['code'])) {
            result(['code' => '']);
        }
        $bid                  = $this->get_bid();
        $code                 = $params['code'];
        $db_coupon_send_note  = new CouponSendNote();
        $map_coupon_send_note = [
            ['bid', '=', $bid],
            ['code', '=', $code],
        ];
        $code_info            = $db_coupon_send_note->where($map_coupon_send_note)->field(['status'])->findOrEmpty();
        if ($code_info->isEmpty()) {
            result(['code' => '']);
        }
        result(['status' => $code_info['status'], 'code' => $code]);
    }

    public function code_detail()
    {
        $params        = $this->params;
        $guid          = $params['guid'];
        $bid           = $this->get_bid();
        $member_guid   = $this->get_member_guid();
        $db_coupon     = new Coupon();
        $map_sell_code = ['guid' => $guid];
        $coupon_info   = $db_coupon->get_sell_code($map_sell_code, true);
        $pic_list      = [
            tools()::add_thumbnail($coupon_info['pic'])
        ];
        if (!empty($coupon_info['pic1'])) {
            $pic_list[] = tools()::add_thumbnail($coupon_info['pic1']);
        }
        if (!empty($coupon_info['pic2'])) {
            $pic_list[] = tools()::add_thumbnail($coupon_info['pic2']);
        }
        if (!empty($coupon_info['pic3'])) {
            $pic_list[] = tools()::add_thumbnail($coupon_info['pic3']);
        }
        $coupon_info['pic_list']               = $pic_list;
        $coupon_info['pic']                    = tools()::add_thumbnail($coupon_info['pic']);
        $db_favorites                          = new FavoritesModel();
        $map                                   = [
            ['bid', '=', $bid],
            ['type', '=', 2],
            ['relation_guid', '=', $guid],
            ['member_guid', '=', $member_guid],
        ];
        $is_favorite                           = $db_favorites->where($map)->value('status');
        $coupon_info['is_favorite']            = $is_favorite ? 1 : 0;
        $coupon_info['description']            = tools()::remove_taobao_jd_class($coupon_info['description']);
        $coupon_info['description']            = tools()::add_rich_img_class($coupon_info['description']);
        $coupon_info['simple_description']     = tools()::add_rich_img_class($coupon_info['simple_description']);
        $coupon_info['selling_price']          = $coupon_info['discount_selling_price'];
        $coupon_info['selling_original_price'] = $coupon_info['discount_selling_price'];
        result($coupon_info);
    }

    public function share_qrcode()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $member_guid   = $this->get_member_guid();
        $appid         = $this->get_appid();
        $goods_guid    = $params['goods_guid'];
        $db_coupon     = new Coupon();
        $map           = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $goods_info    = $db_coupon->where($map)->find();
        $goods_pic     = $goods_info['pic'];
        $goods_name    = $goods_info['name'];
        $goods_price   = $goods_info['selling_price'];
        $db_business   = new \app\model\Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $business_name = $business_info['business_name'];
        $path          = '/pages/index/coupon_detail?guid=' . $goods_guid . '&share_member_guid=' . $member_guid;
        $version       = '**********';
        $file_path     = '/temp/images/' . md5($version . $appid . $path . $goods_pic . $goods_name . $goods_price . $business_name) . '.png';
        $from          = 'cache';
        $absolute_path = tools()::get_absolute_path($file_path);
        if (!file_exists($absolute_path)) {
            $from         = 'api';
            $mini         = weixin($appid)::WeMiniQrcode();
            $qrcodeBinary = $mini->createMiniPath($path, 300, false, ['r' => '0', 'g' => '0', 'b' => '0'], false);

            // 使用白底+商品图合成方式
            $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
            Image::configure(['driver' => $driver_name]);

            // 1. 创建白色画布
            $image = Image::canvas(750, 1334, '#fff');

            // 2. 商品图片
            $goods_pic_local_path = tools()::web_url_to_local_absolute_path($goods_pic);
            $goods_image          = Image::make($goods_pic_local_path)->resize(750, null, function (Constraint $constraint) {
                $constraint->aspectRatio();
            });
            $image->insert($goods_image, 'top-left', 0, 0);

            // 3. 计算商品图高度
            $goods_image_height = $goods_image->height();

            // 4. 其余元素插入到商品图下方
            // 二维码（直接用二进制内容生成Image对象，无需file_put_contents）
            $qrcode_image = Image::make($qrcodeBinary);
            $image->insert($qrcode_image, 'bottom-right', 30, 30);

            // 字体路径
            $font = tools()::get_absolute_path('/static/css/fonts/MSYH.TTC');

            // 商品名
            $image->text($goods_name, 10, 780, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(36);
                $font_draw->color('#000000');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 价格
            $image->text('￥' . $goods_price, 10, 920, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(48);
                $font_draw->color('#ff4544');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 商家名
            $image->text($business_name, 10, 1130, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(36);
                $font_draw->color('#575757');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            // 长按识别
            $image->text('长按识别小程序访问', 10, 1200, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(30);
                $font_draw->color('#575757');
                $font_draw->align('left');
                $font_draw->valign('top');
            });

            $image->save($absolute_path);
        }
        $data = [
            'from'    => $from,
            'pic_url' => tools()::path_to_web($file_path),
        ];
        result($data);
    }

    public function member_code_list()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $goods_guid          = $params['goods_guid'] ?? null;
        $status              = isset($params['status']) ? (int)$params['status'] : null;
        $member_guid         = $this->get_member_guid();
        $db_coupon_send_note = new CouponSendNote();
        $join                = [
            ['coupon c', 'csn.coupon_guid = c.guid AND csn.bid = c.bid'],
        ];
        $field               = [
            'c.name',
            'c.pic',
            'c.type',
            'c.share_status',
            'c.bid',
            'cast(c.value as decimal(9,0))' => 'value',
            'csn.status',
            'csn.coupon_guid',
            'csn.send_num',
            'csn.used_num',
            'csn.send_value',
            'csn.used_value',
            Db::raw('csn.send_num - csn.used_num as available_use_num'),
            Db::raw('csn.send_value - csn.used_value as available_use_value'),
            'csn.bid',
            'csn.guid',
            'csn.code',
            'csn.password',
            'csn.mobile',
            Db::raw("date_format(csn.expire_time,'%Y-%m-%d') as expire_time"),
            Db::raw("date_format(csn.availability_time,'%Y-%m-%d') as availability_time"),
        ];
        $map                 = [
            ['csn.bid', '=', $bid],
            ['c.delete_time', 'null', null],
            ['csn.delete_time', 'null', null],
            ['csn.revoke_time', 'null', null],
            ['csn.member_guid', '=', $member_guid],
        ];
        if ($goods_guid) {
            $db_coupon_goods_item  = new CouponGoodsItem();
            $map_coupon_goods_item = [
                ['bid', '=', $bid],
                ['goods_guid', '=', $goods_guid],
            ];
            $coupon_guid_array     = $db_coupon_goods_item->where($map_coupon_goods_item)->column('coupon_guid');
            if ($coupon_guid_array) {
                $map[] = ['csn.coupon_guid', 'IN', $coupon_guid_array];
            }
        }
        $now_time = format_timestamp();
        if (!is_null($status)) {
            switch ($status) {
                case 0:
                    $map[] = ['csn.status', 'IN', [0]];
                    $map[] = ['csn.expire_time', '>', $now_time];
                    break;
                case 1:
                    $map[] = ['csn.status', 'IN', [1]];
                    break;
                case 2:
                    $map[] = ['csn.expire_time', '<', $now_time];
                    break;
            }
        }
        $member_code_list  = $db_coupon_send_note->alias('csn')->append(['is_expired'])->join($join)->field($field)->where($map)->order(['csn.create_time' => 'DESC'])->select();
        $map               = [
            ['csn.bid', '=', $bid],
            ['csn.member_guid', '=', $member_guid],
            ['csn.delete_time', 'null', null],
            ['csn.revoke_time', 'null', null],
            ['c.delete_time', 'null', null],
            //           ['csn.used_num', '=', 0],
            ['csn.status', 'IN', [0]],
            ['csn.expire_time', '>', $now_time],
        ];
        $available_use_num = $db_coupon_send_note->alias('csn')->append(['is_expired'])->join($join)->where($map)->count();
        //已使用卡券数量
        $map      = [
            ['csn.bid', '=', $bid],
            ['csn.member_guid', '=', $member_guid],
            ['csn.delete_time', 'null', null],
            ['csn.revoke_time', 'null', null],
            ['c.delete_time', 'null', null],
            //['csn.used_num', '>', 0],
            ['csn.status', 'IN', [1]],
            //['csn.expire_time', '>', $now_time],
        ];
        $used_num = $db_coupon_send_note->alias('csn')->append(['is_expired'])->join($join)->where($map)->count();
        //过期卡券数量
        $map         = [
            ['csn.bid', '=', $bid],
            ['csn.member_guid', '=', $member_guid],
            ['csn.delete_time', 'null', null],
            ['csn.revoke_time', 'null', null],
            ['c.delete_time', 'null', null],
            ['csn.expire_time', '<', $now_time],
        ];
        $expired_num = $db_coupon_send_note->alias('csn')->append(['is_expired'])->join($join)->where($map)->count();
        if (!$member_code_list->isEmpty()) {
            foreach ($member_code_list as $key => $val) {
                $member_code_list[$key]['pic'] = tools()::add_thumbnail_mini($val['pic']);
            }
        }
        $data = [
            'list'              => $member_code_list,
            'available_use_num' => $available_use_num,
            'used_num'          => $used_num,
            'expired_num'       => $expired_num,
        ];
        result($data);
    }

    public function get_coupon_url()
    {
        $params             = $this->params;
        $bid                = $this->get_bid();
        $config             = get_config_by_bid($bid);
        $is_sample_business = $config['is_sample_business'];
        if (empty($is_sample_business)) {
            error('商家暂时未开放体验站功能');
        }
        $coupon_guid         = $params['coupon_guid'];
        $db_coupon_send_note = new CouponSendNote();
        $map                 = [
            ['bid', '=', $bid],
            ['coupon_guid', '=', $coupon_guid],
            ['status', '=', 0],
            ['availability_time', '>', format_timestamp()],
            ['expire_time', '<', format_timestamp()],
        ];
        $coupon_send_note    = $db_coupon_send_note->where($map)->order(['create_time' => 'ASC'])->findOrEmpty();
        if ($coupon_send_note->isEmpty()) {
            error('该卡券体验需要联系客服');
        }
        $url_params = [
            'bid'         => $bid,
            'coupon_guid' => $coupon_guid,
            'c'           => $coupon_send_note['code'],
            'p'           => $coupon_send_note['password'],
        ];
        $url        = (string)url('member/code/index', $url_params, false, true);
        result(['url' => $url]);
    }

    public function category_list()
    {
        $params             = $this->params;
        $bid                = $this->get_bid();
        $config             = get_config_by_bid($bid);
        $is_sample_business = $config['is_sample_business'];
        if (empty($is_sample_business)) {
            error('系统繁忙');
        }
        $db_goods_category               = new CouponCategory();
        $map_goods_category              = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
            ['name', 'NOT LIKE', '%.%'],
        ];
        $order_goods_category            = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        $filed_goods_category            = [
            'bid',
            'guid',
            'name',
            'mini_pic'
        ];
        $goods_category_list_with_coupon = $db_goods_category->where($map_goods_category)->field($filed_goods_category)->order($order_goods_category)->append(['coupon'])->order(['sort' => 'ASC'])->select();
        result($goods_category_list_with_coupon);
    }

    public function code_list()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $db_coupon   = new Coupon();
        $coupon_list = $db_coupon->get_sell_code();
        result($coupon_list);
    }


    public function get_last_order_info()
    {
        return $this->last_order_info($this->get_bid(), $this->get_member_guid());
    }

    protected function last_order_info($bid, $member_guid)
    {
        $config                  = get_config_by_bid($bid);
        $is_load_last_order_info = $config['is_load_last_order_info'];
        if (empty($is_load_last_order_info)) {
            return [];
        }
        $db_goods_order = new GoodsOrder();
        $map            = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['delete_time', 'NULL', NULL],
        ];
        $order_info     = $db_goods_order->where($map)->field(['id_card_number', 'remark', 'mobile', 'address', 'true_name', 'province_id', 'city_id', 'area_id'])->order(['create_time' => 'DESC'])->findOrEmpty();
        $db_area        = new Area();
        $order_info     = $order_info->isEmpty() ? $db_area->ip_to_area_id() : $order_info->toArray();
        $db_area_rule   = new AreaRule();
        $map_area_rule  = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $count          = $db_area_rule->where($map_area_rule)->count();
        if ($count > 0 && $db_area_rule->check_area($bid, $order_info) == false) {
            $order_info = [];
        }
        return $order_info;
    }

    public function get_start_page_config()
    {
        $params      = $this->params;
        $coupon_guid = $params['coupon_guid'];
        $bid         = $this->get_bid();
        $db_coupon   = new Coupon();
        $map         = [['bid', '=', $bid], ['guid', '=', $coupon_guid]];
        $coupon_info = $db_coupon->field(['start_page_img'])->where($map)->find();
        if (empty($coupon_info['start_page_img'])) {
            error('启动页未定义');
        }
        $url            = (string)url('member/code/detail', ['bid' => $bid, 'guid' => $coupon_guid], false, true);
        $start_page_img = $coupon_info['start_page_img'];
        $data           = [
            'color' => '#50905C',
            'url'   => $url,
            'img'   => tools()::add_thumbnail($start_page_img),
        ];
        result($data);
    }

    public function get_after_submit_order_config()
    {
        $params                          = $this->params;
        $bid                             = $this->get_bid();
        $config                          = get_config_by_bid($bid);
        $after_submit_order_tips_title   = $config['after_submit_order_tips_title'];
        $after_submit_order_tips_content = $config['after_submit_order_tips_content'];
        $weapp_username                  = $config['weapp_username'];
        $appid                           = $config['appid'];
        $follow_wechat                   = $config['follow_wechat'];
        $wechat_qrcode                   = '';
        $subscribe_url                   = '';
        $subscribe_template_id_list      = '';
        if ($appid && $follow_wechat) {
            $db_wechat_config = new WechatConfig();
            $wechat_info      = $db_wechat_config->get_appid_info($appid, ['user_name']);
            $wechat_user_name = $wechat_info['user_name'];
            if ($wechat_user_name) {
                $wechat_qrcode = 'https://open.weixin.qq.com/qr/code?username=' . $wechat_user_name;
            }
        }
        $db_business     = new \app\model\Business();
        $is_examples_bid = $db_business->is_examples_bid($bid);
        if ($appid && $is_examples_bid) {
            $subscribe_url = WeixinService::get_subscribe_url($bid, $appid);
        }
        $data = [
            'weapp_username'             => $weapp_username,
            'wechat_qrcode'              => $wechat_qrcode,
            'subscribe_template_id_list' => $subscribe_template_id_list,
            'subscribe_url'              => $subscribe_url,
            'title'                      => $after_submit_order_tips_title,
            'content'                    => '<b>' . $after_submit_order_tips_content . '</b>'
        ];
        result($data);
    }

    public function get_submit_order_config()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $token  = $params['token'] ?? null;
        $way    = $params['way'] ?? 1;
        if (!$token && $way != 2) {
            error('token不能为空');
        }
        $choose_goods_list = $params['choose_goods_list'] ?? null; //用于支持日期规则支持限制产品级
        //   array(1) {
        //   ["f482584c-fe5a-9f3d-db66-1b45c53f4f4d"] => string(1) "1"
        //}
        $choose_goods_guid_list = $choose_goods_list ? array_keys($choose_goods_list) : null; //用于支持日期规则支持限制产品级

        $goods_info = $params['goods_info'] ?? null; //用于支持日期规则支持限制产品级
        if (empty($choose_goods_guid_list) && !empty($goods_info)) {
            $choose_goods_guid_list = array_column($goods_info, 'guid');
        }

        $db_coupon_send_note = new CouponSendNote();

        if ($token) {
            $token_info                           = $db_coupon_send_note->get_code_info_by_token($bid, $token);
            $coupon_guid                          = $token_info['coupon_guid'];
            $map                                  = [['bid', '=', $bid], ['guid', '=', $coupon_guid]];
            $db_coupon                            = new Coupon();
            $coupon_info                          = $db_coupon->field(['cycle_delivery_min_interval_days', 'cycle_delivery'])->where($map)->find();
            $config                               = get_config_by_bid($bid, 'coupon', $coupon_guid);
            $cycle_delivery_max_exchange_num_once = $config['cycle_delivery_max_exchange_num_once'];
            $available_num                        = $token_info['available_num'];
            $available_num                        = min($available_num, $cycle_delivery_max_exchange_num_once);
            $disable_date_list                    = $this->get_disable_date_list_by_coupon_guid($bid, $coupon_guid, null, $choose_goods_guid_list);
            $disable_date_array                   = $disable_date_list['disable_date_list'];
            $disable_date_list_mark               = $disable_date_list['disable_date_list_mark'];
        } else {
            $config                 = get_config_by_bid($bid);
            $coupon_info            = [];
            $available_num          = 0;
            $disable_date_array     = [];
            $disable_date_list_mark = [];
        }

        $default_type = 1;  //1 快递发货 2到店自提
        if ($config['is_show_address'] == 0 && $config['is_show_request_send_or_pick_up_store']) {
            $default_type = 2;  //1 快递发货 2到店自提
        }
        if ($config['is_show_request_send_or_pick_up_store'] == 0 && $config['is_show_address'] == 0) {
            $default_type = 0;  // 不需要快递配送和到店自提
        }


        $db_extend_field         = new ExtendField();
        $show_extend_field_array = $db_extend_field->get_extend_field_array($bid);

        $prepare_goods_days = (int)$config['prepare_goods_days'];
        $latest_submit_time = (int)$config['latest_submit_time']; //暂时只支持小时 如 18
        if (date('H') >= $latest_submit_time) {
            //备货天数为0 则判断是否过了当天最晚预约时间
            $prepare_goods_days += 1;
        }
        //        if ($prepare_goods_days == 0) {
        //            if (date('H') >= $latest_submit_time) {
        //                $prepare_goods_days = 1;
        //            }
        //        }

        $max_request_send_or_pick_up_days = (int)$config['max_request_send_or_pick_up_days'];

        $data = [
            'default_type'                          => $default_type,
            'is_show_remark'                        => $config['is_show_remark'], //是否展示备注
            'is_show_true_name'                     => $config['is_show_true_name'], //是否展示姓名
            'true_name_text'                        => $config['true_name_text'],
            'is_show_last_name'                     => $config['is_show_last_name'], //是否展示姓氏
            'is_show_sex'                           => $config['is_show_sex'], //是否展示性别
            'allow_modify_cycle_delivery'           => $config['allow_modify_cycle_delivery'], //是否允修改周期
            'is_show_upload_image'                  => $config['is_show_upload_image'],
            'upload_image_example_tips'             => $config['upload_image_example_tips'],
            'upload_image_example_url'              => $config['upload_image_example_url'],
            'request_send_or_pick_up_time_text'     => $config['request_send_or_pick_up_time_text'],
            'is_show_request_send_or_pick_up_time'  => $config['is_show_request_send_or_pick_up_time'],
            'is_show_request_send_or_pick_up_store' => $config['is_show_request_send_or_pick_up_store'],
            'is_show_paid_money'                    => $config['is_show_paid_money'],
            'is_show_id_card_number'                => $config['is_show_id_card_number'],
            'is_show_address'                       => $config['is_show_address'],
            'is_show_address_area'                  => $config['is_show_address_area'],
            'weapp_service_type'                    => $config['weapp_service_type'],
            'service_phone'                         => $config['service_phone'],
            'wechat_service_url'                    => $config['wechat_service_url'],
            'wechat_service_corp_id'                => $config['wechat_service_corp_id'],
            'remark_placeholder'                    => $config['remark_placeholder'],
            'area_picker_type'                      => $config['area_picker_type'],
            'max_request_send_or_pick_up_days'      => $max_request_send_or_pick_up_days,
            'prepare_goods_days'                    => $prepare_goods_days,
            'disable_date_before'                   => date('Y-m-d', strtotime("+$prepare_goods_days day")),
            'disable_date_list'                     => $disable_date_array,
            'disable_date_list_mark'                => $disable_date_list_mark,
            'available_num'                         => $available_num,
            'coupon_info'                           => $coupon_info,
            'show_extend_field_array'               => $show_extend_field_array,
            'last_order_info'                       => $this->last_order_info($bid, $this->get_member_guid())
        ];
        result($data);
    }

    public function add_coupon_recommend_note()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();


        $db_goods_order = new GoodsOrder();
        $map            = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            //            ['delete_time', 'null', null],
            //            ['status', 'IN', [1, 2, 3, 4, 5, 6]],
        ];
        $count          = $db_goods_order->where($map)->value('id');
        if (empty($count)) {
            error('您没有推荐权限,请联系客服');
        }
        $code                = $params['code'];
        $password            = $params['password'];
        $data                = ['code' => $code, 'password' => $password];
        $db_coupon_send_note = new CouponSendNote();
        $coupon_send_note    = $db_coupon_send_note->verify($bid, $data, false);
        if ($coupon_send_note === false) {
            error($db_coupon_send_note->getError());
        }
        $coupon_send_note_guid    = $coupon_send_note['guid'];
        $db_coupon_recommend_note = new CouponRecommendNote();
        $map                      = [
            ['bid', '=', $bid],
            ['coupon_send_note_guid', '=', $coupon_send_note_guid],
        ];
        $bind_member_guid         = $db_coupon_recommend_note->where($map)->value('member_guid');
        if ($bind_member_guid) {
            if ($member_guid == $bind_member_guid) {
                error('您已添加过此优惠券');
            } else {
                error('此优惠券已添加过');
            }
        }

        $insert_data = [
            'guid'                  => create_guid(),
            'bid'                   => $bid,
            'member_guid'           => $member_guid,
            'coupon_send_note_guid' => $coupon_send_note_guid,
            'code'                  => $code,
            'status'                => 0,
        ];
        $db_coupon_recommend_note->save($insert_data);
        success('添加成功');
    }

    public function get_coupon_recommend_note()
    {
        $db_goods_order = new GoodsOrder();
        $map            = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            //            ['delete_time', 'null', null],
            //            ['status', 'IN', [1, 2, 3, 4, 5, 6]],
        ];
        $count          = $db_goods_order->where($map)->value('id');
        $count          = $count ? 1 : is_host();
        $list           = [];
        if ($count) {
            $db    = new CouponRecommendNote();
            $join  = [
                ['goods_order go', 'crn.bid=go.bid AND crn.reward_coupon_send_note_guid = go.coupon_send_note_guid', 'LEFT'],
            ];
            $field = [
                'crn.*',
                'go.guid'        => 'order_guid',
                'go.create_time' => 'order_create_time',
            ];
            $map   = [
                ['crn.bid', '=', $this->get_bid()],
                ['crn.member_guid', '=', $this->get_member_guid()],
                //            ['crn.delete_time', 'null', null],
                //            ['crn.status', 'IN', [1, 2, 3, 4, 5, 6]],
            ];
            $list  = $db->alias('crn')->where($map)->join($join)->field($field)->order(['crn.create_time' => 'DESC'])->select()->toArray();
            foreach ($list as $key => $val) {
                if ($val['status'] == 1 && $val['order_guid']) {
                    $list[$key]['status'] = 2;
                }
            }
        }

        $data = [
            'has_order' => (bool)$count,
            'list'      => $list,
        ];
        result($data);
    }

    public function code_to_member_money()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $code                  = $params['code'] ?? '';
        $password              = $params['password'] ?? '';
        $coupon_send_note_guid = $params['coupon_send_note_guid'] ?? '';
        $data                  = [];
        if ($code && $password) {
            $data = ['code' => $code, 'password' => $password];
        } elseif ($coupon_send_note_guid) {
            $data = ['coupon_send_note_guid' => $coupon_send_note_guid];
        }
        $db_coupon_send_note = new CouponSendNote();
        $available_value     = $db_coupon_send_note->code_to_member_money($data);
        $config              = get_config_by_bid($bid);
        success('充值成功' . $available_value . $config['money_unit']);
    }

    public function get_choose_goods_config()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $config      = get_config_by_bid($bid);
        $coupon_guid = '';
        $token       = $params['token'] ?? '';
        if (!empty($token)) {
            $info = cache($token);
            if (empty($info)) {
                throw new NotNotifyException('请退出后重试');
            }
            $coupon_guid = $info['coupon_guid'];
        }
        $choose_goods_notice_pop = tools()::add_rich_img_class($config['choose_goods_notice_pop']);
        $db_banner               = new Banner();
        $banner_list             = $db_banner->get_banner_list_of_limit_coupon($bid, 5, $coupon_guid);
        $data                    = [
            'banner_list'             => $banner_list,
            'choose_goods_notice_pop' => $choose_goods_notice_pop,
            'choose_goods_notice'     => $config['choose_goods_notice']
        ];
        result($data);
    }


    public function get_config()
    {
        $params = $this->params;
        $q      = $params['q'] ?? null;
        if ($q) {
            $q = urldecode($q);
            if (strpos($q, '/u/') !== false) {
                //短链接
                $short_code   = substr($q, -6);
                $db_short_url = new ShortUrl();
                $long_url     = $db_short_url->get_long_url($short_code);
                if ($long_url) {
                    $q = $long_url;
                }
            }
            $params = tools()::parse_url_params($q);
        }
        $code                = $params['code'] ?? null;
        $coupon_guid         = $params['coupon_guid'] ?? null;
        $bid                 = $this->get_bid();
        $db_business         = new \app\model\Business();
        $business_info       = $db_business->get_business_info_by_account_or_guid($bid);
        $business_name       = $business_info['business_name'];
        $config              = get_config_by_bid($bid);
        $map                 = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['type', '=', 1]
        ];
        $db_coupon_send_note = new CouponSendNote();
        if (!empty($code)) {
            //如果有传入券号则只获取该卡券或者全局banner
            $map_coupon_send_note = [
                ['bid', '=', $bid],
                ['code', '=', $code]
            ];
            $coupon_guid          = $db_coupon_send_note->where($map_coupon_send_note)->value('coupon_guid');
        }
        if ($coupon_guid) {
            $coupon_config = get_config_by_bid($bid, 'coupon', $coupon_guid);
            $config        = array_merge($config, $coupon_config);
        }
        $db_banner                  = new Banner();
        $banner_list                = $db_banner->get_banner_list_of_limit_coupon($bid, 1, $coupon_guid, true);
        $config['pick_up_notice']   = tools()::add_rich_img_class($config['pick_up_notice']);
        $data                       = [
            'title'                       => $business_name,
            'pick_up_notice'              => $config['pick_up_notice'],
            'allow_submit_order'          => (int)$config['allow_submit_order'],
            'pick_up_notice_pop'          => $config['pick_up_notice_pop'],
            'pick_up_type'                => (int)$config['pick_up_type'],
            'pickup_background_color'     => $config['pickup_background_color'],
            'pickup_background_image_url' => $config['pickup_background_image_url'],
            'show_scan_button'            => (bool)$config['show_scan_button'],
            'pick_up_page_html'           => $config['pick_up_page_html'],
            'code_field_alias'            => $config['code_field_alias'],
            'password_field_alias'        => $config['password_field_alias'],
            'banner_list'                 => $banner_list,
        ];
        $data['code_is_all_number'] = $db_coupon_send_note->code_is_all_number();
        result($data);
    }

    public function get_service_config()
    {
        //        $referer = $this->request->header('referer');
        $params = $this->params;
        $url    = $params['url'] ?? '';
        if ($url) {
            $params = array_merge($params, tools()::parse_url_params($url));
        }
        $q = $params['q'] ?? null;
        if ($q) {
            $q      = urldecode($q);
            $params = array_merge($params, tools()::parse_url_params($q));
        }
        $code                = $params['code'] ?? null;
        $coupon_guid         = $params['coupon_guid'] ?? null;
        $bid                 = $this->get_bid();
        $config              = get_config_by_bid($bid);
        $db_coupon_send_note = new CouponSendNote();
        if (!empty($code)) {
            //如果有传入券号则只获取该卡券或者全局banner
            $map_coupon_send_note = [
                ['bid', '=', $bid],
                ['code', '=', $code]
            ];
            $coupon_guid          = $db_coupon_send_note->where($map_coupon_send_note)->value('coupon_guid');
        }
        if ($coupon_guid) {
            $coupon_config = get_config_by_bid($bid, 'coupon', $coupon_guid);
            $config        = array_merge($config, $coupon_config);
        }
        $service_phone      = $config['service_phone'];
        $wechat_service_url = $config['wechat_service_url'];
        $is_open_duokefu    = $config['is_open_duokefu'];
        if (empty($wechat_service_url) && $is_open_duokefu) {
            $wechat_service_url = (string)url('member/kefu/index', ['bid' => $bid], false, true);
        }
        result([
            'service_phone'      => $service_phone,
            'wechat_service_url' => $wechat_service_url,
            'bid'                => $bid,
            'debug'              => is_debug(),
            'member_guid'        => $this->get_member_guid()
        ]);
    }
}
