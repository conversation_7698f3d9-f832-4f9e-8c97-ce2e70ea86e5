<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;

use app\model\DeviceTradeNoteEveryday;
use app\model\YkyDevice;
use app\model\YkyUserMap;
use Exception;

class Device extends BasicMemberApi
{
    /**
     * 验证是否有登陆
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function check_user()
    {
        $db_yky_user_map = new YkyUserMap();
        $map             = [
            ['appid', '=', $this->get_appid()],
            ['openid', '=', $this->get_openid()],
        ];
        $user_info       = $db_yky_user_map->where($map)->field(['user_name', 'user_account'])->find();
        if (empty($user_info)) {
            error('请先绑定商家账号');
        }
        return $user_info;
    }

    public function list()
    {
        $user_info = $this->check_user();
        $params    = $this->params;
        $db        = new YkyDevice();
        $field     = ['sn_code'];
        $map       = [
            ['business_account', '=', $user_info['user_name']],
            ['user_account', '=', $user_info['user_account']],
            ['sn_code', 'LIKE', 'YKA%'],
        ];
        //       $map       = [];
        $list = $db->where($map)->field($field)->select();
        result($list);
    }

    public function device_trade_note_everyday()
    {
        $user_info = $this->check_user();
        $params    = $this->params;
        $sn_code   = $params['sn_code'];
        $db        = new DeviceTradeNoteEveryday();
        $map       = [
            ['sn_code', '=', $sn_code]
        ];
        $order     = ['trade_date' => 'DESC'];
        $list      = $db->where($map)->order($order)->select();
        result($list);
    }
}