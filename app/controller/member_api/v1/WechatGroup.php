<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use Exception;

class WechatGroup extends BasicMemberApi
{
    /**
     *详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db     = new \app\model\WechatGroup();
        $params = $this->params;
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $params['guid']]
        ];
        $info   = $db->where($map)->find();
        result($info);
    }
}