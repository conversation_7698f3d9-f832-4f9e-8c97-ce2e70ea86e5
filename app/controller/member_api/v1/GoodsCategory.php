<?php

namespace app\controller\member_api\v1;

use app\model\GoodsCategory as GoodsCategoryModel;
use Exception;

class GoodsCategory extends BasicMemberApi
{
    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db_goods_category = new GoodsCategoryModel();
        $data              = [];
        $map               = [
            ['bid', '=', $this->get_bid()]
        ];
        $order             = [
            'sort'        => 'DESC',
            'create_time' => 'DESC',
        ];
        $list              = $db_goods_category->where($map)->order($order)->select()->toArray();
        foreach ($list as $key => $val) {
            //一级分类则追加
            $val['big_pic_url'] = $val['pic'];
            $val['pic_url']     = $val['pic'];
            if ($val['parent_guid'] == tools()::get_empty_guid()) {
                $val['list'] = [];
                foreach ($list as $k => $v) {
                    //属于子类则插入
                    if ($v['parent_guid'] == $val['guid']) {
                        $v['big_pic_url'] = $v['pic'];
                        $v['pic_url']     = $v['pic'];
                        $val['list'][]    = $v;
                    }
                }
                $data[] = $val;
            }
        }
        result(['list' => $data]);
    }
}