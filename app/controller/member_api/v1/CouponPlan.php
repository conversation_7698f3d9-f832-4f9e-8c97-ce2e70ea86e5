<?php

namespace app\controller\member_api\v1;

use app\model\GoodsCategory;
use app\model\CouponPlan as CouponPlanModel;
use app\model\Goods as GoodsModel;
use app\model\CouponPlanGoodsItem as CouponPlanGoodsItemModel;
use app\common\service\NotifyService;
use Exception;

class CouponPlan extends BasicMemberApi
{
    public function detail()
    {
        $bid              = $this->get_bid();
        $member_guid      = $this->get_member_guid();
        $params           = $this->params;
        $guid             = $params['guid'];
        $map              = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['guid', '=', $guid]
        ];
        $db_coupon_plan   = new CouponPlanModel();
        $coupon_plan_info = $db_coupon_plan->where($map)->findOrFail();
        //查询商品
        $db_coupon_plan_goods_item = new CouponPlanGoodsItemModel();
        $map                       = [
            ['bid', '=', $bid],
            ['coupon_plan_guid', '=', $guid]
        ];
        $goods_list                = [];
        $goods_guid_array          = $db_coupon_plan_goods_item->where($map)->column('goods_guid');
        if (!empty($goods_guid_array)) {
            $db_goods   = new GoodsModel();
            $map        = [
                ['bid', '=', $bid],
                ['guid', 'IN', $goods_guid_array]
            ];
            $goods_list = $db_goods->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC'])->select();
        }

        $data = [
            'coupon_plan_info' => $coupon_plan_info,
            'goods_list'       => $goods_list,
        ];
        result($data);
    }

    public function edit()
    {
        $bid                = $this->get_bid();
        $member_guid        = $this->get_member_guid();
        $params             = $this->params;
        $guid               = $params['guid'];
        $name               = $params['name'];
        $simple_description = $params['simple_description'];
        $map                = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
            ['member_guid', '=', $member_guid],
        ];

        $update_data    = [
            'name'               => $name,
            'simple_description' => $simple_description,
        ];
        $db_coupon_plan = new CouponPlanModel();
        $db_coupon_plan::update($update_data, $map);
        success('更新成功');
    }

    public function add()
    {
        $bid                = $this->get_bid();
        $member_guid        = $this->get_member_guid();
        $params             = $this->params;
        $name               = $params['name'];
        $simple_description = $params['simple_description'];
        $guid               = create_guid();
        $data               = [
            'name'               => $name,
            'simple_description' => $simple_description,
            'bid'                => $bid,
            'member_guid'        => $member_guid,
            'guid'               => $guid
        ];
        $db_coupon_plan     = new CouponPlanModel();
        $db_coupon_plan->save($data);
        $url  = (string)url('member/coupon_plan/detail', ['bid' => $bid, 'guid' => $guid], false, true);
        $data = [
            'url'         => $url,
            'title'       => '有新方案咨询',
            'detail'      => '方案简介:' . $simple_description,
            'user'        => '【有新方案咨询】',
            'remark'      => '点击查看',
            'name'        => '方案名称:' . $name,
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_business()->limit_agent()->set_data($data)->set_bid($bid)->send();
        result(['guid' => $guid], '新建成功');
    }

    public function list()
    {
        $db_coupon_plan = new CouponPlanModel();
        $bid            = $this->get_bid();
        $member_guid    = $this->get_member_guid();
        $params         = $this->params;
        $keyword        = $params['keyword'] ?? '';
        $map            = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid]
        ];
        if ($keyword) {
            $map[] = ['name', 'LIKE', '%' . $keyword . '%'];
        }
        unset($this->params['keyword']);
        $list = $db_coupon_plan->where($map)->order(['create_time' => 'DESC']);
        result($this->_paginate($list));
    }

    public function add_goods()
    {
        $db_coupon_plan_goods_item = new CouponPlanGoodsItemModel();
        $params                    = $this->params;
        $bid                       = $this->get_bid();
        $goods_guid                = $params['goods_guid'];
        $coupon_plan_guid          = $params['coupon_plan_guid'];
        $data                      = [
            'bid'              => $bid,
            'goods_guid'       => $goods_guid,
            'coupon_plan_guid' => $coupon_plan_guid,
            'guid'             => create_guid(),
        ];
        $db_coupon_plan_goods_item->save($data);

        $db_coupon_plan = new CouponPlanModel();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_plan_guid],
        ];
        $db_coupon_plan->where($map)->setInc('goods_num');

        success('添加成功');
    }

    public function del_goods()
    {
        $db_coupon_plan_goods_item = new CouponPlanGoodsItemModel();
        $params                    = $this->params;
        $bid                       = $this->get_bid();
        $member_guid               = $this->get_member_guid();
        $goods_guid                = $params['goods_guid'];
        $coupon_plan_guid          = $params['coupon_plan_guid'];
        $map                       = [
            ['bid', '=', $bid],
            ['goods_guid', '=', $goods_guid],
            ['coupon_plan_guid', '=', $coupon_plan_guid],
        ];
        $db_coupon_plan_goods_item->where($map)->delete();

        $db_coupon_plan = new CouponPlanModel();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_plan_guid],
        ];
        $db_coupon_plan->where($map)->setDec('goods_num');
        success('删除成功');
    }

    /**
     *快速购买
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_list()
    {
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $coupon_plan_guid    = $params['coupon_plan_guid'];
        $keyword             = $params['keyword'] ?? '';
        $cache_key           = __FUNCTION__ . ':' . $bid;
        $goods_category_list = cache($cache_key);
        if (!$goods_category_list) {
            $db_goods_category    = new GoodsCategory();
            $map_goods_category   = [
                ['bid', '=', $bid],
                ['status', '=', 1],
                ['parent_guid', '=', tools()::get_empty_guid()],
            ];
            $filed_goods_category = [
                'bid',
                'guid',
                'parent_guid',
                'name',
                'mini_pic'
            ];
            $order_goods_category = [
                'sort'        => 'ASC',
                'create_time' => 'DESC'
            ];

            $goods_category_list = $db_goods_category->where($map_goods_category)->field($filed_goods_category)->order($order_goods_category)->append(['all_goods', 'sub_category'])->order(['sort' => 'ASC'])->select();
            $goods_category_list = tools()::object2array($goods_category_list);
            cache($cache_key, $goods_category_list, 600);
        }
        foreach ($goods_category_list as $key => $val) {
            $goods_category_list[$key]['goods'] = $val['all_goods'];
            foreach ($goods_category_list[$key]['goods'] as $k => $v) {
                if ($keyword && strpos($v['name'], $keyword) === false) {
                    unset($goods_category_list[$key]['goods'][$k]);
                }
            }
            unset($goods_category_list[$key]['all_goods']);
            if (empty($goods_category_list[$key]['goods'])) {
                unset($goods_category_list[$key]);
            }
        }
        $goods_category_list       = array_values($goods_category_list);
        $db_coupon_plan_goods_item = new CouponPlanGoodsItemModel();
        $map                       = [
            ['bid', '=', $bid],
            ['coupon_plan_guid', '=', $coupon_plan_guid],
        ];
        $selected_goods_guid       = $db_coupon_plan_goods_item->where($map)->column('status', 'goods_guid');
        result(['list' => $goods_category_list, 'selected_goods_guid' => $selected_goods_guid]);
    }
}