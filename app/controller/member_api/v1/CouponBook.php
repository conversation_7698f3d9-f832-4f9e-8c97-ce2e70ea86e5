<?php

namespace app\controller\member_api\v1;

use app\model\Coupon;
use app\model\Banner as BannerModel;
use app\model\CouponBookItem;
use app\model\CouponBookShare;
use app\model\CouponGoodsItem;
use Exception;

class CouponBook extends BasicMemberApi
{
    /**
     *获取配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $guid           = $params['guid'];
        $share_guid     = $params['share_guid'] ?? '';
        $db_coupon_book = new \app\model\CouponBook();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $info           = $db_coupon_book->where($map)->findOrFail();

        $db_coupon_book_item = new CouponBookItem();
        $coupon_guid_array   = $db_coupon_book_item->get_coupon_guid_array($bid, $guid);
        $db_coupon           = new Coupon();
        $map                 = [
            ['bid', '=', $bid],
            ['guid', 'IN', $coupon_guid_array],
        ];
        $code_list           = $db_coupon->field(['bid', 'guid', 'name'])->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC'])->select();

        $db_banner     = new BannerModel();
        $banner_list   = $db_banner->get_banner_list($bid, 9);
        $config        = get_config_by_bid($bid);
        $return_config = ['coupon_book_item_bg_color_selected' => $config['coupon_book_item_bg_color_selected'], 'coupon_book_item_bg_color' => $config['coupon_book_item_bg_color']];
        $share_info    = [];
        if ($share_guid) {
            $db_coupon_book_share = new CouponBookShare();
            $map                  = [
                ['bid', '=', $bid],
                ['guid', '=', $share_guid],
            ];
            $share_info           = $db_coupon_book_share->where($map)->find()->toArray();
        }
        result([
            'code_list'   => $code_list,
            'banner_list' => $banner_list,
            'info'        => $info,
            'config'      => $return_config,
            'share_info'  => $share_info],
        );
    }

    public function add_share_note()
    {
        $bid              = $this->get_bid();
        $params           = $this->params;
        $true_name        = $params['true_name'];
        $mobile           = $params['mobile'];
        $company          = $params['company'];
        $coupon_book_guid = $params['coupon_book_guid'];
        $member_guid      = $this->get_member_guid();
        $guid             = create_guid();
        $data             = [
            'guid'             => $guid,
            'bid'              => $bid,
            'member_guid'      => $member_guid,
            'coupon_book_guid' => $coupon_book_guid,
            'true_name'        => $true_name,
            'mobile'           => $mobile,
            'company'          => $company,
        ];
        $db               = new CouponBookShare();
        $db->save($data);
        $url = (string)url('member/coupon_book/index', ['share_guid' => $guid, 'guid' => $coupon_book_guid, 'bid' => $bid]);
        result(['guid' => $guid, 'url' => $url], '生成成功,请点击右上角分享');
    }

    public function goods_list()
    {
        $bid                  = $this->get_bid();
        $coupon_guid          = $this->params['coupon_guid'];
        $db_coupon_goods_item = new CouponGoodsItem();
        $map                  = [
            ['bid', '=', $bid],
            ['coupon_guid', '=', $coupon_guid],
            ['status', '=', 1]
        ];
        $goods_guid_array     = $db_coupon_goods_item->where($map)->column('goods_guid');
        $map                  = [
            ['bid', '=', $bid],
            ['guid', 'IN', $goods_guid_array]
        ];

        $db_goods   = new \app\model\Goods();
        $goods_list = $db_goods->field(['bid', 'guid', 'name', 'pic', 'price'])->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC'])->append(['pic_small'])->select();
        result($goods_list);
    }
}