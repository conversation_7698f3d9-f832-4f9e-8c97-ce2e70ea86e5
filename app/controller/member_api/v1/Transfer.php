<?php

namespace app\controller\member_api\v1;

use app\common\service\SmsService;
use Exception;

class Transfer extends BasicMemberApi
{
    /**
     *获取会员信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_member_info()
    {
        $params = $this->params;
        $config = get_config_by_bid($this->get_bid());
        $yky    = new \OpenApi\Yky($config);
        $result = $yky->get_member_info($params['card_id']);
        if ($result === false) {
            error($yky->message);
        }
        result($result);
    }

    /**
     *积分提现
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function point_transfer()
    {
        $params = $this->params;
        $config = get_config_by_bid($this->get_bid());
        $yky    = new \OpenApi\Yky($config);
        //TODO 校验密码或者验证码,根据传过来的type
        $type = $config['valueRechargeValidateType'];
        if ($type == 'password') {
            $post_data = [
                'cardId'   => $params['from_card_id'],
                'password' => $params['password']
            ];
            //密码不能为空
            if ($params['password'] == '') {
                error('请输入会员卡密码');
            }
            $result = $yky->MemberLogin($post_data);
            if ($result === false) {
                error('您输入的密码错误');
            }
        }
        if ($type == 'vcode') {
            error('暂时不支持验证码模式!');
            $sms    = SmsService::get_instance($this->get_bid());
            $result = $sms->verify_sms_code($params['mobile'], $params['sms_code']);
        }
        //用会员信息的卡号做真正的卡号 这样可以支持输入手机号转账
        $result = $yky->get_member_info($params['to_card_id']);
        if ($result === false) {
            error('您输入的卡号不存在');
        }
        $to_card_id = $result['CardId'];
        $data       = [
            'from_card_id' => $params['from_card_id'],
            'to_card_id'   => $to_card_id,
            'point'        => $params['point'],
            'memo'         => $params['memo'],
        ];
        $result     = $yky->point_transfer($data);
        if ($result === false) {
            error($yky->message);
        }
        success('积分转账成功');

    }

    /**
     *储值转账
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function value_transfer()
    {
        $params = $this->params;
        $config = get_config_by_bid($this->get_bid());
        $yky    = new \OpenApi\Yky($config);
        //TODO 校验密码或者验证码,根据传过来的type
        $type = $config['valueRechargeValidateType'];
        if ($type == 'password') {
            $post_data = [
                'cardId'   => $params['from_card_id'],
                'password' => $params['password']
            ];
            //密码不能为空
            if ($params['password'] == '') {
                error('请输入会员卡密码');
            }
            $result = $yky->MemberLogin($post_data);
            if ($result === false) {
                error('您输入的密码错误');
            }
        }
        if ($type == 'vcode') {
            $sms    = SmsService::get_instance($this->get_bid());
            $result = $sms->verify_sms_code($params['mobile'], $params['sms_code']);
        }
        //用会员信息的卡号做真正的卡号 这样可以支持输入手机号转账
        $result = $yky->get_member_info($params['to_card_id']);
        if ($result === false) {
            error('您输入的卡号不存在');
        }
        $to_card_id = $result['CardId'];
        $data       = [
            'from_card_id' => $params['from_card_id'],
            'to_card_id'   => $to_card_id,
            'value'        => $params['value'],
            'memo'         => $params['memo'],
        ];
        $result     = $yky->value_transfer($data);
        if ($result === false) {
            error($yky->message);
        }
        success('储值转账成功');
    }
}