<?php

namespace app\controller\member_api\v1;

use app\model\Business;
use app\model\Member;
use app\common\service\SmsService;
use app\common\service\TokenService;
use Exception;
use Oauth\Contracts\OauthInterface;
use Oauth\Oauth;

class Passport extends BasicMemberApi
{

    /**
     * 获取授权链接
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_auth_url()
    {
        $params    = $this->params;
        $oauth     = $this->get_oauth_instance();
        $oauth_url = $oauth->get_oauth_url($params);
        result(['oauth_url' => $oauth_url]);
    }

    /**
     * 获取授权单例
     * @access public
     * @return OauthInterface
     * @throws Exception
     */
    protected function get_oauth_instance()
    {
        $driver = !empty($this->params['drive']) ? $this->params['drive'] : $this->get_jwt_data()['drive'];
        return Oauth::driver($driver);
    }

    /**
     * 获取会员信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_member_info()
    {
        $db   = new Member();
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $this->get_member_guid()]
        ];
        $data = $db->where($map)->find();
        result($data);
    }

    /**
     * 注册会员
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function register()
    {
        $params = $this->params;
        if ($params['password'] !== $params['password_confirm']) {
            error('两次输入的密码不一致');
        }
        $mobile      = $params['mobile'];
        $verify_code = $params['verify_code'];
        $sms         = SmsService::get_instance($this->get_bid());
        $result      = $sms->verify_sms_code($mobile, $verify_code);
        $db_member   = new Member();
        $data        = ['bid' => $this->get_bid(), 'mobile' => $mobile, 'password' => $params['password']];
        $member_info = $db_member->register($data);
        if (!$member_info) {
            error('注册失败:' . $db_member->getError());
        }
        success('注册成功');
    }

    /**
     * 修改密码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_password()
    {
        $params = $this->params;
        if ($params['password'] !== $params['password_confirm']) {
            error('两次输入的密码不一致');
        }
        $db_member   = new Member();
        $map         = ['bid' => $this->get_bid(), 'guid' => $this->get_member_guid(), 'password' => $params['old_password']];
        $member_info = $db_member->get_member_info($map);
        $update_data = [
            'bid'      => $this->get_bid(),
            'guid'     => $this->get_member_guid(),
            'password' => $params['password']
        ];
        $member      = $db_member->update_member_info($update_data);
        if ($member) {
            success('修改成功');
        } else {
            error('密码修改失败');
        }
    }

    /**
     * 退出
     * @access public
     * @return mixed
     */
    public function login_out()
    {
        $params                 = $this->params;
        $bid                    = $this->get_bid();
        $access_token_cache_key = $bid . '_access_token';
        cookie($access_token_cache_key, null);
        success('退出成功');
    }

    public function get_config()
    {
        $bid         = $this->get_bid();
        $db_banner   = new \app\model\Banner();
        $banner_list = $db_banner->get_banner_list($bid, 11, true);
        $config      = get_config_by_bid($bid);
        $data        = [
            'banner_list' => $banner_list,
            'config'      => [
                'mobile_auth_type'  => $config['mobile_auth_type'],
                'login_page_notice' => tools()::add_rich_img_class($config['login_page_notice'])
            ],
        ];
        result($data);
    }

    /**
     * 登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function login()
    {
        $params     = $this->params;
        $oauth      = $this->get_oauth_instance();
        $login_info = $oauth->login($params);
        $bid        = $params['bid'];
        // 解码数据
        $expire      = !empty($login_info['expires_in']) ? $login_info['expires_in'] : 3600 * 24 * 7;
        $db_business = new Business();
        $sid         = $db_business->get_business_sid_by_bid($bid);
        $token       = [
            'member_guid' => $login_info['member_info']['member_guid'],
            'member_id'   => $login_info['member_info']['id'],
            'bid'         => $bid,
            'sid'         => $sid,
            'exp'         => $expire,
            'drive'       => $params['drive'],
            'appid'       => $login_info['appid'],
            'openid'      => $login_info['openid'],
            'oauth_token' => $login_info['oauth_token']
        ];
        $jwt         = TokenService::encode($token);
        $data        = [
            'access_token' => $jwt,
            'expires_in'   => $expire,
            'expires_time' => $expire + time(),
            'member_info'  => $login_info['member_info']
        ];
        result($data);
    }

    /**
     * 小程序获取用户信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_user_info()
    {
        $params                = $this->params;
        $oauth                 = $this->get_oauth_instance();
        $params['bid']         = $this->get_bid();
        $params['openid']      = $this->get_openid();
        $params['member_guid'] = $this->get_member_guid();
        $params['oauth_token'] = $this->get_jwt_data()['oauth_token'];
        $params['code']        = $this->params['code'] ?? null;
        if (!empty($params['encrypted_data'])) {
            $params['encrypted_data'] = urldecode($this->request->param('encrypted_data', '', null));
        }
        $user_info = $oauth->get_user_info($params);
        result($user_info);
    }

    /**
     * 小程序获取地址
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_phone_number()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $code                  = $params['code'] ?? null;
        $get_phone_number_code = $params['get_phone_number_code'] ?? null;
        $mini                  = weixin($this->get_appid())::WeMiniCrypt();
        if ($get_phone_number_code) {
            //说明是通过新的服务端接口获取手机号
            $result = $mini->getUserPhonenumber($get_phone_number_code);
            $result = $result['phone_info'];
        } else {
            //老的接口获取
            if (empty($params['iv'])) {
                error('个人小程序不支持授权获取手机号');
            }
            $iv     = $params['iv'];
            $decode = urldecode($this->request->param('encrypted_data', '', null));
            $result = $mini->decodeData($code, $iv, $decode);
        }
        if (!is_array($result)) {
            error($result);
        }
        $mobile      = $result['purePhoneNumber'];
        $update_data = ['mobile' => $mobile];
        $db_member   = new Member();
        $member      = $db_member->update_member_info_and_update_coupon_send_note_member_guid($bid, $member_guid, $update_data);
        $data        = [
            'mobile'    => $mobile,
            'user_info' => $member
        ];
        result($data);
    }
}