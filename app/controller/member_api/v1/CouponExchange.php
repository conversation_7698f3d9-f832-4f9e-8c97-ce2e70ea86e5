<?php

namespace app\controller\member_api\v1;

use app\model\Coupon;
use app\model\CouponSendNote;
use app\model\JuheRechargeOrder;
use app\model\Member;
use app\model\MemberMoneyNote;
use app\model\WechatTransfersOrder;
use Exception;
use Juhe\Juhe;
use think\facade\Db;

class CouponExchange extends BasicMemberApi
{
    /**
     *券兑换储值
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function money()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        //$exchange_type = $params['exchange_type'];
        $exchange_type = 3; //兑换储值
        $code          = $params['code'];
        $password      = $params['password'];
        $db            = new CouponSendNote();
        $info          = $db->verify($bid, ['code' => $code, 'password' => $password]);
        if ($info === false) {
            error($db->getError());
        }
        //校验卡密面值
        $map_coupon   = [
            ['bid', '=', $bid],
            ['guid', '=', $info['coupon_guid']],
        ];
        $db_coupon    = new Coupon();
        $coupon       = $db_coupon->field(['status', 'expire_time', 'value'])->where($map_coupon)->find();
        $coupon_value = $coupon['value']; //充值金额
        $order_guid   = create_guid();
        $data         = [
            'status'        => 1, //把卡密状态改成1,说明已使用
            'member_guid'   => $member_guid,
            'relation_guid' => $order_guid,
            'used_time'     => format_timestamp(),
            'used_way'      => $exchange_type,
        ];
        $map          = [
            ['bid', '=', $bid],
            ['guid', '=', $info['guid']],
            ['status', '=', 0], //未使用
            ['code', '=', $code],
            ['password', '=', $password],
            ['member_guid', 'null', null],//没有挂靠会员
            ['used_time', 'null', null]//没有使用过
        ];
        $update       = $db::update($data, $map);
        if (!$update) {
            error('发送记录更新失败');
        }
        $config               = get_config_by_bid($bid);
        $db_member_money_note = new MemberMoneyNote();
        $data                 = [
            'guid'        => $order_guid,
            'bid'         => $bid,
            'member_guid' => $member_guid,
            'way'         => 3, //卡密兑换
            'type'        => 1, //充值
            'money'       => tools()::nc_price_calculate($coupon_value, '*', $config['coupon_exchange_money_rate']),
            'memo'        => '[卡密兑换]来自卡号:' . $code,
        ];
        $recharge             = $db_member_money_note->recharge_money($data);
        success('充值成功');
    }


    /**
     * 券兑换油卡/话费充值
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function exchange()
    {
        $params           = $this->params;
        $bid              = $this->get_bid();
        $exchange_type    = $params['exchange_type'];
        $code             = $params['code'];
        $password         = $params['password'];
        $mobile           = $params['mobile'];
        $recharge_card_id = '';
        $value            = $params['coupon_value'];
        $member_guid      = $this->get_member_guid();
        //油卡充值面额校验
        if ($exchange_type == 1 && !in_array($value, [50, 100, 200, 500, 1000])) {
            error('充值面额暂时不支持');
        }
        //手机话费充值面额校验
        if ($exchange_type == 2 && !in_array($value, [50, 100, 200, 500])) {
            error('充值面额暂时不支持');
        }
        $update_data = [];
        if (empty($member['mobile'])) {
            $update_data['mobile'] = $mobile;
        }
        if ($exchange_type == 1) {
            $recharge_card_id = $params['oil_card_id'];
            if (!tools()::is_oil_card_id($recharge_card_id)) {
                error('请输入正确的油卡号');
            }
            if (empty($member['oil_card_id'])) {
                $update_data['oil_card_id'] = $recharge_card_id;
            }
        }
        if ($exchange_type == 2) {
            $recharge_card_id = $mobile;
        }
        if (!empty($update_data)) {
            $db_member = new Member();
            //更新油卡或者手机号
            $update_data['bid']  = $this->get_bid();
            $update_data['guid'] = $this->get_member_guid();
            $db_member->update_member_info($update_data);
        }
        $db   = new CouponSendNote();
        $info = $db->verify($bid, ['code' => $code, 'password' => $password]);
        if ($info === false) {
            error($db->getError());
        }
        //校验卡密面值
        $map_coupon   = [
            ['bid', '=', $bid],
            ['guid', '=', $info['coupon_guid']],
        ];
        $db_coupon    = new Coupon();
        $coupon       = $db_coupon->field(['status', 'expire_time', 'value'])->where($map_coupon)->find();
        $coupon_value = $coupon['value'];
        if ($value != $coupon_value) {
            error('面值错误,您的卡密面值为:' . $coupon_value . ',当前选择面值:' . $value);
        }
        $coupon_value = floatval($coupon_value);
        //通过金额和卡号找到id
        //校验当天已提交订单个数,不能大于10笔
        $db_juhe_recharge_order = new JuheRechargeOrder();
        $map_order              = [
            ['bid', '=', $bid],
            ['game_userid', '=', $recharge_card_id],
            ['type', '=', $exchange_type],
        ];
        $is_recharge            = $db_juhe_recharge_order->where($map_order)->whereDay('create_time')->count();
        if ($is_recharge >= 10) {
            error('每人每天最多兑换10张哦~');
        }
        //@todo 根据发送工号判断 是否有权限充值对应类型
        $order_guid = create_guid();
        $data       = [
            'status'        => 1, //把卡密状态改成1,说明已使用
            'used_mobile'   => $mobile,
            'member_guid'   => $member_guid,
            'relation_guid' => $order_guid,
            'used_time'     => format_timestamp(),
            'used_way'      => $exchange_type, //1 兑换油卡 2 兑换手机话费
        ];
        if (empty($info['mobile'])) {
            //如果记录中手机号为空 则更新手机号字段
            $data['mobile'] = $mobile;
        }
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $info['guid']],
            ['status', '=', 0], //未使用
            ['code', '=', $code],
            ['password', '=', $password],
            ['member_guid', 'null', null],//没有挂靠会员
            ['used_time', 'null', null]//没有使用过
        ];
        $update = $db::update($data, $map);
        if (!$update) {
            error('发送记录更新失败');
        }
        $order_id      = strtolower(md5(trim($code) . trim($password))); //用卡号+密码MD5处理做订单号,防止重复提交
        $data          = [
            'bid'              => $bid,
            'order_guid'       => $order_guid,
            'member_guid'      => $member_guid,
            'way'              => 2, //途径卡券兑换
            'type'             => $exchange_type, // 1 油卡 2话费
            'orderid'          => $order_id,
            'amount'           => $coupon_value,
            'mobile'           => $mobile,
            'recharge_card_id' => $recharge_card_id,
            'relation_guid'    => $info['guid'],
            'meno'             => '来源卡号:' . $code
        ];
        $config        = get_config_by_bid($bid);
        $config['bid'] = $bid;
        $juhe_service  = Juhe::get_instance($config, $exchange_type);
        $apply         = $juhe_service->apply($data); //预下单
        if (!$apply) {
            error($juhe_service->err_msg . '券号:' . $code);
        }
        $key                = 'juhe:oil:last_recharge_time:' . $recharge_card_id;
        $last_recharge_time = cache($key);
        $interval           = 20 * 60; //每张油卡充值间隔时间
        $later_min          = 10; //默认提示语延迟分钟数量
        $later_time         = 0; //默认无延迟
        $job_name           = 'Juhe@recharge';
        if ($last_recharge_time) {
            //存在上次充值时间 则需要延迟执行
            $next_recharge_time = ((int)$last_recharge_time) + $interval;//下次充值时间戳
            $later_time         = $next_recharge_time - time(); //需要延迟多少秒充值
            cache($key, $next_recharge_time, $later_time);
            $later_min = (int)($later_time % (3600) / 60);
        } else {
            cache($key, time(), $interval);//缓存20分钟有效
        }
        //组装job数据,建立异步任务
        $job_attach = [
            'bid'        => $bid,
            'order_guid' => $order_guid,
            'type'       => $exchange_type, //卡密充值类型
        ];
        $queue_name = (get_system_config('pause_juhe_recharge') == 1) ? 'failed' : '';
        $result     = job()->set_queue_name($queue_name)->set_job_name($job_name)->push_job($job_attach, $later_time);
        if (!$result) {
            error('提交失败,请稍后再试');
        }
        $balance_key = $juhe_service->getBalanceCacheKey();
        $msg         = cache($balance_key) ? '提交成功,系统升级中,请留意到帐通知' : '提交成功,' . $later_min . '分钟左右到帐~';
        success($msg);
    }

    /**
     *券兑换微信零钱
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function wechat()
    {
        error('该功能维护中,建议兑换其他类型');
        $params = $this->params;
        $bid    = $this->get_bid();
        //$exchange_type = $params['exchange_type'];
        $exchange_type = 4;
        $code          = $params['code'];
        $password      = $params['password'];
        $member_guid   = $this->get_member_guid();
        $db            = new CouponSendNote();
        $info          = $db->verify($bid, ['code' => $code, 'password' => $password]);
        if ($info === false) {
            error($db->getError());
        }
        //校验卡密面值
        $map_coupon                         = [
            ['bid', '=', $bid],
            ['guid', '=', $info['coupon_guid']],
        ];
        $db_coupon                          = new Coupon();
        $coupon                             = $db_coupon->field(['status', 'expire_time', 'value'])->where($map_coupon)->find();
        $config                             = get_config_by_bid($bid);
        $coupon_exchange_wechat_wallet_rate = $config['coupon_exchange_wechat_wallet_rate'];
        if ($coupon_exchange_wechat_wallet_rate <= 0 || $coupon_exchange_wechat_wallet_rate > 1) {
            throw new Exception('兑换比例设置有误,请联系管理员');
        }
        $coupon_value = $coupon['value'];
        $amount       = (int)tools()::nc_price_calculate(floatval($coupon_value), '*', $coupon_exchange_wechat_wallet_rate * 100);
        //通过金额和卡号找到id
        //@todo 根据发送工号判断 是否有权限充值对应类型
        $order_guid = create_guid();
        $data       = [
            'status'        => 1, //把卡密状态改成1,说明已使用
            'used_mobile'   => '',
            'member_guid'   => $member_guid,
            'relation_guid' => $order_guid,
            'used_time'     => format_timestamp(),
            'used_way'      => $exchange_type, //1 兑换油卡 2 兑换手机话费 4 微信零钱包
        ];
        $map        = [
            ['bid', '=', $bid],
            ['guid', '=', $info['guid']],
            ['status', '=', 0], //未使用
            ['code', '=', $code],
            ['password', '=', $password],
            //           ['member_guid', 'null', null],//没有挂靠会员
            //           ['used_time', 'null', null]//没有使用过
        ];
        $update     = $db::update($data, $map);
        if (!$update) {
            error('发送记录更新失败');
        }
        $db = new WechatTransfersOrder();
        // 调用方法,替换掉- 长度保持32位
        $partner_trade_no = str_replace('-', '', $info['guid']);
        $data             = [
            'guid'             => $order_guid,
            'bid'              => $bid,
            'appid'            => $this->get_appid(),
            'openid'           => $this->get_openid(),
            'member_guid'      => $this->get_member_guid(),
            'way'              => 3, //途径,1积分提现,2 魔术包提现...
            'partner_trade_no' => $partner_trade_no,
            'amount'           => $amount,
            'DESC'             => '卡券提现' . $code,
        ];
        $result           = $db->save($data);
        //组装job数据,建立异步任务
        $job_attach = [
            'bid'        => $bid,
            'order_guid' => $order_guid,
        ];
        $result     = job()->set_job_name('Weixin@transfers')->push_job($job_attach);
        success('提交成功');
    }

    /**
     *兑换储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function exchange_money_note()
    {
        $map        = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
            ['way', '=', 3]
        ];
        $db         = new MemberMoneyNote();
        $order_list = $db->field(['guid', 'money', 'type', 'way', 'memo', 'create_time', 'update_time'])->where($map)->limit(50)->order('create_time', 'DESC')->select();
        result($order_list);
    }

    /**
     *兑换油卡/话费记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function exchange_oil_card_or_mobile_recharge_note()
    {
        $map        = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $this->get_member_guid()],
        ];
        $db         = new JuheRechargeOrder();
        $order_list = $db->field(['guid', 'game_userid', 'value', 'type', 'status', 'result', 'third_status', 'third_message', 'create_time', 'update_time', 'meno'])->where($map)->limit(50)->order('create_time', 'DESC')->select();
        result($order_list);
    }

    /**
     *兑换储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function exchange_wechat_note()
    {
        $join       = [
            ['coupon_send_note csn', 'csn.bid = wtfo.bid AND csn.relation_guid = wtfo.guid'],
        ];
        $map        = [
            ['csn.used_way', '=', 4],
            ['wtfo.bid', '=', $this->get_bid()],
            ['wtfo.openid', '=', $this->get_openid()]
        ];
        $field      = [
            'csn.code',
            'wtfo.guid',
            'csn.mobile',
            'wtfo.way',
            Db::raw('round(wtfo.amount/100,2) as amount'),
            'wtfo.desc',
            'wtfo.message',
            'wtfo.create_time',
            'wtfo.status',
            'wtfo.payment_time',
        ];
        $db         = new WechatTransfersOrder();
        $order_list = $db->alias('wtfo')->where($map)->join($join)->field($field)->order(['wtfo.create_time' => 'DESC'])->limit(50)->select()->toArray();
        result($order_list);
    }
}
