<?php

namespace app\controller\member_api\v1;

use app\model\YkyConsumeFreeOfCharge;
use app\model\YkyConsumeNote;
use app\model\YkyCouponQrcodeNote;
use app\model\YkyCouponSendRule;
use app\model\YkyMemberAllValueNote;
use app\model\YkyPlusMemberOrder;
use app\model\YkyRecommendRewardNote;
use Exception;
use OpenApi\Bolink;
use think\facade\Db;
use xieyongfa\yky\Yky;

class Yikayi extends BasicMemberApi
{
    /**
     *获取会员信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function get_member_info()
    {
        $params = $this->params;
        $config = get_config_by_bid($this->get_bid());
        $yky    = new \OpenApi\Yky($config);
        $result = $yky->get_member_info($params['card_id']);
        if ($result === false) {
            error($yky->message);
        }
        result($result);
    }

    public function get_coupon_list()
    {
        $params           = $this->params;
        $bid              = $this->get_bid();
        $yky_member_guid  = $this->get_yky_member_guid_by_cookie();
        $config           = get_config_by_bid($bid);
        $coupon_guid      = '("c228c50b-6243-11f0-87a2-34735a990690")';
        $yky_coupon       = Yky::Coupon($config);
        $yky_card_guid='18603047034';

        $post_data        = [
            //'where'   => "1=1 AND Flag=1 AND MemberGuid='" . $yky_member_guid . "' AND CouponGuid IN " . $coupon_guid . " AND EnableCount>0 ",
            'where'   => "1=1 AND Flag=1 AND MemberCardId='" . $yky_card_guid . "' AND CouponGuid IN " . $coupon_guid . " AND EnableCount>0 ",
            // 'where'   => "1=1 AND Flag=1 AND MemberGuid='" . $yky_member_guid . "' AND CouponGuid NOT IN " . $coupon_guid . " AND EnableCount>0 ",
            'orderBy' => 'EndDate ASC'
        ];
        $coupon_send_list = $yky_coupon->Get_CoupnSendPagedV2($post_data);
        if ($coupon_send_list === false) {
            error($yky_coupon->message);
        }
        result($coupon_send_list);
    }


    public function plus_member_order_overview()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $yky_member_guid = $this->get_yky_member_guid_by_cookie();
        $config          = get_config_by_bid($bid);
        $member          = Yky::Member($config);
        $member_info     = $member->Get_MemberInfo($yky_member_guid);
        if (empty($member_info['data'])) {
            error('请先注册成为会员');
        }
        $member_info             = current($member_info['data']);
        $today                   = date('Y-m-d');
        $db_yky_coupon_send_rule = new YkyCouponSendRule();
        $map                     = [
            ['bid', '=', $bid],
            ['status', '=', 1]
        ];
        $min_value               = $db_yky_coupon_send_rule->where($map)->min('min_value');

        $db    = new YkyPlusMemberOrder();
        $map   = [
            ['bid', '=', $bid],
            ['yky_member_guid', '=', $yky_member_guid],
            ['start_time', '<', $today],
            ['end_time', '>', $today],
        ];
        $field = [
            '*',
            Db::raw('(consume_money + add_value_money) as total_paid_money'),
            Db::raw("date_format(start_time,'%Y-%m-%d') as start_date"),
            Db::raw("date_format(end_time,'%Y-%m-%d') as end_date"),
            Db::raw('(consume_money + add_value_money) as total_paid_money'),
            Db::raw('(' . $min_value . ' - consume_money - add_value_money) as diff_total_paid_money')
        ];
        $info  = $db->field($field)->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
        if ($info->isEmpty()) {
            $map  = [
                ['bid', '=', $bid],
                ['yky_member_guid', '=', $yky_member_guid],
            ];
            $info = $db->field($field)->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
        }
        $count               = $info->isEmpty() ? 0 : 1;
        $info_total          = $count ? $info : [];
        $add_value_note_list = [];
        $consume_note_list   = [];
        if ($count) {
            $start_time                    = $info['start_date'];
            $end_time                      = $info['end_time'];
            $db_yky_consume_note           = new YkyConsumeNote();
            $map_yky_consume_note          = [
                ['bid', '=', $bid],
                ['way', '=', 11],
                ['member_guid', '=', $yky_member_guid],
                ['operate_time', '>=', $start_time],
                ['operate_time', '<', $end_time],
            ];
            $field                         = [
                'bill_number',
                'operate_time',
                Db::raw("ROUND(IFNULL(paid_thirdpay + paid_money + paid_other + paid_card, 0), 2) AS consume_money")
            ];
            $consume_note_list             = $db_yky_consume_note->field($field)->where($map_yky_consume_note)->order(['operate_time' => 'DESC'])->select()->toArray();
            $db_member_all_value_note      = new YkyMemberAllValueNote();
            $map_yky_member_all_value_note = [
                ['bid', '=', $bid],
                ['way', '=', 2],
                ['type', '=', 1],
                //                ['paid_thirdpay|paid_other|paid_card|paid_cash', '>', 0],
                ['operate_time', '>=', $start_time],
                ['operate_time', '<', $end_time],
                ['member_guid', '=', $yky_member_guid],
            ];
            $field                         = [
                'bill_number',
                'operate_time',
                Db::raw("ROUND(IFNULL(paid_thirdpay + paid_other + paid_card, 0), 2) AS add_value_money")
            ];
            $add_value_note_list           = $db_member_all_value_note->field($field)->where($map_yky_member_all_value_note)->order(['operate_time' => 'DESC'])->select()->toArray();
        }

        result([
            'count'               => $count,
            'add_value_note_list' => $add_value_note_list,
            'consume_note_list'   => $consume_note_list,
            'info'                => $info_total,
            'member_info'         => $member_info
        ]);
    }

    public function recommend_reward_note()
    {
        $params          = $this->params;
        $level           = $params['level'] ?? 0;
        $bid             = $this->get_bid();
        $yky_member_guid = $this->get_yky_member_guid_by_cookie();
        $config          = get_config_by_bid($bid);
        $member          = Yky::Member($config);
        $member_info     = $member->Get_MemberInfo($yky_member_guid);
        if (empty($member_info['data'])) {
            error('请先注册成为会员');
        }
        $member_info = current($member_info['data']);
        $db          = new YkyRecommendRewardNote();
        $map         = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['yky_member_guid', '=', $yky_member_guid],
        ];
        $field       = [
            'count(1)' => 'count',
            Db::raw("IFNULL(SUM(reward_money),0) AS sum_reward_money"),
            // 统计等级 1 的金额
            Db::raw("IFNULL(SUM(CASE WHEN level = '1' THEN reward_money ELSE 0 END), 0) AS level_1_reward_money"),
            // 统计等级 2 的金额
            Db::raw("IFNULL(SUM(CASE WHEN level = '2' THEN reward_money ELSE 0 END), 0) AS level_2_reward_money"),
            // 统计等级 3 的金额
            Db::raw("IFNULL(SUM(CASE WHEN level = '3' THEN reward_money ELSE 0 END), 0) AS level_3_reward_money"),
        ];
        $data        = $db->field($field)->where($map)->find();
        $map         = [
            ['rrn.bid', '=', $bid],
            ['rrn.status', '=', 1],
            ['rrn.yky_member_guid', '=', $yky_member_guid],
        ];
        if ($level > 0) {
            $map[] = ['rrn.level', '=', $level];
        }
        $join  = [
            ['yky_chain_store ycs', 'rrn.bid=ycs.bid AND rrn.chain_store_guid=ycs.guid'],
        ];
        $field = [
            'rrn.*',
            'ycs.store_name AS chain_store_name'
        ];
        $list  = $db->alias('rrn')->join($join)->field($field)->where($map)->order(['rrn.create_time' => 'DESC'])->append(['from_member_info'])->select();
        result(['data' => $data, 'list' => $list, 'member_info' => $member_info]);
    }

    public function get_consume_free_of_charge_data()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $yky_member_guid = $this->get_yky_member_guid_by_cookie();
        $db              = new YkyConsumeFreeOfCharge();
        $map             = [
            ['bid', '=', $bid],
            ['yky_member_guid', '=', $yky_member_guid],
            ['status', '=', 0],
        ];
        $field           = [
            'count(1)' => 'consume_times',
            Db::raw("IFNULL(AVG(total_paid),0) as avg_total_paid"),
            Db::raw("IFNULL(SUM(total_paid),0) as sum_total_paid"),
        ];
        $data            = $db->field($field)->where($map)->find();
        $list            = $db->where($map)->order(['create_time' => 'DESC'])->select();
        result(['data' => $data, 'list' => $list]);
    }


    /**
     *积分提现
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_coupon()
    {
        $params      = $this->params;
        $coupon_guid = $params['coupon_guid'];
        $token       = $params['token'] ?? '';
        //todo 验证token有效性,并且使用分布式锁防止并发
        $config      = get_config_by_bid($this->get_bid());
        $coupon      = Yky::Coupon($config);
        $member      = Yky::Member($config);
        $member_info = $member->GetMemberGuidByOpenId(['thirdOpenId' => $this->get_openid()]);
        if (empty($member_info['data'])) {
            error('请先注册成为会员');
        }
        $member_info = current($member_info['data']);
        $card_id     = $member_info['CardId'];
        $post_data   = ['cardIds' => $card_id, 'sendCount' => 1, 'couponGuid' => $coupon_guid];
        $api_result  = $coupon->SendCoupon($post_data);
        success('发送成功');
    }

    private function get_yky_member_guid_by_cookie()
    {
        $bid             = $this->get_bid();
        $key             = $bid . '_yky_member_guid_vale';
        $yky_member_guid = cookie($key);
        if (empty($yky_member_guid)) {
            if (is_debug()) {
                return 'c8b151f9-d676-11e8-9f73-0010185de866'; // 卡号8
            }
            error('请先注册成为会员');
        }
        return $yky_member_guid;
    }

    public function send_coupon_by_note_guid()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $config      = get_config_by_bid($bid);
        $member      = Yky::Member($config);
        $member_info = $member->Get_MemberInfo($this->get_yky_member_guid_by_cookie());
        if (empty($member_info['data'])) {
            error('请先注册成为会员');
        }
        $member_info               = current($member_info['data']);
        $card_id                   = $member_info['CardId'];
        $yky_member_guid           = $member_info['MemberGuid'];
        $note_guid                 = $params['note_guid'];
        $db_yky_coupon_qrcode_note = new YkyCouponQrcodeNote();
        $map                       = [
            ['bid', '=', $bid],
            ['guid', '=', $note_guid],
        ];
        $note_info                 = $db_yky_coupon_qrcode_note->where($map)->findOrFail();
        $status                    = $note_info['status'];
        $create_time               = $note_info['create_time'];
        $page_guid                 = $note_info['page_guid'];
        if ($status != 0) {
            error('当前二维码已经被领取过');
        }
        if (time() - strtotime($create_time) > 600) {
            error('当前二维码已过期');
        }
        $coupon_guid      = $note_info['yky_coupon_guid'];
        $yky_user_account = $note_info['yky_user_account'];
        $post_data        = ['cardIds' => $card_id, 'sendCount' => 1, 'couponGuid' => $coupon_guid, 'userAccount' => $yky_user_account];
        $coupon           = Yky::Coupon($config);
        $api_result       = $coupon->SendCoupon($post_data);
        if ($api_result === false) {
            error($coupon->message);
        }
        $coupon_send_guid = $api_result['couponSendGuid'] ?? '';
        $update_data      = [
            'status'                => 1,
            'yky_member_guid'       => $yky_member_guid,
            'yky_card_id'           => $card_id,
            'coupon_send_note_guid' => $coupon_send_guid,
        ];
        $db_yky_coupon_qrcode_note::update($update_data, $map);
        $socket_data          = ['code' => 0, 'msg' => '领取成功'];
        $config               = get_config_by_bid($bid);
        $yunhuiyuan_username  = $config['yunhuiyuan_username'];
        $yunhuiyuan_bid       = $config['yunhuiyuan_bid'];
        $coupon_send_note_url = 'https://' . $yunhuiyuan_username . '.h5.yunhuiyuan.cn/Coupon/MyCouponDetail?guid=' . $coupon_send_guid . '&bid=' . $yunhuiyuan_bid;
        $my_coupon_url        = 'https://' . $yunhuiyuan_username . '.h5.yunhuiyuan.cn/Coupon/MyCoupon?bid=' . $yunhuiyuan_bid;
        publish_websocket($socket_data, $page_guid);
        result([
            'coupon_send_note_url' => $coupon_send_note_url,
            'my_coupon_url'        => $my_coupon_url,
        ]);
    }

    public function member_info()
    {
        $params = $this->params;
        $config = get_config_by_bid($this->get_bid());
        $yky    = new \OpenApi\Yky($config);
        $result = $yky->get_member_info_by_openid($this->get_openid());
        if ($result === false) {
            error($yky->message);
        }
        result(['member' => $result]);
    }

    public function point_to_point()
    {
        $config = get_config_by_bid($this->get_bid());
        $to_yky = new \OpenApi\Yky($config);
        $result = $to_yky->get_member_info_by_openid($this->get_openid());
        if ($result === false) {
            error($to_yky->message);
        }
        $mobile   = $result['Mobile'];
        $params   = $this->params;
        $openid   = $params['from_openid'];
        $config   = ['openid' => '6043356B72184EC38DFB995CF74857A1', 'secret' => 'VIAQQG'];
        $from_yky = new \OpenApi\Yky($config);
        $result   = $from_yky->get_member_info($mobile);
        $point    = $result['EnablePoint'];
        if ($point <= 0) {
            error('没有可用积分');
        }
        $point     = 10;
        $post_data = [
            'cardId' => $mobile,
            'point'  => -$point,
            'meno'   => $openid,
        ];
        //扣除积分
        $from_yky->Update_MemberPoint($post_data);
        //增加积分
        $post_data['point'] = $point;
        $to_yky->Update_MemberPoint($post_data);
        success('增加成功');
    }

    public function get_business_point_list()
    {
        $params        = $this->params;
        $mobile        = $params['mobile'];
        $business_list = []; //todo 用数据库存储起来 bid openid secret
        $business_list = [
            ['openid' => '9B823341007911EA855420040FFA82A0', 'secret' => 'EHHMSR'],
            //           ['openid' => '4C6A2C58823011EBAB5020040FFA82A0', 'secret' => 'YPV544'],
            //           ['openid' => 'E1EB202397FB11E9BC5E001018640E28', 'secret' => 'TERAC4'],
            //           ['openid' => '8066A173A4C011EA933A20040FFA82A0', 'secret' => 'K84BAM'],

        ];
        $list          = [['label' => '请选择', 'value' => '']];
        foreach ($business_list as $config) {
            //           $member = Yky::Member($config);
            //           $result = $member->Get_MemberInfo($mobile);
            $yky    = new \OpenApi\Yky($config);
            $result = $yky->get_member_info($mobile);
            if ($result !== false) {
                $business_info = $yky->Get_BusinessInfo();
                $list[]        = [
                    'label'    => $business_info['data']['BusinessName'] . '-' . $result['EnablePoint'],
                    'value'    => $config['openid'],
                    'disabled' => $result['EnablePoint'] == 0
                ];
            }
        }
        result($list);
    }

    /**
     * 使用洗车券并开闸
     * @access public
     * @return void
     * @throws Exception
     */
    public function use_wash_car_coupon()
    {
        $params = $this->params;
        $bid    = $this->get_bid();

        // 获取参数
        $coupon_send_guid = $params['coupon_send_guid'] ?? '';
        $device_guid      = $params['guid'] ?? '';

        // 参数验证
        if (empty($coupon_send_guid)) {
            error('缺少优惠券参数');
        }

        if (empty($device_guid)) {
            error('缺少设备参数');
        }

        // 获取会员信息（验证会员身份）
        $yky_member_guid = $this->get_yky_member_guid_by_cookie();
        if (empty($yky_member_guid)) {
            error('请先登录');
        }

        // 获取配置
        $config = get_config_by_bid($bid);

        // 1. 核销优惠券
        $yky_coupon = Yky::Coupon($config);
        $sub_data   = [
            'couponSendGuid' => $coupon_send_guid,
            'subCount'       => 1,
        ];

        $coupon_result = $yky_coupon->SubCoupon($sub_data);
        if ($coupon_result === false) {
            error('优惠券核销失败：' . $yky_coupon->message);
        }

        if (!isset($coupon_result['couponUsedNoteGuid'])) {
            error('优惠券核销失败，请重试');
        }
         // 2. 根据设备GUID获取通道ID（暂时写死用于测试）
        $channel_id = '100189008'; // 测试用的固定通道ID

        // 3. 调用Bolink开闸
        $bolink     = new Bolink();
        $liftrod_id = time(); // 使用时间戳作为操作ID
        $operate    = 0; // 0-抬杆，1-落杆

        $gate_result = $bolink->operateLiftrod($channel_id, $liftrod_id, $operate);
                if ($gate_result === false) {
                    // 开闸失败，但券已核销，记录错误但不回滚
                    wr_log('开闸失败，券已核销：' . $bolink->errmsg . '，券核销ID：' . $coupon_result['couponUsedNoteGuid'], 1, $bid);
                    error('开闸失败：' . $bolink->errmsg);
                }

        // 4. 记录操作日志
        wr_log('洗车券核销成功并开闸：设备' . $device_guid . '，通道' . $channel_id . '，券核销ID：' . $coupon_result['couponUsedNoteGuid'], 1, $bid);

        // 5. 返回成功结果
        result([
            'coupon_used_guid' => $coupon_result['couponUsedNoteGuid'],
            'channel_id'       => $channel_id,
            'device_guid'      => $device_guid,
            'operate_time'     => date('Y-m-d H:i:s'),
            'gate_result'      => $gate_result
        ], '开闸成功');
    }
}
