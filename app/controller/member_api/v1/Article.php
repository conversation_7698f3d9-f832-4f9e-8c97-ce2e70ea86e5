<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use Exception;

class Article extends BasicMemberApi
{
    /**
     *文章详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db     = new \app\model\Article();
        $params = $this->params;
        $bid    = $this->get_bid();
        $key    = $params['key'] ?? ($params['type'] ?? null);
        $guid   = $params['guid'] ?? null;
        $map    = [['bid', '=', $bid]];
        if ($key) {
            $map[] = ['key', '=', $key];
        }
        if ($guid) {
            $map[] = ['guid', '=', $guid];
        }
        if (!$key && !$guid) {
            error('参数错误');
        }
        $info            = $db->where($map)->find();
        $info['content'] = tools()::add_rich_img_class($info['content']);
        result($info);
    }
}