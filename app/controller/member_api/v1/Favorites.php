<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;

use app\model\Coupon;
use app\model\Favorites as FavoritesModel;
use Exception;


class Favorites extends BasicMemberApi
{
    /**
     *添加收藏
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db_favorites        = new FavoritesModel();
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $member_guid         = $this->get_member_guid();
        $type                = $params['type']; // 1 商品 2 卡券 3 专题
        $map                 = [
            ['bid', '=', $bid],
            ['type', '=', $type],
            ['status', '=', 1], //状态是已收藏
            ['member_guid', '=', $member_guid],
        ];
        $relation_guid_array = $db_favorites->where($map)->column('relation_guid');
        if (empty($relation_guid_array)) {
            result([]);
        }
        switch ($type) {
            case 1:
                $db_goods    = new \app\model\Goods();
                $map         = [
                    ['bid', '=', $bid],
                    ['guid', 'IN', $relation_guid_array],
                ];
                $this->model = $db_goods->where($map)->order(['create_time' => 'DESC']);
                break;
            case 2:
                $db_coupon   = new Coupon();
                $map         = [
                    ['bid', '=', $bid],
                    ['guid', 'IN', $relation_guid_array],
                ];
                $this->model = $db_coupon->where($map)->order(['create_time' => 'DESC']);
                break;
            case 3:
                $db_topic    = new \app\model\Topic();
                $map         = [
                    ['bid', '=', $bid],
                    ['guid', 'IN', $relation_guid_array],
                ];
                $this->model = $db_topic->where($map)->order(['create_time' => 'DESC']);
                break;
            default:
                break;
        }
        unset($this->params['type']); //删除type字段,避免污染
        $list = $this->_list();
        result($list);
    }

    /**
     *添加收藏
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $db          = new FavoritesModel();
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $guid        = $params['guid'];
        $type        = $params['type']; //1 商品 2 专题
        $map         = [
            ['bid', '=', $bid],
            ['type', '=', $type],
            ['relation_guid', '=', $guid],
            ['member_guid', '=', $member_guid],
        ];
        $count       = $db->where($map)->count();
        if (!$count) {
            $data = [
                'guid'          => create_guid(),
                'bid'           => $bid,
                'type'          => $type,
                'relation_guid' => $guid,
                'member_guid'   => $member_guid,
                'status'        => 1
            ];
            $db->save($data);
        } else {
            $update_data = [
                'status' => 1
            ];
            $db::update($update_data, $map);
        }
        success('收藏成功');
    }

    /**
     *取消收藏
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function remove()
    {
        $db          = new FavoritesModel();
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $guid        = $params['guid'];
        $type        = $params['type'];
        $map         = [
            ['bid', '=', $bid],
            ['type', '=', $type],
            ['relation_guid', '=', $guid],
            ['member_guid', '=', $member_guid],
        ];
        $update_data = [
            'status' => 0
        ];
        $db::update($update_data, $map);
        success('取消收藏成功');
    }
}