<?php

namespace app\controller\member_api\v1;

use app\model\Feedback as FeedbackModel;
use Exception;

class Feedback extends BasicMemberApi
{
    public function list()
    {
        $params            = $this->params;
        $bid               = $this->get_bid();
        $db_feedback_model = new FeedbackModel();
        $map               = [['bid', '=', $bid]];
        $feedback          = $db_feedback_model->order(['create_time' => 'DESC'])->where($map)->select();
        result($feedback);
    }

    /**
     *详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db_feedback_model = new FeedbackModel();
        $params            = $this->params;
        $bid               = $this->get_bid();
        $info              = $db_feedback_model->get_detail($bid, $params['guid']);
        result($info);
    }

    /**
     *详情
     * @access public
     * @return void
     * @throws Exception
     */
    public function add()
    {
        $db_feedback_model = new FeedbackModel();
        $params            = $this->params;
        $bid               = $this->get_bid();
        $member_guid       = $this->get_member_guid();
        $insert_data       = [
            'bid'         => $bid,
            'guid'        => create_guid(),
            'member_guid' => $member_guid,
            'name'        => $params['name'],
            'mobile'      => $params['mobile'],
            'title'       => $params['title'],
            'content'     => $params['content'],
            'image_list'  => $params['image_list'],
        ];
        $result            = $db_feedback_model->save($insert_data);
        success('反馈成功');
    }
}