<?php

namespace app\controller\member_api\v1;

use app\model\SubMerchant;
use app\model\SubMerchantQueryNote;
use app\model\SubMerchantRateApplyNote;
use app\model\SubMerchants;
use app\model\YkyActivitySubmit;
use app\model\YkyAgentMap;
use Exception;

class Merchant extends BasicMemberApi
{
    /**
     * 验证是否有登陆
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function login()
    {
        $params     = $this->params;
        $guid       = $params['guid'];
        $cache_key  = 'merchant_login:' . $guid;
        $login_info = cache($cache_key);
        if (!$login_info) {
            //缓存不存在 则说明是第一次请求,需要发起异步任务
            $job_data = [
                'merchant_code' => '',
                'guid'          => $guid,
            ];
            job()->set_job_name('Merchant@send_login_url')->push_job($job_data);
            $login_info = ['status' => 2];
            cache($cache_key, $login_info, 3600);  //任务发布成功,等待执行
        }
        result($login_info);
    }

    /**
     * 申请调整费率
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply_change_rate()
    {
        $this->check_agent();
        $params        = $this->params;
        $merchant_id   = $params['sub_mch_id'];
        $db            = new SubMerchantRateApplyNote();
        $map           = [['merchant_id', '=', $merchant_id]];
        $merchant_info = $db->where($map)->count();
        if ($merchant_info) {
            error('该商户号' . $merchant_id . '已经提交过费率申请,请等待客服审核');
        }
        $sub    = new \OpenApi\SubMerchant();
        $data   = ['merchant_code' => $merchant_id];
        $result = $sub->crate_benchmarking_getapplylist($data);
        if (!empty($result['applylist'])) {
            $msg = '商户号:' . $merchant_id . '已经于' . $result['applylist'][0]['apply_time'] . '申请过费率调整,请联系客服确认';
            wr_log($msg, 1);
            error($msg);
        }
        try {
            $result      = $sub->crate_benchmarking_validate($merchant_id);
            $insert_data = [
                'merchant_id'         => $merchant_id,
                'apply_appid'         => $this->get_appid(),
                'apply_openid'        => $this->get_openid(),
                'rate'                => $params['rate'],
                'category'            => $result['category'],
                'merchant_name'       => $result['merchant_name'],
                'merchant_short_name' => $result['merchant_short_name'],
            ];
            $db->save($insert_data);
            $this->save_sub_merchant_query_note($merchant_id, 2); //保存查询
            wr_log('商户号:' . $merchant_id . ' 申请调整费率:' . $params['rate']);
            job()->set_job_name('Merchant@change_merchant_rate')->push_job(['merchant_id' => $merchant_id, 'rate' => $params['rate']]); //自动修改费率
        } catch (Exception $e) {
            error($e->getMessage());
        }
        success('提交成功,请留意公众号消息通知');
    }

    /**
     * 验证是否有登陆
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function check_agent()
    {
        $db_yky_agent_map = new YkyAgentMap();
        $map              = [
            ['appid', '=', $this->get_appid()],
            ['openid', '=', $this->get_openid()],
        ];
        $agent_info       = $db_yky_agent_map->where($map)->field(['agent_account'])->find();
        if (empty($agent_info)) {
            error('请先绑定代理商账号');
        }
        return $agent_info;
    }

    /**
     * 保存查询记录
     * @access protected
     * @param string|integer $merchant_id 商户号
     * @param integer $way 途径
     * @return mixed
     */
    protected function save_sub_merchant_query_note($merchant_id, $way = 1)
    {
        $db_query    = new SubMerchantQueryNote();
        $insert_data = [
            'merchant_id' => $merchant_id,
            'way'         => $way,
            'appid'       => $this->get_appid(),
            'openid'      => $this->get_openid()
        ];
        return $db_query->save($insert_data);
    }

    /**
     * 活动报名
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_agent_info()
    {
        $agent_info = $this->check_agent();
        result($agent_info);
    }

    /**
     * 活动报名
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_submit()
    {
        $agent_info  = $this->check_agent();
        $params      = $this->params;
        $db          = new YkyActivitySubmit();
        $type        = $params['type'];
        $account     = $params['account'];
        $map         = [
            ['type', '=', $type],
            ['account', '=', $account],
        ];
        $submit_info = $db->where($map)->field(['status'])->find();
        if (!empty($submit_info) && $submit_info['status'] == 0) {
            error('您提交的账号正在审核中,请耐心等待');
        }
        if (!empty($submit_info) && $submit_info['status'] == 1) {
            error('您提交的账号已经在活动名单中!');
        }
        if ($type == 1) {
            //校验商家账号是否正确
            $login_url    = 'https://www.yunhuiyuan.cn';
            $post_data    = ['account' => $account, 'password' => time()];
            $login_result = curl()->post($login_url, $post_data)->get_body();
            if ($login_result === false) {
                $msg = '商户账号:' . $account . '信息获取失败';
                wr_log($msg, 1);
            }
            if ($login_result['message'] === '账号或密码错误') {
                error('您输入的商家账号不存在!'); //如果商家账号正确 message:账号或密码错误! 多个叹号
            }
        } elseif ($type == 2) {
            if ($account != $agent_info['agent_account']) {
                error('仅允许为自己报名,您的账号' . $agent_info['agent_account'] . ',您报名的账号:' . $account);
            }
        }
        $insert_data = [
            'type'          => $type,
            'activity_type' => $params['activity_type'],
            'account'       => $account,
            'apply_appid'   => $this->get_appid(),
            'apply_openid'  => $this->get_openid(),
            'status'        => 0, //待处理
        ];
        $db->save($insert_data);
        wr_log('收到账号【' . $account . '】的到店红包活动报名申请', 1);
        success('提交成功,T+1个工作日生效');
    }

    /**
     * 申请刷脸权限
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        $params        = $this->params;
        $merchant_id   = $params['sub_mch_id'];
        $db            = new SubMerchant();
        $map           = [['merchant_id', '=', $merchant_id]];
        $merchant_info = $db->where($map)->count();
        if ($merchant_info) {
            error('该商户号已经提交过,请耐心等待!');
        }
        //校验商家账号是否正确
        $business_account = $params['business_account'];
        $login_url        = 'https://www.yunhuiyuan.cn';
        $post_data        = ['account' => $business_account, 'password' => time()];
        $login_result     = curl()->post($login_url, $post_data)->get_body();
        if ($login_result === false) {
            $msg = '商户账号:' . $business_account . '信息获取失败';
            wr_log($msg, 1);
        }
        if ($login_result['message'] === '账号或密码错误') {
            error('您输入的商家账号不存在!'); //如果商家账号正确 message:账号或密码错误! 多个叹号
        }
        $sub = new \OpenApi\SubMerchant();
        try {
            $merchant_info = $sub->get_apply_micro_list(['mchId' => $merchant_id]);
        } catch (Exception $e) {
            $msg = '您输入的商户号:' . $merchant_id . '不存在或者尚未入驻成功';
            error($e->getCode() == '10005' ? $msg : $e->getMessage());
        }
        if (empty($merchant_info['applyMicroList'])) {
            //$merchant_status_text = $sub->get_status_text($merchant_info['applymentState']);
            //error('您输入的商户号:' . $merchant_id . '尚未入驻成功,当前状态[' . $merchant_status_text . '],请确保入驻成功后再次提交!');
            //error('您输入的商户号:' . $merchant_id . '不存在或者尚未入驻成功');
        }
        //校验商户号是否合法,通过find_merchant_info方法已经可确认是否入驻成功的商户号
        //       $result = $db->query_sub_dev_config($merchant_id);
        //       if (!is_array($result)) {
        //           //error('您输入的商户号:' . $merchant_id . '有误或者不属于一卡易渠道!');
        //           error('您输入的商户号:' . $merchant_id . '有误,请确保入驻成功后再次提交!');
        //       }
        $insert_data = [
            'merchant_id'      => $merchant_id,
            //           'merchant_name'    => $params['merchant_name'],
            //           'industry'         => $params['industry'],
            'business_account' => $params['business_account'],
            'apply_appid'      => $this->get_appid(),
            'apply_openid'     => $this->get_openid(),
            'apply_time'       => microsecond(),
            'status'           => -1, //待邀请
            'merchant_status'  => 1,// 有效商户号
        ];
        $db->save($insert_data);
        $msg = "收到新的刷脸支付申请\r\n";
        $msg .= "商户号:" . $merchant_id . "\r\n";
        //       $msg .= "行业:" . $params['industry'] . "\r\n";
        //       $msg .= "商户名称:" . $params['merchant_name'] . "\r\n";
        $msg .= "商家账号:" . $params['business_account'];
        //$db->notify($msg, true);
        job()->set_job_name('Pay@find_appid_by_business_account')->push_job(['merchant_id' => $merchant_id]); //通过商家账号查找appid
        job()->set_job_name('Pay@check_wait_examine_merchant_count')->push_job(); //检查是否需要告警
        job()->set_job_name('Merchant@authorize_pay_and_refund')->push_job(['merchant_id' => $merchant_id]); //自动发起邀请
        success('提交成功,将在当天发起刷脸权限邀请，请留意消息通知,点击消息查看如何确认授权操作');
    }

    /**
     * 查询商户信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_merchant()
    {
        $this->check_agent();
        $search_key = $this->params['search_key'];
        $db         = new SubMerchants();
        $map        = [
            ['id', '=', $search_key]
        ];
        //数据库中查询
        $merchant_id = 0;
        $info        = $db->where($map)->find();
        if (!is_null($info)) {
            $merchant_id  = $info['merchant_id'];
            $company_name = $info['company_name'];
        } else {
            error('该申请单编号不存在,请注意要输入申请单编号,而不是商户号!');
            //           $sub = new \OpenApi\SubMerchant();
            //           try {
            //               $info = $sub->find_merchant_info(['merchantId' => $search_key]);
            //           } catch (\Exception $e) {
            //               error($e->getMessage() . ';请输入申请单编号,不是商户号!');
            //           }
            //           $merchant_id  = $info['tenpayMchId'];
            //           $company_name = $info['companyName'];
        }
        if ($merchant_id) {
            $msg = '申请单编号:' . $search_key . '<br/>';
            $msg .= '商户号:' . $merchant_id . '<br/>';
            $msg .= '商户名称:' . $company_name . '<br/>';
            success($msg);
        }
        error('您输入的申请单编号有误!');
    }

    /**
     * 查询商户费率
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_rate()
    {
        $merchant_id   = $this->params['sub_mch_id'];
        $db            = new SubMerchantRateApplyNote();
        $map           = [['merchant_id', '=', $merchant_id]];
        $merchant_info = $db->where($map)->order(['create_time' => 'DESC'])->find();
        if (!$merchant_info) {
            error('该商户号未提交过费率修改,费率可能是千分之6');
        }
        $sub      = new \OpenApi\SubMerchant();
        $apply_id = $merchant_info['apply_id'];
        if ($apply_id == 0) {
            error('当前费率申请还在审核中,请次日查询!');
        }
        //优先从数据库中查询
        if ($merchant_info['after_rate'] > 0) {
            $rate = $merchant_info['after_rate'];
        } else {
            $result      = $sub->get_rate_detail($apply_id);
            $rate        = $result['rate'];
            $map         = [
                ['apply_id', '=', $apply_id],
                ['merchant_id', '=', $merchant_id],
                ['id', '=', $merchant_info['id']]
            ];
            $update_data = ['after_rate' => $result['rate']];
            $db::update($update_data, $map);
        }
        $msg = '商户号:' . $merchant_id . '当前费率:' . $rate;
        wr_log($msg);
        success($msg);
    }

    /**
     * 查询商户号审核状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query()
    {
        $merchant_id = $this->params['sub_mch_id'];
        $db          = new SubMerchant();
        $map         = [['merchant_id', '=', $merchant_id]];
        $status      = $db->where($map)->value('status');
        if (is_null($status)) {
            error('您输入的商户号:' . $merchant_id . '尚未提交审核');
        }
        $success_msg = '恭喜,商户号:' . $merchant_id . '已通过人脸审核!';
        //$error_msg   = '抱歉,商户号:' . $merchant_id . '尚未通过人脸审核,请耐心等待,审核通过后"一卡易总部"公众号将模板消息通知您!';
        $error_msg = '您输入的商户号:' . $merchant_id . '待您同意授权邀请,具体操作请联系在线客服-支付组';
        $this->save_sub_merchant_query_note($merchant_id, 1);
        if ($status === 1) {
            wr_log($merchant_id . '-通过本地数据库查询成功,已进件审核通过');
            success($success_msg); //优先查看数据库中如果是审核通过的 直接返回
        } elseif ($status == -1) {
            $msg = '您输入的商户号:' . $merchant_id . '待总部发起授权,请留意消息通知!';
            error($msg);
        }
        //$examine_result = $db->get_face_pay_examine_result($merchant_id); 原方法
        $examine_result = $db->get_face_pay_result($merchant_id); //有调用支付下单接口
        if ($examine_result === true) {
            //$update_data = ['status' => 1]; //todo 靠轮询
            //$db::update($update_data, $map);
            wr_log($success_msg);
            //job()->set_job_name('Pay@face_pay_pass_notify')->push_job(['merchant_id' => $merchant_id]); //如果曾经查询过,则进行通知
            success($success_msg);
            //临时都返回未通过
        } elseif ($examine_result === false || is_string($examine_result)) {
            //未通过判断商户号是否属于一卡易渠道
            $result = $db->query_sub_dev_config($merchant_id);
            if (!is_array($result)) {
                //$merchant_status -1 其他 0 未知 1 有效 2 错误商户号 3商户号正确但是归属关系不正确 ,判断方法不够准确, 统一返回模糊提示
                //               $merchant_status = $db->get_merchant_id_status_by_err_msg($result);
                //               if ($merchant_status == 2) {
                //                   $error_msg = '您输入的商户号可能有误,请仔细核对!';
                //               } elseif ($merchant_status == 3) {
                //                   $error_msg = '您输入的商户号可能不属于一卡易渠道,无法查询!';
                //               }
                $error_msg = '查询失败,您输入的商户号:' . $merchant_id . '有误或者不属于一卡易渠道!';
            }
            wr_log($error_msg);
            error($error_msg);
        } elseif (is_string($examine_result)) {
            $msg = '查询异常:' . $examine_result;
            wr_log($msg, 1);
            error($msg);
        } else {
            error('未知错误');
        }
    }

    /**
     * 查询刷脸立减活动参与情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_coupon_used_record()
    {
        $params                 = $this->params;
        $merchant_id            = $params['merchant_id'];
        $db_yky_activity_submit = new YkyActivitySubmit();
        return $db_yky_activity_submit->query_coupon_used_record($merchant_id);
    }
}