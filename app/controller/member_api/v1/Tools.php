<?php

namespace app\controller\member_api\v1;

use app\model\BusinessRenewNote;
use app\model\JuheRechargeOrder;
use app\model\WechatTransfersOrder;
use Exception;
use OpenApi\SubMerchant;

class Tools extends BasicMemberApi
{
    /**
     *数据统计
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function total()
    {
        $config = ['merchant_code' => '**********'];
        $sub    = new SubMerchant($config);
        $result = $sub->get_account_list();
        $amount = $result['data']['v3accountList']['notTrade_useful']['amount'];
        $amount = str_replace(',', '', $amount);

        $account_number = '44201017500052506842';
        $cache_key      = 'bank_balance:' . $account_number;
        $cache_value    = cache($cache_key);

        $db_goods_order          = new \app\model\GoodsOrder();
        $today_goods_order_count = $db_goods_order->whereDay('create_time')->count();

        $super_admin_business_guid = config('app.super_admin_business_guid');
        $db_business_renew_note    = new BusinessRenewNote();
        $map_business_renew_note   = [
            ['operator_bid', '=', $super_admin_business_guid],
            ['type', '=', 1],
        ];
        //本月新签收入
        $this_month_new_buy = $db_business_renew_note->where($map_business_renew_note)->whereMonth('create_time')->sum('income_money');
        //本月续费收入
        $map_business_renew_note = [
            ['operator_bid', '=', $super_admin_business_guid],
            ['type', '=', 3],
        ];
        $this_month_renew        = $db_business_renew_note->where($map_business_renew_note)->whereMonth('create_time')->sum('income_money');
        $data                    = [
            '云会员运营账户余额' => $amount,
            '建行余额'           => floatval($cache_value),
            '本月新签'           => $this_month_new_buy,
            '本月续费'           => $this_month_renew,
            '今日订单数'         => $today_goods_order_count,
        ];
        result($data);


        $db                 = new WechatTransfersOrder();
        $map                = [
            ['status', '=', -1]
        ];
        $failed_money       = $db->whereDay('create_time')->where($map)->sum('amount');//零钱包今日失败金额
        $map                = [
            ['status', '=', 1]
        ];
        $today_pay_money    = $db->whereDay('payment_time')->where($map)->sum('amount'); //零钱包今日到帐金额
        $db                 = new JuheRechargeOrder();
        $map                = [
            ['status', 'in', [0, -1]],
        ];
        $juhe_wait_money    = $db->whereDay('create_time')->where($map)->sum('value');
        $map                = [
            ['status', '=', 1],
        ];
        $juhe_success_money = $db->whereDay('create_time')->where($map)->sum('value');
        $config             = ['merchant_code' => '**********'];
        $sub                = new SubMerchant($config);
        $result             = $sub->get_account_list();
        $amount             = $result['data']['v3accountList']['notTrade_useful']['amount'];
        $amount             = str_replace(',', '', $amount);
        $data               = [
            '云会员运营账户余额'   => $amount,
            '零钱包今日失败金额'   => tools()::nc_price_fen2yuan($failed_money),
            '零钱包今日到帐金额'   => tools()::nc_price_fen2yuan($today_pay_money),
            '聚合今日等待充值金额' => $juhe_wait_money,
            '聚合今日充值成功金额' => $juhe_success_money,
        ];
        result($data);
    }

    /**
     *首页数据
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function home()
    {
        $db    = new JuheRechargeOrder();
        $money = $db->get_wait_for_recharge_money();
        $data  = [
            'wait_for_recharge_money' => $money
        ];
        result($data);
    }
}