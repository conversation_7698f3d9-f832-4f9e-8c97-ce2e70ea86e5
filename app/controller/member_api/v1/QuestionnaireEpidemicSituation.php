<?php

namespace app\controller\member_api\v1;

use app\model\QuestionnaireEpidemicSituation as QuestionnaireEpidemicSituationModel;

class QuestionnaireEpidemicSituation extends BasicMemberApi
{
    public function add()
    {
        $db                    = new QuestionnaireEpidemicSituationModel();
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $params['bid']         = $bid;
        $params['guid']        = create_guid();
        $params['member_guid'] = $this->get_member_guid();
        $db->save($params);
        success('提交成功');
    }

    public function detail()
    {
        $db          = new QuestionnaireEpidemicSituationModel();
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']]
        ];
        $info        = $db->where($map)->append(['mobile_with_star', 'id_card_with_star', 'name_with_star'])->findOrFail();
        result($info);
    }

    public function list()
    {
        $db          = new QuestionnaireEpidemicSituationModel();
        $params      = $this->params;
        $bid         = $this->get_bid();
        $member_guid = $this->get_member_guid();
        $map         = [
            ['bid', '=', $bid]
        ];
        $list        = $db->where($map)->order(['create_time' => 'DESC'])->select();
        result($list);
    }
}