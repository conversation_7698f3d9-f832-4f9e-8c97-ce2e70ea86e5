<?php

namespace app\controller\member_api\v1;

use app\model\Store as StoreModel;
use Exception;
use think\facade\Db;

class Store extends BasicMemberApi
{
    /**
     *门店列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db        = new StoreModel();
        $params    = $this->params;
        $longitude = $params['lon'] ?? 0;
        $latitude  = $params['lat'] ?? 0;
        $map       = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
        ];
        $field     = [
            'a1.name'                                   => 'province_name',
            'a2.name'                                   => 'city_name',
            'a3.name'                                   => 'area_name',
            "CONCAT(a1.name,a2.name,a3.name,s.address)" => 'address_info',
            's.*',
            Db::raw("ROUND(6378.138*2*ASIN(SQRT(POW(SIN(($latitude*PI()/180-latitude*PI()/180)/2),2)+COS($latitude*PI()/180)*COS(latitude*PI()/180)*POW(SIN(($longitude*PI()/180-longitude*PI()/180)/2),2)))*1000) as distance")
        ];
        $join      = [
            ['area a1', "s.province_id = a1.id"],
            ['area a2', "s.city_id = a2.id AND a2.pid=a1.id"],
            ['area a3', "s.area_id = a3.id AND a3.pid=a2.id"],
        ];
        unset($this->params['lon']);
        unset($this->params['lat']);
        if (!empty($params['store_name'])) {
            $map[] = ['store_name', 'LIKE', '%' . $params['store_name'] . '%'];
        }
        if (!empty($params['store_name'])) {
            $map[] = ['store_name', 'LIKE', '%' . $params['store_name'] . '%'];
        }
        if (!empty($params['industry_guid'])) {
            $map[] = ['industry_guid', '=', $params['industry_guid']];
        }
        if (!empty($params['brand_guid'])) {
            $map[] = ['brand_guid', '=', $params['brand_guid']];
        }
        unset($this->params['store_name']);
        unset($this->params['industry_guid']);
        unset($this->params['brand_guid']);
        $result = $db->alias('s')->join($join)->field($field)->where($map)->order(['distance' => 'ASC', 'sort' => 'ASC'])->select();
        foreach ($result as $key => $val) {
            $result[$key]['icon']          = tools()::add_thumbnail_mini($val['icon']);
            $result[$key]['pic']           = tools()::add_thumbnail_mini($val['pic']);
            $result[$key]['distance_text'] = $val['distance'] > 1000 ? tools()::nc_price_calculate($val['distance'], '/', 1000) . ' km' : $val['distance'] . ' m';
        }
        result($result);
    }

    /**
     *门店列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list_bak()
    {
        $db        = new StoreModel();
        $params    = $this->params;
        $longitude = $params['lon'] ?? 0;
        $latitude  = $params['lat'] ?? 0;
        $map       = [['bid', '=', $this->get_bid()]];
        $field     = [
            'a1.name'                                   => 'province_name',
            'a2.name'                                   => 'city_name',
            'a3.name'                                   => 'area_name',
            "CONCAT(a1.name,a2.name,a3.name,s.address)" => 'address_info',
            's.*',
            Db::raw("ROUND(6378.138*2*ASIN(SQRT(POW(SIN(($latitude*PI()/180-latitude*PI()/180)/2),2)+COS($latitude*PI()/180)*COS(latitude*PI()/180)*POW(SIN(($longitude*PI()/180-longitude*PI()/180)/2),2)))*1000) as distance")
        ];
        $join      = [
            ['area a1', "s.province_id = a1.id"],
            ['area a2', "s.city_id = a2.id AND a2.pid=a1.id"],
            ['area a3', "s.area_id = a3.id AND a3.pid=a2.id"],
        ];
        unset($this->params['lon']);
        unset($this->params['lat']);
        if (!empty($params['store_name'])) {
            $map[] = ['store_name', 'LIKE', '%' . $params['store_name'] . '%'];
        }
        unset($this->params['store_name']);

        $this->model = $db->alias('s')->join($join)->field($field)->where($map)->order(['distance' => 'ASC']);
        result($this->_list());
    }

    /**
     *门店详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db     = new StoreModel();
        $params = $this->params;
        $info   = $db->get_detail($params);
        result($info);
    }
}