<?php

namespace app\controller\member_api\v1;

use app\model\Cart as CartModel;
use app\model\Goods as GoodsModel;
use Exception;
use think\facade\Db;

class Cart extends BasicMemberApi
{
    /**
     *获取购物车明细
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get()
    {
        $db_cart               = new CartModel();
        $db_member             = new \app\model\Member();
        $bid                   = $this->get_bid();
        $member_guid           = $this->get_member_guid();
        $member_discount_ratio = $db_member->get_member_discount_ratio($bid, $member_guid);
        $field                 = [
            'c.guid'   => 'cart_guid',
            'g.guid'   => 'goods_guid',
            'g.name'   => 'goods_name',
            //            'g.price'  => 'unitPrice',
            Db::raw("ROUND(g.price*$member_discount_ratio,2) as unitPrice"),
            'c.amount' => 'num',
            'g.pic'    => 'goods_pic',
            Db::raw("ROUND(g.price*c.amount*$member_discount_ratio,2) as price"),
            //            'g.price*c.amount' => "price"
            'attr'     => 'attr_list'
        ];
        $join                  = [
            ['goods g', 'c.goods_guid=g.guid AND c.bid=g.bid'],
        ];
        $map                   = [
            ['c.bid', '=', $bid],
            ['c.member_guid', '=', $member_guid],
        ];
        $list                  = $db_cart->alias('c')->where($map)->field($field)->join($join)->select();
        $list                  = tools()::object2array($list);
        $total_price           = 0;
        $host                  = config('app.app_host_domain');
        foreach ($list as $key => $val) {
            //处理主图
            $list[$key]['goods_pic'] = tools()::add_thumbnail_mini($val['goods_pic']);
            $list[$key]['attr_list'] = json_decode($list[$key]['attr_list'], true);
            if (!empty($list[$key]['attr_list'])) {
                $db_goods = new GoodsModel();
                $sku_info = $db_goods->get_goods_sku_info_by_sku_json($bid, $val['attr_list'], $val['goods_guid']);
                if (!empty($sku_info)) {
                    //如果SKU被删除了,则依旧使用原价
                    $list[$key]['unitPrice'] = tools()::nc_price_calculate($sku_info['price'], '*', $member_discount_ratio);
                    $list[$key]['price']     = tools()::nc_price_calculate($list[$key]['unitPrice'], '*', $list[$key]['num']);
                }
            }
            $list[$key]['price']     = (float)$list[$key]['price'];
            $list[$key]['unitPrice'] = (float)$list[$key]['unitPrice'];
            $total_price             += $val['price'];
            $list[$key]['disabled']  = false;
            $list[$key]['max_num']   = 100;
        }
        $data = [
            'list'        => $list,
            'total_num'   => count($list),
            'total_price' => $total_price,
            'page_count'  => 1,
            'row_count'   => count($list),
            'mch_list'    => []
        ];
        result($data);
    }

    /**
     *删除购物车中商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function remove()
    {
        $member_guid    = $this->get_member_guid();
        $cart_guid_list = json_decode($this->request->param('cart_guid_list', '', null), true);
        if (empty($cart_guid_list)) {
            error('没有要删除的商品');
        }
        $map = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $member_guid],
            ['guid', 'in', $cart_guid_list],
        ];
        $db  = new CartModel();
        $db->where($map)->delete();
        success('删除成功');
    }

    /**
     *添加减少数量/移除商品/购物车
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update()
    {
        $params      = $this->params;
        $member_guid = $this->get_member_guid();
        $db          = new CartModel();
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $member_guid],
        ];
        $goods_guid  = null;
        if (!empty($params['goods_guid'])) {
            $goods_guid = $params['goods_guid'];
            $map[]      = ['goods_guid', '=', $goods_guid];
        }
        if (!empty($params['cart_guid'])) {
            $cart_guid = $params['cart_guid'];
            $map[]     = ['guid', '=', $cart_guid];
        }
        if (!empty($params['attr'])) {
            $attr  = $params['attr'];
            $map[] = ['attr', '=', $attr];
        }
        $amount = (int)$params['amount'];
        if ($amount == 0) {
            //删除该商品
            $db->where($map)->delete();
            success('商品删除成功');
        }
        $count = $db->where($map)->count();
        if ($count) {
            //存在则更新数量(可能是添加也可能是减少)
            $update_data = [
                'amount' => $amount,
            ];
            $db::update($update_data, $map);
            success('数量更新成功');
        } elseif ($goods_guid) {
            $insert_data = [
                'guid'        => create_guid(),
                'member_guid' => $member_guid,
                'bid'         => $this->get_bid(),
                'amount'      => $amount,
                'attr'        => json_decode($params['attr'], true),
                'goods_guid'  => $goods_guid,
            ];
            $db->save($insert_data);
            success('加入成功');
        } else {
            error('出错了');
        }
    }

    /**
     *删除或者清空 购物车
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function clear()
    {
        $member_guid = $this->get_member_guid();
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['member_guid', '=', $member_guid],
        ];
        $db          = new CartModel();
        $db->where($map)->delete();
        success('清空成功');
    }
}