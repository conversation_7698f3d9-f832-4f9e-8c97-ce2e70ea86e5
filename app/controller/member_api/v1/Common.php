<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;

use app\model\Area;
use app\model\AreaRule;
use Exception;

class Common extends BasicMemberApi
{
    public function get_copyright()
    {
        $bid            = $this->get_bid();
        $config         = get_config_by_bid($bid);
        $copy_right_url = $config['copy_right_url'];
        $copy_right_url = str_replace('{$BID}', $bid, $copy_right_url);
        $data           = [
            'copy_right_url'    => $copy_right_url,
            'copy_right_name'   => $config['copy_right_name'],
            'copy_right_footer' => $config['copy_right_footer'],
        ];
        result($data);
    }

    public function ip_to_area_id()
    {
        $db = new Area();
        result($db->ip_to_area_id());
    }

    /**
     *地区
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function area()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $pid    = $params['pid'] ?? 1;
        $limit  = $params['limit'] ?? true;
        $token  = $params['token'] ?? null; //卡券提货时代token 包含 bid  coupon_guid coupon_send_note_guid

        $db_area   = new Area();
        $map       = [['pid', '=', $pid]];
        $list      = $db_area->field(['id', 'name', 'level'])->where($map)->order('id')->select()->toArray();
        $map       = [['id', '=', $pid]];
        $pid_level = $db_area->where($map)->value('level');
        if (!in_array($pid_level, ['city']) && $limit) {
            $db_area_rule = new AreaRule();
            $rule_list    = $db_area_rule->get_rule_list($bid, $token);
            foreach ($rule_list as $rule) {
                $type         = $rule['type']; // 1 限制可选 2 限制不可选
                $area_id_list = $rule['area_id_list'];
                foreach ($list as $key => $val) {
                    if ($type == 1) {
                        if (!in_array($val['id'], $area_id_list)) {
                            unset($list[$key]);
                        }
                    } elseif ($type == 2) {
                        if (in_array($val['id'], $area_id_list)) {
                            unset($list[$key]);
                        }
                    }
                }

            }
        }
        result(array_values($list));
    }

    /**
     *地区
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function district()
    {
        $db            = new Area();
        $list          = $db->field(['id', 'pid', 'name', 'level'])->select()->toArray();
        $province_list = $this->build_list($list, 1);
        foreach ($province_list as $i => $province) {
            $city_list = $this->build_list($list, $province['id']);
            foreach ($city_list as $j => $city) {
                $district_list         = $this->build_list($list, $city['id']);
                $city_list[$j]['list'] = $district_list;
            }
            $province_list[$i]['list'] = $city_list;
        }
        result($province_list);
    }

    /**
     *创建列表
     * @access protected
     * @param array $list
     * @param integer $pid
     * @return mixed
     * @throws Exception
     */
    protected function build_list($list, $pid)
    {
        $data = [];
        foreach ($list as $key => $val) {
            if ($val['pid'] == $pid) {
                $data[] = ['id' => $val['id'], 'name' => $val['name']];
            }
        }
        return $data;
    }
}