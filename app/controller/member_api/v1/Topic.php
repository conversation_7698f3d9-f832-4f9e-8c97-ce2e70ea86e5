<?php

namespace app\controller\member_api\v1;

use app\model\Favorites as FavoritesModel;
use Exception;

class Topic extends BasicMemberApi
{
    /**
     *首页
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $db_topic    = new \app\model\Topic();
        $this->model = $db_topic->where($map)->order(['sort' => 'ASC'])->append(['cover_pic_mini']);
        $list        = $this->_list();
        result($list);
    }

    /**
     *文章详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db                  = new \app\model\Topic();
        $params              = $this->params;
        $member_guid         = $this->get_member_guid();
        $bid                 = $this->get_bid();
        $guid                = $params['guid'];
        $map                 = [
            ['bid', '=', $bid],
            ['guid', '=', $guid]
        ];
        $info                = $db->where($map)->findOrFail();
        $db_favorites        = new FavoritesModel();
        $map                 = [
            ['bid', '=', $bid],
            ['type', '=', 3],
            ['relation_guid', '=', $guid],
            ['member_guid', '=', $member_guid],
        ];
        $is_favorite         = $db_favorites->where($map)->value('status');
        $info['is_favorite'] = $is_favorite ? 1 : 0;
        $info['content']     = tools()::add_rich_img_class($info['content']);
        result($info);
    }
}
