<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use app\model\XiaoQuHouse;
use app\model\XiaoQuBindInfo;
use app\middleware\CheckMemberPageAccessPermission;
use app\middleware\MemberWechatUserInfo;
use OpenApi\ZhuJianJu;

class House extends BasicMemberApi
{
    protected array $middleware = [
        MemberWechatUserInfo::class,
        CheckMemberPageAccessPermission::class,
    ];

    public function zhu_jian_ju()
    {
        //返回最后更新时间
        $last_update_time = cache('zjj:last_update_time') ?: format_timestamp();
        $last_update_time = date('m-d H:i', strtotime($last_update_time));

        $db_xiao_qu_bind_info = new XiaoQuBindInfo();
        $last_bind_list       = $db_xiao_qu_bind_info->get_last_bind_list();

        $db_xiao_qu      = new XiaoQuHouse();
        $is_super_member = $db_xiao_qu->is_super_member();
        $is_super_member = 1;
        $bind_list       = $db_xiao_qu->get_bind_list();
        $last_vote_list  = [];
        if ($is_super_member) {
            $last_vote_list = $db_xiao_qu->get_last_vote_list();
        }
        $bound_rooms_rate = $db_xiao_qu->get_bound_rooms_rate();
        $zzj              = new ZhuJianJu();

        //投票情况
        $bind_info_array                  = $zzj->get_list_ea_area_bind();
        $bind_ea_area_house_success_total = $bind_info_array['BindEaAreaHouseSuccessTotal'];//总户数
        $bind_ea_area_owner_success_total = $bind_info_array['BindEaAreaOwnerSuccessTotal'];//总人数
        $ea_area_house_total_rate         = $bind_info_array['EaAreaHouseTotalRate'];

        $bind_info = [
            'bind_house_num'  => $bind_ea_area_house_success_total,
            'bind_person_num' => $bind_ea_area_owner_success_total,
            'bind_rate'       => $ea_area_house_total_rate,
        ];

        //投票情况
        $total_voted_info           = $zzj->get_total_voted_info_v2();
        $official_voted_info        = $zzj->get_official_voted_info();
        $temp_voted_conversion_rate = 0.8;
        $temp_voted_house_num       = $total_voted_info['total_voted_house_num'] - $official_voted_info['official_voted_house_num'];
        $temp_voted_person_num      = (int)($temp_voted_house_num * $temp_voted_conversion_rate); //临时投票人数预估
        $temp_voted_info            = [
            'temp_voted_house_num'  => $temp_voted_house_num, //临时投票户数
            'temp_voted_person_num' => $temp_voted_person_num, //临时投票人数
            'temp_voted_house_area' => $total_voted_info['total_voted_house_area'] - $official_voted_info['official_voted_house_area'], //临时投票户数'
        ];
        //总人数=正式票人数+临时票人数(估算出来)
        $many_house_person_num   = $bind_ea_area_house_success_total - $bind_ea_area_owner_success_total;//总绑定户数-总绑定人数 = 一人多户的人数
        $total_voted_person_num1 = $total_voted_info['total_voted_house_num'] - $many_house_person_num;
        $total_voted_person_num2 = $official_voted_info['official_voted_person_num'] + $temp_voted_person_num;
        if ($total_voted_person_num1 > $total_voted_person_num2) {
            $temp_voted_info['temp_voted_person_num'] += $total_voted_person_num1 - $total_voted_person_num2;
        }
        $total_voted_info       = $zzj->get_total_voted_info_v2();
        $total_voted_house_num  = $total_voted_info['total_voted_house_num'];//投票户数
        $total_voted_house_area = $total_voted_info['total_voted_house_area'];//投票户数

        $db_xiao_qu_house                           = new XiaoQuHouse();
        $map_xiao_qu_house                          = [
            ['vote_date_time', 'NOT NULL', NULL]
        ];
        $total_voted_person_num                     = $db_xiao_qu_house->where($map_xiao_qu_house)->count('DISTINCT certificate_code_full');
        $total_voted_info['total_voted_person_num'] = $total_voted_person_num;

        $total_house_num   = 2317;//总户数
        $total_person_num  = 1988;//总人数
        $total_house_area  = 236800;
        $achievement_ratio = 2 / 3;

        $total_info = [
            'total_house_num'  => $total_house_num,//总户数
            'total_person_num' => $total_person_num,//总人数
            'total_house_area' => $total_house_area,//总面积
        ];


        $target_person_num        = ceil($total_person_num * $achievement_ratio);//目标总人数
        $default_target_house_num = ceil($total_house_num * $achievement_ratio);//目标总户数
        $target_house_num         = $total_voted_person_num == 0 ? $default_target_house_num : (int)($target_person_num * $total_voted_house_num / $total_voted_person_num);//目标总户数
        $target_house_area        = ceil($total_house_area * $achievement_ratio);//目标总面积
        $target_info              = [
            'target_house_num'  => $target_house_num,
            'target_person_num' => $target_person_num,
            'target_house_area' => $target_house_area
        ];

        $differ_house_num  = ceil($target_house_num - $total_voted_house_num);//差额总户数
        $differ_person_num = ceil($target_person_num - $total_voted_person_num);//差额总人数
        $differ_house_area = ceil($target_house_area - $total_voted_house_area);//差额总面积

        $differ_info = [
            'differ_house_num'  => $differ_house_num,
            'differ_person_num' => $differ_person_num,
            'differ_house_area' => $differ_house_area
        ];
        $return_data = [
            'temp_voted_conversion_rate' => $temp_voted_conversion_rate,//临时票转化率
            'last_update_time'           => $last_update_time,//最后更新时间
            'bound_rooms_rate'           => $bound_rooms_rate, //分栋绑定情况
            'last_bind_list'             => $last_bind_list,//最后X条绑定记录
            'bind_list'                  => $bind_list, //多维数组 绑定记录 分栋展示
            'last_vote_list'             => $last_vote_list,//最新投票数据
            'total_info'                 => $total_info,//小区总情况
            'target_info'                => $target_info,//目标总情况
            'differ_info'                => $differ_info,//差额情况
            'bind_info'                  => $bind_info,//绑定情况
            'official_voted_info'        => $official_voted_info,//正式票情况
            'temp_voted_info'            => $temp_voted_info,//临时票情况
            'total_voted_info'           => $total_voted_info,//总投票情况
            'is_super_member'            => $is_super_member,//是否超级会员 用于判断前端权限
            'member_id'                  => $this->get_member_id(),//用户ID
            'person_progress'            => (int)($total_voted_person_num / $target_person_num * 100),
            'area_progress'              => (int)($total_voted_house_area / $target_house_area * 100),
        ];
        result($return_data);
    }

}