<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\member_api\v1;


use app\model\Business;
use app\model\PayQrcodeItem;
use app\model\ShortUrl;
use app\model\UserBindNote;
use app\model\YkyAgentMap;
use app\model\YkyUserMap;
use Exception;

class Bind extends BasicMemberApi
{
    /**
     * 绑定支付二维码
     * @access public
     * @return void
     * @throws Exception
     */
    public function qrcode()
    {
        $params       = $this->params;
        $openid       = $this->get_openid();
        $bid          = $this->get_bid();
        $qrcode_guid  = $params['guid'];
        $account      = $params['account'];
        $user_account = $params['user_account'];
        $password     = $params['password'];
        //验证商家信息
        $db_business   = new Business();
        $business_info = $db_business->login_verify($account);
        if (!$business_info) {
            error($db_business->getError());
        }
        //验证工号
        $db_user                = new \app\model\User();
        $data['business_guid']  = $business_info['guid'];
        $data['login_password'] = $password;
        $data['user_account']   = $user_account;
        $user_info              = $db_user->login_verify($data);
        if ($user_info === false) {
            // 登录失败要记录在日志里
            error($db_user->getError());
        }
        //验证通过
        $db_qrocde_bind_note = new PayQrcodeItem();
        $map                 = [
            ['guid', '=', $qrcode_guid],
            ['bind_bid', '=', ''],
            ['bind_user_guid', '=', ''],
            ['bind_time', 'null', null],
        ];
        $qrcode_note         = $db_qrocde_bind_note->where($map)->find();
        if (!$qrcode_note) {
            error('二维码已失效');
        }
        $update_data = [
            'bind_time'      => microsecond(),
            'bind_bid'       => $business_info['guid'],
            'bind_user_guid' => $user_info['guid']
        ];
        $qrcode_note->where($map)->update($update_data);
        $short_url_code = $qrcode_note['short_url_code'];
        $db_short_url   = new ShortUrl();
        $long_url       = (string)url('member/bind/qrcode', ['bid' => $bid, 'guid' => $qrcode_guid], false, true);
        $map            = [
            ['code', '=', $short_url_code],
            ['long_url', '=', $long_url]
        ];
        $new_long_url   = (string)url('member/pay', ['bid' => $business_info['guid'], 'user_guid' => $user_info['guid']], false, true);
        $result         = $db_short_url->where($map)->update(['long_url' => $new_long_url]);
        if ($result) {
            result(['url' => $new_long_url, 'msg' => '绑定成功']);
        } else {
            error('绑定失败!');
        }
    }

    /**
     * 绑定一卡易工号
     * @access public
     * @return void
     * @throws Exception
     */
    public function yky_user()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $db     = new YkyUserMap();
        $login  = $db->login($params['user_name'], $params['password'], $params['user_account']);
        if ($login !== true) {
            error($db->getError());
        }
        $bind = $db->bind_user($this->get_appid(), $this->get_openid(), $params['user_name'], $params['user_account'], $params['password'], $bid);
        if ($bind !== true) {
            error($db->getError());
        }
        success('绑定成功');
    }

    /**
     * 绑定代理商账号
     * @access public
     * @return void
     * @throws Exception
     */
    public function yky_agent()
    {
        $params   = $this->params;
        $account  = $params['account'];
        $password = $params['password'];
        $url      = 'http://openapi.1card1.cn/Test/Add_Business';
        $data     = [
            'AgentAccount' => $account,
            'Password'     => $password,
            'Data'         => '{}',
        ];
        $result   = curl()->post($url, $data)->get_body();
        if (empty($result)) {
            error('系统繁忙,请联系在线客服反馈');
        }
        $result  = json_decode($result['message'], JSON_UNESCAPED_UNICODE);
        $message = $result['message'];
        if (strpos($message, 'businessAccount') === false) {
            error('账号或密码错误');
        }
        $appid            = $this->get_appid();
        $openid           = $this->get_openid();
        $bid              = $this->get_bid();
        $db_yky_agent_map = new YkyAgentMap();
        $map              = [
            ['bid', '=', $bid],
            ['appid', '=', $appid],
            ['openid', '=', $openid],
        ];
        $bind_note        = $db_yky_agent_map->where($map)->count();
        if (empty($bind_note)) {
            $insert_data = [
                'guid'          => create_guid(),
                'bid'           => $bid,
                'agent_account' => $account,
                'appid'         => $appid,
                'openid'        => $openid,
                'status'        => 1
            ];
            $db_yky_agent_map->save($insert_data);
        }
        success('绑定成功');
    }
}