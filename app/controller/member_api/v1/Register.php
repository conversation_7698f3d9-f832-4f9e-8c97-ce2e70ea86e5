<?php

namespace app\controller\member_api\v1;


use app\model\TrialApplication;
use app\common\service\NotifyService;
use Exception;

class Register extends BasicMemberApi
{
    /**
     * 申请试用
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_trial_application = new TrialApplication();
        $ip                   = tools()::get_client_ip();
        $region               = tools()::get_client_ip_region_info($ip);
        $data                 = [
            'guid'           => create_guid(),
            'bid'            => $bid,
            'member_guid'    => $this->get_member_guid(),
            'company'        => $params['company'],
            'true_name'      => $params['true_name'],
            'mobile'         => $params['mobile'],
            'account'        => $params['account'],
            'version_guid'   => $params['version_guid'],
            'password'       => md5($params['password']),
            'ip'             => $ip,
            'ip_region_info' => $region,
            'status'         => 0, //待审核
        ];
        $db_trial_application->save($data);
        //        notify()->set_key_name(NotifyService::BusinessApplyTrialSuccessfully)->set_data($data)->set_bid($bid)->send();
        $url  = (string)url('admin/trial_application/index', ['bid' => $bid], false, true);
        $data = [
            'url'         => $url,
            'title'       => '商家【' . $params['company'] . '】试用申请已提交',
            'detail'      => '账号:' . $params['account'],
            'user'        => '【账号申请】',
            'name'        => $ip . '(' . $region . ')',
            'remark'      => '点击审核',
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_system()->set_data($data)->set_bid($bid)->send();
        success('提交成功,我们将尽快联系您');
    }
}