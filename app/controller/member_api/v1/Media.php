<?php

namespace app\controller\member_api\v1;

use app\model\MediaCategory;
use Exception;

class Media extends BasicMemberApi
{
    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function media_list()
    {
        $db_media      = new \app\model\Media();
        $bid           = $this->get_bid();
        $params        = $this->params;
        $map           = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $category_guid = $params['category_guid'] ?? null;
        $type_guid     = $params['type_guid'] ?? null;
        if ($category_guid) {
            $map[] = ['category_guid', '=', $category_guid];
        }
        if ($type_guid) {
            $map[] = ['type_guid', '=', $type_guid];
        }
        $order       = [
            'sort'        => 'ASC',
            'create_time' => 'DESC',
        ];
        $this->model = $db_media->where($map)->order($order)->append(['pic_with_water_mark']);
        result($this->_list());
    }

    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function search_list()
    {
        $db_media_category   = new MediaCategory();
        $bid                 = $this->get_bid();
        $map                 = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $order               = [
            'sort'        => 'ASC',
            'create_time' => 'DESC',
        ];
        $media_category_list = $db_media_category->where($map)->order($order)->select()->toArray();
        //        $db_media_type       = new \app\model\MediaType();
        //        $media_type_list     = $db_media_type->where($map)->order($order)->select()->toArray();

        result(

            [
                ['name'     => '分类',
                 'key_name' => 'category_guid',
                 'data'     => $media_category_list,
                ],
                //                ['name'     => '类型',
                //                 'key_name' => 'type_guid',
                //                 'data'     => $media_type_list,
                //                ],
            ]
        );
    }
}