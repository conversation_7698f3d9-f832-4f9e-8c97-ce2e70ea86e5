<?php

namespace app\controller\gateway;


use app\model\CropWechatApp;
use app\model\CropWechatAuth;
use Exception;
use Stoneworld\Wechat\Utils\Bag;

class WorkWeixinCommand extends BasicGateway
{
    public function get_suite_id()
    {
        if (request()->isPost()) {
            $xmlInput = file_get_contents('php://input');
            $array    = tools()::xml2arr($xmlInput);
            return $array['ToUserName'];
        }
        return $this->request->route('suite_id', '');
    }

    /**
     *企业微信事件推送
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $platform_corpid = $this->request->route('platform_corpid');
        $suite_id        = $this->get_suite_id();
        if ($suite_id) {
            $db_crop_wechat_app = new CropWechatApp();
            $server_config      = $db_crop_wechat_app->get_suite_config($platform_corpid, $suite_id);
            if (request()->isGet()) {
                $server_config['appid'] = $platform_corpid;
            } else {
                $server_config['appid'] = $suite_id;
            }
            $server = work_weixin()::Server($server_config);

            // 监听所有事件
            $server->on('info', function ($info) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $info Bag
                 */
                $data      = $info->toArray();
                $info_type = $data['InfoType'];
                switch ($info_type) {
                    case 'suite_ticket':
                        $suite_id     = $data['SuiteId'];
                        $suite_ticket = $data['SuiteTicket'];
                        $db           = new CropWechatApp();
                        $map          = [['suite_id', '=', $suite_id]];
                        $update_data  = [
                            'suite_ticket'             => $suite_ticket,
                            'suite_ticket_update_time' => format_timestamp()
                        ];
                        $db::update($update_data, $map);
                        break;
                    case 'create_auth':
                        wr_log($info_type, 1);
                        $suite_id         = $data['SuiteId'];
                        $auth_code        = $data['AuthCode'];
                        $config           = [
                            'platform_corpid' => $platform_corpid,
                            'suite_id'        => $suite_id,
                        ];
                        $auth             = work_weixin($config)::Auth();
                        $result           = $auth->get_permanent_code($auth_code);
                        $crop_wechat_auth = new CropWechatAuth();
                        $auth_corpid      = $result['auth_corp_info']['corpid'];
                        $agent_id         = $result['auth_info']['agent'][0]['agentid'];
                        wr_log($auth_corpid);
                        $map         = [
                            ['auth_corpid', '=', $auth_corpid]
                        ];
                        $count       = $crop_wechat_auth->where($map)->count();
                        $update_data = [
                            'info'            => $result,
                            'platform_corpid' => $platform_corpid,
                            'suite_id'        => $suite_id,
                            'agent_id'        => $agent_id,
                            'auth_corpid'     => $auth_corpid,
                            'permanent_code'  => $result['permanent_code'],
                        ];
                        if ($count > 0) {
                            $crop_wechat_auth::update($update_data, $map);
                        } else {
                            $crop_wechat_auth->save($update_data);
                        }
                        break;
                    case 'change_auth':
                        $suite_id    = $data['SuiteId'];
                        $auth_corpid = $data['AuthCorpId'];
                        wr_log($auth_corpid . '变更授权', 1);
                        break;

                    case 'cancel_auth':
                        $suite_id    = $data['SuiteId'];
                        $auth_corpid = $data['AuthCorpId'];
                        wr_log($auth_corpid . '取消授权', 1);
                        break;
                    case 'change_external_contact':
                        wr_log('change_external_contact', 1);
                        $suite_id    = $data['SuiteId'];
                        $change_type = $data['ChangeType'] ?? '';
                        switch ($change_type) {
                            case 'add_external_contact':
                                //添加联系人
                                wr_log($change_type, 1);
                                wr_log($data, 1);
                                $user_id          = $data['UserID'];
                                $external_user_id = $data['ExternalUserID'];
                                $auth_corpid      = $data['AuthCorpId'];
                                $welcome_code     = $data['WelcomeCode'] ?? '';
                                $msg              = '您好,user_id:' . $user_id . ';external_user_id=' . $external_user_id . '(本消息由API接口发送)';
                                if ($welcome_code) {
                                    $config    = [
                                        'platform_corpid' => $platform_corpid,
                                        'suite_id'        => $suite_id,
                                        'auth_corpid'     => $auth_corpid,
                                    ];
                                    $contact   = work_weixin($config)::Contact();
                                    $post_data = [
                                        'welcome_code' => $welcome_code,
                                        'text'         => ['content' => $msg],
                                    ];
                                    $contact->send_welcome_msg($post_data);
                                }
                                break;
                            default:
                                wr_log($change_type, 1);
                                wr_log($data, 1);
                                break;
                        }
                        break;
                    default:
                        wr_log('暂时不支持的InfoType:' . $info_type, 1);
                        wr_log($data, 1);
                        break;
                }
                return 'success';
            });

            // 监听所有事件
            $server->on('event', function ($event) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $event Bag
                 */
                $data  = $event->toArray();
                $event = $data['Event'];
                wr_log($event, 1);
                switch ($event) {
                    case '':
                        break;
                    default:
                        break;

                }
                return 'success';
            });
            // 监听所有事件
            $server->on('message', function ($message) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $event Bag
                 */
                $data = $message->toArray();
                wr_log($data, 1);
                return 'success';
            });
            // 只监听指定类型事件
            //       $server->on('event', 'subscribe', function ($event) {
            //           wr_log('收到关注事件，关注者openid: ' . $event['FromUserName'], 1);
            //           return Message::make('text')->content('感谢您关注');
            //       });
            return (string)$server->server();
        }
        return '';
    }

}