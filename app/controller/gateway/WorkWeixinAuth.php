<?php

namespace app\controller\gateway;


use app\model\CropWechatAuth;
use app\model\Parameter as ParameterModel;
use Exception;
use Stoneworld\Wechat\Message;
use Stoneworld\Wechat\Utils\Bag;


class WorkWeixinAuth extends BasicGateway
{
    /**
     *企业微信事件推送
     * @access public
     * @return string
     * @throws Exception
     */
    public function index()
    {
        $params          = $this->params;
        $platform_corpid = $params['platform_corpid'];
        $config          = config('work_weixin_platform');
        if (empty($config[$platform_corpid])) {
            error('当前应用未配置');
        }
        $cropid_config = $config[$platform_corpid];
        $server_config = [
            'appid'          => $cropid_config['corp_id'],
            'appsecret'      => $cropid_config['provider_secret'],
            'encodingaeskey' => $cropid_config['encoding_aes_key'],
            'token'          => $cropid_config['token'],
        ];
        $server        = work_weixin()::Server($server_config);
        // 监听所有事件
        $server->on('info', function ($info) use ($platform_corpid) {
            /**
             * 输入
             * @var $info Bag
             */
            $data = $info->toArray();
            wr_log($data, 1);
            return 'success';
        });
        $result = (string)$server->server();
        wr_log($result, 1);
        return $result;
    }

    /**
     *开放平台授权以及授权回调
     * @access public
     * @return string|void
     * @throws Exception
     */
    public function auth()
    {
        $referer = $this->request->header('referer');
        $url     = $this->request->url(true);
        if (!$referer) {
            return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $params          = $this->params;
        $platform_corpid = 'wwfe969e93feed1d0f';
        $suite_id        = 'ww45d16a562d76f34f';
        $config          = ['platform_corpid' => $platform_corpid, 'suite_id' => $suite_id];
        $auth            = work_weixin($config)::Auth();
        $auth_code       = $params['auth_code'] ?? null;
        // 定义表单验证规则
        $callback = $this->request->param('callback', '', null);
        if (!$auth_code) {
            //校验不通过则构造授权链接
            $rules = ['bid' => ['require']];
            $this->validate($params, $rules);
            //确定回调地址
            $callback     = $callback ?: urlencode($this->get_referer_url());
            $link_str     = $this->get_link_str($callback);
            $redirect_uri = url('', [], false, true) . $link_str . 'callback=' . $callback;
            //获取企业微信授权地址
            $auth_url = $auth->get_auth_url($redirect_uri);
            redirect($auth_url);
        }
        //授权成功后 通过auth_code 获得公众号/小程序 信息
        $callback   = urldecode($callback);
        $url_params = tools()::parse_url_params($callback);
        if (empty($url_params['bid'])) {
            error('授权失败,正在跳转中~');
        }
        $bid    = $url_params['bid'];
        $result = $auth->get_permanent_code($auth_code);
        //    "access_token": "access_token",
        //   "refresh_token": "refresh_token",
        //   "description": "",
        //   "error_code": "0",
        //   "expires_in": "86400",
        //   "open_id": "aaa-bbb-ccc",
        //   "refresh_expires_in": "86400",
        //   "scope": "user_info"
        $crop_wechat_auth = new CropWechatAuth();
        $auth_corpid      = $result['auth_corp_info']['corpid'];
        $agent_id         = $result['auth_info']['agent'][0]['agentid'];
        $map              = [
            ['auth_corpid', '=', $auth_corpid],
        ];
        $count            = $crop_wechat_auth->where($map)->count();
        $update_data      = [
            'platform_corpid' => $platform_corpid,
            'suite_id'        => $suite_id,
            'auth_corpid'     => $auth_corpid,
            'info'            => $result,
            'agent_id'        => $agent_id,
            'permanent_code'  => $result['permanent_code'],
        ];
        if ($count > 0) {
            $crop_wechat_auth::update($update_data, $map);
        } else {
            $crop_wechat_auth->save($update_data);
        }
        $db = new ParameterModel();
        $db->update_config('crop_wechat_appid', $auth_corpid, $bid);
        success('授权成功,正在跳转中~', $callback);
    }

    /**
     *获取来源URL
     * @access protected
     * @return array|string
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $bid      = $params['bid'];
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid'])) {
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return string
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }
}