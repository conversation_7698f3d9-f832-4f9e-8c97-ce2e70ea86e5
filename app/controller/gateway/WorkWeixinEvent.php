<?php

namespace app\controller\gateway;


use app\model\CropWechatApp;
use app\model\CropWechatAuth;
use Exception;
use Stoneworld\Wechat\Message;
use Stoneworld\Wechat\Utils\Bag;

class WorkWeixinEvent extends BasicGateway
{
    public function get_suite_id()
    {
        if (request()->isPost()) {
            $xmlInput = file_get_contents('php://input');
            $array    = tools()::xml2arr($xmlInput);
            return $array['ToUserName'];
        }
        $params = $this->params;
        return $params['suite_id'] ?? '';
    }

    /**
     *企业微信事件推送
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params          = $this->params;
        $platform_corpid = $params['platform_corpid'];
        $suite_id        = $params['suite_id'];
        if ($suite_id) {
            $db_crop_wechat_app     = new CropWechatApp();
            $server_config          = $db_crop_wechat_app->get_suite_config($platform_corpid, $suite_id);
            $server_config['appid'] = $this->get_suite_id();
            $server                 = work_weixin()::Server($server_config);
            // 监听所有事件
            $server->on('info', function ($info) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $info Bag
                 */
                $data = $info->toArray();
                return 'success';
            });
            // 监听所有事件
            $server->on('event', function ($event) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $event Bag
                 */
                $data       = $event->toArray();
                $event_name = $data['Event'] ?? '';
                if (!in_array($event_name, ['enter_agent'])) {
                    return Message::make('text')->content('收到【系统事件】:' . json_encode($data, JSON_UNESCAPED_UNICODE));
                }
                return 'success';
            });

            $server->on('message', function ($message) use ($platform_corpid) {
                /**
                 * 输入
                 * @var $message Bag
                 */
                $data = $message->toArray();
                //$message 为用户发送的消息对象，你可以访问请求消息的所有属性，比如：$message->FromUserName 得到发送消息的人的 openid，$message->MsgType 获取消息的类型如 text 或者 image 等
                return Message::make('text')->content('收到【消息内容】:' . json_encode($data, JSON_UNESCAPED_UNICODE));
            });
            return (string)$server->server();
        }
        wr_log('没有$suite_id参数');
        return '';
    }
}