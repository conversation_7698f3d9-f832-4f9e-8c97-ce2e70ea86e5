<?php

namespace app\controller\gateway;

use app\model\PayOrder;
use Exception;

class PayNotify extends BasicGateway
{
    /**
     *支付退款推送
     * @access public
     * @return string
     * @throws Exception
     */
    public function refund()
    {
        $appid = $this->params['appid'];
        $pay   = weixin($appid)::WeChatPay();
        try {
            $notifyInfo = $pay->getRefundNotify();
            logToFile($notifyInfo);
            wr_log('收到微信支付退款回调:' . json_encode($notifyInfo, JSON_UNESCAPED_UNICODE));
            return $pay->getNotifySuccessReply();
        } catch (Exception $e) {
            wr_log(__FUNCTION__ . ' Error: ' . $e->getMessage(), 1);
            return $pay->getNotifyFailedReply($e->getMessage());
        }
    }

    /**
     *支付推送(弃用)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function pay()
    {
        // 获取支付通知
        $appid = $this->params['appid'];
        // 实例支付接口
        $pay = weixin($appid)::WeChatPay();
        try {
            $notifyInfo = $pay->getNotify();
            logToFile($notifyInfo);
        } catch (Exception $e) {
            // 接口失败的处理
            wr_log(__FUNCTION__ . ' Error: ' . $e->getMessage(), 1);
            return $pay->getNotifyFailedReply($e->getMessage());
        }
        $value = tools()::nc_price_fen2yuan($notifyInfo['total_fee']);
        //支付通知数据获取成功
        if ($notifyInfo['result_code'] == 'SUCCESS' && $notifyInfo['return_code'] == 'SUCCESS') {
            // TODO 这里去完成你的订单状态修改操作
            try {
                wr_log('收到微信支付回调成功,订单号:' . $notifyInfo['out_trade_no'] . ',金额:' . $value . '元', 1);
                $db    = new PayOrder();
                $map   = [['bill_number', '=', $notifyInfo['out_trade_no']]];
                $order = $db->where($map)->find();
                if (!$order) {
                    return $pay->getNotifyFailedReply('订单号不存在');
                }
                if ($order['status'] !== 1) {
                    $update_data = [
                        'status'         => 1,
                        'third_trade_no' => $notifyInfo['transaction_id'],
                        'trade_time'     => tools()::format_time($notifyInfo['time_end']),
                        'notify_data'    => json_encode($notifyInfo, JSON_UNESCAPED_UNICODE),
                    ];
                    $db::update($update_data, $map);
                    if (!empty($order['job_attach'])) {
                        $job_data   = $order['job_attach'];
                        $queue_name = $job_data['queue'] ?? '';
                        //推送任务
                        job()->set_queue_name($queue_name)->set_job_name($job_data['class'])->push_job($job_data['data']);
                    }
                }
                return $pay->getNotifySuccessReply();
            } catch (Exception $e) {
                wr_log(__FUNCTION__ . ' Error: ' . $e->getMessage(), 1);
                return $pay->getNotifyFailedReply($e->getMessage());
            }
        }
    }
}