<?php

namespace app\controller\gateway;


use app\model\PayChannel;
use app\model\PayOrder;
use app\model\PayParameter;
use Exception;
use Pays\Pays;
use Throwable;
use WePayV3\Risk;

class Pay extends BasicGateway
{
    public function risk()
    {
        $channel_id     = $this->request->route('channel_id');
        $db_pay_channel = new PayChannel();
        $config         = $db_pay_channel->get_channel_parameter_by_channel_id($channel_id);
        $risk           = Risk::instance($config);
        $data           = $risk->notify();
        if (empty($data['result'])) {
            return json(['code' => 'FAIL', 'message' => 'empty data']);
        }
        wr_log($data);
        $result = json_decode($data['result'], true);
        //{
        //  "record_id": "1169649162023121110023121147556447623",
        //  "sub_mchid": "1658958367",
        //  "company_name": "淳化城关农丰农资经营部",
        //  "punish_plan": "调整收款额度( 单日10000元 )，关闭信用卡支付权限",
        //  "punish_time": "2023-12-11T13:12:36+08:00",
        //  "punish_description": "",
        //  "risk_type": "INACTIVE_TRANSACTION",
        //  "risk_description": "交易停滞"
        //}
        $msg = '商户号:' . $result['sub_mchid'] . "\r\n";
        $msg .= '公司名称:' . $result['company_name'] . "\r\n";
        $msg .= '处罚方案:' . $result['punish_plan'] . "\r\n";
        $msg .= '处罚时间:' . date('Y-m-d H:i:s', strtotime($result['punish_time'])) . "\r\n";
        $msg .= '处罚原因:' . $result['risk_description'] . "\r\n";
        wr_log($msg);
        return json(['code' => 'SUCCESS', 'message' => '']);
    }

    /**
     *支付成功推送
     * @access public
     * @return string|void
     * @throws Exception
     */
    public function notify()
    {
        if ($this->request->isGet()) {
            error('不支持的请求方式!');
        }
        $order_guid        = $this->request->route('guid');
        $db_pay_order      = new PayOrder();
        $begin_create_time = date("Y-m-d H:i:s", strtotime('-1 day'));
        $map               = [
            ['guid', '=', $order_guid],
            ['create_time', '>', $begin_create_time]
        ];
        $order             = $db_pay_order->where($map)->findOrFail();
        $scene             = $order['scene'];
        $db_pay_parameter  = new PayParameter();
        $pay_parameter     = $db_pay_parameter->get_parameter_by_scene_id_or_guid($order['bid'], $order['pay_parameter_guid']);
        $class             = ucfirst($pay_parameter['class']);
        $pay               = new Pays([$class => $pay_parameter['parameter']]);
        $pay_class         = $pay->driver($class)->gateway($scene);
        try {
            $order_data = $pay_class->getNotify();
            logToFile($order_data);
        } catch (Exception $e) {
            // 接口失败的处理
            wr_log(tools()::exception_to_string($e), 1);
            return $pay_class->getNotifyFailedReply();
        }
        //支付通知数据获取成功
        if ($db_pay_order->isSuccess($order_data)) {
            // TODO 这里去完成你的订单状态修改操作 orderStatus 还有可能是 REFUND
            if (isset($order_data['trade_state']) && $order_data['trade_state'] !== 'SUCCESS') {
                wr_log('trade_state not SUCCESS', 1);
                return $pay_class->getNotifyFailedReply();
            }
            try {
                //                wr_log('收到支付回调成功,订单号:' . $order_data['out_trade_no'] . ',金额:' . tools()::ncPriceFen2yuan($order_data['total_fee']) . '元');
                if ($order['status'] !== 1) {
                    $order_data['notify_data'] = $order_data['raw_data'] ?? $order_data;
                    $db_pay_order->update_order_status_to_success($order_data, ['bid' => $order['bid'], 'bill_number' => $order['bill_number']]);
                }
                return $pay_class->getNotifySuccessReply();
            } catch (Exception|Throwable $e) {
                wr_log(tools()::exception_to_string($e, true));
                wr_log('支付回调处理失败: Error: ' . $e->getMessage(), 1);
                return $pay_class->getNotifyFailedReply();
            }
        } elseif ($order_data['trade_state'] == 'REFUND') {
            wr_log($order_data, 1);
            return $pay_class->getNotifyFailedReply();
        }
    }
}