<?php

namespace app\controller\gateway;


use app\common\service\CropWeixinService;
use Exception;
use Stoneworld\Wechat\Message;
use Stoneworld\Wechat\Messages\AIStream;
use Stoneworld\Wechat\Messages\AITemplateCard;

class WorkWeixin extends BasicGateway
{
    /**
     *企业微信事件推送
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {

        $agentid = intval(input('agentid', 0));
        $appid   = input('appid');
        $obj     = CropWeixinService::get_instance($appid, $agentid);
        $server  = $obj->Server();

        // 通用消息监听器 - 记录所有消息的解密后数据
        $server->on('message', function ($message) {
            $messageData = $message->toArray();
            $msgType     = $messageData['MsgType'] ?? '';

            debug_log("=== 企业微信消息解密后数据 ===");
            debug_log("消息类型: " . $msgType);
            debug_log("完整消息数据: " . json_encode($messageData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            // 记录消息详细信息
            $this->logMessageDetails($msgType, $messageData);

            // 检查是否是AI机器人消息
            if (isset($messageData['aibotid'])) {
                debug_log("这是AI机器人消息，根据关键词选择回复类型");
                $userContent = $messageData['text']['content'] ?? '';

                // 记录AI机器人消息详情
                $this->logAIBotEvent($messageData);

                // 根据关键词选择不同的回复类型
                return $this->handleAIMessage($userContent);
            } else {
                debug_log("这是企业微信消息，使用Message回复");
                // 企业微信消息，返回普通Message对象
                return Message::make('text')->content('已经收到了您的企业微信消息');
            }
        });
        // 监听指定类型
        //       $server->on('message', 'text', function ($message) {
        //           return Message::make('text')->content('我们已经收到您发送的文字！');
        //       });
        //       // 监听指定类型
        //       $server->on('message', 'image', function ($message) {
        //           return Message::make('text')->content('我们已经收到您发送的图片！');
        //       });
        // 通用事件监听器 - 记录所有事件的解密后数据
        $server->on('event', function ($event) {
            $eventData = $event->toArray();
            $eventType = $eventData['Event'] ?? '';

            debug_log("=== 企业微信事件解密后数据 ===");
            debug_log("事件类型: " . $eventType);
            debug_log("完整事件数据: " . json_encode($eventData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            // 根据不同事件类型进行详细记录
            $this->logEventDetails($eventType, $eventData);

            return 'success';
        });
        // 只监听指定类型事件
        //        $server->on('event', 'subscribe', function ($event) {
        //            wr_log('收到关注事件，关注者openid: ' . $event['FromUserName'], 1);
        //            return Message::make('text')->content('感谢您关注');
        //        });

        $server->on('event', 'kf_msg_or_event', function ($event) use ($obj) {
            //            wr_log('收到kf_msg_or_event事件', 1);
            //            wr_log($event);
            if (!empty($event['Token'])) {
                $servicer = $obj->Servicer();
                $result   = $servicer->sync_msg(['token' => $event['Token'], 'open_kfid' => $event['OpenKfId']]);
                debug_log($result);
            }

            //            return Message::make('text')->content('感谢您关注');
        });

        $result = $server->server();
        return (string)$result;
    }

    /**
     * 处理AI机器人消息，根据关键词返回不同类型的回复
     * @param string $userContent 用户消息内容
     * @return AIStream|AITemplateCard
     */
    private function handleAIMessage($userContent)
    {
        debug_log("处理AI消息，用户内容: " . $userContent);

        // 检查关键词并返回相应类型的回复
        if (strpos($userContent, '文本通知') !== false || strpos($userContent, '文本卡片') !== false) {
            return $this->getTextNoticeCard($userContent);
        } elseif (strpos($userContent, '图文展示') !== false || strpos($userContent, '图文卡片') !== false) {
            return $this->getNewsNoticeCard($userContent);
        } elseif (strpos($userContent, '按钮交互') !== false || strpos($userContent, '按钮卡片') !== false) {
            return $this->getButtonInteractionCard($userContent);
        } elseif (strpos($userContent, '投票选择') !== false || strpos($userContent, '投票卡片') !== false) {
            return $this->getVoteInteractionCard($userContent);
        } elseif (strpos($userContent, '多项选择') !== false || strpos($userContent, '多选卡片') !== false) {
            return $this->getMultipleInteractionCard($userContent);
        } elseif (strpos($userContent, '卡片') !== false || strpos($userContent, '模板') !== false) {
            return $this->getDefaultTemplateCard($userContent);
        } elseif (strpos($userContent, '流式') !== false || strpos($userContent, '演示') !== false) {
            return $this->getStreamResponse($userContent);
        } elseif (strpos($userContent, '长回复') !== false || strpos($userContent, '详细') !== false) {
            return $this->getLongStreamResponse($userContent);
        } else {
            return $this->getDefaultResponse($userContent);
        }
    }

    /**
     * 获取文本通知模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getTextNoticeCard($userContent)
    {
        debug_log("返回文本通知模板卡片");

        $cardData = [
            'card_type'               => 'text_notice',
            'source'                  => [
                'icon_url'   => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'       => 'AI助手',
                'desc_color' => 1
            ],
            'main_title'              => [
                'title' => '📋 文本通知卡片',
                'desc'  => '您触发了文本通知类型的模板卡片'
            ],
            'emphasis_content'        => [
                'title' => '✨',
                'desc'  => '演示'
            ],
            'quote_area'              => [
                'type'       => 0,
                'title'      => '您的消息',
                'quote_text' => $userContent
            ],
            'sub_title_text'          => '这是一个文本通知模板卡片的演示，支持丰富的内容展示。',
            'horizontal_content_list' => [
                [
                    'keyname' => '消息类型',
                    'value'   => '文本通知'
                ],
                [
                    'keyname' => '时间',
                    'value'   => date('Y-m-d H:i:s')
                ],
                [
                    'keyname' => '状态',
                    'value'   => '✅ 成功'
                ]
            ],
            'jump_list'               => [
                [
                    'type'     => 3,
                    'title'    => '💬 智能回复',
                    'question' => '什么是文本通知卡片？'
                ]
            ],
            'card_action'             => [
                'type' => 1,
                'url'  => 'https://work.weixin.qq.com'
            ],
            'task_id'                 => 'text_notice_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取图文展示模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getNewsNoticeCard($userContent)
    {
        debug_log("返回图文展示模板卡片");

        $cardData = [
            'card_type'               => 'news_notice',
            'source'                  => [
                'icon_url'   => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'       => 'AI助手',
                'desc_color' => 2
            ],
            'main_title'              => [
                'title' => '🖼️ 图文展示卡片',
                'desc'  => '您触发了图文展示类型的模板卡片'
            ],
            'card_image'              => [
                'url'          => 'https://wework.qpic.cn/wwpic/354393_4zpkKXd7SrGMvfg_1629280616/0',
                'aspect_ratio' => 2.25
            ],
            'vertical_content_list'   => [
                [
                    'title' => '🎯 功能特点',
                    'desc'  => '支持图片展示、丰富的内容布局和多种交互方式'
                ]
            ],
            'horizontal_content_list' => [
                [
                    'keyname' => '用户消息',
                    'value'   => substr($userContent, 0, 20) . (strlen($userContent) > 20 ? '...' : '')
                ],
                [
                    'keyname' => '卡片类型',
                    'value'   => '图文展示'
                ]
            ],
            'jump_list'               => [
                [
                    'type'     => 3,
                    'title'    => '📖 了解更多',
                    'question' => '图文卡片有什么优势？'
                ]
            ],
            'card_action'             => [
                'type' => 1,
                'url'  => 'https://work.weixin.qq.com'
            ],
            'task_id'                 => 'news_notice_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取按钮交互模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getButtonInteractionCard($userContent)
    {
        debug_log("返回按钮交互模板卡片");

        $cardData = [
            'card_type'               => 'button_interaction',
            'source'                  => [
                'icon_url'   => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'       => 'AI助手',
                'desc_color' => 3
            ],
            'main_title'              => [
                'title' => '🔘 按钮交互卡片',
                'desc'  => '您触发了按钮交互类型的模板卡片'
            ],
            'sub_title_text'          => '请选择您感兴趣的功能，点击按钮进行交互。',
            'horizontal_content_list' => [
                [
                    'keyname' => '用户输入',
                    'value'   => $userContent
                ],
                [
                    'keyname' => '交互类型',
                    'value'   => '按钮点击'
                ]
            ],
            'button_selection'        => [
                'question_key' => 'user_type',
                'title'        => '您的身份',
                'disable'      => false,
                'option_list'  => [
                    [
                        'id'   => 'developer',
                        'text' => '开发者'
                    ],
                    [
                        'id'   => 'user',
                        'text' => '普通用户'
                    ],
                    [
                        'id'   => 'admin',
                        'text' => '管理员'
                    ]
                ],
                'selected_id'  => 'user'
            ],
            'button_list'             => [
                [
                    'text'  => '👍 点赞',
                    'style' => 1,
                    'key'   => 'like_button'
                ],
                [
                    'text'  => '💬 评论',
                    'style' => 2,
                    'key'   => 'comment_button'
                ],
                [
                    'text'  => '🔄 分享',
                    'style' => 3,
                    'key'   => 'share_button'
                ],
                [
                    'text'  => '⭐ 收藏',
                    'style' => 4,
                    'key'   => 'favorite_button'
                ]
            ],
            'task_id'                 => 'button_interaction_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取流式回复演示
     * @param string $userContent 用户内容
     * @return AIStream
     */
    private function getStreamResponse($userContent)
    {
        debug_log("返回流式回复演示");

        $content = "**流式回复演示开始...**\n\n" .
            "这是一个模拟的流式回复效果。在真实的流式场景中，这段文字会逐步显示。\n\n" .
            "**您的消息：** {$userContent}\n\n" .
            "**流式特点：**\n" .
            "- ⚡ 减少等待时间的感知\n" .
            "- 🎭 增强交互的真实感\n" .
            "- 📱 提升用户参与度\n\n" .
            "**演示结束** ✨";

        return AIStream::make('stream_' . uniqid(), true, $content);
    }

    /**
     * 获取投票选择模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getVoteInteractionCard($userContent)
    {
        debug_log("返回投票选择模板卡片");

        $cardData = [
            'card_type'     => 'vote_interaction',
            'source'        => [
                'icon_url' => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'     => 'AI助手'
            ],
            'main_title'    => [
                'title' => '🗳️ 投票选择卡片',
                'desc'  => '请选择您最喜欢的AI助手功能'
            ],
            'checkbox'      => [
                'question_key' => 'favorite_features',
                'option_list'  => [
                    [
                        'id'   => 'text_reply',
                        'text' => '💬 智能文本回复'
                    ],
                    [
                        'id'         => 'stream_reply',
                        'text'       => '🔄 流式消息回复',
                        'is_checked' => true
                    ],
                    [
                        'id'   => 'template_card',
                        'text' => '🎴 模板卡片消息'
                    ],
                    [
                        'id'   => 'image_process',
                        'text' => '🖼️ 图片处理功能'
                    ]
                ],
                'disable'      => false,
                'mode'         => 1  // 多选模式
            ],
            'submit_button' => [
                'text' => '提交投票',
                'key'  => 'submit_vote'
            ],
            'task_id'       => 'vote_interaction_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取多项选择模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getMultipleInteractionCard($userContent)
    {
        debug_log("返回多项选择模板卡片");

        $cardData = [
            'card_type'     => 'multiple_interaction',
            'source'        => [
                'icon_url' => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'     => 'AI助手'
            ],
            'main_title'    => [
                'title' => '☑️ 多项选择卡片',
                'desc'  => '请选择您需要的服务类型（可多选）'
            ],
            'select_list'   => [
                [
                    'question_key' => 'service_type',
                    'title'        => '服务类型',
                    'selected_id'  => 'consulting',
                    'option_list'  => [
                        [
                            'id'   => 'consulting',
                            'text' => '💡 技术咨询'
                        ],
                        [
                            'id'   => 'development',
                            'text' => '⚙️ 开发服务'
                        ],
                        [
                            'id'   => 'support',
                            'text' => '🛠️ 技术支持'
                        ]
                    ]
                ]
            ],
            'checkbox'      => [
                'question_key' => 'additional_services',
                'option_list'  => [
                    [
                        'id'   => 'training',
                        'text' => '📚 培训服务'
                    ],
                    [
                        'id'   => 'maintenance',
                        'text' => '🔧 维护服务'
                    ],
                    [
                        'id'   => 'documentation',
                        'text' => '📖 文档编写'
                    ]
                ],
                'disable'      => false
            ],
            'submit_button' => [
                'text' => '确认选择',
                'key'  => 'confirm_selection'
            ],
            'task_id'       => 'multiple_interaction_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取默认模板卡片
     * @param string $userContent 用户内容
     * @return AITemplateCard
     */
    private function getDefaultTemplateCard($userContent)
    {
        debug_log("返回默认模板卡片");

        $cardData = [
            'card_type'               => 'text_notice',
            'source'                  => [
                'icon_url'   => 'https://wework.qpic.cn/wwpic/252813_jOfDHtcISzuodLa_1629280209/0',
                'desc'       => 'AI助手',
                'desc_color' => 1
            ],
            'main_title'              => [
                'title' => '🎴 通用模板卡片',
                'desc'  => '您触发了通用模板卡片功能'
            ],
            'quote_area'              => [
                'type'       => 0,
                'title'      => '您的消息',
                'quote_text' => $userContent
            ],
            'sub_title_text'          => '这是一个通用的模板卡片演示。',
            'horizontal_content_list' => [
                [
                    'keyname' => '消息类型',
                    'value'   => '通用卡片'
                ],
                [
                    'keyname' => '时间',
                    'value'   => date('Y-m-d H:i:s')
                ]
            ],
            'task_id'                 => 'default_template_' . uniqid()
        ];

        return AITemplateCard::make($cardData);
    }

    /**
     * 获取长回复流式消息
     * @param string $userContent 用户内容
     * @return AIStream
     */
    private function getLongStreamResponse($userContent)
    {
        debug_log("返回长回复流式消息");

        $content = "**正在分析您的问题...**\n\n" .
            "## 关于长回复的详细说明\n\n" .
            "**什么是长回复？**\n" .
            "长回复是指AI助手提供的详细、全面的回答，通常包含多个段落和丰富的信息。\n\n" .
            "**您的问题：** {$userContent}\n\n" .
            "**应用场景：**\n" .
            "1. **复杂问题解答** - 需要深入分析的技术问题\n" .
            "2. **教学指导** - 提供步骤详细的操作指南\n" .
            "3. **方案建议** - 给出多种解决方案的对比分析\n\n" .
            "**特点：**\n" .
            "- 📝 内容丰富，信息量大\n" .
            "- 🎯 结构清晰，层次分明\n" .
            "- 💡 包含思考过程和推理逻辑\n" .
            "- 🔍 提供具体示例和实践建议\n\n" .
            "希望这个详细的回答能够帮助您理解长回复的概念和应用！";

        return AIStream::make('stream_' . uniqid(), true, $content);
    }

    /**
     * 获取默认回复
     * @param string $userContent 用户内容
     * @return AIStream
     */
    private function getDefaultResponse($userContent)
    {
        debug_log("返回默认回复");

        $content = "我收到了您的消息：「{$userContent}」\n\n" .
            "我正在学习中，您可以尝试发送以下关键词来体验不同功能：\n\n" .
            "**📋 模板卡片类型：**\n" .
            "• **文本通知** - 发送\"文本通知\"或\"文本卡片\"\n" .
            "• **图文展示** - 发送\"图文展示\"或\"图文卡片\"\n" .
            "• **按钮交互** - 发送\"按钮交互\"或\"按钮卡片\"\n" .
            "• **投票选择** - 发送\"投票选择\"或\"投票卡片\"\n" .
            "• **多项选择** - 发送\"多项选择\"或\"多选卡片\"\n" .
            "• **通用卡片** - 发送\"卡片\"或\"模板\"\n\n" .
            "**🔄 流式消息类型：**\n" .
            "• **流式演示** - 发送\"流式\"或\"演示\"\n" .
            "• **长回复** - 发送\"长回复\"或\"详细\"\n\n" .
            "✨ 试试看吧！每种卡片都有不同的交互效果！";

        return AIStream::make('stream_' . uniqid(), true, $content);
    }

    /**
     * 记录消息详细信息
     * @param string $msgType 消息类型
     * @param array $messageData 消息数据
     */
    private function logMessageDetails($msgType, $messageData)
    {
        debug_log("--- 消息详细分析 ---");

        switch ($msgType) {
            case 'text':
                debug_log("【文本消息】");
                debug_log("内容: " . ($messageData['Content'] ?? 'N/A'));
                break;
            case 'image':
                debug_log("【图片消息】");
                debug_log("媒体ID: " . ($messageData['MediaId'] ?? 'N/A'));
                debug_log("图片URL: " . ($messageData['PicUrl'] ?? 'N/A'));
                break;
            case 'voice':
                debug_log("【语音消息】");
                debug_log("媒体ID: " . ($messageData['MediaId'] ?? 'N/A'));
                debug_log("格式: " . ($messageData['Format'] ?? 'N/A'));
                break;
            case 'video':
                debug_log("【视频消息】");
                debug_log("媒体ID: " . ($messageData['MediaId'] ?? 'N/A'));
                debug_log("缩略图媒体ID: " . ($messageData['ThumbMediaId'] ?? 'N/A'));
                break;
            case 'file':
                debug_log("【文件消息】");
                debug_log("媒体ID: " . ($messageData['MediaId'] ?? 'N/A'));
                break;
            case 'location':
                debug_log("【位置消息】");
                debug_log("纬度: " . ($messageData['Location_X'] ?? 'N/A'));
                debug_log("经度: " . ($messageData['Location_Y'] ?? 'N/A'));
                debug_log("缩放级别: " . ($messageData['Scale'] ?? 'N/A'));
                debug_log("位置信息: " . ($messageData['Label'] ?? 'N/A'));
                break;
            default:
                debug_log("未知消息类型: " . $msgType);
                debug_log("消息包含的字段: " . implode(', ', array_keys($messageData)));
                break;
        }

        // 记录通用字段
        debug_log("发送者: " . ($messageData['FromUserName'] ?? 'N/A'));
        debug_log("接收者: " . ($messageData['ToUserName'] ?? 'N/A'));
        debug_log("消息ID: " . ($messageData['MsgId'] ?? 'N/A'));
        debug_log("应用ID: " . ($messageData['AgentID'] ?? 'N/A'));
        debug_log("创建时间: " . ($messageData['CreateTime'] ?? 'N/A'));
    }

    /**
     * 记录事件详细信息
     * @param string $eventType 事件类型
     * @param array $eventData 事件数据
     */
    private function logEventDetails($eventType, $eventData)
    {
        debug_log("--- 事件详细分析 ---");

        switch ($eventType) {
            case 'template_card_event':
                $this->logTemplateCardEvent($eventData);
                break;
            case 'template_card_menu_event':
                $this->logTemplateCardMenuEvent($eventData);
                break;
            case 'subscribe':
                $this->logSubscribeEvent($eventData);
                break;
            case 'unsubscribe':
                $this->logUnsubscribeEvent($eventData);
                break;
            case 'enter_agent':
                $this->logEnterAgentEvent($eventData);
                break;
            case 'location':
                $this->logLocationEvent($eventData);
                break;
            case 'batch_job_result':
                $this->logBatchJobResultEvent($eventData);
                break;
            case 'change_contact':
                $this->logChangeContactEvent($eventData);
                break;
            case 'sys_approval_change':
                $this->logSysApprovalChangeEvent($eventData);
                break;
            default:
                debug_log("未知事件类型: " . $eventType);
                debug_log("事件包含的字段: " . implode(', ', array_keys($eventData)));
                break;
        }
    }

    /**
     * 记录模板卡片事件
     */
    private function logTemplateCardEvent($eventData)
    {
        debug_log("【模板卡片事件】");
        debug_log("事件Key: " . ($eventData['EventKey'] ?? 'N/A'));
        debug_log("任务ID: " . ($eventData['TaskId'] ?? 'N/A'));
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));

        if (isset($eventData['SelectedItems'])) {
            debug_log("用户选择项: " . json_encode($eventData['SelectedItems'], JSON_UNESCAPED_UNICODE));
        }

        if (isset($eventData['ResponseCode'])) {
            debug_log("响应代码: " . $eventData['ResponseCode']);
        }
    }

    /**
     * 记录模板卡片菜单事件
     */
    private function logTemplateCardMenuEvent($eventData)
    {
        debug_log("【模板卡片菜单事件】");
        debug_log("事件Key: " . ($eventData['EventKey'] ?? 'N/A'));
        debug_log("任务ID: " . ($eventData['TaskId'] ?? 'N/A'));
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));
    }

    /**
     * 记录关注事件
     */
    private function logSubscribeEvent($eventData)
    {
        debug_log("【用户关注事件】");
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));
        debug_log("关注时间: " . ($eventData['CreateTime'] ?? 'N/A'));
        debug_log("应用ID: " . ($eventData['AgentID'] ?? 'N/A'));
    }

    /**
     * 记录取消关注事件
     */
    private function logUnsubscribeEvent($eventData)
    {
        debug_log("【用户取消关注事件】");
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));
        debug_log("取消关注时间: " . ($eventData['CreateTime'] ?? 'N/A'));
        debug_log("应用ID: " . ($eventData['AgentID'] ?? 'N/A'));
    }

    /**
     * 记录进入应用事件
     */
    private function logEnterAgentEvent($eventData)
    {
        debug_log("【用户进入应用事件】");
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));
        debug_log("进入时间: " . ($eventData['CreateTime'] ?? 'N/A'));
        debug_log("应用ID: " . ($eventData['AgentID'] ?? 'N/A'));
    }

    /**
     * 记录位置事件
     */
    private function logLocationEvent($eventData)
    {
        debug_log("【位置上报事件】");
        debug_log("用户ID: " . ($eventData['FromUserName'] ?? 'N/A'));
        debug_log("纬度: " . ($eventData['Latitude'] ?? 'N/A'));
        debug_log("经度: " . ($eventData['Longitude'] ?? 'N/A'));
        debug_log("精度: " . ($eventData['Precision'] ?? 'N/A'));
        debug_log("应用ID: " . ($eventData['AgentID'] ?? 'N/A'));
    }

    /**
     * 记录批量任务结果事件
     */
    private function logBatchJobResultEvent($eventData)
    {
        debug_log("【批量任务结果事件】");
        debug_log("任务ID: " . ($eventData['JobId'] ?? 'N/A'));
        debug_log("任务类型: " . ($eventData['JobType'] ?? 'N/A'));
        debug_log("错误码: " . ($eventData['ErrCode'] ?? 'N/A'));
        debug_log("错误信息: " . ($eventData['ErrMsg'] ?? 'N/A'));
    }

    /**
     * 记录通讯录变更事件
     */
    private function logChangeContactEvent($eventData)
    {
        debug_log("【通讯录变更事件】");
        debug_log("变更类型: " . ($eventData['ChangeType'] ?? 'N/A'));

        if (isset($eventData['UserID'])) {
            debug_log("用户ID: " . $eventData['UserID']);
        }
        if (isset($eventData['Name'])) {
            debug_log("姓名: " . $eventData['Name']);
        }
        if (isset($eventData['Department'])) {
            debug_log("部门: " . json_encode($eventData['Department']));
        }
        if (isset($eventData['Id'])) {
            debug_log("部门ID: " . $eventData['Id']);
        }
        if (isset($eventData['ParentId'])) {
            debug_log("父部门ID: " . $eventData['ParentId']);
        }
    }

    /**
     * 记录审批申请状态变化事件
     */
    private function logSysApprovalChangeEvent($eventData)
    {
        debug_log("【审批申请状态变化事件】");
        debug_log("申请单号: " . ($eventData['ThirdNo'] ?? 'N/A'));
        debug_log("审批单号: " . ($eventData['OpenSpNo'] ?? 'N/A'));
        debug_log("申请状态: " . ($eventData['StatusChangeEvent'] ?? 'N/A'));
        debug_log("操作人: " . ($eventData['StatuChangeEvent']['OpUserName'] ?? 'N/A'));
    }

    /**
     * 记录AI机器人相关事件
     */
    private function logAIBotEvent($eventData)
    {
        debug_log("【AI机器人事件】");
        debug_log("机器人ID: " . ($eventData['aibotid'] ?? 'N/A'));
        debug_log("消息ID: " . ($eventData['msgid'] ?? 'N/A'));
        debug_log("聊天类型: " . ($eventData['chattype'] ?? 'N/A'));

        if (isset($eventData['from'])) {
            debug_log("发送者: " . json_encode($eventData['from'], JSON_UNESCAPED_UNICODE));
        }

        if (isset($eventData['text'])) {
            debug_log("文本内容: " . json_encode($eventData['text'], JSON_UNESCAPED_UNICODE));
        }
    }

    public function index_bak()
    {
        $agentid = intval(input('agentid', 0));
        $options = [
            'agentid' => $agentid,
        ];
        $weObj   = new \OpenApi\WorkWeixin($options);
        $weObj->valid(); //注意, 企业号与普通公众号不同，必须打开验证，不要注释掉
        debug_log($weObj->getRevData());
        $type    = $weObj->getRev()->getRevType();
        $content = $weObj->getRev()->getRevContent();
        switch ($type) {
            case \OpenApi\WorkWeixin::MSGTYPE_TEXT:
                //文本回复
                $weObj->text($content)->reply();
                return '';
                break;
            //           case \OpenApi\Workweixinauth::MSGTYPE_EVENT:
            //               break;
            //           case \OpenApi\Workweixinauth::MSGTYPE_IMAGE:
            //               break;
            default:
                //$weObj->text("this type is " . $type)->reply();
        }
        return '';
    }
}
