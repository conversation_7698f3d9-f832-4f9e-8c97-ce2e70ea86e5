<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/4/10
 * Time: 9:07
 */

namespace app\controller\gateway;

use app\model\GoodsPriceMonitor;
use app\model\Parameter;
use app\model\UserBindNote;
use app\model\WeappApplyPrivacyInterfaceNote;
use app\model\WeappSubmitNote;
use app\model\WechatCard;
use app\model\WechatConfig;
use app\model\WechatSceneQrcode;
use app\common\service\NotifyService;
use app\model\WechatSubscribeNote;
use Exception;
use OpenApi\Taobaoke;
use Storage\Storage;
use TencentYoutuyun\Conf;
use TencentYoutuyun\Youtu;
use WeChat\Contracts\BasicPushEvent;
use WeMini\Receive;

/**
 * 微信接口响应
 *
 * @category app
 * @subpackage controllers/wechat
 * <AUTHOR> <<EMAIL>>
 * @date 2015/11/30 20:52
 */
class WechatEvent extends BasicGateway
{

    /**
     * 微信回复对象
     * @var $receive_obj \WeChat\Receive|BasicPushEvent|Receive
     */
    protected $receive_obj;
    protected $openid;
    protected $appid;
    protected $type;
    protected $receive_data;

    /**
     * 接口入口
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        try {
            $this->appid = $this->request->route('appid');
            $appid       = $this->appid;
            /* 创建接口操作对象 */
            $config = weixin($appid)::get_appid_config();
            switch ($this->type = $config['type']) {
                case 1:
                    $receive_obj = weixin($appid)::WeChatReceive();
                    break;
                case 2:
                    $receive_obj = weixin($appid)::WeMiniReceive();
                    break;
                default:
                    return 'not support type';
            }
            $this->receive_obj = $receive_obj;
            /* 全网发布接口测试 */
            if (in_array($appid, ['wx570bc396a51b8ff8', 'wxd101a85aa106f53e'])) {
                return $this->publish_to_all();
            }
            /* 先获取openid,确保后续回复等功能正常使用 */
            $this->openid = $receive_obj->getOpenid();
            // 获取当前推送的所有数据
            /* 记录接口日志start */
            $data               = $receive_obj->getReceive();
            $this->receive_data = $data;
            if (empty($data)) {
                $errmsg = "Ticket event handling failed";
                logToFile($errmsg);
            } else {
                logToFile($data);
            }
            /* 记录接口日志end */
            $type = $receive_obj->getMsgType();
            $type = '_' . strtolower($type);
            if (method_exists($this, $type)) {
                return $this->$type();
            }
            return $this->_default();
        } catch (Exception $e) {
            wr_log('Message:' . $e->getMessage() . ' Code:' . $e->getCode() . ' File:' . $e->getFile() . ' Line:' . $e->getLine(), 1);
            wr_log(tools()::exception_to_string($e, true));
            return 'failed';
        }
    }

    protected function publish_to_all()
    {
        $wechat = $this->receive_obj;
        /* 分别执行对应类型的操作 */
        switch (strtolower($wechat->getMsgType())) {
            case 'text':
                $receive = $wechat->getReceive();
                if ($receive['Content'] === 'TESTCOMPONENT_MSG_TYPE_TEXT') {
                    return $wechat->text('TESTCOMPONENT_MSG_TYPE_TEXT_callback')->reply([], true);
                }
                $key     = str_replace("QUERY_AUTH_CODE:", '', $receive['Content']);
                $service = weixin()::WeOpenService();
                $service->getQueryAuthorizerInfo($key);
                return $wechat->text("{$key}_from_api")->reply([], true);
            case 'event':
                $receive = $wechat->getReceive();
                return $wechat->text("{$receive['Event']}from_callback")->reply([], true);
            default:
                return 'success';
        }
    }

    /**
     * 默认事件处理
     * @return string
     */
    protected function _default()
    {
        return 'success';
        //return $this->wechat->transferCustomerService()->reply();
    }

    public function is_debug()
    {
        return in_array($this->openid, ['o0aNN5Z81L5IE_82rrZD20BDee8c', 'oa9824wrg5Txc-zl1AP4EgElpvfk']);
    }

    /**
     * 图文回复
     * @param integer $news_id
     * @return string
     * @throws Exception
     */
    protected function reply_news($news_id = 0)
    {
        return $this->_default();
    }

    /**
     * EVENT事件处理
     */
    protected function _event()
    {
        $receive_obj = $this->receive_obj;
        $event       = $receive_obj->getRevEvent();
        $eventName   = strtolower($event['event']);
        if (method_exists($this, $eventName)) {
            return $this->$eventName();
        }
        $data = $receive_obj->getReceive();
        wr_log(json_encode($data, JSON_UNESCAPED_UNICODE), 1);
        wr_log($this->openid . ':' . $eventName, 1);
        return $this->_default();
        //return $this->reply_text('OTHER_EVENT:' . $this->openid . ':' . $eventName);
    }

    /**
     * 智能默认回复
     * @param string $msg
     * @return string
     * @throws Exception
     */
    protected function reply_text($msg = '')
    {
        if (empty($msg)) {
            return $this->_default();
        }
        return $this->receive_obj->text($msg)->reply();
    }

    protected function is_platform_appid()
    {
        return $this->appid == 'wx07ce368b922c9841';
    }

    /**
     * 文本事件处理
     */
    protected function _text()
    {
        $content          = $this->receive_obj->getRevContent(); //关键词回复
        $appid            = $this->appid;
        $cache_key_prefix = 'kefu_mp_miniapp:';
        if ($this->type == 1) {
            //公众号消息,转发给小程序
            $cache_key = $cache_key_prefix . $this->openid;
            if ($kefu_mp_miniapp = cache($cache_key)) {
                $mini_appid      = $kefu_mp_miniapp['appid'];
                $mini_openid     = $kefu_mp_miniapp['openid'];
                $component_appid = $kefu_mp_miniapp['component_appid'];
                $app             = weixin($mini_appid, $component_appid)::WeChatCustom();
                $data            = [
                    'touser'  => $mini_openid,
                    'msgtype' => 'text',
                    'text'    => ['content' => $content]
                ];
                $app->send($data);
            }
            //wr_log('公众号消息:' . $content);
            if (strpos(strtolower($content), 'openid') !== false) {
                return $this->reply_text($this->openid);
            }
            if ($this->is_platform_appid()) {
                //淘宝客
                if (strpos($content, '.tb.cn') !== false) {
                    $class  = new Taobaoke();
                    $result = $class->get_url_by_password_content($content);
                    if ($result !== false) {
                        return $this->reply_text($result);
                    } else {
                        return $this->reply_text($class->errmsg);
                    }
                } elseif (strpos($content, '.jd.com') !== false && tools()::is_url($content)) {
                    //京东价格商品监控,用于30天保价
                    $goods_id = tools()::search_str('product/', '.html', $content);
                    $db       = new GoodsPriceMonitor();
                    $map      = [
                        ['goods_id', '=', $goods_id],
                        ['end_time', '>', format_timestamp()],
                    ];
                    $count    = $db->where($map)->count();
                    if ($count) {
                        return $this->reply_text('该商品已经在监控中');
                    }
                    $goods_price = $db->get_jd_price($goods_id);
                    $goods_name  = $db->get_jd_goods_name($goods_id);
                    $data        = [
                        'url'         => $content,
                        'goods_id'    => (int)$goods_id,
                        'name'        => $goods_name,
                        'platform'    => 'jd',
                        'begin_time'  => format_timestamp(),
                        'end_time'    => date('Y-m-d H:i:s', strtotime('+31 day')),
                        'begin_price' => $goods_price,
                        'last_price'  => $goods_price
                    ];
                    $db->save($data);
                    return $this->reply_text('商品' . $goods_name . '价格监控成功,有价格变动31天内会通知你');
                }

                $yi_liu_ban_student_name_list = explode(',', get_system_config('yi_liu_ban_student_name'));
                $is_in_student_yi_liu         = [];
                foreach ($yi_liu_ban_student_name_list as $yi_liu_ban_student_name) {
                    if (strpos($content, $yi_liu_ban_student_name) !== false) {
                        $is_in_student_yi_liu[] = $yi_liu_ban_student_name;
                    }
                }
                if (!empty($is_in_student_yi_liu)) {
                    $is_not_in_student_yi_liu = array_diff($yi_liu_ban_student_name_list, $is_in_student_yi_liu);
                    sort($is_not_in_student_yi_liu);
                    return $this->reply_text(empty($is_not_in_student_yi_liu) ? count($yi_liu_ban_student_name_list) . '名学生都在名单中' : '以下' . count($is_not_in_student_yi_liu) . '名学生未在名单中:' . "\r\n" . implode("\r\n", $is_not_in_student_yi_liu));
                }


                $xiao_er_ban_student_name_list = explode(',', get_system_config('xiao_er_ban_student_name'));
                $is_in_student_xiao_er         = [];
                foreach ($xiao_er_ban_student_name_list as $xiao_er_ban_student_name) {
                    if (strpos($content, $xiao_er_ban_student_name) !== false) {
                        $is_in_student_xiao_er[] = $xiao_er_ban_student_name;
                    }
                }
                if (!empty($is_in_student_xiao_er)) {
                    $is_not_in_student_xiao_er = array_diff($xiao_er_ban_student_name_list, $is_in_student_xiao_er);
                    sort($is_not_in_student_xiao_er);
                    return $this->reply_text(empty($is_not_in_student_xiao_er) ? count($xiao_er_ban_student_name_list) . '名学生都在名单中' : '以下' . count($is_not_in_student_xiao_er) . '名学生未在名单中:' . "\r\n" . implode("\r\n", $is_not_in_student_xiao_er));
                }

            }
        }
        if ($this->type == 2) {
            //小程序逻辑,转发给公众号模板消息
            $db_parameter = new Parameter();
            $bid          = $db_parameter->get_bid_by_appid($appid);
            $data         = [
                'url'         => '',
                'title'       => '您有新的小程序客服消息,请及时回复',
                'name'        => '小程序客服消息',
                'detail'      => $content,
                'user'        => '【小程序】',
                'remark'      => '可直接在本窗口中回复',
                'create_time' => format_timestamp(),
            ];
            notify()->set_key_name(NotifyService::Notice)->limit_business()->set_data($data)->set_bid($bid)->send();
            $db_user_bind_note = new UserBindNote();
            $map               = [['bid', '=', $bid]];
            $bind_note         = $db_user_bind_note->field(['appid', 'openid'])->where($map)->select();
            foreach ($bind_note as $key => $val) {
                cache($cache_key_prefix . $val['openid'], ['component_appid' => $this->receive_obj->config->get('component_appid'), 'openid' => $this->openid, 'appid' => $this->appid], 3600 * 48);
            }
            wr_log('小程序消息:' . $content, 0, $bid);
        }
        return $this->receive_obj->transferCustomerService()->reply();
    }

    /**
     * 图片事件处理
     */
    protected function _image()
    {
        return $this->_default();//暂时不做任何回复
        if (!$this->is_platform_appid()) {
            return $this->_default();//暂时不做任何回复
        }
        try {
            $PicUrl    = $this->receive_data['PicUrl'];
            $appid     = '10135459';
            $secretId  = 'AKIDnww6hQu8tV2SBsRBbpDFG6qSvTJ5aO3b';
            $secretKey = '7GF3hAicVEpESVghXC9xnsyvhIvStCRW';
            $userid    = 'xyf';
            Conf::setAppInfo($appid, $secretId, $secretKey, $userid, conf::API_YOUTU_END_POINT);
            $uploadRet = YouTu::handtrackingclassify_url($PicUrl);
            if (isset($uploadRet['errorcode']) && $uploadRet['errorcode'] === 0) {
                if (empty($uploadRet['items'])) {
                    return $this->reply_text('您的图片没有任何手势~');
                }
                //               $msg = json_encode($uploadRet);
                //               return $this->reply_text($msg);
                $label = $uploadRet['items'][0]['label'];
                if ($label == 'LIKE') {
                    $msg = '亲,感谢您的点赞~';
                } else {
                    $array = [
                        'HEART'   => '比心',
                        'Paper'   => '布',
                        'SCISSOR' => '剪刀',
                        'FIST'    => '拳头',
                        '1'       => '1',
                        'ONE'     => '1',
                        'LOVE'    => '我爱你',
                        'LIKE'    => '点赞',
                        'OK'      => 'OK',
                        'Rock'    => '摇滚手势',
                        '6'       => '6',
                        '8'       => '8',
                        'lift'    => '托',
                    ];
                    $label = $array[$label] ?? $label;
                    $msg   = '您的手势是:' . $label;

                }
                return $this->reply_text($msg);
            } else {
                $msg = '您的手势没有点赞哦~';
                // $msg = json_encode($uploadRet);
                return $this->reply_text($msg);
            }
            return $this->_default();
        } catch (Exception $e) {
            return $this->reply_text($e->getMessage());
        }

    }

    /**
     * 链接事件处理
     */
    protected function _link()
    {
        return $this->_default();
    }

    /**
     * 语音消息处理
     */
    protected function _voice()
    {
        return $this->_default();
    }

    /**
     * 语音消息处理
     */
    protected function _video()
    {
        return $this->_default();
    }

    /**
     * 位置类事情回复
     */
    protected function _location()
    {
        if (!$this->is_platform_appid()) {
            return $this->_default();//暂时不做任何回复
        }
        $vo   = $this->receive_obj->getRevData();
        $url  = "http://apis.map.qq.com/ws/geocoder/v1/?location={$vo['Location_X']},{$vo['Location_Y']}&key=IRTBZ-3OHAW-GKRRP-OI6Q6-7NOQ2-D5FQ5";
        $data = json_decode(file_get_contents($url), true);
        if (!empty($data) && intval($data['status']) === 0) {
            $msg = $data['result']['formatted_addresses']['recommend'];
        } else {
            $msg = "{$vo['Location_X']},{$vo['Location_Y']}";
        }
        return $this->reply_text($msg);
    }

    /**
     * kf_close_session
     */
    protected function kf_close_session()
    {
        return $this->_default();
    }

    /**
     * kf_create_session
     */
    protected function kf_create_session()
    {
        return $this->_default();
    }

    //插入或者更新用户信息

    /**
     * kf_switch_session
     */
    protected function kf_switch_session()
    {
        return $this->_default();
    }


    //TODO 关注与取消关注事件推送

    /**
     * 关注事件
     */
    protected function subscribe()
    {
        $this->insert_or_update_user_info(); //通过异步任务更新会员信息
        $event = $this->receive_obj->getRevEvent();
        if (!empty($event['key']) && stripos($event['key'], 'qrscene_') !== false) {
            //EventKey	事件KEY值，qrscene_为前缀，后面为二维码的参数值
            //Ticket	二维码的ticket，可用来换取二维码图片
            $qr_scene_str = str_replace('qrscene_', '', $event['key']);
            //            wr_log($event, 1);
            return $this->get_scene_id_reply($qr_scene_str);
        }
        return $this->_default();
    }

    protected function get_scene_id_reply($scene_id)
    {
        //        wr_log('场景码扫码: ' . $scene_id);
        $db_wechat_scene_qrcode = new WechatSceneQrcode();
        $map                    = [['scene_id', '=', $scene_id]];
        $scene_qrcode           = $db_wechat_scene_qrcode->where($map)->findOrEmpty();
        if (!$scene_qrcode->isEmpty()) {
            $type = $scene_qrcode['type'];
            switch ($type) {
                case 1:
                    //绑定工号场景
                    $info              = $scene_qrcode['info'];
                    $db_user_bind_note = new UserBindNote();
                    $bind_data         = [
                        'bind_bid'       => $info['bid'],
                        'bind_user_guid' => $info['user_guid'],
                        'appid'          => $this->appid,
                        'openid'         => $this->openid,
                        'way'            => 1, //1 公众号 2 小程序
                        'from'           => 1,//来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程
                    ];
                    $result            = $db_user_bind_note->bind_user($bind_data);
                    if ($result === false) {
                        return $this->reply_text($db_user_bind_note->getError());
                    }
                    $appid        = $this->appid;
                    $db_parameter = new Parameter();
                    $bid          = $db_parameter->get_bid_by_appid($appid);
                    if (empty($bid)) {
                        return $this->_default();
                    }
                    $bind_url      = (string)url('admin/passport/bind_user_success', ['bid' => $bid], false, true);
                    $business_name = $result["business_name"];
                    $user_account  = $result["user_account"];
                    $text          = "<a href='$bind_url'>点击确认绑定($business_name,工号: $user_account)</a>";
                    return $this->reply_text($text);
                default:
                    return $this->_default();
            }
        }
        return $this->_default();
    }

    protected function insert_or_update_user_info($data = array())
    {
        $default_data = ['appid' => $this->appid, 'openid' => $this->openid];
        $data         = array_merge($data, $default_data);
        $data         = ['data' => $data];
        return job()->set_job_name('Weixin@refresh_wechat_user_info')->push_job($data);
    }

    /**
     * 取消关注事件,回复success
     */
    protected function unsubscribe()
    {
        //更新取消关注事件以及关注状态
        $data = [
            'unsubscribe_time' => $this->receive_data['CreateTime'],
            'subscribe'        => 0
        ];
        $this->insert_or_update_user_info($data);
        return $this->_default();
    }

    /**
     * 已关注的用户扫场景二维码的事件
     */
    protected function scan()
    {
        $event = $this->receive_obj->getRevEvent();
        if (!empty($event['key'])) {
            $scene_id = $event['key'];
            return $this->get_scene_id_reply($scene_id);
        }
        return $this->_default();
    }
    //TODO 自定义菜单以及相关基础事件推送

    /**
     * 扫码二维码
     */
    protected function scanqrcode()
    {
        return $this->_default();
    }

    /**
     * 点击菜单拉取消息时的事件推送
     */
    protected function click()
    {
        $event = $this->receive_obj->getRevEvent();
        return $this->_default();
        return $this->reply_text($event['key']);
    }

    /**
     * 点击菜单跳转链接时的事件推送
     */
    protected function view()
    {
        $event = $this->receive_obj->getRevEvent();
        $link  = $event['key'];
        return $this->_default();
        return $this->reply_text($this->openid . ":" . $event['key']);
    }

    protected function trade_manage_remind_access_api()
    {
        wr_log($this->receive_data);
        return $this->_default();
    }

    protected function trade_manage_remind_shipping()
    {
        wr_log($this->receive_data);
        return $this->_default();
    }

    protected function trade_manage_order_settlement()
    {
        wr_log($this->receive_data);
        return $this->_default();
    }

    /**
     * 上报地理位置事件
     */
    protected function location()
    {
        $receive_data = $this->receive_data;
        $data         = [
            'latitude'  => $receive_data['Latitude'],
            'longitude' => $receive_data['Longitude'],
            'precision' => $receive_data['Precision'],
        ];
        $this->insert_or_update_user_info($data);
        return $this->_default();
    }

    /**
     * 扫码推事件的事件推送
     */
    protected function scancode_push()
    {
        return $this->_default();
    }

    /**
     * 扫码推事件且弹出“消息接收中”提示框的事件推送
     */
    protected function scancode_waitmsg()
    {
        $scanInfo = $this->receive_obj->getRev()->getRevScanInfo();
        if (isset($scanInfo['ScanResult'])) {
            return $this->reply_text($scanInfo['ScanResult']);
        }
        return $this->_default();
    }

    /**
     * 弹出系统拍照发图的事件推送
     */
    protected function pic_sysphoto()
    {
        return $this->_default();
    }

    /**
     * 弹出系统拍照发图的事件推送
     */
    protected function pic_photo_or_album()
    {
        return $this->_default();
    }

    /**
     * 弹出微信相册发图器的事件推送
     */
    protected function pic_weixin()
    {
        return $this->_default();
    }
    //TODO 以下是卡券相关事件推送

    /**
     * 弹出微信相册发图器的事件推送
     */
    protected function location_select()
    {
        return $this->_default();
    }

    /**
     * 卡券通过审核
     */
    protected function card_pass_check()
    {
        $receive_data   = $this->receive_data;
        $card_id        = $receive_data['CardId'];
        $db_wechat_card = new WechatCard();
        $map            = [
            ['appid', '=', $this->appid],
            ['card_id', '=', $card_id]
        ];
        $update_data    = ['audit_status' => 1, 'audit_time' => format_timestamp()];
        $db_wechat_card::update($update_data, $map);
        return $this->_default();
    }

    /**
     * 卡券通过审核
     */
    protected function card_not_pass_check()
    {
        $receive_data  = $this->receive_data;
        $card_id       = $receive_data['CardId'];
        $refuse_reason = $receive_data['RefuseReason'];

        $db_wechat_card = new WechatCard();
        $map            = [
            ['appid', '=', $this->appid],
            ['card_id', '=', $card_id]
        ];
        $update_data    = ['audit_status' => -1, 'refuse_reason' => $refuse_reason, 'audit_time' => format_timestamp()];
        $db_wechat_card::update($update_data, $map);
        return $this->_default();
    }

    /**
     * 用户领取卡券
     */
    protected function user_get_card()
    {
        return $this->_default();
    }

    /**
     * 用户领取卡券
     */
    protected function user_gifting_card()
    {
        return $this->_default();
    }

    /**
     * 用户删除卡券
     */
    protected function user_del_card()
    {
        return $this->_default();
    }

    /**
     * 卡券核销事件
     */
    protected function user_consume_card()
    {
        return $this->_default();
    }

    /**
     * 微信买单事件
     */
    protected function user_pay_from_pay_cell()
    {
        return $this->_default();
    }

    /**
     *  进入会员卡事件推送
     */
    protected function user_view_card()
    {
        return $this->_default();
    }

    /**
     * 从卡券进入公众号会话事件推送
     */
    protected function user_enter_session_from_card()
    {
        return $this->_default();
    }

    /**
     * 会员卡内容更新事件
     */
    protected function update_member_card()
    {
        return $this->_default();
    }

    /**
     * 卡券库存报警事件
     */
    protected function card_sku_remind()
    {
        return $this->_default();
    }

    protected function product_spu_audit()
    {
        return $this->_default();
    }

    protected function product_spu_listing()
    {
        return $this->_default();
    }

    protected function product_spu_update()
    {
        return $this->_default();
    }

    /**
     *  券点流水详情事件
     */
    protected function card_pay_order()
    {
        return $this->_default();
    }

    //TODO 门店审核事件推送

    /**
     * 会员卡激活事件推送
     */
    protected function submit_membercard_user_info()
    {
        return $this->_default();
    }

    //TODO 扫一扫事件推送

    /**
     * 门店审核事件推送
     */
    protected function poi_check_notify()
    {
        return $this->_default();
    }

    /**
     * 打开商品主页事件推送
     */
    protected function user_scan_product()
    {
        return $this->_default();
    }

    /**
     * 进入公众号事件推送
     */
    protected function user_scan_product_enter_session()
    {
        return $this->_default();
    }

    /**
     * 地理位置信息异步推送
     */
    protected function user_scan_product_async()
    {
        return $this->_default();
    }

    //TODO 以下是公众号认证相关事件

    /**
     * 商品审核结果推送
     */
    protected function user_scan_product_verify_action()
    {
        return $this->_default();
    }

    /**
     * 认证通过事件
     */
    protected function qualification_verify_success()
    {
        return $this->_default();
    }

    /**
     * 认证失败事件
     */
    protected function qualification_verify_fail()
    {
        return $this->_default();
    }

    /**
     * 名称认证成功事件
     */
    protected function naming_verify_success()
    {
        return $this->_default();
    }

    /**
     * 名称认证失败事件
     */
    protected function naming_verify_fail()
    {
        return $this->_default();
    }

    /**
     * 提醒年审事件
     */
    protected function annual_renew()
    {
        return $this->_default();
    }

    /**
     * 认证失效事件
     */
    protected function verify_expired()
    {
        return $this->_default();
    }

    /**
     * 认证支付成功事件
     */
    protected function wx_verify_pay_succ()
    {
        return $this->_default();
    }

    /**
     * 认证已派单事件
     */
    protected function wx_verify_dispatch()
    {
        return $this->_default();
    }

    protected function wx_verify_refill()
    {
        $info        = $this->get_appid_info();
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        $appid       = $this->appid;
        $msg         = '公众号认证重新提交提醒,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        $msg         .= $data['RefillReason'];
        wr_log($msg, 1);
        return $this->_default();
    }

    //TODO 发送模板消息和群发事件推送

    /**
     * Wi-Fi连网成功
     */
    protected function wificonnected()
    {
        return $this->_default();
    }

    /**
     * 模板消息结果事件推送
     */
    protected function templatesendjobfinish()
    {
        return $this->_default();
    }

    //TODO 小程序相关接口

    /**
     * 群发消息结果事件推送
     */
    protected function masssendjobfinish()
    {
        return $this->_default();
    }

    protected function user_enter_tempsession()
    {
        return $this->_default();
    }

    protected function view_miniprogram()
    {
        return $this->_default();
    }

    protected function user_authorization_revoke()
    {
        return $this->_default();
    }

    protected function user_info_modified()
    {
        return $this->_default();
    }

    protected function user_authorization_cancellation()
    {
        return $this->_default();
    }


//https://developers.weixin.qq.com/miniprogram/dev/framework/security.html#%E5%B0%8F%E7%A8%8B%E5%BA%8F%E8%BF%9D%E8%A7%84%E5%A4%84%E7%BD%9A%E4%BF%A1%E6%81%AF%E9%80%9A%E7%9F%A5
//[ToUserName] => gh_ff95a6ff7e5b
//[FromUserName] => oPyQB5kKKFAb3ipIs19vtuSizJR0
//[CreateTime] => 1701922942
//[MsgType] => event
//[Event] => wxa_punish_event
//[punish_id] => 661611
//[appid] => wxa13587dca8233bfb
//[punish_time] => 1701922942
//[illegal_reason] => 所选类目与小程序运营内容不符合
//[illegal_content] => http://mmbiz.qpic.cn/sz_mmbiz_png/CLwmDXXnhbc9vGqO4oia2dB3aq5C4sgsP09picKNDicbp3iaUaU1bTSY6DvKInIWibxSlxib5T8DPzPHrsUogxf9KRvQ/0?wx_fmt=png
//[detail] => {"warned_type":2,"rectify_deadline":1702182141,"warned_function_names":["被搜索"],"warned_ban_days":[1]}
//[rule_url] => https://developers.weixin.qq.com/miniprogram/product/index.html#_5-7-类目不符行为
//[rule_name] => 《微信小程序平台运营规范》5.行为规范-5.7类目不符行为
//[adjust_guide_url] => https://mp.weixin.qq.com/s/j7BxO03LqlYdi6Qnn-oR3Q
//    [event_type] => 1
    protected function wxa_punish_event()
    {
        $info           = $this->get_appid_info();
        $receive_obj    = $this->receive_obj;
        $receive_data   = $receive_obj->getReceive();
        $appid          = $this->appid;
        $illegal_reason = $receive_data['illegal_reason'] ?? '';
        $rule_name      = $receive_data['rule_name'] ?? '';
        $db_parameter   = new Parameter();
        $bid            = $db_parameter->get_bid_by_appid($appid);
        $content        = $illegal_reason . "\r\n" . $rule_name;
        $content        = str_replace('&nbsp', "\r\n", $content);
        $msg            = '小程序告警,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        $data           = [
            'url'         => $receive_data['rule_url'] ?? '',
            'title'       => '小程序告警',
            'name'        => $msg,
            'detail'      => $content,
            'user'        => $info['principal_name'] . '-' . $info['nick_name'],
            'remark'      => '点击查看详情',
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->set_data($data)->limit_agent()->set_bid($bid)->send();
        return $this->_default();
    }
    //{
    //"ToUserName": "gh_442d0ae0416e",
    //"FromUserName": "oqf664luX3AMZIk5CBodXsy96P2o",
    //"CreateTime": "1677808047",
    //"MsgType": "event",
    //"Event": "wxa_illegal_record",
    //"illegal_record_id": "4_453901",
    //"appid": "wxbb5c9cf7fdff4ca6",
    //"create_time": "1677808047",
    //"illegal_reason": "所选类目与小程序运营内容不符合",
    //"illegal_content": "小程序涉及在线售卖配送食品服务，请补充商家自营-食品饮料类目；&nbsp;如暂无法提供该类目所需资质，请下架相关内容，重新提交审核。",
    //"rule_url": "https:\/\/developers.weixin.qq.com\/miniprogram\/product\/index.html#_5-7-类目不符行为",
    //"rule_name": "《微信小程序平台运营规范》5.行为规范-5.7类目不符行为"
    //}
    //小程序被处罚
    protected function wxa_illegal_record()
    {
        $info            = $this->get_appid_info();
        $receive_obj     = $this->receive_obj;
        $receive_data    = $receive_obj->getReceive();
        $appid           = $this->appid;
        $illegal_content = $receive_data['illegal_content'] ?? '';
        $illegal_reason  = $receive_data['illegal_reason'] ?? '';
        $rule_name       = $receive_data['rule_name'] ?? '';
        $db_parameter    = new Parameter();
        $bid             = $db_parameter->get_bid_by_appid($appid);
        $content         = $illegal_content . "\r\n" . $illegal_reason . "\r\n" . $rule_name;
        $content         = str_replace('&nbsp', "\r\n", $content);
        $msg             = '小程序被处罚,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        $data            = [
            'url'         => $receive_data['rule_url'] ?? '',
            'title'       => '小程序被处罚',
            'name'        => $msg,
            'detail'      => $content,
            'user'        => $info['principal_name'] . '-' . $info['nick_name'],
            'remark'      => '点击查看规则详情',
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->set_data($data)->limit_agent()->set_bid($bid)->send();
        return $this->_default();
    }

    /**
     * 小程序审核通过事件推送
     */
    protected function weapp_audit_success()
    {
        //更新审核记录
        $db          = new WeappSubmitNote();
        $update_data = ['status' => 0, 'audit_time' => format_timestamp()];
        $db->update_last_submit_note($this->appid, $update_data);
        $info = $this->get_appid_info();
        $msg  = '小程序已通过,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        wr_log($msg);
        //send_dingtalk($msg);
        $job_data = ['appid' => $this->appid];
        job()->set_job_name('Weapp@audit_callback')->push_job($job_data);
        return $this->_default();
    }

    protected function wxa_appeal_record()
    {
        $info               = $this->get_appid_info();
        $appid              = $this->appid;
        $msg                = '小程序申诉结果,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        $receive_obj        = $this->receive_obj;
        $data               = $receive_obj->getReceive();
        $appeal_status      = $data['appeal_status'];
        $status_array       = [
            '1' => '正在处理',
            '2' => '申诉通过',
            '3' => '申诉不通过',
            '4' => '申诉已撤销',
        ];
        $appeal_status_text = $status_array[$appeal_status] ?? '状态:' . $appeal_status;
        $punish_description = $data['punish_description'] ?? '未知原因'; //处罚原因
        $content            = $data['material']['illegal_material']['content'] ?? '未知详细原因';//详细原因
        $reason             = $data['material']['appeal_material']['reason'] ?? '未知申诉描述';//申诉描述
        $db_parameter       = new Parameter();
        $bid                = $db_parameter->get_bid_by_appid($appid);
        $msg                = '小程序申诉结果通知,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $this->appid . ')';
        $data               = [
            'url'         => '',
            'title'       => $msg,
            'name'        => '小程序被申诉结果通知',
            'detail'      => $punish_description . $content,
            'user'        => '【申诉结果】' . $appeal_status_text,
            'remark'      => '申诉原因:' . $reason,
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->set_data($data)->limit_agent()->limit_system()->set_bid($bid)->send();
        return $this->_default();
    }

    /**
     * 小程序审核不通过事件推送
     */
    protected function weapp_audit_fail()
    {
        $receive_obj           = $this->receive_obj;
        $appid                 = $this->appid;
        $data                  = $receive_obj->getReceive();
        $db                    = new WeappSubmitNote();
        $reason                = $data['Reason'];
        $update_data           = ['status' => 1, 'reason' => $reason, 'audit_time' => format_timestamp()];
        $screenshot_image_list = [];
        if (isset($data['ScreenShot'])) {
            //兼容下看是否有截图字段
            $screenshot                = $data['ScreenShot'];
            $update_data['screenshot'] = $screenshot;
            $mid_array                 = explode('|', $screenshot);
            $weixin                    = weixin($appid)::WeChatMedia();
            foreach ($mid_array as $mid) {
                $content = $weixin->getMaterial($mid);
                $this->request->__set('_stream', $content);
                $upload                  = Storage::aliyun();
                $file                    = $upload->upload();
                $screenshot_image_list[] = $file['url'];
            }
            $update_data['screenshot_image_list'] = $screenshot_image_list;
        }
        $db->update_last_submit_note($appid, $update_data);
        $info = $this->get_appid_info();
        $msg  = '小程序未通过,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $appid . ')原因:' . $reason;
        wr_log($msg, 1);
        //send_dingtalk($msg);
        $db_parameter        = new Parameter();
        $bid                 = $db_parameter->get_bid_by_appid($appid);
        $info['create_time'] = format_timestamp();
        $info['msg']         = '小程序:' . $info['nick_name'] . '未通过';
        $info['reason']      = str_replace('<br>', "\r\n", $reason);
        $info['url']         = !empty($screenshot_image_list) ? current($screenshot_image_list) : '';
        notify()->set_key_name(NotifyService::AuditFailure)->set_data($info)->set_bid($bid)->send();
        return $this->_default();
    }


    protected function wxa_category_audit()
    {
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        $info        = $this->get_appid_info();
        $appid       = $this->appid;
        $msg         = '小程序类目审核完成,' . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $appid . ')';
        wr_log($msg, 1);
        wr_log($data, 1);
        return $this->_default();
    }

    protected function get_appid_info()
    {
        $db_wechat_config = new WechatConfig();
        $appid            = $this->appid;
        return $db_wechat_config->get_appid_info($appid, ['principal_name', 'nick_name']);
    }

    protected function wxa_privacy_apply()
    {
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        $appid       = $this->appid;
        $info        = $this->get_appid_info();
        $result_info = $data['result_info'];
        $api_name    = $result_info['api_name'];
        $audit_id    = $result_info['audit_id'];
        $audit_time  = format_timestamp($result_info['audit_time']);
        $status      = $result_info['status'];
        $success     = $result_info['status'] == 3;
        $status_text = $success ? '通过' : '驳回';
        $reason      = $result_info['reason'];
        $reason      = is_array($reason) ? json_encode($reason, JSON_UNESCAPED_UNICODE) : $reason;
        $msg         = '小程序接口:' . $api_name . '审核' . $status_text . ',' . (!empty($reason) ? $reason : '') . $info['principal_name'] . '-' . $info['nick_name'] . '(Appid:' . $appid . ')';
        wr_log($msg);

        //更新接口审核状态
        $component_appid                       = weixin()::get_component_appid();
        $map                                   = [
            ['component_appid', '=', $component_appid],
            ['appid', '=', $appid],
            ['audit_id', '=', $audit_id],
        ];
        $update_data                           = [
            'status'     => $status,
            'reason'     => $reason,
            'audit_time' => $audit_time,
        ];
        $db_weapp_apply_privacy_interface_note = new WeappApplyPrivacyInterfaceNote();
        $db_weapp_apply_privacy_interface_note::update($update_data, $map);

        //通知代理商
        $db_parameter = new Parameter();
        $bid          = $db_parameter->get_bid_by_appid($appid);
        if ($bid) {
            $info                = $this->get_appid_info();
            $info['create_time'] = $audit_time;
            $info['msg']         = '小程序【' . $info['nick_name'] . '】接口:' . $api_name . '审核' . $status_text;
            $info['reason']      = $reason;
            notify()->set_key_name($success ? NotifyService::AuditSuccess : NotifyService::AuditFailure)->limit_agent()->set_data($info)->set_bid($bid)->send();
        }
        //自动提交审核
        $map   = [
            ['component_appid', '=', $component_appid],
            ['appid', '=', $appid],
            ['status', 'IN', [0, 2]],
        ];
        $count = $db_weapp_apply_privacy_interface_note->where($map)->count();
        if ($count == 0) {
            //没有审核中 和 审核失败的记录 则代表完全通过, 走自动提交审核逻辑
            $data = ['component_appid' => $component_appid, 'appid' => $appid];
            return job()->set_job_name('Weapp@submit')->push_job($data);
        }
        return $this->_default();
    }

    protected function complaint_callback()
    {
        //        "BussiCallBackInfo": {
        //        "appid": "wxa8f190ef2ec914bb",
        //        "option_type": "2",
        //        "complaint_order_id": "1608767",
        //        "status": "101",
        //        "create_time": "1662628770",
        //        "expire_time": "1662887970",
        //        "type": "621",
        //        "order_id": "payorder@_4200001585202209085408804678",
        //        "phone_number": "18485440916",
        //        "open_id": "og0tG5NosyRZSM9oV1xFfcGClQeo",
        //        "pay_time": "1662627159",
        //        "total_cost": "5800",
        //        "product_name": "[商城购物]",
        //        "out_trade_no": "20220908165234475914"
        //    }
        $receive_obj          = $this->receive_obj;
        $data                 = $receive_obj->getReceive();
        $bussi_call_back_info = $data['BussiCallBackInfo'];
        $complaint_order_id   = $bussi_call_back_info['complaint_order_id'];
        $appid                = $bussi_call_back_info['appid'];
        $pay_time             = $bussi_call_back_info['pay_time'];
        $total_cost           = $bussi_call_back_info['total_cost'];
        $out_trade_no         = $bussi_call_back_info['out_trade_no'];
        $expire_time          = $bussi_call_back_info['expire_time'];
        $product_name         = $bussi_call_back_info['product_name'];
        $msg                  = '投诉单号:' . $complaint_order_id . "\r\n";
        $msg                  .= 'APPID:' . $appid . "\r\n";
        $msg                  .= '商户单号:' . $out_trade_no . "\r\n";
        $msg                  .= '商品名称:' . $product_name . "\r\n";
        $msg                  .= '支付金额:' . tools()::nc_price_fen2yuan($total_cost) . "元\r\n";
        $msg                  .= '支付时间:' . format_timestamp($pay_time) . "\r\n";
        $msg                  .= '过期时间:' . format_timestamp($expire_time) . "\r\n";
        wr_log($msg, 1);
        return $this->_default();
    }


    /**
     * 用户操作订阅通知弹窗 场景：用户在图文等场景内订阅通知的操作
     * @link https://developers.weixin.qq.com/doc/offiaccount/Subscription_Messages/api.html#%E4%BA%8B%E4%BB%B6%E6%8E%A8%E9%80%81
     */
    protected function subscribe_msg_popup_event()
    {
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        $db          = new WechatSubscribeNote();
        $appid       = $this->appid;
        $db->add_note($appid, $data);
        wr_log(json_encode($data, JSON_UNESCAPED_UNICODE), 1);
        return $this->_default();
    }

    /**
     * 用户管理订阅通知 场景：用户在服务通知管理页面做通知管理时的操作
     * @link https://developers.weixin.qq.com/doc/offiaccount/Subscription_Messages/api.html#%E4%BA%8B%E4%BB%B6%E6%8E%A8%E9%80%81
     */
    protected function subscribe_msg_change_event()
    {
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        wr_log(json_encode($data, JSON_UNESCAPED_UNICODE), 1);
        return $this->_default();
    }

    /**
     * 订阅消息发送成功通知
     * @link https://developers.weixin.qq.com/doc/offiaccount/Subscription_Messages/api.html#%E4%BA%8B%E4%BB%B6%E6%8E%A8%E9%80%81
     */
    protected function subscribe_msg_sent_event()
    {
        //{"ToUserName":"gh_5a61b1bc6969","FromUserName":"oFuYf5xqFbOuglQJgwxQFrJUEQnE","CreateTime":"**********","MsgType":"event","Event":"subscribe_msg_sent_event","SubscribeMsgSentEvent":{"List":{"TemplateId":"lp5NKLA1PZFEhBmRM9J777o2RVUtVYo7YbKxFSQbPXw","MsgID":"3609964143513550860","ErrorCode":"0","ErrorStatus":"success"}}}
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        wr_log(json_encode($data, JSON_UNESCAPED_UNICODE));
        return $this->_default();
    }

    /**
     * 记录接口日志
     */
    protected function _logs()
    {
        $receive_obj = $this->receive_obj;
        $data        = $receive_obj->getReceive();
        if (empty($data)) {
            return;
        }
        if (isset($data['Event']) && in_array($data['Event'], array('scancode_push', 'scancode_waitmsg', 'scan'))) {
            $scanInfo = $this->receive_obj->getRev()->getRevScanInfo();
            $data     = array_merge($data, $scanInfo);
        }
        if (isset($data['Event']) && in_array($data['Event'], array('location_select'))) {
            $locationInfo = $this->receive_obj->getRev()->getRevSendGeoInfo();
            $data         = array_merge($data, $locationInfo);
        }
        // $this->formdata->save('wechat_message', array_change_key_case($data, CASE_LOWER));
    }

    //TODO 关键词处理 待完善

    /**
     * 关键字处理
     * @param string $keys 关键字（常规或规格关键字）
     * @param bool $default 是否启用默认模式
     * @return type
     */
    protected function _keys($keys, $default = false)
    {
        return $this->_default();
        return $this->_keys('wechat_keys#keys#default', true);
    }
}
