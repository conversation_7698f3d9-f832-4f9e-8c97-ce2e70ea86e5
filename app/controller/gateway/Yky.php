<?php

namespace app\controller\gateway;

use app\model\PayOrder;
use Exception;

class Yky extends BasicGateway
{
    /**
     *一卡易支付推送
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function pay_notify()
    {
        $params = $this->params;
        $config = isset($params['bid']) ? get_config_by_bid($params['bid']) : config('app.sms_recharge_config');
        $yky    = new \OpenApi\Yky($config);
        if (!$yky->verify_signature()) {
            $data = [
                'status'  => -1,
                'message' => '签名验证失败',
            ];
            wr_log('验证签名失败', 1);
            return json($data);
        }
        //验证签名通过后先更新订单状态
        $notify_data   = json_decode($this->request->param('data', '', null), true);
        $bill_number   = $notify_data['billNumber'];
        $key           = __FUNCTION__ . $bill_number;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        try {
            $db    = new PayOrder();
            $map   = [
                ['bill_number', '=', $bill_number]
            ];
            $order = $db->where($map)->find();
            if (!$order) {
                $data = [
                    'status'  => -1,
                    'message' => '订单记录不存在',
                ];
                $lock_instance->unlock();
                return json($data);
            }
            if ($order['status'] == 1) {
                $data = [
                    'status'  => 0,
                    'message' => '订单已处理',
                ];
                $lock_instance->unlock();
                return json($data);
            }
            //更新订单状态
            $update_data = [
                'status'         => 1,
                'third_trade_no' => $notify_data['consumeBillNumber'],
                'trade_time'     => $notify_data['paidTime'],
                'notify_data'    => json_encode($notify_data, JSON_UNESCAPED_UNICODE)
            ];
            $map         = [
                ['bill_number', '=', $bill_number],
                ['guid', '=', $order['guid']]
            ];
            $db::update($update_data, $map);
            //执行业务
            if (!empty($order['job_attach'])) {
                $job_attach = $order['job_attach'];
                $queue_name = $job_attach['queue'] ?? 'default';
                job()->set_queue_name($queue_name)->set_job_name($job_attach['job'])->push_job($job_attach['data']);
            }
            $data = [
                'status'  => 0,
                'message' => '处理成功'
            ];
            $lock_instance->unlock();
            return json($data);
        } catch (Exception $e) {
            $data = [
                'status'  => -1,
                'message' => $e->getMessage()
            ];
            $lock_instance->unlock();
            return json($data);
        }
    }
}