<?php

namespace app\controller\gateway;

use app\model\DouyinConfig;
use app\model\Parameter as ParameterModel;
use Exception;
use xieyongfa\douyin\Douyin;

class DouyinAuth extends BasicGateway
{
    /**
     *开放平台授权以及授权回调
     * @access public
     * @return string|void
     * @throws Exception
     */
    public function auth()
    {
        $referer = $this->request->header('referer');
        $url     = $this->request->url(true);
        if (!$referer) {
            return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $params      = $this->params;
        $config      = config('douyin');
        $douyinOauth = Douyin::Oauth($config);
        $code        = $params['code'] ?? null;
        // 定义表单验证规则
        $callback = $this->request->param('callback', '', null);
        if (!$code) {
            //校验不通过则构造授权链接
            $rules = ['bid' => ['require']];
            $this->validate($params, $rules);
            //确定回调地址
            $callback     = $callback ?: urlencode($this->get_referer_url());
            $link_str     = $this->get_link_str($callback);
            $redirect_uri = url('', [], false, true) . $link_str . 'callback=' . $callback;
            //获取抖音授权地址
            $redirect_uri = tools()::replace_readonly_to_www($redirect_uri);
            $auth_url     = $douyinOauth->GetOAuthUrl($redirect_uri);
            redirect($auth_url);
        }
        //授权成功后 通过auth_code 获得公众号/小程序 信息
        $callback   = urldecode($callback);
        $url_params = tools()::parse_url_params($callback);
        if (empty($url_params['bid'])) {
            error('授权失败,正在跳转中~');
        }
        $bid    = $url_params['bid'];
        $result = $douyinOauth->GetAccessToken($code);
        //    "access_token": "access_token",
        //   "refresh_token": "refresh_token",
        //   "description": "",
        //   "error_code": "0",
        //   "expires_in": "86400",
        //   "open_id": "aaa-bbb-ccc",
        //   "refresh_expires_in": "86400",
        //   "scope": "user_info"
        $db_douyin_config = new DouyinConfig();
        $open_id          = $result['open_id'];
        $client_key       = $douyinOauth->client_key;
        $map              = ['client_key' => $client_key, 'open_id' => $open_id];
        $count            = $db_douyin_config->where($map)->count();
        $time             = time();
        $update_data      = [
            'access_token'               => $result['access_token'],
            'refresh_token'              => $result['refresh_token'],
            'access_token_expires_time'  => format_timestamp($result['expires_in'] + $time),
            'refresh_token_expires_time' => format_timestamp($result['refresh_expires_in'] + $time),
            'open_id'                    => $result['open_id'],
            'scope'                      => $result['scope'],
        ];
        if ($count > 0) {
            $db_douyin_config::update($update_data, $map);
        } else {
            $update_data['client_key'] = $client_key;
            $db_douyin_config->save($update_data);
        }
        $db = new ParameterModel();
        $db->update_config('douyin_open_id', $open_id, $bid);
        success('授权成功,正在跳转中~', $callback);
    }

    /**
     *获取来源URL
     * @access protected
     * @return array|string
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $bid      = $params['bid'];
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid'])) {
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return string
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }
}
 