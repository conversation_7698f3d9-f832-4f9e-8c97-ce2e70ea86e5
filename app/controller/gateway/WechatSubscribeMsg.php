<?php

namespace app\controller\gateway;


use app\model\WechatSubscribeNote;
use Exception;

class WechatSubscribeMsg extends BasicGateway
{
    /**
     *开放平台授权以及授权回调
     * @access public
     * @return string|void
     * @throws Exception
     */
    public function index()
    {
        $params       = $this->params;
        $bid          = $params['bid'];
        $appid        = $params['appid'];
        $callback_url = $params['callback_url'];
        $template_id  = $params['template_id'] ?? '';
        $openid       = $params['openid'] ?? '';
        $action       = $params['action'] ?? ''; //action	用户点击动作，"confirm"代表用户确认授权，"cancel"代表用户取消授权
        $scene        = $params['scene'] ?? 0;

        // 如果有必要的参数，保存到数据库并跳转到callback_url
        if ($openid && $template_id && $scene && $action) {
            // 创建数据库记录
            $subscribe_status = $action == 'confirm' ? 1 : -1;
            $db               = new WechatSubscribeNote();
            $data             = [
                'guid'             => create_guid(),
                'appid'            => $appid,
                'openid'           => $openid,
                'bid'              => $bid,
                'template_id'      => $template_id,
                'scene'            => $scene,
                'subscribe_status' => $subscribe_status,
            ];
            // 保存数据
            $db->save($data);
            wr_log('subscribe_status=' . $subscribe_status, 1);
            redirect($callback_url);
        } else {
            // 如果没有必要的参数，构造subscribe_url并跳转
            $wechat        = weixin($appid)::WeChatNewtmpl();
            $subscribe_url = $wechat->getSubscribeUrl(request()->url(true));
            redirect($subscribe_url);
        }
    }

    /**
     *获取来源URL
     * @access protected
     * @return array|string
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $bid      = $params['bid'];
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid'])) {
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return string
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }
}
 