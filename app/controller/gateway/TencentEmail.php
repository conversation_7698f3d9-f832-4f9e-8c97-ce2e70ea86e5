<?php

namespace app\controller\gateway;

use app\model\SmsChannel;
use Exception;
use SendSms\SendSms;

class TencentEmail extends BasicGateway
{
    /**
     *腾讯邮箱推送
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function receive()
    {
        $options  = [
            'appid'          => 'wmf003dbf345607b8d',
            'appsecret'      => 'UqbkLng2H6vBrg2dxjBaRcWdg50V64n0QzgaQlZ04TA',
            'token'          => 'D7yew5dGg2JXBvdByH3lmBZ',
            'encodingAesKey' => 'zxEikLNLYjcqkBjkUkGRiGcWwT3HDlDNBhYtEXPtbMT',
        ];
        $emailObj = new \OpenApi\TencentEmail($options);
        $emailObj->valid(); //注意, 企业号与普通公众号不同，必须打开验证，不要注释掉
        $emailObj->getRev();
        $data = $emailObj->getRevData();
        logToFile($data);
        $json = json_encode($data, JSON_UNESCAPED_UNICODE);
        //       wr_log($json);
        $msg_type = $data['MsgType'];
        $arr      = [];
        job()->set_job_name('Email@receive_callback')->push_job($data);
        switch ($msg_type) {
            case 'UnRead':
                $arr = [
                    'title'    => '未读邮件提醒',
                    'keyword1' => '用户: ' . $data['UserID'],
                    'keyword2' => '未读邮件数: ' . $data['UnReadCount'],
                ];
                break;
            case 'Mail':
                $title  = $data['Title'];
                $mobile = '18603047034';
                if (false !== strpos($title, '分流通知') || false !== strpos($title, '12306')) {
                    $db     = new SmsChannel();
                    $config = $db->get_sms_channel_config(4); //平台腾讯云短信通道
                    $sms    = SendSms::tencent($config);
                    $msg    = '监控系统提醒您:已经抢票成功,请及时处理!';
                    $result = $sms->smsSingleSender($mobile, $msg);
                    $msg    = '尊敬的谢永发您好！您收到一封来自抢票通知的邮件,请及时处理！';
                    $result = $sms->smsVoicePromptSender($mobile, $msg);
                }
                $arr = [
                    'title'    => '新邮件提醒',
                    'keyword1' => '发件人: ' . $data['FromUser'],
                    'keyword2' => '标题: ' . $data['Title'],
                ];
                break;
            default:
                error('暂时不支持的格式');
        }
        //send_workweixin_textcard($arr);
    }
}