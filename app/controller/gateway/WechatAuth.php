<?php

namespace app\controller\gateway;

use app\model\FastRegisterWeapp;
use app\model\Parameter as ParameterModel;
use app\model\WechatComponentVerifyTicket;
use app\model\WechatConfig;
use app\common\service\NotifyService;
use app\common\tools\Visitor;
use Exception;
use think\facade\Db;
use think\Model;
use WeOpen\Service;

class WechatAuth extends BasicGateway
{
    protected $appid;
    /**
     *
     * @var Db|Model
     */
    protected $db;
    /**
     *
     * @var Service
     */
    protected $server;

    protected function initialize()
    {
        parent::initialize();
        $this->server = weixin()::WeOpenService();
        $this->db     = new WechatConfig();
    }

    /**
     *处理公众号服务推送的ticket/处理授权相关的操作
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        try {
            $data = $this->server->getComonentTicket();
        } catch (Exception $e) {
            $errmsg = "Ticket event handling failed" . $e->getMessage();
            logToFile($errmsg);
            return $errmsg;
        }
        logToFile($data);
        $auth_info_type = strtolower($data['InfoType']);
        $event_time     = format_timestamp($data['CreateTime'] ?? time());
        if (in_array($auth_info_type, ['unauthorized', 'authorized', 'updateauthorized'])) {
            //更新授权状态 授权 取消授权 更新授权
            $this->appid         = $data['AuthorizerAppid'];
            $auth_info['status'] = ($auth_info_type == 'unauthorized') ? 0 : 1;
            if ($auth_info_type == 'unauthorized') {
                //解除授权则记录解除授权时间
                $auth_info['unauthorized_time'] = $event_time;
            }
            //首次授权 包含 AuthorizationCode 字段 则一并更新授权信息
            if (isset($data['AuthorizationCode'])) {
                $this->update_authorization_info_by_code($data['AuthorizationCode']);
            }
            $component_appid = weixin()::get_component_appid();
            $map             = [
                ['authorizer_appid', '=', $this->appid],
                ['component_appid', '=', $component_appid],
            ];
            $this->db::update($auth_info, $map);
        } elseif (in_array($auth_info_type, ['notify_third_fasteregister', 'notify_third_fastverifybetaapp'])) {
            //快速注册小程序审核事件
            $status               = $data['status'];
            $auth_code            = $data['auth_code'] ?? null;
            $info                 = $data['info'];
            $appid                = $data['appid'] ?? '';
            $status_text_array    = [
                '-1'     => '注册失败',
                '0'      => '注册成功',
                '100001' => '已下发的模板消息法人并未确认且已超时（24h），未进行身份证校验',
                '100002' => '已下发的模板消息法人并未确认且已超时（24h），未进行人脸识别校验',
                '100003' => '已下发的模板消息法人并未确认且已超时（24h）',
                '101'    => '工商数据返回：“企业已注销”',
                '102'    => '工商数据返回：“企业不存在或企业信息未更新”',
                '103'    => '工商数据返回：“企业法定代表人姓名不一致”',
                '104'    => '工商数据返回：“企业法定代表人身份证号码不一致”',
                '105'    => '法定代表人身份证号码，工商数据未更新，请 5-15 个工作日之后尝试',
                '1000'   => '工商数据返回：“企业信息或法定代表人信息不一致”',
                '1001'   => '主体创建小程序数量达到上限',
                '1002'   => '主体违规命中黑名单',
                '1003'   => '管理员绑定账号数量达到上限',
                '1004'   => '管理员违规命中黑名单',
                '1005'   => '管理员手机绑定账号数量达到上限',
                '1006'   => '管理员手机号违规命中黑名单',
                '1007'   => '管理员身份证创建账号数量达到上限',
                '1008'   => '管理员身份证违规命中黑名单',
            ];
            $name                 = $info['name'];
            $code                 = $info['code'];
            $code_type            = $info['code_type'];
            $legal_persona_wechat = $info['legal_persona_wechat'];
            $legal_persona_name   = $info['legal_persona_name'];
            $component_phone      = $info['component_phone'] ?? '';

            $db_fast_register_weapp = new FastRegisterWeapp();
            $map                    = [
                ['name', '=', $name],
                ['code', '=', $code],
                ['code_type', '=', $code_type],
                ['legal_persona_wechat', '=', $legal_persona_wechat],
                ['legal_persona_name', '=', $legal_persona_name],
            ];
            $last_note              = $db_fast_register_weapp->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
            if (!$last_note->isEmpty()) {
                $guid        = $last_note['guid'];
                $update_data = [
                    'examine_status' => $status,
                    'examine_time'   => format_timestamp(),
                ];
                if ($status == 0) {
                    $update_data['status'] = 2;
                }
                if ($auth_code) {
                    $update_data['auth_code'] = $auth_code;
                }
                if ($auth_code) {
                    $update_data['appid'] = $appid;
                }
                $map[] = ['guid', '=', $guid];
                $db_fast_register_weapp::update($update_data, $map);
                if ($auth_code) {
                    $this->appid = $appid;
                    $this->update_authorization_info_by_code($auth_code);
                }
                $bid = $last_note['bid'];
                if ($bid && $appid) {
                    //仅当存在APPID的时候再添加执行回调 否则会导致APPID被清空
                    $this->add_auth_callback($bid, $appid, 2);
                }
                $title  = '注册';
                $title  .= ($status == 0) ? '成功' : '失败';
                $desc   = $status_text_array[$status] ?? $data['msg'];
                $title  .= !in_array($status, [0]) ? $desc : '';
                $detail = '';
                if ($appid) {
                    $detail = 'APPID:' . $appid;
                } elseif ($data['msg']) {
                    $detail = $data['msg'];
                }
                //发送模板消息通知
                $notify_data = [
                    'url'         => '',
                    'title'       => $title,
                    'detail'      => $detail,
                    'user'        => $title,
                    'name'        => '小程序注册-' . $name,
                    'remark'      => '如有疑问请联系客服',
                    'create_time' => format_timestamp(),
                ];
                notify()->set_key_name(NotifyService::Notice)->limit_business()->limit_agent()->set_data($notify_data)->set_bid($last_note['create_bid'])->send();
            }
        } elseif ($auth_info_type == 'component_verify_ticket') {
            $db   = new WechatComponentVerifyTicket();
            $data = ['component_appid' => $data['AppId'], 'component_verify_ticket' => $data['ComponentVerifyTicket']];
            $db->save($data);
        } elseif ($auth_info_type == 'notify_3rd_wxa_wxverify') {
//            [AppId] => wxa0c0db9b3bec99f9
//            [CreateTime] => 1703994404
//    [InfoType] => notify_3rd_wxa_wxverify
//            [appid] => wx81d8d0e5f355dc59
//            [expired] => 1703919479
//    [message] => 小程序appid:    wx81d8d0e5f355dc59
//账号名称:    聚状元靓鸡
//认证时间:    2022年12月30日
//详情:    为确保运营主体经营存续、命名合规且线上服务可用，后续平台将执行认证年审机制。当前账号认证已超过365天，请在2024年01月30日前完成认证年审，到期未完成年审将影响新版本发布和“被搜索”能力。逾期 30 天将影响“被分享”能力，逾期 60 天将影响新用户访问及支付功能。微信认证申请指引：登录账号后台-左侧导航栏-【微信认证】。
            $appid   = $data['appid'];
            $message = $data['message'];
            wr_log($message);
        } elseif ($auth_info_type == 'order_path_audit_result_notify') {
            wr_log('order_path_audit_result_notify');
            wr_log($data);
        } else {
            wr_log('未知的授权事件:' . $auth_info_type, 1);
        }
        return 'success';
    }

    /**
     *更新授权信息
     * @access protected
     * @param string $code
     * @return void
     * @throws Exception
     */
    protected function update_authorization_info_by_code($code)
    {
        //通过code获取授权方信息
        $auth_info = $this->server->getQueryAuthorizerInfo($code);
        $this->update_authorizer_info($auth_info);
        //通过获取公众号基本信息,再次更新资料
        $this->appid = $auth_info['authorizer_appid'];
        $auth_info   = $this->server->getAuthorizerInfo($this->appid);
        $this->update_authorizer_info($auth_info);
        $this->update_account_basicinfo();
    }

    protected function update_account_basicinfo()
    {
        $class           = weixin($this->appid)::WeMiniAccount();
        $info            = $class->getAccountBasicinfo();
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['authorizer_appid', '=', $this->appid],
            ['component_appid', '=', $component_appid],
        ];
        $this->db::update(['account_info' => $info], $map);
    }

    /**
     * 从数据库中 更新公众号或者小程序授权信息
     * @access protected
     * @param array $auth_info
     * @return mixed
     * @throws Exception
     */
    protected function update_authorizer_info($auth_info)
    {
        if (!is_array($auth_info)) {
            return false;
        }
        $appid = $auth_info['authorizer_appid'];
        //如果没有传入 则标志为 已授权
        if (!isset($auth_info['status'])) {
            $auth_info['status'] = 1;
        }
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['authorizer_appid', '=', $appid],
            ['component_appid', '=', $component_appid],
        ];
        $exist           = $this->db->where($map)->count();
        if (!$exist) {
            $auth_info['component_appid'] = $component_appid;
            $this->db->save($auth_info);
        } else {
            $this->db::update($auth_info, $map);
        }
        //清空access_token
        $key = 'wechat_access_token:' . $appid . ':' . $component_appid;
        cache($key, null);
        return true;
    }

    public function auth_success()
    {
        success('授权成功,请手动关闭当前页面', false);
    }

    /**
     * @return void
     * @throws \WeChat\Exceptions\InvalidResponseException
     * @throws \WeChat\Exceptions\LocalCacheException
     */
    public function re_bind_admin()
    {
        $params   = $this->params;
        $taskid   = $params['taskid'];
        $appid    = $params['appid'];
        $instance = weixin($appid)::WeMiniAccount();
        $result   = $instance->componentreBindAdmin($taskid);
        success('管理员修改成功,请手动关闭当前页面', false);
    }

    /**
     * @link https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/2.0/api/Register_Mini_Programs/fast_registration_of_mini_program.html
     * 注册
     */
    public function register_weapp()
    {
        $params             = $this->params;
        $ticket             = $params['ticket'];
        $appid              = $params['appid'];
        $instance           = weixin($appid)::WeChatMini();
        $result             = $instance->fastRegister($ticket);
        $weappid            = $result['appid'];
        $authorization_code = $result['authorization_code'];
        $is_wx_verify_succ  = $result['is_wx_verify_succ'];
        $is_link_succ       = $result['is_link_succ'];
        $this->appid        = $weappid;
        $this->update_authorization_info_by_code($authorization_code);
        $db  = new ParameterModel();
        $bid = $db->get_bid_by_appid($appid);
        if ($bid && $weappid) {
            $this->add_auth_callback($bid, $weappid, 2);
        }
        success('小程序注册成功,请手动关闭当前页面', false);
    }

    /**
     * @param $bid
     * @param $appid
     * @param $auth_type
     * @return void
     * @throws Exception
     */
    protected function add_auth_callback($bid, $appid, $auth_type)
    {
        // 修改商家参数
        $db       = new ParameterModel();
        $key_name = $auth_type == 1 ? 'appid' : 'weappid';
        $db->update_config($key_name, $appid, $bid);
        $job_data = [
            'appid'     => $appid,
            'bid'       => $bid,
            'auth_type' => $auth_type,
        ];
        job()->set_job_name('Weapp@auth_callback')->push_job($job_data);
    }

    /**
     *开放平台授权以及授权回调
     * @access public
     * @return string|void
     * @throws Exception
     */
    public function auth()
    {
        $referer = $this->request->header('referer');
        $url     = $this->request->url(true);
        if (!$referer) {
            return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $params   = $this->params;
        $callback = $this->request->param('callback', '', 'urldecode');
        if (empty($params['auth_code']) || empty($params['callback'])) {
            //校验不通过则构造授权链接
            $rules = [
                //            'bid'       => 'require',
                'auth_type' => ['require', 'in:1,2,3'],
            ];
            $this->validate($params, $rules);
            //确定回调地址
            $callback            = $callback ?: $this->get_referer_url();
            $callback            = $callback ?: (string)url('gateway/wechat_auth/auth_success', [], true, true);
            $url                 = (string)url('', [], true, true);
            $link_str            = $this->get_link_str($url);
            $base_redirect_uri   = $url . $link_str . 'callback=';
            $pc_callback_uri     = urlencode($callback);
            $mobile_callback_uri = urlencode(urlencode(urlencode($callback)));
            //获取微信授权地址,微信浏览器中构造无需扫码的链接
            $auth_url = Visitor::is_wechat_browser() ? $this->server->getAuthRedirectNoScan($base_redirect_uri . $mobile_callback_uri, $params['auth_type']) : $this->server->getAuthRedirect($base_redirect_uri . $pc_callback_uri, $params['auth_type']);
            if ($auth_url === false) {
                error('微信授权失败');
            }
            redirect($auth_url);
        }
        //授权成功后 通过auth_code 获得公众号/小程序 信息
        $code = $params['auth_code'];
        $this->update_authorization_info_by_code($code);
        //更新商家参数 把appid或者 weappid 更新
        $arr = tools()::parse_url_params($callback);
        if (!empty($arr['bid']) && !empty($arr['auth_type'])) {
            $this->add_auth_callback($arr['bid'], $this->appid, $arr['auth_type']);
        }
        success('授权成功,正在跳转中~', $callback);
    }

    /**
     *获取来源地址
     * @access protected
     * @return mixed
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $link_str = $this->get_link_str($referer);
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        if (empty($arr['auth_type'])) {
            $referer .= $link_str . 'auth_type=' . $params['auth_type'];
        }
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid']) && !empty($params['bid'])) {
            $bid     = $params['bid'];
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return string
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return strpos($url, '?') !== false ? '&' : '?';
    }
}