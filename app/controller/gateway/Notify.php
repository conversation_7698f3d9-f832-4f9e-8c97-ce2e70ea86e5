<?php

namespace app\controller\gateway;

use app\model\NotifyLog;
use app\model\Parameter;
use Exception;
use Juhe\Juhe;
use think\App;

class Notify extends BasicGateway
{
    /**
     * 当前通知id (数据库中记录)
     * @var int
     */
    protected int $notify_id = 0;
    protected string $key_name = '';

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->key_name = request()->action();
        $this->save_log();
    }

    public function save_log()
    {
        $params = $this->params;
        $bid    = $params['bid'] ?? '';
        $url    = $this->request->url(true);
        $db     = new NotifyLog();
        switch ($this->key_name) {
            case 'donghua':
                $params = file_get_contents("php://input");
                $params = preg_replace('~[\r\n]+~', '', $params);//防止部分字段存在换行符 导致json不合法 特此兼容
                if (tools()::is_json($params)) {
                    $params = json_decode($params, true);
                }
                break;
            default:
                break;
        }
        $db->key_name = $this->key_name;
        $db->content  = $params;
        $db->url      = $url;
        $db->bid      = $bid;
        if ($params) {
            $db->save();
            $this->notify_id = $db->id; //获取主键的值 即自增ID
            $job_data        = [
                'notify_id' => $this->notify_id,
                'key_name'  => $this->key_name,
                'bid'       => $bid
            ];
            job()->set_job_name('Notify@callback')->push_job($job_data);
        }
    }

    /**
     *创蓝推送
     * @access public
     * @return string
     * @throws Exception
     */
    public function chuanglan()
    {
        $params = $this->params;
        $data   = $params[0];
        $phone  = $data['phone'] ?? '';
        if ($phone) {
            $redirect_link_copy_to_email = get_system_config('redirect_link_copy_to_email');
            $email_address               = explode(',', $redirect_link_copy_to_email);
            $msg                         = '手机号:' . $phone . "<br/>";
            $msg                         .= '地区:' . $data['province'] . ($data['city'] ?? '') . "<br/>";
            $msg                         .= 'IP:' . $data['ip'] . "<br/>";
            $msg                         .= '手机:' . $data['equipment'] . "<br/>";
            $msg                         .= '系统:' . $data['system'] . "<br/>";
            $msg                         .= '链接:' . $data['orginalUrl'] . "<br/>";
            $email_data                  = [
                'account' => 'xyfqq',
                'send_to' => '<EMAIL>',
                'title'   => '【有用户点击短信链接】' . $phone,
                'subject' => $msg,
                'copy_to' => $email_address
            ];
            $send_result                 = send_email($email_data);
        }
        return 'success';
    }

    public function yin_bao()
    {
        return 'success';
    }

    public function lian_tuo_fu()
    {
        return 'success';
    }

    public function kefu()
    {
        wr_log($this->params);
        return 'success';
    }

    public function wechat()
    {
        wr_log($this->params);
        success();
    }

    public function codeup()
    {
        $params = $this->params;
        if ($params) {
            $branch         = str_replace('refs/heads/', '', $params['ref']);  //'refs/heads/master'
            $data['branch'] = $branch;
            $data['notify'] = $params;
            job()->set_job_name('Tools@git_web_hook_callback')->push_job($data);
        }
        success();
    }

    public function mayu()
    {
        $content = file_get_contents('php://input');
        $params  = (array)json_decode($content, true);
        if (empty($params)) {
            error('请求数据为空');
        }
        $branch         = str_replace('refs/heads/', '', $params['ref']);  //'refs/heads/master'
        $data['branch'] = $branch;
        $data['notify'] = $params;
        job()->set_job_name('Tools@git_web_hook_callback')->push_job($data);
        success();
    }

    public function donghua_old()
    {
//        $params = $this->params;
        //以下已经转移成异步处理了,防止接口超时
//        foreach ($params as $key => $val) {
//            //"PatSex":"男",
//            //"PatTel":"13804354490",
//            //"PatAddr":"",
//            //"PatName":"李浩然",
//            //"FeeTotal":"5.54",
//            //"PatCreNo":"220104201011097315"
//            $val['guid'] = $val['guid'] ?? create_guid();
//            job()->set_job_name('Donghua@add_note')->push_job($val);
//        }
        success('处理成功');
    }

    public function donghua()
    {
        $params = $this->params;
        logToFile($params);
        success('处理成功');
    }

    public function je_he_sinopec()
    {
        /**
         * 接收话费\加油卡\流量充值业务 异步通知参数 处理成功请返回：success 或 任意字符串 //重试机制：聚合数据在请求回调地址时，如果无任何内容返回或无响应，会重新发起最多3次，间隔3s左右
         *  http://www.XXXXXX.net/gateway/juhenotify/sinopec?openid=JH876cb5248d348576dc382a4894b320c2&type=[1,2]
         * @access public
         * @return mixed
         * @throws Exception
         */
        $params = $this->params;
        if (empty($params['openid'])) {
            return '';
        }
        $openid = $params['openid'];
        //通过openid查找bid
        $db_parameter = new Parameter();
        $bid          = $db_parameter->get_bid_by_key_name_and_value('juheOpenId', $openid);
        if (empty($bid)) {
            return '';
        }
        $config        = get_config_by_bid($bid);
        $config['bid'] = $bid;
        if (empty($params['type'])) {
            return '';
        }
        $type = (int)$params['type'];
        // 1 油卡 2 话费
        if (!in_array($type, [1, 2])) {
            return '';
        }
        return Juhe::get_instance($config, $type)->notify();
    }
}