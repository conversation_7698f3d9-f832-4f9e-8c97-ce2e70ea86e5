<?php

namespace app\controller\gateway;

use app\common\service\ExpressService;
use app\model\NotifyLog;
use Exception;

class Express extends BasicGateway
{
    /**
     *
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function trace_push()
    {
        $params = $this->params;
        $db     = new NotifyLog();
        $data   = ['type' => 2, 'content' => $params];
        $db->save($data);
        wr_log('收到物流信息推送', 1);
        wr_log($params, 1);
        return json(['code' => 200, 'msg' => 'success']);
    }


    public function auth()
    {
        //        $referer = $this->request->header('referer');
        //        $url     = $this->request->url(true);
        //        if (!$referer) {
        //            return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        //        }
        $params             = $this->params;
        $bid                = $params['bid'];
        $express_channel_id = $params['express_channel_id'];
        $express_instance   = ExpressService::get_instance($bid);
        $express            = $express_instance::get_express_instance($express_channel_id);
        $callback           = $this->request->param('callback', '', null);
        if (empty($callback)) {
            error('callback参数不能为空');
        }
        //        $callback           = $callback ?: urlencode($this->get_referer_url());
        //        $callback           = $callback ?: $referer;
        $params['callback'] = urlencode($callback);
        $redirect_uri       = (string)url('', $params, false, true);
        $result             = $express->auth($params, $redirect_uri);
        $callback           = urldecode($callback);
        $arr                = tools()::parse_url_params($callback);
        if ($result) {
            success('授权成功', $callback);
        } else {
            error($express->getError());
        }
    }

    /**
     *获取来源URL
     * @access protected
     * @return mixed
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $bid      = $params['bid'];
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $link_str = $this->get_link_str($referer);
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid'])) {
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return mixed
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }
}