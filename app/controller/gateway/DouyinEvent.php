<?php

namespace app\controller\gateway;


use Exception;

class DouyinEvent extends BasicGateway
{
    public function check_signature($signature)
    {
        // 获取消息体
        $appSecret = 'e09958098271c3812976b45e4bef8c0f';
        try {
            $wholeStr = '';
            $body     = file_get_contents('php://input');
            $lines    = explode("\r\n", $body);
            $str      = implode('', $lines);
            $wholeStr = $str;
        } catch (Exception $e) {
            error('获取请求内容失败');
        }
        $data = $appSecret . $wholeStr;
        $sign = sha1($data);
        if ($sign != $signature) {
            return false;
        }
        return true;
    }

    /**
     *企业微信事件推送
     * @access public
     * @return \think\response\Json
     * @throws Exception
     */
    public function index()
    {
        $params    = $this->params;
        $request   = $this->request;
        $signature = $request->header('x-douyin-signature');
        if (empty($signature)) {
            error('请求方式不正确');
        }
        if (!$this->check_signature($signature)) {
            wr_log('签名不正确', 1);
            error('签名不正确');
        }
        logToFile($params);
        $msg_id       = $request->header('msg-id');
        $event        = $params['event'];
        $client_key   = $params['client_key'];
        $from_user_id = $params['from_user_id'];
        $content      = $params['content'];
        $log_id       = $params['log_id'];
        $event_id     = $params['event_id'] ?? 0;
        switch ($event) {
            case 'verify_webhook':
                return json(['challenge' => $content['challenge']]);
            default:
                success('ok');
        }
    }
}