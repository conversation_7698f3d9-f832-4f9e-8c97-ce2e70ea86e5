<?php

namespace app\controller\gateway;

use app\model\GateNote;

class Heartbeat extends BasicGateway
{
    public function gate()
    {
        if (!$this->request->isPost()) {
            error('请POST访问');
        }
        $params         = $this->params;
        $bid            = '960df08c-94c5-8f6c-b445-abfca8b99d62';
        $type           = $params['type'] ?? '';
        $data           = [
            'error_num' => 0,
            'error_str' => 'noerror',
        ];
        $on             = false;
        $displayContent = '';
        $speechContent  = '欢迎光临';
        $db             = new GateNote();
        $save_data      = [
            'bid'  => $bid,
            'guid' => create_guid()
        ];
        switch ($type) {
            case 'online':
                //在线识别进入闸门
                $plate_num              = $params['plate_num'] ?? '';
                $plate_color            = $params['plate_color'] ?? '';
                $displayContent         = $plate_num;
                $cam_ip                 = $params['cam_ip'] ?? '';
                $cam_id                 = $params['cam_id'];
                $save_data['cam_id']    = $cam_id;
                $save_data['plate_num'] = $plate_num;
                $plus_cam_id_list       = ['1803001bb9a5'];
                $common_cam_id_list     = ['1803001bba10'];
                $entry_cam_id_list      = ['1803001bb97c', '18030008469e'];
                $plus_coupon_guid       = '("449654a2-f965-11ef-87a2-34735a990690")';
                $common_coupon_guid     = '("1e0adb87-3888-11ec-8433-20040fec86d4","bff59a0a-1d7d-11f0-87a2-34735a990690","d7a35ca0-f964-11ef-87a2-34735a990690","66faa328-0eb4-11f0-87a2-34735a990690")';
                //449654a2-f965-11ef-87a2-34735a990690 超级
                //d7a35ca0-f964-11ef-87a2-34735a990690 普通
                $config     = get_config_by_bid($bid);
                $yky_member = \xieyongfa\yky\Yky::Member($config);
                $member     = $yky_member->Get_MemberInfo($plate_num);
                if ($member === false) {
                    $displayContent .= ',还未注册';
                    $displayContent .= ',未授权';
                    $displayContent .= ',请联系加油站';
                    $speechContent  = '未授权'; //播报未授权
                } else {
                    $member_info = $member['data'][0];
//                    ["MemberGroupName"] => string(12) "四星会员"
//                    ["RegisterTime"] => string(10) "2020-01-13"
//                    ["DurationTime"] => string(12) "永久有效"
                    $member_group_name            = $member_info['MemberGroupName'];
                    $duration_time                = $member_info['DurationTime'];
                    $duration_time                = str_replace('-', '', $duration_time);
                    $card_id                      = $member_info['CardId'];
                    $yky_member_guid              = $member_info['MemberGuid'];
                    $save_data['card_id']         = $card_id;
                    $save_data['yky_member_guid'] = $yky_member_guid;
                    $member_guid                  = $member_info['MemberGuid'];
                    $member_group_name            = str_replace('超级', '', $member_group_name);
                    if ($member_group_name == '中远海事工程Plus') {
                        $member_group_name = '海事工程';
                    }
                    if (in_array($cam_id, $entry_cam_id_list)) {
                        //入口闸门逻辑
                        $displayContent .= ',' . $member_group_name;
                        $displayContent .= ',一车一杆';
                        $displayContent .= ',欢迎光临';
                        $on             = true;
                    } else {
                        //洗车区逻辑 包含普通洗车区 超级会员洗车区
//                        wr_log('车牌:' . $plate_num . ';卡号:' . $card_id . ';级别:' . $member_group_name . ';有效期' . $duration_time);
                        if (in_array($member_group_name, ['会员PLUS', '海事工程', '员工卡', '企业会员', '老卡会员'])) {
                            //超级会员 直接放行
                            //todo 需要判断有效期
                            $displayContent .= ',' . $member_group_name;
                            $displayContent .= ',有效期' . $duration_time;
                            $displayContent .= ',欢迎光临';
                            $on             = true;
                        } else {
                            //其他会员 只是展示 不放行
                            //todo 需要判断有效期
                            $displayContent .= ',普通会员';
                            //继续判断会员下时是否有券
                            $yky_coupon = \xieyongfa\yky\Yky::Coupon($config);
                            //449654a2-f965-11ef-87a2-34735a990690 超级
                            //d7a35ca0-f964-11ef-87a2-34735a990690 普通
                            $coupon_guid = in_array($cam_id, $plus_cam_id_list) ? $plus_coupon_guid : $common_coupon_guid;
//                            $empty_tips       = in_array($cam_id, $plus_cam_id_list) ? '无洗车券' : '无自助券';
                            $empty_tips       = '无洗车券';
                            $post_data        = [
                                'where'   => "1=1 AND Flag=1 AND MemberGuid='" . $member_guid . "' AND CouponGuid IN " . $coupon_guid . " AND EnableCount>0 ",
                                'orderBy' => 'EndDate ASC'
                            ];
                            $coupon_send_list = $yky_coupon->Get_CoupnSendPagedV2($post_data);
                            if ($coupon_send_list === false) {
                                $displayContent .= ',优惠券获取失败';
                                $displayContent .= ',请联系工作人员';
                                $speechContent  = '车辆匹配异常';
                            }
                            if (!empty($coupon_send_list['data'])) {
                                $first_coupon          = $coupon_send_list['data'][0];
                                $coupon_send_note_guid = $first_coupon['Guid'];
                                $sub_data              = [
                                    'couponSendGuid' => $coupon_send_note_guid,
                                    'subCount'       => 1,
                                ];
                                $result                = $yky_coupon->SubCoupon($sub_data);
                                if (isset($result['couponUsedNoteGuid'])) {
                                    //核销成功
                                    $on             = true;
                                    $displayContent .= ',用券洗车';
                                    $displayContent .= ',欢迎光临';
                                } else {
                                    //核销失败
                                    $displayContent .= ',核券失败';
                                    $displayContent .= ',请联系工作人员';
                                }
                                //进行核销
                            } else {
                                //没有优惠券逻辑
                                $displayContent .= ',' . $empty_tips;
                                $displayContent .= ',禁止驶入';
                                $speechContent  = '未授权';
                            }
                        }
                    }
                }
                break;
            case 'heartbeat':
                //心跳
                break;
            default:
                break;
        }
        if ($on) {
            $data['gpio_data'] = [['ionum' => 'io1', 'action' => 'on']];
        }
        if ($displayContent) {
            $save_data['display_content'] = $displayContent;
            $save_data['speech_content']  = $speechContent;
            $db->save($save_data);
            $rs485_data         = $this->generateDataPacket($displayContent, $speechContent);
            $data['rs485_data'] = [
                [
                    'encodetype' => 'hex2string',
                    'data'       => $rs485_data
                ],
            ];
        }
//   {"error_num":0,"error_str":"noerror","gpio_data":[{"ionum":"io1","action":"on"}]}
        return (json($data, 200, ['Content-Length' => strlen(json_encode($data, JSON_UNESCAPED_UNICODE))]));
    }

    public function generateDataPacket($displayContent, $speechContent)
    {
//        wr_log('显示内容' . $displayContent . ';播报内容:' . $speechContent);
        // 示例使用
        // SF:取值为 0 时表示下载到临时区，取值为 1 时表示下载到存储区，频繁修改的内容建议下载到临时区。
        $SF = $this->intToHex(0);
        //GST: 界面显示时间，单位为秒。该参数只有在多界面的显示模板才生效，取值为0 时表示使用模板默认值，取值为 255 表示一直显示当前界面，取值 1~254 表示用户自定义的界面显示时间。
        $GST                 = $this->intToHex(0);
        $displayLines        = explode(',', $displayContent);
        $TEXT_CONTEXT_NUMBER = $this->intToHex(count($displayLines));
        //TEXT_CONTEXT_NUMBER:为文本参数数量，即几行文本。目前版本最大支持 4 行。
        //TEXT_CONTEXT:为文本参数，每个文本参数控制一行，用 0X0D 分开，最后一个文
        //本参数用 0X00 结束，最多 4 个文本参数。文本参数的结构为 LID + DM + DS + DT  + DR + TC[4]+TL +TEXT[...]+0X0D/0X00 各参数取值含义如下。
        // 将显示内容按逗号分割成多行
        $dataPacket = $SF . $GST . $TEXT_CONTEXT_NUMBER;

        // 行号，从0开始
        $lineNumber = 0;

        // 遍历所有行，构建数据包
        foreach ($displayLines as $line) {
            // 编码当前行内容为GBK
            $encodedLine = $this->chineseToGb2312Hex($line); // 语音文本内容
            // 文本参数结构：LID DM DS DT DR TC[4] TL TEXT 0D/00
            $lid = $this->intToHex($lineNumber); // 行号
            //LID:为显示行号。0 表示第 1 行，1 表示第 2 行，以此类推。
            $dm = "01"; // 显示模式
            //DM:为显示模式
            //DM 取值含义
            //编码 描述
            //0x00 立即显示
            //0x01 从右向左移动
            //0x02 从左向右移动
            //0x03 从下向上移动
            //0x04 从上向下移动
            //0x05 向下拉窗
            //0x06 向上拉窗
            //显示屏通信协议-v2.0
            //0x07 向左拉窗
            //0x08 向右拉窗
            //0x0D 逐字显示
            //0x15 连续左移
            $ds = "01"; // 显示速度
            //DS:为显示速度，建议取值为 0；
            $dt = "05"; // 停留时间
            //DT:停留时间，单位为秒，最大为 255 秒；
            $dr = "00"; // 显示次数
            //DR：为显示次数，0 为无限循环显示。
            $findex = "03"; // 字体索引
            //FINDEX:为字体索引。注意:每种主板支持字体不一样
            //FINDEX 取值含义
            //取值 描述
            //0x00 ASCII8
            //0x01 ASCII10
            //0x02 ASCII13
            //0x03 宋体 16
            //0x04 宋体 24
            //0x05 宋体 32
            //0x06 宋体 48
            //0x07 宋体 64
            $flags = "00"; // 显示标志位
            //FLAGS:显示标志位,目前没有用到，作为保留值，固定取值为 0X00
            $tc = "FF000000"; // 文本颜色，RGBA
            //TC：为文本颜色，32 位数据类型，存储结构为 RGBA，R 为红色分量，G 为绿色分量，B 为蓝色分量，A 为透明值目前保留为 0，每个颜色分量占用一个字节即 8 位。
            $tl = $this->intToHex(strlen($encodedLine) / 2); // 文本长度
            //TL:为文本长度。
            $text = $encodedLine; // 文本内容
            //TEXT:为文本内容，最大 32 字节。
            $end = $lineNumber < count($displayLines) - 1 ? "0D" : "00"; // 行尾标记
            // 添加到数据包
            $dataPacket .= $lid . $dm . $ds . $dt . $dr . $findex . $flags . $tc . $tl . $text . $end;
            $lineNumber++;
        }
        // 语音内容
        $vf = "0A"; // 语音标志
        //VF:语音标志，固定取值为 0X0A。
        $voice = $this->chineseToGb2312Hex($speechContent); // 语音文本内容
        //VOICE:语音文本内容。
        $vtl = $this->intToHex(strlen($voice) / 2); // 语音文本长度
        //VTL:语音文本长度。
        $voiceEnd = "00"; // 语音结束标记
        // 添加语音内容到数据包
        $dataPacket .= $vf . $vtl . $voice . $voiceEnd;
        // 计算数据长度
        $dataLength = strlen(str_replace(" ", "", $dataPacket)) / 2;
        $dl         = $this->intToHex($dataLength);
        // 添加数据长度到数据包
        $data  = "0064FFFF6F" . $dl . $dataPacket;
        $crc16 = $this->crc16($data);
        // 返回构建的数据包
        return $data . $crc16;
    }

    public function intToHex($int)
    {
        // 将整数转换为十六进制字符串
        $hex = dechex($int);
        // 确保字符串长度为偶数，不足部分在左侧填充0
        if (strlen($hex) % 2 != 0) {
            $hex = '0' . $hex;
        }
        // 将十六进制字符串转换为小端模式
        $hex = strrev(chunk_split(strrev($hex), 2, ''));
        // 移除可能的额外空格
        $hex = str_replace(' ', '', $hex);
        // 转换为大写字母
        return strtoupper($hex);
    }

// 中文转HEX
    public function chineseToGb2312Hex($text)
    {
        // 将GBK编码的二进制数据转换为十六进制字符串 将十六进制字符串转换为大写
        return strtoupper(bin2hex(iconv('UTF-8', 'GBK', $text)));
    }


    public function crc16($string)
    {
        $string = str_replace(' ', '', $string);
        $pFrame = hex2bin($string);
        $count  = strlen($pFrame);

        $_CRCHi = [
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
            0x00, 0xC1, 0x81, 0x40
        ];

        $_CRCLo = [
            0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7,
            0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E,
            0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09, 0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9,
            0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC,
            0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
            0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32,
            0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D,
            0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A, 0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38,
            0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF,
            0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
            0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1,
            0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4,
            0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F, 0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB,
            0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA,
            0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
            0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0,
            0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97,
            0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C, 0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E,
            0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89,
            0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
            0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83,
            0x41, 0x81, 0x80, 0x40
        ];

        $CRCHi = 0xFF;
        $CRCLo = 0xFF;
        for ($i = 0; $i < $count; $i++) {
            $index = $CRCLo ^ ord($pFrame[$i]);
            $CRCLo = $CRCHi ^ $_CRCHi[$index];
            $CRCHi = $_CRCLo[$index];
        }
        return strtoupper(str_pad(dechex($CRCLo), 2, '0', STR_PAD_LEFT) . str_pad(dechex($CRCHi), 2, '0', STR_PAD_LEFT));
    }
}