<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/5/7
 * Time: 17:41
 */

namespace app\controller\gateway;


use app\model\YlhAccessToken;
use app\common\service\TokenService;
use Exception;
use OpenApi\Yunlianhuiv2;

class <PERSON><PERSON>hui extends BasicGateway
{
    /**
     *首页
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params = $this->params;
        if (!empty($params['error_description'])) {
            error($params['error_description']);
        }
        $token = $params['state'];
        $jwt   = TokenService::decode($token);
        //TODO 解密token之后校验有效期,并且存储起来相关数据
        $ylh = new Yunlianhuiv2();
        // 定义表单验证规则
        $rules = ['code' => 'require'];
        // 验证表单数据
        $this->validate($params, $rules);
        $code   = $params['code'];
        $result = $ylh->get_token_by_code($code);
        if ($result === false) {
            error($ylh->errmsg);
        }
        $bid              = $jwt['bid'];
        $account          = $jwt['account'];
        $user             = $jwt['user'];
        $config           = get_config_by_bid($bid);
        $yky              = new \OpenApi\Yky($config);
        $chain_store_guid = $yky->get_chain_store_guid_by_user_account($user);
        if ($chain_store_guid === false) {
            error($yky->message);
        }
        //存储授权后信息
        $db   = new YlhAccessToken();
        $data = [
            'yky_account'          => $account,
            'yky_user'             => $user,
            'yky_chain_store_guid' => $chain_store_guid,
            'access_token'         => $result['access_token'],
            'token_type'           => $result['token_type'],
            'refresh_token'        => $result['refresh_token'],
            'expired_time'         => date("Y-m-d H:i:s", intval(time() + intval($result['expires_in']))),
            'scope'                => $result['scope'],
        ];
        //先查询是否存在
        $map  = [
            ['yky_chain_store_guid', '=', $chain_store_guid],
            ['bid', '=', $bid],
        ];
        $guid = $db->where($map)->value('guid');
        if (!$guid) {
            //不存在则插入基本信息
            $guid                         = create_guid();
            $data['bid']                  = $bid;
            $data['guid']                 = $guid;
            $data['license_expired_time'] = date('Y-m-d H:i:s', strtotime('+30 day')); //授权过期时间,默认30天试用期
            $db->save($data);
        } else {
            //存在则更新
            $map = [
                ['guid', '=', $guid],
                ['bid', '=', $bid],
            ];
            $db::update($data, $map);
        }
        $map = [
            ['guid', '=', $guid],
            ['bid', '=', $bid],
        ];
        //更新userid
        $user_id = $ylh->get_user_id($result['access_token']);
        if ($user_id) {
            $update_data = [
                'ylh_user_id' => $user_id,
            ];
            $db::update($update_data, $map);
        }
        $key = 'ylh:refresh_token' . $user_id; //缓存刷新token
        cache($key, $result['refresh_token'], 3600 * 24);
        //更新user_info
        $user_info = $ylh->get_user_info();
        if ($user_info) {
            $update_data = [
                'member_id' => $user_info['member_id'],
                'mobile'    => $user_info['mobile'],
                'rcm_id'    => $user_info['rcm_id'],
            ];
            $db::update($update_data, $map);
        }
        //TODO 更新推荐人卡号的信息
        // if ($card_id = $jwt['extend']) {
        if (!empty($user_info['mobile'])) {
            $card_id                  = $user_info['mobile'];
            $recommend_member_card_id = $yky->get_recommend_member_card_id($card_id);
            if ($recommend_member_card_id && ($recommend_member = $yky->get_member_info($recommend_member_card_id))) {
                $upgrade_member_group_name_arr = explode(',', $config['upgrade_member_group_name']);
                if ($recommend_member && in_array($recommend_member['MemberGroupName'], $upgrade_member_group_name_arr)) {
                    //符合则更新一卡易推荐人卡号,用于后续返利
                    $update_data = [
                        'yky_rec_member_card_id' => $recommend_member['CardId'],
                    ];
                    $db::update($update_data, $map);
                }
            }
        }
        success('授权成功');
    }

    /**
     *授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function auth()
    {
        $ylh    = new Yunlianhuiv2();
        $params = $this->params;
        $token  = $params['token'];
        $jwt    = TokenService::decode($token);
        //TODO 解密token之后校验有效期
        $ylh->redirect_authorize_url($token);
    }
}