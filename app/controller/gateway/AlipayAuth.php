<?php

namespace app\controller\gateway;

use app\model\AlipayConfig;
use app\model\PayChannel;
use app\model\PayParameter;
use Exception;
use Pays\Pays;

class AlipayAuth extends BasicGateway
{
    /**
     *开放平台授权以及授权回调
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $referer = $this->request->header('referer');
        $url     = $this->request->url(true);
        if (!$referer) {
            return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $params = $this->params;
        // 定义表单验证规则
        $rules = [
            'app_auth_code' => 'require',
            'app_id'        => 'require',
        ];
        // 验证表单数据
        $result = true;
        try {
            $this->validate($params, $rules);
        } catch (Exception $e) {
            $result = false;
        }
        $db_pay_channel = new PayChannel();
        $callback       = $this->request->param('callback', '', null);
        if (true !== $result) {
            //校验不通过则构造授权链接
            $rules = [
                'bid'                => ['require'],
                'pay_parameter_guid' => ['require'],
            ];
            $this->validate($params, $rules);
            //确定回调地址
            $callback     = $callback ?: urlencode($this->get_referer_url());
            $link_str     = $this->get_link_str($callback);
            $redirect_uri = url('', [], false, true) . $link_str . 'callback=' . $callback;
            //获取支付宝授权地址
            $channel_parameter = $db_pay_channel->get_channel_parameter($params['bid'], $params['pay_parameter_guid']);
            $app_id            = $channel_parameter['app_id'];
            $data              = [
                'platformCode' => 'O',
                'taskType'     => 'INTERFACE_AUTH',
                'agentOpParam' => [

                    'isvAppId'    => $app_id,
                    'appTypes'    => ['MOBILEAPP', 'WEBAPP', 'PUBLICAPP', 'TINYAPP', 'BASEAPP'],
                    'redirectUri' => $redirect_uri,
                    'state'       => 'test'
                ]
            ];
            $auth_url          = 'https://b.alipay.com/page/message/tasksDetail?bizData=' . urlencode(json_encode($data));
            //            $base_url = 'https://openauth.alipay.com/oauth2/appToAppBatchAuth.htm?app_id=' . $channel_parameter['app_id'] . '&application_type=MOBILEAPP,WEBAPP,PUBLICAPP,TINYAPP,ARAPP&redirect_uri=';
            //            $auth_url = $base_url . urlencode($redirect_uri);
            redirect($auth_url);
        }
        //授权成功后 通过auth_code 获得公众号/小程序 信息
        $app_id        = $params['app_id'];
        $app_auth_code = $params['app_auth_code'];
        $callback      = urldecode($callback);
        $arr           = tools()::parse_url_params($callback);
        if (empty($arr['bid'] || empty($arr['pay_parameter_guid']))) {
            error('授权失败,正在跳转中~');
        }
        $bid                = $arr['bid'];
        $pay_parameter_guid = $arr['pay_parameter_guid'];
        $channel_parameter  = $db_pay_channel->get_channel_parameter($bid, $pay_parameter_guid);
        $config             = [
            // 支付宝支付参数
            'alipay' => $channel_parameter
        ];
        // 实例支付对象
        $err_msg = '';
        try {
            $pay    = new Pays($config);
            $data   = [
                'grant_type' => 'authorization_code',
                'code'       => $app_auth_code,
            ];
            $result = $pay->driver('alipay')->gateway('auth')->apply($data);
            if (!empty($result['tokens'])) {
                foreach ($result['tokens'] as $key => $val) {
                    $map            = [
                        'app_id'      => $app_id,
                        'auth_app_id' => $val['auth_app_id']
                    ];
                    $db_alipay_auth = new AlipayConfig();
                    $count          = $db_alipay_auth->where($map)->count();
                    $update_data    = [
                        'app_auth_token'    => $val['app_auth_token'],
                        'app_refresh_token' => $val['app_refresh_token'],
                        'expires_in'        => $val['expires_in'],
                        're_expires_in'     => $val['re_expires_in'],
                        'user_id'           => $val['user_id'],
                        'auth_app_id'       => $val['auth_app_id'],
                    ];
                    if ($count > 0) {
                        //更新
                        $db_alipay_auth::update($update_data, $map);
                    } else {
                        $update_data['app_id'] = $app_id;
                        $db_alipay_auth->save($update_data);
                    }
                    //更新支付参数
                    $db_pay_parameter   = new PayParameter();
                    $map                = [
                        ['bid', '=', $bid],
                        ['guid', '=', $pay_parameter_guid],
                    ];
                    $pay_parameter_info = $db_pay_parameter->where($map)->find();
                    $pay_parameter      = [
                        'pid'            => $val['user_id'],
                        'app_auth_token' => $val['app_auth_token'],
                        'debug'          => false,
                    ];
                    $update_data        = ['parameter' => $pay_parameter];
                    $pay_parameter_info::update($update_data, $map);
                }
            }
        } catch (Exception $e) {
            $err_msg = $e->getMessage();
        }
        if ($err_msg) {
            wr_log($err_msg);
            success('授权失败:' . $err_msg, $callback);
        } else {
            success('授权成功,正在跳转中~', $callback);
        }
    }

    /**
     *获取来源URL
     * @access protected
     * @return mixed
     * @throws Exception
     */
    protected function get_referer_url()
    {
        $params   = $this->params;
        $bid      = $params['bid'];
        $referer  = $this->request->header('referer') ?: $this->request->domain();
        $link_str = $this->get_link_str($referer);
        $arr      = tools()::parse_url_params($referer);//将url解析成参数数组
        if (empty($arr['pay_parameter_guid'])) {
            $referer .= $link_str . 'pay_parameter_guid=' . $params['pay_parameter_guid'];
        }
        $link_str = $this->get_link_str($referer);
        if (empty($arr['bid'])) {
            $referer .= $link_str . 'bid=' . $bid;
        }
        return $referer;
    }

    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return mixed
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }
}
 