<?php

namespace app\controller\gateway;

use Exception;
use Supervisor\Supervisor;
use think\facade\Console;
use Throwable;

class Monitor extends BasicGateway
{
    /**
     *web心跳
     * @access public
     * @return string
     */
    public function heartbeat(): string
    {
        $cache_key_list = [
            'last_crontab_time' => ['max_time' => '120'],
            'last_queue_time'   => ['max_time' => '120', 'console' => ['command' => 'queue:work', 'parameters' => ['--once=10', '--sleep=0']]]
        ];
        $error          = 0;
        $key_prefix     = tools()::get_lan_ip() . ':' . config('app.app_name') . ':';
        foreach ($cache_key_list as $key => $val) {
            $cache_key         = $key_prefix . $key;
            $last_crontab_time = (int)cache($cache_key);//获取定时任务最后执行时间
            $diff              = time() - $last_crontab_time;
            if ($diff > $val['max_time']) {
                $msg = $key . '的任务已经有' . tools()::sec2time($diff) . '未执行,请及时处理!';
                if (!empty($val['console'])) {
                    try {
                        Console::call($val['console']['command'], $val['console']['parameters'] ?? []);
                    } catch (Exception|Throwable $e) {
                    }
                }
                send_ding_talk($msg);
                ++$error;
            }
        }
        try {
            //$process_list = ['crontab', 'socket_io', 'queue_monitor'];
            $host_name_array = ['127.0.0.1', config('app.slave_lan_ip')];
            $process_list    = explode(',', get_system_config('auto_restart_process'));
            foreach ($host_name_array as $host_name) {
                $service     = new Supervisor(['hostname' => $host_name]);
                $all_process = $service->getAllProcesses();
                if ($all_process !== false) {
                    foreach ($all_process as $process) {
                        $process_group   = $process->getGroup();
                        $process_payload = $process->getPayload();
                        if (!$process->isRunning() && in_array($process_group, $process_list)) {
                            $stop_time = $process_payload['stop'];
                            if ((time() - $stop_time) > 60) {
                                $host_name = $host_name == '127.0.0.1' ? tools()::get_lan_ip() : $host_name;
                                $msg       = '服务器IP:' . $host_name . '进程【' . $process_group . '】已经被停止,请及时处理!';
                                send_ding_talk($msg);
                                $service->startProcess($process_group);
                            }
                        }
                    }
                }
            }

        } catch (Exception|Throwable $e) {
        }
        return $error > 0 ? 'FAIL' : 'SUCCESS';
    }

    /**
     *web心跳
     * @access public
     * @return string
     */
    public function ping(): string
    {
        return 'pong';
    }

    /**
     *定时任务执行情况监控
     * @access public
     * @return string
     * @throws Exception
     */
    public function crontab(): string
    {
        $params = $this->params;
        if (empty($params['key'])) {
            return 'FAIL';
        }
        $cache_key         = $params['key'];
        $max_diff          = $params['diff'] ?? 600;//默认超过10分钟告警
        $max_diff          = max($max_diff, 60);//最低仅允许1分钟告警
        $last_crontab_time = (int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':' . $cache_key);//获取定时任务最后执行时间
        $diff              = time() - $last_crontab_time;
        if ($diff > $max_diff) {
            $msg = $cache_key . '任务已经有' . tools()::sec2time($diff) . '未执行,请及时处理!';
            send_ding_talk($msg);
            return 'FAIL';
        }
        return 'SUCCESS';
    }
}