<?php

namespace app\controller\gateway;

use app\model\SmsCallback;
use Exception;
use SendSms\SendSms;

class Sms extends BasicGateway
{
    /**
     *短信回执
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function callback()
    {
        $db          = new SmsCallback();
        $params      = $this->params;
        $type        = $params['type'] ?? '';
        $channel     = $params['channel'] ?? 'tencent';
        $sms         = SendSms::driver($channel);
        $push_params = $this->request->post();
        $result      = json_encode($push_params, JSON_UNESCAPED_UNICODE);
        $data        = [
            'guid'    => create_guid(),
            'type'    => $type,
            'channel' => $channel,
            'result'  => $result,
        ];
        $db->save($data);
        $callback = $sms->callback($push_params, $type);
        //        wr_log($result);
        return is_array($callback) ? json($callback) : $callback;
    }
}