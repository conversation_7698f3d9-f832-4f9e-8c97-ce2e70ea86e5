<?php
declare (strict_types=1);

namespace app\controller\openapi\v1;


use OpenApi\SubMerchant;

class Merchant extends BasicOpenapi
{
    public function change_rate()
    {
        error('暂时不支持在线修改费率,请联系在线客服!');
        $params     = $this->params;
        $mch_id     = $params['mch_id'];
        $sub_mch_id = $params['sub_mch_id'];
        $rate       = $params['rate'];
        $config     = ['merchant_code' => $mch_id];
        $sub        = new SubMerchant($config);
        $result     = $sub->crate_benchmarking($sub_mch_id, $rate);
        $msg        = '服务商商户号:' . $mch_id . '子商户号:' . $sub_mch_id . '费率修改成功,调整后费率:' . $rate;
        wr_log($msg);
        success('修改成功');
    }

    public function query_rate()
    {
        $params     = $this->params;
        $mch_id     = $params['mch_id'];
        $sub_mch_id = $params['sub_mch_id'];
        $config     = ['merchant_code' => $mch_id];
        $sub        = new SubMerchant($config);
        $data       = ['merchant_code' => $sub_mch_id];
        $result     = $sub->crate_benchmarking_getapplylist($data);
        if (!empty($result['applylist'])) {
            $apply_id = $result['applylist'][0]['apply_id'];
            $result   = $sub->get_rate_detail($apply_id);
            $rate     = $result['rate'];
        } else {
            $result = $sub->get_merchant_info_api($sub_mch_id);
            $rate   = $result['max'];
        }
        $msg = '服务商商户号:' . $mch_id . '子商户号:' . $sub_mch_id . '当前费率:' . $rate;
        wr_log($msg);
        $result = [
            'rate'       => $rate,
            'mch_id'     => $mch_id,
            'sub_mch_id' => $sub_mch_id,
        ];
        result($result);
    }
}