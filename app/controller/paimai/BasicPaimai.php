<?php

namespace app\controller\paimai;

use app\BaseController;
use app\model\Member as MemberModel;
use Exception;

class BasicPaimai extends BaseController
{
    /**
     * 魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return string
     * @throws Exception
     */
    public function __call($name, $arguments)
    {
        return $this->fetch(strtolower($this->request->action()));
    }

    /**
     * [__construct description]
     * <AUTHOR>
     * @dateTime 2018-05-21 20:47:02
     */
    protected function initialize()
    {
        $db_member   = new MemberModel();
        $map         = ['bid' => $this->get_bid(), 'guid' => $this->get_member_guid()];
        $member_info = $db_member->get_member_info($map);
        $this->assign('member_info', $member_info);
    }
}
