<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\paimai;


use app\model\Article;
use app\model\AuctionActivity;
use app\model\AuctionGoods;
use app\model\AuctionGoodsOrder;
use app\model\GoodsCategoryArticle;
use app\model\Member;
use Exception;

class Index extends BasicPaimai
{
    /**
     * 首页
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params              = $this->params;
        $db_auction_activity = new AuctionActivity();
        $now_time            = format_timestamp();
        $join                = [
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
        ];
        $field               = [
            'aa.guid'                   => 'activity_guid',
            'aa.title',
            'aa.pic',
            'aa.amount',
            'aa.create_time',
            'aa.offer_count',
            'aa.begin_time',
            'aa.end_time',
            'gc.name'                   => 'category_name',
            'gc.guid'                   => 'category_guid',
            'aa.number'                 => 'number',
            'CONCAT(gc.name,aa.number)' => 'goods_category_with_date',
        ];
        $map                 = [
            ['aa.bid', '=', $this->get_bid()],
            ['aa.status', '=', '1']
        ];
        $category_guid       = '';
        $status              = 0;
        if (!empty($params['category_guid'])) {
            $category_guid = $params['category_guid'];
            $map[]         = ['gc.guid', '=', $category_guid];
        }
        if (isset($params['status'])) {
            $status = $params['status'];
            switch ($status) {
                case 1:
                    $map[] = [
                        ['begin_time', '>', $now_time]
                    ];
                    break;
                case 2:
                    $map[] = [
                        ['begin_time', '<', $now_time],
                        ['end_time', '>', $now_time],
                    ];
                    break;
                case 3:
                    $map[] = [
                        ['end_time', '<', $now_time]
                    ];
                    break;
                default:
                    break;
            }
        }
        $list                = $db_auction_activity->alias('aa')->order('end_time desc')->where($map)->join($join)->field($field)->paginate($this->get_paginate_config(6));
        $field               = [
            'gc.guid' => 'category_guid',
            'gc.name' => 'category_name',
        ];
        $map                 = [
            ['aa.bid', '=', $this->get_bid()],
            ['aa.status', '=', '1']
        ];
        $goods_category_list = $db_auction_activity->alias('aa')->order('end_time desc')->where($map)->join($join)->field($field)->select()->toArray();
        $goods_category_list = array_unique($goods_category_list, SORT_REGULAR);
        $this->assign([
            'list'                => $list,
            'goods_category_list' => $goods_category_list,
            'title'               => "首页",
            'category_guid'       => $category_guid,
            'status'              => $status,
        ]);
        return $this->fetch();
    }

    /**
     * 详情
     * @access public
     * @return string
     * @throws Exception
     */
    public function detail()
    {
        $params              = $this->params;
        $guid                = $params['guid'];
        $db_auction_activity = new AuctionActivity();
        $map                 = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];
        $info                = $db_auction_activity->where($map)->find();
        //会员可用额度
        $db_member = new Member();
        $map       = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $this->get_member_guid()],
        ];
        $member    = $db_member->where($map)->find();
        //当前标已用额度
        $db_auction_goods_order = new AuctionGoodsOrder();
        $join                   = [
            ['auction_goods ag', 'ago.goods_guid=ag.guid AND ago.bid=ag.bid'],
            ['auction_activity aa', 'aa.guid=ag.activity_guid AND aa.bid=ag.bid'],
        ];
        $map                    = [
            ['aa.bid', '=', $this->get_bid()],
            ['aa.guid', '=', $guid],
            ['ago.status', 'in', [0, 1]],
            ['ago.member_guid', '=', $this->get_member_guid()],
        ];
        $this_used_credit_limit = $db_auction_goods_order->alias('ago')->join($join)->where($map)->sum('offer_price');
        //个人已用额度
        $map               = [
            ['aa.bid', '=', $this->get_bid()],
            ['ago.status', 'in', [0, 1]],
            ['ago.member_guid', '=', $this->get_member_guid()],
        ];
        $used_credit_limit = $db_auction_goods_order->alias('ago')->join($join)->where($map)->sum('offer_price');
        $article           = '';
        $db_article        = new Article();
        if (!empty($info['article_guid'])) {
            $map     = [
                ['bid', '=', $this->get_bid()],
                ['guid', '=', $info['article_guid']],
            ];
            $article = $db_article->where($map)->value('content');
        }
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['key', '=', 'instruction'],
        ];
        $instruction = $db_article->where($map)->value('content');
        $config      = $this->get_config();
        $is_left     = ($member['money'] / $config['credit_ratio']) > $used_credit_limit; //是否剩余,额度减去已用
        $time_type   = $this->get_times_status($info['begin_time'], $info['end_time']);
        //图文列表
        $article_list       = [];
        $article_guid_array = $info['article_guid_json'] ?: [];
        if (!empty($article_guid_array)) {
            $db_goods_category_article   = new GoodsCategoryArticle();
            $map                         = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'in', $article_guid_array],
            ];
            $goods_category_article_list = $db_goods_category_article->where($map)->field(['guid', 'content'])->select()->toArray();
            foreach ($article_guid_array as $article_guid) {
                foreach ($goods_category_article_list as $key => $val) {
                    if ($val['guid'] == $article_guid) {
                        $article_list[] = $val;
                    }
                }
            }
        }
        $this->assign([
            'info'                   => $info,
            'guid'                   => $guid,
            'used_credit_limit'      => $used_credit_limit,
            'this_used_credit_limit' => $this_used_credit_limit,
            'member'                 => $member,
            'config'                 => $config,
            'article'                => $article,
            'instruction'            => $instruction,
            'file_url'               => $info['file_url'],
            'is_left'                => $is_left,
            'time_type'              => $time_type,
            'article_list'           => $article_list
        ]);
        return $this->fetch();
    }

    /**
     * 获取状态
     * @access public
     * @param integer $start_time 开始时间
     * @param integer $end_time 结束时间
     * @return mixed
     */
    protected function get_times_status($start_time, $end_time)
    {
        if (strtotime($start_time) > time()) {
            return 1;
        }
        if (strtotime($end_time) < time()) {
            return 3;
        }
        return 2;
    }

    /**
     * 我的
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my()
    {
        $db_goods            = new AuctionGoods();
        $map                 = [
            ['g.bid', '=', $this->get_bid()]
        ];
        $join                = [
            ['auction_activity aa', 'aa.guid=g.activity_guid AND aa.bid=g.bid'],
            ['goods_category gc', 'gc.guid=aa.goods_category_guid AND gc.bid=aa.bid'],
            ['auction_goods_order ago', "ago.goods_guid=g.guid AND ago.bid=g.bid  AND ago.status in(0,1) AND ago.member_guid='$this->get_member_guid()'"],
        ];
        $field               = ['gc.name' => 'category_name', 'gc.guid' => 'category_guid'];
        $goods_category_list = $db_goods->alias('g')->where($map)->group($field)->join($join)->field($field)->select()->toArray();
        $category_guid       = '';
        $status              = '';
        $params              = $this->params;
        if (!empty($params['category_guid'])) {
            $category_guid = $params['category_guid'];
        }
        if (isset($params['status']) && $params['status'] !== '') {
            $status = intval($params['status']);
        }
        $this->assign(
            [
                'goods_category_list' => $goods_category_list,
                'category_guid'       => $category_guid,
                'status'              => $status
            ]
        );
        return $this->fetch();
    }
}