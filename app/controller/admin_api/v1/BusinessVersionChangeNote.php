<?php

namespace app\controller\admin_api\v1;

use app\model\BusinessVersionChangeNote as BusinessVersionChangeNoteModel;

class BusinessVersionChangeNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $db          = new BusinessVersionChangeNoteModel();
        $join        = [

            ['business b', 'b.guid = bvcn.bid', 'LEFT'],  //商家信息
            ['business b1', 'b1.guid = b.parent_guid', 'LEFT'], //商家代理商信息
            ['business b2', 'b2.guid = bvcn.operator_bid', 'LEFT'], //操作员信息
        ];
        $field       = [
            'bvcn.*',
            'b.account'                                   => 'business_account',
            'b1.account'                                  => 'agent_account',
            'b2.account'                                  => 'operator_account',
            "CONCAT(b.business_name,'(',b.account,')')"   => 'business_info',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent_info',
            "CONCAT(b2.business_name,'(',b2.account,')')" => 'operator_info',
        ];
        $db_business = new \app\model\Business();
        $map         = [
            ['b2.delete_time', 'null', null]
        ];
        if ($db_business->is_agent()) {
            $map[] = ['bvcn.operator_bid', '=', $this->get_bid()];
        }
        $this->model = $db->alias('bvcn')->where($map)->join($join)->field($field)->order(['bvcn.create_time' => 'DESC']);
        result($this->_list());
    }
}
