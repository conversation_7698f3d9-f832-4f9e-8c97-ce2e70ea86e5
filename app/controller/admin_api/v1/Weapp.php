<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\WeappApplyPrivacyInterfaceNote;
use app\model\WeappCommitNote;
use app\model\WeappSubmitNote;
use app\model\WechatConfig;
use app\common\tools\Visitor;
use Exception;
use Throwable;

class Weapp extends BasicAdminApi

{
    public function update_order_detail_path()
    {
        $params      = $this->params;
        $appid       = $params['appid'];
        $instance    = weixin($appid)::WeMiniShipping();
        $result      = $instance->getOrderDetailPath();
        $before_path = $result['path'];
        $path        = 'pages/order/order?id=${商品订单号}';
        if ($before_path != $path) {
            $result = $instance->updateOrderDetailPath($path);
            success('之前未设置订单详情页路径或不一致,现在已设置为:' . $path);
        }
        success('当前小程序订单详情页路径已经设置为:' . $path);
    }

    public function re_bind_admin()
    {
        $params       = $this->params;
        $appid        = $params['appid'];
        $instance     = weixin()::WeOpenMiniApp();
        $callback_url = (string)url('gateway/wechat_auth/re_bind_admin', ['appid' => $appid], false, true);
        $url          = $instance->getComponentreBindAdmin($appid, $callback_url);
        $url          = tools()::build_url_with_referer($url);
        result(['url' => $url]);
    }

    /**
     *获取token
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_access_token()
    {
        $weappid = $this->get_business_weappid();
        if (!$weappid) {
            error('商家暂时未绑定小程序!');
        }
        $wechat       = weixin($weappid)::WeChatOauth();
        $access_token = $wechat->getAccessToken();
        if ($access_token) {
            result(['access_token' => $access_token]);
        }
        error('access_token获取失败!');
    }

    /**
     * 获取商家小程序appid
     * @access public
     * @return mixed
     */
    protected function get_business_weappid()
    {
        $config = get_config_by_bid($this->get_bid());
        return $config['weappid'];
    }

    /**
     *获取用户数据
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_user_info()
    {
        return $this->get_decode_data();
    }

    /**
     *解密数据
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_decode_data()
    {
        $code           = $this->request->param('code', '', null);
        $encrypted_data = $this->request->param('encrypted_data', '', null);
        $iv             = $this->request->param('iv', '', null);
        $weappid        = $this->get_business_weappid();
        $mini           = weixin($weappid)::WeMiniCrypt();
        $result         = $mini->decodeData($code, $iv, $encrypted_data);
        if (!is_array($result)) {
            wr_log($result);
            error($result);
        }
        wr_log($result);
        result($result);
    }

    /**
     *获取手机号数据
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_phone_info()
    {
        return $this->get_decode_data();
    }

    /**
     * 获取体验二维码
     * @access public
     * @return mixed
     */
    public function get_qrcode()
    {
        $params  = $this->params;
        $weappid = $params['appid'];
        $data    = [];
        try {
            $wechat = weixin($weappid)::WeMiniCode();
            $result = $wechat->getQrcode('', 'base64_encode');
            $data   = ['base64' => $result];
        } catch (Exception|Throwable $e) {
            error('二维码获取失败:' . $e->getMessage());
        }
        result($data);
    }

    /**
     *  撤销提交的审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function undocodeaudit()
    {
        $params  = $this->params;
        $weappid = $params['appid'];
        $wechat  = weixin($weappid)::WeMiniCode();
        $wechat->undoCodeAudit();
        $db          = new WeappSubmitNote();
        $update_data = ['status' => -3, 'reason' => '主动撤审'];
        $db->update_last_submit_note($weappid, $update_data);
        success('撤销成功!');
    }


    /**
     * 第1步 提交代码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function commit()
    {
        $params = $this->params;
        //       $appid                = $params['appid'];
        //       $subpackages          = $params['subpackages'];
        //       $plugins              = $params['plugins'];
        $commit_data          = [
            'appid'       => $params['appid'],
            'domain'      => $params['domain'],
            'subpackages' => $params['subpackages'] ?? [],
            'plugins'     => $params['plugins'] ?? [],
            'template_id' => $params['template_id'],
        ];
        $db_weapp_commit_note = new WeappCommitNote();
        $commit               = $db_weapp_commit_note->commit_app($commit_data);
        if ($commit) {
            success('上传成功');
        }
        error('上传失败' . $db_weapp_commit_note->getError());
    }

    /**
     * 第2步 提交提审
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit()
    {
        $params            = $this->params;
        $weappid           = $params['appid'];
        $weapp_submit_note = new WeappSubmitNote();
        $submit            = $weapp_submit_note->submit_app($weappid);
        if ($submit) {
            success('提审成功');
        }
        error('提审失败' . $weapp_submit_note->getError());
    }

    /**
     * 第3步 审核通过后发布上线
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function release()
    {
        $params          = $this->params;
        $db              = new WeappSubmitNote();
        $publish_release = $db->publish_release($params['appid']);
        if ($publish_release === true) {
            success('上线成功!');
        }
        error('上线失败:' . $db->getError());
    }

    /**
     * 绑定体验者
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function bind_tester()
    {
        $param     = $this->params;
        $wechat_id = $param['wechat_id'];
        $weappid   = $param['appid'];
        $service   = weixin($weappid)::WeMiniTester();
        $success   = false;
        try {
            $success = $service->bindTester($wechat_id);
        } catch (\Exception $e) {
            switch ($e->getCode()) {
                case 85003:
                    error('当前微信号绑定了过多体验者，请访问微信小程序【小程序助手】解绑其他小程序后再试！');;
                    break;
                case 85004:
                    error('当前微信号已绑定过,无需重复绑定！');;
                    break;
                default:
                    error('添加失败' . $e->getMessage());
            }
        }
        if ($success) {
            success('添加成功');
        } else {
            error('添加失败或者已经存在该微信号');
        }
    }

    /**
     * 更新小程序审核状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function speed_up_audit()
    {
        $param       = $this->params;
        $guid        = $param['guid'];
        $db          = new WeappSubmitNote();
        $map         = [['guid', '=', $guid]];
        $submit_note = $db->where($map)->find();
        $status      = $submit_note['status'];
        if ($status === 0) {
            success('状态已经是审核通过,无需加急');
        }
        $weappid = $submit_note['appid'];
        $service = weixin($weappid)::WeMiniCode();
        $result  = $service->speedUpAudit($submit_note['auditid']);
        success('加急成功');
    }

    /**
     * 更新小程序审核状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_audit_status()
    {
        $param       = $this->params;
        $guid        = $param['guid'];
        $db          = new WeappSubmitNote();
        $map         = [['guid', '=', $guid]];
        $submit_note = $db->where($map)->find();
        $status      = $submit_note['status'];
        if ($status === 0) {
            success('状态已经是审核通过,无需更新');
        }
        $weappid     = $submit_note['appid'];
        $service     = weixin($weappid)::WeMiniCode();
        $result      = $service->getAuditStatus($submit_note['auditid']);
        $status      = $result['status'];
        $map         = [['guid', '=', $guid]];
        $update_data = ['status' => $status];
        if (!empty($result['reason'])) {
            $update_data['reason'] = $result['reason'];
        }
        $db::update($update_data, $map);
        success('状态更新成功');
    }

    /**
     * 提交审核记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_note()
    {
        $this->model     = new WeappSubmitNote();
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['wsn.component_appid', '=', $component_appid]
        ];
        $join            = [
            ['wechat_config wc', 'wsn.component_appid = wc.component_appid and wsn.appid = wc.authorizer_appid'],
        ];
        $field           = [
            'wsn .*',
            'wc.nick_name',
            'wc.principal_name',
        ];

        $this->model = $this->model->alias('wsn')->join($join)->field($field)->where($map);
        result($this->_list());
    }

    /**
     * 提交记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function commit_note()
    {
        $this->model     = new WeappCommitNote();
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['component_appid', '=', $component_appid]
        ];
        $this->model     = $this->model->where($map);
        result($this->_list());
    }

    /**
     * 修改小程序参数配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update()
    {
        $db = new WechatConfig();
        // 先处理文件上传,获取表单上传文件,默认一次获取多个
        $update_data = $this->params;
        if (isset($update_data['ssl_cer'])) {
            $update_data['ssl_cer'] = tools()::web_to_path($update_data['ssl_cer']);
        }
        if (isset($update_data['ssl_key'])) {
            $update_data['ssl_key'] = tools()::web_to_path($update_data['ssl_key']);
        }
        $update_data['ssl_cer_string'] = $this->request->param('ssl_cer_string', '', null);
        $update_data['ssl_key_string'] = $this->request->param('ssl_key_string', '', null);
        $component_appid               = weixin()::get_component_appid();
        $business_weappid              = $this->get_business_weappid();
        if ($business_weappid != $update_data['authorizer_appid']) {
            error('appid不合法,修改失败');
        }
        $map    = [
            ['authorizer_appid', '=', $business_weappid],
            ['component_appid', '=', $component_appid],
        ];
        $update = $db->allowField(['ssl_cer', 'ssl_key', 'ssl_cer_string', 'ssl_key_string', 'mch_id', 'partnerkey', 'authorizer_appid', 'authorizer_appsecret'])::update($update_data, $map);
        success('修改成功');
    }

    /**
     * 获取商家小程序appid
     * @access public
     * @return mixed
     */
    public function business_weappid()
    {
        $appid = $this->get_business_weappid();
        result(['appid' => $appid]);
    }

    /**
     *获取小程序模板ID
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_template_id_list()
    {
        $appid                  = $this->get_business_weappid();
        $service                = weixin($appid)::WeMiniTemplate();
        $pay_tpl                = ''; //订单支付(模板ID: AT0009 )
        $revoke_tpl             = ''; //订单取消(模板ID: AT0024 )
        $send_tpl               = ''; //订单发货(模板ID: AT0007 )
        $refund_tpl             = ''; //订单退款(模板ID: AT0036 )
        $cash_success_tpl       = ''; //提现成功(模板ID: AT0830 )
        $cash_fail_tpl          = ''; //提现失败(模板ID: AT1242 )
        $apply_tpl              = ''; //分销审核(模板ID: AT0674 )
        $pintuan_success_notice = ''; //拼团成功通知(模板ID: AT0051 )
        $pintuan_fail_notice    = ''; //拼团失败通知(模板ID: AT0310 )
        $yy_success_notice      = ''; //预约成功(模板ID: AT0009 )
        $yy_refund_notice       = ''; //预约失败(模板ID: AT0036 )
        $mch_tpl_1              = ''; //审核结果通知(模板ID: AT0146 )
        $mch_tpl_2              = ''; //新订单通知(模板ID: AT0079 )
        $tpl_msg_id             = ''; //账户变动通知(模板ID: AT0677 )
        $lottery_success_notice = ''; //中奖结果通知(模板ID: AT1186 )
        $activity_success_tpl   = ''; //活动参与成功(模板ID: AT1348 ) //被封了 替换成 AT0027
        $activity_refund_tpl    = ''; //活动参与失败(模板ID: AT1863 ) //被封了 替换成 AT0028
        $template_array         = [
            'pay_tpl'                => ['template_id' => 'AT0009', 'keyword_id_list' => [5, 6, 11, 4]],
            'revoke_tpl'             => ['template_id' => 'AT0024', 'keyword_id_list' => [24, 5, 4, 17]],
            'send_tpl'               => ['template_id' => 'AT0007', 'keyword_id_list' => [5, 2, 23]],
            'refund_tpl'             => ['template_id' => 'AT0036', 'keyword_id_list' => [33, 13, 3, 4]],
            'cash_success_tpl'       => ['template_id' => 'AT0830', 'keyword_id_list' => [5, 8, 4]],
            'cash_fail_tpl'          => ['template_id' => 'AT1242', 'keyword_id_list' => [3, 1]],
            'apply_tpl'              => ['template_id' => 'AT0674', 'keyword_id_list' => [2, 4]],
            'pintuan_success_notice' => ['template_id' => 'AT0051', 'keyword_id_list' => [6, 13, 15]],
            'pintuan_fail_notice'    => ['template_id' => 'AT0310', 'keyword_id_list' => [2, 5, 6]],
            //           'yy_success_notice'      => ['template_id' => 'AT0009', 'keyword_id_list' => []],
            //           'yy_refund_notice'       => ['template_id' => 'AT0036', 'keyword_id_list' => []],
            'mch_tpl_1'              => ['template_id' => 'AT0146', 'keyword_id_list' => [33, 1]],
            'mch_tpl_2'              => ['template_id' => 'AT0079', 'keyword_id_list' => [6, 15]],
            'tpl_msg_id'             => ['template_id' => 'AT0677', 'keyword_id_list' => [1, 3]],
            'lottery_success_notice' => ['template_id' => 'AT1186', 'keyword_id_list' => [11, 6]],
            'activity_success_tpl'   => ['template_id' => 'AT0029', 'keyword_id_list' => [62, 11, 27]],
            //'activity_refund_tpl'    => ['template_id' => 'AT1863', 'keyword_id_list' => [6, 4, 1]],
            'activity_refund_tpl'    => ['template_id' => 'AT0028', 'keyword_id_list' => [5, 6, 7]],
        ];
        $template_list          = $service->getTemplateList();
        if (!empty($template_list['list'])) {
            foreach ($template_list['list'] as $key => $val) {
                //循环删除
                $service->delTemplate($val['template_id']);
            }
        }
        $after_template_list = [];
        foreach ($template_array as $key => $val) {
            try {
                $result                    = $service->addTemplate($val['template_id'], $val['keyword_id_list']);
                $after_template_list[$key] = $result['template_id'];
            } catch (Exception $e) {
                $after_template_list[$key] = '';
                wr_log('模板ID:' . $val['template_id'] . '获取失败:' . $e->getMessage(), 1);
                //error('批量添加失败,请重试:'.$e->getMessage());
            }
        }
        $after_template_list['yy_success_notice'] = $after_template_list['pay_tpl'];
        $after_template_list['yy_refund_notice']  = $after_template_list['refund_tpl'];
        result($after_template_list);
    }

    /**
     * 小程序授权信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db                                 = new WechatConfig();
        $bid                                = $this->get_bid();
        $info                               = '';
        $last_commit_version                = '';
        $last_submit_note                   = '';
        $wait_submit_version                = '';
        $weapp_apply_privacy_interface_note = [];
        $weappid                            = $this->get_business_weappid();
        $url                                = (string)url('gateway/wechat_auth/auth', ['bid' => $bid, 'auth_type' => 2], true, true);
        //       if (!$weappid) {
        //       return ' < script>window.location.href="'.$url.'";</script > ';//这种形式的referer才是真正的当前页面
        //           redirect($url);//如果传入指定的callback 则可以用此方法跳转
        //       }
        if ($weappid) {
            $component_appid = weixin()::get_component_appid($weappid);
            $map             = [
                ['authorizer_appid', '=', $weappid],
                ['component_appid', '=', $component_appid]];
            $info            = $db->where($map)->findOrEmpty();
            if ($info->isEmpty() && Visitor::is_readonly()) {
                error("未授权公众号,请登录正式环境授权");
            } else {
                $qrcode_url         = urlencode(urlencode($info['qrcode_url']));
                $info['qrcode_url'] = '/index/plugins/output_img?url=' . $qrcode_url;
            }
            $db_weapp_submit_note                  = new WeappSubmitNote();
            $last_submit_note                      = $db_weapp_submit_note->get_last_submit_note($weappid);
            $db_weapp_commit_note                  = new WeappCommitNote();
            $last_commit_version                   = $db_weapp_commit_note->get_last_commit_version($weappid);
            $wait_submit_version                   = $db_weapp_commit_note->get_last_commit_version($weappid, [1]);
            $map                                   = [['appid', '=', $weappid], ['component_appid', '=', $component_appid]];
            $db_weapp_apply_privacy_interface_note = new WeappApplyPrivacyInterfaceNote();
            $weapp_apply_privacy_interface_note    = $db_weapp_apply_privacy_interface_note->where($map)->order(['create_time' => 'DESC'])->select();
        }
        $data = [
            'info'                               => $info,
            'auth_url'                           => $url,
            'last_commit_version'                => $last_commit_version,
            'last_submit_note'                   => $last_submit_note,
            'wait_submit_version'                => $wait_submit_version,
            'weapp_apply_privacy_interface_note' => $weapp_apply_privacy_interface_note,
        ];
        result($data);
    }
}