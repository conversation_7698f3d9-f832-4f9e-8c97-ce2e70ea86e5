<?php

namespace app\controller\admin_api\v1;

use Exception;

class AgentWeb extends BasicAdminApi
{
    /**
     * 代理商列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function partner_list()
    {
        $agent_web = new \OpenApi\AgentWeb();
        $result    = $agent_web->get_partner_list();
        $data      = [
            'data'         => $result['list'],
            'current_page' => $result['pages']['current_page'],
            'last_page'    => $result['pages']['last_page'],
            'per_page'     => $result['pages']['per_page'],
            'total'        => $result['pages']['total'],
        ];
        result($data);
    }
}