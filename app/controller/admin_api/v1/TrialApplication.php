<?php

namespace app\controller\admin_api\v1;

use app\common\service\NotifyService;
use Exception;

class TrialApplication extends BasicAdminApi
{
    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [];
        $db_business = new \app\model\Business();
        if (!$db_business->is_admin()) {
            $map[] = ['bid', '=', $this->get_bid()];
        }
        $this->model = $this->model->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }


    /**
     *审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $params                = $this->params;
        $guid                  = $params['guid'];
        $db_trial_application  = new \app\model\TrialApplication();
        $db_business           = new \app\model\Business();
        $map_trial_application = [
            ['guid', '=', $guid]
        ];
        if (!$db_business->is_admin()) {
            $map_trial_application[] = ['bid', '=', $this->get_bid()];
        }
        $note          = $db_trial_application->where($map_trial_application)->find()->toArray();
        $mobile        = $note['mobile'];
        $bid           = $note['bid'];
        $db_business   = new \app\model\Business();
        $map           = [['account', '=', $mobile]];
        $note_business = $db_business->where($map)->findOrEmpty();
        if (!$note_business->isEmpty()) {
            error('该手机号已经注册过');
        }
        $data   = [
            'account'        => $note['account'] ?: $mobile,
            'mobile'         => $mobile,
            'type'           => 1,
            'business_name'  => $note['company'],
            'true_name'      => $note['true_name'],
            'version_guid'   => $note['version_guid'],
            'login_password' => $note['password'],
            'memo'           => '在线申请试用'
        ];
        $result = $note_business->add($data);
        if (!$result) {
            error($note_business->getError());
        }
        //触发通知
        $data['create_time'] = format_timestamp();
        $data['bid']         = $note['bid'];
        $data['company']     = $note['company'];
        $data['member_guid'] = $note['member_guid'];
        notify()->set_key_name(NotifyService::RegisteredBusinessSuccessfully)->set_member_mobile($mobile)->set_data($data)->set_map($note)->set_bid($bid)->send();
        $update_data = [
            'status'       => 1,
            'examine_time' => format_timestamp()
        ];
        $db_trial_application::update($update_data, $map_trial_application);
        success('审核通过');
    }
}