<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\GiftPackage as GiftPackageModel;
use app\model\YkyCouponExchangeOrder;
use app\model\YkyCouponTransferNote;
use Exception;
use OpenApi\Yky;

class GiftPackage extends BasicAdminApi
{
    /**
     *魔术包列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *同步魔术包
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function sync()
    {
        $config        = get_config_by_bid($this->get_bid());
        $Yky           = new Yky($config);
        $data['where'] = " TypeName='普通券' AND Title like '魔术包%'";
        $result        = $Yky->Get_CouponPagedV2($data);
        if ($result === false) {
            error('接口:' . $Yky->action . "返回失败,原因是:" . $Yky->message);
        }
        $resultArr = $result['data'];
        foreach ($resultArr as $data) {
            $CashModel = new GiftPackageModel();
            $CashModel->sync($data);
        }
        success('同步成功');
    }

    /**
     *魔术包记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function note()
    {
        $this->model = new YkyCouponExchangeOrder();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *转账记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function transfer_note()
    {
        $join        = [
            ['gift_package gp', 'a.coupon_guid=gp.coupon_guid'],
        ];
        $db          = new YkyCouponTransferNote();
        $this->model = $db->alias('a')->join($join)->field(['a.from_card_id', 'a.to_card_id', 'a.create_time', 'gp.coupon_title']);
        result($this->_list());
    }
}