<?php

namespace app\controller\admin_api\v1;

use app\model\YkyMember as YkyMemberModel;
use Exception;

class YkyMember extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyMemberModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}