<?php

namespace app\controller\admin_api\v1;

use app\model\UserBindNote as UserBindNoteModel;
use Exception;

class UserBindNote extends BasicAdminApi
{
    /**
     *所有短信发送记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function all()
    {
        $db              = new UserBindNoteModel();
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['wc.component_appid', '=', $component_appid]
        ];
        $join            = [
            ['business b', 'ubn.bid = b.guid'],
            ['user u', 'ubn.user_guid = u.guid AND ubn.bid=u.bid'],
            ['wechat_config wc', 'ubn.appid = wc.authorizer_appid'],
            ['wechat_user_info wui', 'wui.appid = ubn.appid AND wui.openid = ubn.openid', 'LEFT'],
        ];
        $field           = [
            'ubn.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
            "CONCAT(u.name,'(',u.account,')')"          => 'user_info',
            'u.account'                                 => 'user_account',
            'wc.nick_name'                              => 'wechat_appid_nick_name',
            'wui.nickname'                              => 'user_nick_name',
            'wui.headimgurl'                            => 'user_head_img_url',
            'wui.subscribe'                             => 'subscribe',
        ];
        $db_business     = new \app\model\Business();
        $business_type   = $db_business->get_business_type($this->get_bid());
        if ($db_business->is_admin_type($business_type)) {

        } elseif ($db_business->is_business_type($business_type)) {
            $map[] = ['b.guid', '=', $this->get_bid()];
        } elseif ($db_business->is_agent_type($business_type)) {
            $map[] = ['b.parent_guid', '=', $this->get_bid()];
        } else {
            error('暂时不支持查询');
        }
        $this->model = $db->alias('ubn')->join($join)->field($field)->where($map);
        result($this->_list());
    }
}