<?php

namespace app\controller\admin_api\v1;

use app\model\JobsFailed as JobsFailedModel;
use think\facade\Queue;

class JobsFailed extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new JobsFailedModel();
        $this->model = $this->model->order(['id' => 'DESC'])->append(['payload_text']);
        result($this->_list());
    }

    public function retry()
    {
        $id      = $this->params['id'];
        $db      = new JobsFailedModel();
        $map     = [['id', '=', $id]];
        $job     = $db->where($map)->findOrFail();
        $payload = $job['payload'];
        $job_id  = Queue::connection($job['connection'])->push($payload['job'], $payload['data'], $job['queue']);
        if ($job_id) {
            $db->where($map)->delete();
            success('重试成功,新任务ID:' . $job_id);
        } else {
            error('重试失败');
        }
    }
}