<?php

namespace app\controller\admin_api\v1;

use app\model\User;
use Exception;

class MediaType extends BasicAdminApi
{
    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_user           = new User();
        $map               = [
            ['bid', '=', $this->get_bid()],
            //            ['create_user_id', 'IN', $db_user->getChildArr()]
        ];
        $db_media_category = new \app\model\MediaType();
        $this->model       = $db_media_category->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC']);
        result($this->_list());

    }
}
