<?php

namespace app\controller\admin_api\v1;

use Exception;

class BusinessVersion extends BasicAdminApi
{
    /**
     *商家功能包
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $business_guid = $this->params['business_guid'];
        $map           = [
            ['business_guid', '=', $business_guid]
        ];
        result($this->_list());
    }
}