<?php

namespace app\controller\admin_api\v1;

use app\model\Rule as RuleModel;
use app\model\User;
use Exception;

class Rule extends BasicAdminApi
{
    /**
     *功能列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db          = new RuleModel();
        $this->model = $db->order(['parent_guid' => 'ASC']);
        result($this->_list());
    }

    /**
     *功能列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_rule = new RuleModel();
        $params  = $this->params;
        $field   = [
            'guid',
            'parent_guid',
            'name'  => 'path',
            'title' => 'name',
            'type',
            //           'icon',
            'sort',
            'is_auth',
        ];
        $map     = [];
        if (!empty($params['parent_guid'])) {
            $map[] = ['parent_guid', '=', $params['parent_guid']];
        }
        $list = $db_rule->field($field)->where($map)->order(['parent_guid' => 'DESC', 'sort' => 'ASC'])->select()->toArray();
        result($list);
    }

    /**
     *功能详情
     * @access public
     * @return mixed
     * @throws Exception
     */

    public function detail()
    {
        $db_rule = new RuleModel();
        $list    = $db_rule->find($this->params['guid']);
        result($list);
    }

    /**
     *系统登录后菜单 layui_admin
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function menu()
    {
        $db_rule = new RuleModel();
        $result  = $db_rule->get_menus_by_bid_user_guid();
        result($result);
    }

    /**
     *系统登录后菜单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function menus()
    {
        $db_rule = new RuleModel();
        $result  = $db_rule->get_menus_by_role_guid_layui();
        result($result);
    }

    /**
     *获取权限分组
     * @access public
     * @return void
     * @throws Exception
     */
    public function permission()
    {
        $bid                 = $this->get_bid();
        $user_guid           = $this->get_user_guid();
        $db_user             = new User();
        $default_user_guid   = $db_user->get_default_user_guid();
        $is_admin_user       = $user_guid == $default_user_guid;
        $user_permission     = $db_user->get_user_permission_array($user_guid, $bid);
        $business_permission = $is_admin_user ? $user_permission : $db_user->get_user_permission_array($default_user_guid, $bid);
        result(['user_permission' => $user_permission, 'business_permission' => $business_permission]);
    }

    /**
     *获取菜单层级
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_organization()
    {
        $db = new RuleModel();
        //       $map   = [['status', '=', 1]];
        $map   = [];
        $rules = $db->where($map)->field(['guid' => 'id', 'parent_guid' => 'pId', 'title' => 'name'])->order(['sort' => 'ASC'])->select()->toArray();
        result($rules);
    }

    /**
     *更新菜单层级
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_organization()
    {
        $db_rule     = new RuleModel();
        $params      = $this->params;
        $guid        = $params['guid'];
        $parent_guid = $params['parent_guid'] ?: tools()::get_empty_guid();
        $node_str    = $params['node_str'];
        //更新层级关系
        $update_organization = $db_rule->update_organization($guid, $parent_guid);
        //更新排序
        if ($params['need_sort'] == 1) {
            $db_rule->update_sort($node_str);
        }
        //清空缓存
        $db_rule->remove_all_rule_cache();
        if ($update_organization) {
            success('更新成功');
        } else {
            error('未更新');
        }
    }
}
