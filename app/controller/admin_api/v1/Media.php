<?php

namespace app\controller\admin_api\v1;

use app\model\User;
use Exception;

class Media extends BasicAdminApi
{

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_user     = new User();
        $map         = [
            ['bid', '=', $this->get_bid()],
            //            ['create_user_id', 'IN', $db_user->getChildArr()]
        ];
        $db_media    = new \app\model\Media();
        $this->model = $db_media->where($map)->order(['sort' => 'ASC', 'category_guid' => 'ASC', 'type_guid' => 'ASC', 'create_time' => 'DESC'])->append(['pic_mini']);
        result($this->_list());
    }
}
