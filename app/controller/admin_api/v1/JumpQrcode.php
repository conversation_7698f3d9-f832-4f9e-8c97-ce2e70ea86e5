<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use app\model\TxtFile;
use Exception;
use WeMini\QrcodeJump;

class JumpQrcode extends BasicAdminApi
{
    protected function get_weapp_class(): QrcodeJump
    {
        $params = $this->params;
        $appid  = $params['appid'];
        return weixin($appid)::WeMiniQrcodeJump();
    }

    public function scan_to_code()
    {
        $params   = $this->params;
        $scan_bid = $params['scan_bid'];
        //先判断是否对接了小程序
        $config  = get_config_by_bid($scan_bid);
        $weappid = $config['weappid'];
        if (empty($weappid)) {
            error('请先授权对接小程序');
        }
        $db_coupon              = new Coupon();
        $verify_url_qrcode_list = $db_coupon->get_verify_url_qrcode_list($scan_bid);
        $short_url              = $verify_url_qrcode_list['short_url'];
        $data                   = [
            'is_edit'         => 0,
            'prefix'          => $short_url,
            'permit_sub_rule' => 2,
            'path'            => '/pages/code/index',
            'open_version'    => 3,
        ];
        $this->params['appid']  = $weappid;
        $this->download_qrcode_text();
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $we_mini_qrcode_jump->addJumpQRCode($data);
        $we_mini_qrcode_jump->publishJumpQRCode(['prefix' => $short_url]);
        success('设置扫提货码跳转到小程序成功!');
    }

    protected function download_qrcode_text()
    {
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $db                  = new TxtFile();
        $params              = $this->params;
        $appid               = $params['appid'];
        $map                 = [['appid', '=', $appid]];
        $note                = $db->where($map)->findOrEmpty();
        if ($note->isEmpty()) {
            $result = $we_mini_qrcode_jump->downloadQRCodeText();
            $data   = [
                'appid'        => $appid,
                'file_name'    => $result['file_name'],
                'file_content' => $result['file_content'],
            ];
            $db->save($data);
        }
        return true;
    }

    /**
     * 扫码打开小程序
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $appid               = $params['appid'];
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $list                = $we_mini_qrcode_jump->getJumpQRCode();
        result($list['rule_list']);
    }

    /**
     * 扫码打开小程序
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        if (empty($params['debug_url'])) {
            unset($params['debug_url']);
        }
        if (!empty($params['params'])) {
            $params['path'] .= $params['params'];
        }
        unset($params['params']);
        $this->download_qrcode_text();
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $we_mini_qrcode_jump->addJumpQRCode($params);
        success('添加成功');
    }

    /**
     * 扫码打开小程序
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function publish()
    {
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $we_mini_qrcode_jump->publishJumpQRCode(['prefix' => $params['prefix']]);
        success('修改成功');
    }


    /**
     * 扫码打开小程序
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        if (empty($params['debug_url'])) {
            unset($params['debug_url']);
        }
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $we_mini_qrcode_jump->addJumpQRCode($params);
        success('修改成功');
    }

    /**
     * 扫码打开小程序
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $we_mini_qrcode_jump = $this->get_weapp_class();
        $we_mini_qrcode_jump->deleteJumpQRCode(['prefix' => $params['prefix']]);
        success('删除成功');
    }
}