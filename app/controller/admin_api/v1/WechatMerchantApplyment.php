<?php

namespace app\controller\admin_api\v1;

use app\model\WechatMerchantApplyment as WechatMerchantApplymentModel;
use Exception;

class WechatMerchantApplyment extends BasicAdminApi
{
    /**
     * 我的代理
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $db           = new WechatMerchantApplymentModel();
        $params       = $this->params;
        $applyment_id = $params['applyment_id'];
        $bid          = $params['bid'];
        $create_bid   = $this->get_bid();
        $map          = [
            ['applyment_id', '=', $applyment_id]
        ];
        $count        = $db->where($map)->count();
        if ($count > 0) {
            error('当前申请单编号已经录入过,无需重复录入');
        }
        $insert_data = [
            'guid'         => create_guid(),
            'bid'          => $bid,
            'applyment_id' => $applyment_id,
            'create_bid'   => $create_bid,
        ];
        $db->save($insert_data);
        job()->set_job_name('Monitor@wechat_merchant_applyment')->push_job();
        success('申请单录入成功!');
    }
}