<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\CrontabClass as CrontabClassModel;
use Exception;

class CrontabClass extends BasicAdminApi
{
    /**
     *定时任务列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new CrontabClassModel();
        $this->model = $db->order(['id' => 'DESC']);
        result($this->_list());
    }
}