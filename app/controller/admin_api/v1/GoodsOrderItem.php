<?php

namespace app\controller\admin_api\v1;


class GoodsOrderItem extends BasicAdminApi
{
    public function edit()
    {
        $params            = $this->params;
        $bid               = $this->get_bid();
        $order_guid        = $params['order_guid'];
        $before_goods_guid = $params['before_goods_guid'];
        $goods_guid        = $params['goods_guid'];

        $db_goods_order_item    = new \app\model\GoodsOrderItem();
        $map_goods_order_item   = [
            ['bid', '=', $bid],
            ['order_guid', '=', $order_guid],
            ['goods_guid', '=', $before_goods_guid],
        ];
        $before_goods_item_info = $db_goods_order_item->where($map_goods_order_item)->findOrFail();
        $before_sku_guid        = $before_goods_item_info['sku_guid'];
        if (!empty($before_sku_guid)) {
            error('暂时不支持SKU商品换货');
        }

        $db_goods         = new \app\model\Goods();
        $map_goods        = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $after_goods_info = $db_goods->where($map_goods)->findOrFail();
//        $map_goods         = [
//            ['bid', '=', $bid],
//            ['guid', '=', $before_goods_guid],
//        ];
//        $before_goods_info = $db_goods->where($map_goods)->findOrFail();

        $db_goods_order   = new \app\model\GoodsOrder();
        $map_goods_order  = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $goods_order_info = $db_goods_order->where($map_goods_order)->findOrFail();

        $before_goods_price = $before_goods_item_info['goods_price'];
        $after_goods_price  = $after_goods_info['price'];
        //先UPDATE 订单明细表
        $update_goods_order_item = [
            'goods_guid'  => $after_goods_info['guid'],
            'goods_name'  => $after_goods_info['name'],
            'goods_price' => $after_goods_price
        ];
        $db_goods_order_item::update($update_goods_order_item, $map_goods_order_item);
        $diff_price = tools()::nc_price_calculate($after_goods_price, '-', $before_goods_price);

        $before_goods_order_goods_info  = $goods_order_info['goods_info'];
        $before_goods_order_goods_money = $goods_order_info['goods_money'];
        $before_goods_order_total_money = $goods_order_info['total_money'];
        $after_goods_order_goods_money  = tools()::nc_price_calculate($before_goods_order_goods_money, '+', $diff_price);
        $after_goods_order_total_money  = tools()::nc_price_calculate($before_goods_order_total_money, '+', $diff_price);

        foreach ($before_goods_order_goods_info as $key => $val) {
            if ($val['guid'] == $before_goods_guid) {
                //开始重新赋值
                $before_goods_order_goods_info[$key]['guid']  = $after_goods_info['guid'];
                $before_goods_order_goods_info[$key]['pic']   = $after_goods_info['pic'];
                $before_goods_order_goods_info[$key]['name']  = $after_goods_info['name'];
                $before_goods_order_goods_info[$key]['price'] = $after_goods_info['price'];
                $before_goods_order_goods_info[$key]['specs'] = $after_goods_info['specs'];
            }
        }
        $update_goods_order = [
            'goods_money' => $after_goods_order_goods_money,
            'total_money' => $after_goods_order_total_money,
            'goods_info'  => $before_goods_order_goods_info,
        ];
        $db_goods_order::update($update_goods_order, $map_goods_order);
        success('换货成功');
    }
}