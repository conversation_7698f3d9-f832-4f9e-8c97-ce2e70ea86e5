<?php

namespace app\controller\admin_api\v1;

use app\model\PhysicalExaminationNote as PhysicalExaminationNoteModel;
use Exception;
use xieyongfa\yky\Yky;

class PhysicalExaminationNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new PhysicalExaminationNoteModel();
        $map         = [
            ['bid', '=', $this->get_bid()],
            //            ['status', '=', 1],
        ];
        $this->model = $this->model->where($map);
        $params      = $this->params;
        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'count(1)'         => 'count',
                'SUM(total_price)' => 'total_price',
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '共 <b style="color: red"> ' . $data['count'] . '</b> 笔,共 <b style="color: red"> ' . number_format($data['total_price'], 2) . '</b> 元';
            success($total_text);
        }
        result($this->_list());
    }

    /**
     * 修复支付失败的体检记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function repair()
    {
        $params = $this->params;
        $guid   = $params['guid'];
        $bid    = $this->get_bid();

        // 1. 查询支付失败的记录
        $db  = new PhysicalExaminationNoteModel();
        $map = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
            ['status', '=', -1] // 只能修复支付失败的记录
        ];

        $record = $db->where($map)->findOrEmpty();
        if ($record->isEmpty()) {
            error('记录不存在或状态不正确，只能修复支付失败的记录');
        }

        // 2. 获取配置和用户信息
        $config       = get_config_by_bid($bid);
        $user_guid    = $record['user_guid'];
        $db_user      = new \app\model\User();
        $user_info    = $db_user->get_user_info($user_guid, $bid);
        $user_account = $user_info['account'];

        // 3. 重新构造扣费数据
        $yky_consume  = Yky::Consume($config);
        $consume_data = [
            'userAccount'  => $user_account,
            'cardId'       => $record['card_id'],
            'totalMoney'   => $record['total_price'],
            'paidMoney'    => 0,
            'paidPoint'    => 0,
            'paidValue'    => $record['total_price'],
            'paidCard'     => 0,
            'totalPaid'    => $record['total_price'],
            'paidOther'    => 0,
            'otherPayType' => '',
            'uniqueCode'   => $record['guid'],
            'meno'         => '体检交费修复:单号' . $record['clinic_code'],
        ];

        // 4. 重新发起扣费
        $result = $yky_consume->Consume($consume_data);
        if ($result === false) {
            // 修复失败，记录日志
            wr_log('体检记录修复失败：' . $record['clinic_code'] . ' - ' . $yky_consume->message, true, $bid);
            error('修复失败：' . $yky_consume->message);
        }

        // 5. 修复成功，更新状态
        $response_data   = $yky_consume->getResponseData();
        $yky_bill_number = $response_data['billNumber'] ?? ($response_data['message'] ?? '未知单号');
        $update_data     = [
            'status'          => 1,
            'pay_time'        => format_timestamp(),
            'message'         => $yky_consume->message,
            'yky_bill_number' => $yky_bill_number
        ];

        $db::update($update_data, $map);

        // 6. 记录成功日志
        wr_log('体检记录修复成功：' . $record['clinic_code'] . ' - 单据号：' . $yky_bill_number, false, $bid);

        success('修复成功，支付状态已同步');
    }
}
