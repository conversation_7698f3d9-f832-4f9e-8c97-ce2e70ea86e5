<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\PayChannel;
use app\model\PayParameter as PayParameterModel;
use app\model\PayScene;
use Exception;


class PayParameter extends BasicAdminApi
{
    /**
     *获取参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get()
    {
        $params               = $this->params;
        $bid                  = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $db_pay_parameter     = new PayParameterModel();
        $db_business          = new \app\model\Business();
        $map                  = [['guid', '=', $bid]];
        $parent_bid           = $db_business->where($map)->value('parent_guid');
        $db_pay_channel       = new PayChannel();
        $add_channel_id_array = [];
        if (!empty($parent_bid)) {
            //有代理优先看是否有自有通道
            $map                  = [
                ['bid', '=', $parent_bid],
                ['status', '=', 1]
            ];
            $add_channel_id_array = $db_pay_channel->where($map)->order(['priority' => 'ASC'])->column('id');
        }
        if (empty($add_channel_id_array)) {
            //如果没找到通道 则看总部通道
            $map                  = [
                //                ['bid', 'null', null],
                ['status', '=', 1]
            ];
            $add_channel_id_array = $db_pay_channel->where($map)->whereRaw("(bid IS NULL OR bid ='')")->order(['priority' => 'ASC'])->column('id');
        }
        foreach ($add_channel_id_array as $channel_id) {
            $db_pay_parameter = new PayParameterModel();
            $map              = [['bid', '=', $bid], ['channel_id', '=', $channel_id]];
            $count            = $db_pay_parameter->where($map)->count();
            if (!$count) {
                $parameter_guid = $db_pay_parameter->add_parameter($channel_id, $bid);
                if ($parameter_guid === false) {
                    error('自动添加通道失败:' . $db_pay_parameter->getError());
                }
            }
        }
        $join          = [
            ['pay_channel pc', "pc.id = pp.channel_id",],
            ['pay_channel_provider pcp', "pcp.id=pc.channel_provider_id",],
        ];
        $field         = ['pp.*', 'pc.name', 'pcp.key', 'pc.scene_id' => 'channel_scene_id', 'pcp.scene_id' => 'provider_scene_id'];
        $map           = [['pp.bid', '=', $bid]];
        $pay_parameter = $db_pay_parameter->alias('pp')->join($join)->field($field)->where($map)->order(['status' => 'DESC', 'priority' => 'ASC'])->select()->toArray();
        $db_pay_scene  = new PayScene();
        $scene         = $db_pay_scene->order(['drive' => 'DESC', 'scene_id' => 'ASC'])->select();
        $result        = ['pay_parameter' => $pay_parameter, 'scene' => $scene, 'bid' => $bid];
        result($result);
    }

    /**
     *添加通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add_channel()
    {
        $params           = $this->params;
        $channel_id       = intval($params['channel_id']);
        $bid              = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $db_pay_parameter = new PayParameterModel();
        $result           = $db_pay_parameter->add_parameter($channel_id, $bid);
        if ($result) {
            success('添加成功');
        } else {
            error('添加失败:' . $db_pay_parameter->getError());
        }
    }

    /**
     *编辑支付参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db     = new PayParameterModel();
        $params = $this->params;
        $bid    = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $update = $db->update_parameter($bid, $params);
        if ($update) {
            success('修改成功');
        } else {
            error('修改失败');
        }
    }
}