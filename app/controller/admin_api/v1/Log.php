<?php

namespace app\controller\admin_api\v1;


use Exception;

class Log extends BasicAdminApi
{
    /**
     * 操作日志
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $order       = [
            'id' => 'DESC',
        ];
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->order($order)->where($map);
        result($this->_list());
    }

    /**
     * 系统日志
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function log()
    {
        $order  = [
            'l.id' => 'DESC',
        ];
        $map    = [];
        $params = $this->params;
        if (empty($params['create_time'])) {
            $begin_date = date('Y-m-d', strtotime('-3 days'));
            $map[]      = ['l.create_time', '> time', $begin_date];
        }
        $field       = [
            'l.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            'u.account'                                 => 'user_account',
            'u.name'                                    => 'user_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
        ];
        $join        = [
            ['business b', 'l.bid = b.guid', 'LEFT'],
            ['user u', 'l.bid = u.bid AND l.user_id = u.id', 'LEFT'],
        ];
        $this->model = $this->model->alias('l')->join($join)->field($field)->order($order)->where($map);
        result($this->_list());
    }
}
