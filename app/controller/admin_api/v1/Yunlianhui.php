<?php

namespace app\controller\admin_api\v1;

use app\model\Jobs;
use app\model\YkyDailyRewardOrder;
use app\model\YlhAccessToken;
use app\model\YlhPointOrder;
use app\model\YlhRewardOrder;
use Exception;

class Yunlianhui extends BasicAdminApi
{
    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->model = new YlhAccessToken();
        result($this->_list());
    }

    /**
     *编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $this->model = new YlhAccessToken();
        $this->model->edit($this->params);
        success('编辑成功');
    }

    /**
     *奖励订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reward_order()
    {
        $this->model = new YlhRewardOrder();
        result($this->_list());
    }

    /**
     *每日奖励订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reward_date_order()
    {
        $this->model = new YkyDailyRewardOrder();
        result($this->_list());
    }

    /**
     *订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function order()
    {
        $this->model = new YlhPointOrder();
        result($this->_list());
    }

    /**
     *重试
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function retry()
    {
        $guid = input('guid');
        if (!$guid) {
            error('参数错误');
        }
        $db  = new Jobs();
        $map = [['payload', 'like', '%' . $guid . '%']];
        $db->where($map)->update([
            'queue'        => 'default',
            'attempts'     => 0,
            'reserved'     => 0,
            'reserved_at'  => null,
            'available_at' => time(),
            'created_at'   => time(),
        ]);
        success("重试成功");
    }

    /**
     *调整状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_status()
    {
        $status      = $this->params['status'] == 'true' ? 1 : 0;
        $db          = new YlhAccessToken();
        $map         = [
            ['guid', '=', $this->params['guid']],
            ['bid', '=', $this->get_bid()],
        ];
        $update_data = ['status' => $status];
        $db::update($update_data, $map);
        if ($status === 1) {
            success('开启成功');
        } elseif ($status === 0) {
            success('关闭成功');
        } else {
            error('操作失败');
        }
    }

    /**
     *授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function authorization()
    {
        $db = new YlhAccessToken();
        //先判断当前可用授权数量 是否超过
        $map      = [
            ['bid', '=', $this->get_bid()],
            ['license_status', '=', 1],
        ];
        $is_auth  = $db->where($map)->count();
        $max_auth = get_system_config('ylh_auth_license');
        if ($is_auth >= $max_auth) {
            error('购买失败,您当前可用' . $max_auth . '个授权');
        }
        //修改授权状态以及过期时间
        $map                        = [
            ['guid', '=', $this->params['guid']],
            ['bid', '=', $this->get_bid()],
            ['license_status', '=', 0],
        ];
        $info                       = $db->where($map)->find();
        $after_license_expired_time = date('Y-m-d H:i:s', strtotime('+1 year', strtotime($info['license_expired_time'])));
        $update_data                = ['license_expired_time' => $after_license_expired_time, 'license_status' => 1];
        $result                     = $db::update($update_data, $map);
        if ($result) {
            success('购买成功');
        } else {
            error('购买失败');
        }
    }
}
