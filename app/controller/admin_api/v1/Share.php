<?php

namespace app\controller\admin_api\v1;

use Exception;

class Share extends BasicAdminApi
{
    /**
     * 图文列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function cropper()
    {
        //https://file.yikayi.net/file/uploads/28/202206/03/9d131918-ed32-02df-ad8f-f5b14fcfa3f4.png
        $bid                     = $this->get_bid();
        $config                  = get_config_by_bid($bid);
        $share_background_image  = $config['share_background_image'];
        $share_background_config = $config['share_background_config'];
        result([
            'business_guid'           => $bid,
            'share_background_image'  => $share_background_image,
            'share_background_config' => $share_background_config,
        ]);
    }
}