<?php

namespace app\controller\admin_api\v1;


use Exception;

class ExpressQueryNote extends BasicAdminApi
{
    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db   = new \app\model\ExpressQueryNote();
        $map  = [];
        $join = [
            ['business b', 'eqn.bid = b.guid', 'INNER'],
            ['goods_order go', 'eqn.bid = go.bid AND eqn.order_guid = go.guid ', 'INNER'],
        ];
        if (empty($this->params['create_time'])) {
            $begin_date = date('Y-m-d', strtotime('-3 days'));
            $map[]      = ['eqn.create_time', '>', $begin_date];
        }
        $field       = [
            'eqn.*',
            'go.id'                                     => 'order_id',
            'go.create_time'                            => 'order_create_time',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
        ];
        $this->model = $db->alias('eqn')->append(['result_text'])->join($join)->field($field)->where($map)->order(['id' => 'DESC']);
        result($this->_list());
    }
}