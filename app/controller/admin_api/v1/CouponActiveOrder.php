<?php

namespace app\controller\admin_api\v1;

use app\model\UserMoneyNote;
use Exception;

class CouponActiveOrder extends BasicAdminApi
{
    public function revoke()
    {
        $params                  = $this->params;
        $bid                     = $this->get_bid();
        $db_coupon_active_order  = new \app\model\CouponActiveOrder();
        $guid                    = $params['order_guid'];
        $map_coupon_active_order = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $order_info              = $db_coupon_active_order->where($map_coupon_active_order)->findOrFail();
        if ($order_info['status'] != 1) {
            error('仅支持撤销已支付订单');
        }
        $pay_type = $order_info['pay_type']; //1 微信 2余额
        if ($pay_type == 1) {
            error('暂不支持撤销微信激活订单');
        }
        if ($order_info['member_id'] > 0) {
            error('暂不支持撤销会员激活卡券订单');
        }
        //先检查当前卡券是否被使用
        $coupon_send_note_guid = $order_info['coupon_send_note_guid'];
        $map                   = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_note_guid],
        ];
        $db_coupon_send_note   = new \app\model\CouponSendNote();
        $coupon_send_note_info = $db_coupon_send_note->field(['status'])->where($map)->findOrFail();
        if ($coupon_send_note_info['status'] != 0) {
            error('撤销失败,原因:当前卡券状态不是【已激活】!');
        }
        //先还原卡的激活状态为 待激活
        $update_data = [
            'status'           => -1,
            'active_time'      => NULL,
            'active_user_guid' => NULL,
        ];
        $db_coupon_send_note::update($update_data, $map);
        $total_money = $order_info['total_money'];
        if ($pay_type == 2) {
            //进行储值扣费
            $db_user_money_note = new UserMoneyNote();
            $data               = [
                'bid'       => $bid,
                'user_guid' => $this->get_user_guid(),
                'way'       => -3,// 途径 1 后台充值 2后台扣除 3 付费激活卡券扣除
                'type'      => 1, //充值
                'money'     => $total_money,
                'memo'      => '[撤销激活卡券]' . $order_info['coupon_code'],
            ];
            $db_user_money_note->recharge_money($data);
        }
        $update_data = [
            'status'           => -2,
            'revoke_time'      => format_timestamp(),
            'revoke_user_id'   => $this->get_user_id(),
            'revoke_user_guid' => $this->get_user_guid(),
        ];
        $db_coupon_active_order::update($update_data, $map_coupon_active_order);
        success('撤销成功,退回:' . $total_money . '元!');
    }

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $this->model = new \app\model\CouponActiveOrder();
        $order       = ['cao.id' => 'DESC'];
        $join        = [
            ['coupon c', 'cao.bid = c.bid AND cao.coupon_guid= c.guid'],
            ['coupon_send_note csn', 'cao.bid = csn.bid AND cao.coupon_send_note_guid= csn.guid'],
            ['user u', 'cao.bid = u.bid AND cao.user_guid= u.guid', 'LEFT'],
            ['member m', 'cao.bid = m.bid AND cao.member_guid= m.guid', 'LEFT'],
            //            ['user u2', 'csn.bid = u2.bid AND csn.owner_user_id= u2.id', 'LEFT'],
        ];
        $field       = [
            'c.name'    => 'coupon_name',
            'cao.*',
            'csn.code',
            'csn.owner_user_id',
            'u.account' => 'user_account',
            'u.name'    => 'user_name',
            'u.id'      => 'active_user_id',
            'm.id'      => 'member_id',
            //            'u2.account' => 'owner_user_account',
        ];
        $db_user     = new \app\model\User();
        $map         = [
            ['cao.bid', '=', $bid],
            ['csn.owner_user_id', 'IN', $db_user->getChildUserIdArray()], // 只显示自己范围内的订单
        ];
        $this->model = $this->model->alias('cao')->where($map)->join($join)->order($order);
        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'sum(cao.total_money)' => 'sum_total_money',
                'csn.code'             => 'code',
                'u.account'            => 'user_account',//为了防止按工号查询到的时候字段报错
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '总金额 <b style="color: red"> ' . number_format($data['sum_total_money'], 2) . '</b> 元';
            success($total_text);
        }
        $this->model = $this->model->field($field);
        result($this->_list());
    }

    public function report()
    {
        $params = $this->params;
        //        $group_field_list = $params[':group'] ?? [];
        //        unset($this->params[':group']);
        $default_field        = [
            //           'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date',
            "IFNULL(s.store_name,'')"                     => 'store_name',
            'c.name'                                      => 'coupon_name',
            "IFNULL(CONCAT(u.name,'(',u.account,')'),'')" => 'owner_user_info',
            'sum(cao.total_money)'                        => 'total_money',
            'sum(cao.active_reward_user_money)'           => 'active_reward_user_money',
            'count(cao.id)'                               => 'num',
            //'cast(sum(total_amount)/100 as decimal(9,2))'   => 'total_amount'
        ];
        $mapping              = [
            'store_name'      => ['max(s.guid)' => 'store_guid'],
            'owner_user_info' => ['max(csn.owner_user_id)' => 'owner_user_id'],
        ];
        $default_header_field = [
            'coupon_name'              => '卡券',
            'store_name'               => '门店',
            'owner_user_info'          => '卡券归属',
            'total_money'              => '订单总额',
            'active_reward_user_money' => '用户佣金',
            'num'                      => '订单数',
        ];

        $bid         = $this->get_bid();
        $db_user     = new \app\model\User();
        $map         = [
            ['cao.bid', '=', $bid],
            ['cao.status', '=', 1],
            ['csn.owner_user_id', 'IN', $db_user->getChildUserIdArray()], // 查看范围
        ];
        $this->model = new \app\model\CouponActiveOrder();
        $join        = [
            ['coupon c', 'cao.bid = c.bid AND cao.coupon_guid= c.guid'],
            ['coupon_send_note csn', 'cao.bid = csn.bid AND cao.coupon_send_note_guid= csn.guid'],
            ['user u', 'csn.bid = u.bid AND u.id= csn.owner_user_id', 'LEFT'],
            ['store s', 'u.bid = s.bid AND u.store_guid= s.guid', 'LEFT'],
        ];
        $this->model = $this->model->alias('cao')->join($join)->where($map);
        result($this->_build_report_query($mapping, $default_field, $default_header_field));
    }
}
