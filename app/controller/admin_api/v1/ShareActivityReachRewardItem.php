<?php

namespace app\controller\admin_api\v1;

use app\model\ShareActivityReachRewardItem as ShareActivityReachRewardItemModel;
use Exception;

/**
 * 分享活动达标奖励项目管理
 * Class ShareActivityReachRewardItem
 * @package app\controller\admin_api\v1
 */
class ShareActivityReachRewardItem extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new ShareActivityReachRewardItemModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $params      = $this->params;

        $map = [
            ['bid', '=', $bid],
            ['delete_time', 'NULL', NULL]
        ];

        if (!empty($params['share_activity_guid'])) {
            $map[] = ['share_activity_guid', '=', $params['share_activity_guid']];
        }

        // 排序
        $order = [
            'level'       => 'ASC',
            'create_time' => 'DESC'
        ];

        $this->model = $db->where($map)->order($order);
        result($this->_list());
    }

    /**
     * 新增
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $db     = new ShareActivityReachRewardItemModel();

        // 验证等级是否已存在
        $map = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $params['share_activity_guid']],
            ['level', '=', $params['level']],
            ['delete_time', 'NULL', NULL]
        ];

        $exists = $db->where($map)->count();
        if ($exists) {
            error('该等级的奖励已存在');
        }

        $data = array_merge($params, ['bid' => $bid]);
        $db->add($data);
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $db     = new ShareActivityReachRewardItemModel();

        // 验证等级是否已存在（排除自身）
        $map = [
            ['bid', '=', $bid],
            ['share_activity_guid', '=', $params['share_activity_guid']],
            ['level', '=', $params['level']],
            ['guid', '<>', $params['guid']],
            ['delete_time', 'NULL', NULL]
        ];

        $exists = $db->where($map)->count();
        if ($exists) {
            error('该等级的奖励已存在');
        }

        $data = array_merge($params, ['bid' => $bid]);
        $db->edit($data);
        success('修改成功');
    }

    /**
     * 删除
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        $db     = new ShareActivityReachRewardItemModel();

        $map = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']]
        ];

        $update_data = ['delete_time' => format_timestamp()];
        $db::update($update_data, $map);
        success('删除成功');
    }

    /**
     * 详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        $db     = new ShareActivityReachRewardItemModel();

        $map = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']],
            ['delete_time', 'NULL', NULL]
        ];

        $info = $db->where($map)->find();
        if (empty($info)) {
            error('数据不存在');
        }

        result($info);
    }
}
