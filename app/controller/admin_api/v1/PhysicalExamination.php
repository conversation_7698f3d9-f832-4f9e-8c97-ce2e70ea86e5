<?php

namespace app\controller\admin_api\v1;

use app\model\PhysicalExaminationNote;
use Exception;
use xieyongfa\yky\Yky;

class PhysicalExamination extends BasicAdminApi

{
    /**
     *支付二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function charge()
    {
        $params      = $this->params;
        $card_id     = $params['card_id'];
        $member_guid = $params['member_guid'];
        $bid         = $this->get_bid();
        $config      = get_config_by_bid($bid);
        $yky         = Yky::Member($config);
        $result      = $yky->Get_MemberInfo($member_guid);
        if ($result === false) {
            error($yky->message);
        }
        $member_info         = $result['data'][0];
        $member_enable_value = $member_info['EnableValue'];
        $member_name         = $member_info['TrueName'];
        if (empty($params['data'])) {
            error('请选择订单!');
        }
        $order_data           = json_decode($params['data'], true);
        $allTotalNeedPayPrice = 0;
        foreach ($order_data as $order) {
            $total_need_pay_price = $order['totalNeedPayPrice'];
            if ($total_need_pay_price <= 0) {
                error('请勿勾选无需支付的订单');
            }
            $allTotalNeedPayPrice = tools()::nc_price_calculate($allTotalNeedPayPrice, '+', $total_need_pay_price, 2);
        }
        if ($allTotalNeedPayPrice <= 0) {
            error('没有需要待支付的订单');
        }
        if ($member_enable_value < $allTotalNeedPayPrice) {
            $diff = tools()::nc_price_calculate($allTotalNeedPayPrice, '-', $member_enable_value, 2);
            error('扣费失败,会员卡号:' . $card_id . ' 余额仅剩' . $member_enable_value . '元,请先到会员系统充值' . $diff . '元以上后再进行缴费!');
        }
        $db_user      = new \app\model\User();
        $user_guid    = $this->get_user_guid();
        $user_info    = $db_user->get_user_info($user_guid, $bid);
        $user_account = $user_info['account'];
        foreach ($order_data as $key => $order) {
            $totalNeedPayPrice = $order['totalNeedPayPrice'];
            if ($totalNeedPayPrice <= 0) {
                continue;
            }
            $clinicCode = $order['clinicCode'];
//            $uniqueCode = md5(json_encode($order, JSON_UNESCAPED_UNICODE));
//            "feeDetailInfos": [
//                    {
//                        "sequenceNo": "T861869",
//                        "itemCode": "ZH3717",
//                        "itemName": "全血细胞计数+5分类检测",
//                        "price": 23,
//                        "feeFlag": "1"
//                    },
            $yky_consume  = Yky::Consume($config);
            $order_guid   = create_guid();
            $consume_data = [
                'userAccount'  => $user_account,
                //            'password'     => '888888',
                'uniqueCode'   => $order_guid,
                'cardId'       => $card_id,
                'totalMoney'   => $totalNeedPayPrice,
                'paidMoney'    => 0,
                'paidPoint'    => 0,
                'paidValue'    => $totalNeedPayPrice,
                'paidCard'     => 0,
                'totalPaid'    => $totalNeedPayPrice,
                'paidOther'    => 0,
                'otherPayType' => '',
                'meno'         => '体检交费:单号' . $clinicCode,
            ];
            $detail_data  = [];
            foreach ($order['feeDetailInfos'] as $feeDetailInfos) {
                if ($feeDetailInfos['feeFlag'] == 0) {
                    $detail_data[] = [
                        'sequenceNo' => $feeDetailInfos['sequenceNo'],
                        'itemCode'   => $feeDetailInfos['itemCode'],
                        'itemName'   => $feeDetailInfos['itemName'],
                        'price'      => $feeDetailInfos['price'],
                        'qty'        => $feeDetailInfos['qty'],
                        'totalPrice' => $feeDetailInfos['totalPrice'],
                    ];
                }
            }
            $note_data = [
                'guid'        => $order_guid,
                'bid'         => $bid,
                'user_guid'   => $user_guid,
                'detail'      => $detail_data,
                'card_id'     => $card_id,
                'total_price' => $totalNeedPayPrice,
                'clinic_code' => $clinicCode,
                'member_name' => $member_name,
                'status'      => 0
            ];
            $db        = new PhysicalExaminationNote();
            $db->save($note_data);
            $map    = [
                ['bid', '=', $bid],
                ['guid', '=', $order_guid],
            ];
            $result = $yky_consume->Consume($consume_data);
            if ($result === false) {
                $update_data = [
                    'status'  => -1,
                    'message' => $yky_consume->message,
                ];
                $db::update($update_data, $map);
                error($yky_consume->message);
            }
            //更新结果
            $response_data   = $yky_consume->getResponseData();
            $yky_bill_number = $response_data['billNumber'] ?? ($response_data['message'] ?? '未知单号');
            $update_data     = [
                'status'          => 1,
                'pay_time'        => format_timestamp(),
                'message'         => $yky_consume->message,
                'yky_bill_number' => $yky_bill_number
            ];
            $db::update($update_data, $map);
            $url    = 'http://1.183.25.138:8082/MemberBusiness/ChargeFee';
            $data   = [
                // 'memberNo' => "004001",
                'memberNo'   => $card_id,
                'memberName' => '',
                'clinicCode' => $clinicCode
            ];
            $result = curl()->post($url, $data)->get_body();
            
            // 如果不是最后一笔数据，则休眠3秒
            if ($key < count($order_data) - 1) {
                sleep(3);
            }
        }
        result($this->params, '扣费成功');
    }

    public function get_order_list()
    {
        $params  = $this->params;
        $card_id = $params['card_id'];
        $bid     = $this->get_bid();
        $config  = get_config_by_bid($bid);
        $yky     = Yky::Member($config);
        $result  = $yky->Get_MemberInfo($card_id);
        if ($result === false) {
            error($yky->message);
        }
        $member_info = $result['data'][0];
        $member_name = $member_info['TrueName'];
        $url         = 'http://1.183.25.138:8082/MemberBusiness/QueryFeeInfo';
        $data        = [
            'memberNo'   => $card_id,
            'memberName' => "",
        ];
        $result      = curl()->post($url, $data)->get_body();
        foreach ($result['data'] as $key => $val) {
            $totalPrice        = 0;
            $totalNeedPayPrice = 0;
            foreach ($val['feeDetailInfos'] as $k => $v) {
                $fee_flag                                                 = $v['feeFlag']; //0 未付款 1 已付款 2 已作废
                $item_total_price                                         = tools()::nc_price_calculate($v['qty'], '*', $v['price'], 2);
                $result['data'][$key]['feeDetailInfos'][$k]['totalPrice'] = $item_total_price;
                $totalPrice                                               = tools()::nc_price_calculate($totalPrice, '+', $item_total_price, 2);
                if ($fee_flag == 0) {
                    $totalNeedPayPrice = tools()::nc_price_calculate($totalNeedPayPrice, '+', $item_total_price, 2);;
                }
            }
            $result['data'][$key]['totalPrice']        = $totalPrice;
            $result['data'][$key]['totalNeedPayPrice'] = $totalNeedPayPrice;
            $result['data'][$key]['feeFlag']           = $totalNeedPayPrice > 0 ? 0 : 1;
//            array_sum(array_column($val['feeDetailInfos'], 'price'));
        }
        $order_list = $result['data'] ?? [];
        if (empty($order_list)) {
            error('当前卡号' . $card_id . '没有任何订单');
        }
        $name = $order_list[0]['name'];
        if (empty($member_name) && $name) {
            //更新会员信息
            $update_data = ['cardId' => $card_id, 'trueName' => $name];
            $result      = $yky->Update_Member($update_data);
//            wr_log('卡号' . $card_id . ';姓名更新为:' . $name, 1);
        }
        result(['member_info' => $member_info, 'order_list' => $order_list]);
    }

    /**
     * 修复支付失败的体检记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function repair()
    {
        $params = $this->params;
        $guid   = $params['guid'];
        $bid    = $this->get_bid();

        // 1. 查询支付失败的记录
        $db  = new PhysicalExaminationNote();
        $map = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
            ['status', '=', -1] // 只能修复支付失败的记录
        ];

        $record = $db->where($map)->findOrEmpty();
        if ($record->isEmpty()) {
            error('记录不存在或状态不正确，只能修复支付失败的记录');
        }

        // 2. 获取配置和用户信息
        $config       = get_config_by_bid($bid);
        $user_guid    = $record['user_guid'];
        $db_user      = new \app\model\User();
        $user_info    = $db_user->get_user_info($user_guid, $bid);
        $user_account = $user_info['account'];

        // 3. 重新构造扣费数据
        $yky_consume  = Yky::Consume($config);
        $consume_data = [
            'userAccount'  => $user_account,
            'cardId'       => $record['card_id'],
            'totalMoney'   => $record['total_price'],
            'paidMoney'    => 0,
            'paidPoint'    => 0,
            'paidValue'    => $record['total_price'],
            'paidCard'     => 0,
            'totalPaid'    => $record['total_price'],
            'paidOther'    => 0,
            'otherPayType' => '',
            'meno'         => '体检交费修复:单号' . $record['clinic_code'],
        ];

        // 4. 重新发起扣费
        $result = $yky_consume->Consume($consume_data);
        if ($result === false) {
            // 修复失败，记录日志
            wr_log('体检记录修复失败：' . $record['clinic_code'] . ' - ' . $yky_consume->message, true, $bid);
            error('修复失败：' . $yky_consume->message);
        }

        // 5. 修复成功，更新状态
        $response_data   = $yky_consume->getResponseData();
        $yky_bill_number = $response_data['billNumber'] ?? ($response_data['message'] ?? '未知单号');
        $update_data     = [
            'status'          => 1,
            'pay_time'        => format_timestamp(),
            'message'         => $yky_consume->message,
            'yky_bill_number' => $yky_bill_number
        ];

        $db::update($update_data, $map);

        // 6. 记录成功日志
        wr_log('体检记录修复成功：' . $record['clinic_code'] . ' - 单据号：' . $yky_bill_number, 1, $bid);

        success('修复成功，支付状态已同步');
    }
}