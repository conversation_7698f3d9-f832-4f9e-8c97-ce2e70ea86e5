<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;


use Exception;

class SystemConfig extends BasicAdminApi
{
    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        result($this->_list());
    }

    /**
     *获取配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_config()
    {
        result(get_system_config());
    }

    /**
     *获取基础配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_base_config()
    {
        $config = [
            'login_page_background' => get_system_config('login_page_background'),
            'login_page_title'      => get_system_config('login_page_title'),
        ];
        result($config);
    }
}