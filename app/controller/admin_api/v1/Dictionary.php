<?php

namespace app\controller\admin_api\v1;

use app\model\Area;
use app\model\Article;
use app\model\Coupon;
use app\model\CouponCategory;
use app\model\Express;
use app\model\ExpressChannel;
use app\model\ExpressConfig;
use app\model\GoodsCategory;
use app\model\MemberGroup;
use app\model\NotifyKeyNameList;
use app\model\ParameterDefault;
use app\model\PayChannelProvider;
use app\model\PayScene;
use app\model\Role;
use app\model\Rule;
use app\model\SmsChannel;
use app\model\User;
use app\model\Version;
use app\model\YkyCoupon;
use Exception;
use think\facade\Db;
use think\Model;
use Throwable;

class Dictionary extends BasicAdminApi
{
    public function weapp_categories()
    {
        $appid     = 'wx0dcf9a18357b9b57';
        $cache_key = $appid . ':' . __FUNCTION__;
        if (!$categories = cache($cache_key)) {
            $instance   = weixin($appid)::WeMiniAccount();
            $result     = $instance->getAllCategories();
            $categories = $result['categories_list']['categories'];
            cache($cache_key, $categories, 600);
        }
        $pid = input('pid', 0);
//        $children   = $categories[0]['children'];
        $data = [];
        foreach ($categories as $key => $val) {
            $id        = $val['id'];
            $father_id = $val['father'] ?? 0;
            $name      = $val['name'] ?? '';
            $level     = $val['level'] ?? 0;
            if ($pid == $father_id) {
                $data[] = ['id' => $id, 'name' => $name];
            }
        }
        result($data);
    }

    /**
     *模块列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function module_list()
    {
        $module_list  = [];
        $module_lists = tools()::get_current_dir(app()->getBasePath());
        if ($module_lists) {
            sort($module_lists);
            foreach ($module_lists as $module) {
                $module_list[] = [
                    'name'  => $module,
                    'value' => $module
                ];
            }
        }
        result($module_list);
    }

    /**
     *类列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function class_list()
    {
        $request     = request();
        $module_name = $request->param('module_name_str', '', null);
        $class_list  = [];
        $file_list   = tools()::scan_dir(app()->getBasePath() . $module_name . DIRECTORY_SEPARATOR . 'controller');
        if ($file_list) {
            sort($file_list);
            foreach ($file_list as $file) {
                if (is_string($file)) {
                    $class_name   = (str_replace('.php', '', basename($file)));
                    $class_list[] = [
                        'name'  => $class_name,
                        'value' => 'app\\' . $module_name . '\\controller\\' . $class_name
                    ];
                }
            }
        }
        result($class_list);
    }

    /**
     *方法列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function action_list()
    {
        $request     = request();
        $class_name  = $request->param('class_name_str', '', null);
        $action_list = [];
        $list        = tools()::get_this_class_methods($class_name);
        $class       = new $class_name;
        if (property_exists($class, 'action_list')) {
            $list = array_merge($list, array_keys($class->action_list));
        }
        sort($list);
        foreach ($list as $action) {
            $action_list[] = [
                'name'  => $action,
                'value' => $class_name . '@' . $action
            ];
        }
        result($action_list);
    }

    /**
     *快递公司
     * @access public
     * @return void
     * @throws Exception
     */
    public function express_config()
    {
        $db      = new ExpressConfig();
        $map     = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1]
        ];
        $db_user = new User();
        if (!$db_user->is_all_data_range()) {
            //查询不限制用户或者限定了当前用户可使用的电子面单模板
            $user_guid_json = json_encode([$this->get_user_guid()]);
            $map[]          = Db::raw("JSON_CONTAINS(user_guid_json,'$user_guid_json')>0 OR data_range_type = 0");
        }
        result($db->field(['guid' => 'express_config_guid', 'name'])->order(['create_time' => 'ASC'])->where($map)->select()->toArray());
    }

    /**
     *快递公司
     * @access public
     * @return void
     * @throws Exception
     */
    public function express_kuaidiniao()
    {
        $db  = new Express();
        $map = [
            //           ['status', '=', 1],
            ['kuaidiniao_code', 'not null', null],
        ];
        result($db->field(['kuaidiniao_code' => 'shipper_code', 'name'])->order(['sort' => 'ASC'])->where($map)->select()->toArray());
    }

    /**
     *快递公司
     * @access public
     * @return void
     * @throws Exception
     */
    public function express_all()
    {
        $db  = new Express();
        $map = [
            //           ['status', '=', 1]
        ];
        result($db->field(['code', 'name'])->order(['sort' => 'ASC'])->where($map)->select()->toArray());
    }

    public function express_channel()
    {
        $db     = new ExpressChannel();
        $map    = [
            //           ['status', '=', 1]
        ];
        $params = $this->params;
        if (isset($params['status'])) {
            $map[] = ['status', '=', $params['status']];
        }
        result($db->field(['id' => 'channel_id', 'name'])->order(['id' => 'ASC'])->where($map)->select()->toArray());
    }

    /**
     *快递公司
     * @access public
     * @return void
     * @throws Exception
     */
    public function express()
    {
        $db       = new Express();
        $bid      = $this->get_bid();
        $join     = [
            ['express_business b', "a.code = b.express_code AND b.bid='$bid'", 'LEFT'],
        ];
        $field    = [
            Db::raw("IFNULL(b.sort,a.sort) as sort"),
            'a.name'   => 'name',
            'a.code'   => 'code',
            'b.status' => 'express_status',
            Db::raw("IFNULL(b.status,a.status) as status"),
        ];
        $subQuery = $db->alias('a')->join($join)->field($field)->buildSql();
        $map      = [
            ['status|express_status', '=', 1]
        ];
        $result   = Db::table($subQuery . ' a')->where($map)->order(['sort' => 'ASC', 'name' => 'ASC'])->select()->toArray();
        result($result);
    }

    /**
     *地区
     * @access public
     * @return void
     * @throws Exception
     */
    public function area()
    {
        $pid = input('pid', 0);
        $db  = new Area();
        $map = [['pid', '=', $pid]];
        result($db->field(['id', 'name'])->order('id')->where($map)->select()->toArray());
    }

    /**
     *用户
     * @access public
     * @return void
     * @throws Exception
     */
    public function user()
    {
        $db    = new User();
        $map   = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
            ['delete_time', 'null', null],
            ['id', 'IN', $db->getChildUserIdArray()],
        ];
        $field = [
            Db::raw("CONCAT(account,'(',name,')') as account"),
            Db::raw("id as used_user_id"),
            Db::raw("id as owner_user_id"),
            Db::raw("id as operator_user_id"),
            'id'   => 'user_id',
            'guid' => 'user_guid',
        ];
        result($db->where($map)->field($field)->select()->toArray());
        //       result($db->where($map)->field(['account', 'guid' => 'user_guid'])->select()->toArray());
    }

    /**
     *用户
     * @access public
     * @return void
     * @throws Exception
     */
    public function store()
    {
        $db               = new \app\model\Store();
        $map              = [
            ['bid', '=', $this->get_bid()],
        ];
        $field            = [
            //           Db::raw("CONCAT(account,'(',name,')') as account"),
            //           Db::raw("id as used_user_id"),
            //           Db::raw("id as owner_user_id"),
            //           Db::raw("id as operator_user_id"),
            'store_name',
            'guid',
        ];
        $params           = $this->params;
        $data_range_limit = isset($params['data_range_limit']) && ($params['data_range_limit'] == 'true');
        if ($data_range_limit) {
            //查看范围限制,通过用户操作门店 获取行业guid array
            $db_user          = new User();
            $user_guid_array  = $db_user->getChildUserGuidArray();
            $map_user         = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'IN', $user_guid_array],
            ];
            $store_guid_array = $db_user->where($map_user)->column('store_guid');
            $map[]            = ['guid', 'IN', $store_guid_array];
        }
        result($db->where($map)->field($field)->select()->toArray());
        //       result($db->where($map)->field(['account', 'guid' => 'user_guid'])->select()->toArray());
    }

    /**
     *用户
     * @access public
     * @return void
     * @throws Exception
     */
    public function user_guid()
    {
        $db    = new User();
        $map   = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
            ['delete_time', 'null', null],
            ['id', 'IN', $db->getChildUserIdArray()],
        ];
        $field = [
            Db::raw("CONCAT(account,'(',name,')') as account"),
            'guid' => 'user_guid',
        ];
        result($db->where($map)->field($field)->select()->toArray());
        //       result($db->where($map)->field(['account', 'guid' => 'user_guid'])->select()->toArray());
    }

    /**
     *角色
     * @access public
     * @return void
     * @throws Exception
     */
    public function role()
    {
        $db  = new Role();
        $map = [
            ['bid', '=', $this->get_bid()],
            ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'role_guid'])->select()->toArray());
    }

    /**
     *商家
     * @access public
     * @return void
     * @throws Exception
     */
    public function agent_list()
    {
        $db  = new \app\model\Business();
        $map = [
            ['type', 'IN', [2, 3]],
            ['delete_time', 'NULL', NULL],
        ];
        result($db->where($map)->field(["CONCAT(account,'(',business_name,')')" => 'agent_info', 'account', 'guid'])->order(['create_time' => 'DESC'])->select()->toArray());
    }

    /**
     *商家
     * @access public
     * @return void
     * @throws Exception
     */
    public function business()
    {
        $db            = new \app\model\Business();
        $map           = [
            ['delete_time', 'null', null]
        ];
        $db_business   = new \app\model\Business();
        $business_type = $db_business->get_business_type($this->get_bid());
        if ($db_business->is_admin_type($business_type)) {

        } elseif ($db_business->is_business_type($business_type)) {
            $map[] = ['guid', '=', $this->get_bid()];
        } elseif ($db_business->is_agent_type($business_type)) {
            $map[] = ['parent_guid', '=', $this->get_bid()];
        } else {
            error('暂时不支持查询');
        }
        $field = [
            Db::raw("CONCAT(account,'(',business_name,')') as account"),
            'guid' => 'bid',
        ];
        result($db->field($field)->where($map)->order(['account' => 'ASC'])->select()->toArray());
    }

    /**
     *会员
     * @access public
     * @return void
     * @throws Exception
     */
    public function member()
    {
        $db     = new \app\model\Member();
        $params = $this->params;
        $bid    = $this->get_bid();
        $map    = [
            ['bid', '=', $bid],
        ];
        if (!empty($params['keyword'])) {
            $map[] = ['id|name|mobile', 'LIKE', '%' . $params['keyword'] . '%'];
        }
        $field = [
            Db::raw("CONCAT(id,'(', IFNULL(name,'') , '-', IFNULL(mobile,'') ,')' ) as name"),
            'guid' => 'member_guid',
        ];
        result($db->where($map)->field($field)->order(['create_time' => 'DESC'])->paginate($this->get_paginate_config())->toArray());
    }

    /**
     *用户等级
     * @access public
     * @return void
     * @throws Exception
     */
    public function member_level_1()
    {
        $db  = new \app\model\Member();
        $map = [
            ['bid', '=', $this->get_bid()],
            ['level', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'share_member_guid'])->order(['name' => 'ASC'])->select()->toArray());
    }

    /**
     *版本
     * @access public
     * @return void
     * @throws Exception
     */
    public function version()
    {
        $db            = new Version();
        $map           = [];
        $type          = input('type');
        $status        = input('status');
        $business_guid = input('business_guid');
        if ($type) {
            $map[] = ['type', '=', $type];
        }
        $list        = $db->field(['name', 'guid' => 'version_guid'])->where($map)->order(['create_time' => 'DESC']);
        $db_business = new \app\model\Business();
        if ($status) {
            if ($business_guid) {
                $business_info = $db_business->get_business_info_by_account_or_guid($business_guid);
                $where         = [['status', '=', $status], ['guid', '=', $business_info['version_guid']]];
                $list          = $list->where(function ($query) use ($where) {
                    /* @var $query Model */
                    $query->whereOr($where);
                });
            } else {
                $list = $list->where([['status', '=', $status]]);
            }
        } else {
            if ($db_business->is_agent($this->get_bid()) && $type == 1) {
                $list = $list->where([['status', '=', 1]]);
            }
        }
        $list = $list->select()->toArray();
        result($list);
    }

    /**
     *功能
     * @access public
     * @return void
     * @throws Exception
     */
    public function rule_id()
    {
        $db = new Rule();
        result($db->field(['id', 'title'])->select()->toArray());
    }

    public function rule_path()
    {
        $db = new Rule();
        result($db->field(['name', 'title'])->select()->toArray());
    }

    /**
     *功能
     * @access public
     * @return void
     * @throws Exception
     */
    public function rule_guid()
    {
        $db = new Rule();
        result($db->field(['guid', 'title'])->order(['create_time' => 'DESC'])->select()->toArray());
    }

    /**
     *功能
     * @access public
     * @return void
     * @throws Exception
     */
    public function rule_parent_guid()
    {
        $db     = new Rule();
        $list   = $db->field(['guid' => 'parent_guid', 'title'])->select()->toArray();
        $list[] = ['parent_guid' => tools()::get_empty_guid(), 'title' => '顶级菜单'];
        result($list);
    }

    /**
     *品牌
     * @access public
     * @return void
     * @throws Exception
     */
    public function tag()
    {
        $db  = new \app\model\Tag();
        $map = [['bid', '=', $this->get_bid()]];
        result($db->where($map)->field(['name', 'guid' => 'tag_guid'])->select()->toArray());
    }

    /**
     *品牌
     * @access public
     * @return void
     * @throws Exception
     */
    public function brand()
    {
        $db               = new \app\model\Brand();
        $map              = [['bid', '=', $this->get_bid()]];
        $params           = $this->params;
        $data_range_limit = isset($params['data_range_limit']) && ($params['data_range_limit'] == 'true');
        if ($data_range_limit) {
            //查看范围限制,通过用户操作门店 获取行业guid array
            $db_user          = new User();
            $user_guid_array  = $db_user->getChildUserGuidArray();
            $map_user         = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'IN', $user_guid_array],
            ];
            $store_guid_array = $db_user->where($map_user)->column('store_guid');
            $db_store         = new \app\model\Store();
            $map_store        = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'IN', $store_guid_array],
            ];
            $brand_guid_array = $db_store->where($map_store)->column('brand_guid');
            $map[]            = ['guid', 'IN', $brand_guid_array];
        }
        result($db->where($map)->field(['name', 'guid' => 'brand_guid'])->select()->toArray());
    }

    /**
     *品牌
     * @access public
     * @return void
     * @throws Exception
     */
    public function industry()
    {
        $db               = new \app\model\Industry();
        $map              = [['bid', '=', $this->get_bid()]];
        $params           = $this->params;
        $data_range_limit = isset($params['data_range_limit']) && ($params['data_range_limit'] == 'true');
        if ($data_range_limit) {
            //查看范围限制,通过用户操作门店 获取行业guid array
            $db_user             = new User();
            $user_guid_array     = $db_user->getChildUserGuidArray();
            $map_user            = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'IN', $user_guid_array],
            ];
            $store_guid_array    = $db_user->where($map_user)->column('store_guid');
            $db_store            = new \app\model\Store();
            $map_store           = [
                ['bid', '=', $this->get_bid()],
                ['guid', 'IN', $store_guid_array],
            ];
            $industry_guid_array = $db_store->where($map_store)->column('industry_guid');
            $map[]               = ['guid', 'IN', $industry_guid_array];
        }
        result($db->where($map)->field(['name', 'guid' => 'industry_guid'])->select()->toArray());
    }

    /**
     *商品类别
     * @access public
     * @return void
     * @throws Exception
     */
    public function coupon_category()
    {
        $db    = new CouponCategory();
        $bid   = $this->get_bid();
        $map   = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
        ];
        $count = $db->where([['bid', '=', $bid]])->value('id');
        if (empty($count)) {
            $data = [
                'guid'        => create_guid(),
                'parent_guid' => tools()::get_empty_guid(),
                'bid'         => $bid,
                'name'        => '礼品卡'
            ];
            $db->save($data);
        }
        result($db->where($map)->field(['name', 'guid' => 'category_guid'])->order(['sort' => 'ASC'])->select()->toArray());
    }

    /**
     *商品类别
     * @access public
     * @return void
     * @throws Exception
     */
    public function media_type()
    {
        $db  = new \app\model\MediaType();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'type_guid'])->order(['sort' => 'ASC'])->select()->toArray());
    }

    /**
     *商品
     * @access public
     * @return void
     * @throws Exception
     */
    public function goods()
    {
        $db  = new \app\model\Goods();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'goods_guid'])->order(['sort' => 'ASC', 'create_time' => 'DESC'])->select()->toArray());
    }

    /**
     *商品类别
     * @access public
     * @return void
     * @throws Exception
     */
    public function media_category()
    {
        $db  = new \app\model\MediaCategory();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'category_guid'])->order(['sort' => 'ASC'])->select()->toArray());
    }

    /**
     *商品类别
     * @access public
     * @return void
     * @throws Exception
     */
    public function goods_category()
    {
        $db     = new GoodsCategory();
        $bid    = $this->get_bid();
        $map    = [
            ['bid', '=', $bid],
            //           ['status', '=', 1],
        ];
        $params = $this->params;
        $count  = $db->where([['bid', '=', $bid]])->value('id');
        if (empty($count)) {
            $data = [
                'guid'        => create_guid(),
                'parent_guid' => tools()::get_empty_guid(),
                'bid'         => $bid,
                'name'        => '默认类别'
            ];
            $db->save($data);
        }
        $parent_guid = $params['parent_category_guid'] ?? '';
        if (!empty($parent_guid)) {
            $map[] = ['parent_guid', '=', $parent_guid];
        }
        $list = [];
        if ($parent_guid == tools()::get_empty_guid()) {
            $list[] = ['name' => '顶级分类', 'category_guid' => tools()::get_empty_guid()];
        }
        $all_list = $db->where($map)->field(['name', 'guid' => 'category_guid'])->order(['sort' => 'ASC'])->select()->toArray();
        foreach ($all_list as $key => $val) {
            $list[] = $val;
        }
        result($list);
    }

    /**
     *会员级别
     * @access public
     * @return void
     * @throws Exception
     */
    public function member_group()
    {
        $db  = new MemberGroup();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            //            ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'member_group_guid'])->select()->toArray());
    }

    public function sms_channel()
    {
        $db = new SmsChannel();
        result($db->field(['name', 'id' => 'sms_channel_id'])->order(['id' => 'ASC'])->select()->toArray());
    }

    public function user_group()
    {
        $db  = new \app\model\UserGroup();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            //            ['status', '=', 1],
        ];
        result($db->where($map)->field(['name', 'guid' => 'user_group_guid'])->select()->toArray());
    }

    /**
     *文章
     * @access public
     * @return void
     * @throws Exception
     */
    public function article()
    {
        $db  = new  Article();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid]
        ];
        result($db->where($map)->field(['title', 'guid' => 'article_guid'])->select()->toArray());
    }

    /**
     *优惠券
     * @access public
     * @return void
     * @throws Exception
     */
    public function coupon()
    {
        $db      = new Coupon();
        $bid     = $this->get_bid();
        $map     = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $db_user = new User();
        if (!$db_user->is_all_data_range()) {
            $user_guid_json = json_encode([$this->get_user_guid()]);
            $map[]          = Db::raw("JSON_CONTAINS(user_guid_json,'$user_guid_json')>0 OR data_range_type = 0");
        }
        result($db->where($map)->field(['name', 'guid' => 'coupon_guid'])->order(['sort' => 'ASC'])->select()->toArray());
    }

    /**
     *配置
     * @access public
     * @return void
     * @throws Exception
     */
    public function config_group()
    {
        $system_config_group = get_system_config('config_group');
        $data                = explode(',', $system_config_group);
        $group_array         = [];
        foreach ($data as $group) {
            $group_array[] = [
                'name' => $group,
                'type' => $group
            ];
        }
        result($group_array);

        $db          = new ParameterDefault();
        $data        = $db->distinct('group')->order(['group' => 'ASC'])->column('group');
        $group_array = [];
        foreach ($data as $group) {
            $group_array[] = [
                'name' => $group,
                'type' => $group
            ];
        }
        result($group_array);
    }

    /**
     *系统配置
     * @access public
     * @return void
     * @throws Exception
     */
    public function system_config_group()
    {
        $system_config_group = get_system_config('system_config_group');
        $data                = explode(',', $system_config_group);
        $group_array         = [];
        foreach ($data as $group) {
            $group_array[] = [
                'name' => $group,
                'type' => $group
            ];
        }
        result($group_array);
    }

    /**
     *支付通道
     * @access public
     * @return void
     * @throws Exception
     */
    public function pay_channel()
    {
        $db = new \app\model\PayChannel();
        result($db->field(['name', 'id' => 'channel_id'])->select()->toArray());
    }

    /**
     *支付通道
     * @access public
     * @return void
     * @throws Exception
     */
    public function pay_channel_provider()
    {
        $db = new PayChannelProvider();
        result($db->field(['name', 'id' => 'channel_provider_id'])->select()->toArray());
    }

    /**
     *支付通道
     * @access public
     * @return void
     * @throws Exception
     */
    public function pay_scene()
    {
        $db_pay_scene = new PayScene();
        result($db_pay_scene->field(['scene_id', 'description'])->order(['drive' => 'DESC', 'scene_id' => 'ASC'])->select()->toArray());
    }

    /**
     *一卡易优惠券
     * @access public
     * @return void
     * @throws Exception|Throwable
     */
    public function yky_coupon()
    {
        $db     = new YkyCoupon();
        $bid    = $this->get_bid();
        $map    = [['bid', '=', $bid]];
        $config = get_config_by_bid($bid);
        if ($config['openid']) {
            try {
                job()->set_sync_connections()->set_job_name('SyncYikayiNote@get_coupon')->push_job(['bid' => $bid]); //同步执行一次
            } catch (Exception $e) {
            }
        }
        $field = [
            Db::raw("CONCAT(title,'(',type_name,')') as title"),
            'guid' => 'coupon_guid'
        ];
        result($db->where($map)->field($field)->order(['title' => 'ASC'])->select()->toArray());
    }

    public function get_bid()
    {
//        $params  = $this->params;
//        $referer = $params['referer'] ?? ''; //此方法被iframe的时候不可靠 获取到的还是浏览器地址栏上URL
        $referer = $this->request->header('referer');
        if ($referer && strpos($referer, 'admin/parameter/index') !== false) {
            $url_params = tools()::parse_url_params($referer);
            if (!empty($url_params['guid'])) {
                return $url_params['guid'];
            }
        }
        return parent::get_bid();
    }

    /**
     *一卡易优惠券
     * @access public
     * @return void
     * @throws Exception
     */
    public function yky_chain_store()
    {
        $db  = new \app\model\YkyChainStore();
        $bid = $this->get_bid();
        $map = [['bid', '=', $bid]];
        result($db->where($map)->field(['store_name', 'guid' => 'chain_store_guid'])->order(['store_name' => 'ASC'])->select()->toArray());
    }

    /**
     *一卡易工号列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function yky_user()
    {
        $db  = new \app\model\YkyUser();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
        ];
        result($db->where($map)->field(["CONCAT(user_account,'(',true_name,')')" => 'true_name', 'user_account'])->order(['user_account' => 'ASC'])->select()->toArray());
    }

    /**
     *一卡易门店工号
     * @access public
     * @return void
     * @throws Exception
     */
    public function yky_chain_store_user_account()
    {
        $db               = new \app\model\YkyUser();
        $params           = $this->params;
        $chain_store_guid = $params['chain_store_guid'];
        $bid              = $this->get_bid();
        $map              = [
            ['bid', '=', $bid],
            ['chain_store_guid', '=', $chain_store_guid],
        ];
        result($db->where($map)->field(["CONCAT(user_account,'(',true_name,')')" => 'user_account_true_name', 'user_account'])->order(['user_account' => 'ASC'])->select()->toArray());
    }

    /**
     *
     * @access public
     * @return void
     * @throws Exception
     */
    public function notify_key_name_list()
    {
        $db = new NotifyKeyNameList();
        result($db->field(['title', 'key_name'])->order(['create_time' => 'DESC'])->select()->toArray());
    }

    /**
     *
     * @access public
     * @return void
     * @throws Exception
     */
    public function freight_template()
    {
        $db  = new \app\model\FreightTemplate();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
        ];
        result($db->where($map)->field(['name', 'guid' => 'freight_template_guid'])->order(['create_time' => 'DESC'])->select()->toArray());
    }
}