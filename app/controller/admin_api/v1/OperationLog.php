<?php

namespace app\controller\admin_api\v1;


use Exception;

class OperationLog extends BasicAdminApi
{
    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db  = new \app\model\OperationLog();
        $map = [];
        if (empty($this->params['create_time'])) {
            $begin_date = date('Y-m-d', strtotime('-14 days'));
            $map        = [['ol.create_time', '>', $begin_date]];
        }
        if (!empty($this->params['key']) && $this->params['key'] == 'business_account' && !empty($this->params['value'])) {
            if ($this->params['value'] != 1) {
                $db_business = new \app\model\Business();
                $bid         = $db_business->get_business_bid_by_account($this->params['value']);
                $map[]       = ['ol.bid', '=', $bid];
            }
            unset($this->params['key']);
            unset($this->params['value']);
        } else {
            $map[] = ['ol.bid', '=', $this->get_bid()];
        }
        $join        = [
            ['business b', 'ol.bid = b.guid', 'INNER'],
            ['rule r', 'ol.path = r.name', 'LEFT'],
            ['user u', 'ol.bid = u.bid AND ol.user_id = u.id', 'LEFT'],
        ];
        $field       = [
            'ol.*',
            'r.title'                                   => 'rule_name',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            'u.account'                                 => 'user_account',
            'u.name'                                    => 'user_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
        ];
        $this->model = $db->alias('ol')->join($join)->append(['params_text'])->field($field)->where($map)->order(['id' => 'DESC']);
        result($this->_list());
    }
}