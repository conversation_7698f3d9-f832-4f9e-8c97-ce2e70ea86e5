<?php

namespace app\controller\admin_api\v1;

use app\model\Goods as GoodsModel;
use app\model\GoodsTraceCodeGenerateNote as GoodsTraceCodeGenerateNoteModel;
use app\model\GoodsTraceCodeQueryNote;
use app\common\tools\Excel;

class GoodsTraceCodeGenerateNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new GoodsTraceCodeGenerateNoteModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    public function export()
    {
        $db                                = new \app\model\GoodsTraceCode();
        $bid                               = $this->get_bid();
        $join                              = [
            ['goods g', " gtc.goods_guid = g.guid AND gtc.bid = g.bid"],
        ];
        $field                             = [
            'gtc.batch',
            'gtc.code',
        ];
        $guid                              = $this->params['guid'];
        $map                               = [
            ['gtc.bid', '=', $bid],
            ['generate_guid', '=', $guid],
        ];
        $db_goods_trace_code_generate_note = new GoodsTraceCodeGenerateNoteModel();
        $map_goods_generate_note           = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $goods_guid                        = $db_goods_trace_code_generate_note->where($map_goods_generate_note)->value('goods_guid');
        $db_goods                          = new GoodsModel();
        $map_goods                         = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid],
        ];
        $code_url_field_name               = 'code_url';
        $verify_url_field_name             = 'verify_url';
        $append_field_array                = [$code_url_field_name, $verify_url_field_name];
        $data                              = $db->alias('gtc')->join($join)->where($map)->append($append_field_array)->field($field)->select()->toArray();
        if (!$data) {
            error('没有要导出的数据~');
        }
        $goods_name = $db_goods->where($map_goods)->value('name');
        $header     = [
            //           'batch'    => '批次码',
            'code'       => '溯源码',
            'code_url'   => '产品详情链接',
            'verify_url' => '查真伪链接'
        ];
        $num        = count($data);
        if (array_intersect(array_keys($header), $append_field_array)) {
            //自动替换链接
            $db_goods_trace_code = new \app\model\GoodsTraceCode();
            $url_info            = $db_goods_trace_code->get_goods_trace_url($bid);
            $long_url            = $url_info['long_url'];
            $short_url           = $url_info['short_url'];

            $verify_url_info  = $db_goods_trace_code->get_goods_trace_verify_url($bid);
            $verify_long_url  = $verify_url_info['long_url'];
            $verify_short_url = $verify_url_info['short_url'];

            foreach ($data as $key => $val) {
                foreach ($val as $k => $v) {
                    if (in_array($k, $append_field_array)) {
                        $v              = str_replace($long_url . '&', $short_url . '?', $v);
                        $v              = str_replace($verify_long_url . '&', $verify_short_url . '?', $v);
                        $data[$key][$k] = $v;
                    }
                }
            }
        }
        $file = new Excel();
        $file->arrayToExcel($header, $data, $goods_name . '(' . $num . '张)');
    }

    /**
     *删除生成记录以及关联的所有记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $password = $params['password'];
        $db_user  = new \app\model\User();
        if (!$db_user->verify_user_password($password)) {
            error('您输入的密码不正确');
        }
        $generate_note_guid  = $params['generate_guid'];
        $item                = 0;
        $bill_number         = tools()::get_bill_number();
        $db_goods_trace_code = new \app\model\GoodsTraceCode();
        $map                 = [
            ['bid', '=', $bid],
            ['generate_guid', '=', $generate_note_guid]
        ];
        $code_array          = $db_goods_trace_code->where($map)->column('code');
        //1 删除 $db_goods_trace_code_query_note
        $db_goods_trace_code_query_note = new GoodsTraceCodeQueryNote();
        $map                            = [
            ['bid', '=', $bid],
            ['code', 'IN', $code_array]
        ];
        $item                           += (int)$db_goods_trace_code_query_note->delete_to_recycle_bin($map, $bill_number);
        // 2 删除 $db_goods_trace_code
        $map  = [
            ['bid', '=', $bid],
            ['generate_guid', '=', $generate_note_guid]
        ];
        $item += (int)$db_goods_trace_code->delete_to_recycle_bin($map, $bill_number);
        // 3 删除 $db_goods_trace_code_generate_note
        $db_goods_trace_code_generate_note = new GoodsTraceCodeGenerateNoteModel();
        $map                               = [
            ['bid', '=', $bid],
            ['guid', '=', $generate_note_guid]
        ];
        $item                              += (int)$db_goods_trace_code_generate_note->delete_to_recycle_bin($map, $bill_number);
        // 提交事务
        success('数据清空成功,共计删除' . $item . '条记录!');
    }
}
