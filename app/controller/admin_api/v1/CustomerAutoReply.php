<?php

namespace app\controller\admin_api\v1;

use app\model\CustomerAutoReply as CustomerAutoReplyModel;

/**
 * 客服自动回复管理API
 */
class CustomerAutoReply extends BasicAdminApi
{
    /**
     * 获取自动回复列表
     * GET customer_auto_reply/list
     */
    public function list()
    {
        $this->model = new CustomerAutoReplyModel();
        $bid         = $this->get_bid();

        // 基础查询条件
        $map         = [['bid', '=', $bid], ['delete_time', '=', null]];
        $this->model = $this->model->where($map)->order('sort_order asc, create_time desc');
        result($this->_list());
    }

    /**
     * 创建自动回复项目
     * POST customer_auto_reply/create
     */
    public function add()
    {
        $params           = $this->request->param();
        $bid              = $this->get_bid();
        $question         = $params['question'] ?? '';
        $answer           = $params['answer'] ?? '';
        $sortOrder        = $params['sort_order'] ?? null;
        $triggerCondition = $params['trigger_condition'] ?? 3;
        $isEnabled        = $params['is_enabled'] ?? 1;

        if (empty($question) || empty($answer)) {
            error('问题内容和回答内容不能为空');
        }

        $data = [
            'bid'               => $bid,
            'question'          => $question,
            'answer'            => $answer,
            'trigger_condition' => $triggerCondition,
            'is_enabled'        => $isEnabled
        ];

        if ($sortOrder !== null) {
            $data['sort_order'] = $sortOrder;
        }

        $item = CustomerAutoReplyModel::createItem($data);

        if ($item) {
            result($item, 0, '创建成功');
        } else {
            error('创建失败');
        }
    }

    /**
     * 更新自动回复项目
     * POST customer_auto_reply/update
     */
    public function edit()
    {
        $params           = $this->request->param();
        $guid             = $params['guid'] ?? '';
        $question         = $params['question'] ?? '';
        $answer           = $params['answer'] ?? '';
        $sortOrder        = $params['sort_order'] ?? null;
        $triggerCondition = $params['trigger_condition'] ?? null;
        $isEnabled        = $params['is_enabled'] ?? null;

        if (empty($guid)) {
            error('项目ID不能为空');
        }

        $item = CustomerAutoReplyModel::where('guid', $guid)->find();
        if (!$item) {
            error('项目不存在');
        }

        $updateData = [];
        if (!empty($question)) $updateData['question'] = $question;
        if (!empty($answer)) $updateData['answer'] = $answer;
        if ($sortOrder !== null) $updateData['sort_order'] = $sortOrder;
        if ($triggerCondition !== null) $updateData['trigger_condition'] = $triggerCondition;
        if ($isEnabled !== null) $updateData['is_enabled'] = $isEnabled;

        if (empty($updateData)) {
            error('没有要更新的数据');
        }

        $result = $item->save($updateData);

        if ($result) {
            success('更新成功');
        } else {
            error('更新失败');
        }
    }

    /**
     * 删除自动回复项目
     * DELETE customer_auto_reply/delete
     */
    public function delete()
    {
        $guid = $this->request->param('guid', '');
        $bid  = $this->get_bid();

        if (empty($guid)) {
            error('项目ID不能为空');
        }

        $item = CustomerAutoReplyModel::where([
            ['guid', '=', $guid],
            ['bid', '=', $bid]
        ])->find();

        if (!$item) {
            error('项目不存在或无权限删除');
        }

        // 删除操作仅更新 delete_time    字段为当前时间，实现逻辑删除
        $item->delete_time = date('Y-m-d H:i:s.u');
        $result            = $item->save();

        if ($result) {
            success('删除成功');
        } else {
            error('删除失败');
        }
    }

    /**
     * 获取统计数据
     * GET customer_auto_reply/statistics
     */
    public function statistics()
    {
        $bid = $this->get_bid();

        // 总数统计
        $total   = CustomerAutoReplyModel::where('bid', $bid)->count();
        $enabled = CustomerAutoReplyModel::where([
            ['bid', '=', $bid],
            ['is_enabled', '=', 1]
        ])->count();

        // 热门问题
        $popular = CustomerAutoReplyModel::getPopularItems($bid, 5);

        // 总点击次数
        $totalClicks = CustomerAutoReplyModel::where('bid', $bid)->sum('click_count');

        result([
            'total'         => $total,
            'enabled'       => $enabled,
            'disabled'      => $total - $enabled,
            'total_clicks'  => $totalClicks,
            'popular_items' => $popular
        ]);
    }


    /**
     * 批量更新排序
     * POST customer_auto_reply/sort
     */
    public function sort()
    {
        $params = $this->request->param();
        $items  = $params['items'] ?? [];

        if (empty($items) || !is_array($items)) {
            error('排序数据不能为空');
        }

        try {
            foreach ($items as $item) {
                if (isset($item['guid']) && isset($item['sort_order'])) {
                    CustomerAutoReplyModel::where('guid', $item['guid'])
                        ->update(['sort_order' => $item['sort_order']]);
                }
            }

            result([], 0, '排序更新成功');
        } catch (\Exception $e) {
            error('排序更新失败：' . $e->getMessage());
        }
    }
}
