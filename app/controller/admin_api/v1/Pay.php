<?php

namespace app\controller\admin_api\v1;

use app\model\CouponActiveOrder;
use app\model\PayOrder as PayOrderModel;
use app\common\tools\Visitor;
use Exception;

class Pay extends BasicAdminApi
{
    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if ($params['bid'] != $bid) {
            error('商家标识校验不通过');
        }
        $type         = $params['type'];  //code_active  | user_batch_code_active
        $order_guid   = $params['order_guid'];
        $driver       = $params['driver'] ?? '';
        $scene        = $params['scene'] ?? '';
        $out_trade_no = '';
        $total_money  = 0;
        $options      = [];
        switch ($type) {
            case 'user_batch_code_active':
                $map                    = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid],
                ];
                $db_coupon_active_order = new CouponActiveOrder();
                $order_info             = $db_coupon_active_order->where($map)->findOrFail();
                $total_money            = $order_info['total_money'];
                $out_trade_no           = $order_info['third_order_number'];
                $map                    = [
                    ['bid', '=', $bid],
                    ['third_order_number', '=', $out_trade_no],
                    ['status', '=', -1], //待支付
                    ['pay_type', '=', 1], //微信支付

                ];
                $total_money            = $db_coupon_active_order->where($map)->sum('total_money');
                if ($total_money <= 0) {
                    error('待支付订单金额为0');
                }
                $int_total_fee = tools()::nc_price_yuan2fen($total_money);
                $options       = [
                    'bid'          => $bid,
                    'body'         => '[卡券批量激活]',
                    'way'          => 9, //卡券激活
                    'out_trade_no' => $out_trade_no,
                    'total_fee'    => $int_total_fee,
                    'openid'       => $this->get_openid(),
                    'appid'        => $this->get_appid(),
                    'job_attach'   => [
                        'class' => 'Code@user_batch_code_active_pay_success_callback',
                        'data'  => [
                            'bid'                => $bid,
                            'order_guid'         => $order_guid,
                            'bill_number'        => $out_trade_no,
                            'third_order_number' => $out_trade_no,
                        ]]
                ];
                break;
            case 'code_active':
                $map                    = [
                    ['bid', '=', $bid],
                    ['guid', '=', $order_guid],
                ];
                $db_coupon_active_order = new CouponActiveOrder();
                $order_info             = $db_coupon_active_order->where($map)->findOrFail();
                $total_money            = $order_info['total_money'];
                $out_trade_no           = $order_info['third_order_number'];
                $pay_type               = $order_info['pay_type'];
                //创建完成订单之后,需要pay下单
                switch ($pay_type) {
                    case 1:
                        //微信公众号支付,
                        $int_total_fee = tools()::nc_price_yuan2fen($total_money);
                        $options       = [
                            'bid'          => $bid,
                            'body'         => '[卡券激活]',
                            'way'          => 7, //卡券激活
                            'out_trade_no' => $out_trade_no,
                            'total_fee'    => $int_total_fee,
                            'openid'       => $this->get_openid(),
                            'appid'        => $this->get_appid(),
                            'job_attach'   => [
                                'class' => 'Code@code_active_pay_success_callback',
                                'data'  => [
                                    'bid'         => $bid,
                                    'order_guid'  => $order_guid,
                                    'bill_number' => $out_trade_no,
                                ]]
                        ];
                        $map           = [
                            ['bid', '=', $bid],
                            ['guid', '=', $order_guid]
                        ];
                        $update_data   = ['pay_type' => 1];
                        $db_coupon_active_order::update($update_data, $map);
                        break;
                    default:
                        throw new Exception('暂时不支持的支付方式');
                }
                break;
            default:
                throw new Exception('暂不支持的业务类型' . $type);
        }
        if (empty($driver) || empty($scene)) {
            if (Visitor::is_wechat_browser()) {
                $driver = 'wechat';
                $scene  = 'mp';
            } elseif (Visitor::is_alipay_browser()) {
                $driver = 'alipay';
                $scene  = 'wap';
            } else {
                error('请在微信或者支付宝中打开');
            }
        }

        $options = pay($bid)->driver($driver)->scene($scene)->apply($options);
        //下单成功后更新业务
        switch ($type) {
            case 'recharge_business_sms':
                wr_log('recharge_business_sms 下单成功', 1);
                break;
            case 'recharge_business_money':
                wr_log('recharge_business_money 下单成功', 1);
                break;
        }

        $data = [
            'pay_options'           => $options,
            'third_pay_bill_number' => $out_trade_no,
            'total_money'           => $total_money,
            'server'                => Visitor::get_user_agent(),
            'scene'                 => $scene,
        ];
        result($data);
    }

    /**
     *获取支付宝刷脸信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_alipayface_authinfo()
    {
        $params  = $this->params;
        $bid     = $this->get_bid();
        $options = [
            'zimmetainfo' => $params['zimmetainfo'],
        ];
        $result  = pay($bid)->driver('alipay')->scene('facepay')->get_alipayface_authinfo($options);
        result($result);
    }

    /**
     *获取微信刷脸信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_wxpayface_authinfo()
    {
        $params  = $this->params;
        $bid     = $this->get_bid();
        $options = [
            'store_id'   => $params['store_id'],
            'store_name' => $params['store_name'],
            'device_id'  => $params['device_id'],
            'rawdata'    => $params['rawdata'],
        ];
        $result  = pay($bid)->driver('wechat')->scene('facepay')->get_wxpayface_authinfo($options);
        result($result);
    }

    /**
     *付款码支付
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function micropay()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $out_trade_no = $params['out_trade_no'];
        $auth_code    = $params['auth_code'];
        $driver       = '';
        if (preg_match("/^(10|11|12|13|14|15)\d{16}$/", $auth_code)) {
            $driver = 'wechat'; //10~15开头18位 微信付款码
        } elseif (preg_match("/^28\d{15,16}$/", $auth_code)) {
            $driver = 'alipay'; //17位是分期码 28开头 18位支付宝付款码
        } else {
            error('付款码格式有误,请核实是否微信或支付宝付款码!');
        }
        $this->check_out_trade_no($out_trade_no);
        $device_id  = isset($params['device_id']) ? $params['device_id'] : '';
        $order_guid = create_guid();
        $ip         = tools()::get_client_ip();
        $options    = [
            'bid'              => $bid,
            'guid'             => $order_guid,
            'user_guid'        => $this->get_user_guid(),
            'body'             => '付款码支付',
            'auth_code'        => $auth_code,
            'device_id'        => $device_id,
            'spbill_create_ip' => $ip,
            'total_fee'        => (int)$params['total_fee'],
            'out_trade_no'     => $out_trade_no,
        ];
        $result     = pay($bid)->driver($driver)->scene('pos')->apply($options);
        wr_log($result);
        if ($this->isSuccess($result)) {
            $db = new PayOrderModel();
            //需要考虑支付中 ORDER_PAYING :支付中; SUCCESS:支付成功 ;PAYERROR :支付失败;REVOKED:已撤销 ;REFUND :转入退款
            $map         = [
                ['bid', '=', $bid],
                ['guid', '=', $order_guid],
                ['bill_number', '=', $out_trade_no],
            ];
            $update_data = [
                'status'         => 1,
                'buyer_id'       => isset($result['openid']) ? $result['openid'] : '', //付款码支付 更新 openid
                'third_trade_no' => $result['transaction_id'],
                'trade_time'     => tools()::format_time($result['time_end']),
                'notify_data'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
            $db::update($update_data, $map);
            $return = [
                'trade_state'     => 'SUCCESS',
                'trade_state_des' => '支付成功',
                'total_fee'       => $result['total_fee'],
                'transaction_id'  => $result['transaction_id'],
                'out_trade_no'    => $result['out_trade_no'],
                'time_end'        => $result['time_end'],
            ];
            result($return);
        } else {
            $return = [
                'out_trade_no'     => $out_trade_no,
                'trade_state'      => $result['trade_state'],
                'trade_state_desc' => $result['err_code_des'],
            ];
            result($return);
        }
    }

    /**
     *检查订单号
     * @access protected
     * @param string $out_trade_no 订单号
     * @return mixed
     * @throws Exception
     */
    protected function check_out_trade_no($out_trade_no)
    {
        if (date('Y') !== substr($out_trade_no, 0, 4)) {
            error('out_trade_no格式有误,请传入以' . date('Y') . '开头20位数字');
        }
        $time  = substr($out_trade_no, 0, 14);
        $times = tools()::format_time($time);
        $abs   = abs(time() - strtotime($times));
        if ($abs > 600) {
            error('out_trade_no 已过期:与服务器时差超过10分钟!');
        }
    }

    /**
     * 判断结果是否成功
     * @param $result
     * @return bool
     */
    protected function isSuccess($result)
    {
        if (!is_array($result)) {
            return false;
        }
        return isset($result['return_code']) && ($result['return_code'] === 'SUCCESS') && isset($result['result_code']) && ($result['result_code'] === 'SUCCESS');
    }

    /**
     *刷脸支付
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function facepay()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $out_trade_no = $params['out_trade_no'];
        $this->check_out_trade_no($out_trade_no);
        $device_id  = isset($params['device_id']) ? $params['device_id'] : '';
        $order_guid = create_guid();
        $ip         = tools()::get_client_ip();
        $options    = [
            'bid'              => $bid,
            'guid'             => $order_guid,
            'user_guid'        => $this->get_user_guid(),
            'body'             => '刷脸支付',
            'face_code'        => $params['face_code'],
            'device_id'        => $device_id,
            'openid'           => $params['openid'],
            'spbill_create_ip' => $ip,
            'total_fee'        => (int)$params['total_fee'],
            'out_trade_no'     => $out_trade_no,
        ];
        $result     = pay($bid)->driver('wechat')->scene('facepay')->apply($options);
        wr_log($result);
        if ($this->isSuccess($result)) {
            $db = new PayOrderModel();
            //需要考虑支付中 ORDER_PAYING :支付中; SUCCESS:支付成功 ;PAYERROR :支付失败;REVOKED:已撤销 ;REFUND :转入退款
            $map         = [
                ['bid', '=', $bid],
                ['guid', '=', $order_guid],
                ['bill_number', '=', $out_trade_no],
            ];
            $update_data = [
                'status'         => 1,
                'third_trade_no' => $result['transaction_id'],
                'trade_time'     => tools()::format_time($result['time_end']),
                'notify_data'    => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
            $db::update($update_data, $map);
            $return = [
                'trade_state'     => 'SUCCESS',
                'trade_state_des' => '支付成功',
                'total_fee'       => $result['total_fee'],
                'transaction_id'  => $result['transaction_id'],
                'out_trade_no'    => $result['out_trade_no'],
                'time_end'        => $result['time_end'],
            ];
            result($return);
        } else {
            $return = [
                'out_trade_no'     => $out_trade_no,
                'trade_state'      => $result['trade_state'],
                'trade_state_desc' => $result['err_code_des'],
            ];
            result($return);
        }
    }

    public function query_order()
    {
        $bill_number = $this->params['bill_no'];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['bill_number', '=', $bill_number],
        ];
        $db          = new PayOrderModel();
        $order       = $db->where($map)->order(['id' => 'DESC'])->findOrEmpty();
        if ($order->isEmpty()) {
            error('订单号不存在');
        }
        $redirect_url  = '';
        $redirect_path = '';
        $way           = $order['way'];
        switch ($way) {
            case 7: //卡券激活
                $bid             = $order['bid'];
                $job_attach_data = $order['job_attach']['data'];
                $order_guid      = $job_attach_data['order_guid'];
                //                $redirect_url    = (string)url('/member/code/code_list', ['bid' => $bid], false, true);
                //                $redirect_path   = (string)url('/pages/code_list/index', ['bid' => $bid, 'order_guid' => $order_guid], false);
                break;
            case 6: //短信充值
                break;
        }
        $data = [
            'order_data'    => $order,
            'redirect_url'  => $redirect_url,
            'redirect_path' => $redirect_path
        ];
        result($data);
    }

    /**
     *订单查询
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $out_trade_no = $params['out_trade_no'];
        $db_pay_order = new PayOrderModel();
        $result       = $db_pay_order->query_order($bid, $out_trade_no);
        if ($db_pay_order->isSuccess($result) && $result['trade_state'] == 'SUCCESS') {
            $return = [
                'trade_state'     => 'SUCCESS',
                'trade_state_des' => '支付成功',
                'total_fee'       => $result['total_fee'],
                'transaction_id'  => $result['transaction_id'],
                'out_trade_no'    => $result['out_trade_no'],
                'time_end'        => $result['time_end'],
            ];
            result($return);
        } else {
            $return = [
                'out_trade_no'     => $out_trade_no,
                'trade_state'      => $result['trade_state'],
                'trade_state_desc' => $result['err_code_des'],
            ];
            result($return);
        }
    }
}