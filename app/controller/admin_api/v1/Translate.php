<?php

namespace app\controller\admin_api\v1;

use app\model\TranslateNote;
use app\model\User;
use app\common\service\TranslateService;
use Exception;

class Translate extends BasicAdminApi
{

    /**
     *充值记录统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function report()
    {
        $db          = new TranslateNote();
        $bid         = $this->get_bid();
        $join        = [
            ['user u', 'tn.user_guid = u.guid AND tn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'u.id',
            'u.account',
            'u.name',
            'SUM(tn.length)' => 'total_length',
            'COUNT(1)'       => 'total_count',
        ];
        $db_user     = new User();
        $map         = [
            ['tn.bid', '=', $bid],
            ['tn.user_id', 'IN', $db_user->getChildUserIdArray()], // 查看范围
        ];
        $group       = ['u.account'];
        $this->model = $db->alias('tn')->join($join)->field($field)->where($map)->group($group)->order(['total_count']);
        result($this->_list());
    }

    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_config()
    {
        error('当前接口已下线,请升级客户端版本');
        $bid      = $this->get_bid();
        $params   = $this->params;
        $instance = TranslateService::get_instance($bid);
        $data     = $instance->get_config($params);
        if ($data === false) {
            error($instance->message);
        }
        result($data);
    }

    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function translate()
    {
        error('当前接口已下线,请升级客户端版本');
        $bid                 = $this->get_bid();
        $params              = $this->params;
        $instance            = TranslateService::get_instance($bid);
        $params['user_guid'] = $this->get_user_guid();
        $params['user_id']   = $this->get_user_id();
        $params['sid']       = $this->get_sid();
        $data                = $instance->translate($params);
        if ($data === false) {
            error($instance->message, -1, null, $instance->sub_code);
        }
        result($data);
    }
}