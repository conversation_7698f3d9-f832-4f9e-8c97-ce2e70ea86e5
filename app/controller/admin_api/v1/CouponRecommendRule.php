<?php

namespace app\controller\admin_api\v1;

use app\model\CouponRecommendRule as CouponRecommendRuleModel;
use Exception;

class CouponRecommendRule extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db = new CouponRecommendRuleModel();
        $this->model = $db;
        $bid = $this->get_bid();
        
        $map = [
            ['bid', '=', $bid],
        ];
        
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 新增
     * @access public
     * @return void
     * @throws Exception
     */
    public function add()
    {
        // 验证中间件已自动完成参数验证
        $params = $this->params;
        
        // 直接使用已验证的参数进行业务处理
        $db = new CouponRecommendRuleModel();
        $db->add($params);
        
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return void
     * @throws Exception
     */
    public function edit()
    {
        // 验证中间件已自动完成参数验证
        $params = $this->params;
        
        // 直接使用已验证的参数进行业务处理
        $db = new CouponRecommendRuleModel();
        $db->edit($params);
        
        success('编辑成功');
    }

    /**
     * 删除
     * @access public
     * @return void
     * @throws Exception
     */
    public function del()
    {
        // 验证中间件已自动完成参数验证
        $params = $this->params;
        
        // 直接使用已验证的参数进行业务处理（软删除）
        $db = new CouponRecommendRuleModel();
        $db->del($params);
        
        success('删除成功');
    }
}
