<?php

namespace app\controller\admin_api\v1;


use Exception;

class NotifyRecord extends BasicAdminApi
{
    /**
     *所有短信发送记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db  = new \app\model\NotifyRecord();
        $map = [];
        if (empty($this->params['create_time'])) {
            $begin_date = date('Y-m-d', strtotime('-3 days'));
            $map        = [['nr.create_time', '>', $begin_date]];
        }
        if (!empty($this->params['key']) && $this->params['key'] == 'business_account' && !empty($this->params['value'])) {
            if ($this->params['value'] != 1) {
                $db_business = new \app\model\Business();
                $bid         = $db_business->get_business_bid_by_account($this->params['value']);
                $map[]       = ['nr.bid', '=', $bid];
            }
            unset($this->params['key']);
            unset($this->params['value']);
        } else {
            $map[] = ['nr.bid', '=', $this->get_bid()];
        }
        $join        = [
            ['business b', 'nr.bid = b.guid', 'LEFT'],
            ['notify_template_default t2', 't2.id = nr.notify_template_id'],
            ['notify_key_name_list t3', 't3.key_name = t2.key_name'],
        ];
        $field       = [
            'nr.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
            't3.title'                                  => 'title',
            't2.provider'                               => 'provider',
            't2.driver'                                 => 'driver'
        ];
        $this->model = $db->alias('nr')->join($join)->field($field)->where($map);
        result($this->_list());
    }
}