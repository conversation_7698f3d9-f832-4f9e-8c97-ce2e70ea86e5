<?php

namespace app\controller\admin_api\v1;

use app\model\User;
use Exception;
use think\facade\Db;

class CouponShareNote extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $this->model = new \app\model\CouponShareNote();
        $order       = ['id' => 'DESC'];
        $join        = [
            ['coupon c', 'coupon_share_note.bid = c.bid AND coupon_share_note.coupon_guid= c.guid AND c.delete_time IS NULL',],
            ['member m', 'coupon_share_note.bid = m.bid AND coupon_share_note.share_member_guid= m.guid'],
            ['coupon_send_note csn', 'coupon_share_note.bid = csn.bid AND coupon_share_note.share_send_note_guid= csn.guid'],
            ['member m2', 'coupon_share_note.bid = m2.bid AND coupon_share_note.receive_member_guid= m2.guid', 'LEFT'],
            ['coupon_send_note csn2', 'coupon_share_note.bid = csn2.bid AND coupon_share_note.receive_send_note_guid= csn2.guid', 'LEFT'],
        ];
        $field       = [
            'c.name' => 'coupon_name',
            'coupon_share_note.*',

            Db::raw("date_format(coupon_share_note.share_time,'%Y-%m-%d %H:%i:%s') as share_time"),
            Db::raw("date_format(coupon_share_note.receive_time,'%Y-%m-%d %H:%i:%s') as receive_time"),

            'csn.code' => 'share_code',
            'm.id'     => 'share_member_id',
            'm.name'   => 'share_member_name',
            'm.mobile' => 'share_member_mobile',

            'csn2.code' => 'receive_code',
            'm2.id'     => 'receive_member_id',
            'm2.name'   => 'receive_member_name',
            'm2.mobile' => 'receive_member_mobile',

            Db::raw("date_format(csn.active_time,'%Y-%m-%d') as active_time"),


        ];
        $db_business = new \app\model\Business();
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(m.mobile,4,4,'****') as share_member_mobile");
            $field[] = Db::raw("INSERT(m2.mobile,4,4,'****') as receive_member_mobile");
        }
        $db_user     = new User();
        $map         = [
            ['coupon_share_note.bid', '=', $bid],
            //           ['cso.status', '<>', -1], //不展示待支付订单
            //           ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildArr()], // 只显示自己范围内的订单
        ];
        $this->model = $this->model->alias('coupon_share_note')->where($map)->join($join)->field($field)->order($order);
        result($this->_list());
    }
}
