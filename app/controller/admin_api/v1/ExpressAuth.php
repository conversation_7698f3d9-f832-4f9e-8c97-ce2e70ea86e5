<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\common\service\ExpressService;
use Exception;

class ExpressAuth extends BasicAdminApi

{
    /**
     *获取连接字符串
     * @access protected
     * @param string $url
     * @return mixed
     * @throws Exception
     */
    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }

    public function update()
    {
        $bid                = $this->get_bid();
        $params             = $this->params;
        $guid               = $params['guid'];
        $express_channel_id = $params['express_channel_id'];
        $express_instance   = ExpressService::get_instance($bid);
        $express            = $express_instance::get_express_instance($express_channel_id);
        $auth_note          = $express->updateAuthNote($bid, $guid, $params);
        success('更新成功');
    }

    /**
     * 小程序授权信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db                   = new \app\model\ExpressChannel();
        $url                  = $this->request->header('referer', $this->request->url(true));
        $bid                  = $this->get_bid();
        $map                  = [
            ['auth_url', '<>', '']
        ];
        $list                 = $db->where($map)->order(['id' => 'DESC'])->select();
        $express_channel_list = [];
        foreach ($list as $key => $val) {
            $express_channel_id     = $val['id'];
            $auth_url               = $val['auth_url'];
            $auth_url               = str_replace(['{BID}', '{EXPRESS_CHANNEL_ID}'], [$bid, $express_channel_id], $auth_url);
            $auth_url               .= $this->get_link_str($auth_url) . 'callback=' . urlencode($url);
            $val['auth_url']        = $auth_url;
            $express_instance       = ExpressService::get_instance($bid);
            $express                = $express_instance::get_express_instance($express_channel_id);
            $auth_note              = $express->getAuthNote($bid, $express_channel_id);
            $val['auth_note']       = $auth_note;
            $express_channel_list[] = $val;
        }
        result($express_channel_list);
    }
}