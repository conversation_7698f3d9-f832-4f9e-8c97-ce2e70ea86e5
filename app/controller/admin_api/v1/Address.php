<?php

declare(strict_types=1);

namespace app\controller\admin_api\v1;

use app\model\AddressParserNote;

/**
 * 地址解析控制器
 */
class Address extends BasicAdminApi
{
    /**
     * 自动解析地址
     * @return void
     */
    public function auto_parse()
    {
        $address_text = $this->params['address'];
        if (empty($address_text)) {
            error('请复制地址!');
        }
        $db = new AddressParserNote();
        $db->auto_parse($address_text);
    }
}
