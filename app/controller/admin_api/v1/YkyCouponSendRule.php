<?php

namespace app\controller\admin_api\v1;

use app\model\YkyCouponSendRule as YkyCouponSendRuleModel;
use Exception;

class YkyCouponSendRule extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyCouponSendRuleModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 新增
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $min_value = $params['min_value'];
        $max_value = $params['max_value'];
        if ($params['member_group_guid']) {
            $params['member_group_guid'] = explode(',', $params['member_group_guid']);
        } else {
            unset($params['member_group_guid']);
        }
        if ($params['chain_store_guid']) {
            $params['chain_store_guid'] = explode(',', $params['chain_store_guid']);
        } else {
            unset($params['chain_store_guid']);
        }
        if ($params['goods_item_guid']) {
            $params['goods_item_guid'] = explode(',', $params['goods_item_guid']);
        } else {
            unset($params['goods_item_guid']);
        }
        if ($min_value >= $max_value) {
            error('起始金额需要大于截止金额');
        }
        $db               = new YkyCouponSendRuleModel();
        $map              = [['bid', '=', $bid]];
        $exists_min_value = $db->where($map)->min('min_value', false);
        if (!is_null($exists_min_value)) {
            //如果存在起始金额再进行查询 最大金额 减少数据库查询次数
            $exists_max_value = $db->where($map)->min('max_value');
            // 必须最大值小于已存在规则的最小值 或者最小值 大于 已存在规则的最大值 这样才能说明无冲突
            if (!($max_value <= $exists_min_value || $min_value >= $exists_max_value)) {
                if (empty($params['member_group_guid']) && empty($params['chain_store_guid']) && empty($params['goods_item_guid'])) {
                    error('起始金额和截止金额和已有规则有冲突,且正在创建不限制门店和级别的规则'); // 暂时放开,允许随便创建
                }
            }
        }
        $db->add($params);
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        if ($params['member_group_guid']) {
            $params['member_group_guid'] = explode(',', $params['member_group_guid']);
        } else {
            unset($params['member_group_guid']);
        }
        if ($params['chain_store_guid']) {
            $params['chain_store_guid'] = explode(',', $params['chain_store_guid']);
        } else {
            unset($params['chain_store_guid']);
        }
        if ($params['goods_item_guid']) {
            $params['goods_item_guid'] = explode(',', $params['goods_item_guid']);
        } else {
            unset($params['goods_item_guid']);
        }
        $db = new YkyCouponSendRuleModel();
        $db->edit($params);
        success('编辑成功');
    }

    /**
     * 删除
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $db          = new YkyCouponSendRuleModel();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']],
        ];
        $update_data = ['delete_time' => format_timestamp()];
        $db::update($update_data, $map);
        success('删除成功');
    }


    /**
     * 列表
     * @access 门店列表
     * @return mixed
     * @throws Exception
     */
    public function member_group_list()
    {
        $params                  = $this->params;
        $db                      = new \app\model\YkyMemberGroup();
        $db_yky_coupon_send_rule = new YkyCouponSendRuleModel();
        $bid                     = $this->get_bid();
        $map                     = [
            ['bid', '=', $bid],
        ];
        $list                    = $db->where($map)->field(['group_name', 'guid' => 'member_group_guid'])->order(['group_name' => 'ASC'])->select()->toArray();
        $guid                    = $params['guid'] ?? '';
        if ($guid) {
            //本规则的级别默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_coupon_send_rule->where($map)->field(['member_group_guid'])->find();
            if (is_array($rule['member_group_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['member_group_guid'], $rule['member_group_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的级别禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['member_group_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['member_group_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['member_group_guid'], $val['member_group_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    /**
     * 列表
     * @access 门店列表
     * @return mixed
     * @throws Exception
     */
    public function chain_store_list()
    {
        $params                  = $this->params;
        $db                      = new \app\model\YkyChainStore();
        $bid                     = $this->get_bid();
        $map                     = [
            ['bid', '=', $bid],
        ];
        $list                    = $db->where($map)->field(['store_name', 'guid' => 'chain_store_guid'])->order(['store_name' => 'ASC'])->select()->toArray();
        $guid                    = $params['guid'] ?? '';
        $db_yky_coupon_send_rule = new YkyCouponSendRuleModel();
        if ($guid) {
            //本规则的门店默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_coupon_send_rule->where($map)->field(['chain_store_guid'])->find();
            if (is_array($rule['chain_store_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['chain_store_guid'], $rule['chain_store_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //       //不在本规则的门店禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['chain_store_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['chain_store_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['chain_store_guid'], $val['chain_store_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    /**
     * 列表
     * @access 商品列表
     * @return mixed
     * @throws Exception
     */
    public function goods_item_list()
    {
        $params                  = $this->params;
        $db                      = new \app\model\YkyGoods();
        $bid                     = $this->get_bid();
        $map                     = [
            ['bid', '=', $bid],
        ];
        $list                    = $db->where($map)->field(['name', 'guid' => 'goods_item_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid                    = $params['guid'] ?? '';
        $db_yky_coupon_send_rule = new YkyCouponSendRuleModel();
        if ($guid) {
            //本规则的门店默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->find();
            if (is_array($rule['goods_item_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['goods_item_guid'], $rule['goods_item_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }
}