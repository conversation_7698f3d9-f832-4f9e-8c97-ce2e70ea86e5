<?php

namespace app\controller\admin_api\v1;

use app\model\ParameterDefault;
use think\facade\Db;

class BusinessParameterVisible extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $bid   = $this->params['bid'];
        $db    = new ParameterDefault();
        $join  = [
            ['parameter p', "pd.key_name = p.key_name AND p.bid='$bid'", 'LEFT'],
            ['business_parameter_visible bpv', "pd.key_name = bpv.key_name AND bpv.bid='$bid'", 'LEFT'],
        ];
        $field = [
            'pd.sort'         => 'sort',
            'pd.key_name'     => 'key_name',
            'pd.title'        => 'title',
            'bpv.create_time' => 'create_time',
            'bpv.update_time' => 'update_time',
            Db::raw("IFNULL(bpv.status,0) as status"),
            Db::raw("IFNULL(bpv.bid,'$bid') as bid"),
        ];
        $map   = [
            ['pd.is_public', '=', 1],
        ];
        unset($this->params['bid']);
        $this->model = $db->alias('pd')->join($join)->where($map)->field($field)->order(['status' => 'DESC', 'sort' => 'ASC']);
        result($this->_list());
    }

    public function edit()
    {
        $params   = $this->params;
        $key_name = $params['key_name'];
        $bid      = $params['bid'];
        $status   = $params['status'];
        $db       = new \app\model\BusinessParameterVisible();
        $map      = [
            ['bid', '=', $bid],
            ['key_name', '=', $key_name],
        ];
        $info     = $db->where($map)->findOrEmpty();
        if ($info->isEmpty()) {
            if ($status == 1) {
                $data = [
                    'key_name' => $key_name,
                    'guid'     => create_guid(),
                    'bid'      => $bid,
                    'status'   => 1,
                ];
                $db->save($data);
            }
        } else {
            if ($status == 0) {
                $db->where($map)->delete();
            }
            if ($status == 1) {
                $db::update(['status' => 1], $map);
            }
        }
        success('编辑成功');
    }
}
