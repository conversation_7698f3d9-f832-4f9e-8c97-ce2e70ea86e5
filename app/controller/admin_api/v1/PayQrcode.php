<?php

namespace app\controller\admin_api\v1;

use Exception;

class PayQrcode extends BasicAdminApi
{
    /**
     *支付二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *所有支付二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function all()
    {
        $join        = [
            ['business b', 'pq.bid = b.guid'],
        ];
        $field       = [
            'pq.*',
            'b.account'       => 'business_account',
            'b.business_name' => 'business_name',
        ];
        $db          = new \app\model\PayQrcode();
        $this->model = $db->alias('pq')->join($join)->field($field)->order(['pq.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *下载支付二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function download()
    {
        $params        = $this->params;
        $bill_number   = $params['bill_number'];
        $db_pay_qrcode = new \app\model\PayQrcode();
        $map           = [
            ['bill_number', '=', $bill_number]
        ];
        $files_path    = $db_pay_qrcode->where($map)->value('download_url');
        return download($files_path, pathinfo($files_path, PATHINFO_FILENAME));
    }
}