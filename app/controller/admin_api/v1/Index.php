<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\Coupon as CouponModel;
use app\model\CouponSendNote;
use app\model\GoodsOrder;
use app\model\JuheRechargeOrder;
use app\model\Member;
use app\model\Network;
use app\model\Rule;
use app\model\UpgradeNote;
use app\model\User as UserModel;
use app\model\WeappTemplateDraftList;
use app\model\WechatRedPacketOrder;
use Exception;
use think\db\Query;
use think\facade\Db;
use think\Model;
use Throwable;

class Index extends BasicAdminApi
{
    /**
     *首页配置
     * @access public
     * @return void
     * @throws Exception
     */
    public function config()
    {
        $db_user           = new UserModel();
        $bid               = $this->get_bid();
        $user_guid         = $this->get_user_guid();
        $map               = [
            ['guid', '=', $user_guid],
            ['bid', '=', $bid]
        ];
        $user_info         = $db_user->field(['bid', 'guid', 'name', 'account', 'tel'])->where($map)->find();
        $config            = get_config_by_bid($bid);
        $home_page         = $config['home_page'];
        $db_business       = new BusinessModel();
        $all_business_info = $db_business->get_business_info_by_account_or_guid($bid);
        if (empty($home_page)) {
            //商家级配置不存在寻找 版本级别
            $db_version                = new \app\model\Version();
            $map                       = [['guid', '=', $all_business_info['version_guid']]];
            $version_default_home_page = $db_version->where($map)->value('default_home_page');
            //版本级别 配置不存在 则直接找系统参数
            $home_page = $version_default_home_page ?: get_system_config('default_home_page');
        }
        $db_rule = new Rule();
        if (!$db_user->is_admin_role_user() && $db_rule->check_rule($bid, $user_guid, Rule::HOME_PAGE) === false) {
            //        if (!$db_user->is_admin_role_user() && !$db_business->is_examples_bid($bid)) {
            //非 10000 工号 查看简单的页面
            $home_page = '/admin/index/home';
        }
        $business_info['license_status'] = $all_business_info['license_status'];
        $db                              = new \app\model\Notice();
        $notice_list                     = $db->get_notice_list();

        $renew_tips_text = '';
        $expired_time    = $all_business_info['expired_time'];
        $days            = (int)((strtotime($expired_time) - time()) / 86400);
        if ($days < 0) {
            $renew_tips_text = "您的账号已过期 " . abs($days) . " 天,请及时续期 !";
        } elseif ($days == 0) {
            $renew_tips_text = "您的账号今天到期,请及时续期 !";
        } elseif ($days <= 31) {
            $renew_tips_text = "您的账号还有 " . $days . " 天到期,请及时续期 !";
        }
        $data = [
            'debug'           => is_debug(),
            'business_info'   => $business_info,
            'user_info'       => $user_info,
            'notice_list'     => $notice_list,
            'renew_tips_text' => $renew_tips_text,
            'home_page'       => $home_page,
        ];
        result($data);
    }

    /**
     *kuxiang
     * @access public
     * @return void
     * @throws Exception
     */
    public function tihuo()
    {
        $bid             = $this->get_bid();
        $db_upgrade_note = new UpgradeNote();
        $upgrade_note    = $db_upgrade_note->get_home_upgrade_note();

        $db_business   = new BusinessModel();
        $account_info  = $db_business->get_home_account_info();
        $business_type = $db_business->get_business_type($bid);
        $is_admin      = $db_business->is_admin_type($business_type);
        $is_agent      = $db_business->is_agent_type($business_type);
        $is_business   = $db_business->is_business_type($business_type);

        $count = [];

        if ($is_admin || $is_agent) {
            $map = [
                ['license_status', '=', 2],
                ['delete_time', 'NULL', NULL],
            ];
            if ($is_agent) {
                $map[] = ['parent_guid', '=', $bid];
            }
            $total_business_count = $db_business->where($map)->count();
            $count[]              = [
                'key'   => '签约商家',
                'value' => $total_business_count,
                'url'   => '/admin/business/index',
                'title' => '商家列表'
            ];
        }
        if ($is_admin) {
            $db_weapp_template_draft_list = new WeappTemplateDraftList();
            $component_appid              = weixin()::get_component_appid();
            $map                          = [
                ['component_appid', '=', $component_appid]
            ];
            $template_draft_list_count    = $db_weapp_template_draft_list->where($map)->count();
            $count[]                      = [
                'key'   => '小程序模板',
                'value' => $template_draft_list_count,
                'url'   => '/admin/weopen/template_draft_list',
                'title' => '小程序模板'
            ];

            $db_wechat_config      = new \app\model\WechatConfig();
            $authorizer_list_count = $db_wechat_config->where($map)->count();
            $count[]               = [
                'key'   => '授权列表',
                'value' => $authorizer_list_count,
                'url'   => '/admin/wechat_config/index',
                'title' => '授权列表'
            ];
            $count[]               = [
                'key'   => '默认配置',
                'value' => '默认配置',
                'url'   => '/admin/config/index',
                'title' => '默认配置'
            ];
            $count[]               = [
                'key'   => '系统配置',
                'value' => '系统配置',
                'url'   => '/admin/system_config/index',
                'title' => '系统配置'
            ];
            $count[]               = [
                'key'   => '定时任务',
                'value' => '定时任务',
                'url'   => '/admin/crontab/index',
                'title' => '定时任务'
            ];
        }

        $db_user       = new UserModel();
        $child_user_id = $db_user->getChildUserIdArray();
        $db_rule       = new Rule();
        $user_info     = $db_user->get_user_info($this->get_user_guid());
        // 判断是否超级超管理
        $is_admin_user = $db_rule->is_admin_by_role_guid($user_info['role_guid'], $bid);

        if ($is_business) {

            $db_coupon  = new CouponModel();
            $map_coupon = [
                ['bid', '=', $this->get_bid()],
                ['operator_user_id', 'IN', $child_user_id],
            ];
            $coupon_num = $db_coupon->where($map_coupon)->count();

            $db_goods  = new \app\model\Goods();
            $map_goods = [
                ['bid', '=', $bid],
                ['create_user_id|owner_user_id', 'IN', $child_user_id]
            ];

            $goods_num = $db_goods->where($map_goods)->count();

            $db_coupon_send_note  = new CouponSendNote();
            $join                 = [
                ['coupon c', 'csn.bid = c.bid AND csn.coupon_guid= c.guid'],
            ];
            $map_coupon_send_note = [
                ['csn.bid', '=', $bid],
                ['csn.delete_time', 'null', null],
                ['c.delete_time', 'null', null],
                //                ['csn.status', '<>', -1],
                ['csn.owner_user_id', 'IN', $child_user_id],
            ];
            $coupon_send_num      = $db_coupon_send_note->alias('csn')->join($join)->where($map_coupon_send_note)->count();

            $db_goods_order   = new GoodsOrder();
            $join             = [
                ['coupon c', 'go.bid = c.bid AND go.coupon_guid= c.guid', 'LEFT'],
                ['coupon_send_note csn', 'go.bid = csn.bid AND go.coupon_send_note_guid= csn.guid', 'LEFT'],
                //           ['area a1', 'go.province_id = a1.id', 'LEFT'],
                //           ['area a2', 'go.city_id = a2.id', 'LEFT'],
                //           ['area a3', 'go.area_id = a3.id', 'LEFT'],
            ];
            $today_begin_time = date('Y-m-d 00:00:00');
            $map              = [
                ['go.bid', '=', $bid],
                ['go.status', 'NOT IN', [-1, -2, -3]], // 不展示待支付,已取消,已退款订单
                ['go.create_time', '>', $today_begin_time],
                ['go.delete_time', 'null', null],
                ['csn.owner_user_id|go.owner_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
            ];
            $goods_order_num  = $db_goods_order->alias('go')->where($map)->join($join)->count();

            $map                  = [
                ['go.bid', '=', $bid],
                ['go.status', '=', 0], //不展示待支付订单
                ['go.delete_time', 'NULL', null], //过滤已删除订单
                ['csn.owner_user_id|go.owner_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
            ];
            $unfinished_order_num = $db_goods_order->alias('go')->where($map)->join($join)->count();


            $count[] = [
                'key'   => '今日订单数',
                'value' => $goods_order_num,
                'url'   => '/admin/goods_order/index',
                'title' => '订单列表'
            ];

            $map                  = [
                ['go.bid', '=', $bid],
                ['go.paid_wechat|go.paid_money', '>', 0], //不展示待支付订单
            ];
            $has_wechat_pay_order = $db_goods_order->alias('go')->where($map)->value('id');
            if ($has_wechat_pay_order) {
                $map        = [
                    ['go.bid', '=', $bid],
                    ['go.status', '<>', -1], //不展示待支付订单
                    ['go.pay_time', '>', $today_begin_time],
                    ['go.paid_wechat|go.paid_money', '>', 0],
                    ['go.owner_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
                ];
                $today_paid = $db_goods_order->alias('go')->where($map)->sum(Db::raw('paid_wechat + paid_money'));
                $count[]    = [
                    'key'   => '今日实收(元)',
                    'value' => $today_paid,
                    'url'   => '/admin/goods_order/index',
                    'title' => '订单列表'
                ];
            }
            array_push($count,
                [
                    'key'   => '待发货订单',
                    'value' => $unfinished_order_num,
                    'url'   => '/admin/goods_order/index?status=0',
                    'title' => '订单列表'
                ],
                [
                    'key'   => '产品数量',
                    'value' => $goods_num,
                    'url'   => '/admin/goods/index',
                    'title' => '产品列表'
                ],
                [
                    'key'   => '卡密数量',
                    'value' => $coupon_send_num,
                    'url'   => '/admin/coupon_send_note/index',
                    'title' => '卡密列表'
                ]
            );
            if ($is_admin_user) {
                $count[] =
                    [
                        'key'   => '卡券数量',
                        'value' => $coupon_num,
                        'url'   => '/admin/code/index',
                        'title' => '卡券列表'
                    ];
            }

            $db_member_distributor_apply_note = new \app\model\MemberDistributorApplyNote();

            $map                   = [
                ['bid', '=', $bid],
                ['status', '=', 0], // 待审核分销订单
            ];
            $distributor_apply_num = $db_member_distributor_apply_note->where($map)->count();
            if ($distributor_apply_num > 0) {
                $count[] = [
                    'key'   => '待审核分销',
                    'value' => $distributor_apply_num,
                    'url'   => '/admin/member_distributor_apply_note/index',
                    'title' => '分销申请'
                ];
            }
        }
        $url_list = [];
        if ($is_business) {
            try {
                $db_coupon              = new CouponModel();
                $verify_url_qrcode_list = $db_coupon->get_verify_url_qrcode_list($bid);
                $long_url               = $verify_url_qrcode_list['long_url'];
                $short_url              = $verify_url_qrcode_list['short_url'];
                $qrcode_url             = $verify_url_qrcode_list['qrcode_url'];
                $download_url           = $verify_url_qrcode_list['download_url'];
                $url_list[]             = [
                    'name'         => '提货码',
                    'qrcode_url'   => $qrcode_url,
                    'long_url'     => $long_url,
                    'short_url'    => $short_url,
                    'download_url' => $download_url,
                ];
            } catch (Throwable) {
            }

            //            $bid                                           = $this->get_bid();
            //            $user_guid                                     = $this->get_user_guid();
            //            $db_rule                                       = new Rule();
            //            $has_goods_trace_code_generate_note_index_auth = $db_rule->checkRule($bid, $user_guid, Rule::GOODS_TRACE_CODE_GENERATE_NOTE_INDEX);
            //            if ($has_goods_trace_code_generate_note_index_auth) {
            //                $path         = 'member/goods_trace/index';
            //                $db_short_url = new ShortUrl();
            //                $url_info     = $db_short_url->get_url_with_qrcode($path, ['bid' => $bid]);
            //                $long_url     = $url_info['long_url'];
            //                $short_url    = $url_info['short_url'];
            //                $qrcode_url   = $url_info['qrcode_url'];
            //                $download_url = $url_info['download_url'];
            //                $url_list[]   = [
            //                    'name'         => '溯源码',
            //                    'qrcode_url'   => $qrcode_url,
            //                    'long_url'     => $long_url,
            //                    'short_url'    => $short_url,
            //                    'download_url' => $download_url,
            //                ];
            //            }
        }
        //检测密码
        $check_password = $db_user->check_password($user_info);
        $business_list  = [];
        if ($is_admin || $is_agent) {
            $db_goods_order = new GoodsOrder();
            $create_time    = date('Y-m-d 00:00:00');
            $map            = [
                ['go.create_time', '>', $create_time]
            ];
            if ($is_agent) {
                $map[] = ['b.parent_guid', '=', $bid];
            }
            $join          = [
                ['business b', 'go.bid = b.guid']
            ];
            $field         = [
                'b.id'            => 'sid',
                'b.business_name' => 'business_name',
                'count(1)'        => 'count',
            ];
            $group         = [
                'business_name'
            ];
            $order         = [
                'count' => 'DESC'
            ];
            $business_list = $db_goods_order->alias('go')->cache(true, 600)->join($join)->field($field)->where($map)->order($order)->group($group)->limit(10)->select();
        }
        $data = [
            'is_admin_user'  => (int)$is_admin_user,
            'account_info'   => $account_info,
            'user_info'      => $user_info,
            'upgrade_log'    => $upgrade_note,
            'count'          => $count,
            'url_list'       => $url_list,
            'check_password' => $check_password,
            'business_type'  => $business_type,
            'business_list'  => $business_list
        ];
        result($data);
    }

    /**
     *kuxiang
     * @access public
     * @return void
     * @throws Exception
     */
    public function kuxiang()
    {
        $bid             = $this->get_bid();
        $money           = 2980;
        $db_upgrade_note = new UpgradeNote();
        $upgrade_note    = $db_upgrade_note->get_home_upgrade_note();
        $db_business     = new BusinessModel();
        $account_info    = $db_business->get_home_account_info();
        $map             = [
            ['parent_guid', '=', $bid]
        ];
        $total           = $db_business->where($map)->count();
        $map             = [
            ['parent_guid', '=', $bid],
            ['license_status', '=', 2]
        ];
        $deal_total      = $db_business->where($map)->count();
        $count           = [
            '累计余额' => $account_info['total_license_num'] * $money,
            '可用余额' => $account_info['license_num'] * $money,
            '累计商户' => $total,
            '签约商户' => $deal_total,
            '试用中'   => $total - $deal_total,
        ];
        $data            = [
            'account_info' => $account_info,
            'upgrade_log'  => $upgrade_note,
            'count'        => $count
        ];
        result($data);
    }

    /**
     *kuxiang代理
     * @access public
     * @return void
     * @throws Exception
     */
    public function kuxiang_agent()
    {
        $db_upgrade_note = new UpgradeNote();
        $upgrade_note    = $db_upgrade_note->get_home_upgrade_note();
        $db_business     = new BusinessModel();
        $account_info    = $db_business->get_home_account_info();
        $login           = new \OpenApi\AgentWeb();
        $result          = $login->get_welcome_info();

        $count = [
            '虚拟余额' => 50000,
            '真实余额' => $result['total']['my_balance'],
            '商户数'   => $result['total']['my_merchant_count'],
        ];
        $data  = [
            'account_info' => $account_info,
            'upgrade_log'  => $upgrade_note,
            'count'        => $count
        ];
        result($data);
    }

    /**
     *home
     * @access public
     * @return void
     * @throws Exception
     */
    public function home()
    {
        $db_upgrade_note = new UpgradeNote();
        $upgrade_note    = $db_upgrade_note->get_home_upgrade_note();
        $db_business     = new BusinessModel();
        $account_info    = $db_business->get_home_account_info();
        //统计会员数
        $db_member          = new Member();
        $today_member_count = $db_member->whereDay('create_time')->count();
        $total_member_count = $db_member->count();
        //统计聚合订单
        $db_juhe        = new JuheRechargeOrder();
        $field          = ['count(1)' => 'total_count', 'sum(value)' => 'total_value'];
        $today_recharge = $db_juhe->field($field)->whereDay('create_time')->findOrEmpty();
        $total_recharge = $db_juhe->field($field)->findOrEmpty();
        //统计红包个数

        $db_red_pack    = new WechatRedPacketOrder();
        $field          = ['count(1)' => 'total_count', 'convert(sum(total_amount)/100,signed)' => 'total_value'];
        $today_red_pack = $db_red_pack->field($field)->whereDay('create_time')->findOrEmpty();
        // $total_red_pack = $db_red_pack->field($field)->findOrEmpty();
        $count   = [
            '今日会员'     => $today_member_count,
            '累计会员'     => $total_member_count,
            '今日充值笔数' => (int)$today_recharge['total_count'],
            '今日充值金额' => $today_recharge['total_value'] ?? 0,
            '累计充值笔数' => (int)$total_recharge['total_count'],
            '累计充值金额' => $total_recharge['total_value'] ?? 0,
            '今日红包笔数' => (int)$today_red_pack['total_count'],
            '今日红包金额' => $today_red_pack['total_value'] ?? 0,
        ];
        $db_rule = new Rule();
        $menu    = $db_rule->get_menus_by_bid_user_guid();
        $menu    = array_chunk($menu, 8);
        $data    = [
            'account_info' => $account_info,
            'upgrade_log'  => $upgrade_note,
            'count'        => $count,
            'menu'         => $menu
        ];
        result($data);
    }

    /*
         * @return mixed
     */
    public function tihuo_report()
    {
        $db_user        = new UserModel();
        $child_user_id  = $db_user->getChildUserIdArray();
        $bid            = $this->get_bid();
        $db_goods_order = new GoodsOrder();
        $join           = [
//            ['coupon c', 'go.bid = c.bid AND go.coupon_guid= c.guid', 'LEFT'],
        ];
        $db_business    = new BusinessModel();
        $map            = [
            ['go.status', '<>', -1], //不展示待支付订单
            ['go.create_time', '>=', Db::raw(' NOW() - interval 10 day')]
        ];
        if ($db_business->is_business()) {
            $join[] = ['coupon_send_note csn', 'go.bid = csn.bid AND go.coupon_send_note_guid= csn.guid', 'LEFT'];
            $map[]  = ['go.bid', '=', $bid];
            $map[]  = ['csn.owner_user_id|go.owner_user_id', 'IN', $child_user_id];  // 只显示自己范围内的订单
        } elseif ($db_business->is_agent()) {
            $join[] = ['business b', 'go.bid = b.guid'];
            $map[]  = ['b.parent_guid', '=', $bid];
        }
        $db_goods_order = $db_goods_order->alias('go')->join($join);
        $field          = [
            'CONCAT(DATE_FORMAT(go.create_time, "%m月%d日" ),"")' => '日期',
            //            'CONCAT(DATE_FORMAT(go.create_time, "%y年%m月%d日" ),"")' => '日期',
            'count(1)'                                            => '订单数',
        ];
        //       $db    = new Member();
        //       $map   = [
        //           //  ['bid', '=', $this->get_bid()],
        //       ];
        //       $field = [
        //           'CONCAT(DATE_FORMAT(create_time,"%Y-%m-%d" ),"")' => '日期',
        //           'count(1)'                                        => '数量',
        //       ];
        result($this->build_echart_data($db_goods_order, $map, $field, 14));
    }

    /**
     *网络情况
     * @access public
     * @return void
     * @throws Exception
     */
    public function network()
    {
        $db    = new Network();
        $map   = [
            ['time_to_first_byte', '>', 0],
            ['time_to_response', '>', 0],
            ['time_to_dns', '>', 0],
            ['create_time', '>=', Db::raw(' NOW() - interval 10 day')]
        ];
        $field = [
            'CONCAT(DATE_FORMAT(create_time, "%d日%H点" ),"")' => '时间',
            'count(1)'                                         => '访问数',
            'convert(avg(time_to_dns),signed)'                 => '平均DNS解析时间',
            'convert(max(time_to_dns),signed)'                 => '最大DNS解析时间',
            'convert(avg(time_to_first_byte),signed)'          => '平均首字节响应时间',
            'convert(max(time_to_first_byte),signed)'          => '最大首字节响应时间',
            'convert(avg(time_to_response),signed)'            => '平均服务器处理时间',
            'convert(max(time_to_response),signed)'            => '最大服务器处理时间',
        ];
        //       $db    = new Member();
        //       $map   = [
        //           //  ['bid', '=', $this->get_bid()],
        //       ];
        //       $field = [
        //           'CONCAT(DATE_FORMAT(create_time,"%Y-%m-%d" ),"")' => '日期',
        //           'count(1)'                                        => '数量',
        //       ];
        result($this->build_echart_data($db, $map, $field, 24));
    }

    /**
     *
     * @param Model|Query $db
     * @param array $map 条件
     * @param array $fields 字段
     * @param int $limit 条数
     * @param string $order 排序规则
     * @return array
     * @throws Exception
     */
    public function build_echart_data($db, $map, $fields, $limit = 7, $order = 'DESC')
    {
        $order_field   = reset($fields);
        $fields_string = '';
        foreach ($fields as $key => $val) {
            $fields_string .= $key . ' AS ' . $val . ',';
        }
        $fields_string = rtrim($fields_string, ',');
        $result        = $db->where($map)->cache(true, 600)->field($fields_string)->group(key($fields))->orderRaw($order_field . ' ' . $order)->limit($limit)->select();
        if ($result->isEmpty()) {
            return [];
        }
        $result                         = tools()::object2array($result);
        $result                         = array_reverse($result);
        $default_data                   = [
            'title'   => [
                'text'    => '数据统计',
                'subtext' => '最近' . $limit . '条',
            ],
            'tooltip' => ['trigger' => 'axis',],
            'legend'  => [
                //'data' => [],
            ],
            'toolbox' => [
                'show'       => true,
                'calculable' => true,
                //               'feature'    => [
                //                   'mark'        => ['show' => true,],
                //                   'dataView'    => [
                //                       'show'     => true,
                //                       'readOnly' => false,
                //                   ],
                //                   'magicType'   => [
                //                       'show' => true,
                //                       'type' => ['line', 'bar', 'stack', 'tiled'],
                //                   ],
                //                   'restore'     => ['show' => true,],
                //                   'saveAsImage' => ['show' => true,],
                //               ],
            ],
            'xAxis'   => [
                'type'        => 'category',
                'boundaryGap' => false,
                // 'data'        => ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                //'data'        => [],
            ],
            'yAxis'   => [
                'type' => 'value',
            ],
            'series'  => []
        ];
        $fields_array                   = array_keys($result[0]);
        $group_by_field                 = array_shift($fields_array);
        $default_data['legend']['data'] = $fields_array;
        $default_data['xAxis']['data']  = array_column($result, $group_by_field);
        foreach ($fields_array as $field) {
            $default_data['series'][] = [
                'name'      => $field,
                'type'      => 'line',
                'smooth'    => true,
                'itemStyle' => ['normal' => ['areaStyle' => ['type' => 'default']]],
                'data'      => array_column($result, $field),
            ];
        }
        return $default_data;
    }

    /**
     *统计
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function total()
    {
        return $this->tihuo_report();
        return $this->network();
        $db    = new \app\model\PayOrder();
        $map   = [
            ['status', '=', 1],
            ['create_time', '>=', Db::raw(' NOW() - interval 7 day')]
        ];
        $field = [
            'CONCAT(DATE_FORMAT(create_time, "%m月%d日" ),"")' => '时间',
            'count(1)'                                         => '支付笔数',
            '0+cast(sum(total_fee)/100 as char)'               => '支付总额',
            //           'convert(avg(time_to_dns),signed)'               => '平均DNS解析时间',
            //           'convert(max(time_to_dns),signed)'               => '最大DNS解析时间',
            //           'convert(avg(time_to_first_byte),signed)'        => '平均首字节响应时间',
            //           'convert(max(time_to_first_byte),signed)'        => '最大首字节响应时间',
            //           'convert(avg(time_to_response),signed)'          => '平均服务器处理时间',
            //           'convert(max(time_to_response),signed)'          => '最大服务器处理时间',
        ];
        //       $db    = new Member();
        //       $map   = [
        //           //  ['bid', '=', $this->get_bid()],
        //       ];
        //       $field = [
        //           'CONCAT(DATE_FORMAT(create_time,"%Y-%m-%d" ),"")' => '日期',
        //           'count(1)'                                        => '数量',
        //       ];
        result($this->build_echart_data($db, $map, $field, 24));
    }

}
