<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\PayScene;
use Exception;

class PayChannelProvider extends BasicAdminApi
{
    /**
     *获取支付通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get()
    {
        $db                    = new \app\model\PayChannelProvider();
        $map                   = [['status', '=', 1]];
        $data['provider_list'] = $db->where($map)->select();
        $db_pay_scene          = new PayScene();
        $scene                 = $db_pay_scene->order(['scene_id' => 'ASC'])->select();
        $data['scene']         = $scene;
        result($data);
    }
}