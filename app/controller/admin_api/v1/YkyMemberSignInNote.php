<?php

namespace app\controller\admin_api\v1;

use app\model\YkyMemberSignInActivity as YkyMemberSignInActivityModel;
use app\model\YkyMemberSignInNote as YkyMemberSignInNoteModel;
use think\facade\Db;
use xieyongfa\yky\Yky;

class YkyMemberSignInNote extends BasicAdminApi
{
    public function get_info()
    {
        $params  = $this->params;
        $card_id = $params['code'];
        $bid     = $this->get_bid();
        $config  = get_config_by_bid($bid);
        $yky     = Yky::Member($config);
        $result  = $yky->Get_MemberInfo($card_id);
        if ($result === false) {
            error($yky->message);
        }
        $member_info                          = $result['data'][0];
        $member_group_guid                    = $member_info['MemberGroupGuid'];
        $db_yky_member_sign_in_activity_model = new YkyMemberSignInActivityModel();
        $map                                  = [
            ['bid', '=', $bid]
        ];
        $member_group_guid_json               = json_encode([$member_group_guid]);
        $empty_guid                           = '';
        $empty_guid_json                      = json_encode([$empty_guid]);
        $map[]                                = Db::raw("JSON_CONTAINS(member_group_guid,'$member_group_guid_json')>0 OR JSON_CONTAINS(member_group_guid,'$empty_guid_json')>0 OR member_group_guid IS NULL");
        $activity_info                        = $db_yky_member_sign_in_activity_model->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
        if ($activity_info->isEmpty()) {
            error('当前级别没有签到活动');
        }
        result(['member_info' => $member_info, 'activity_info' => $activity_info]);
    }

    public function get_last_note_list()
    {
        $db   = new YkyMemberSignInNoteModel();
        $map  = [['bid', '=', $this->get_bid()]];
        $list = $db->where($map)->order(['create_time' => 'DESC'])->limit(10)->select();
        result($list);
    }

    public function add()
    {
        $params                           = $this->params;
        $bid                              = $this->get_bid();
        $activity_guid                    = $params['activity_guid'];
        $card_id                          = $params['code'];
        $member_guid                      = $params['member_guid'];
        $db_yky_member_sign_in_note_model = new YkyMemberSignInNoteModel();
        $begin_time                       = date('Y-m-d 00:00:00');
        $map                              = [
            ['bid', '=', $bid],
            ['create_time', '>', $begin_time],
            ['yky_member_guid', '=', $member_guid],
        ];
        $count                            = $db_yky_member_sign_in_note_model->where($map)->value('id');
        if ($count) {
            error('今日已签到过,请勿重复签到!');
        }
        $db_yky_member_sign_in_activity_model = new YkyMemberSignInActivityModel();
        $map                                  = [
            ['bid', '=', $bid],
            ['guid', '=', $activity_guid],
        ];
        $activity_info                        = $db_yky_member_sign_in_activity_model->where($map)->findOrFail();
        $point                                = $activity_info['value'];
        $db_user                              = new \app\model\User();
        $user_guid                            = $this->get_user_guid();
        $user_info                            = $db_user->get_user_info($user_guid, $bid);
        $user_account                         = $user_info['account'];
        $config                               = get_config_by_bid($bid);
        $yky_point                            = Yky::Point($config);
        $post_data                            = [
            'cardId'      => $card_id,
            'point'       => $point,
            'userAccount' => $user_account,
            'meno'        => $activity_info['name'] . '【签到送积分】',
        ];
        $result                               = $yky_point->Update_MemberPoint($post_data);
        if ($result === false) {
            error($yky_point->message);
        }
        $point_bill_number = $result['message'];
        $insert_data       = [
            'guid'               => create_guid(),
            'third_bill_number'  => $point_bill_number,
            'yky_member_card_id' => $card_id,
            'yky_member_guid'    => $member_guid,
            'point'              => $point,
            'activity_guid'      => $activity_guid,
            'user_guid'          => $this->get_user_guid(),
            'status'             => 1,
            'bid'                => $bid,
        ];
        $db_yky_member_sign_in_note_model->save($insert_data);
        success('签到成功');
    }

    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new YkyMemberSignInNoteModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }
}
