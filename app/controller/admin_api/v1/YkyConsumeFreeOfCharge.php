<?php

namespace app\controller\admin_api\v1;

use app\model\YkyConsumeFreeOfCharge as YkyConsumeFreeOfChargeModel;

class YkyConsumeFreeOfCharge extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new YkyConsumeFreeOfChargeModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    public function undo()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (empty($params['id'])) {
            error('参数错误');
        }
        $id_array    = explode(',', $params['id']);
        $db          = new YkyConsumeFreeOfChargeModel();
        $map         = [
            ['id', 'IN', $id_array],
            ['status', '=', 0],
            ['bid', '=', $bid],
        ];
        $update_data = ['status' => -1, 'undo_time' => format_timestamp()];
        $db::update($update_data, $map);
        $map                   = [['id', 'IN', $id_array], ['bid', '=', $bid]];
        $yky_member_guid_array = $db->where($map)->column('yky_member_guid');
        $yky_member_guid_array = array_unique($yky_member_guid_array);
        foreach ($yky_member_guid_array as $yky_member_guid) {
            $db->update_member_data($bid, $yky_member_guid);
        }
        success('撤销成功');
    }
}
