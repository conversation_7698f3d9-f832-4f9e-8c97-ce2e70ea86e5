<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use Exception;

class Page extends BasicAdminApi
{

    /**
     * 所有页面
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->model = new \app\model\Page();
        $this->model = $this->model->append(['web_url']);
        result($this->_list());
    }

    /**
     * 我的页面
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my_page()
    {
        $this->model           = new \app\model\Page();
        $db_business           = new BusinessModel();
        $business_info         = $db_business->get_business_info_by_account_or_guid($this->get_bid());
        $business_version_guid = $business_info['version_guid'];
        $db_user               = new \app\model\User();
        $rule_array            = $db_user->get_user_rule_guid_array($this->get_user_guid(), $this->get_bid());
        $map1                  = [['version_guid', '=', $business_version_guid]];
        $map2                  = [
            ['rule_guid', '=', ''],
            ['version_guid', '=', ''],
        ];
        $map3                  = [['rule_guid', 'IN', $rule_array]];
        $this->model           = $this->model->whereOr([$map1, $map2, $map3])->append(['web_url']);
        result($this->_list());
    }
}
