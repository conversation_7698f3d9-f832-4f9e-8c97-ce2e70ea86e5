<?php

namespace app\controller\admin_api\v1;

use app\model\CouponCashNote as CouponCashNoteModel;
use app\model\UserMoneyCashNote as UserMoneyCashNoteModel;
use app\common\service\NotifyService;
use Exception;

class CouponCashNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $db          = new CouponCashNoteModel();
        $bid         = $this->get_bid();
        $db_user     = new \app\model\User();
        $map         = [
            ['csn.owner_user_id', 'in', $db_user->getChildUserIdArray()],
            ['csn.bid', '=', $bid],
            ['csn.delete_time', 'null', null],
        ];
        $join        = [
            ['coupon_send_note csn', 'ccn.coupon_send_guid = csn.guid AND ccn.bid = csn.bid'],
            ['user u', 'ccn.operator_user_guid = u.guid AND ccn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'ccn.*',
            'csn.code',
            "CONCAT(u.account,'-',u.name)" => 'user_info',
        ];
        $this->model = $db->alias('ccn')->order(['ccn.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        result($this->_list());
    }

    /**
     *订单详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $guid       = $params['guid'];
        $db         = new CouponCashNoteModel();
        $map        = [
            ['ccn.bid', '=', $bid],
            ['ccn.guid', '=', $guid],
        ];
        $join       = [
            ['coupon_send_note csn', 'ccn.coupon_send_guid = csn.guid AND ccn.bid = csn.bid'],
        ];
        $field      = [
            'ccn.*',
            'csn.code',
        ];
        $order_info = $db->alias('ccn')->field($field)->join($join)->where($map)->findOrFail();
        result($order_info);
    }

    /**
     *审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $guid                 = $params['guid'];
        $db_coupon_cash_note  = new CouponCashNoteModel();
        $map_coupon_cash_note = [
            ['guid', '=', $guid],
            ['bid', '=', $bid],
            ['status', '=', 0],
        ];
        $update_data          = [
            'status'             => 2,
            'operator_user_guid' => $this->get_user_guid(),
            'examine_time'       => format_timestamp()
        ];
        $db_coupon_cash_note::update($update_data, $map_coupon_cash_note);

//        $data = [
//            'url'         => '',
//            'title'       => '',
//            'name'        => '【资金提现申请通过】', //流程名称
//            'create_time' => format_timestamp(),  //操作时间
//            'user'        => '您发起的资金提现已经审核通过,请留意到账', //操作人员
//            'detail'      => '提现金额:' . $cash_money, //流程摘要
//            'remark'      => '',
//        ];
//        notify()->set_key_name(NotifyService::Notice)->limit_business()->set_user_guid([$user_guid, $this->get_user_guid()])->set_data($data)->set_bid($bid)->send();
//
        success('审核通过');
    }
}