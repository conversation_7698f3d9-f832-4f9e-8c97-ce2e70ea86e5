<?php

namespace app\controller\admin_api\v1;

use app\model\AlipayTransfersOrder as AlipayTransfersOrderModel;

class AlipayTransfersOrder extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new AlipayTransfersOrderModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }


    public function retry()
    {
        $order_guid = $this->params['guid'];
        $bid        = $this->get_bid();
        $db         = new AlipayTransfersOrderModel();
        $result     = $db->transfer($bid, $order_guid, true);
        if ($result) {
            success('转账成功');
        } else {
            error($db->getError());
        }
    }
}
