<?php

namespace app\controller\admin_api\v1;

use app\model\Rule as RuleModel;
use Exception;

class Version extends BasicAdminApi
{
    /**
     *版本列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $order       = ['status' => 'DESC', 'create_time' => 'DESC'];
        $this->model = $this->model->order($order);
        result($this->_list());
    }

    /**
     *版本授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function access()
    {
        $type = $this->params['access_type'] ?? 'rules';
        switch ($type) {
            case 'rules':
                $db_rule = new RuleModel();
                $node    = $db_rule->get_version_rule_node_info($this->params['guid']);
                result($node);
                break;
            case 'pages':
                $PageModel = new \app\model\Page();
                $node      = $PageModel->get_version_pages_node_info($this->params['guid']);
                result($node);
                break;
            default:
                break;
        }
        success('获取成功');
    }
}
