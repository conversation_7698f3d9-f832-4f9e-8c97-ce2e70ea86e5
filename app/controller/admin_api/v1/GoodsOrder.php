<?php

namespace app\controller\admin_api\v1;

use app\middleware\BusinessStatusAndExpiredVerify;
use app\model\ExportTask;
use app\model\ExpressOrder;
use app\model\ExtendField as ExtendFieldModel;
use app\model\GoodsOrder as GoodsOrderModel;
use app\model\Rule;
use app\model\User;
use app\common\service\ExpressService;
use app\common\service\NotifyService;
use app\common\tools\Excel;
use app\common\tools\Visitor;
use Exception;


class GoodsOrder extends BasicAdminApi
{
    protected array $middleware = [
        BusinessStatusAndExpiredVerify::class => ['only' => ['index', 'detail']],
    ];

    public function re_notify()
    {
        $bid        = $this->get_bid();
        $params     = $this->params;
        $guid_array = explode(',', $params['guid']);
        foreach ($guid_array as $guid) {
            $order_data = ['bid' => $bid, 'guid' => $guid];
            job()->set_job_name('GoodsOrder@after_send_out_goods')->push_job(['order_data' => $order_data]);
        }
        success('重发' . count($guid_array) . '个订单通知!');
    }

    public function del()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $order_guid = $params['order_guid'];
        $password   = $params['password'];
        $db_user    = new User();
        if (!$db_user->verify_user_password($password)) {
            error('您输入的密码不正确!');
        }
        $db_goods_order = new GoodsOrderModel();
        $db_goods_order->del(['bid' => $bid, 'order_guid' => $order_guid]);
        success('操作成功');
    }

    /**
     * 批量修改订单的省市区和详细地址
     * @access public
     * @return void
     */
    public function batch_edit()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $order_guids = explode(',', $params['guid']); // 获取批量订单的guid列表
        $province_id = $params['province_id'] ?? null;
        $city_id     = $params['city_id'] ?? null;
        $area_id     = $params['area_id'] ?? null;
        $address     = $params['address'] ?? null;

        $update_data = [];
        if ($province_id) {
            $update_data['province_id'] = $province_id;
        }
        if ($city_id) {
            $update_data['city_id'] = $city_id;
        }
        if ($area_id) {
            $update_data['area_id'] = $area_id;
        }
        if ($address) {
            $update_data['address'] = $address;
        }
        $count = 0;
        if (!empty($update_data) && !empty($order_guids)) {
            $db_goods_order = new GoodsOrderModel();
            $map            = [
                ['bid', '=', $bid],
                ['status', '=', 0],
                ['guid', 'IN', $order_guids]
            ];
            $db_goods_order::update($update_data, $map);
            $count = $db_goods_order->getNumRows();
        }
        if ($count) {
            success('批量修改成功' . $count . '个订单');
        } else {
            error('没有需要修改的订单');
        }
    }

    public function edit()
    {
        $params                       = $this->params;
        $bid                          = $this->get_bid();
        $order_guid                   = $params['guid'];
        $request_send_or_pick_up_time = $params['request_send_or_pick_up_time'] ?? null;
        $true_name                    = $params['true_name'] ?? null;
        $mobile                       = $params['mobile'] ?? null;
        $province_id                  = $params['province_id'] ?? null;
        $city_id                      = $params['city_id'] ?? null;
        $area_id                      = $params['area_id'] ?? null;
        $address                      = $params['address'] ?? null;
        $express_code                 = $params['express_code'] ?? null;
        $express_no                   = $params['express_no'] ?? null;
        $send_out_goods_remark        = $params['send_out_goods_remark'] ?? null;

        $express_code_2          = $params['express_code_2'] ?? null;
        $express_no_2            = $params['express_no_2'] ?? null;
        $send_out_goods_remark_2 = $params['send_out_goods_remark_2'] ?? null;
        $extend_field_1          = $params['extend_field_1'] ?? null;
        $extend_field_2          = $params['extend_field_2'] ?? null;
        $extend_field_3          = $params['extend_field_3'] ?? null;

        $owner_user_id  = $params['owner_user_id'] ?? null;
        $db_goods_order = new GoodsOrderModel();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
            //            ['status','=', 0],
        ];
        $update_data    = [];
        if ($request_send_or_pick_up_time) {
            $update_data['request_send_or_pick_up_time'] = $request_send_or_pick_up_time;
        }
        if ($true_name) {
            $update_data['true_name'] = $true_name;
        }
        if ($mobile) {
            $update_data['mobile'] = $mobile;
        }
        if ($province_id) {
            $update_data['province_id'] = $province_id;
        }
        if ($city_id) {
            $update_data['city_id'] = $city_id;
        }
        if ($area_id) {
            $update_data['area_id'] = $area_id;
        }
        if ($address) {
            $update_data['address'] = $address;
        }
        if ($address) {
            $update_data['address'] = $address;
        }
        if ($extend_field_1) {
            $update_data['extend_field_1'] = $extend_field_1;
        }
        if ($extend_field_2) {
            $update_data['extend_field_2'] = $extend_field_2;
        }
        if ($extend_field_3) {
            $update_data['extend_field_3'] = $extend_field_3;
        }


        if ($owner_user_id) {
            $update_data['owner_user_id']   = $owner_user_id;
            $db_user                        = new User();
            $update_data['owner_user_guid'] = $db_user->get_user_guid_by_id($owner_user_id, $bid);
        }

        if ($express_code) {
            $update_data['express_code'] = $express_code;
        }
        if ($express_no) {
            $db_goods_order            = new GoodsOrderModel();
            $express_no                = $db_goods_order->filter_express_no($express_no);
            $update_data['express_no'] = $express_no;
        }
        if ($send_out_goods_remark) {
            $update_data['send_out_goods_remark'] = $send_out_goods_remark;
        }
        if ($express_code_2) {
            $update_data['express_code_2'] = $express_code_2;
        }
        if ($express_no_2) {
            $update_data['express_no_2'] = $express_no_2;
        }
        if ($send_out_goods_remark_2) {
            $update_data['send_out_goods_remark_2'] = $send_out_goods_remark_2;
        }

        $db_goods_order::update($update_data, $map);
        success('修改成功');
    }

    /**
     *订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid            = $this->get_bid();
        $db_goods_order = new GoodsOrderModel();
        $info           = $db_goods_order->build_query($bid, $this->get_user_guid(), $this->params);
        $this->params   = $info['params'];
        $this->model    = $info['model'];
        $params         = $this->params;
        if (!empty($params['keyword'])) {
            $this->params['key']   = 'pick_up_code&go.mobile&true_name&go.bill_number&csn.code&go.extend_field_1&go.extend_field_2&go.extend_field_3';
            $this->params['value'] = trim($params['keyword']);
            unset($this->params['keyword']);
        }
        //unset($this->params['append_order_detail']);
        $list = $this->_list();
        foreach ($list['data'] as $key => $val) {
            $query_route_url                       = (string)url('member/tools/kuaidi', ['bid' => $bid, 'coname' => 'www', 'nu' => $val['express_no']], false, true);
            $list['data'][$key]['query_route_url'] = $query_route_url;
            $list['data'][$key]['goods_info']      = $db_goods_order->build_goods_info_pic($list['data'][$key]['goods_info']);
        }
        result($list);
    }

    public function parse_qrcode()
    {
        $params = $this->params;
        $text   = $params['text'];
        $text   = str_replace('https://ucmp.sf-express.com/wxaccess/weixin/activity/wxapp_b2sf_order?p1=', '', $text);
        $data   = [];
        if (tools()::is_url($text)) {
            $url_params = tools()::parse_url_params($text);
            if (!empty($url_params['c'])) {
                $data['code'] = $url_params['c'];
            }
            if (!empty($url_params['p'])) {
                $data['password'] = $url_params['p'];
            }
        } elseif (is_string($text)) {
            $data['express_no'] = $text;
            $data['keyword']    = $text;
            $data['code']       = $text;
        }
        //        if (empty($data)) {
        //            //error('不是有效的二维码');
        //        }
        result($data);
    }


    protected function has_auth_multiple_express_send_out_goods()
    {
        //判断是否有多包裹发货权限
        $bid       = $this->get_bid();
        $user_guid = $this->get_user_guid();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::MULTIPLE_EXPRESS_SEND_OUT_GOODS);
    }


    public function prepare_goods_report()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $this->model = new GoodsOrderModel();
        $order       = ['go.id' => 'DESC'];
        $join        = [
            ['goods_order_item goi', 'goi.bid = go.bid AND goi.order_guid = go.guid'],
            ['goods g', 'goi.bid = g.bid AND goi.goods_guid= g.guid'],
        ];
        $field       = [
            'g.name'   => 'goods_name',
            'g.specs'  => 'goods_specs',
            'count(*)' => 'count',
        ];
        if (!empty($params['user_id'])) {
            $join[]                     = ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'];
            $field['csn.owner_user_id'] = 'user_id';
        }
        $db_user     = new User();
        $map         = [
            ['go.bid', '=', $bid],
            ['go.status', '=', 0], //只查询待发货订单
            ['go.delete_time', 'NULL', null], //过滤已删除订单
            ['go.owner_user_id', 'IN', $db_user->getChildUserIdArray()], // 只显示自己范围内的订单
        ];
        $group       = [
            'g.name',
            'g.specs',
        ];
        $this->model = $this->model->alias('go')->where($map)->join($join)->field($field)->order($order)->group($group);
        $list        = $this->_list();
        result($list);
    }


    /**
     *订单详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $guid           = $params['guid'];
        $db_goods_order = new GoodsOrderModel();
        $order_info     = $db_goods_order->get_order_detail($bid, $guid);
        result($order_info);
    }


    public function export_order_item()
    {
        $db_goods_order = new GoodsOrderModel();
        $info           = $db_goods_order->build_item_query(null, null, $this->params);
        $model_info     = $info['model'];
        $this->params   = $info['params'];
        $data           = $this->_select($model_info);
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        return $db_goods_order->export_order_item_data($data);
//        $header = [
//            'bill_number'     => '订单号',
//            'goods_name'      => '商品名',
//            'owner_user_info' => '供应商',
//            'goods_specs'     => '规格',
//            'amount'          => '件数',
//            'price'           => '单价',
//            'mobile'          => '手机号',
//            'true_name'       => '姓名',
//            'address_info'    => '地址',
//            'express_no'      => '快递单号',
//            'express_name'    => '快递公司',
//            'remark'          => '备注',
//            'create_time'     => '下单时间',
//            'status_text'     => '状态',
//        ];
//        if ($this->has_auth_cost_price()) {
//            tools()::insert_array_key_value($header, 'cost_price', '成本价', 'price');
//        }
//        //处理扩展字段
//        $header = $this->append_extra_field($this->get_bid(), $header);
//        $header = $this->append_extend_field($this->get_bid(), $header);
//        $file   = new Excel();
//        $file->arrayToExcel($header, $data, '商品订单明细_' . date('Y-m-d-H-i-s'));
    }

    protected function append_extra_field($bid, $header)
    {
        //处理扩展字段
        $config                         = get_config_by_bid($bid);
        $goods_order_export_field       = $config['goods_order_export_field'];
        $goods_order_export_field_array = explode(',', $goods_order_export_field);
        $extra_field_mapping            = [
            'province_name'    => '省',
            'city_name'        => '市',
            'area_name'        => '区',
            'address'          => '详细地址',
            'code_send_remark' => '卡号备注',
            'code_mobile'      => '卡号手机号',
        ];
        foreach ($goods_order_export_field_array as $field) {
            foreach ($extra_field_mapping as $key => $val) {
                if ($field == $val) {
                    $header[$key] = $val;
                }
            }
        }
        return $header;
    }

    protected function append_extend_field($bid, $header)
    {
        //处理扩展字段
        $db_extend_field         = new ExtendFieldModel();
        $map_extend_field        = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $show_extend_field_array = $db_extend_field->where($map_extend_field)->order(['key_name' => 'ASC'])->select();
        foreach ($show_extend_field_array as $key => $val) {
            $key_name          = $val['key_name'];
            $name              = $val['name'];
            $header[$key_name] = $name;
        }
        return $header;
    }

    /**
     *导出订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export()
    {
        if (!Visitor::is_swoole()) {
            ini_set('memory_limit', '1024M');
        }
        $bid                  = $this->get_bid();
        $user_guid            = $this->get_user_guid();
        $config               = get_config_by_bid($bid);
        $db_goods_order       = new GoodsOrderModel();
        $info                 = $db_goods_order->build_query(null, null, $this->params);
        $model                = $info['model'];
        $this->params         = $info['params'];
        $large_amount_of_data = $config['large_amount_of_data'];
        $map                  = $this->build_condition_map($model);
        logToFile('导出订单开始，当前内存:' . tools()::get_memory_get_usage());
        if ($large_amount_of_data) {
            if (empty($map) && 1 == 2) {
                //说明在全量数据导出, 并且是大商家, 此时返回task_guid, 前端继续轮询
                $db_export_task = new ExportTask();
                if ($db_export_task->has_task($bid, $user_guid)) {
                    error('当前有导出任务在执行,请到设置--文件下载中查看进度！');
                }
                $db_export_task->add_task($bid, $user_guid, 1);
                result([], '导出任务提交成功,请稍候在设置--文件下载中查看！');
            }
            $data = $this->_chunk($model);
        } else {
            $data = $this->_select($model);
        }
        logToFile('导出订单完毕，当前内存:' . tools()::get_memory_get_usage());
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $db_goods_order->export_data($data);

//        $header                                   = [
//            'bill_number'           => '订单号',
//            'status_text'           => '状态',
//            'goods_info_text'       => '商品信息',
//            //            'total_amount'          => '总件数',
//            'create_time'           => '下单时间',
//            'send_or_pick_up_time'  => '发货时间',
//            'express_name'          => '快递公司',
//            'express_no'            => '快递单号',
//            'send_out_goods_remark' => '发货备注',
//        ];
//        $has_auth_multiple_express_send_out_goods = $this->has_auth_multiple_express_send_out_goods();
//        if ($has_auth_multiple_express_send_out_goods) {
//            $header['express_name_2']          = '快递公司2';
//            $header['express_no_2']            = '快递单号2';
//            $header['send_out_goods_remark_2'] = '发货备注2';
//        }
//        //处理扩展字段
//        $header = $this->append_extra_field($this->get_bid(), $header);
//        $header = $this->append_extend_field($bid, $header);
//        //自动增加支付金额列
//        $is_not_empty_or_zero_filed           = [
//            'owner_user_info'              => '归属者',
//            'coupon_name'                  => '卡券名称',
//            'mobile'                       => '手机号',
//            'true_name'                    => '姓名',
//            'address_info'                 => '地址',
//            'id_card_number'               => '身份证号',
//            'request_send_or_pick_up_time' => '预约时间',
//            'code'                         => '券号',
//            'remark'                       => '备注',
//            //            'share_member_name'            => '分销者',
//            //            'brokerage_money'              => '佣金金额',
//            'total_money'                  => '订单金额',
//            'freight_money'                => '运费',
//            'extra_charges_money'          => '附加费',
//            'paid_money'                   => '储值支付',
//            'paid_point'                   => '积分支付',
//            'paid_wechat'                  => '微信支付',
//        ];
//        $has_auth_member_brokerage_note_index = $this->has_auth_member_brokerage_note_index();
//        if ($has_auth_member_brokerage_note_index) {
//            $is_not_empty_or_zero_filed['share_member_name'] = '分销者';
//            $is_not_empty_or_zero_filed['brokerage_money']   = '佣金金额';
//        }
//        if ($config['is_show_request_send_or_pick_up_store'] == 1) {
//            $is_not_empty_or_zero_filed['request_send_or_pick_up_store_name'] = '自提门店';
//        }
//        foreach ($is_not_empty_or_zero_filed as $key => $value) {
//            foreach ($data as $k => $v) {
//                if (!tools()::is_empty_or_zero($v[$key])) {
//                    $header[$key] = $value;
//                    break;
//                }
//            }
//        }
//        $file = new Excel();
//        $file->arrayToExcel($header, $data, '商品订单_' . date('Y-m-d-H-i-s'));
    }

    /**
     *发货
     * @access public
     * @return mixed
     *
     * @throws Exception
     */
    public function refund_third_paid_money()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $guid           = $params['guid'];
        $db_goods_order = new GoodsOrderModel();
        $refund_result  = $db_goods_order->refund_third_paid_money($bid, $guid);
        if (!$refund_result) {
            success('重新退款成功!');
        } else {
            error('退款失败:' . $refund_result);
        }
    }

    /**
     *发货
     * @access public
     * @return mixed
     *
     * @throws Exception
     */
    public function refund()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $password = $params['password'];
        $db_user  = new User();
        if (!$db_user->verify_user_password($password)) {
            error('您输入的密码不正确!');
        }
        $guid           = $params['guid'];
        $db_goods_order = new GoodsOrderModel();
        $refund_result  = $db_goods_order->refund(['order_guid' => $guid]);
        if (!$refund_result) {
            success('退款成功');
        } else {
            error('业务退款成功:' . $refund_result);
        }
    }

    protected function notify_refresh_home_data($bid)
    {
        notify()->set_key_name(NotifyService::RefreshHomeData)->set_data(['from' => 'send_out_goods'])->set_bid($bid)->send();
    }

    /**
     *发货
     * @access public
     * @return void
     * @throws Exception
     */
    public function send_out_goods()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $guid                  = $params['guid'] ?? null;
        $send_type             = (int)$params['send_type'];
        $express_config_guid   = $params['express_config_guid'] ?? null;
        $express_code          = $params['express_code'] ?? null;
        $express_no            = $params['express_no'] ?? null;
        $express_index         = $params['express_index'] ?? 1;
        $send_out_goods_remark = $params['send_out_goods_remark'] ?? null;
        $file_url              = $params['file'] ?? null;
        switch ($send_type) {
            case 1: //输入单号发货
                if (empty($express_code) || empty($express_no)) {
                    error('请输入快递信息');
                }
                if (empty($guid)) {
                    error('单号信息不能为空');
                }
                $db_goods_order = new GoodsOrderModel();
                $express_data   = [
                    'express_code'          => $express_code,
                    'express_no'            => $express_no,
                    'express_index'         => $express_index,
                    'send_out_goods_remark' => $send_out_goods_remark
                ];
                $result         = $db_goods_order->send_out_goods($bid, $guid, $express_data, $send_type);
                if ($result) {
                    $db_goods_order->notify_refresh_home_data($bid, $guid);
                    success('发货成功');
                } else {
                    error($db_goods_order->getError());
                }
                break;
            case 2: //导入excel
                if (empty($file_url)) {
                    error('请上传文件');
                }
                if (!tools()::is_url($file_url)) {
                    error('请上传正确的文件');
                }
                $this->batch_send_out_goods();
                break;
            case 3: //电子面单发货
                if (empty($express_config_guid) || empty($guid)) {
                    error('请选择面单模板');
                }
                $guid_array  = explode(',', $guid);
                $success_num = 0;
                $failed_num  = 0;
                $msg         = '';
                if (count($guid_array) >= 10) {
                    $method      = 'create_express_order_and_send_out_goods';
                    $class       = 'app\model\GoodsOrder';
                    $param_array = [];
                    foreach ($guid_array as $guid) {
                        $param_array[] = [$bid, $guid, $express_config_guid];
                    }
                    $result = job()->batch_call($class, $method, $param_array, 45);
                    if ($result === false) {
                        error('电子面单发货超时,请稍候刷新页面查看最新状态');
                    }
                    foreach ($result['result_list'] as $key => $val) {
                        $val = json_decode($val, true);
                        if ($val['success']) {
                            $success_num++;
                        } else {
                            $failed_num++;
                            $msg .= $val['msg'] . ';';
                        }
                    }
                } else {
                    foreach ($guid_array as $guid) {
                        $db_goods_order = new GoodsOrderModel();
                        $result         = $db_goods_order->create_express_order_and_send_out_goods($bid, $guid, $express_config_guid);
                        if ($result ['success'] === false) {
                            $failed_num++;
                            $msg .= $result ['msg'] . ';';
                            continue;
                        }
                        $success_num++;
                    }
                }
                $db_goods_order = new GoodsOrderModel();
                if ($success_num > 0 && $failed_num == 0) {
                    $db_goods_order->notify_refresh_home_data($bid);
                    result(['success_num' => $success_num, 'send_type' => $send_type], '电子面单发货完成,共发货' . $success_num . '单');
                }
                if ($failed_num > 0 && $success_num == 0) {
                    error('电子面单发货失败,共失败' . $failed_num . '单,原因:' . $msg);
                }
                if ($failed_num > 0 && $success_num > 0) {
                    $db_goods_order->notify_refresh_home_data($bid);
                    $msg = '电子面单发货完成,成功【' . $success_num . '】单,失败【' . $failed_num . '】单,原因:' . $msg;
                    wr_log($msg);
                    result(['success_num' => $success_num, 'send_type' => $send_type], $msg);
                }
                break;
            case 4: //直接核销
                if (empty($guid)) {
                    error('单号信息不能为空');
                }
                $db_goods_order = new GoodsOrderModel();
                $express_data   = ['express_code' => $express_code, 'express_no' => $express_no];
                $result         = $db_goods_order->send_out_goods($bid, $guid, $express_data, $send_type);
                if ($result) {
                    $db_goods_order->notify_refresh_home_data($bid);
                    success('核销成功');
                } else {
                    error($db_goods_order->getError());
                }
                break;
            case 5: // 无需物流发货
                $db_goods_order = new GoodsOrderModel();
                if (empty($guid)) {
                    error('请选择需要发货的订单!');
                }
                $guid_array  = explode(',', $guid);
                $success_num = 0;
                $failed_num  = 0;
                $msg         = '';
                foreach ($guid_array as $guid) {
                    $result = $db_goods_order->send_out_goods($bid, $guid, [], $send_type);
                    $result && $success_num++;
                    if (!$result) {
                        $msg .= $db_goods_order->getError() . ';';
                        $failed_num++;
                    }
                }
                if ($success_num > 0 && $failed_num == 0) {
                    $db_goods_order->notify_refresh_home_data($bid);
                    success('发货成功【' . $success_num . '】个订单');
                } elseif ($success_num > 0 && $failed_num > 0) {
                    success('发货成功【' . $success_num . '】个订单,失败【' . $failed_num . '】个订单,原因:' . $msg);
                } else {
                    error('发货失败【' . $failed_num . '】个订单,原因:' . $msg);
                }
                break;
            default:
                error('暂不支持的发货方式');
        }
    }

    public function batch_send_out_goods()
    {
        $bid      = $this->get_bid();
        $file     = new Excel();
        $param    = $this->params;
        $file_url = $param['file'];
        $replace  = $param['replace'] ?? false;
        if (empty($file_url)) {
            error('请上传文件');
        }
        if (!tools()::is_url($file_url)) {
            error('请上传正确的文件');
        }
        $cache_key   = __FUNCTION__ . ':' . md5($file_url);
        $cache_value = cache($cache_key);
        $cache_exp   = 10; //分钟
        if ($cache_value) {
            error($cache_exp . '分钟内有上传过该文件,请至订单列表核实发货状态');
        }
        ignore_user_abort(true);
        set_time_limit(0);
        $required_field                           = [
            'bill_number'  => '订单号',
            'express_name' => '快递公司',
            'express_no'   => '快递单号',
        ];
        $has_auth_multiple_express_send_out_goods = $this->has_auth_multiple_express_send_out_goods();
        $optional_field                           = [
            'send_out_goods_remark'   => '发货备注',
            'express_name_2'          => '快递公司2',
            'express_no_2'            => '快递单号2',
            'send_out_goods_remark_2' => '发货备注2',
        ];
        $arr                                      = $file->load($file_url)->excelToArray($required_field, $optional_field);
        //循环校验合法数据 得到要导入的数组
        $data      = [];
        $err_msg   = '';
        $total_row = 0;
        foreach ($arr as $key => $val) {
            // 定义表单验证规则
            $rules = [
                'bill_number|订单号'    => 'require',
                'express_name|快递公司' => 'require',
                'express_no|快递单号'   => 'require',
            ];
            if (!$has_auth_multiple_express_send_out_goods) {
                // 仅仅当没有多包裹发货权限的时候 才验证excel数据
                try {
                    $this->validate($val, $rules);
                } catch (Exception $e) {
                    //验证失败忽略本条数据 有时候部分订单不需要发货 无需返回到前端
//                    $err_msg .= $e->getMessage();
                    continue;
                }
            }
            $data[] = $val;
            $total_row++;
        }
        $success_num                       = 0;
        $total_num                         = 0;
        $max_sync_num                      = 50; // 低于50单同步发货
        $async_send_out_goods              = $total_row > $max_sync_num;//是否异步发货
        $task_bill_number                  = tools()::get_bill_number();
        $job_data_array                    = []; //用于批量插入job
        $max_express_no_num                = $has_auth_multiple_express_send_out_goods ? 2 : 1;
        $express_name_express_code_mapping = [];
        foreach ($data as $bill) {
            //循环读取 excel 行数据
            $bill_number = $bill['bill_number']; //订单号是同一个
            $bill_number = tools()::find_string_bill_number($bill_number);
            if (empty($bill_number)) {
                continue;
            }
            for ($i = 0; $i < $max_express_no_num; $i++) {
                $express_index                    = $i + 1;
                $express_index_string             = $express_index > 1 ? $express_index : '';
                $field_suffix                     = $express_index > 1 ? '_' . $express_index : '';
                $express_name_field_name          = 'express_name' . $field_suffix;
                $express_no_field_name            = 'express_no' . $field_suffix;
                $send_out_goods_remark_field_name = 'send_out_goods_remark' . $field_suffix;
                $express_name                     = $bill[$express_name_field_name];
                $express_no                       = $bill[$express_no_field_name];
                $send_out_goods_remark            = $bill[$send_out_goods_remark_field_name];
                $send_out_goods_remark            = trim($send_out_goods_remark);
                $express_name                     = tools()::find_string_cn_and_en($express_name); //只保留英文 因为有EMS
                $db_goods_order                   = new GoodsOrderModel();
                $express_no                       = $db_goods_order->filter_express_no($express_no);
                if (empty($express_no) && empty($express_name)) {
                    //如果快递单号或者快递公司名称都为空 则不处理
                    continue;
                }
                if ($express_no && empty($express_name)) {
                    //如果快递单号或者快递公司名称都为空 则不处理
                    $err_msg .= '单号:' . $bill_number . ':快递公司' . $express_index_string . '为空,';
                    continue;
                }
                if ($express_name && empty($express_no)) {
                    //如果快递单号或者快递公司名称都为空 则不处理
                    $err_msg .= '单号:' . $bill_number . ':快递单号' . $express_index_string . '为空,';
                    continue;
                }
                if ($express_no && strpos($express_no, 'VLOOKUP')) {
                    $err_msg .= '单号:' . $bill_number . ':快递单号' . $express_index_string . '包含Excel公式!';
                    continue;
                }
                $total_num++; //共导入订单数
                $db_goods_order = new GoodsOrderModel();
                if ($express_name == '无需物流' && $express_no == '无需物流') {
                    //快递公司和快递单号都是无需物流,则特殊处理
                    if ($async_send_out_goods === false) {
                        $result = $db_goods_order->send_out_goods($bid, $bill_number, [], 5);
                    } else {
                        //send_out_goods(string $bid, string $order_guid_or_bill_number, array $express_data, int $send_type)
                        $job_data         = [
                            'task_bill_number'          => $task_bill_number,
                            'bid'                       => $bid,
                            'order_guid_or_bill_number' => $bill_number,
                            'express_data'              => [],
                            'user_id'                   => $this->get_user_id(),
                            'user_guid'                 => $this->get_user_guid(),
                            'send_type'                 => 5,
                        ];
                        $job_data_array[] = $job_data;
                    }
                } else {
                    $express_name = strtoupper($express_name);// ems 转 EMS
                    //做替换 中通快递@中通速递|韵达快递@韵达  标准名称@别称 多个|隔开
                    $config                    = get_config_by_bid($bid);
                    $express_name_replace_list = $config['express_name_replace_list'];
                    if (!empty($express_name_replace_list)) {
                        $express_name_replace_list = explode('|', $express_name_replace_list);
                        foreach ($express_name_replace_list as $express_name_replace) {
                            $express_name_replace_array = explode('@', $express_name_replace);
                            if (count($express_name_replace_array) == 2) {
                                $express_name_replace_name       = $express_name_replace_array[0];
                                $express_name_replace_alias_name = $express_name_replace_array[1];
                                if ($express_name == $express_name_replace_alias_name) {
                                    $express_name = $express_name_replace_name;
                                }
                            }
                        }
                    }
                    if (!isset($express_name_express_code_mapping[$express_name])) {
                        $db_express   = new \app\model\Express();
                        $map          = [['name|alias_name', '=', $express_name]];
                        $express_code = $db_express->where($map)->value('code');
                        //无论是否能查询到快递公司code 都赋值到数组中 避免全部都是无效code的时候 每一次都无效查询
                        $express_name_express_code_mapping[$express_name] = (string)$express_code;
                    }
                    //如果快递名称缓存是空的 则不再执行
                    if (empty($express_name_express_code_mapping[$express_name])) {
                        $err_msg .= '单号' . $bill_number . ':快递公司' . $express_index_string . '【' . $express_name . '】不存在,请检查名称,';
                        continue;
                    }
                    $express_data = [
                        'express_code'          => $express_name_express_code_mapping[$express_name],
                        'express_no'            => $express_no,
                        'send_out_goods_remark' => $send_out_goods_remark,
                        'express_index'         => $express_index,
                        'replace'               => $replace,
                    ];
                    if ($async_send_out_goods === false) {
                        $result = $db_goods_order->send_out_goods($bid, $bill_number, $express_data, 2);
                    } else {
                        $job_data         = [
                            'task_bill_number'          => $task_bill_number,
                            'bid'                       => $bid,
                            'user_id'                   => $this->get_user_id(),
                            'user_guid'                 => $this->get_user_guid(),
                            'order_guid_or_bill_number' => $bill_number,
                            'express_data'              => $express_data,
                            'send_type'                 => 2,
                        ];
                        $job_data_array[] = $job_data;
                    }
                }
                if ($async_send_out_goods === false && !empty($result)) {
                    $success_num++;
                } elseif ($async_send_out_goods === false && empty($result)) {
                    $err_msg .= '单号:' . $bill_number . ':' . $db_goods_order->getError() . ',';
                }
            }
        }
        if ($total_num == 0) {
            error('excel中没有订单数据!' . $err_msg);
        }
        cache($cache_key, $file_url, $cache_exp * 60);
        if ($async_send_out_goods) {
            //异步发货需要并发完成发货,以获取具体错误内容
            $total_job_num = count($job_data_array);
            if ($total_job_num == 0) {
                $msg = '发货失败,请检查数据是否正确!';
                $err_msg && $msg .= ',' . $err_msg;
                error($msg);
            }
            $param_array = [];
            foreach ($job_data_array as $job_data) {
                $job_data['express_data']['return_msg'] = true; //返回具体错误信息
                $param_array[]                          = [$job_data['bid'], $job_data['order_guid_or_bill_number'], $job_data['express_data'], $job_data['send_type'], $job_data['user_guid'], $job_data['user_id']];
            }
            $result = job()->batch_call('app\model\GoodsOrder', 'send_out_goods', $param_array, 15);
            if ($result === false) {
                $msg = '共导入【' . $total_num . '】条数据 ,发货任务已提交【' . $total_job_num . '】条,发货状态请稍候刷新订单列表为准';
                error($msg);
            }
            foreach ($result['result_list'] as $result_string) {
                if ($result_string === 'true') {
                    $success_num++;
                } else {
                    $err_msg .= $result_string . ';';
                }
            }
        }
        $fail_num = $total_num - $success_num;
        $msg      = '共导入【' . $total_num . '】条数据 ,发货成功【' . $success_num . '】条';
        if ($fail_num > 0) {
            $msg .= ',发货失败【' . $fail_num . '】条';
            $msg .= ';原因:' . $err_msg;
        }
        wr_log($msg);
        $db_goods_order = new GoodsOrderModel();
        $db_goods_order->notify_refresh_home_data($bid);
        if ($fail_num > 0) {
            error($msg);
        } else {
            success($msg);
        }
    }

    public function confirm()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $order_guid = $params['order_guid'];
        $db         = new GoodsOrderModel();
        $db->confirm_order($bid, $order_guid);
        success('订单已完成');
    }

    public function cancel_express_order()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $guid           = $params['order_guid'];
        $db_goods_order = new GoodsOrderModel();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $order          = $db_goods_order->where($map)->order(['update_time' => 'DESC'])->find();
        if ($order['status'] != 1) {
            error('只能撤销已发货订单');
        }
        $instance = ExpressService::get_instance($bid);
        $result   = $instance->cancel_order($guid);
        if ($result === false) {
            error($instance->message);
        }
        $update_data = [
            'status'               => 0,
            'send_or_pick_up_time' => null,
            'express_code'         => '',
            'express_no'           => '',
            'send_type'            => 0
        ];
        $db_goods_order::update($update_data, $map);
        success('快递单取消成功');
    }

    public function report()
    {
        $params = $this->params;
        //        $group_field_list = $params[':group'] ?? [];
        //        unset($this->params[':group']);
        $default_field        = [
            //           'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date',
            'g.name'                                      => 'goods_name',
            'c.name'                                      => 'coupon_name',
            "IFNULL(CONCAT(u.name,'(',u.account,')'),'')" => 'owner_user_info',
            'sum(g.cost_price)'                           => 'cost_price',
            'sum(goi.amount)'                             => 'num',
            //'cast(sum(total_amount)/100 as decimal(9,2))'   => 'total_amount'
        ];
        $mapping              = [
            'goods_name'      => ['max(g.guid)' => 'goods_guid'],
            'owner_user_info' => ['max(g.owner_user_id)' => 'owner_user_id'],
        ];
        $default_header_field = [
            'coupon_name'     => '卡券',
            'goods_name'      => '商品名称',
            'owner_user_info' => '供应商',
            'cost_price'      => '成本总额',
            'num'             => '数量',
        ];

        $bid         = $this->get_bid();
        $db_user     = new User();
        $map         = [
            ['goi.bid', '=', $bid],
            ['go.status', 'IN', [0, 1, 2]],
            ['go.delete_time', 'null', null],
            ['c.delete_time', 'null', null],
            ['go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildUserIdArray()], // 只显示自己范围内的订单
        ];
        $this->model = new \app\model\GoodsOrderItem();
        $join        = [
            ['goods g', 'goi.bid = g.bid AND goi.goods_guid= g.guid'],
            ['goods_order go', 'goi.bid = go.bid AND goi.order_guid= go.guid'],
            ['coupon c', 'go.bid = c.bid AND go.coupon_guid= c.guid'],
            ['user u', 'u.bid = g.bid AND u.guid= g.owner_user_guid', 'LEFT'],
        ];
        $this->model = $this->model->alias('goi')->join($join)->where($map);
        result($this->_build_report_query($mapping, $default_field, $default_header_field));
    }

    public function report_print_status()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $order_guid     = explode(',', $params['guid']);
        $db_goods_order = new GoodsOrderModel();
        $map            = [
            ['bid', '=', $bid],
            ['status', '<>', 0],
            ['guid', 'IN', $order_guid],
        ];
        $db_goods_order->where($map)->data(['last_print_time' => format_timestamp()])->setInc('print_times');
        success('上报成功');
    }

    public function print_express_order()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $print_result = [];
        if (strpos($params['guid'], ',') === false && isset($params['type']) && $params['type'] = 2) {
            //            if (empty($params['guid'])) {
            //                error('请选择需要打印的订单');
            //            }
            $express_map      = [
                ['bid', '=', $bid],
                ['status', '=', 1],
                ['relation_guid', '=', $params['guid']],
            ];
            $db_express_order = new ExpressOrder();
            $html             = $db_express_order->where($express_map)->order(['create_time' => 'DESC'])->value('print_tpl');
            if ($html) {
                $data = [
                    'channel_name' => 'kdniao',
                    'type'         => 'html',
                    'html'         => $html,
                ];
                result($data);
            }
        }
        if (empty($html)) {
            //            if (empty($params['guid'])) {
            //                error('请选择需要打印的订单');
            //            }
            $guid         = explode(',', $params['guid']);
            $instance     = ExpressService::get_instance($bid);
            $print_result = $instance->print_order($guid);
            if ($print_result === false) {
                error($instance->message);
            }
        }
        //        if ($this->request->isGet()) {
        //            return $html ?: result($print_result);
        //        }
        result($print_result);
    }

    public function create_express_order()
    {
        error('该接口已经下线,请通过发货操作!');
        $params     = $this->params;
        $bid        = $this->get_bid();
        $guid_array = explode(',', $params['guid']);
        $instance   = ExpressService::get_instance($bid);
        foreach ($guid_array as $guid) {
            $db_goods_order = new GoodsOrderModel();
            $map            = [
                ['bid', '=', $bid],
                ['status', '=', 0],
                ['guid', '=', $guid],
            ];
            $order_info     = $db_goods_order->where($map)->findOrEmpty();
            if (!$order_info->isEmpty()) {
                $result = $instance->create_order($guid);
                if ($result === false) {
                    error($instance->message);
                }
                $express_data = ['express_code' => $result['express_code'], 'express_no' => $result['express_no']];
                $result       = $db_goods_order->send_out_goods($bid, $guid, $express_data, 3);
            }
        }
        success('电子发货成功');
    }


}
