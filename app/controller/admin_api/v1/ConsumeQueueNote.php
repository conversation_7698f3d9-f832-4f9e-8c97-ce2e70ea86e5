<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\ConsumeQueueNote as ConsumeQueueNoteModel;
use Exception;

class ConsumeQueueNote extends BasicAdminApi
{
    /**
     *订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $order       = ['create_time' => 'DESC'];
        $this->model = $this->model->where($map)->order($order);
        result($this->_list());
    }

    /**
     *提前返现
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reward()
    {
        $params = $this->params;
        $db     = new ConsumeQueueNoteModel();
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $params['guid']],
        ];
        $note   = $db->where($map)->findOrFail();
        if ($note['status'] != 0) {
            error('仅允许提前奖励待奖励状态的订单');
        }
        $data = [
            'bid'              => $note['bid'],
            'number'           => $note['reward_number'],
            'bill_number'      => $note['bill_number'],
            'chain_store_guid' => $note['chain_store_guid'],
            'order_guid'       => create_guid(),
            'reward_source'    => '提前返现',
        ];
        job()->set_job_name('Yikayi@reward_consume_queue_order')->push_job($data);
        success('提前返现任务提交成功,稍后刷新页面查看');
    }

    /**
     *撤销
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function revoke()
    {
        $params = $this->params;
        $db     = new ConsumeQueueNoteModel();
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $params['guid']],
        ];
        $note   = $db->where($map)->findOrFail();
        if ($note['status'] != 0) {
            error('仅允许撤销待奖励状态的订单');
        }
        $update_data = ['status' => -2, 'message' => '手动撤销'];
        $db::update($update_data, $map);
        success('返现撤销成功');
    }
}