<?php

namespace app\controller\admin_api\v1;

use app\model\YkyUser as YkyUserModel;
use app\common\service\UrlService;
use Exception;

class YkyUser extends BasicAdminApi
{
    /**
     * 获取支付码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_barter_qrcode()
    {

        $params       = $this->params;
        $bid          = $this->get_bid();
        $guid         = $params['guid'];
        $store_guid   = $params['chain_store_guid'];
        $user_account = $params['user_account'];
        $db           = new YkyUserModel();
        $map          = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $store        = $db->where($map)->find();
        // http://www.yikayi.net/member/pay/barter?bid=425aee2e-5d3e-47d5-0d1a-80acb497988b&store_guid=42aab81e-e06f-11ea-8c97-20040fed9860&user_account=10000
        $long_url       = (string)url('member/pay/barter', ['bid' => $store['bid'], 'store_guid' => $store_guid, 'user_account' => $user_account], false, true);
        $short_url_code = UrlService::long_to_short($long_url);
        $short_url      = UrlService::get_full_url($short_url_code);
        $data           = [
            'title' => $store['store_name'] . '-' . $user_account,
            'url'   => $short_url
        ];
        result($data);
    }

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyUserModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
        ];
        $this->model = $db->where($map)->order(['user_group_type' => 'ASC', 'create_time' => 'DESC', 'user_account' => 'ASC']);
        result($this->_list());
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $db     = new YkyUserModel();
        $db->edit($params);
        success('编辑成功');
    }
}