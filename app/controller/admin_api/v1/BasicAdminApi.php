<?php
declare (strict_types=1);

namespace app\controller\admin_api\v1;

use app\BaseController;
use Exception;

class BasicAdminApi extends BaseController
{

    /**
     *魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return void
     * @throws Exception
     */
    public function __call($name, $arguments)
    {
        $this->__call_model_action($name, $arguments);
    }

    /**
     *初始化
     * @access protected
     * @return void
     * @throws Exception
     */
    protected function initialize()
    {
        $this->init_model_class();
    }


    protected function _build_report_query($mapping, $default_field, $default_header_field)
    {
        $params                    = $this->params;
        $group_field_list          = $params[':group'] ?? [];
        $group_field_list_is_empty = true;
        $total_row_field_list      = [];
        foreach ($group_field_list as $group_field) {
            if ($group_field) {
                $group_field_list_is_empty = false;
                break;
            }
        }
        if ($group_field_list_is_empty) {
            $group = [];
            $field = $default_field; //直接使用默认字段
            foreach ($default_field as $key => $val) {
                if (strpos($key, 'sum(') === false && strpos($key, 'count(') === false) {
                    //不包含统计方法 说明是字段
                    $group[] = $key;
                } else {
                    $total_row_field_list[] = $val;
                }
                if (isset($mapping[$val])) {
                    $field[$key] = $val;
                }
            }
        } else {
            //根据 $group_list 重构 group
            $group = [];
            $field = [];
            foreach ($group_field_list as $k => $v) {
                foreach ($default_field as $key => $val) {
                    if ($val == $v) {
                        //不包含统计方法 说明是字段
                        $group[] = $key;
                        //                        array_push($field, [$key => $val]);
                        $field[$key] = $val;
                    }
                }
            }
            //继续追加 求和统计字段到 $field
            foreach ($default_field as $key => $val) {
                if (strpos($key, 'sum(') !== false || strpos($key, 'count(') !== false) {
                    //包含统计方法 说明是统计字段
                    //                        array_push($field, [$key => $val]);
                    $field[$key]            = $val;
                    $total_row_field_list[] = $val;
                }
            }
        }
        foreach ($field as $key => $val) {
            $order = [$val => 'DESC'];
            break;
        }
        $this->model = $this->model->cache(5)->group($group)->field($field)->order($order);
        $list        = $this->_list();
        $header      = [];
        foreach ($default_header_field as $key => $val) {
            foreach ($field as $k => $v) {
                if ($key == $v) {
                    $header[$key] = $val;
                }
            }
        }
        return ['list' => $list, 'header' => $header, 'total_row_field_list' => $total_row_field_list];
    }
}