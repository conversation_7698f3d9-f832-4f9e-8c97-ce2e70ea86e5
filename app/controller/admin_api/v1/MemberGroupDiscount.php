<?php

namespace app\controller\admin_api\v1;

use app\model\MemberGroup as MemberGroupModel;
use Exception;

class MemberGroupDiscount extends BasicAdminApi
{
    /**
     *编辑卡券折扣
     * @access public
     * @return void
     * @throws Exception
     */
    public function edit()
    {
        $params            = $this->params;
        $bid               = $this->get_bid();
        $db                = new \app\model\MemberGroupDiscount();
        $type              = $params['type'];
        $discount          = $params['discount'];
        $object_guid       = $params['object_guid'];
        $member_group_guid = $params['member_group_guid'];
        if ($discount < 0) {
            error('系数需要大于0');
        }
        switch ($type) {
            case 1: // 折扣系数
                if ($discount == 0) {
                    error('系数不能等于0');
                }
                if ($discount > 1) {
                    error('系数不能大于1, 0.01-1.00之间');
                }
            case 2: //会员级别 商品佣金

        }
        $map  = [
            'bid'               => $bid,
            'type'              => $type,
            'object_guid'       => $object_guid,
            'member_group_guid' => $member_group_guid,
        ];
        $note = $db->where($map)->findOrEmpty();
        if (!$note->isEmpty()) {
            $update_data = ['discount' => $discount];
            $db::update($update_data, $map);
            success('修改成功!');
        } else {
            $data = [
                'guid'              => create_guid(),
                'bid'               => $bid,
                'type'              => $type,
                'object_guid'       => $object_guid,
                'member_group_guid' => $member_group_guid,
                'discount'          => $discount,
            ];
            $db->save($data);
            success('修改成功');
        }
    }

    /**
     *折扣
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $referer = $this->request->header('referer');
        $bid     = $this->get_bid();
        $arr     = tools()::parse_url_params($referer);//将url解析成参数数组
        $arr     = $this->params;
        if (empty($arr['guid'])) {
            error('缺少guid参数');
        }
        $db          = new MemberGroupModel();
        $object_guid = $arr['guid'];
        $type        = $arr['type'];
        unset($this->params['guid']);
        unset($this->params['type']);
        $join        = [
            ['member_group_discount mgd', "mg.guid = mgd.member_group_guid and mg.bid=mgd.bid AND mgd.type='$type' and mgd.object_guid='$object_guid'", 'LEFT'],
        ];
        $map         = [
            ['mg.bid', '=', $bid],
        ];
        $field       = [
            'mg.guid'                                 => 'member_group_guid',
            'mg.name',
            "IFNULL(mgd.object_guid, '$object_guid')" => 'object_guid',
            'IFNULL(mgd.discount, 1)'                 => 'discount',
        ];
        $this->model = $db->alias('mg')->field($field)->join($join)->where($map)->order(['mg.create_time' => 'DESC']);
        result($this->_list());
    }
}