<?php

namespace app\controller\admin_api\v1;

use app\model\PayBrokerage as PayBrokerageModel;
use app\common\tools\Excel;
use Exception;

class PayBrokerage extends BasicAdminApi
{
    /**
     *所有分润
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function all_brokerage()
    {
        $params = $this->params;
        $join   = [
            ['business b', 'pb.order_bid = b.guid'],
            ['pay_order po', 'pb.order_bid = po.bid AND pb.order_guid = po.guid'],
            ['business b2', 'pb.bid = b2.guid'],
        ];
        $db     = new PayBrokerageModel();
        if (!empty($params['_total'])) {
            //合计
            $field      = [
                'count(1)'                                            => 'count',
                'IFNULL(sum(pb.brokerage_money*pb.brokerage_rate),0)' => 'sum',
            ];
            $data       = $db->alias('pb')->join($join)->field($field);
            $data       = $this->_find($data);
            $total_text = '合计分润:<b>' . $data['count'] . '</b>笔,共<b>' . number_format($data['sum'], 2) . '</b>元';
            success($total_text);
        }
        $field       = [
            'pb.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
            'po.trade_time'                             => 'trade_time',
            'b2.account'                                => 'agent_account'
            //           "CONCAT(u.name,'(',u.account,')')"          => 'service',
        ];
        $this->model = $db->alias('pb')->join($join)->field($field)->order(['pb.create_time' => 'DESC', 'po.trade_time' => 'DESC', 'pb.level' => 'DESC']);
        result($this->_list());
    }

    /**
     *导出分润记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_all_brokerage()
    {
        $join  = [
            ['business b', 'pb.order_bid = b.guid'],
            ['pay_order po', 'pb.order_bid = po.bid AND pb.order_guid = po.guid'],
            ['business b2', 'pb.bid = b2.guid'],
        ];
        $field = [
            'pb.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
            'po.trade_time'                             => 'trade_time',
            'b2.account'                                => 'agent_account',
            'pb.brokerage_money*pb.brokerage_rate'      => 'really_brokerage_money',
            '(po.total_fee)/100'                        => 'total_fee',
            'po.trade_time',
            'po.bill_number',
            'po.third_trade_no',
            //           "CONCAT(u.name,'(',u.account,')')"          => 'service',
        ];
        $db    = new PayBrokerageModel();
        $data  = $db->alias('pb')->field($field)->join($join)->order('po.create_time', 'DESC');
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'agent_account'          => '代理账号',
            'business_account'       => '商家账号',
            'business_name'          => '商家名称',
            'bill_number'            => '订单号',
            'total_fee'              => '金额',
            'fee_rate'               => '费率',
            'fee'                    => '手续费金额',
            'brokerage_rate'         => '分润比例',
            'really_brokerage_money' => '分润金额',
            'third_trade_no'         => '交易单号',
            'trade_time'             => '交易时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *我的分润
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my_brokerage()
    {
        $bid     = $this->get_bid();
        $map     = [
            ['pb.bid', '=', $bid]
        ];
        $db_rule = new \app\model\Rule();
        if ($db_rule->is_admin() === false) {
            $map[] = ['pb.user_guid', '=', $this->get_user_guid()];
        }
        $join = [
            ['business b', 'pb.order_bid = b.guid'],
            ['pay_order po', 'pb.order_bid = po.bid AND pb.order_guid = po.guid'],
            ['user u', 'pb.bid = u.bid AND pb.user_guid = u.guid'],
        ];
        $db   = new PayBrokerageModel();
        if (!empty($params['_total'])) {
            //合计
            $field      = [
                'count(1)'                                            => 'count',
                'IFNULL(sum(pb.brokerage_money*pb.brokerage_rate),0)' => 'sum',
            ];
            $data       = $db->alias('pb')->join($join)->field($field);
            $data       = $this->_find($data);
            $total_text = '合计分润:<b>' . $data['count'] . '</b>笔,共<b>' . number_format($data['sum'], 2) . '</b>元';
            success($total_text);
        }
        $field       = [
            'pb.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
            //           "CONCAT(u.name,'(',u.account,')')"          => 'service',
            'po.trade_time'                             => 'trade_time',
            'u.account'                                 => 'user_account'
        ];
        $this->model = $db->alias('pb')->join($join)->field($field)->where($map)->order(['pb.create_time' => 'DESC', 'po.trade_time' => 'DESC', 'pb.level' => 'DESC']);
        result($this->_list());
    }

    /**
     *导出我的分润
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_my_brokerage()
    {
        $bid = $this->get_bid();
        $map = [
            ['pb.bid', '=', $bid]
        ];
        if (!is_array($map)) {
            error('系统繁忙,请稍后再试~');
        }
        $join  = [
            ['business b', 'pb.order_bid = b.guid'],
            ['pay_order po', 'pb.order_bid = po.bid AND pb.order_guid = po.guid'],
            ['user u', 'pb.bid = u.bid AND pb.user_guid = u.guid'],
        ];
        $field = [
            'pb.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
            'pb.brokerage_money*pb.brokerage_rate'      => 'really_brokerage_money',
            //           "CONCAT(u.name,'(',u.account,')')"          => 'service',
            '(po.total_fee)/100'                        => 'total_fee',
            'po.trade_time',
            'po.bill_number',
            'po.third_trade_no',
            'u.account'                                 => 'user_account'
        ];
        $db    = new PayBrokerageModel();
        $data  = $db->alias('pb')->field($field)->join($join)->where($map)->order('po.create_time', 'DESC');
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'business_account'       => '商家账号',
            'business_name'          => '商家名称',
            'user_account'           => '业务员工号',
            'bill_number'            => '订单号',
            'total_fee'              => '金额',
            'fee_rate'               => '费率',
            'fee'                    => '手续费金额',
            'brokerage_rate'         => '分润比例',
            'really_brokerage_money' => '分润金额',
            'third_trade_no'         => '交易单号',
            'trade_time'             => '交易时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }
}