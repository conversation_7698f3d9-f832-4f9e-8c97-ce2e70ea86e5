<?php

namespace app\controller\admin_api\v1;

use app\model\Business;
use app\model\Coupon;
use app\model\CouponGenerateNote as CouponGenerateNoteModel;
use app\model\CouponSendNote;
use app\model\CouponUsedNote;
use app\model\GoodsOrder;
use app\model\GoodsOrderItem;
use app\model\User;
use app\common\tools\Excel;
use app\common\tools\Visitor;
use Cyokup\EasyAiChat\EasyAiChat;
use Exception;
use think\facade\Db;

class CouponGenerateNote extends BasicAdminApi
{

    /**
     *检查
     * @access protected
     * @param integer $count
     * @return mixed
     * @throws Exception
     */
    protected function check($count)
    {
        $max = 100000;
        if ($count > $max) {
            error('一次性最多导出' . $max . '条记录,本次数据有' . $count . '条');
        }
    }

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db    = new CouponGenerateNoteModel();
        $bid   = $this->get_bid();
        $join  = [
            //    ['coupon c', " cgn.coupon_guid = c.guid AND cgn.bid = c.bid AND c.delete_time IS NULL"],
            ['coupon c', " cgn.coupon_guid = c.guid AND cgn.bid = c.bid"],
        ];
        $field = [
            'c.name',
            'cast(c.value as decimal(9,0))' => 'value',
            'cgn.*',
            Db::raw("date_format(cgn.expire_time,'%Y-%m-%d') as expire_time"),
            Db::raw("date_format(cgn.availability_time,'%Y-%m-%d') as availability_time"),
        ];
        $map[] = ['cgn.bid', '=', $bid];
        //默认查看自己的子集
        $db_user     = new User();
        $map         = [
            ['cgn.operator_user_id|cgn.owner_user_id', 'in', $db_user->getChildUserIdArray()]
        ];
        $this->model = $db->alias('cgn')->join($join)->field($field)->where($map);
        result($this->_list());
    }

    public function parse_image()
    {
        $params       = $this->params;
        $image_base64 = $params['image_base64'] ?? '';

        if (empty($image_base64)) {
            error('请上传图片');
        }

        $aiModel = new EasyAiChat();

        // 构建AI提示词
        $prompt = "请分析这张图片中的卡券信息，
        提取字段并以JSON数组格式返回，
        每个卡券包含：name(卡券名称), price(价格数字), amount(数量数字), expire_time(失效日期YYYY-MM-DD), specs(规格), prefix(卡前缀), start_code(起始卡号), skip_number(跳数字)。
        只返回JSON数组，
        注意以下几点:
        1. 只要识别到 XXX型 就优先用他作为卡券名称
        2.如果没有明确的过期时间 则用2099-12-31填充
        3.卡前默认就为空即可
        4.如果有卡背面有卡号 则用来作为起始卡号
        5.跳数字默认为空,无需识别
        6.所有都是字符串 不要嵌套数组 特别是specs
        7.规格直接拼接文本 不需要嵌套数组,可以适当增加括号增加可读性
        8.XXXX张这种请视为数量
        ";

        $messages = [
            [
                'role'    => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt
                    ],
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => 'data:image/jpeg;base64,' . $image_base64
                        ]
                    ]
                ]
            ]
        ];

        $response = $aiModel::glm4v()->chat($messages);

        // GLM-4V返回的是数组 [$content, $tokens]，取第一个元素
        $content = is_array($response) ? $response[0] : $response;

        // 使用通用的JSON清理方法
        result($content);


    }

    private function parseAiJson($cleanResponse)
    {
        // 直接解析已经清理过的JSON
        dump($cleanResponse);
        dump($cleanResponse);
        dump($cleanResponse);
        return json_decode($cleanResponse, true);
    }

    public function batch_add()
    {
        $params   = $this->params;
        $bid      = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $file_url = $params['file'] ?? '';
        $data     = $params['data'] ?? [];
        if (!empty($file_url)) {
            $file                 = new Excel();
            $allowField           = [
                'name'        => '卡券名称',
                'price'       => '价格',
                'specs'       => '规格',
                'amount'      => '数量',
                'prefix'      => '卡前缀',
                'expire_time' => '卡失效日期'
            ];
            $optional_field_array = [
                'start_code'  => '起始卡号',
                'skip_number' => '跳数字'
            ];
            //卡券名称	价格	规格	数量
            $arr = $file->load($file_url)->excelToArray($allowField, $optional_field_array);
        } elseif (!empty($data)) {
            $arr = $data;
        } else {
            error('请上传文件或填写数据');
        }

        //循环校验合法数据 得到要导入的数组
        foreach ($arr as $key => $val) {
            // 验证excel数据
            $name              = $val['name'];
            $amount            = $val['amount'];
            $specs             = $val['specs'];
            $price             = $val['price'];
            $prefix            = $val['prefix'];
            $expire_time       = $val['expire_time'];
            $start_code        = $val['start_code'];
            $skip_number       = $val['skip_number'];
            $skip_number       = tools()::remove_empty_string($skip_number);
            $skip_number       = tools()::sbc2_dbc($skip_number);
            $skip_number       = explode(',', trim($skip_number)); //多个跳数字逗号隔开
            $skip_number       = array_filter($skip_number); //否则会变成跳过0
            $skip_number_array = [];
            foreach ($skip_number as $k => $v) {
                $skip_number_array[$v] = 'on'; // {4: "on",7:"on"}
            }
            $val['bid'] = $bid;
            //查找卡券
            $db_coupon               = new Coupon();
            $coupon_info             = $db_coupon->get_coupon_info_by_data($bid, $val);
            $coupon_guid             = $coupon_info['guid'];
            $db_coupon_generate_note = new CouponGenerateNoteModel();
            //创建卡密
            $code_type = empty($start_code) ? 1 : 2;
            $data      = [
                'bid'               => $bid,
                'way'               => 1, //1系统生成 2文件导入
                'code_type'         => $code_type, //1系统生成 2指定开始卡号
                'start_code'        => $start_code, //指定开始卡号
                'coupon_guid'       => $coupon_guid,
                'coupon_way'        => 1,
                'amount'            => $amount,
                'prefix'            => $prefix,
                'availability_time' => date('Y-m-d 00:00:00'),
                'expire_time'       => $expire_time,
                'skip_number'       => $skip_number_array,
            ];
            $result    = $db_coupon_generate_note->add($data);
            if ($result === false) {
                error('创建失败' . $db_coupon_generate_note->getError());
            }
        }
        success('快速生成成功');
    }

    public function batch_export()
    {
        $bid        = $this->get_bid();
        $guid       = $this->params['guid'];
        $guid_array = explode(',', $guid);
        if (empty($guid_array)) {
            error('请勾选要导出的卡密生成记录');
        }
        $file_list       = [];
        $path_guid       = create_guid();
        $file_name_array = [];
        $i               = 0;
        $total_num       = 0;
        foreach ($guid_array as $guid) {
            $i++;
            $db_coupon_generate_note = new CouponGenerateNoteModel();
            $export_data             = $db_coupon_generate_note->get_export_data($bid, $guid);
            if ($export_data === false) {
                error($db_coupon_generate_note->getError());
            }
            $file      = new Excel();
            $header    = $export_data['header'];
            $data      = $export_data['data'];
            $file_name = $export_data['file_name'];
            if (in_array($file_name, $file_name_array)) {
                $file_name = $file_name . '_' . $i;
            }
            $file_name_array[] = $file_name;
            $local_file_name   = $file->arrayToExcel($header, $data, $file_name, $path_guid, true);
            $total_num         += count($data);
            $file_list[]       = $local_file_name;
        }
        $zip_file_name = $path_guid . '.zip';
        $result        = tools()::zip(tools()::get_absolute_path_disk() . $path_guid, tools()::get_absolute_path_disk() . $zip_file_name);
        if ($result) {
            $db_business        = new Business();
            $business_info      = $db_business->get_business_info_by_account_or_guid($bid);
            $download_file_name = $business_info['business_name'] . '-(' . count($file_list) . '份合计' . $total_num . '张卡密)';
            tools()::auto_download($zip_file_name, $download_file_name);
        } else {
            error('文件压缩失败,请反馈给客服!');
        }
    }

    /**
     *导出生成记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function export()
    {
        $bid                     = $this->get_bid();
        $guid                    = $this->params['guid'];
        $db_coupon_generate_note = new CouponGenerateNoteModel();
        $export_data             = $db_coupon_generate_note->get_export_data($bid, $guid);
        if ($export_data === false) {
            error($db_coupon_generate_note->getError());
        }
        $file      = new Excel();
        $header    = $export_data['header'];
        $data      = $export_data['data'];
        $file_name = $export_data['file_name'];
        $file->arrayToExcel($header, $data, $file_name);
    }


    /**
     *删除生成记录以及关联的所有记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (Visitor::is_readonly() === false) {
            $password = $params['password'];
            $db_user  = new User();
            if (!$db_user->verify_user_password($password)) {
                error('您输入的密码不正确');
            }
        }
        $generate_note_guid          = $params['generate_guid'];
        $item                        = 0;
        $bill_number                 = tools()::get_bill_number();
        $db_coupon_send_note         = new CouponSendNote();
        $map                         = [
            ['bid', '=', $bid],
            ['generate_guid', '=', $generate_note_guid]
        ];
        $coupon_send_note_guid_array = $db_coupon_send_note->where($map)->column('guid');
        //1 删除coupon_used_note
        $db_coupon_used_note = new CouponUsedNote();
        $map                 = [
            ['bid', '=', $bid],
            ['coupon_send_guid', 'IN', $coupon_send_note_guid_array]
        ];
        $item                += (int)$db_coupon_used_note->delete_to_recycle_bin($map, $bill_number);
        // 2 查询关联订单guid
        $map                    = [
            ['bid', '=', $bid],
            ['coupon_send_note_guid', 'IN', $coupon_send_note_guid_array]
        ];
        $db_goods_order         = new GoodsOrder();
        $goods_order_guid_array = $db_goods_order->where($map)->column('guid');
        //3 通过订单guid删除 goods_order_goods_item
        $db_goods_order_item = new GoodsOrderItem();
        $map                 = [
            ['bid', '=', $bid],
            ['order_guid', 'IN', $goods_order_guid_array]
        ];
        $item                += (int)$db_goods_order_item->delete_to_recycle_bin($map, $bill_number);
        //4 删除 goods_order
        $map  = [
            ['bid', '=', $bid],
            ['guid', 'IN', $goods_order_guid_array]
        ];
        $item += (int)$db_goods_order->delete_to_recycle_bin($map, $bill_number);
        // 5 删除 coupon_send_note
        $map  = [
            ['bid', '=', $bid],
            ['generate_guid', '=', $generate_note_guid]
        ];
        $item += (int)$db_coupon_send_note->delete_to_recycle_bin($map, $bill_number);
        // 6 删除
        $db_coupon_generate_note = new CouponGenerateNoteModel();
        $map                     = [
            ['bid', '=', $bid],
            ['guid', '=', $generate_note_guid]
        ];
        $item                    += (int)$db_coupon_generate_note->delete_to_recycle_bin($map, $bill_number);
        // 提交事务
        success('数据清空成功,共计删除' . $item . '条记录!');
    }

    /**
     *删除生成记录以及关联的所有记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function delete()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $password = $params['password'];
        if ($password != get_system_config('safe_password')) {
            error('安全密码不正确,请联系客服');
        }
        $generate_note_guid = $params['generate_guid'];
        $result             = true;
        $item               = 0;
        // 启动事务
        Db::startTrans();
        try {
            $db_coupon_send_note         = new CouponSendNote();
            $map                         = [
                ['bid', '=', $bid],
                ['generate_guid', '=', $generate_note_guid]
            ];
            $coupon_send_note_guid_array = $db_coupon_send_note->where($map)->column('guid');
            //1 删除coupon_used_note
            $db_coupon_used_note = new CouponUsedNote();
            $map                 = [
                ['bid', '=', $bid],
                ['coupon_send_guid', 'IN', $coupon_send_note_guid_array]
            ];
            $item                += (int)$db_coupon_used_note->where($map)->delete();
            // 2 查询关联订单guid
            $map                    = [
                ['bid', '=', $bid],
                ['coupon_send_note_guid', 'IN', $coupon_send_note_guid_array]
            ];
            $db_goods_order         = new GoodsOrder();
            $goods_order_guid_array = $db_goods_order->where($map)->column('guid');
            //3 通过订单guid删除 goods_order_goods_item
            $db_goods_order_item = new GoodsOrderItem();
            $map                 = [
                ['bid', '=', $bid],
                ['order_guid', 'IN', $goods_order_guid_array]
            ];
            $item                += (int)$db_goods_order_item->where($map)->delete();
            //4 删除 goods_order
            $map  = [
                ['bid', '=', $bid],
                ['guid', 'IN', $goods_order_guid_array]
            ];
            $item += (int)$db_goods_order->where($map)->delete();
            // 5 删除 coupon_send_note
            $map  = [
                ['bid', '=', $bid],
                ['generate_guid', '=', $generate_note_guid]
            ];
            $item += (int)$db_coupon_send_note->where($map)->delete();
            // 7 删除
            $db_coupon_generate_note = new CouponGenerateNote();
            $map                     = [
                ['bid', '=', $bid],
                ['guid', '=', $generate_note_guid]
            ];
            $item                    += (int)$db_coupon_generate_note->where($map)->delete();
            // 提交事务
            Db::commit();
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $result = false;
            wr_log('数据清空失败' . $e->getMessage());
        }
        if ($result === false) {
            error('数据清空失败,请联系客服');
        } else {
            success('数据清空成功,共计删除' . $item . '条记录!');
        }
    }
}
