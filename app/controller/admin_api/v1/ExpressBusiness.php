<?php

namespace app\controller\admin_api\v1;

use Exception;
use think\facade\Db;

class ExpressBusiness extends BasicAdminApi
{

    /**
     * 代理商列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\Express();
        $bid         = $this->get_bid();
        $join        = [
            ['express_business b', "a.code = b.express_code AND b.bid='$bid'", 'LEFT'],
        ];
        $field       = [
            Db::raw("IFNULL(b.sort,a.sort) as sort"),
            'a.name'       => 'name',
            'a.alias_name' => 'alias_name',
            'a.code'       => 'code',
            Db::raw("IFNULL(b.status,a.status) as status"),
        ];
        $map1        = [
            ['a.status', '=', 1],
            //           ['b.status', '=', 1]
        ];
        $map2        = [
            ['b.status', 'IN', [0, 1]],
            //           ['b.status', '=', 1]
        ];
        $subQuery    = $db->alias('a')->join($join)->whereOr([$map1, $map2])->field($field)->buildSql();
        $this->model = Db::table($subQuery . ' a')->order(['status' => 'DESC', 'sort' => 'ASC', 'name' => 'ASC']);
        result($this->_list());
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params              = $this->params;
        $code                = $params['code'];
        $bid                 = $this->get_bid();
        $db_express_business = new \app\model\ExpressBusiness();
        $map                 = [
            ['bid', '=', $bid],
            ['express_code', '=', $code],
        ];
        $id                  = $db_express_business->where($map)->value('id');
        if ($id) {
            error('您已经添加过该快递了哦');
        }
        $insert_data = [
            'status'       => 1,
            'sort'         => 1,
            'express_code' => $code,
            'bid'          => $bid,
        ];
        $db_express_business->save($insert_data);
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params              = $this->params;
        $code                = $params['code'];
        $bid                 = $this->get_bid();
        $db_express_business = new \app\model\ExpressBusiness();
        $map                 = [
            ['bid', '=', $bid],
            ['express_code', '=', $code],
        ];
        $db_express_business->where($map)->delete();
        success('删除成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params              = $this->params;
        $code                = $params['code'];
        $bid                 = $this->get_bid();
        $db_express_business = new \app\model\ExpressBusiness();
        $map                 = [
            ['bid', '=', $bid],
            ['express_code', '=', $code],
        ];
        $id                  = $db_express_business->where($map)->value('id');
        if ($id) {
            $map[]       = ['id', '=', $id];
            $update_data = [];
            if (isset($params['sort'])) {
                $update_data['sort'] = $params['sort'];
            }
            if (isset($params['status'])) {
                $update_data['status'] = $params['status'];
            }
            if (empty($update_data)) {
                error('修改失败');
            }
            $db_express_business::update($update_data, $map);
            success('修改成功');
        } else {
            $sort        = $params['sort'] ?? 1;
            $status      = $params['status'] ?? 1;
            $insert_data = [
                'status'       => $status,
                'sort'         => $sort,
                'express_code' => $code,
                'bid'          => $bid,
            ];
            $db_express_business->save($insert_data);
            success('修改成功!');
        }
    }
}
