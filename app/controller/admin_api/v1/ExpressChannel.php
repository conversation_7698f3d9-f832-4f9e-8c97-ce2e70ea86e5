<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\ExpressChannel as ExpressChannelModel;
use Exception;

class ExpressChannel extends BasicAdminApi
{
    /**
     *获取通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new ExpressChannelModel();
        $this->model = $db->order(['create_time' => 'DESC', 'id' => 'DESC']);
        result($this->_list());
    }

    /**
     *添加通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params              = $this->params;
        $form_data           = tools()::_parse_fs_form_data($params, 'channel_parameter');
        $db                  = new ExpressChannelModel();
        $form_data['status'] = $form_data['status'] ?? 0;
        $update              = $db->save($form_data);
        if ($update) {
            success('添加成功');
        } else {
            error('添加失败');
        }
    }

    /**
     *编辑通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        if (!empty($params['id'])) {
            // 通过列表点击 开启 关闭 等操作的编辑
            $update_data       = $params;
            $update_data['id'] = $params['id'];
        } else {
            // 详情页编辑
            $update_data           = tools()::_parse_fs_form_data($params, 'channel_parameter');
            $update_data['status'] = $update_data['status'] ?? 0;
        }
        $id     = intval($update_data['id']);
        $db     = new ExpressChannelModel();
        $map    = [['id', '=', $id]];
        $update = $db::update($update_data, $map);
        if ($update) {
            success('更新成功');
        } else {
            error('更新失败');
        }
    }

    /**
     *获取参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_parameter()
    {
        $id      = $this->params['id'];
        $db      = new ExpressChannelModel();
        $map     = [['id', '=', $id]];
        $channel = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($channel['channel_parameter']));
    }
}