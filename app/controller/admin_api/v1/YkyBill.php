<?php

namespace app\controller\admin_api\v1;


use Exception;

class YkyBill extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\YkyBill();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
        ];
        $this->model = $db->where($map)->append(['info_text'])->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}