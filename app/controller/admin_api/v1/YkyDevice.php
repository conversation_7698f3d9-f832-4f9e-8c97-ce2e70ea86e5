<?php

namespace app\controller\admin_api\v1;

use app\model\YkyDevice as YkyDeviceModel;
use Exception;

class YkyDevice extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyDeviceModel();
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['sn_code', 'LIKE', 'YKA%'],
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db          = new YkyDeviceModel();
        $this->model = $db;
        $db->edit($this->params);
        success('编辑成功');
    }
}