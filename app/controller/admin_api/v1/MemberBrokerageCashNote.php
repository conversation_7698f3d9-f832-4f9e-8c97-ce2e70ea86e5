<?php

namespace app\controller\admin_api\v1;

use app\model\MemberBrokerageNote;
use Exception;

class MemberBrokerageCashNote extends BasicAdminApi
{
    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\MemberBrokerageCashNote();
        $join        = [
            ['member m', 'mbcn.member_guid = m.guid AND mbcn.bid = m.bid'],
            ['user u', 'mbcn.operator_user_guid = u.guid AND mbcn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'mbcn.*',
            'u.account',
            "CONCAT(m.id,'-',m.name)" => 'member_info',
        ];
        $map         = [
            ['mbcn.bid', '=', $this->get_bid()],
        ];
        $this->model = $db->alias('mbcn')->order(['mbcn.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        result($this->_list());
    }


    /**
     *审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $params                         = $this->params;
        $guid                           = $params['guid'];
        $db_member_brokerage_cash_note  = new \app\model\MemberBrokerageCashNote();
        $db_business                    = new \app\model\Business();
        $map_member_brokerage_cash_note = [
            ['guid', '=', $guid]
        ];
        if (!$db_business->is_admin()) {
            $map_member_brokerage_cash_note[] = ['bid', '=', $this->get_bid()];
        }
        $note                     = $db_member_brokerage_cash_note->where($map_member_brokerage_cash_note)->find()->toArray();
        $db_member_brokerage_note = new MemberBrokerageNote();
        $bill_number              = $note['bill_number'];
        $unique_code              = $bill_number . '_cash';
        $data                     = [
            'guid'          => create_guid(),
            'bid'           => $note['bid'],
            'member_guid'   => $note['member_guid'],
            'way'           => 1, //卡密兑换
            'type'          => -1, //充值
            'brokerage'     => $note['cash_money'],
            'unique_code'   => $unique_code,
            'relation_guid' => $guid,
            'memo'          => '[提现支出],关联提现订单号:' . $bill_number,
        ];
        $recharge                 = $db_member_brokerage_note->recharge_brokerage($data);
        // $db_member                = new \app\model\Member();
        //触发通知
        //       $data['create_time'] = format_timestamp();
        //       $data['bid']         = $note['bid'];
        //       $data['company']     = $note['company'];
        //       $data['member_guid'] = $note['member_guid'];
        // notify()->set_key_name(NotifyService::RegisteredBusinessSuccessfully)->set_member_mobile($mobile)->set_data($data)->set_map($note)->set_bid($bid)->send();
        $update_data = [
            'status'       => 2,
            'examine_time' => format_timestamp()
        ];
        $db_member_brokerage_cash_note::update($update_data, $map_member_brokerage_cash_note);
        success('审核通过');
    }
}