<?php

namespace app\controller\admin_api\v1;

use app\model\Business;
use app\model\User;
use app\model\UserBindNote;
use app\model\WeappUserInfo;
use app\common\service\SmsService;
use app\common\service\TokenService;
use app\common\tools\Visitor;
use Exception;

class Passport extends BasicAdminApi
{
    /**
     *发送短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_sms_code()
    {
        $params      = $this->params;
        $mobile      = $params['mobile'];
        $type        = $params['type']; //reset_password
        $success_key = __FUNCTION__ . ':' . $type . ':' . date("Y-M-d");
        $total_num   = (int)cache($success_key);
        if ($total_num > 0) {
            error('暂不支持在线找回密码,请联系客服');
        }
        switch ($type) {
            case 'reset_password':
                $db_business  = new Business();
                $map_business = [
                    ['mobile', '=', $mobile],
                    ['delete_time', 'null', null],
                ];
                $count        = $db_business->where($map_business)->count();
                if ($count > 1) {
                    error('暂不支持当前手机号重置密码,请联系客服');
                }
                if ($count == 0) {
                    error('暂不支持当前手机号重置密码,请联系客服!');
                }
                break;
            default:
                break;
        }
        $sms = SmsService::get_instance();
        $sms->send_sms_code($mobile);
        // 判断手机号是否有绑定多个商家 暂时不支持绑定多个商家的重置
        // 判断今天最多发送多少次验证码
        cache($success_key, $total_num + 1);
        success('验证码发送成功!');
    }

    /**
     *发送短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reset_password()
    {
        $params         = $this->params;
        $mobile         = $params['mobile'];
        $sms_code       = $params['sms_code'];
        $login_password = $params['login_password'];
        $repassword     = $params['repassword'];
        $sms            = SmsService::get_instance();
        $check          = tools()::check_password($login_password);
        if ($check !== true) {
            error($check);
        }
        $sms->verify_sms_code($mobile, $sms_code);
        $db_business   = new Business();
        $map_business  = [
            ['mobile', '=', $mobile],
            ['delete_time', 'null', null],
        ];
        $business_info = $db_business->where($map_business)->order(['id' => 'DESC'])->findOrFail();
        $db_user       = new User();
        $update_data   = [
            'password'           => $login_password,
            'login_failed_times' => 0
        ];
        $map_user      = [
            ['bid', '=', $business_info['guid']],
            ['account', '=', 10000]
        ];
        $db_user::update($update_data, $map_user);
        wr_log('商家账号:' . $business_info['account'] . '-' . $business_info['business_name'] . '密码重置成功', 1);
        success('密码重置成功,请用新密码重新登录');
    }

    public function login()
    {
        $params = $this->params;
        // 解码数据
        $code   = $params['code'];
        $bid    = $params['bid'];
        $appid  = $params['appid'];
        $mini   = weixin($appid)::WeMiniCrypt();
        $result = $mini->session($code);
        if (empty($result['openid'])) {
            throw new Exception($result['errmsg'], $result['errcode']);
        }
        $db_weapp_user_info = new WeappUserInfo();
        $db_weapp_user_info->save_user_info($appid, $result);

        $openid            = $result['openid'];
        $db_user_bind_note = new UserBindNote();
        $map               = [
            //'bid'    => $bid,
            ['openid', '=', $openid],
            ['appid', '=', $appid],
            ['status', '=', 1],
        ];
        $user_bind_note    = $db_user_bind_note->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
        if ($user_bind_note->isEmpty()) {
            $token_data = [
                'exp'    => 3600,
                'bid'    => $bid,
                'appid'  => $appid,
                'openid' => $openid,
                'from'   => 4,
                'way'    => 2,
            ];
            $token      = TokenService::encode($token_data);
            $data       = [
                'status' => 0,
                'token'  => $token,
            ];
            result($data);
        }
        $user_info = [
            'appid'  => $appid,
            'openid' => $openid,
            'bid'    => $user_bind_note['bid'],
            'guid'   => $user_bind_note['user_guid'],
        ];
        // 获取逻辑里面会自动写入cookie,用于后面身份校验
        $db_user = new User();
        $data    = $db_user->get_access_token($user_info, 'client_credential');
        $data    = [
            'status' => 1,
            'token'  => $data
        ];
        result($data);
    }

    /**
     * 绑定用户
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function bind_user()
    {
        $params = $this->params;
        $token  = $params['token'];
        //        $bid    = $params['bid'] ?? null;
        $jwt = TokenService::verify($token);
        //        $token_bid    = $jwt['bid'] ?? null;
        $token_appid  = $jwt['appid'];
        $token_openid = $jwt['openid'];
        $token_from   = $jwt['from'] ?? 0;
        $token_way    = $jwt['way'] ?? (Visitor::is_mini_app_browser() ? 2 : 1);

        $account      = $params['account'];
        $user_account = $params['user_account'];
        $password     = $params['password'];
        //验证商家信息
        $db_business   = new Business();
        $business_info = $db_business->login_verify($account);
        if (!$business_info) {
            error($db_business->getError());
        }
        //验证工号
        $db_user                = new User();
        $data['business_guid']  = $business_info['guid'];
        $data['login_password'] = $password;
        $data['user_account']   = $user_account;
        $user_info              = $db_user->login_verify($data);
        if ($user_info === false) {
            // 登录失败要记录在日志里
            error($db_user->getError());
        }
        $user_guid         = $user_info['guid'];
        $bind_bid          = $business_info['guid'];
        $db_user_bind_note = new UserBindNote();

        $bind_data = [
            'bind_bid'       => $bind_bid,
            'bind_user_guid' => $user_guid,
            'appid'          => $token_appid,
            'openid'         => $token_openid,
            'way'            => $token_way, //1 公众号 2 小程序
            'from'           => $token_from,//来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程
        ];
        $result    = $db_user_bind_note->bind_user($bind_data);
        if ($result === false) {
            error($db_user_bind_note->getError());
        }
        success('绑定成功');
    }

    /**
     * 绑定系统用户(从用户列表扫码直接绑定,已经改成扫场景二维码绑定了,以下方法暂时没有经过验证,后续需要统一用上面方法)
     * @access public
     * @return void
     * @throws Exception
     */
    public function user()
    {
        $params = $this->params;
        $openid = $this->get_openid();
        //$bid       = $this->get_bid();
        $token     = $params['token'];
        $cache_key = 'user_bind:' . $token;
        $user_info = cache($cache_key);
        if (empty($user_info)) {
            error('链接失效了,请重新扫码');
        }
        $bind_bid          = $user_info['bid'];
        $config            = get_config_by_bid($bind_bid);
        $appid             = $config['appid'];
        $bind_user_guid    = $user_info['user_guid'];
        $map               = [
            ['bid', '=', $bind_bid],
            ['user_guid', '=', $bind_user_guid],
            ['appid', '=', $appid],
            ['openid', '=', $openid],
            ['status', '=', 1],
        ];
        $db_user_bind_note = new UserBindNote();
        $bind_note         = $db_user_bind_note->where($map)->count();
        if (empty($bind_note)) {
            $insert_data = [
                'guid'      => create_guid(),
                'bid'       => $bind_bid,
                'user_guid' => $bind_user_guid,
                'appid'     => $appid,
                'openid'    => $openid,
                'status'    => 1
            ];
            $db_user_bind_note->save($insert_data);
            success('绑定成功');
        } else {
            error('您已经绑定过!');
        }
    }
}