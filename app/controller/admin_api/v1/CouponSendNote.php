<?php

namespace app\controller\admin_api\v1;


use app\common\service\NotifyService;
use app\model\Coupon;
use app\model\CouponActiveOrder;
use app\model\CouponSendNote as CouponSendNoteModel;
use app\model\User;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;

class CouponSendNote extends BasicAdminApi
{

    /**
     *获取卡号详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_code_password_info()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $code_password = $params['code_password'] ?? '';
        $code          = $params['code'] ?? '';
        $password      = $params['password'] ?? '';
        if ($code_password) {
//            $code_password_length = 12;
//            $code_length          = 6;
            $password_length = 6;
//            if (strlen($code_password) !== $code_password_length) {
//                error('目前仅支持' . $code_password_length . '位卡号');
//            }
            if (strlen($code_password) <= 6) {
                error('卡密长度必需大于6位数');
            }
            //100002123456
            $code     = substr($code_password, 0, strlen($code_password) - $password_length);
            $password = substr($code_password, -$password_length);
        } elseif ($code && $password) {

        } else {
            error('请输入卡号和密码');
        }
        $data['code']     = $code;
        $data['password'] = $password;

        $db_coupon_send_note = new CouponSendNoteModel();
        $result              = $db_coupon_send_note->verify($bid, $data);
        if ($result === false) {
            error($db_coupon_send_note->getError());
        }
        $coupon_guid = $result['coupon_guid'];
        $db_coupon   = new Coupon();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info = $db_coupon->where($map)->find();
        if ($coupon_info['type'] !== 3) {
            error('该卡不是充值卡,不支持直接扣费');
        }
        result($result);
    }

    public function deduct_money()
    {
        $bid                   = $this->get_bid();
        $params                = $this->params;
        $db                    = new CouponSendNoteModel();
        $deduct_money          = $params['deduct_money'];
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $data                  = [
            'bid'                   => $bid,
            'coupon_send_note_guid' => $coupon_send_note_guid,
            'way'                   => 4, //直接扣除
            'used_num'              => 0,
            'used_value'            => $deduct_money, //由于只能转赠数量 ,所以使用金额是0
            'user_guid'             => $this->get_user_guid()
        ];
        $result                = $db->use_coupon($data);
        if ($result === false) {
            error($db->getError());
        }
        success('扣费成功' . $deduct_money . '元');
    }

    protected function build_query()
    {
        $db          = new CouponSendNoteModel();
        $bid         = $this->get_bid();
        $params      = $this->params;
        $join        = [
            ['member m', "csn.member_guid = m.guid AND csn.bid = m.bid", 'LEFT'],
            ['coupon_generate_note cgn', "csn.generate_guid = cgn.guid AND csn.bid = cgn.bid", 'LEFT'],
            ['user u', " csn.owner_user_id = u.id AND csn.bid = u.bid", 'LEFT'],
            ['coupon c', "csn.coupon_guid = c.guid AND csn.bid = c.bid", 'LEFT'],
        ];
        $field       = [
            'c.name',
            'c.bid',
            'cast(c.value as decimal(9,0))' => 'value',
            'csn.way',
            'csn.guid',
            Db::raw("IFNULL(csn.member_guid,'') AS member_guid"),
            'csn.coupon_guid',
            'csn.owner_user_id',
            'csn.batch',
            'csn.mobile',
            'csn.code',
            'csn.password',
            'csn.active_user_guid',
            Db::raw("CASE WHEN cgn.safe_level = 0 THEN  csn.password WHEN cgn.safe_level IS NULL THEN csn.password ELSE '******' END  as password"),
            'csn.status',
            'csn.create_time',
            'csn.used_way',
            'csn.used_time',
            'csn.send_time',
            'csn.send_num',
            'csn.used_num',
            Db::raw("csn.send_num - csn.used_num as available_num"),
            'csn.send_value',
            'csn.used_value',
            Db::raw("csn.send_value - csn.used_value as available_value"),
            'csn.send_remark',
            'csn.used_rate_limit_type',
            'csn.used_rate_limit_num',
            Db::raw("date_format(csn.active_time,'%Y-%m-%d %H:%i') as active_time"),
            Db::raw("date_format(csn.expire_time,'%Y-%m-%d') as expire_time"),
            Db::raw("date_format(csn.availability_time,'%Y-%m-%d') as availability_time"),
            'csn.revoke_time',
            'm.card_id',
            'm.mobile'                      => 'member_mobile',
            'm.name'                        => 'member_name',
            'm.id'                          => 'member_id',
            'u.account',
            Db::raw("CONCAT(u.account,'(',u.name,')') as owner_user_info"),
        ];
        $db_business = new \app\model\Business();
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(m.mobile,4,4,'****') as member_mobile");
            $field[] = Db::raw("INSERT(csn.mobile,4,4,'****') as mobile");
        }
        //默认查看自己的子集
        $db_user = new User();
        $map     = [
            ['csn.owner_user_id', 'IN', $db_user->getChildUserIdArray()],
            ['csn.bid', '=', $bid],
            ['csn.delete_time', 'null', null],
            //            ['c.delete_time', 'null', null]
        ];
        $order   = [
            'csn.create_time' => 'DESC',
            'csn.id'          => 'DESC',
            //            'csn.code'        => 'DESC'
        ];
        if (isset($params['start_code']) && $params['start_code'] !== '') {
            //            $map[] = ['csn.serial_number', '>=', (int)$params['start_code']];
            $map[] = ['csn.code', '>=', $params['start_code']];
            unset($this->params['start_code']);
        }

        if (isset($params['end_code']) && $params['end_code'] !== '') {
            //            $map[] = ['csn.serial_number', '<=', (int)$params['end_code']];
            $map[] = ['csn.code', '<=', $params['end_code']];
            unset($this->params['end_code']);
        }
        if (!empty($params['key']) && $params['key'] == 'code' && !empty($params['value']) && strpos($params['value'], ',') !== false) {
            $map[] = ['csn.code', 'IN', explode(',', $this->params['value'])];
            unset($this->params['key']);
            unset($this->params['value']);
        }
        $this->model = $db->alias('csn')->join($join)->field($field)->append(['status_text'])->where($map)->order($order);
        return $this->model;
    }

    /**
     *卡券列表_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->build_query();
        result($this->_list());
    }

    /**
     *导出发送记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export()
    {
        ini_set("memory_limit", "1024M");
        $config               = get_config_by_bid($this->get_bid());
        $large_amount_of_data = $config['large_amount_of_data'];
        $model                = $this->build_query();
        if ($large_amount_of_data) {
            $data = $this->_chunk($model);
        } else {
            $data = $this->_select($model);
        }
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $header = [
            'name'            => '卡券名称',
            'batch'           => '批次码',
            'mobile'          => '手机号',
            'member_mobile'   => '会员手机',
            'member_name'     => '会员姓名',
            'status_text'     => '状态',
            'code'            => '券号',
            'password'        => '密码',
            'owner_user_info' => '归属者',
            'send_num'        => '总次数',
            'used_num'        => '已兑次数',
            'send_value'      => '总余额',
            'used_value'      => '已兑余额',
            'expire_time'     => '过期时间',
            'create_time'     => '生成时间',
            'active_time'     => '激活时间',
            'used_time'       => '使用时间',
            'send_remark'     => '备注',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *检查
     * @access protected
     * @param integer $count
     * @return mixed
     * @throws Exception
     */
    protected function check($count)
    {
        $max = 100000;
        if ($count > $max) {
            error('一次性最多导出' . $max . '条记录,本次数据有' . $count . '条');
        }
    }

    /**
     *发送优惠
     * @access public
     * @return void
     * @throws Exception
     */
    public function send()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $param            = $this->params;
        $file             = new Excel();
        $operator_user_id = $this->get_user_id();
        $bid              = $this->get_bid();
        $file_url         = $param['file'] ?? '';
        $mobile           = $param['mobile'] ?? '';
        $send_amount      = $param['send_amount'] ?? '';
        $coupon_guid      = $param['coupon_guid'];
        $send_remark      = $param['send_remark'] ?? '';
        if (empty($coupon_guid)) {
            error('请选择需要发送的卡券');
        }
        $way              = 3; //后台手动发送
        $send_data        = [];
        $send_mobile_list = [];
        if ($mobile && $send_amount) {
            //单发
            if (!tools()::is_mobile($mobile)) {
                error('您输入的手机号格式不正确');
            }
            if (!is_numeric($send_amount)) {
                error('发送张数只能是数字!');
            }
            $send_mobile_list[] = [
                'mobile'      => $mobile,
                'send_amount' => $send_amount,
                'send_remark' => $send_remark,
            ];
        } else {
            if (empty($file_url)) {
                error('请上传文件后再发送');
            }
            if (!tools()::is_url($file_url)) {
                error('文件路径不正确,请重新上传excel文件');
            }
            $allowField       = [
                'mobile'      => '手机号',
                'send_amount' => '张数',
                'send_remark' => '备注'
            ];
            $send_mobile_list = $file->load($file_url)->excelToArray($allowField);
            //循环校验合法数据 得到要导入的数组
            $errmsg = '';
            foreach ($send_mobile_list as $key => $val) {
                // 验证excel数据
                $mobile      = $val['mobile'];
                $amount      = $val['send_amount'];
                $send_remark = $val['send_remark'];
                try {
                    $this->validate($val, '.batch_send_excel');
                } catch (Exception $e) {
                    //验证失败 抛出异常 防止部分成功 部分失败
                    $errmsg .= '第' . ($key + 2) . '行:' . $e->getMessage() . '(手机号:' . $mobile . ')' . "<br/>";
                }
            }
            if ($errmsg) {
                error('数据校验不通过:' . "<br/>" . $errmsg);
            }
        }
        $coupon_guid_array = explode(',', $coupon_guid);
        $db_coupon         = new Coupon();
        $map               = [
            ['bid', '=', $bid],
            ['guid', 'IN', $coupon_guid_array]
        ];
        $coupon_info_array = $db_coupon->field(['guid', 'send_num', 'data_range_type', 'user_guid_json'])->where($map)->select()->toArray();
        foreach ($send_mobile_list as $key => $val) {
            $mobile      = $val['mobile'];
            $amount      = $val['send_amount'];
            $send_remark = $val['send_remark'];
            foreach ($coupon_info_array as $k => $v) {
                $coupon_guid     = $v['guid'];
                $coupon_send_num = $v['send_num'];
                $data_range_type = $v['data_range_type'];
                $user_guid_json  = $v['user_guid_json'];
                if ($data_range_type == 0) {
                    $owner_user_id = $operator_user_id;
                } else {
                    $db_user       = new User();
                    $owner_user_id = $db_user->get_user_id_by_guid(current($user_guid_json), $bid);
                }
                $send_data[] = [
                    'way'                   => $way,
                    'bid'                   => $bid,
                    'coupon_guid'           => $coupon_guid,
                    'mobile'                => $mobile,
                    'send_amount'           => $amount * $coupon_send_num,
                    'send_remark'           => $send_remark,
                    'operator_user_id'      => $operator_user_id,
                    'owner_user_id'         => $owner_user_id,
                    'coupon_send_note_guid' => create_guid()
                ];
            }
        }
        $db    = new CouponSendNoteModel();
        $count = count($send_data);
        if ($count > 50) {
            $result = $db->batch_send($bid, $send_data);
            if ($result === false) {
                error($db->getError());
            }
        } else {
            job()->set_job_name('Code@send')->set_sync_connections()->push_job($send_data);
        }
        success('提交成功' . $count . '条, 请在【卡密列表】查看发送结果');
    }

    public function change_status()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $batch                 = $params['batch'] ?? '';
        $coupon_send_note_guid = $params['coupon_send_guid'] ?? '';
        $status                = $params['status'];
        $map                   = [
            ['bid', '=', $bid],
        ];
        if (!empty($batch)) {
            $map[] = ['batch', '=', $batch];
        }
        if (!empty($coupon_send_note_guid)) {
            $map[] = ['guid', '=', $coupon_send_note_guid];
        }
        $update_data = ['status' => $status];
        if ($status == -1) {
            //反激活 只能修改待使用的卡密
            $update_data['active_time'] = null;
            $map[]                      = ['status', '=', 0];
        }
        if ($status == 0) {
            $update_data['active_time']      = format_timestamp();
            $update_data['active_user_guid'] = $this->get_user_guid();
            //激活 只能修改待激活的卡密
            $map[] = ['status', '=', -1];
        }
        $db_coupon_send_note = new CouponSendNoteModel();
        $db_coupon_send_note::update($update_data, $map);
        success('修改成功');
    }

    public function batch_send_code_expired_remind()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'];
        $db_coupon_send_note   = new CouponSendNoteModel();
        $map                   = [
            ['csn.bid', '=', $bid],
            ['csn.status', '=', 0],
            ['csn.expire_time', '>', format_timestamp()],
            ['csn.guid', 'IN', explode(',', $coupon_send_note_guid)],
        ];
        $field                 = [
            'csn.code',
            'csn.password',
            'csn.send_remark',
            'csn.mobile',
            'c.name' => 'coupon_name'
        ];
        $join                  = [
            ['coupon c', "csn.coupon_guid = c.guid AND csn.bid = c.bid"],
        ];
        $code_array            = $db_coupon_send_note->alias('csn')->join($join)->field($field)->where($map)->select()->toArray();
        $i                     = 0;
        foreach ($code_array as $k => $v) {
            if ($v['mobile']) {
                //尊敬的（卡卷名称）单位职工（卡密备注），您的职工慰问品还未兑换，已即将过期，请及时领取，有疑问请拨打客服电话，客服1：13772507061,客服2：19829606590
                notify()->set_key_name(NotifyService::CodeExpiredRemind)->set_member_mobile($v['mobile'])->set_data($v)->set_bid($bid)->send();
                $i++;
            }
        }
        if ($i == 0) {
            error('没有符合条件的卡券');
        }
        //发送模板消息通知
        success('通知成功' . $i . '个卡券,请留意短信发送记录');
    }

    public function revoke()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $guid                 = $params['coupon_send_guid'];
        $db_coupon_send_note  = new CouponSendNoteModel();
        $map_coupon_send_note = [
            ['bid', '=', $bid],
            ['guid', '=', $guid]
        ];
        $info                 = $db_coupon_send_note->where($map_coupon_send_note)->findOrFail();
        if (!in_array($info['status'], [0, -1])) {
            error('仅允许作废未激活和已激活的卡券');
        }
        $update_data = ['status' => -2, 'revoke_time' => format_timestamp()];
        $db_coupon_send_note::update($update_data, $map_coupon_send_note);
        success('作废成功');
    }

    public function check_code_num()
    {
        $params                    = $this->params;
        $bid                       = $this->get_bid();
        $edit_from                 = $params['edit_from'];
        $start_code                = $params['start_code'] ?? '';
        $end_code                  = $params['end_code'] ?? '';
        $coupon_generate_note_guid = $params['coupon_generate_note_guid'] ?? '';
        $coupon_send_note_guid     = $params['coupon_send_note_guid'] ?? '';
        $only_wait_active          = $params['only_wait_active'] ?? 0;
        if ($edit_from == 1 && (!$start_code || !$end_code)) {
            result(['status' => 0]);
        }
        if ($edit_from == 2 && !$coupon_generate_note_guid) {
            result(['status' => 0]);
        }
        if ($start_code && $end_code && strlen($start_code) != strlen($end_code)) {
            result(['status' => 0]);
        }
        $db_coupon_send_note  = new CouponSendNoteModel();
        $map_coupon_send_note = $db_coupon_send_note->build_map_by_params($bid, $params);
        $count                = $db_coupon_send_note->where($map_coupon_send_note)->count();
        $data                 = ['status' => 1, 'count' => $count];
        if ($only_wait_active && $count > 0) {
            $db_coupon_active_order            = new CouponActiveOrder();
            $active_coupon_info                = $db_coupon_active_order->get_active_coupon_info($map_coupon_send_note);
            $db_user                           = new User();
            $user_info                         = $db_user->get_user_info($this->get_user_guid(), $bid);
            $data['active_coupon_info']        = $active_coupon_info;
            $data['user_info']['money']        = floatval($user_info['money']);
            $data['user_info']['credit_money'] = floatval($user_info['credit_money']);
        }
        result($data);
    }

    public function batch_edit()
    {
        $params                    = $this->params;
        $bid                       = $this->get_bid();
        $batch                     = $params['batch'] ?? '';
        $coupon_send_note_guid     = $params['coupon_send_note_guid'] ?? '';
        $coupon_generate_note_guid = $params['coupon_generate_note_guid'] ?? '';
        $start_code                = $params['start_code'] ?? '';
        $end_code                  = $params['end_code'] ?? '';
        $edit_type                 = $params['edit_type'] ?? '';
        $edit_from                 = $params['edit_from'] ?? ''; // 1 起始-截止卡号 2批次  3勾选多张卡券 4 单独修改一张卡券
        $log_text                  = '将';
        $db_user                   = new User();
        $map_coupon_send_note      = [
            ['bid', '=', $bid],
            ['owner_user_id', 'IN', $db_user->getChildUserIdArray()],
        ];
        if ($edit_type == 1 && !isset($params['status'])) {
            error('请选择卡状态');
        }
        if (in_array($edit_from, [1, 2, 3]) && !in_array($edit_type, [1, 6])) {
            //修改卡状态或者售卡 需要更新卡状态
            //通过批量修改 但是类型不是改卡状态 则不修改卡字段
            unset($params['status']);
        }
        if ($edit_type && $start_code && $end_code) {
            $map_coupon_send_note[] = ['code', '>=', $start_code];
            $map_coupon_send_note[] = ['code', '<=', $end_code];
            $log_text               .= '卡号段:' . $start_code . '-' . $end_code;
        }
        if (!empty($batch)) {
            $map_coupon_send_note[] = ['batch', '=', $batch];
            $log_text               .= '批次码:' . $batch;
        }

        if (!empty($coupon_send_note_guid)) {
            $map_coupon_send_note[] = ['guid', 'IN', explode(',', $coupon_send_note_guid)];
            $db_coupon_send_note    = new CouponSendNoteModel();
            $temp_map               = [
                ['bid', '=', $bid],
                ['guid', 'IN', explode(',', $coupon_send_note_guid)],
            ];
            $code_array             = $db_coupon_send_note->where($temp_map)->column('code');
            $log_text               .= '卡号:' . join(',', $code_array);
        }

        $update = [];
        if (!empty($params['availability_time']) && ($edit_type == 3 || $edit_from == 4)) {
            $update['availability_time'] = $params['availability_time'];
            $log_text                    .= ',生效日期修改为:' . $params['availability_time'];
        }

        if (!empty($params['expire_time']) && (in_array($edit_type, [3, 6]) || $edit_from == 4)) {
            $update['expire_time'] = $params['expire_time'];
            $log_text              .= ',失效日期修改为:' . $params['availability_time'];
        }
        if (!empty($params['send_num'])) {
            $update['send_num'] = $params['send_num'];
            $log_text           .= ',可用次数修改为:' . $params['send_num'];
        }
        if (isset($params['send_remark']) && ($edit_type == 5 || $edit_from == 4)) {
            $update['send_remark'] = $params['send_remark'];
            $log_text              .= ',卡券备注修改为:' . $params['send_remark'];
        }

        if (isset($params['used_rate_limit_type']) && $params['used_rate_limit_type'] !== '') {
            $update['used_rate_limit_type'] = $params['used_rate_limit_type'];
        }
        if (!empty($params['owner_user_id']) && ($edit_type == 2 || $edit_from == 4)) {
            $update['owner_user_id'] = $params['owner_user_id'];
            $db_user                 = new User();
            $user_info               = $db_user->get_user_info($params['owner_user_id'], $bid);
            $log_text                .= ',卡券归属修改为:' . $user_info['account'] . '(' . $user_info['name'] . ')';
        }
        if (isset($params['used_rate_limit_num']) && $params['used_rate_limit_num'] !== '') {
            $update['used_rate_limit_num'] = $params['used_rate_limit_num'];
        }
        if (!empty($params['coupon_guid']) && ($edit_type == 4 || $edit_from == 4)) {
            $update['coupon_guid'] = $params['coupon_guid'];
            //计算 send_value
            $db_coupon   = new Coupon();
            $map_coupon  = [
                ['bid', '=', $bid],
                ['guid', '=', $params['coupon_guid']],
            ];
            $coupon_info = $db_coupon->where($map_coupon)->find();
            if ($coupon_info['value'] > 0) {
                $update['send_value'] = tools()::nc_price_calculate($coupon_info['value'], '*', $params['send_num'] ?? 1);
            }
            $log_text .= ',关联卡券修改为:' . $coupon_info['name'];
        }
        if (!empty($params['goods_item_guid'])) {
            $update['goods_item_guid'] = explode(',', $params['goods_item_guid']);
        }
        if (isset($params['status']) && $params['status'] == 0) {
            $update['status']           = 0; //状态改成已激活
            $log_text                   .= ',状态修改为: 已激活';
            $update['active_time']      = format_timestamp(); //同步赋值激活时间为当前时间
            $update['active_user_guid'] = $this->get_user_guid();
            if (in_array($edit_from, [1, 2, 3])) {
                //如果是根据卡号段/根据批次/勾选多张卡券 则只修改另一种状态的
                $map_coupon_send_note[] = ['status', '=', -1];
            }
        } elseif (isset($params['status']) && $params['status'] == -1) {
            if (in_array($edit_from, [1, 2, 3])) {
                //如果是根据卡号段/根据批次/勾选多张卡券 则只修改另一种状态的
                $map_coupon_send_note[] = ['status', '=', 0];
            }
            $update['status'] = -1;
            $log_text         .= ',状态修改为: 待激活';
        } elseif (isset($params['status']) && $params['status'] == -2) {
            if (in_array($edit_from, [1, 2, 3])) {
                //如果是根据卡号段/根据批次/勾选多张卡券 则只修改另一种状态的
                $map_coupon_send_note[] = ['status', 'IN', [-1, 0]]; //只修改未使用的;
            }
            $update['status'] = -2;
            $log_text         .= ',状态修改为: 已作废';
        } elseif ($edit_type == 6) {
            //售卡场景
            $map_coupon_send_note[] = ['status', '=', -1];
        } elseif (!empty($params['owner_user_id']) && ($edit_type == 2 || $edit_from == 4)) {
            $map_coupon_send_note[] = ['status', 'IN', [-1, 0, 1]];//修改卡归属允许修改已使用的卡
        } else {
            $map_coupon_send_note[] = ['status', 'IN', [-1, 0]]; //只修改未使用的
        }
        if ($edit_type == 6) {
            if (empty($params['renew_type'])) {
                error('请选择延期时间');
            }
            $log_text .= ',操作了售卡';
            //售卡场景
            $update['status']            = 0; //状态改成已激活
            $update['active_time']       = format_timestamp(); //同步赋值激活时间为当前时间
            $update['active_user_guid']  = $this->get_user_guid();
            $update['availability_time'] = date('Y-m-d 00:00:00'); //同步赋值生效时间为当前时间
            switch ($params['renew_type']) {
                case 1://1年
                    $after_expired_time    = date('Y-m-d H:i:s', strtotime('+1 year'));
                    $update['expire_time'] = $after_expired_time;
                    break;
                case 2://2年
                    $after_expired_time    = date('Y-m-d H:i:s', strtotime('+2 year'));
                    $update['expire_time'] = $after_expired_time;
                    break;
                case 3://3年
                    $after_expired_time    = date('Y-m-d H:i:s', strtotime('+3 year'));
                    $update['expire_time'] = $after_expired_time;
                    break;
                case 4:// + 天数
                    $renew_days            = $params['renew_days'];
                    $after_expired_time    = date('Y-m-d H:i:s', strtotime("+$renew_days day"));
                    $update['expire_time'] = $after_expired_time;
                    break;
                case 5:// 指定到期时间
                    $update['expire_time'] = $params['after_expire_time'];
                    break;
                default:
                    break;
            }
            if (!empty($update['expire_time'])) {
                $log_text .= ',有效期为:' . $update['expire_time'];
            }
        }

        if (empty($coupon_send_note_guid) && !empty($coupon_generate_note_guid)) {
            // 优先修改 优惠券 生成记录
            $map_coupon_generate_note = [
                ['bid', '=', $bid],
                ['guid', '=', $coupon_generate_note_guid],
            ];
            $db_coupon_generate_note  = new \app\model\CouponGenerateNote();
            $db_coupon_generate_note::update($update, $map_coupon_generate_note);
        }

        //修改发送记录单独需要修改的字段
        if (!empty($params['member_guid'])) {
            //            $map_coupon_send_note[] = ['member_guid', 'null', null];
            $update['member_guid'] = $params['member_guid'];
        }
        if (empty($update)) {
            error('没有更新条件');
        }
        $db_coupon_send_note = new CouponSendNoteModel();
        $count               = $db_coupon_send_note->where($map_coupon_send_note)->count();
        if ($count > 0) {
            $db_coupon_send_note::update($update, $map_coupon_send_note);
            $log_text .= ',修改成功【' . $count . '】张';
            wr_log($log_text);
            success('修改成功【' . $count . '】张');
        } else {
            success('没有符合条件的卡券被修改');
        }
    }

    protected function remove_update_field($update, $edit_type)
    {
        //1 卡状态  2 卡归属  3  有效期    4 关联卡券   5   备注 6    售卡
        $field = [
            1 => ['status'],
            2 => ['owner_user_id'],
            3 => ['owner_user_id']
        ];
    }

    // 单张卡号付费激活
    public function active()
    {
        $params                 = $this->params;
        $coupon_code            = trim($params['code']);
        $db_coupon_active_order = new CouponActiveOrder();
        $db_coupon_active_order->active($coupon_code);
    }

    public function sale_code()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $coupon_send_note_guid = $params['coupon_send_note_guid'] ?? '';
//        $coupon_generate_note_guid = $params['coupon_generate_note_guid'] ?? '';
        $send_remark    = $params['send_remark'] ?? '';
        $start_code     = $params['start_code'] ?? '';
        $end_code       = $params['end_code'] ?? '';
        $edit_from      = $params['edit_from'] ?? ''; // 1 起始-截止卡号 2批次  3勾选多张卡券 4 单独修改一张卡券
        $use_wechat_pay = $params['use_wechat_pay'] ?? 0;// 是否使用微信支付
        if ($edit_from == 1 && !$start_code && !$end_code) {
            error('请输入起始截止卡号!');
        }
        $db_coupon_active_order = new CouponActiveOrder();
        $db_coupon_send_note    = new CouponSendNoteModel();
        $map_coupon_send_note   = $db_coupon_send_note->build_map_by_params($bid, $params);
        if (count($map_coupon_send_note) == 2) {
            error('请选择要售卡的卡密或输入起始截止卡号');
        }
        $count = $db_coupon_send_note->where($map_coupon_send_note)->count();
        if ($count > 0) {
            $result             = $db_coupon_active_order->batch_active($map_coupon_send_note, $send_remark);
            $active_status      = $result['active_status'];
            $total_num          = $result['total_num'];
            $active_total_price = $result['active_total_price'];
            if ($active_status == 1) {
                $msg = '激活成功【' . $total_num . '】张';
                result($result, $msg);
            } elseif ($active_status == -1 && $use_wechat_pay == 1) {
                $msg = '待激活【' . $total_num . '】张,需付费:' . $active_total_price . '元';
                result($result, $msg);
            } elseif ($active_status == -1 && $use_wechat_pay == 0) {
                error('激活失败【' . $total_num . '】张,需付费:' . $active_total_price . '元,请先充值用户资金');
            }
        } else {
            error('没有符合条件的卡券被激活');
        }
    }
}