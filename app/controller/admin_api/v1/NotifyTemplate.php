<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;


use app\model\Business;
use Exception;

class NotifyTemplate extends BasicAdminApi
{
    /**
     *获取
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_notify_template_default  = new \app\model\NotifyTemplateDefault();
        $bid                         = $this->get_bid();
        $params                      = $this->params;
        $map                         = [
            //           ['status', '=', 1],
            ['provider', 'IN', ['member', 'business']],
        ];
        $config                      = get_config_by_bid($bid);
        $appid                       = $config['appid'];
        $hide_member_wechat_template = empty($appid) || $appid == get_system_config('platform_appid');
        $db_business                 = new Business();
        $is_super_business           = $db_business->is_examples_bid($this->get_bid());
        if ($is_super_business) {
            $hide_member_wechat_template = false;
            $map[]                       = ['is_super', 'IN', [0, 1]];
        } else {
            $map[] = ['is_super', 'IN', [0]];
        }
        if (!empty($params['key_name'])) {
            $map[] = ['key_name', '=', $params['key_name']];
        }
        if (!empty($params['provider'])) {
            $map[] = ['provider', '=', $params['provider']];
        }
        if (!empty($params['driver'])) {
            $map[] = ['driver', '=', $params['driver']];
        }
        $db_user                     = new \app\model\User();
        $user_guid                   = $this->get_user_guid();
        $rule_guid_array             = $db_user->get_user_rule_guid_array($user_guid, $bid);
        $rule_guid_array[]           = '';
        $map[]                       = ['rule_guid', 'IN', $rule_guid_array];
        $all_notify_template_default = $db_notify_template_default->where($map)->order(['key_name' => 'DESC', 'provider' => 'ASC', 'driver' => 'ASC'])->select()->toArray();
        $template_id_array           = array_column($all_notify_template_default, 'id');
        $db_notify_template          = new \app\model\NotifyTemplate();
        $map                         = [
            ['bid', '=', $this->get_bid()],
            ['default_template_id', 'IN', $template_id_array],
        ];
        $template_list               = $db_notify_template->where($map)->select();
        foreach ($template_list as $template) {
            foreach ($all_notify_template_default as $key => $val) {
                if ($template['default_template_id'] == $val['id']) {
                    //如果有设置自定义模板,则内容进行合并
                    $all_notify_template_default[$key]['template'] = $template['template'] ?: $val['template'];
                    $all_notify_template_default[$key]['status']   = $template['status'];
                    $all_notify_template_default[$key]['param']    = array_merge($val['param'] ?? [], $template['param'] ?? []);
                }
                $provider = $val['provider'];
                $driver   = $val['driver'];
                if ($hide_member_wechat_template && $provider == 'member' && $driver == 'wechat') {
                    unset($all_notify_template_default[$key]);
                }
            }
        }
        foreach ($all_notify_template_default as $key => $val) {
            $all_notify_template_default[$key]['bid'] = $this->get_bid();
        }
        result($all_notify_template_default);
    }

    /**
     *编辑模板
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $update_data = $this->params;
        if (empty($update_data['action']) && !empty($update_data['fsFormData'])) {
            $update_data = tools()::_parse_fs_form_data($this->params, 'param');
        }
        $bid                 = $update_data['bid'] ?? $this->get_bid();
        $default_template_id = intval($update_data['id']);
        //判断模板表有无数据, 没有则插入, 有则更新
        $db_notify_template = new \app\model\NotifyTemplate();
        $map                = [
            ['bid', '=', $bid],
            ['default_template_id', '=', $default_template_id]
        ];
        $template_id        = $db_notify_template->where($map)->value('id');
        $data               = [
            'bid'                 => $bid,
            'default_template_id' => $default_template_id,
            'template'            => $update_data['template'] ?? '',
            'status'              => $update_data['status'] ?? 0,
        ];
        if ($template_id) {
            //存在则更新
            $db_notify_template::update($data, $map);
            success('更新成功');
        } else {
            $db_notify_template->save($data);
            success('添加成功');
        }
    }

    /**
     *获取参数 暂时无用
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_param()
    {
        result([]);
        $id      = $this->params['id'];
        $db      = new \app\model\NotifyTemplateDefault();
        $map     = [['id', '=', $id]];
        $channel = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($channel['param'] ?? []));
    }
}