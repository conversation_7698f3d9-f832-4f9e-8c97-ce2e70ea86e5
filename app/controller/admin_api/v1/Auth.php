<?php

namespace app\controller\admin_api\v1;

use app\model\Auth as AuthModel;

class Auth extends BasicAdminApi
{
    public function all()
    {
        $filed       = ['id', 'status', 'memo', 'guid', 'ip', 'domain', 'create_time', 'update_time'];
        $this->model = $this->model->field($filed);
        result($this->_list());
    }

    public function index()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $filed       = ['id', 'status', 'memo', 'guid', 'ip', 'domain', 'create_time', 'update_time'];
        $this->model = $this->model->field($filed)->where($map);
        result($this->_list());
    }

    public function download()
    {
        $guid            = $this->params['guid'];
        $map             = [['guid', '=', $guid]];
        $db              = new AuthModel();
        $token           = $db->where($map)->value('token');
        $auth_token_path = runtime_path('_temp' . DIRECTORY_SEPARATOR . 'auth_token');
        $base_path       = $auth_token_path . $guid . DIRECTORY_SEPARATOR;
        if (!is_dir($base_path) && $base_path) {
            mkdir($base_path, 0755, true);
        }
        $str       = '<?php return "' . $token . '";';
        $file_name = 'key.php';
        file_put_contents($base_path . $file_name, $str);
        return download($base_path . $file_name, $file_name);
    }
}