<?php

namespace app\controller\admin_api\v1;

use app\model\ShareActivityRewardNote as ShareActivityRewardNoteModel;

class ShareActivityRewardNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new ShareActivityRewardNoteModel();

        $map = [
            ['sarn.bid', '=', $this->get_bid()]
        ];

        $join = [
            ['share_activity sa', 'sarn.share_activity_guid = sa.guid AND sa.bid = sarn.bid']
        ];

        $field = [
            'sarn.*',
            'sa.title as activity_title',
        ];

        $this->model = $this->model
            ->alias('sarn')
            ->join($join)
            ->where($map)
            ->field($field);

        result($this->_list());
    }
}
