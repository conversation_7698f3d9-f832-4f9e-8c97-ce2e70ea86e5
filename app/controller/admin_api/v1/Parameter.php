<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\Parameter as ParameterModel;
use app\model\ParameterDefault;
use Exception;

class Parameter extends BasicAdminApi
{
    /**
     *获取参数
     * @access public
     * @return void
     * @throws Exception
     */
    public function get()
    {
        $key_name = $this->params['key_name'];
        $config   = get_config_by_bid($this->get_bid());
        result([$key_name => $config[$key_name]]);
    }

    /**
     *更新参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update()
    {
        $db          = new ParameterModel();
        $params      = $this->params;
        $object      = $params['object'] ?? null;
        $object_guid = $params['object_guid'] ?? null;
        $bid         = $params['business_guid'];
        $config      = get_config_by_bid($bid, $object, $object_guid);
        foreach ($params as $key => $val) {
            if (isset($config[$key]) && !in_array($key, ['object', 'object_guid', 'business_guid', 'guid'])) {
                $db->update_config($key, $val, $bid, $object, $object_guid);
            }
        }
        success('修改成功');
    }

    /**
     *更新参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_sort()
    {
        $params               = $this->params;
        $db_parameter_default = new ParameterDefault();
        $group_name           = $params['group_name'];
        $key_list             = $params['key_list'];
        foreach ($key_list as $key => $value) {
            $map         = [['key_name', '=', $value]];
            $update_data = [
                'group' => $group_name,
                'sort'  => (int)$key,
            ];
            $db_parameter_default::update($update_data, $map);
        }
        $db_parameter_default::auto_remove_cache();
        $db_parameter = new ParameterModel();
        $db_parameter::remove_all_cache();
        result($key_list, '更新成功');
    }

    /**
     *参数列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params  = $this->params;
        $db_user = new \app\model\User();
        //没有guid参数 说明是商家自己进入系统中进行参数设置
        $bid         = $this->get_bid();
        $user_guid   = $this->get_user_guid();
        $guid        = $params['guid'] ?? null;
        $object      = $params['object'] ?? '';
        $object_guid = $params['object_guid'] ?? '';
        $keyword     = $params['keyword'] ?? null;
        $key_name    = $params['key_name'] ?? null; //多个key_name 用 | 分割
        if (!empty($key_name)) {
            $key_name = explode('|', $key_name);
        }
        if ($guid) {
            $bid       = $guid;
            $user_guid = $db_user->get_default_user_guid($bid);
        }
        $rule_guid_array        = $db_user->get_user_rule_guid_array($user_guid, $bid);
        $business_config        = get_config_by_bid($bid, $object, $object_guid);
        $db_business            = new BusinessModel();
        $business_version_array = $db_business->get_business_version_array($bid);
        $is_admin               = $db_business->is_admin();
        $is_agent               = $db_business->is_agent();
        $db_parameter_default   = new ParameterDefault();
        $map                    = [];
        if (!empty($params['group'])) {
            $groups = explode(',', $params['group']);
            $map[]  = ['group', 'IN', $groups];
        }
        if ($object) {
            $map[] = ['object', '=', $object];
        }
        $default_parameter = $db_parameter_default->where($map)->order(['group' => 'DESC', 'sort' => 'ASC'])->select()->append(['option_array'])->toArray();

        $map_business_parameter_visible = [
            ['status', '=', 1],
            ['bid', '=', $bid]
        ];
        $db_business_parameter_visible  = new \app\model\BusinessParameterVisible();
        $key_name_array                 = $db_business_parameter_visible->where($map_business_parameter_visible)->column('key_name');
        //$group             = $model->where($map)->group("`group`")->column('group');
        foreach ($default_parameter as $key => $val) {
            $i = 0;
            if ($keyword && strpos($val['title'], $keyword) === false) {
                //过滤keyword
                unset($default_parameter[$key]);
                continue;
            }
            if ($is_admin || ($is_agent && $val['is_public'] == 1) || in_array($val['rule_guid'], $rule_guid_array) || in_array($val['version_guid'], $business_version_array) || in_array($val['key_name'], $key_name_array)) {
                //超级管理员 或者 权限符合 或者版本符合 才能看见该参数
                $default_parameter[$key]['value'] = (isset($business_config[$val['key_name']])) ? $business_config[$val['key_name']] : $val['default_value'];
                $type                             = $val['type'];
                if ($type == 'checkbox') {
                    //复选框转成数组
                    $default_parameter[$key]['value'] = explode(',', $default_parameter[$key]['value']);
                    $business_config_array            = isset($business_config[$val['key_name']]) ? explode(',', $business_config[$val['key_name']]) : [];
                    foreach ($val['option_array'] as $k => $v) {
                        if (in_array($v, $default_parameter[$key]['value']) && !empty($business_config_array)) {
                            $v = $business_config_array[$i];
                            $i++;
                        }
                        $default_parameter[$key]['option_array'][$k] = [
                            'value'   => $k,
                            'checked' => in_array($v, $default_parameter[$key]['value'])
                        ];
                    }
                    // 对 $default_parameter[$key]['option_array'] 重新排序  需要考虑  $default_parameter[$key]['value']  的顺序
                    $default_parameter[$key]['option_array'] = tools()::re_sort_array($default_parameter[$key]['option_array'], $default_parameter[$key]['value']);
                }
            } else {
                unset($default_parameter[$key]);
            }
        }
        if (!empty($key_name)) {
            foreach ($default_parameter as $key => $val) {
                if (!in_array($val['key_name'], $key_name)) {
                    unset($default_parameter[$key]);
                }
            }
        }
        $db_business           = new BusinessModel();
        $business_info         = $db_business->get_business_info_by_account_or_guid($bid);
        $business_config_group = array_unique(array_column($default_parameter, 'group'));
        $system_config_group   = get_system_config('config_group');
        $system_config_group   = explode(',', $system_config_group);
        foreach ($system_config_group as $key => $val) {
            if (!in_array($val, $business_config_group)) {
                unset($system_config_group[$key]);
            }
        }
        $data = [
            'group'         => array_values($system_config_group),
            'business_info' => $business_info,
            'config'        => $default_parameter,
            'keyword'       => $keyword,
        ];
        result($data);
    }
}