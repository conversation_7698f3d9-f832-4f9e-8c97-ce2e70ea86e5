<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use Exception;

class AreaRule extends BasicAdminApi
{
    //   /**
    //    *编辑卡券_NEW
    //    * @access public
    //    * @return mixed
    //    * @throws \Exception
    //    */
    //   public function del()
    //   {
    //       $params       = $this->params;
    //       $bid          = $this->get_bid();
    //       $guid         = $params['guid'];
    //       $db_area_rule = new \app\model\AreaRule();
    //       $map          = [
    //           ['bid', '=', $bid],
    //           ['guid', '=', $guid],
    //       ];
    //       $db_area_rule->where($map)->delete();
    //       success('删除成功');
    //   }

    /**
     *版本列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $order       = ['create_time' => 'DESC'];
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map)->order($order);
        result($this->_list());
    }

    /**
     *版本授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function access()
    {
        $db   = new \app\model\AreaRule();
        $node = $db->get_rule_node_info($this->params['guid']);
        result($node);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function coupon_item_list()
    {
        $params    = $this->params;
        $db        = new Coupon();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $json_key  = 'coupon_item_guid';
        $field_key = 'coupon_item_guid';
        $list      = $db->where($map)->field(['name', 'guid' => $field_key])->order(['name' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $rule      = [];
        if ($guid) {
            //本规则的商品默认选中
            $db   = new \app\model\AreaRule();
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db->where($map)->field([$json_key])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule[$json_key])) {
            foreach ($list as $key => $val) {
                if (in_array($val[$field_key], $rule[$json_key])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }
}
