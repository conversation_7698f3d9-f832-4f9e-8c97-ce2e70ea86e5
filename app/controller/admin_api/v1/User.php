<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\Goods;
use app\model\User as UserModel;
use app\model\WechatSceneQrcode;
use Exception;
use think\facade\Db;

class User extends BasicAdminApi
{
    public function edit()
    {
        $db_user                          = new UserModel();
        $params                           = $this->params;
        $params ['force_update_password'] = true;
        $result                           = $db_user->edit($params);
        if ($result) {
            success('编辑成功');
        } else {
            error($db_user->getError());
        }
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_category_list_json()
    {
        $params   = $this->params;
        $db       = new \app\model\GoodsCategory();
        $bid      = $this->get_bid();
        $map      = [['bid', '=', $bid]];
        $json_key = 'goods_category_guid_json';
        $list     = $db->where($map)->field(['name', 'guid' => 'goods_category_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid     = $params['guid'] ?? '';
        $rule     = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_user = new UserModel();
            $map     = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule    = $db_user->where($map)->field([$json_key])->findOrEmpty();
        }

        if (!empty($rule) && is_array($rule[$json_key])) {
            foreach ($list as $key => $val) {
                if (in_array($val['goods_category_guid'], $rule[$json_key])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_list_json()
    {
        $params   = $this->params;
        $db       = new Goods();
        $bid      = $this->get_bid();
        $map      = [['bid', '=', $bid]];
        $json_key = 'goods_guid_json';
        $list     = $db->where($map)->field(['name', 'guid' => 'goods_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid     = $params['guid'] ?? '';
        $rule     = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_user = new UserModel();
            $map     = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule    = $db_user->where($map)->field([$json_key])->findOrEmpty();
        }

        if (!empty($rule) && is_array($rule[$json_key])) {
            foreach ($list as $key => $val) {
                if (in_array($val['goods_guid'], $rule[$json_key])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_user_guid_json()
    {
        $params    = $this->params;
        $db        = new UserModel();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $field     = [
            Db::raw("CONCAT(account,'(',name,')') as user_info"),
            'guid' => 'user_guid'
        ];
        $list      = $db->where($map)->field($field)->order(['create_time' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $user_info = [];
        if ($guid) {
            $map       = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $user_info = $db->where($map)->field(['user_guid_json'])->findOrEmpty();
        }

        if (!empty($user_info) && is_array($user_info['user_guid_json'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['user_guid'], $user_info['user_guid_json'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    public function get_user_share_qrcode()
    {
        //todo 生成分销二维码
        $params = $this->params;
        $bid    = $this->get_bid();
        error('功能还未上线');
    }

    public function get_user_bind_url()
    {
        $params                      = $this->params;
        $bid                         = $this->get_bid();
        $user_guid                   = $params['user_guid'];
        $config                      = get_config_by_bid($bid);
        $user_bind_wechat_appid_type = $config['user_bind_wechat_appid_type'];
        if ($user_bind_wechat_appid_type == 0) {
            //用平台公众号通知
            $wechat_appid = get_system_config('platform_wechat_appid');
        } else {
            // $user_bind_wechat_appid_type == 1
            $config       = get_config_by_bid($bid);
            $wechat_appid = $config['appid'];
            if (empty($wechat_appid)) {
                error('请先授权公众号生成二维码');
            }
        }

        $qrcode_weixin          = weixin($wechat_appid)::WeChatQrcode();
        $scene_id               = tools()::get_bill_number();
        $expire_seconds         = 2592000;
        $qrcode                 = $qrcode_weixin->create($scene_id, $expire_seconds);
        $db_wechat_scene_qrcode = new WechatSceneQrcode();
        $guid                   = create_guid();
        $data                   = [
            'type'          => 1,
            'guid'          => $guid,
            'bid'           => $bid,
            'user_guid'     => $this->get_user_guid(),
            'appid'         => $wechat_appid,
            'scene_id'      => $scene_id,
            'qrcode_url'    => $qrcode['url'],
            'qrcode_ticket' => $qrcode['ticket'],
            'info'          => [
                'bid'       => $bid,
                'user_guid' => $user_guid,
            ]
        ];
        $db_wechat_scene_qrcode->save($data);
        result(['url' => $qrcode['url']]);
    }

    public function get_user_bind_url_bak()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];
        if (empty($appid)) {
            error('请先授权公众号生成二维码');
        }
        $user_guid = $params['user_guid'];
        $token     = create_guid();
        $data      = [
            'bid'         => $bid,
            'create_time' => time(),
            'user_guid'   => $user_guid
        ];
        $cache_key = 'user_bind:' . $token;
        cache($cache_key, $data, 3600);
        $path    = 'admin/passport/bind_user';
        $web_url = (string)url($path, ['bid' => $bid, 'token' => $token], false, true);
        $web_url = tools()::replace_readonly_to_www($web_url);
        result(['url' => $web_url]);
    }

    /**
     *用户信息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function info()
    {
        $bid                           = $this->get_bid();
        $params                        = $this->params;
        $user_guid                     = !empty($params['user_guid']) ? $params['user_guid'] : $this->get_user_guid();
        $db                            = new UserModel();
        $map                           = [
            ['bid', '=', $bid],
            ['guid', '=', $user_guid],
        ];
        $user_info                     = $db->where($map)->findOrFail();
        $db_business                   = new BusinessModel();
        $business_info                 = $db_business->get_business_info_by_account_or_guid($bid);
        $business_info['expired_time'] = date('Y-m-d', strtotime($business_info['expired_time']));
        $user_info['business_info']    = $business_info;
        $db_store                      = new \app\model\Store();
        $map_store                     = [
            ['bid', '=', $bid],
            ['guid', '=', $user_info['store_guid']],
        ];
        $store_info                    = $db_store->where($map_store)->find();
        $user_info['store_name']       = $store_info['store_name'] ?? '';
        result($user_info);
    }

    /**
     *个人信息修改
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function profile()
    {
        $db            = new UserModel();
        $params        = $this->params;
        $params['bid'] = $this->get_bid(); //默认修改本商家下的密码
        $result        = $db->edit($params);
        if ($result === false) {
            error($db->getError());
        }
        success('修改成功');
    }

    /**
     *用户列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $field   = [
            'bid',
            'guid',
            'name',
            'role_guid',
            'account',
            'tel',
            'status',
            'openid',
            'store_guid',
            'last_login_time',
            'create_time',
            'enable_usb_key',
            'usb_key_id',
            'usb_key_token',
            'usb_key_decrypt_key',
        ];
        $field   = ['*'];
        $db_user = new UserModel();
        //默认查看自己的子集
        $bid           = $this->get_bid();
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $type          = $business_info['type'];
        $map           = [
            ['u.delete_time', 'null', null]
        ];
        switch ($type) {
            case 1:
                //商家
                $map[] = ['u.bid', '=', $bid];
                $map[] = ['u.id', 'IN', $db_user->getChildUserIdArray()];
                break;
            case 2:
            case 3:
                $bid   = $this->params['bid'] ?? $bid;
                $map[] = ['u.bid', '=', $bid];
                break;
            default:
                break;
        }
        $field       = [
            'u.*',
            'wu.status'              => 'online_status',
            'wu.last_login_out_time' => 'last_login_out_time',
        ];
        $join        = [
            ['websocket_user wu', 'u.guid = wu.user_guid', 'LEFT'],
        ];
        $this->model = $this->model->alias('u')->join($join)->where($map)->field($field)->order(['status' => 'DESC', 'create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *获取组织机构
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_organization()
    {
        $db    = new UserModel();
        $map   = [['bid', '=', $this->get_bid()]];
        $users = $db->where($map)->field(['guid as id', 'parent_guid as pId', "CONCAT(account,'(',name,')')" => "name"])->order('guid ASC')->select()->toArray();
        foreach ($users as $key => $val) {
            $users[$key]['open'] = true;
        }
        result($users);
    }

    /**
     *更新组织机构
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_organization()
    {
        $db          = new UserModel();
        $params      = $this->params;
        $guid        = $params['guid'];
        $parent_guid = $params['parent_guid'];
        $update_data = [
            'parent_guid' => $parent_guid
        ];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];
        $update      = $db::update($update_data, $map);
        if ($update) {
            success('更新成功');
        } else {
            error('未更新');
        }
    }

    /**
     *锁定用户
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function lock()
    {
        $params    = $this->params;
        $userModel = new UserModel();
        if (($userId = $userModel->lock_user($params)) === false) {
            error('系统错误,请联系管理员');
        }
        success('锁定成功');
    }

    /**
     *解锁用户
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function unlock()
    {
        $params    = $this->params;
        $userModel = new UserModel();
        if (($userId = $userModel->unlock($params)) === false) {
            error('系统错误,请联系管理员');
        }
        success('解锁成功');
    }

    /**
     *解锁用户
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reset_login_failed_times()
    {
        $params      = $this->params;
        $guid        = $params['guid'];
        $reset_bid   = $params['bid'];
        $userModel   = new UserModel();
        $update_data = ['login_failed_times' => 0];
        $map         = [
            ['bid', '=', $reset_bid],
            ['guid', '=', $guid],
        ];
        $userModel::update($update_data, $map);
        success('解锁成功,请重新尝试登录');
    }


    public function recharge_money()
    {
        $params             = $this->params;
        $bid                = $this->get_bid();
        $db_user_money_note = new \app\model\UserMoneyNote();
        $type               = $params['type'];
        $user_guid          = $params['user_guid'];
        $money              = $params['money'];
        $data               = [
            'bid'       => $bid,
            'user_guid' => $user_guid,
            'way'       => $money > 0 ? 1 : 2,
            'type'      => $type,
            'money'     => $money,
            'memo'      => '[后台操作]',
        ];
        $db_user_money_note->recharge_money($data);
        success('操作成功');
    }
}