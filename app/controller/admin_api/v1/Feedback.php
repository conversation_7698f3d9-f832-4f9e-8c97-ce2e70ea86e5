<?php

namespace app\controller\admin_api\v1;

use app\model\Feedback as FeedbackModel;
use Exception;

class Feedback extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db_feedback_model = new FeedbackModel();
        $params            = $this->params;
        $bid               = $this->get_bid();
        $info              = $db_feedback_model->get_detail($bid, $params['guid']);
        result($info);
    }
}