<?php

namespace app\controller\admin_api\v1;

use Exception;

class UserMoneyNote extends BasicAdminApi
{

    /**
     *充值
     * @access public
     * @return void
     * @throws Exception
     */
    public function recharge()
    {
        $params             = $this->params;
        $bid                = $this->get_bid();
        $db_user_money_note = new \app\model\UserMoneyNote();
        $type               = $params['type'];
        $user_guid          = $params['user_guid'];
        $money              = $params['money'];
        $data               = [
            'bid'       => $bid,
            'user_guid' => $user_guid,
            'way'       => 1,
            'type'      => $type,
            'money'     => $money,
            'memo'      => '[后台操作]',
        ];
        $db_user_money_note->recharge_money($data);
        success('操作成功');
    }

    /**
     *在线充值商家余额
     * @access public
     * @return void
     * @throws Exception
     */
    public function online_recharge_money()
    {
        $params        = $this->params;
        $recharge_bid  = $this->get_bid();
        $money         = $params['money'];
        $bid           = config('app.recharge_pay_parameter_business_guid');
        $out_trade_no  = tools()::get_bill_number();
        $int_total_fee = tools()::nc_price_yuan2fen($money);
        $order_guid    = create_guid();
        $note_guid     = create_guid();
        $recharge_info = [
            'out_trade_no'       => $out_trade_no,
            'recharge_bid'       => $recharge_bid,
            'total_fee'          => $int_total_fee,
            'money'              => $money,
            'note_guid'          => $note_guid,
            'recharge_user_guid' => $this->get_user_guid(),
        ];
        cache($order_guid, $recharge_info, 3600);
        $pay_url = (string)url('member/pay/submit', ['bid' => $bid, 'order_guid' => $order_guid, 'type' => 'recharge_user_money'], false, true);
        $pay_url = tools()::replace_readonly_to_www($pay_url);
        $data    = [
            'url'        => $pay_url,
            'socket_uid' => $out_trade_no
        ];
        result($data);
    }

    /**
     *储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db    = new \app\model\UserMoneyNote();
        $join  = [
            ['user u', 'umn.operator_user_guid = u.guid AND umn.bid = u.bid', 'LEFT'],
            ['user u2', 'umn.user_guid = u2.guid AND umn.bid = u2.bid', 'LEFT'],
        ];
        $field = [
            'umn.type',
            'umn.user_guid',
            'umn.money',
            'umn.balance',
            'umn.status',
            'umn.create_time',
            'umn.memo',
            'u.account' => 'operator_account',
            'u2.account',
        ];
        $bid   = $this->get_bid();

        $db_user         = new \app\model\User();
        $user_guid_array = $db_user->getChildUserGuidArray();
        $map             = [
            ['umn.bid', '=', $bid],
            ['umn.user_guid', 'IN', $user_guid_array],
        ];

        $this->model = $db->alias('umn')->order(['umn.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        result($this->_list());
    }

    /**
     *充值记录统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function money_note_report()
    {
        $db          = new \app\model\User();
        $join        = [
            ['user u', 'umn.operator_user_guid = u.guid AND umn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'u.id',
            'u.account',
            'u.name',
            'SUM(umn.money)' => 'total_money',
        ];
        $map         = [
            ['umn.bid', '=', $this->get_bid()],
        ];
        $group       = ['u.account'];
        $this->model = $db->alias('umn')->join($join)->field($field)->where($map)->group($group)->order(['total_money']);
        result($this->_list());
    }
}
