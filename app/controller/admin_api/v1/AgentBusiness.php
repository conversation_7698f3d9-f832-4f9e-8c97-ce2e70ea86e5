<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use Exception;

class AgentBusiness extends BasicAdminApi
{
    /**
     *我的刷脸商家
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db        = new BusinessModel();
        $bid       = $this->get_bid();
        $user_guid = $this->get_user_guid();
        $join      = [
            ['business b1', 'b.parent_guid = b1.guid'],
            ['user u', "b.parent_user_guid=u.guid AND b.parent_guid=u.bid"],
        ];
        $map       = [
            ['b.parent_guid', '=', $bid]
        ];
        $db_rule   = new \app\model\Rule();
        if ($db_rule->is_admin() === false) {
            $map[] = ['b.parent_user_guid', '=', $user_guid];
        }
        $field       = [
            'b.guid',
            'b.account',
            'b.business_name',
            'b.mobile',
            'b.create_time',
            'b.expired_time',
            'b.license_status',
            'b.renewal_amount',
            'b.memo',
            'b.last_login_time',
            //"CONCAT(u.business_name,'(',b1.account,')')" => 'agent',
            "CONCAT(u.name)"                              => 'service',
            'b1.account'                                  => 'agent_account',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
        ];
        $order       = ['b.create_time' => 'DESC'];
        $this->model = $db->alias('b')->join($join)->order($order)->append(['is_expired'])->field($field)->where($map);
        result($this->_list());
    }
}
