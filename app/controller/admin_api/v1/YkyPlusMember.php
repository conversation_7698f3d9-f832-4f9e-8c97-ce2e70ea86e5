<?php

namespace app\controller\admin_api\v1;

use app\model\YkyPlusMember as YkyPlusMemberModel;
use app\common\tools\Excel;
use Exception;

class YkyPlusMember extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyPlusMemberModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null]
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    public function export()
    {
        $db    = new YkyPlusMemberModel();
        $bid   = $this->get_bid();
        $map   = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null]
        ];
        $model = $db->where($map)->order(['create_time' => 'DESC']);
        $data  = $this->_select($model);
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $header = [
            'card_id'               => '卡号',
            'total_buy_times'       => '累计购买或续费次数',
            'total_consume_times'   => '累计消费次数',
            'total_consume_money'   => '累计消费金额',
            'total_add_value_times' => '累计充值次数',
            'total_add_value_money' => '累计充值金额',
            'first_buy_bill_number' => '首次购卡单号',
            'first_buy_date_time'   => '首次购卡时间',
            'last_buy_date_time'    => '最后续费时间',
            'last_buy_bill_number'  => '最后续费单号',
            'expired_time'          => '当前过期时间',
            'total_coupon_send_num' => '累计发券张数',
            'total_coupon_used_num' => '累计用券张数',
            'create_time'           => '创建时间',
            'update_time'           => '更新时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data, 'PLUS会员' . format_timestamp());
    }
}