<?php

namespace app\controller\admin_api\v1;

use Exception;

class MemberDistributorApplyNote extends BasicAdminApi
{
    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid         = $this->get_bid();
        $map         = [['m.bid', '=', $bid]];
        $join        = [
            ['member m', 'mdan.member_guid = m.guid AND mdan.bid = m.bid'],
            ['member m2', 'm.share_member_guid = m2.guid AND m.bid = m2.bid', 'LEFT'],
        ];
        $field       = [
            'mdan.*',
            'm.head_img',
            //            'm.brokerage_ratio',
            'm.member_group_guid',
            'm.id'     => 'member_id',
            'm.name'   => 'member_name',
            'm.mobile' => 'member_mobile',
            'm2.id'    => 'share_member_id',
            'm2.guid'  => 'share_member_guid',
        ];
        $this->model = $this->model->alias('mdan')->field($field)->join($join)->where($map)->order(['mdan.create_time' => 'DESC']);
        result($this->_list());
    }


    /**
     *审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $params                           = $this->params;
        $bid                              = $this->get_bid();
        $guid                             = $params['guid'];
        $db_member_distributor_apply_note = new \app\model\MemberDistributorApplyNote();
        $db_member_distributor_apply_note->examine($bid, $guid);
        success('审核通过');
    }
}