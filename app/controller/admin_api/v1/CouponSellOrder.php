<?php

namespace app\controller\admin_api\v1;

use app\model\User;
use Exception;
use think\facade\Db;

class CouponSellOrder extends BasicAdminApi
{
    public function refund()
    {
        $params     = $this->params;
        $bid        = $this->get_bid();
        $order_guid = $params['order_guid'];
        $db         = new \app\model\CouponSellOrder();
        $result     = $db->refund($bid, $order_guid);
        if ($result === false) {
            error($db->getError());
        }
        success('退单成功');
    }

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $this->model = new \app\model\CouponSellOrder();
        $order       = ['cso.id' => 'DESC'];
        $join        = [
            ['coupon c', 'cso.bid = c.bid AND cso.coupon_guid= c.guid'],
            ['member m', 'cso.bid = m.bid AND cso.member_guid= m.guid'],
            ['coupon_send_note csn', 'cso.bid = csn.bid AND cso.coupon_send_note_guid= csn.guid', 'LEFT'],
        ];
        $field       = [
            'c.name' => 'coupon_name',
            'cso.*',
            'csn.code',
            'm.id'   => 'member_id',
            'm.name' => 'member_name',
            'm.mobile',
        ];
        $db_business = new \app\model\Business();
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(m.mobile,4,4,'****') as mobile");
        }
        $db_user     = new User();
        $map         = [
            ['cso.bid', '=', $bid],
            //           ['cso.status', '<>', -1], //不展示待支付订单
            //           ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildArr()], // 只显示自己范围内的订单
        ];
        $this->model = $this->model->alias('cso')->where($map)->join($join)->order($order);
        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'sum(cso.total_money)' => 'sum_total_money',
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '总金额 <b style="color: red"> ' . number_format($data['sum_total_money'], 2) . '</b> 元';
            success($total_text);
        }
        $this->model = $this->model->field($field);
        result($this->_list());
    }
}
