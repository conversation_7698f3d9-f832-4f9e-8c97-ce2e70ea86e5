<?php

namespace app\controller\admin_api\v1;

use Exception;

class BusinessRule extends BasicAdminApi
{
    /**
     *商家权限
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $business_guid = $this->params['business_guid'];
        $map           = [
            ['business_guid', '=', $business_guid]
        ];
        result($this->_list());
    }
}