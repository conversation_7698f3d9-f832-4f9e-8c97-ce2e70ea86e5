<?php

namespace app\controller\admin_api\v1;

use app\model\GoodsCategoryDiscount;
use app\model\MemberGroup as MemberGroupModel;
use app\model\User;
use Exception;

class GoodsCategory extends BasicAdminApi
{
    /**
     *商品类别列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db_user           = new User();
        $map               = [
            ['bid', '=', $this->get_bid()],
            ['create_user_id', 'IN', $db_user->getChildUserIdArray()]
        ];
        $db_goods_category = new \app\model\GoodsCategory();
        $this->model       = $db_goods_category->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC']);
        result($this->_list());
    }

    public function get_goods_category_count()
    {
        $map               = [['bid', '=', $this->get_bid()]];
        $db_goods_category = new \app\model\GoodsCategory();
        $count             = $db_goods_category->where($map)->count();
        result(['goods_category_count' => $count]);
    }

    public function tree()
    {
        $db_rule = new \app\model\GoodsCategory();
        $params  = $this->params;
        $bid     = $this->get_bid();
        $field   = [
            'guid'        => 'id',
            'parent_guid' => 'pId',
            'name',
            'sort',
        ];
        $map     = [
            ['bid', '=', $bid]
        ];
        if (!empty($params['pId'])) {
            $map[] = ['parent_guid', '=', $params['pId']];
        }
        $list = $db_rule->field($field)->where($map)->order(['parent_guid' => 'DESC', 'sort' => 'ASC'])->select()->toArray();
        result($list);
    }

    /**
     *编辑类别折扣
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit_discount()
    {
        $params = $this->params;
        $db     = new GoodsCategoryDiscount();
        $map    = [
            'bid'                 => $this->get_bid(),
            'goods_category_guid' => $params['goods_category_guid'],
            'member_group_guid'   => $params['member_group_guid'],
        ];
        $note   = $db->where($map)->findOrEmpty();
        if (!$note->isEmpty()) {
            $update_data = ['discount' => $params['discount']];
            $db::update($update_data, $map);
        } else {
            $data = [
                'guid'                => create_guid(),
                'bid'                 => $this->get_bid(),
                'goods_category_guid' => $params['goods_category_guid'],
                'member_group_guid'   => $params['member_group_guid'],
                'discount'            => $params['discount'],
            ];
            $db->save($data);
        }
        success('修改成功');
    }

    public function update_sort()
    {
        $db_goods_category = new \app\model\GoodsCategory();
        $params            = $this->params;
        $guid              = $params['guid'];
        $parent_guid       = $params['parent_guid'] ?: tools()::get_empty_guid();
        $node_str          = $params['node_str'];
        $need_sort         = $params['need_sort'] ?? 0;
        //更新层级关系
        $update_organization = $db_goods_category->update_organization($guid, $parent_guid);
        //更新排序
        if ($need_sort == 1) {
            $db_goods_category->update_sort($node_str);
        }
        if ($update_organization) {
            success('更新成功');
        } else {
            error('未更新');
        }
    }

    /**
     *折扣
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function discount()
    {
        $referer = $this->request->header('referer');
        $arr     = tools()::parse_url_params($referer);//将url解析成参数数组
        if (empty($arr['guid'])) {
            error('缺少guid参数');
        }
        $db                  = new MemberGroupModel();
        $goods_category_guid = $arr['guid'];
        $join                = [
            ['goods_category_discount mcd', "mg.guid = mcd.member_group_guid and mg.bid=mcd.bid and mcd.goods_category_guid='$goods_category_guid'", 'LEFT'],
        ];
        $map                 = [
            ['mg.bid', '=', $this->get_bid()]
        ];
        $this->model         = $db->alias('mg')->field(
            [
                'mg.guid'                                                 => "member_group_guid",
                'mg.level'                                                => "level",
                'mg.name',
                "IFNULL(mcd.goods_category_guid, '$goods_category_guid')" => 'goods_category_guid',
                'IFNULL(mcd.discount, 1)'                                 => 'discount',
            ]
        )->join($join)->where($map)->order(['level' => 'DESC']);
        result($this->_list());
    }
}
