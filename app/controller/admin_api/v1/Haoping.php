<?php

namespace app\controller\admin_api\v1;

use app\model\HaopingOrder;
use app\model\Jobs;
use app\model\JobsHistory;
use app\model\WechatRedPacketOrder;
use app\common\service\WechatService;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;

class Haoping extends BasicAdminApi
{
    /**
     *好评返现订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new HaopingOrder();
        $join        = [
            ['user u', " ho.operator_userid = u.id AND ho.bid = u.bid", 'LEFT'],
        ];
        $field       = [
            'u.account',
            'u.name',
            'ho.status' => 'status',
            'ho.*',
        ];
        $map         = [
            ['ho.bid', '=', $this->get_bid()],
        ];
        $this->model = $db->alias('ho')->join($join)->field($field)->where($map)->order(['ho.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *获取红包发送状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_red_packet_send_status()
    {
        $guid = $this->params['guid'];
        $map  = [
            ['guid', '=', $guid],
            ['bid', '=', $this->get_bid()]
        ];
        $db   = new HaopingOrder();
        $info = $db->where($map)->find();
        if (!$info) {
            error('记录不存在');
        }
        if ($info['wechat_status'] == -1 && !empty($info['message'])) {
            error($info['message']);
        }
        $wechat     = WechatService::get_instance();
        $mch_billno = tools()::md5_16($info->getData('order_no'));
        $data       = [
            'bid'        => $this->get_bid(),
            'mch_billno' => $mch_billno
        ];
        $result     = $wechat->get_red_packet_status($data);
        if ($result === false) {
            error($wechat->message);
        }
        $arr = [
            'SENDING'   => '发放中',
            'FAILED'    => '发放失败',
            'SENT'      => '已发放待领取',
            'RECEIVED'  => '已领取',
            'RFUND_ING' => '退款中',
            'REFUND'    => '已退款'
        ];
        if (isset($arr[$result['red_packet_status']])) {
            $result['red_packet_status'] = $arr[$result['red_packet_status']];
        }
        $msg = '状态:' . $result['red_packet_status'];
        if (isset($result['rcv_time'])) {
            $msg .= '(领取时间:' . $result['rcv_time'] . ')';
        }
        success($msg);
    }

    /**
     *统计
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function report()
    {
        $this->model = new WechatRedPacketOrder();
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['status', '=', 1]
        ];
        $group       = 'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")';
        $field       = ['DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date', 'count(*)' => 'num', 'cast(sum(total_amount)/100 as decimal(9,2))' => 'total_amount'];
        $this->model = $this->model->where($map)->group($group)->field($field)->order(['date' => 'DESC']);
        result($this->_list());
        $db          = new HaopingOrder();
        $is_return   = $db->field(["count(1)" => "is_return_count", "COALESCE(SUM(give_amount),0)" => "is_return_amount"])->where($map)->find();
        $map         = [
            ['bid', '=', $bid],
            ['status', '=', -2]
        ];
        $need_return = $db->field(["count(1)" => "need_return_count", "COALESCE(SUM(give_amount),0)" => "need_return_amount"])->where($map)->find();
        $this->assign([
            'is_return'   => $is_return,
            'need_return' => $need_return,
            'title'       => "统计分析"
        ]);
        return $this->fetch();
    }

    /**
     *通过
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function pass()
    {
        $guid = $this->params['guid'];
        $db   = new HaopingOrder();
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];
        $info = $db->where($map)->find();
        if ($info['status'] == 1) {
            error('当前已经是审核通过状态');
        }
        $user_id = $this->get_user_id();
        $config  = get_config_by_bid($this->get_bid());
        if (!$config['sub_mch_id'] && !($config['mch_id'] && $config['appid'])) {
            error('您的配置不完善');
        }
        $order_no   = $info->getData('order_no');
        $mch_billno = tools()::md5_16($order_no);
        $data       = [
            'status'            => 1,
            'mch_billno'        => $mch_billno,
            'last_examine_time' => format_timestamp(),
            'operator_userid'   => $user_id
        ];
        $update     = $db::update($data, $map);
        unset($data);
        if ($update) {
            //TODO 开始插入微信转账队列
            $give_amount       = $info['give_amount'];
            $data['bid']       = $this->get_bid();
            $data['note_guid'] = $guid;
            $job_data          = [
                'wxappid'        => null,
                'bid'            => $this->get_bid(),
                'way'            => 1,//1好评返现
                'client_ip'      => tools()::get_client_ip(),
                'guid'           => create_guid(),
                'openid'         => $info['openid'],
                'total_amount'   => intval($give_amount * 100),
                'mch_billno'     => $mch_billno,
                'sendname'       => '恭喜发财',
                'wishing'        => '恭喜发财',
                'act_name'       => 'act_name',
                'remark'         => 'remark',
                'memo'           => $order_no,
                'total_num'      => 1,
                'scene_id'       => null,
                'risk_info'      => null,
                'consume_mch_id' => null,
                'sub_mch_id'     => null,
                'msgappid'       => null,
                //'consume_mch_id' => $config['sub_mch_id'],
                //'sub_mch_id'     => $config['sub_mch_id'],
                //'msgappid'       => $config['appid'],
            ];
            if ($config['sub_mch_id']) {
                //如果子商户号不为空则走子商户模式
                $job_data['wxappid']        = config('app.wechat_partner_appid');
                $job_data['sub_mch_id']     = $config['sub_mch_id'];
                $job_data['consume_mch_id'] = $config['sub_mch_id'];
                $job_data['msgappid']       = $config['appid'] ?: config('app.wechat_partner_appid');
            } elseif ($config['mch_id'] && $config['appid']) {
                //商户号和appid都配置了则走普通商户模式
                $job_data['wxappid']  = $config['appid'];
                $job_data['msgappid'] = $config['appid'];
            } else {
                //都不符合说明配置不完善
                wr_log('返现guid:' . $guid . '通过成功,但是插入队列失败');
                error('您的配置不完善,通过成功,但是插入队列失败');
            }
            $data['data'] = $job_data;
            job()->set_job_name('Weixin@send_red_packet')->push_job($data);
            success('通过成功');
        } else {
            error('操作失败');
        }
    }

    /**
     *拒绝
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function refuse()
    {
        $db   = new HaopingOrder();
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $this->params['guid']],
        ];
        $info = $db->where($map)->find();
        if ($info['status'] == -1) {
            error('当前已经是被拒绝状态');
        }
        $user_id = $this->get_user_id();
        $data    = [
            'reason'            => $this->params['reason'],
            'last_examine_time' => format_timestamp(),
            'status'            => -1,
            'operator_userid'   => $user_id
        ];
        $update  = $db::update($data, $map);
        if ($update) {
            success('拒绝成功');
        } else {
            error('拒绝失败,请重新操作');
        }
    }

    /**
     *删除
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $guid = $this->params['guid'];
        $db   = new HaopingOrder();
        $map  = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];
        $info = $db->where($map)->find();
        if ($info['wechat_status'] != -1) {
            error('仅支持红包发送失败的订单');
        }
        $db_job  = new Jobs();
        $map_job = [['payload', 'LIKE', '%' . $guid . '%']];
        $note    = $db_job->where($map_job)->find();
        if (!$note) {
            error('队列任务查找失败');
        }
        // 启动事务
        Db::startTrans();
        try {
            $note           = tools()::object2array($note);
            $db_job_history = new JobsHistory();
            //先新增到job归档表
            $db_job_history->save($note);
            //再更新审核记录
            $data   = [
                'message'       => '【人工转账】',
                'wechat_status' => 1
            ];
            $update = $db::update($data, $map);
            //最后删除job
            $db_job->where($map_job)->delete();
            // 提交事务
            Db::commit();
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            error('处理失败:' . $e->getMessage());
        }
        success('处理成功');
    }

    /**
     *导出记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_note()
    {
        $map  = [['bid', '=', $this->get_bid()]];
        $db   = new HaopingOrder();
        $data = $db->where($map)->order(['create_time' => 'desc'])->append(['status_export', 'wechat_status_export', 'order_no_export']);
        $data = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'order_no_export'      => '淘宝单号',
            'give_amount'          => '返现金额',
            'mch_billno'           => '商户单号',
            'third_billno'         => '交易单号',
            'status_export'        => '审核状态',
            'wechat_status_export' => '红包状态',
            'last_examine_time'    => '审核时间',
            'create_time'          => '创建时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *导入订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function import()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $file            = new Excel();
        $param           = $this->params;
        $bid             = $this->get_bid();
        $allowField      = [
            'order_no'    => '订单号',
            'give_amount' => '返现金额',
        ];
        $arr             = $file->load($param['file'])->excelToArray($allowField);
        $is_success      = 0; // 成功数量
        $is_exist_count  = 0; // 已存在数量
        $is_failed_count = 0; // 失败在数量
        foreach ($arr as $key => $val) {
            // 定义表单验证规则
            $rules = [
                'order_no|订单号'      => 'require|number|max:50',
                'give_amount|返现金额' => 'require|float|between:1,200',
            ];
            $row   = $key + 2;
            // 验证excel数据
            try {
                $this->validate($val, $rules);
            } catch (Exception $e) {
                error('第' . $row . '行校验失败:' . $e->getMessage());
            }
            $db       = new HaopingOrder();
            $map      = [
                ['bid', '=', $bid],
                ['order_no', '=', $val['order_no']],
            ];
            $is_exist = $db->where($map)->count();
            if ($is_exist) {
                ++$is_exist_count;
                continue;
            }
            $data            = [
                'guid'        => create_guid(),
                'bid'         => $bid,
                'order_no'    => $val['order_no'],
                'give_amount' => (float)$val ['give_amount']
            ];
            $res             = $db->save($data);
            $is_success      = $res ? ++$is_success : $is_success;
            $is_failed_count = ($res == 0) ? ++$is_failed_count : $is_failed_count;
            unset($data);
        }
        $msg = "成功导入" . $is_success . "条";
        if ($is_exist_count > 0) {
            $msg .= ',' . $is_exist_count . "条已经存在";
        }
        if ($is_failed_count > 0) {
            $msg .= ',失败' . $is_failed_count . "条";
        }
        wr_log($msg);
        success($msg);
    }
}
 