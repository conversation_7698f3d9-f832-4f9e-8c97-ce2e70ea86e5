<?php

namespace app\controller\admin_api\v1;

use app\model\Rule as RuleModel;
use Exception;

class Role extends BasicAdminApi
{
    /**
     *角色列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *角色权限ztree版本
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function access()
    {
        $db_rule  = new RuleModel();
        $node_str = $db_rule->get_role_node_info($this->params['guid']);
        result($node_str);
    }
}
