<?php

namespace app\controller\admin_api\v1;

use app\model\WechatFuncInfo as WechatFuncInfoModel;

class WechatFuncInfo extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new WechatFuncInfoModel();
        $map         = [];
        $this->model = $this->model->where($map)->order(['type' => 'ASC', 'func_id' => 'ASC']);
        result($this->_list());
    }
}
