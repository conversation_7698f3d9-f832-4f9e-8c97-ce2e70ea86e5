<?php

namespace app\controller\admin_api\v1;

use app\model\GoodsAttr as GoodsAttrModel;
use app\model\GoodsAttrGroup as GoodsAttrGroupModel;
use app\model\GoodsSku;
use Exception;

class GoodsAttr extends BasicAdminApi
{

    /**
     *SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *编辑SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params          = $this->params;
        $bid             = $this->get_bid();
        $form_data       = tools()::_parse_fs_form_data_attr($params, 'attr_list');
        $attr_group_name = $form_data['name'];
        $attr_group_guid = $form_data['guid'];
        $map             = [
            ['bid', '=', $bid],
            ['guid', '=', $attr_group_guid],
        ];
        $update_data     = ['name' => $attr_group_name];
        //先保存规格名
        $db_goods_attr_group = new GoodsAttrGroupModel();
        $db_goods_attr_group::update($update_data, $map);
        $attr_list     = $form_data['attr_list'];
        $attr_name     = [];
        $db_goods_attr = new GoodsAttr();
        foreach ($attr_list as $key => $val) {
            if ($val['guid']) {
                $map         = [
                    ['bid', '=', $bid],
                    ['guid', '=', $val['guid']],
                ];
                $update_data = ['goods_attr_name' => $val['name']];
                $db_goods_attr::update($update_data, $map);
            } else {
                $insert_data = [
                    'guid'                  => create_guid(),
                    'bid'                   => $bid,
                    'goods_attr_group_guid' => $attr_group_guid,
                    'goods_attr_name'       => $val['name']
                ];
                $db_goods_attr->save($insert_data);
            }
            $attr_name[] = $val['name'];
        }
        $db_goods_attr = new GoodsAttr();
        $map           = [
            ['bid', '=', $bid],
            ['goods_attr_name', 'not in', $attr_name],
            ['goods_attr_group_guid', '=', $attr_group_guid],
        ];
        $update_data   = ['delete_time' => format_timestamp()];
        $db_goods_attr::update($update_data, $map);
        success('更新成功');
    }

    /**
     *获取商品SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_attr()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $db_goods_sku = new GoodsSku();
        $goods_guid   = $params['goods_guid'] ?? '';
        if (empty($goods_guid)) {
            result([]);
        }
        $join                 = [
            ['goods_sku_item gsi', " gsi.goods_sku_guid = gs.guid AND gsi.bid = gs.bid"],
            ['goods_attr ga', " gsi.goods_attr_guid = ga.guid AND ga.bid = gs.bid"],
        ];
        $field                = [
            'gs.guid'                  => 'goods_sku_guid',
            'gs.price'                 => 'goods_price',
            'gs.stock'                 => 'stock',
            'ga.goods_attr_group_guid' => 'goods_attr_group_guid',
            'ga.guid'                  => 'goods_attr_guid',
        ];
        $map                  = [
            ['gs.bid', '=', $bid],
            ['gs.goods_guid', '=', $goods_guid],
            ['gs.delete_time', 'null', null]
        ];
        $list                 = $db_goods_sku->alias('gs')->where($map)->join($join)->field($field)->select()->toArray();
        $goods_sku_guid_array = [];
        $result               = [];
        foreach ($list as $key => $val) {
            if (!in_array($val['goods_sku_guid'], $goods_sku_guid_array)) {
                $goods_sku_guid_array[] = $val['goods_sku_guid'];
                $push                   = [
                    'ids'   => [],
                    'price' => (float)$val['goods_price'],
                    'stock' => (int)$val['stock'],
                    'sku'   => 0,
                ];
                foreach ($list as $k => $v) {
                    if ($v['goods_sku_guid'] == $val['goods_sku_guid']) {
                        $push['ids'][] = [$v['goods_attr_group_guid'] => $v['goods_attr_guid']];
                    }
                }
                $result[] = $push;
            }
        }
        result($result);
    }

    /**
     *获取商品SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_attr_bak()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_goods_sku         = new GoodsSku();
        $goods_guid           = $params['goods_guid'];
        $join                 = [
            ['goods_sku_item gsi', "gsi.goods_sku_guid = gs.guid AND gsi.bid = gs.bid"],
            ['goods_attr ga', "gsi.goods_attr_guid = ga.guid AND ga.bid = gs.bid"],
        ];
        $field                = [
            'gs.guid'                  => 'goods_sku_guid',
            'gs.price'                 => 'goods_price',
            'gs.stock'                 => 'stock',
            'ga.goods_attr_group_guid' => 'goods_attr_group_guid',
            'ga.guid'                  => 'goods_attr_guid',
        ];
        $map                  = [
            ['gs.bid', '=', $bid],
            ['gs.goods_guid', '=', $goods_guid],
            ['gs.delete_time', 'null', null]
        ];
        $list                 = $db_goods_sku->alias('gs')->where($map)->join($join)->field($field)->select()->toArray();
        $goods_sku_guid_array = [];
        $result               = [];
        foreach ($list as $key => $val) {
            if (!in_array($val['goods_sku_guid'], $goods_sku_guid_array)) {
                $goods_sku_guid_array[] = $val['goods_sku_guid'];
                $push                   = [
                    'ids'   => [],
                    'price' => (float)$val['goods_price'],
                    'stock' => (int)$val['stock'],
                    'sku'   => 0,
                ];
                foreach ($list as $k => $v) {
                    if ($v['goods_sku_guid'] == $val['goods_sku_guid']) {
                        $push['ids'][] = [$v['goods_attr_group_guid'] => $v['goods_attr_guid']];
                    }
                }
                $result[] = $push;
            }
        }
        result($result);
    }

    /**
     *获取SKU列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_attr_list()
    {
        $bid                   = $this->get_bid();
        $db_goods_attr         = new GoodsAttr();
        $db_goods_attr_group   = new GoodsAttrGroupModel();
        $attr_list             = [];
        $map                   = [['bid', '=', $bid]];
        $goods_attr_group_list = $db_goods_attr_group->where($map)->select()->toArray();
        $goods_attr_list       = $db_goods_attr->where($map)->select()->toArray();
        foreach ($goods_attr_group_list as $key => $val) {
            $push = [
                'id'   => $val['guid'],
                'name' => $val['name'],
                'sub'  => [],
            ];
            foreach ($goods_attr_list as $k => $v) {
                if ($v['goods_attr_group_guid'] == $val['guid']) {
                    $push['sub'][] = [
                        'id'   => $v['guid'],
                        'name' => $v['goods_attr_name'],
                    ];
                }
            }
            $attr_list[] = $push;
        }
        result($attr_list);
    }

    /**
     *添加SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del_v2()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $db_goods_attr = new GoodsAttrModel();
        $guid          = $params['id'];
        if (!$db_goods_attr->check_safe_delete($guid)) {
            error('删除失败:' . $db_goods_attr->getError());
        }
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $update_data = ['delete_time' => format_timestamp()];
        $db_goods_attr::update($update_data, $map);
        success('删除成功');
    }

    /**
     *添加SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add_v2()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $goods_attr_group_guid = $params['spec_id'];
        $goods_attr_name       = $params['title'];

        $db_goods_attr_group = new GoodsAttrGroupModel();
        $db_goods_group      = new GoodsAttrModel();
        $map                 = [
            ['bid', '=', $bid],
            ['bid', '=', $bid],
            ['goods_attr_name', '=', $goods_attr_name],
            ['goods_attr_group_guid', '=', $goods_attr_group_guid],
        ];
        $count               = $db_goods_group->where($map)->count();
        if ($count) {
            error('当前规格值已存在!');
        }
        $attr_guid   = create_guid();
        $insert_data = [
            'guid'                  => $attr_guid,
            'bid'                   => $bid,
            'goods_attr_name'       => $goods_attr_name,
            'goods_attr_group_guid' => $goods_attr_group_guid
        ];
        //先保存规格名
        $db_goods_group->save($insert_data);
        result(['id' => $attr_guid]);
    }

    /**
     *添加SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $form_data           = tools()::_parse_fs_form_data_attr($params, 'attr_list');
        $db_goods_attr_group = new GoodsAttrGroupModel();
        $attr_group_name     = $form_data['name'];
        $map                 = [
            ['bid', '=', $bid],
            ['name', '=', $attr_group_name],
        ];
        $count               = $db_goods_attr_group->where($map)->count();
        if ($count) {
            error('当前规格值已存在');
        }
        $attr_group_guid = create_guid();
        $insert_data     = [
            'guid' => $attr_group_guid,
            'bid'  => $bid,
            'name' => $attr_group_name
        ];
        //先保存规格名
        $db_goods_attr_group->save($insert_data);
        //保存规格值
        $goods_attr_list = [];
        foreach ($form_data['attr_list'] as $attr) {
            $goods_attr_list[] = [
                'guid'                  => create_guid(),
                'bid'                   => $bid,
                'goods_attr_group_guid' => $attr_group_guid,
                'goods_attr_name'       => $attr['name'],
            ];
        }
        $db_goods_attr = new GoodsAttrModel();
        $db_goods_attr->saveAll($goods_attr_list, false);
        success('新增成功');
    }

    /**
     *获取SKU
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_attr()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $attr_group_guid      = $params['guid'];
        $map                  = [
            ['bid', '=', $bid],
            ['goods_attr_group_guid', '=', $attr_group_guid],
        ];
        $db_goods_attr        = new GoodsAttr();
        $goods_attr_name_list = $db_goods_attr->where($map)->select();
        $data                 = [];
        foreach ($goods_attr_name_list as $key => $val) {
            $data[] = ['guid' => $val['guid'], 'name' => $val['goods_attr_name'],];
        }
        result($data);
    }
}