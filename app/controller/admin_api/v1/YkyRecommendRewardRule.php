<?php

namespace app\controller\admin_api\v1;

use app\model\YkyRecommendRewardRule as YkyRecommendRewardRuleModel;
use Exception;

class YkyRecommendRewardRule extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyRecommendRewardRuleModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'NULL', NULL]
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 新增
     * @access public
     * @return void
     * @throws Exception
     */
    public function add()
    {
        $params = $this->params;
        if ($params['member_group_guid']) {
            $params['member_group_guid'] = explode(',', $params['member_group_guid']);
        } else {
            unset($params['member_group_guid']);
        }
        if ($params['chain_store_guid']) {
            $params['chain_store_guid'] = explode(',', $params['chain_store_guid']);
        } else {
            unset($params['chain_store_guid']);
        }
        if ($params['coupon_guid']) {
            $params['coupon_guid'] = explode(',', $params['coupon_guid']);
        } else {
            unset($params['coupon_guid']);
        }
        $this->check_data($params);
        $db = new YkyRecommendRewardRuleModel();
        $db->add($params);
        success('添加成功');
    }

    protected function check_data($data)
    {
        $max_value = 0.5;
        if (isset($data['type']) && $data['type'] == 1) {
            $levels = [
                'first_level_reward'  => '一级',
                'second_level_reward' => '二级',
                'third_level_reward'  => '三级'
            ];
            foreach ($levels as $key => $levelName) {
                if (isset($data[$key]) && $data[$key] > $max_value) {
                    error(sprintf('【%s奖励金额】为百分比,请输入0.01-%s之间的数值,0.1代表10%%', $levelName, $max_value));
                }
            }
        }
    }

    /**
     * 编辑
     * @access public
     * @return void
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        if (!empty($params['member_group_guid'])) {
            $params['member_group_guid'] = explode(',', $params['member_group_guid']);
        } else {
            unset($params['member_group_guid']);
        }
        if (!empty($params['chain_store_guid'])) {
            $params['chain_store_guid'] = explode(',', $params['chain_store_guid']);
        } else {
            unset($params['chain_store_guid']);
        }
        if (!empty($params['coupon_guid'])) {
            $params['coupon_guid'] = explode(',', $params['coupon_guid']);
        } else {
            unset($params['coupon_guid']);
        }
        $this->check_data($params);
        $db = new YkyRecommendRewardRuleModel();
        $db->edit($params);
        success('编辑成功');
    }

    /**
     * 删除
     * @access public
     * @return void
     * @throws Exception
     */
    public function del()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $db          = new YkyRecommendRewardRuleModel();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']],
        ];
        $update_data = ['delete_time' => format_timestamp()];
        $db::update($update_data, $map);
        success('删除成功');
    }


    /**
     * 列表
     * @access 会员级别列表
     * @return mixed
     * @throws Exception
     */
    public function member_group_list()
    {
        $params                       = $this->params;
        $db                           = new \app\model\YkyMemberGroup();
        $db_yky_recommend_reward_rule = new YkyRecommendRewardRuleModel();
        $bid                          = $this->get_bid();
        $map                          = [
            ['bid', '=', $bid],
        ];
        $list                         = $db->where($map)->field(['group_name', 'guid' => 'member_group_guid'])->order(['group_name' => 'ASC'])->select()->toArray();
        $guid                         = $params['guid'] ?? '';
        if ($guid) {
            //本规则的级别默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_recommend_reward_rule->where($map)->field(['member_group_guid'])->find();
            if (is_array($rule['member_group_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['member_group_guid'], $rule['member_group_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的级别禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_recommend_reward_rule->where($map)->field(['member_group_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['member_group_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['member_group_guid'], $val['member_group_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    /**
     * 列表
     * @access 门店列表
     * @return mixed
     * @throws Exception
     */
    public function chain_store_list()
    {
        $params                       = $this->params;
        $db                           = new \app\model\YkyChainStore();
        $bid                          = $this->get_bid();
        $map                          = [
            ['bid', '=', $bid],
        ];
        $list                         = $db->where($map)->field(['store_name', 'guid' => 'chain_store_guid'])->order(['store_name' => 'ASC'])->select()->toArray();
        $guid                         = $params['guid'] ?? '';
        $db_yky_recommend_reward_rule = new YkyRecommendRewardRuleModel();
        if ($guid) {
            //本规则的门店默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_recommend_reward_rule->where($map)->field(['chain_store_guid'])->find();
            if (is_array($rule['chain_store_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['chain_store_guid'], $rule['chain_store_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
//        //不在本规则的门店禁用
//        $map        = [
//            ['bid', '=', $bid],
//            ['guid', '<>', $guid],
//        ];
//        $other_rule = $db_yky_recommend_reward_rule->where($map)->field(['chain_store_guid'])->select();
//        foreach ($other_rule as $key => $val) {
//            if (is_array($val['chain_store_guid'])) {
//                foreach ($list as $k => $v) {
//                    if (in_array($v['chain_store_guid'], $val['chain_store_guid'])) {
//                        $list[$k]['disabled'] = true;
//                    }
//                }
//            }
//        }
        result($list);
    }

    /**
     * 列表
     * @access 商品列表
     * @return mixed
     * @throws Exception
     */
    public function coupon_list()
    {
        $params                       = $this->params;
        $db                           = new \app\model\YkyCoupon();
        $bid                          = $this->get_bid();
        $map                          = [
            ['bid', '=', $bid],
        ];
        $list                         = $db->where($map)->field(['title', 'guid' => 'coupon_guid'])->order(['title' => 'ASC'])->select()->toArray();
        $guid                         = $params['guid'] ?? '';
        $db_yky_recommend_reward_rule = new YkyRecommendRewardRuleModel();
        if ($guid) {
            //本规则的门店默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_recommend_reward_rule->where($map)->field(['coupon_guid'])->find();
            if (is_array($rule['coupon_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['coupon_guid'], $rule['coupon_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的商品禁用
        $map = [
            ['bid', '=', $bid],
            ['guid', '<>', $guid],
        ];
//        $other_rule = $db_yky_recommend_reward_rule->where($map)->field(['coupon_guid'])->select();
//        foreach ($other_rule as $key => $val) {
//            if (is_array($val['coupon_guid'])) {
//                foreach ($list as $k => $v) {
//                    if (in_array($v['coupon_guid'], $val['coupon_guid'])) {
//                        $list[$k]['disabled'] = true;
//                    }
//                }
//            }
//        }
        result($list);
    }
}