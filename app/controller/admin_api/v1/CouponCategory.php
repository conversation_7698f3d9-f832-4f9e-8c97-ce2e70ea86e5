<?php

namespace app\controller\admin_api\v1;

use app\model\User;
use Exception;

class CouponCategory extends BasicAdminApi
{
    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_user            = new User();
        $map                = [
            ['bid', '=', $this->get_bid()],
            ['create_user_id', 'IN', $db_user->getChildUserIdArray()]
        ];
        $db_coupon_category = new \app\model\CouponCategory();
        $this->model        = $db_coupon_category->where($map)->order(['sort' => 'ASC', 'create_time' => 'DESC'])->append(['mini_pic_mini']);
        result($this->_list());

    }
}
