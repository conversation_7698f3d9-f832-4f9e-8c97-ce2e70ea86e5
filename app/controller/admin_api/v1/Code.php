<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use app\model\CouponGoodsItem;
use app\model\CouponGoodsCategoryItem;
use app\model\CouponSendNote;
use app\model\CouponUsedNote;
use app\model\Goods;
use app\model\GoodsCategory;
use app\model\GoodsOrder;
use app\model\Rule;
use app\model\ShortUrl;
use app\model\User;
use app\model\User as UserModel;
use app\common\tools\Excel;
use Darabonba\GatewaySpi\Models\InterceptorContext\request;
use Exception;
use think\facade\Db;

class Code extends BasicAdminApi
{
    public function coupon_item_list()
    {
        $db        = new User();
        $map       = [
            ['bid', '=', $this->get_bid()],
            ['operator_user_id', 'IN', $db->getChildUserIdArray()],
        ];
        $db_coupon = new Coupon();
        $list      = $db_coupon->field(['name', 'guid' => 'coupon_item_guid'])->where($map)->order(['status' => 'DESC', 'sort' => 'ASC', 'create_time' => 'DESC', 'name' => 'ASC', 'id' => 'DESC'])->select()->toArray();
        result($list);
    }

    public function report()
    {
        $params                 = $this->params;
        $coupon_item_guid       = $params['coupon_item_guid'] ?? '';
        $coupon_item_guid_array = array_filter(explode(',', $coupon_item_guid));
        $db_user                = new UserModel();
        $bid                    = $this->get_bid();
        $list                   = [];
        $db_coupon_send_note    = new CouponSendNote();
        $map_coupon_send_note   = [
            ['bid', '=', $bid],
            ['owner_user_id', 'in', $db_user->getChildUserIdArray()],
        ];
        if (!empty($coupon_item_guid_array)) {
            $map_coupon_send_note[] = ['coupon_guid', 'IN', $coupon_item_guid_array];
        }
        $db_coupon              = new Coupon();
        $map_coupon             = [
            ['bid', '=', $bid],
            ['delete_time', 'NULL', null],
        ];
        $coupon_guid_all        = $db_coupon->where($map_coupon)->column('guid');
        $map_coupon_send_note[] = ['coupon_guid', 'IN', $coupon_guid_all];
        $map_coupon_send_note[] = ['delete_time', 'NULL', null];
        $field                  = [
            'status',
            'count(1)' => 'num'
        ];
        $all_coupon_send_note   = $db_coupon_send_note->field($field)->where($map_coupon_send_note)->group(['status'])->select()->toArray();
        if (empty($all_coupon_send_note)) {
            error('没有数据');
        }
        $all_coupon_send_note_count = 0;
        foreach ($all_coupon_send_note as $key => $val) {
            $all_coupon_send_note_count += $val['num'];
        }
        $list[] = [
            'key'   => '累计生成数量',
            'value' => $all_coupon_send_note_count,
            'url'   => '#',
            'title' => '#'
        ];
        foreach ($all_coupon_send_note as $key => $val) {
            switch ($val['status']) {
                case -2:
                    $list[] = [
                        'key'   => '已作废(张)',
                        'value' => $val['num'],
                        'url'   => '#',
                        'title' => '#'
                    ];
                    break;
                case -1:
                    $list[] = [
                        'key'   => '待激活(张)',
                        'value' => $val['num'],
                        'url'   => '#',
                        'title' => '#'
                    ];
                    break;
                case 0:
                    $list[] = [
                        'key'   => '已激活(张)',
                        'value' => $val['num'],
                        'url'   => '#',
                        'title' => '#'
                    ];
                    break;
                case 1:
                    $list[] = [
                        'key'   => '已使用(张)',
                        'value' => $val['num'],
                        'url'   => '#',
                        'title' => '#'
                    ];
                    break;
                default:
                    break;
            }
        }
        //分析金额卡/充值卡
        $map_coupon_send_note[]        = ['send_value', '>', 0];
        $field                         = [
            'SUM(send_value) AS send_value',
            'SUM(used_value) AS used_value',
            Db::raw("SUM(send_value) - SUM(used_value) as available_value"),
        ];
        $all_coupon_send_note_of_value = $db_coupon_send_note->field($field)->where($map_coupon_send_note)->findOrEmpty();
        if (!$all_coupon_send_note_of_value->isEmpty() && $all_coupon_send_note_of_value['send_value'] > 0) {
            $list[] = [
                'key'   => '已发行金额',
                'value' => $all_coupon_send_note_of_value['send_value'],
                'url'   => '#',
                'title' => '#'
            ];
            $list[] = [
                'key'   => '已使用金额',
                'value' => $all_coupon_send_note_of_value['used_value'],
                'url'   => '#',
                'title' => '#'
            ];
            $list[] = [
                'key'   => '剩余金额',
                'value' => $all_coupon_send_note_of_value['available_value'],
                'url'   => '#',
                'title' => '#'
            ];
        }

        $db_goods_order = new GoodsOrder();

        $join = [
            ['coupon c', 'go.bid = c.bid and go.coupon_guid = c.guid'],
            ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid'],
        ];

        $db_user = new User();
        $map     = [
            ['go.bid', '=', $bid],
            ['go.status', 'NOT IN', [-1, -2]], //不展示已取消订单
            ['c.delete_time', 'null', null], //不展示已删除的卡券订单
            ['go.delete_time', 'null', null],
            ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildUserIdArray()], // 只显示自己范围内的订单
        ];
        if (!empty($coupon_item_guid_array)) {
            $map[] = ['c.guid', 'IN', $coupon_item_guid_array];
        }
        $field      = [
            'count(1)' => 'num',
            'CASE WHEN go.status = 1 OR go.status = 2 THEN 1 ELSE go.status END AS real_status' // 将状态 1(已发货) 和 2(已完成) 视为 1，其他保持不变
        ];
        $order_info = $db_goods_order->alias('go')->where($map)->join($join)->field($field)->group(['real_status'])->select()->toArray();
        if (!empty($order_info)) {
            foreach ($order_info as $key => $val) {
                switch ($val['real_status']) {
                    case 0:
                        $list[] = [
                            'key'   => '待发货',
                            'value' => $val['num'],
                            'url'   => '#',
                            'title' => '#'
                        ];
                        break;
                    case 1:
                        $list[] = [
                            'key'   => '已发货',
                            'value' => $val['num'],
                            'url'   => '#',
                            'title' => '#'
                        ];
                        break;
                    default:
                        break;
                }
            }
        }
        result(['count' => $list]);
    }

    /**
     *卡券列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_coupon = new Coupon();
        $bid       = $this->get_bid();
        $db_user   = new User();
        $map       = [['bid', '=', $bid]];
        if (!$db_user->is_all_data_range()) {
            if ($db_coupon->is_enable_user_guid_json($bid)) {
                $user_guid_json = json_encode([$this->get_user_guid()]);
                $map[]          = Db::raw("JSON_CONTAINS(user_guid_json,'$user_guid_json')>0 OR data_range_type = 0");
            } else {
                $map[] = ['operator_user_id', 'IN', $db_user->getChildUserIdArray()];
            }
        }
        $this->model = $db_coupon->where($map)->order(['status' => 'DESC', 'sort' => 'ASC', 'create_time' => 'DESC', 'id' => 'DESC']);
        result($this->_list());
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_user_guid_json()
    {
        $params    = $this->params;
        $db        = new UserModel();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $field     = [
            Db::raw("CONCAT(account,'(',name,')') as user_info"),
            'guid' => 'user_guid'
        ];
        $list      = $db->where($map)->field($field)->order(['create_time' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $user_info = [];
        if ($guid) {
            $db_coupon = new Coupon();
            $map       = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $user_info = $db_coupon->where($map)->field(['user_guid_json'])->findOrEmpty();
        }
        if (!empty($user_info) && is_array($user_info['user_guid_json'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['user_guid'], $user_info['user_guid_json'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }

    /**
     *添加卡券
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params                    = $this->params;
        $bid                       = $this->get_bid();
        $params['goods_item_guid'] = explode(',', $params['goods_item_guid']);
        $params['goods_item_guid'] = array_filter($params['goods_item_guid']);
        $goods_item_guid_num       = count($params['goods_item_guid']);

        // 处理商品类别数据（赠礼卡类型）
        if (isset($params['goods_category_guid'])) {
            $params['goods_category_guid'] = explode(',', $params['goods_category_guid']);
            $params['goods_category_guid'] = array_filter($params['goods_category_guid']);
        } else {
            unset($params['goods_category_guid']);
        }

        if (!empty($params['date_rule_guid'])) {
            $params['date_rule_guid'] = explode(',', $params['date_rule_guid']);
        } else {
            unset($params['date_rule_guid']);
        }
        switch ($params['type']) {
            case 1:
                if (!isset($params['exchange_goods_num'])) {
                    error('exchange_goods_num 不能为空');
                }
                if (empty($goods_item_guid_num)) {
                    error('请选择关联产品!');
                }
                $exchange_goods_num = $params['exchange_goods_num'];
                if ($exchange_goods_num <= 0) {
                    error('可提货数量最低需要为1');
                }
                if ($exchange_goods_num > 1) {
                    // 低版本只允许多选一  判断 multiple_choice_goods 权限
                    $db_rule = new Rule();
                    if ($db_rule->check_rule($bid, $this->get_user_guid(), Rule::MULTIPLE_CHOICE_GOODS) === false) {
                        error('您的版本仅支持一对一或者多选一!');
                    }
                }
                if ($params['exchange_goods_num'] > $goods_item_guid_num) {
                    error('可兑数量不能大于产品总件数' . $goods_item_guid_num . '件');
                }
                break;
            case 2:
                if (empty($params['value'])) {
                    error('面额不能为空');
                }
                if (empty($goods_item_guid_num)) {
                    error('请选择关联产品!');
                }
                $params['exchange_goods_num'] = 1;
                break;
            case 3:
                $params['exchange_goods_num'] = 1;
                if (empty($params['value'])) {
                    error('面额不能为空');
                }
                break;
            case 4:
                // 赠礼卡类型，不需要限制商品数量和金额
                $params['exchange_goods_num'] = 1;
                if (empty($params['goods_category_guid'])) {
                    error('请选择限定的商品类别');
                }
                break;
            default:
        }
        $db_coupon = new Coupon();
        $db_coupon->add($params);
        success('添加成功');
    }

    /**
     *编辑卡券
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $db_coupon = new Coupon();
        $db_coupon->del($params);
        success('删除成功');
    }

    public function batch_renew()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (empty($params['renew_type'])) {
            error('请选择延期时长');
        }
        $renew_type = $params['renew_type'];
        $guid_array = explode(',', $params['guid']);
        if (empty($guid_array)) {
            error('请选择卡券');
        }
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', 'IN', $guid_array],
        ];
        $update_data = ['expire_time' => Db::raw('DATE_ADD(expire_time, INTERVAL ' . $renew_type . ' YEAR)')];
        $db_coupon::update($update_data, $map_coupon);
        success('编辑成功');
    }

    /**
     *编辑卡券
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (isset($params['goods_item_guid'])) {
            $params['goods_item_guid'] = explode(',', $params['goods_item_guid']);
            $params['goods_item_guid'] = array_filter($params['goods_item_guid']);
        } else {
            unset($params['goods_item_guid']);
        }

        // 处理商品类别数据（赠礼卡类型）
        if (isset($params['goods_category_guid'])) {
            $params['goods_category_guid'] = explode(',', $params['goods_category_guid']);
            $params['goods_category_guid'] = array_filter($params['goods_category_guid']);
        } else {
            unset($params['goods_category_guid']);
        }

        if (isset($params['date_rule_guid'])) {
            $params['date_rule_guid'] = explode(',', $params['date_rule_guid']);
        } else {
            unset($params['date_rule_guid']);
        }
        $type = $params['type'] ?? null;
        switch ($type) {
            case 1:
                if (!isset($params['exchange_goods_num'])) {
                    error('exchange_goods_num 不能为空');
                }
                if (empty($params['goods_item_guid'])) {
                    error('请选择可提货的产品');
                }
                $goods_item_guid_num = count($params['goods_item_guid']);
                if ($params['exchange_goods_num'] > $goods_item_guid_num) {
                    error('可兑数量不能大于产品总件数' . $goods_item_guid_num . '件');
                }
                break;
            case 2:
                if (!isset($params['value'])) {
                    error('value 不能为空');
                }
                if (empty($params['goods_item_guid'])) {
                    error('请选择可提货的产品');
                }
                break;
            case 4:
                // 赠礼卡类型，验证商品类别
                if (empty($params['goods_category_guid'])) {
                    error('请选择限定的商品类别');
                }
                break;
            default:
        }
        $db_coupon = new Coupon();
        $db_coupon->edit($params);
        success('编辑成功');
    }


    //    /**
    //     *日期规则列表 已废弃 后来改成了 日期规则 关联卡券
    //     * @access public
    //     * @return mixed
    //     * @throws Exception
    //     */
    //    public function date_rule_list()
    //    {
    //        $params = $this->params;
    //        $db     = new DateRule();
    //        $bid    = $this->get_bid();
    //        $map    = [
    //            ['bid', '=', $bid],
    //            ['status', '=', 1],
    //        ];
    //        $list   = $db->where($map)->field(['name', 'guid' => 'date_rule_guid'])->order(['name' => 'ASC'])->select()->toArray();
    //        $guid   = $params['guid'] ?? '';
    //        $rule   = [];
    //        if ($guid) {
    //            //本规则的商品默认选中
    //            $db_coupon = new Coupon();
    //            $map       = [
    //                ['bid', '=', $bid],
    //                ['guid', '=', $guid],
    //            ];
    //            $rule      = $db_coupon->where($map)->field(['date_rule_guid'])->findOrEmpty();
    //        }
    //
    //        if (!empty($rule) && is_array($rule['date_rule_guid'])) {
    //            foreach ($list as $key => $val) {
    //                if (in_array($val['date_rule_guid'], $rule['date_rule_guid'])) {
    //                    $list[$key]['selected'] = true;
    //                }
    //            }
    //        }
    //        //不在本规则的商品禁用
    //        //       $map        = [
    //        //           ['bid', '=', $bid],
    //        //           ['guid', '<>', $guid],
    //        //       ];
    //        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
    //        //       foreach ($other_rule as $key => $val) {
    //        //           if (is_array($val['goods_item_guid'])) {
    //        //               foreach ($list as $k => $v) {
    //        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
    //        //                       $list[$k]['disabled'] = true;
    //        //                   }
    //        //               }
    //        //           }
    //        //       }
    //        result($list);
    //    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_item_list()
    {
        $params = $this->params;
        $db     = new Goods();
        $bid    = $this->get_bid();
        $map    = [['bid', '=', $bid]];
        //只能查看自己创建或者自己供应的产品
        $db_user               = new User();
        $map[]                 = ['create_user_id|owner_user_id', 'in', $db_user->getChildUserIdArray()];
        $list                  = $db->where($map)->field(['name', 'guid' => 'goods_item_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid                  = $params['guid'] ?? '';
        $coupon_send_note_guid = $params['coupon_send_note_guid'] ?? '';
        $goods_item_guid_array = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_coupon             = new Coupon();
            $map                   = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule                  = $db_coupon->where($map)->field(['goods_item_guid'])->findOrEmpty();
            $db_coupon_goods_item  = new CouponGoodsItem();
            $goods_item_guid_array = $db_coupon_goods_item->get_goods_guid_array($bid, $guid);
        } elseif ($coupon_send_note_guid) {
            //本规则的商品默认选中
            $db_coupon_send_note   = new CouponSendNote();
            $map                   = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule                  = $db_coupon_send_note->where($map)->field(['goods_item_guid'])->findOrEmpty();
            $goods_item_guid_array = $rule->isEmpty() ? [] : $rule['goods_item_guid'];
        }
        if (!empty($goods_item_guid_array)) {
            foreach ($list as $key => $val) {
                if (in_array($val['goods_item_guid'], $goods_item_guid_array)) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }

    /**
     * 商品类别列表（用于赠礼卡多选组件）
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goods_category_item_list()
    {
        $params = $this->params;
        $db = new GoodsCategory();
        $bid = $this->get_bid();
        $map = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];

        // 获取所有商品类别
        $list = $db->where($map)->field(['name', 'guid' => 'goods_category_guid'])->order(['sort' => 'ASC', 'name' => 'ASC'])->select()->toArray();

        $guid = $params['guid'] ?? '';
        $goods_category_guid_array = [];

        if ($guid) {
            // 获取卡券已选择的商品类别，默认选中
            $db_coupon_goods_category_item = new CouponGoodsCategoryItem();
            $goods_category_guid_array = $db_coupon_goods_category_item->get_category_guid_array($bid, $guid);
        }

        // 标记已选择的类别
        if (!empty($goods_category_guid_array)) {
            foreach ($list as $key => $val) {
                if (in_array($val['goods_category_guid'], $goods_category_guid_array)) {
                    $list[$key]['selected'] = true;
                }
            }
        }

        result($list);
    }

    public function get_pick_up_qrcode()
    {
        $params                 = $this->params;
        $bid                    = $this->get_bid();
        $guid                   = $params['guid'];
        $db_coupon              = new Coupon();
        $verify_url_qrcode_list = $db_coupon->get_verify_url_qrcode_list($bid, $guid);
        $long_url               = $verify_url_qrcode_list['long_url'];
        $short_url              = $verify_url_qrcode_list['short_url'];
        $qrcode_url             = $verify_url_qrcode_list['qrcode_url'];
        $download_url           = $verify_url_qrcode_list['download_url'];
        $data                   = [
            'name'         => '提货码',
            'qrcode_url'   => $qrcode_url,
            'long_url'     => $long_url,
            'short_url'    => $short_url,
            'download_url' => $download_url,
        ];
        result($data);
    }

    public function get_detail_qrcode()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $guid         = $params['guid'];
        $path         = 'member/code/detail';
        $db_short_url = new ShortUrl();
        $result       = $db_short_url->get_url_with_qrcode($path, ['bid' => $bid], ['guid' => $guid]);
        result($result);
    }

    /**
     *获取卡号详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_code_info()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $code                = $params['code'];
        $db_user             = new User();
        $map                 = [
            ['csn.bid', '=', $bid],
            ['csn.owner_user_id', 'IN', $db_user->getChildUserIdArray()],
            ['csn.code', '=', $code]
        ];
        $join                = [
            ['user u', "csn.bid=u.bid AND csn.owner_user_id=u.id", 'LEFT'],
        ];
        $field               = [
            Db::raw("csn.send_num - csn.used_num as available_num"),
            Db::raw("csn.send_value - csn.used_value as available_value"),
            'csn.*',
            "CONCAT(u.name,'-',u.account)" => 'owner_user_info',
        ];
        $db_coupon_send_note = new CouponSendNote();
        $send_note           = $db_coupon_send_note->alias('csn')->join($join)->field($field)->where($map)->findOrEmpty();
        if ($send_note->isEmpty()) {
            error('卡号不存在');
        }
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $send_note['coupon_guid']]
        ];
        $db_coupon   = new Coupon();
        $coupon_info = $db_coupon->where($map)->findOrEmpty();
        if ($coupon_info->isEmpty()) {
            error('当前卡号关联的卡券已被删除');
        }
        $order_info = [];
        if ($send_note['status'] == 1) {
            $coupon_send_note_guid = $send_note['guid'];
            $db_goods_order        = new GoodsOrder();
            $map                   = [
                ['go.bid', '=', $bid],
                ['go.coupon_send_note_guid', '=', $coupon_send_note_guid]
            ];
            $join                  = [
                ['user u', "go.bid=u.bid AND go.send_or_pick_up_user_guid=u.guid", 'LEFT'],
            ];
            $field                 = [
                'go.*',
                "CONCAT(u.name,'-',u.account)" => 'send_or_pick_up_user_info',
            ];
            $order_info            = $db_goods_order->alias('go')->where($map)->join($join)->field($field)->order(['create_time' => 'DESC'])->findOrEmpty();
        }
        $data = [
            'code'        => $code,
            'send_note'   => $send_note,
            'coupon_info' => $coupon_info,
            'order_info'  => $order_info
        ];
        result($data);
    }

    /**
     *获取产品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_goods_list_by_token()
    {
        $params                = $this->params;
        $bid                   = $this->get_bid();
        $token                 = $params['token'] ?? null;
        $coupon_send_note_guid = $params['coupon_send_note_guid'] ?? null;
        $db_coupon_send_note   = new CouponSendNote();
        $data                  = [];
        if ($token) {
            $data = $db_coupon_send_note->get_code_info_by_token($bid, $token);
        } elseif ($coupon_send_note_guid) {
            $data = $db_coupon_send_note->get_code_info($bid, $coupon_send_note_guid);
        } else {
            error('服务器繁忙');
        }
        result($data);
    }

    /**
     * 验证卡号密码,返回token(存储在redis中)
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function verify_code()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $code     = $params['code'] ?? null;
        $password = $params['password'] ?? null;
        $url      = $params['url'] ?? null;
        $url      = tools()::remove_empty_string($url);
        $data     = [];
        if ($code && $password) {
            $data['code']     = $code;
            $data['password'] = $password;
        } elseif (filter_var($url, FILTER_VALIDATE_URL)) {
            //判断是否短链接,是的话 获取 token
            $str        = '://' . config('app.app_host_domain') . '/u/';
            $url_params = tools()::parse_url_params($url);
            //   ://yikayi.net/u/jHyzXP?c=102867&p=582466  //格式2
            if (!empty($url_params['c']) && !empty($url_params['p'])) {
                $data['code']     = $url_params['c'];
                $data['password'] = $url_params['p'];
            } elseif ((strpos($url, $str) !== false)) {
                // https://www.yikayi.net/admin/code/choose_goods?bid=691ed317-21ab-270e-83fb-3aa30b16054b&token=e800043c-1162-a416-419e-21849a24e02d&from=mobile
                $short_code = tools()::search_str($str, '', $url);
                $db         = new ShortUrl();
                $long_url   = $db->get_long_url($short_code);
                if (!$long_url) {
                    error('此二维码已过期');
                }
                if (strpos($long_url, 'token') !== false) {
                    $url_params = tools()::parse_url_params($long_url);
                    if (!empty($url_params['token'])) {
                        if ($url_params['bid'] != $bid) {
                            error('请勿扫描其他商户二维码');
                        }
                        result([
                            'status' => 1,
                            'data'   => ['token' => $url_params['token']]
                        ]);
                    } else {
                        error('此二维码不正确');
                    }
                }
            } else {
                $data['url'] = $url;
            }
        } elseif (is_numeric($url) && strlen($url) == 9) {
            //核销码
            $db_goods_order  = new GoodsOrder();
            $map_goods_order = [
                ['bid', '=', $bid],
                ['pick_up_code', '=', (int)$url],
            ];
            $order_info      = $db_goods_order->where($map_goods_order)->order(['create_time' => 'DESC'])->findOrEmpty();
            if ($order_info->isEmpty()) {
                error('核销码查询不到商品订单');
            } else {
                $result['redirect_page'] = "/pages/order/detail?order_guid=" . $order_info['guid'];
                result($result);
            }
        } elseif (preg_match('/^c=[A-Za-z0-9]+&p=[A-Za-z0-9]+$/', $url) === 1) {
            $pattern = '/c=([A-Za-z0-9]+)&p=([A-Za-z0-9]+)/';
            preg_match_all($pattern, $url, $matches, PREG_SET_ORDER);
            if (!empty($matches[0])) {
                $data['code']     = $matches[0][1];
                $data['password'] = $matches[0][2];
            } else {
                error('请扫码正确的二维码');
            }
        } else {
            error('卡号密码和URL不能同时为空');
        }
        $db_coupon_send_note = new CouponSendNote();
        $result              = $db_coupon_send_note->code_or_send_note_guid_to_token($bid, $data);
        if ($result === false) {
            error('校验失败:' . $db_coupon_send_note->getError());
        } else {
            result($result);
        }
    }

    public function get_start_page_qrcode()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $guid         = $params['guid'];
        $path         = 'member/code/start_page';
        $db_short_url = new ShortUrl();
        $result       = $db_short_url->get_url_with_qrcode($path, ['bid' => $bid, 'type' => 1, 'coupon_guid' => $guid]);
        result($result);
    }

    public function get_config()
    {
        $params                     = $this->params;
        $db_coupon_send_note        = new CouponSendNote();
        $data                       = [
            'title' => 'title',
        ];
        $data['code_is_all_number'] = $db_coupon_send_note->code_is_all_number();
        result($data);
    }

    /**
     * 核销订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_order()
    {
        $params         = $this->params;
        $bid            = $this->get_bid();
        $db_goods_order = new GoodsOrder();
        $type           = $params['type'] ?? 2; // 到店核销
        $params['type'] = $type;
        $order_data     = $db_goods_order->submit($params);
        if (!isset($order_data['status']) || (isset($order_data['status']) && $order_data['status'] != 0)) {
            error('该订单需要支付,暂不支持到店核销');
        }
        //核销成功自动发货
        if ($type == 2) {
            foreach ($order_data['order_guid_array'] as $order_guid) {
                $result = $db_goods_order->send_out_goods($bid, $order_guid, [], 4);
            }
        }
        result($order_data, '提货成功');
    }

    public function used_report_detail_export()
    {
        //todo 待完善
        $db  = new CouponUsedNote();
        $bid = $this->get_bid();
        $map = [
            ['a.bid', '=', $bid],
            ['a.way', '=', 1], //卡券兑换途径
        ];

        $join = [
            ['coupon_send_note b', 'a.coupon_send_guid = b.guid AND a.bid = b.bid  AND a.coupon_guid = b.coupon_guid'],
            ['coupon c', 'b.coupon_guid = c.guid AND a.bid = c.bid'],
            ['user d', 'b.owner_user_id = d.id AND a.bid= d.bid', 'LEFT'],
            ['goods_order e', 'e.bid=b.bid AND a.relation_guid = e.guid AND e.status != -1']
        ];

        $field = [
            //           'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date',
            'sum(a.used_num)' => 'num',
            'a.used_user_id'  => 'used_user_id',
            'c.name'          => 'coupon_name',
            'd.name'          => 'user_name',
            'd.id'            => 'user_id',
            'd.account'       => 'user_account',
            //           'cast(sum(total_amount)/100 as decimal(9,2))'   => 'total_amount'
        ];
        $group = [
            'a.used_user_id',
            'c.name',
            'd.name',
            'd.id',
            'd.account'
        ];
        $data  = $db->alias('a')->join($join)->where($map)->group($group)->field($field)->order(['num' => 'DESC']);
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'coupon_name' => '卡券',
            'code'        => '卡号',
            'password'    => '密码',
            'code_url'    => '二维码链接'
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *核销分析 NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function used_report()
    {
        $params               = $this->params;
        $default_field        = [
            //           'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date',
            'c.name'                                      => 'coupon_name',
            "IFNULL(CONCAT(u.name,'(',u.account,')'),'')" => 'used_user_info',
            "IFNULL(CONCAT(d.name,'(',d.account,')'),'')" => 'owner_user_info',
            'sum(a.used_num)'                             => 'num',
            'sum(a.used_value)'                           => 'value',
            //'cast(sum(total_amount)/100 as decimal(9,2))'   => 'total_amount'
        ];
        $mapping              = [
            'coupon_name'     => ['max(c.guid)' => 'coupon_guid'],
            'used_user_info'  => ['max(a.used_user_id)' => 'used_user_id'],
            'owner_user_info' => ['max(b.owner_user_id)' => 'owner_user_id'],
        ];
        $default_header_field = [
            'coupon_name'     => '卡券名称',
            'used_user_info'  => '核销者',
            'owner_user_info' => '归属者',
            'num'             => '核销张数',
            'value'           => '核销金额',
        ];

        $this->model = new CouponUsedNote();
        $bid         = $this->get_bid();
        $db_user     = new User();
        $map         = [
            ['a.bid', '=', $bid],
            ['e.delete_time', 'null', null],
            //            ['a.way', '=', 1], // 卡券兑换途径
            ['b.owner_user_id|a.used_user_id', 'IN', $db_user->getChildUserIdArray()], // 查看范围
        ];
        $join        = [
            ['coupon_send_note b', 'a.coupon_send_guid = b.guid AND a.bid = b.bid  AND a.coupon_guid = b.coupon_guid'],
            ['coupon c', 'b.coupon_guid = c.guid AND a.bid = c.bid'],
            ['user d', 'b.owner_user_id = d.id AND a.bid= d.bid', 'LEFT'],
            ['user u', 'a.used_user_id = u.id AND a.bid= u.bid', 'LEFT'],
            ['goods_order e', 'e.bid=b.bid AND a.relation_guid = e.guid AND e.status != -1', 'LEFT']
        ];
        $this->model = $this->model->alias('a')->join($join)->where($map);
        result($this->_build_report_query($mapping, $default_field, $default_header_field));
    }

    /**
     *核销分析 NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function used_report_bak()
    {
        $this->model = new CouponUsedNote();
        $bid         = $this->get_bid();
        $map         = [
            ['a.bid', '=', $bid],
            ['a.way', '=', 1], // 卡券兑换途径
        ];
        $join        = [
            ['coupon_send_note b', 'a.coupon_send_guid = b.guid AND a.bid = b.bid  AND a.coupon_guid = b.coupon_guid'],
            ['coupon c', 'b.coupon_guid = c.guid AND a.bid = c.bid'],
            ['user d', 'b.owner_user_id = d.id AND a.bid= d.bid', 'LEFT'],
            ['goods_order e', 'e.bid=b.bid AND a.relation_guid = e.guid AND e.status != -1']
        ];

        $field       = [
            //           'DATE_FORMAT(red_packet_send_time, "%Y-%m-%d")' => 'date',
            'a.used_user_id'    => 'used_user_id',
            'c.name'            => 'coupon_name',
            'd.name'            => 'user_name',
            'd.id'              => 'user_id',
            'd.account'         => 'user_account',
            'sum(a.used_num)'   => 'num',
            'sum(a.used_value)' => 'value',
            //           'cast(sum(total_amount)/100 as decimal(9,2))'   => 'total_amount'
        ];
        $group       = [
            'a.used_user_id',
            'c.name',
            'd.name',
            'd.id',
            'd.account'
        ];
        $this->model = $this->model->alias('a')->join($join)->where($map)->group($group)->field($field)->order(['num' => 'DESC']);
        result($this->_list());
    }


    /**
     *检查
     * @access protected
     * @param integer $count
     * @return mixed
     * @throws Exception
     */
    protected function check($count)
    {
        $max = 100000;
        if ($count > $max) {
            error('一次性最多导出' . $max . '条记录,本次数据有' . $count . '条');
        }
    }
}
