<?php

namespace app\controller\admin_api\v1;

use Exception;

class Customs extends BasicAdminApi
{
    /**
     *商品类别列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    protected function initialize()
    {
        $this->model = new \app\model\GoodsCategory();
        parent::initialize();
    }
}