<?php

namespace app\controller\admin_api\v1;

use app\model\JuheRechargeOrder;
use app\model\Member as MemberModel;
use app\model\User;
use app\common\tools\Excel;
use Exception;

class Oil extends BasicAdminApi
{
    /**
     *重试油卡订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function retry()
    {
        $order_guid = $this->params['guid'];
        $restart    = job()->set_payload($order_guid)->restart();
        success('重启了' . $restart . '条任务');
    }

    /**
     *编辑油卡号
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $guid        = $this->params['guid'];
        $game_userid = $this->params['game_userid'];
        if (!tools()::is_oil_card_id($game_userid)) {
            error('油卡号格式不正确');
        }
        $map   = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid]
        ];
        $db    = new JuheRechargeOrder();
        $order = $db->where($map)->find();
        if (!$order) {
            error('订单不存在');
        }
        if ($order['game_userid'] == $game_userid) {
            error('充值卡号无需更新');
        }
        $update_data = ['game_userid' => $game_userid];
        $update      = $db::update($update_data, $map);
        wr_log('将订单号:' . $order['orderid'] . '充值卡号由:' . $order['game_userid'] . '修改为:' . $game_userid);
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $order['member_guid']]
        ];
        $db_member   = new MemberModel();
        $update_data = ['oil_card_id' => $game_userid];
        $db_member::update($update_data, $map);
        if ($update) {
            success('修改成功');
        } else {
            error('修改失败');
        }
    }

    /**
     *撤销订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function revoke()
    {
        $db     = new JuheRechargeOrder();
        $revoke = $db->revoke($this->params);
        if ($revoke) {
            success('撤销成功');
        } else {
            error('撤销失败');
        }
    }

    /**
     *导出订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_order_note()
    {
        $db      = new JuheRechargeOrder();
        $join    = [
            ['coupon_send_note csn', 'csn.bid = jro.bid AND csn.relation_guid = jro.guid'],
        ];
        $db_user = new User();
        $map     = [
            ['owner_user_id', 'in', $db_user->getChildUserIdArray()]
        ];
        $data    = $db->field(
            [
                'type',
                'value',
                'code',
                'game_userid',
                'jro.mobile',
                'jro.third_billno',
                'jro.create_time',
                'jro.status',
                'jro.result',
                'jro.third_status',
                'jro.third_message',
            ]
        )->alias('jro')->join($join)->where($map)->order('jro.create_time', 'DESC')->append(['export_status_text', 'export_third_status_text']);
        $data    = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'code'                     => '券号',
            'value'                    => '面值',
            'game_userid'              => '充值帐号',
            'mobile'                   => '手机号',
            'third_billno'             => '第三方订单号',
            'create_time'              => '充值时间',
            'export_status_text'       => '提交状态',
            'result'                   => '提交结果',
            'export_third_status_text' => '充值状态',
            'third_message'            => '充值结果',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function order_note()
    {
        $db          = new JuheRechargeOrder();
        $db_user     = new User();
        $join        = [
            ['coupon_send_note csn', 'csn.bid = jro.bid AND csn.relation_guid = jro.guid'],
        ];
        $map         = [
            ['owner_user_id', 'in', $db_user->getChildUserIdArray()],
            ['jro.bid', '=', $this->get_bid()]
        ];
        $this->model = $db->field(
            [
                'jro.guid',
                'type',
                'value',
                'code',
                'game_userid',
                'jro.mobile',
                'jro.third_billno',
                'jro.create_time',
                'jro.status',
                'jro.result',
                'jro.third_status',
                'jro.third_message',
            ]
        )->alias('jro')->join($join)->where($map)->order(['jro.create_time' => 'DESC']);
        result($this->_list());
    }
}