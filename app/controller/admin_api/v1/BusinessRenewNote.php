<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use Exception;

class BusinessRenewNote extends BasicAdminApi
{
    /**
     * 我的代理
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\BusinessRenewNote();
        $join        = [
            ['business b', 'b.guid = brn.bid', 'LEFT'],  //商家信息
            ['business b1', 'b1.guid = b.parent_guid', 'LEFT'], //商家代理商信息
            ['business b2', 'b2.guid = brn.operator_bid', 'LEFT'], //操作员信息
        ];
        $field       = [
            'brn.*',
            'b.account'                                   => 'business_account',
            'b1.account'                                  => 'agent_account',
            'b2.account'                                  => 'operator_account',
            "CONCAT(b.business_name,'(',b.account,')')"   => 'business_info',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent_info',
            "CONCAT(b2.business_name,'(',b2.account,')')" => 'operator_info',
        ];
        $db_business = new BusinessModel();
        $map         = [
            ['b2.delete_time', 'null', null]
        ];
        if ($db_business->is_agent()) {
            $map[] = ['brn.operator_bid', '=', $this->get_bid()];
        }
        $this->model = $db->alias('brn')->where($map)->join($join)->field($field)->order(['brn.create_time' => 'DESC']);
        result($this->_list());
    }
}
