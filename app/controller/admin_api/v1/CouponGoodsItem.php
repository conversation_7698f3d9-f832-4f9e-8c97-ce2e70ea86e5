<?php

namespace app\controller\admin_api\v1;


use Exception;
use think\facade\Db;

class CouponGoodsItem extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params        = $this->params;
        $bid           = $this->get_bid();
        $category_guid = $params['category_guid'] ?? '';
        $coupon_guid   = $params['coupon_guid'];
        $db            = new \app\model\Goods();
        $field         = [
            'g.*',
            Db::raw("IFNULL(cgi.status,0 ) as status"),
        ];
        $join          = [
            ['coupon_goods_item cgi', "cgi.goods_guid = g.guid AND cgi.bid=g.bid AND cgi.bid='$bid' AND cgi.coupon_guid='$coupon_guid'", 'LEFT'],
        ];
        $map           = [
            ['g.bid', '=', $bid]
        ];
        if (tools()::is_empty_guid($category_guid) || empty($category_guid)) {
            unset($this->params['category_guid']);
        } else {
            $map[] = ['g.category_guid', '=', $category_guid];
        }
        unset($this->params['coupon_guid']);
        $this->model = $db->alias('g')->join($join)->field($field)->append(['pic_mini'])->where($map)->order(['status' => 'DESC', 'sort' => 'ASC', 'name' => 'ASC']);
        result($this->_list());
    }

    public function edit()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $goods_guid  = $params['guid'];
        $coupon_guid = $params['coupon_guid'];
        $db          = new \app\model\CouponGoodsItem();
        $status      = $params['status'];
        $map         = [
            ['bid', '=', $bid],
            ['goods_guid', '=', $goods_guid],
            ['coupon_guid', '=', $coupon_guid],
        ];
        $info        = $db->where($map)->findOrEmpty();
        if ($info->isEmpty()) {
            $data = [
                'guid'        => create_guid(),
                'bid'         => $bid,
                'goods_guid'  => $goods_guid,
                'coupon_guid' => $coupon_guid,
                'status'      => $status,
            ];
            $db->save($data);
            success('更新成功');

        } else {
            $update_data = [
                'status' => $status,
            ];
            $map[]       = ['guid', '=', $info['guid']];
            $db::update($update_data, $map);
            success('更新成功');
        }
    }

    public function get_goods_list()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $coupon_guid          = $params['coupon_guid'];
        $db_coupon_goods_item = new \app\model\CouponGoodsItem();
        $goods_list           = $db_coupon_goods_item->get_goods_list($bid, $coupon_guid);
        foreach ($goods_list as $key => $val) {
            $goods_list[$key]['pic'] = tools()::add_thumbnail_mini($val['pic']);
        }
        $result = $db_coupon_goods_item->get_goods_list_with_cat($bid, $goods_list);
        result($result);
    }

    public function update_sort()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_coupon_goods_item = new \app\model\CouponGoodsItem();
        $coupon_guid          = $params['coupon_guid'];
        $list                 = $params['goods_guid_list'];
        foreach ($list as $key => $value) {
            $map         = [
                ['bid', '=', $bid],
                ['goods_guid', '=', $value],
                ['coupon_guid', '=', $coupon_guid],
            ];
            $update_data = [
                'sort' => (int)$key + 1,
            ];
            $db_coupon_goods_item::update($update_data, $map);
        }
        result([], '更新成功');
    }
}
