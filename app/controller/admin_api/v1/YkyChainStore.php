<?php

namespace app\controller\admin_api\v1;

use app\model\YkyChainStore as YkyChainStoreModel;
use Exception;

class YkyChainStore extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyChainStoreModel();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db          = new YkyChainStoreModel();
        $this->model = $db;
        $db->edit($this->params);
        success('编辑成功');
    }
}