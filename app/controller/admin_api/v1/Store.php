<?php

namespace app\controller\admin_api\v1;

use Exception;

class Store extends BasicAdminApi
{
    /**
     *获取门店列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $this->model->where($map)->order(['status' => 'DESC', 'sort' => 'ASC', 'create_time' => 'DESC']);
        result($this->_list());
    }
}