<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use Exception;

class Agent extends BasicAdminApi
{
    /**
     * 我的代理
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my_agent()
    {
        $db          = new BusinessModel();
        $join        = [
            ['business b1', 'b.parent_guid = b1.guid'],
        ];
        $field       = [
            'b.*',
            'b.account',
            'b1.account'                                  => 'agent_account',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
        ];
        $map         = [
            ['b.type', '=', 2],
            ['b.parent_guid', '=', $this->get_bid()],
            ['b.wxapp_admin_id', '>', 0]
        ];
        $this->model = $db->alias('b')->where($map)->join($join)->field($field)->order(['b.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 代理商列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new BusinessModel();
        $join        = [
            ['business b1', 'b.parent_guid = b1.guid'],
            //['business b2', 'b1.parent_guid = b2.guid'],
        ];
        $field       = [
            'b.*',
            'b1.account'                                  => 'agent_account',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
        ];
        $map         = [
            ['b.type', '=', 2],
            ['b.wxapp_admin_id', '>', 0]
        ];
        $this->model = $db->alias('b')->join($join)->field($field)->where($map)->order(['b.wxapp_admin_id' => 'DESC']);
        result($this->_list());
    }

    /**
     * 代理商列表,用于转移代理商
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function agent_list()
    {
        $map    = [
            //           ['type', 'IN', [2, 3]],
            //           ['wxapp_admin_id', '>', 0]
        ];
        $params = $this->params;
        if (!empty($params['keyword'])) {
            if (ctype_alnum($params['keyword'])) {
                $map[] = ['account', 'like', '%' . $params['keyword'] . '%'];
            } else {
                $map[] = ['business_name', 'like', '%' . $params['keyword'] . '%'];
            }
            unset($this->params['keyword']);
        }
        $this->model = $this->model->where($map)->order(['id' => 'DESC']);
        //       $this->model = $this->model->where($map)->order(['wxapp_admin_id' => 'DESC']);
        $list = $this->_list();
        $data = [
            'code'  => 0,
            'msg'   => 'success',
            'data'  => $list['data'],
            'count' => $list['total'],
        ];
        return json($data);
    }

    protected function initialize()
    {
        $this->model = new BusinessModel();
        parent::initialize();
    }
}
