<?php

namespace app\controller\admin_api\v1;

use Exception;

class SmsReplyNote extends BasicAdminApi
{
    /**
     *所有短信发送记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db            = new \app\model\SmsReplyNote();
        $map           = [];
        $join          = [
            ['business b', 'srn.bid = b.guid'],
        ];
        $field         = [
            'srn.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
        ];
        $db_business   = new \app\model\Business();
        $business_type = $db_business->get_business_type($this->get_bid());
        if ($db_business->is_admin_type($business_type)) {
        } elseif ($db_business->is_business_type($business_type)) {
            $map[] = ['b.guid', '=', $this->get_bid()];
        } elseif ($db_business->is_agent_type($business_type)) {
            $map[] = ['b.parent_guid', '=', $this->get_bid()];
        } else {
            error('暂时不支持查询');
        }
        $this->model = $db->alias('srn')->join($join)->field($field)->where($map);
        result($this->_list());
    }
}