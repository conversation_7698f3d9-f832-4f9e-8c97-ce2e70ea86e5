<?php

namespace app\controller\admin_api\v1;

use app\model\GoodsOrder as GoodsOrderModel;
use app\model\AfterSaleNote as AfterSaleNoteModel;
use think\facade\Db;
use Exception;

class AfterSaleNote extends BasicAdminApi
{
    /**
     * 售后记录列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db = new GoodsOrderModel();
        // 先构建主查询基础
        $this->model = $db->alias('go')
            ->where([
                ['go.bid', '=', $this->get_bid()],
                ['go.after_sale_status', '<>', 0],
            ]);

        // 获取每个订单的最新售后记录ID
        $latestNotesSql = Db::name('after_sale_note')
            ->field('MAX(id) as max_id, order_guid, bid')
            ->where('bid', '=', $this->get_bid())
            ->group('order_guid, bid')
            ->buildSql();

        // 构建JOIN关系
        $join = [
            [$latestNotesSql . ' ln', 'ln.order_guid = go.guid AND ln.bid = go.bid'],
            ['after_sale_note asn', 'asn.id = ln.max_id', 'LEFT'],
            ['member m', 'go.member_guid = m.guid AND go.bid = m.bid', 'LEFT'],
        ];

        $field = [
            'go.guid',
            'go.bill_number' => 'bill_number',
            'go.mobile'      => 'mobile',
            'go.true_name'   => 'true_name',
            'go.after_sale_status',
            'asn.guid'       => 'asn_guid',
            'asn.create_type',
            'asn.content',
            'asn.create_time',
            'm.id'           => 'member_id',
            'm.name'         => 'nickname',
        ];

        $this->model->join($join)
            ->field($field)
            ->order(['asn.create_time' => 'DESC', 'go.id' => 'DESC']);
        result($this->_list());
    }

    /**
     * 售后记录详情
     * @access public
     * @return void
     * @throws Exception
     */
    public function detail()
    {
        $bid        = $this->get_bid();
        $params     = $this->params;
        $order_guid = $params['order_guid'];

        $db_after_sale_note = new AfterSaleNoteModel();
        $map                = [
            ['bid', '=', $bid],
            ['order_guid', '=', $order_guid],
        ];

        $list = $db_after_sale_note->where($map)
            ->order('create_time', 'desc')
            ->select();

        // 格式化创建者类型
        foreach ($list as &$item) {
            $item['create_type_text'] = $item['create_type'] == 1 ? '客户' : '商家';
        }
        // 获取订单售后状态
        $db_order          = new GoodsOrderModel();
        $order_detail      = $db_order->get_order_detail($bid, $order_guid);
        $after_sale_status = $order_detail['after_sale_status'];
        result(['list' => $list, 'after_sale_status' => $after_sale_status, 'order_detail' => $order_detail]);
    }

    /**
     * 回复售后记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function reply()
    {
        $params = $this->params;

        $data = [
            'bid'        => $this->get_bid(),
            'order_guid' => $params['order_guid'],
            'content'    => $params['content'],
            'step'       => 2, // 沟通中
        ];

        $db_after_sale_note = new AfterSaleNoteModel();
        $db_after_sale_note->add($data);
        success('回复成功');
    }

    /**
     * 完成售后工单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function complete()
    {
        $params             = $this->params;
        $data               = [
            'order_guid' => $params['order_guid'],
            'content'    => "完成工单：{$params['content']}",
            'step'       => 3, // 完结工单
        ];
        $db_after_sale_note = new AfterSaleNoteModel();
        $db_after_sale_note->add($data);
        success('完成成功');
    }
}
