<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\BusinessRechargeNote as BusinessRechargeNoteModel;
use Exception;

class BusinessRechargeNote extends BasicAdminApi
{
    /**
     * 我的代理
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new BusinessRechargeNoteModel();
        $join        = [
            ['business b', 'b.guid = brn.bid', 'LEFT'],  //商家信息
            ['business b1', 'b1.guid = b.parent_guid', 'LEFT'], //商家代理商信息
            ['business b2', 'b2.guid = brn.recharge_bid', 'LEFT'], //操作员信息
        ];
        $field       = [
            'brn.*',
            'b.account'                                   => 'business_account',
            'b1.account'                                  => 'agent_account',
            'b2.account'                                  => 'operator_account',
            "CONCAT(b.business_name,'(',b.account,')')"   => 'business_info',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent_info',
            "CONCAT(b2.business_name,'(',b2.account,')')" => 'operator_info',
        ];
        $db_business = new BusinessModel();
        $map         = [];
        if ($db_business->is_agent()) {
            $map[] = ['brn.recharge_bid', '=', $this->get_bid()];
        }
        $this->model = $db->alias('brn')->where($map)->join($join)->field($field)->order(['brn.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *在线充值商家余额
     * @access public
     * @return void
     * @throws Exception
     */
    public function online_recharge_money()
    {
        $params        = $this->params;
        $recharge_bid  = $this->get_bid();
        $money         = $params['money'];
        $bid           = config('app.recharge_pay_parameter_business_guid');
        $out_trade_no  = tools()::get_bill_number();
        $int_total_fee = tools()::nc_price_yuan2fen($money);
        $order_guid    = create_guid();
        $note_guid     = create_guid();
        $recharge_info = [
            'out_trade_no' => $out_trade_no,
            'recharge_bid' => $recharge_bid,
            'total_fee'    => $int_total_fee,
            'money'        => $money,
            'note_guid'    => $note_guid,
        ];
        cache($order_guid, $recharge_info, 3600);
        $pay_url = (string)url('member/pay/submit', ['bid' => $bid, 'order_guid' => $order_guid, 'type' => 'recharge_business_money'], false, true);
        $pay_url = tools()::replace_readonly_to_www($pay_url);
        $data    = [
            'url'        => $pay_url,
            'socket_uid' => $out_trade_no
        ];
        result($data);
    }
}
