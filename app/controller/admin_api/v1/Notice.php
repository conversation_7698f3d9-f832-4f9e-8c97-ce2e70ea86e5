<?php

namespace app\controller\admin_api\v1;


use app\model\NoticeViewNote;
use Exception;

class Notice extends BasicAdminApi
{

    /**
     *获取公告列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_notice_list()
    {
        $db = new \app\model\Notice();
        return $db->get_notice_list();
    }

    /**
     *订单详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params              = $this->params;
        $guid                = $params['guid'];
        $db_notice           = new \app\model\Notice();
        $db_notice_view_note = new NoticeViewNote();
        $map                 = [
            ['guid', '=', $guid]
        ];
        $db_notice::where($map)->setInc('view_times');
        $insert_data = [
            'notice_guid' => $guid,
            'bid'         => $this->get_bid(),
            'sid'         => $this->get_sid(),
            'user_guid'   => $this->get_user_guid(),
            'user_id'     => $this->get_user_id(),
        ];
        $db_notice_view_note->save($insert_data);
        $notice_info            = $db_notice->where($map)->findOrFail();
        $notice_info['content'] = tools()::add_rich_img_class($notice_info['content']);
        result($notice_info);
    }

    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        result($this->_list());
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function version_item_list()
    {
        $params = $this->params;
        $db     = new \app\model\Version();
        $map    = [
            ['type', '=', 1]
        ];
        $list   = $db->where($map)->field(['name', 'guid' => 'version_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid   = $params['guid'] ?? '';
        $rule   = [];
        if ($guid) {
            //本规则的商品默认选中
            $db   = new \app\model\Notice();
            $map  = [
                ['guid', '=', $guid],
            ];
            $rule = $db->where($map)->field(['version_guid_json'])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule['version_guid_json'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['version_guid'], $rule['version_guid_json'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }
}
