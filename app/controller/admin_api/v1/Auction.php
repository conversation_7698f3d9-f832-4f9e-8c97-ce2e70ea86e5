<?php

namespace app\controller\admin_api\v1;

use app\model\AuctionActivity;
use app\model\AuctionGoods;
use app\model\AuctionGoodsOrder;
use app\model\GoodsCategoryArticle;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;

class Auction extends BasicAdminApi
{
    /**
     * 拍卖活动列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity()
    {
        $db     = new AuctionActivity();
        $join   = [
            ['goods_category gc', " aa.goods_category_guid = gc.guid AND aa.bid = gc.bid"],
        ];
        $field  = [
            'CONCAT(gc.name,number,DATE_FORMAT(aa.create_time,"-%m%d"))' => 'goods_category_with_date',
            'aa.*',
        ];
        $map    = [
            ['aa.bid', '=', $this->get_bid()]
        ];
        $params = $this->params;
        if (isset($params['type'])) {
            $type     = $params['type'];
            $now_time = format_timestamp();
            switch ($type) {
                case 1:
                    $map[] = ['begin_time', '>', $now_time];
                    break;
                case 2:
                    $map[] = ['begin_time', '<', $now_time];
                    $map[] = ['end_time', '>', $now_time];
                    break;
                case 3:
                    $map[] = ['end_time', '<', $now_time];
                    break;
                default:
                    break;
            }
            unset($this->params['type']);
        }
        $this->model = $db->alias('aa')->join($join)->field($field)->where($map);
        result($this->_list());
    }

    /**
     * 编辑活动
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_edit()
    {
        $db = new AuctionActivity();
        return $db->edit($this->params);
    }

    /**
     * 拍卖商品订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_goods_order()
    {
        $db    = new AuctionGoodsOrder();
        $join  = [
            ['member m', " ago.member_guid = m.guid AND ago.bid = m.bid"],
            ['auction_goods ag', "ago.goods_guid = ag.guid AND ago.bid = ag.bid"],
        ];
        $field = [
            'ag.name',
            'm.card_id',
            'm.mobile',
            'ago.*',
        ];
        $map   = [
            ['ago.bid', '=', $this->get_bid()]
        ];
        if (!empty($this->params['goods_guid'])) {
            $map[] = ['goods_guid', '=', $this->params['goods_guid']];
        }
        $this->model = $db->alias('ago')->join($join)->field($field)->order(['offer_price' => 'DESC'])->where($map);
        result($this->_list());
    }

    /**
     * 选择
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function choose()
    {
        $db          = new AuctionGoodsOrder();
        $guid        = $this->params['guid'];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid]
        ];
        $update_data = ['status' => 1];
        $db::update($update_data, $map);
        success('更新成功');
    }

    /**
     * 出货
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_out_goods()
    {
        $db          = new AuctionGoodsOrder();
        $params      = $this->params;
        $guid        = $params['guid'];
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid]
        ];
        $update_data = ['express' => $params['express'], 'express_no' => $params['express_no']];
        $db::update($update_data, $map);
        success('发货成功');
    }

    /**
     * 活动商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_goods()
    {
        $db                     = new AuctionGoods();
        $bid                    = $this->get_bid();
        $params                 = $this->params;
        $activity_guid          = $params['activity_guid'];
        $db_auction_goods_order = new AuctionGoodsOrder();
        $map                    = [
            ['bid', '=', $bid],
            ['status', '=', 1]
        ];
        $field                  = [
            'bid',
            'goods_guid',
            'MAX(offer_price)' => 'max_offer_price',
            'member_guid',
            'create_time'      => 'offer_time'
        ];
        $subSql                 = $db_auction_goods_order->field($field)->where($map)->group('goods_guid,member_guid')->buildSql();
        $join                   = [
            [[$subSql => 'ago'], "ago.goods_guid = ag.guid AND ago.bid = ag.bid", 'LEFT'],
            ['member m', "ago.member_guid = m.guid AND ago.bid = m.bid", 'LEFT'],
        ];
        $field                  = [
            'ag.guid',
            'ag.name',
            'ag.no',
            'ag.number',
            'ag.amount',
            'ag.starting_price',
            'ago.max_offer_price',
            'ago.offer_time',
            'm.mobile',
        ];
        $map                    = [
            ['ag.bid', '=', $bid],
            ['activity_guid', '=', $activity_guid]
        ];
        $order                  = ['number' => 'ASC'];
        if (!empty($params['export'])) {
            //           $header = [
            //               'number'          => '序号',
            //               'name'            => '名称',
            //               'starting_price'  => '参考价格',
            //               'max_offer_price' => '最高出价',
            //               'mobile'          => '手机号'
            //           ];
            $file                = new Excel();
            $data                = $db->alias('ag')->join($join)->field($field)->where($map)->order($order)->select();
            $db_auction_activity = new AuctionActivity();
            $map                 = [
                ['bid', '=', $bid],
                ['guid', '=', $activity_guid]
            ];
            $file_name           = $db_auction_activity->where($map)->value('file_url');
            $file_name           = tools()::web_to_path($file_name);
            $original_data       = $file->load($file_name)->excelToArray();
            $header              = array_shift($original_data);
            array_push($header, "最高出价", "手机号");//添加元素
            $i = 0;
            foreach ($original_data as $key => $val) {
                ++$i;
                $original_data[$key] = array_merge($original_data[$key], $this->get_offer_info($i, $data));
            }
            $file->arrayToExcel($header, $original_data);
        }
        $this->model = $db->alias('ag')->join($join)->where($map)->field($field)->order($order);
        result($this->_list());
    }

    /**
     * 获取出价信息
     * @access protected
     * @param string $number
     * @param array $data
     * @return array
     * @throws Exception
     */
    protected function get_offer_info($number, $data)
    {
        foreach ($data as $key => $val) {
            if ($val['number'] == $number) {
                return [$val['max_offer_price'], $val['mobile']];
            }
        }
        return ['', ''];
    }

    /**
     * 获取图文信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function get_article()
    {
        $params                    = $this->params;
        $auction_activity_guid     = $params['guid'];
        $db_auction_activity       = new AuctionActivity();
        $map                       = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $auction_activity_guid],
        ];
        $activity_info             = $db_auction_activity->where($map)->field(['goods_category_guid', 'article_guid_json'])->find();
        $article_guid_array        = $activity_info['article_guid_json'];
        $db_goods_category_article = new GoodsCategoryArticle();
        $map                       = [
            ['bid', '=', $this->get_bid()],
            ['goods_category_guid', '=', $activity_info['goods_category_guid']],
        ];
        $article_list              = $db_goods_category_article->field(['guid', 'title'])->order(['sort' => 'DESC'])->where($map)->select();
        $data                      = [];
        $article_guid_array        = $article_guid_array ?: [];
        //先把已选择的 插入
        foreach ($article_guid_array as $article_guid) {
            foreach ($article_list as $key => $val) {
                if ($article_guid == $val['guid']) {
                    $push_data = [
                        'name'     => $val['title'],
                        'value'    => $val['guid'],
                        'selected' => 'selected',
                    ];
                    $data[]    = $push_data;
                }
            }
        }
        //再插入没有选中的
        foreach ($article_list as $key => $val) {
            if (!in_array($val['guid'], $article_guid_array)) {
                $push_data = [
                    'name'     => $val['title'],
                    'value'    => $val['guid'],
                    'selected' => '',
                ];
                $data[]    = $push_data;
            }
        }
        result($data);
    }

    /**
     * 导入
     * @access public
     * @return void
     * @throws Exception
     */
    public function import()
    {
        $params              = $this->params;
        $file                = new Excel();
        $file_name           = tools()::web_to_path($params['file']);
        $allowField          = [
            'name'           => '名称',
            'no'             => '编号',
            'brand'          => '品牌',
            'type'           => '类型',
            'model'          => '型号',
            'memory'         => '内存',
            'colour'         => '颜色',
            'standard'       => '制式',
            'carrier'        => '运营商',
            'condition'      => '成色',
            'amount'         => '数量',
            'starting_price' => '参考价格',
            'remark'         => '备注',
        ];
        $arr                 = $file->load($file_name)->excelToArray($allowField);
        $goods_category_guid = $params['goods_category_guid'];
        $bid                 = $this->get_bid();
        if (empty($arr)) {
            error('没有要导入的数据!');
        }
        $amount = count($arr);
        // 启动事务
        Db::startTrans();
        try {
            $db            = new AuctionActivity();
            $map           = [
                ['bid', '=', $bid],
                ['goods_category_guid', '=', $goods_category_guid],
            ];
            $number        = $db->where($map)->whereDay('create_time')->count();
            $activity_guid = create_guid();
            $insert_data   = [
                'guid'                => $activity_guid,
                'bid'                 => $bid,
                'number'              => $number + 1,
                'goods_category_guid' => $goods_category_guid,
                'file_url'            => $params['file'],
                'title'               => $params['title'],
                'description'         => request()->param('description', '', null),
                'status'              => $params['status'],
                'amount'              => $amount,
                'begin_time'          => $params['begin_time'],
                'end_time'            => $params['end_time'],
            ];
            $db->save($insert_data);
            $insert_data = [];
            $now_time    = format_timestamp();
            $number      = 0;
            foreach ($arr as $key => $val) {
                $val['guid']          = create_guid();
                $val['bid']           = $bid;
                $val['number']        = ++$number;
                $val['activity_guid'] = $activity_guid;
                $val['create_time']   = $now_time;
                $val['update_time']   = $now_time;
                $insert_data[]        = $val;
            }
            Db::name('auction_goods')->data($insert_data)->limit(100)->insertAll();
            // 提交事务
            Db::commit();
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            error('导入失败' . $e->getMessage());
        }
        success('导入成功' . $amount . '条数据');
    }
}
