<?php

namespace app\controller\admin_api\v1;

use app\model\QuestionnaireEpidemicSituation as QuestionnaireEpidemicSituationModel;
use app\common\tools\Excel;
use Exception;

class QuestionnaireEpidemicSituation extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    public function export()
    {
        $params = $this->params;
        $map    = [['bid', '=', $this->get_bid()]];
        $db     = new QuestionnaireEpidemicSituationModel();
        $data   = $db->where($map)->order(['id' => 'DESC']);
        $data   = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'id'              => '序号',
            'name'            => '姓名',
            'id_card'         => '身份证',
            'mobile'          => '手机号码',
            'address'         => '现住址',
            'from_city_value' => '从何处返惠',
            'come_back_time'  => '返惠时间',
            'temperature'     => '体温',
            'create_time'     => '报备时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $db     = new QuestionnaireEpidemicSituationModel();
        $params = $this->params;
        $bid    = $this->get_bid();
        $map    = [['bid', '=', $bid], ['guid', '=', $params['guid']]];
        $info   = $db->where($map)->find();
        result($info);
    }
}