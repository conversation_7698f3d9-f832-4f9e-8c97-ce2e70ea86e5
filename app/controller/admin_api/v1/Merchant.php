<?php


namespace app\controller\admin_api\v1;

class Merchant extends BasicAdminApi
{
    public function change_rate()
    {
        $params = $this->params;
        wr_log(json_encode($params) . '请求成功', 1);
        $mch_id     = $params['mch_id'];
        $sub_mch_id = $params['sub_mch_id'];
        $rate       = $params['rate'];
        if ($rate < 0.2 || $rate > 0.59) {
            $msg = '子商户号:' . $sub_mch_id . '费率修改失败:费率需0.2-0.59之间';
            wr_log($msg, 1);
            error($msg);
        }
        $config = ['merchant_code' => $mch_id];
        //       $sub    = new \OpenApi\SubMerchant($config);
        //       $result = $sub->crate_benchmarking($sub_mch_id, $rate);
        wr_log(json_encode($params) . '修改成功', 1);
        success('修改成功');
    }
}