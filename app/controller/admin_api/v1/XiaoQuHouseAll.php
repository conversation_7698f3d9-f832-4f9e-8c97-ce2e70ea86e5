<?php

namespace app\controller\admin_api\v1;

use app\model\XiaoQuHouseAll as XiaoQuHouseAllModel;

class XiaoQuHouseAll extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new XiaoQuHouseAllModel();
        $map         = [['bind_status', '=', 1]];
        $this->model = $this->model->where($map)->append(['user_area', 'user_sex', 'user_age'])->order(['id' => 'DESC']);
        result($this->_list());
    }
}
