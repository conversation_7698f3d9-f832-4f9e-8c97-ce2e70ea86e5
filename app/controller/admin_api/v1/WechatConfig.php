<?php

namespace app\controller\admin_api\v1;

use app\model\WechatConfig as WechatConfigModel;
use app\model\WechatFuncInfo;
use Exception;

class WechatConfig extends BasicAdminApi
{
    public function categories_list()
    {
        $appid    = $this->params['appid'];
        $instance = weixin($appid)::WeMiniAccount();
        $result   = $instance->getCategory();
        result($result['categories']);
    }

    public function categories_detail()
    {
        $id        = $this->params['id'];
        $appid     = 'wx0dcf9a18357b9b57';
        $cache_key = $appid . ':' . __FUNCTION__;
        if (!$categories = cache($cache_key)) {
            $instance   = weixin($appid)::WeMiniAccount();
            $result     = $instance->getAllCategories();
            $categories = $result['categories_list']['categories'];
            cache($cache_key, $categories, 600);
        }
        foreach ($categories as $key => $val) {
            $current_id = $val['id'];
            if ($id == $current_id) {
                result($val);
            }
        }
        error('类目不存在');
    }

    public function del_categories()
    {
        $params    = $this->params;
        $first_id  = $params['first'];
        $second_id = $params['second'];
        $appid     = $params['appid'];
        $instance  = weixin($appid)::WeMiniAccount();
        $result    = $instance->delCategroy($first_id, $second_id);
        success('类目删除成功');
    }

    public function add_categories()
    {
        $params         = $this->params;
        $sensitive_type = $params['sensitive_type']; //0不敏感 1敏感
        $first_id       = $params['first_id'];
        $second_id      = $params['second_id'];
        $appid          = $params['appid'];
        $categories     = ['first' => (int)$first_id, 'second' => (int)$second_id];
        if ($sensitive_type == 1) {
            //敏感类目
            $media_instance             = weixin($appid)::WeChatMedia();
            $result                     = $media_instance->add(tools()::web_url_to_local_absolute_path($params['certicates_val']));
            $certicates                 = [];
            $certicates['key']          = $params['certicates_key'];
            $certicates['value']        = $result['media_id'];
            $certicates['is_permanent'] = (int)$params['is_permanent'];
            if ($params['is_permanent'] == 0) {
                $certicates['expire_time'] = strtotime($params['expire_time']);
            }
            $categories['certicates'][] = $certicates;
        }
        $instance = weixin($appid)::WeMiniAccount();
        $result   = $instance->addCategory([$categories]);
        success('添加成功');
    }

    public function func_info()
    {
        $authorizer_appid = $this->params['appid'];
        $db_wechat_config = new  WechatConfigModel();
        $component_appid  = weixin()::get_component_appid();
        $map              = [
            ['component_appid', '=', $component_appid],
            ['authorizer_appid', '=', $authorizer_appid]
        ];
        $info             = $db_wechat_config->where($map)->find();
        $func_id_list     = explode(',', $info['func_info']);
        $map              = [
            ['type', '=', $info['type']]
        ];
        $db               = new WechatFuncInfo();
        $auth_list        = $db->where($map)->order(['func_id' => 'ASC'])->select();
        foreach ($auth_list as $key => $val) {
            $auth_list[$key]['status'] = (int)(in_array($val['func_id'], $func_id_list));
        }
        result($auth_list);
    }

    /**
     * 获取授权列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $this->model     = new WechatConfigModel();
        $field           = [
            'id',
            'authorizer_appid',
            'principal_name',
            'user_name',
            'nick_name',
            'alias',
            'mch_id',
            'type',
            'verify_type_info',
            'service_type_info',
            'qrcode_url',
            'tags',
            'status',
            'auto_commit_trial_version',
            'create_time',
            'update_time',
        ];
        $component_appid = weixin()::get_component_appid();
        $map             = [['component_appid', '=', $component_appid]];
        $this->model     = $this->model->where($map)->field($field)->order(['type' => 'DESC', 'id' => 'DESC']);
        result($this->_list());
    }

    /**
     * @access public
     * @return void
     * @throws Exception
     */
    public function edit()
    {
        $db               = new WechatConfigModel();
        $params           = $this->params;
        $authorizer_appid = $params['authorizer_appid'];
        $component_appid  = weixin()::get_component_appid();
        $map              = [
            ['component_appid', '=', $component_appid],
            ['authorizer_appid', '=', $authorizer_appid]
        ];
        $update_data      = [];

        if (isset($params['tags'])) {
            $update_data['tags'] = $params['tags'];
        }
        if (isset($params['auto_commit_trial_version'])) {
            $update_data['auto_commit_trial_version'] = $params['auto_commit_trial_version'];
        }
        if (!empty($update_data)) {
            $db::update($update_data, $map);
        }
        success('更新成功');
    }

    public function refresh_authorizer_info()
    {
        $authorizer_appid = $this->params['authorizer_appid'];
        $db_wechat_config = new WechatConfigModel();
        $db_wechat_config->refresh_authorizer_info($authorizer_appid);
        success('刷新成功');
    }
}
