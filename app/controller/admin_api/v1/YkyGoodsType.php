<?php

namespace app\controller\admin_api\v1;

use app\model\YkyGoodsType as YkyGoodsTypeModel;
use Exception;

class YkyGoodsType extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyGoodsTypeModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}