<?php

namespace app\controller\admin_api\v1;

use app\model\YkyRecommendRewardNote as YkyRecommendRewardNoteModel;
use Exception;

class YkyRecommendRewardNote extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyRecommendRewardNoteModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['rrn.bid', '=', $bid],
        ];
        $join        = [
            ['yky_recommend_reward_rule rrr', 'rrr.bid=rrn.bid AND rrr.guid=rrn.rule_guid'],
            ['yky_user yu', 'yu.bid=rrn.bid AND rrn.user_account=yu.user_account', 'LEFT']
        ];
        $field       = [
            'rrn.*',
            'rrr.title',
            'yu.store_name'
        ];
        $db_user     = new \app\model\User();
        if (!$db_user->is_admin_user()) {
            //10000 工号以外的用户只能查看自己的数据
            $map[] = ['rrn.user_account', 'IN', $db_user->getChildUserAccountArray()];
        }
        $this->model = $db->alias('rrn')->field($field)->join($join)->where($map)->order(['rrn.create_time' => 'DESC']);
        result($this->_list());
    }

    public function report()
    {
        $db          = new YkyRecommendRewardNoteModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['rrn.bid', '=', $bid],
            ['rrn.status', '=', 1],
        ];
        $join        = [
            ['yky_chain_store ycs', 'ycs.bid=rrn.bid AND rrn.chain_store_guid=ycs.guid', 'LEFT']
        ];
        $field       = [
            'ycs.store_name',
            'SUM(rrn.reward_money)' => 'total_reward_money'
        ];
        $db_user     = new \app\model\User();
        if (!$db_user->is_admin_user()) {
            //10000 工号以外的用户只能查看自己的数据
            $map[] = ['rrn.user_account', 'IN', $db_user->getChildUserAccountArray()];
        }
        $this->model = $db->alias('rrn')->join($join)->field($field)->where($map)->group(['ycs.store_name'])->order(['total_reward_money' => 'DESC']);
        result($this->_list());
    }
}