<?php

namespace app\controller\admin_api\v1;

use app\model\YkyConsumeNote;
use app\model\YkyCouponSendNote;
use app\model\YkyMemberAllPointNote;
use app\model\YkyMemberAllValueNote;
use app\model\YkyMemberValueNote;
use app\model\YkyPointValueExchangeOrder;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;
use Throwable;

class Yikayi extends BasicAdminApi
{
    /**
     * 同步数据
     * @access public
     * @return void
     * @throws Exception
     */
    public function sync()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $action   = $params['action'];
        $job_data = ['bid' => $bid];
        job()->set_job_name('SyncYikayiNote@' . $action)->push_job($job_data); //同步执行一次
        success('同步任务提交成功,稍后刷新查看');
    }

    /**
     * 储值记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function value_note()
    {
        $db  = new YkyMemberAllValueNote();
        $map = [
            ['bid', '=', $this->get_bid()],
        ];
        if (!empty($this->params['start_time'])) {
            $map[] = ['operate_time', '>=', $this->params['start_time']];
            unset($this->params['start_time']);
        }
        if (!empty($this->params['end_time'])) {
            $map[] = ['operate_time', '<', $this->params['end_time']];
            unset($this->params['end_time']);
        }
        $this->model = $db->where($map)->order(['operate_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 积分记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function point_note()
    {
        $db          = new YkyMemberAllPointNote();
        $this->model = $db;
        $map         = [
            ['bid', '=', $this->get_bid()],
        ];
        $this->model = $db->where($map)->order(['operate_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 消费记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function consume_note_retry()
    {
        $db     = new YkyConsumeNote();
        $params = $this->params;
        $bid    = $this->get_bid();
        $guid   = $params['guid'];
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $data   = $db->where($map)->findOrFail();
        $status = $data['status'];
        if ($status != -1) {
            error('该订单不是处理失败的订单,无需处理!');
        }
        $update_data = [
            'status' => 0
        ];
        $db::update($update_data, $map);
        $db->retry($bid, $data);
    }

    /**
     * 发券记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function coupon_send_note_repair()
    {
        $db     = new YkyCouponSendNote();
        $params = $this->params;
        $bid    = $this->get_bid();
        $guid   = $params['guid'];
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $data   = $db->where($map)->findOrFail();
        $status = $data['status'];
        if ($status != 0) {
            error('该订单已经处理完毕,无需处理!');
        }
        $db->retry($bid, $data);
    }

    /**
     * 消费记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function consume_note_repair()
    {
        $db     = new YkyConsumeNote();
        $params = $this->params;
        $bid    = $this->get_bid();
        $guid   = $params['guid'];
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $data   = $db->where($map)->findOrFail();
        $status = $data['status'];
        if ($status != 0) {
            error('该订单已经处理完毕,无需处理!');
        }
        $db->retry($bid, $data);
    }

    /**
     * 发券记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function coupon_send_note()
    {
        $db          = new YkyCouponSendNote();
        $this->model = $db;
        $map         = [
            ['bid', '=', $this->get_bid()],
        ];
        if (!empty($this->params['start_time'])) {
            $map[] = ['operate_time', '>=', $this->params['start_time']];
            unset($this->params['start_time']);
        }
        if (!empty($this->params['end_time'])) {
            $map[] = ['operate_time', '<', $this->params['end_time']];
            unset($this->params['end_time']);
        }
        $this->model = $db->where($map)->order(['operate_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 消费记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function consume_note()
    {
        $db          = new YkyConsumeNote();
        $this->model = $db;
        $map         = [
            ['bid', '=', $this->get_bid()],
        ];
        if (!empty($this->params['start_time'])) {
            $map[] = ['operate_time', '>=', $this->params['start_time']];
            unset($this->params['start_time']);
        }
        if (!empty($this->params['end_time'])) {
            $map[] = ['operate_time', '<', $this->params['end_time']];
            unset($this->params['end_time']);
        }
        $this->model = $db->where($map)->order(['operate_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 导出奖励列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function export_reward_list()
    {
        $db    = new YkyConsumeNote();
        $join  = [
            ['yky_chain_store ycs', "ycn.chain_store_guid = ycs.guid AND ycn.bid = ycs.bid"],
            ['yky_member_value_note ymvn', "ycn.guid = ymvn.relation_guid AND ycn.bid = ymvn.bid AND ymvn.way=1 AND ymvn.status=1", 'LEFT'],
            ['yky_member_value_note ymvn2', "ycn.guid = ymvn2.relation_guid AND ycn.bid = ymvn2.bid AND ymvn2.way=2 AND ymvn2.status=1", 'LEFT'],
        ];
        $field = [
            'ycn.bill_number'   => 'bill_number',
            'ycn.way_desc'      => 'way_desc',
            'ycn.card_id'       => 'card_id',
            'ycs.store_name'    => 'store_name',
            'ycn.total_money'   => 'total_money',
            'ycn.total_paid'    => 'total_paid',
            'ycn.paid_money'    => 'paid_money',
            'ycn.paid_value'    => 'paid_value',
            'ycn.paid_point'    => 'paid_point',
            'ycn.paid_coupon'   => 'paid_coupon',
            'ycn.paid_other'    => 'paid_other',
            'ycn.paid_thirdpay' => 'paid_thirdpay',
            Db::raw("CONVERT(ycn.paid_thirdpay*0.0038,DECIMAL(18,2)) AS charge"),
            Db::raw("IFNULL(ymvn.`value`,0) AS member_reward"),
            Db::raw("IFNULL(ymvn2.`value`,0) AS business_reward"),
            'ycs.rate'          => 'rate',
            Db::raw("CONVERT(ycn.paid_value*(ycs.rate+0.01),DECIMAL(18,2)) AS platform_reward"),
            Db::raw("CONVERT(ycn.paid_thirdpay-CONVERT(ycn.paid_thirdpay*0.0038,DECIMAL(18,2))-IFNULL(ymvn.`value`,0)-	IFNULL(ymvn2.`value`,0)+ycn.paid_value-CONVERT(ycn.paid_value*(ycs.rate+0.01),DECIMAL(18,2)),DECIMAL(18,2)) AS really_money"),
            'ycn.operate_time'  => 'operate_time'
        ];
        $map   = [
            ['ycn.bid', '=', '3b788180-658e-9519-bf17-2793bffc3697'],
            ['ycn.way', '>', 0]
        ];
        $data  = $db->alias('ycn')->order('ycn.operate_time', 'DESC')->join($join)->where($map)->field($field);
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'bill_number'     => '单号',
            'way_desc'        => '类型',
            'card_id'         => '卡号',
            'store_name'      => '消费门店',
            'total_money'     => '应付金额',
            'total_paid'      => '实付金额',
            'paid_money'      => '现金支付',
            'paid_value'      => '储值支付',
            'paid_point'      => '积分支付',
            'paid_coupon'     => '券支付',
            'paid_other'      => '其他支付',
            'paid_thirdpay'   => '移动支付',
            'charge'          => '手续费',
            'member_reward'   => '会员奖励',
            'business_reward' => '商家奖励',
            'rate'            => '比例',
            'platform_reward' => '平台收益',
            'really_money'    => '回款金额',
            'operate_time'    => '消费时间'
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     * 奖励记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function reward_list()
    {
        $db          = new YkyConsumeNote();
        $join        = [
            ['yky_chain_store ycs', "ycn.chain_store_guid = ycs.guid AND ycn.bid = ycs.bid"],
            ['yky_member_value_note ymvn', "ycn.guid = ymvn.relation_guid AND ycn.bid = ymvn.bid AND ymvn.way=1 AND ymvn.status=1", 'LEFT'],
            ['yky_member_value_note ymvn2', "ycn.guid = ymvn2.relation_guid AND ycn.bid = ymvn2.bid AND ymvn2.way=2 AND ymvn2.status=1", 'LEFT'],
        ];
        $field       = [
            'ycn.bill_number'   => 'bill_number',
            'ycn.way_desc'      => 'way_desc',
            'ycn.card_id'       => 'card_id',
            'ycs.store_name'    => 'store_name',
            'ycn.total_money'   => 'total_money',
            'ycn.total_paid'    => 'total_paid',
            'ycn.paid_money'    => 'paid_money',
            'ycn.paid_value'    => 'paid_value',
            'ycn.paid_point'    => 'paid_point',
            'ycn.paid_coupon'   => 'paid_coupon',
            'ycn.paid_other'    => 'paid_other',
            'ycn.paid_thirdpay' => 'paid_thirdpay',
            Db::raw("CONVERT(ycn.paid_thirdpay*0.0038,DECIMAL(18,2)) AS charge"),
            Db::raw("IFNULL(ymvn.`value`,0) AS member_reward"),
            Db::raw("IFNULL(ymvn2.`value`,0) AS business_reward"),
            'ycs.rate'          => 'rate',
            Db::raw("CONVERT(ycn.paid_value*(ycs.rate+0.01),DECIMAL(18,2)) AS platform_reward"),
            Db::raw("CONVERT(ycn.paid_thirdpay-CONVERT(ycn.paid_thirdpay*0.0038,DECIMAL(18,2))-IFNULL(ymvn.`value`,0)-IFNULL(ymvn2.`value`,0)+ycn.paid_value-CONVERT(ycn.paid_value*(ycs.rate+0.01),DECIMAL(18,2)),DECIMAL(18,2)) AS really_money"),
            'ycn.operate_time'  => 'operate_time'
        ];
        $map         = [
            ['ycn.bid', '=', '3b788180-658e-9519-bf17-2793bffc3697'],
            ['ycn.way', '>', 0]
        ];
        $this->model = $db->alias('ycn')->join($join)->field($field)->where($map)->order(['ycn.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 会员充值记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function member_value_note()
    {
        $db          = new YkyMemberValueNote();
        $join        = [
            ['yky_consume_note ycn', "ymvn.relation_guid = ycn.guid AND ycn.bid = ymvn.bid"],
            ['yky_chain_store ycs', "ymvn.member_chain_store_guid = ycs.guid AND ycs.bid = ymvn.bid", 'LEFT'],
        ];
        $field       = [
            'ycn.store_name',
            'ycs.store_name' => 'member_store_name',
            'ymvn.*',
        ];
        $map         = [
            ['ymvn.bid', '=', $this->get_bid()],
        ];
        $this->model = $db->alias('ymvn')->join($join)->where($map)->field($field);
        result($this->_list());
    }

    /**
     * 导出会员充值记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function export_member_value_note()
    {
        $db    = new YkyMemberValueNote();
        $map   = [['ymvn.bid', '=', $this->get_bid()]];
        $join  = [
            ['yky_consume_note ycn', "ymvn.relation_guid = ycn.guid AND ycn.bid = ymvn.bid"],
            ['yky_consume_note ycn2', "ymvn.member_chain_store_guid = ycn2.guid AND ycn2.bid = ymvn.bid", 'LEFT'],
        ];
        $field = [
            'ycn.store_name',
            'ycn2.store_name' => 'member_store_name',
            'ymvn.*',
        ];
        $data  = $db->alias('ymvn')->order('ymvn.create_time', 'DESC')->join($join)->where($map)->field($field)->append(['status_export', 'way_export']);
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'way_export'        => '类型',
            'bill_number'       => '单据号',
            'card_id'           => '卡号',
            'store_name'        => '门店名称',
            'member_store_name' => '会员登记门店名称',
            'value'             => '金额',
            'memo'              => '备注',
            'message'           => '结果',
            'status_export'     => '状态',
            'create_time'       => '充值时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     * 积分储值兑换
     * @access public
     * @return void
     * @throws Exception
     */
    public function point_value_exchange_order()
    {
        $db          = new YkyPointValueExchangeOrder();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     * 积分储值兑换审核
     * @access public
     * @return void
     * @throws Exception|Throwable
     */
    public function point_value_exchange_order_examine()
    {
        $params = $this->params;
        $db     = new YkyPointValueExchangeOrder();
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $params['guid']],
        ];
        $order  = $db->where($map)->find();
        if (empty($order)) {
            error('订单不存在');
        }
        if ($order['examine_status'] !== 0) {
            error('只能审核待审核订单');
        }
        $update_data = [
            'examine_time'      => microsecond(),
            'examine_user_guid' => $this->get_user_guid()
        ];
        switch ($params['examine_status']) {
            case 1; //审核通过逻辑
                $job_data = [
                    'order_guid' => $params['guid'],
                    'bid'        => $this->get_bid()
                ];
                job()->set_job_name('Member@point_or_value_exchange')->push_job($job_data);
                $update_data = [
                    'examine_status' => 1
                ];
                break;
            case -1: //审核拒绝逻辑
                $update_data = [
                    'examine_status' => -1,
                    'result'         => $params['result'],
                ];
                break;
            default:
                error('传入的状态不正确');
        }
        $db::update($update_data, $map);
        success('操作成功!');
    }
}
