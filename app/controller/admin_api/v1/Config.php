<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\ParameterDefault;
use Exception;

class Config extends BasicAdminApi
{
    /**
     *配置列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $order       = [
            'create_time' => 'DESC',
            'group'       => 'DESC',
            'update_time' => 'DESC',
            'title'       => 'DESC',
            'sort'        => 'DESC',
        ];
        $this->model = $this->model->order($order);
        result($this->_list());
    }

    protected function initialize()
    {
        $this->model = new ParameterDefault();
        parent::initialize();
    }
}