<?php

namespace app\controller\admin_api\v1;


use app\model\MemberMoneyNote as MemberMoneyNoteModel;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;

class MemberMoneyNote extends BasicAdminApi
{

    public function build_export_query()
    {
        $db    = new MemberMoneyNoteModel();
        $join  = [
            ['member m', 'mmn.member_guid = m.guid AND mmn.bid = m.bid'],
            ['user u', 'mmn.operator_user_guid = u.guid AND mmn.bid = u.bid', 'LEFT'],
            ['store s', 'u.store_guid = s.guid AND u.bid = s.bid', 'LEFT'],
        ];
        $field = [
            'mmn.type',
            'mmn.way',
            'mmn.member_guid',
            'mmn.yky_member_guid',
            'mmn.money',
            'mmn.balance',
            'mmn.status',
            'mmn.create_time',
            'mmn.memo',
            'm.id'         => 'member_id',
            'm.card_id',
            'u.account'    => 'user_account',
            'u.name'       => 'user_name',
            'u.store_guid',
            's.brand_guid',
            's.store_name' => 'store_name',
            's.industry_guid',
        ];

        $db_user     = new \app\model\User();
        $map         = [
            ['mmn.bid', '=', $this->get_bid()],
            ['mmn.operator_user_guid', 'IN', $db_user->getChildUserGuidArray()],
        ];
        $this->model = $db->alias('mmn')->field($field)->order(['mmn.create_time' => 'DESC'])->join($join)->where($map);
        return $this->model;
    }

    public function export()
    {
        $data = $this->_select($this->build_export_query());
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $header = [
            'member_id'    => '会员编号',
            'user_account' => '操作工号',
            'user_name'    => '操作用户',
            'store_name'   => '操作门店',
            'money'        => '金额',
            'balance'      => '余额',
            'create_time'  => '操作时间',
            'memo'         => '备注',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db     = new MemberMoneyNoteModel();
        $params = $this->params;
        $join   = [
            ['member m', 'mmn.member_guid = m.guid AND mmn.bid = m.bid'],
            ['user u', 'mmn.operator_user_guid = u.guid AND mmn.bid = u.bid', 'LEFT'],
            ['store s', 'u.store_guid = s.guid AND u.bid = s.bid', 'LEFT'],
        ];
        $field  = [
            'mmn.type',
            'mmn.way',
            'mmn.member_guid',
            'mmn.yky_member_guid',
            'mmn.money',
            'mmn.balance',
            'mmn.status',
            'mmn.create_time',
            'mmn.memo',
            'm.id'      => 'member_id',
            'm.card_id',
            'u.account' => 'user_account',
            'u.name'    => 'user_name',
            'u.store_guid',
            's.brand_guid',
            's.industry_guid',
        ];

        $db_user     = new \app\model\User();
        $map         = [
            ['mmn.bid', '=', $this->get_bid()],
            ['mmn.operator_user_guid', 'IN', $db_user->getChildUserGuidArray()],
        ];
        $this->model = $db->alias('mmn')->order(['mmn.create_time' => 'DESC'])->join($join)->where($map);

        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'count(1)'                                                => 'count',
                'IFNULL(sum(mmn.money),0)'                                => 'sum',
                'SUM(case when mmn.money > 0  then mmn.money else 0 end)' => 'total_inc',
                'SUM(case when mmn.money < 0  then mmn.money else 0 end)' => 'total_dec'
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '合计 <b style="color: red">' . number_format($data['sum'], 2) . '</b>元';
            $total_text  = '累计充值 <b style="color: red">' . number_format($data['total_inc'], 2) . '</b>元';
            $total_text  .= ' ,累计扣除 <b style="color: red">' . number_format($data['total_dec'], 2) . '</b>元';
            success($total_text);
        }
        $this->model = $this->model->field($field);
        result($this->_list());
    }

    /**
     *充值记录统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function report()
    {
        $db          = new MemberMoneyNoteModel();
        $join        = [
            ['user u', 'mmn.operator_user_guid = u.guid AND mmn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'u.id',
            'u.account',
            'u.name',
            'SUM(mmn.money)' => 'total_money',
            Db::raw("SUM(CASE WHEN mmn.type = 1 THEN mmn.money ELSE 0 END)  as total_inc_money"),
            Db::raw("SUM(CASE WHEN mmn.type = -1 THEN mmn.money ELSE 0 END)  as total_dec_money"),
        ];
        $map         = [
            ['mmn.bid', '=', $this->get_bid()],
        ];
        $group       = ['u.account'];
        $this->model = $db->alias('mmn')->join($join)->field($field)->where($map)->group($group)->order(['total_money']);
        result($this->_list());
    }
}
