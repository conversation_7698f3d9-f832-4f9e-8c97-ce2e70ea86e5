<?php

namespace app\controller\admin_api\v1;

use Exception;

class ApiLog extends BasicAdminApi
{
    /**
     * 日志列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map    = [];
        $order  = ['request_time' => 'DESC'];
        $params = $this->params;
        if (empty($params['request_time'])) {
            $begin_date = date('Y-m-d', strtotime('-7 days'));
            $map        = [['request_time', '> time', $begin_date]];
        }
        $this->model = $this->model->where($map)->order($order);
        result($this->_list());
    }
}
