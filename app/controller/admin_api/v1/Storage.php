<?php

namespace app\controller\admin_api\v1;

use app\model\Storage as StorageModel;
use app\model\SystemConfig as SystemConfigModel;
use Exception;

class Storage extends BasicAdminApi
{
    /**
     *获取文件存储配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_config()
    {
        $db          = new StorageModel();
        $result      = $db->field(['config', 'type'])->select();
        $config_list = [];
        foreach ($result as $key => $val) {
            $config_list[$val['type']] = $val['config'];
        }
        $storage_type = get_system_config('storage_type');
        $data         = [
            'storage_type' => $storage_type,
            'config_list'  => $config_list
        ];
        result($data);
    }

    /**
     *编辑文件存储
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params       = $this->params;
        $storage_type = $params['storage_type'];
        $config       = [];
        foreach ($params as $key => $val) {
            if (tools()::start_with($key, $storage_type . '-')) {
                $config[str_replace($storage_type . '-', '', $key)] = $val;
            }
        }
        if (!empty($config)) {
            $db    = new StorageModel();
            $map   = [['type', '=', $storage_type]];
            $count = $db->where($map)->count();
            if (!$count) {
                $insert_data = [
                    'guid'   => create_guid(),
                    'type'   => $storage_type,
                    'config' => $config
                ];
                $db->save($insert_data);
            } else {
                $update_data = ['config' => $config];
                $db::update($update_data, $map);
            }
            $db_system_config = new SystemConfigModel();
            $map              = [['key_name', '=', 'storage_type']];
            $update_data      = ['value' => $storage_type];
            $db_system_config::update($update_data, $map);
        }
        result($config);
    }
}