<?php

namespace app\controller\admin_api\v1;


use app\model\MemberMoneyNote;
use app\model\MemberPointNote;
use app\common\service\TokenService;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class Member extends BasicAdminApi
{
    public function info()
    {

    }

    /**
     *会员充值
     * @access public
     * @return void
     * @throws Exception
     */
    public function deduct_money()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_member_money_note = new MemberMoneyNote();
        $type                 = -1;
        $member_guid          = $params['member_guid'];
        //校验 会员状态
        $db_member = new \app\model\Member();
        $map       = ['bid' => $bid, 'guid' => $member_guid];
        $db_member->verify_member_status($map);
        $money = $params['deduct_money'];
        $data  = [
            'bid'         => $bid,
            'member_guid' => $member_guid,
            'way'         => 6,
            'type'        => $type,
            'money'       => $money,
            'memo'        => '[后台操作-操作员]',
        ];
        $db_member_money_note->recharge_money($data);
        success('操作成功');
    }


    /**
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws DataNotFoundException
     * @throws Exception
     */
    public function send_sms()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $content     = $params['content'];
        $db_member   = new \app\model\Member();
        $i           = 0;
        $map         = [
            ['bid', '=', $bid],
            ['mobile', 'not null', null],
        ];
        $mobile_list = $db_member->where($map)->column('mobile');
        $db_business = new \app\model\Business();
        $map         = [['guid', '=', $bid]];
        $sms_num     = (int)$db_business->where($map)->value('sms_num');
        if ($sms_num < $mobile_list) {
            error('可用短信不足,请充值后再发送');
        }
        foreach ($mobile_list as $mobile) {
            if (tools()::is_mobile($mobile)) {
                send_sms($content, $mobile, $bid);
                $i++;
            }
        }
        success('短信发送任务提交成功,共' . $i . '个会员,发送结果请以短信发送记录为准!');
    }

    /**
     * @throws Exception
     */
    public function detail()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $member_guid = $params['guid'] ?? null;
        $token       = $params['token'] ?? null;
        if ($token) {
            //存在token 则JWT解密
            $jwt = TokenService::verify($token);
            if ($bid != $jwt['bid']) {
                error('bid校验失败');
            }
            $member_guid = $jwt['member_guid'];
            $bid         = $jwt['bid'];
        }
        $db_member   = new \app\model\Member();
        $map         = ['bid' => $bid, 'guid' => $member_guid];
        $member_info = $db_member->get_member_info($map);
        $db_member->verify_member_status($map); //校验卡状态
        result($member_info);
    }

    /**
     *会员列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid         = $this->get_bid();
        $map         = [
            ['m.bid', '=', $bid]
        ];
        $join        = [
            ['member m2', 'm.share_member_guid = m2.guid AND m2.bid = m.bid', 'LEFT'],
        ];
        $db_business = new \app\model\Business();
        $field       = [
            'm.*',
            'm.id'      => 'member_id',
            'm.name'    => 'member_name',
            'm.mobile'  => 'member_mobile',
            'm2.id'     => 'share_member_id',
            'm2.name'   => 'share_member_name',
            'm2.mobile' => 'share_member_mobile',
        ];
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(m.mobile,4,4,'****') as member_mobile");
        }
        $this->model = $this->model->alias('m')->join($join)->where($map)->field($field)->order(['m.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *会员充值
     * @access public
     * @return void
     * @throws Exception
     */
    public function recharge_point()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_member_point_note = new MemberPointNote();
        $type                 = $params['type'];
        $member_guid          = $params['member_guid'];
        $point                = $params['point'];
        $data                 = [
            'bid'         => $bid,
            'member_guid' => $member_guid,
            'way'         => $point > 0 ? 3 : 4,
            'type'        => $type,
            'point'       => $point,
            'memo'        => '[后台操作]',
        ];
        $db_member_point_note->recharge_point($data);
        success('操作成功');
    }

    /**
     *会员充值
     * @access public
     * @return void
     * @throws Exception
     */
    public function recharge()
    {
        $params               = $this->params;
        $bid                  = $this->get_bid();
        $db_member_money_note = new MemberMoneyNote();
        $type                 = $params['type'];
        $member_guid          = $params['member_guid'];
        $money                = $params['money'];
        $data                 = [
            'bid'         => $bid,
            'member_guid' => $member_guid,
            'way'         => $money > 0 ? 1 : 6,
            'type'        => $type,
            'money'       => $money,
            'memo'        => '[后台操作]',
        ];
        $db_member_money_note->recharge_money($data);
        success('操作成功');
    }
}
