<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\Crontab as CrontabModel;
use app\model\CrontabExecuteNote;
use Exception;

class Crontab extends BasicAdminApi
{
    /**
     *定时任务列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new CrontabModel();
        $join        = [
            ['business b1', 'b.bid = b1.guid', 'LEFT'],
        ];
        $field       = [
            'b.*',
            'b1.account',
            'b1.business_name',
        ];
        $this->model = $db->alias('b')->join($join)->field($field)->order(['b.status' => 'DESC', 'b.last_execute_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *获取参数列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_payload()
    {
        $guid = $this->params['guid'];
        $db   = new CrontabModel();
        $map  = [['guid', '=', $guid]];
        $job  = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($job['payload']));
    }

    /**
     *立即启动某个定时任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function start()
    {
        $guid        = $this->params['guid'];
        $db          = new CrontabModel();
        $map         = [['guid', '=', $guid]];
        $update_data = ['status' => 1];
        $db::update($update_data, $map);
        success('任务启动成功');
    }

    /**
     *立即停止某个定时任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function stop()
    {
        $guid        = $this->params['guid'];
        $db          = new CrontabModel();
        $map         = [['guid', '=', $guid]];
        $update_data = ['status' => 0];
        $db::update($update_data, $map);
        success('任务停止成功');
    }

    /**
     *立即执行某个定时任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function execute()
    {
        $guid = $this->params['guid'];
        $db   = new CrontabModel();
        $map  = [['guid', '=', $guid]];
        $job  = $db->where($map)->find();
        $db->resolve($job);
        success('任务创建成功');
    }

    /**
     *执行记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function execute_note()
    {
        $params       = $this->params;
        $crontab_guid = $params['crontab_guid'];
        $db           = new CrontabExecuteNote();
        $map          = [['crontab_guid', '=', $crontab_guid]];
        $this->model  = $db->where($map);
        result($this->_list());
    }
}