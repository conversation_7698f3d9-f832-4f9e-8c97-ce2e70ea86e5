<?php

namespace app\controller\admin_api\v1;


use app\model\CouponUsedNote as CouponUsedNoteModel;
use app\common\tools\Excel;
use Exception;

class CouponUsedNote extends BasicAdminApi
{


    /**
     *储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db     = new CouponUsedNoteModel();
        $params = $this->params;
        $join   = [
            ['coupon_send_note csn', 'cun.coupon_send_guid = csn.guid AND cun.bid = csn.bid'],
            ['coupon c', 'cun.coupon_guid = c.guid AND cun.bid = c.bid'],
            ['member m', 'cun.member_guid = m.guid AND cun.bid = m.bid', 'LEFT'],
            ['user u', 'cun.used_user_guid = u.guid AND cun.bid = u.bid', 'LEFT'],
            ['store s', 'u.store_guid = s.guid AND u.bid = s.bid', 'LEFT'],
        ];
        $field  = [
            'cun.way',
            'cun.member_guid',
            'cun.used_num',
            'cun.used_value',
            'cun.create_time',
            'cun.used_user_guid',
            'cun.used_user_id',
            'csn.code',
            'c.name'    => 'coupon_name',
            'm.id'      => 'member_id',
            'm.card_id',
            'u.account' => 'user_account',
            'u.name'    => 'user_name',
            'u.store_guid',
            's.brand_guid',
            's.industry_guid',
        ];

        $db_user     = new \app\model\User();
        $map         = [
            ['cun.bid', '=', $this->get_bid()],
            ['csn.delete_time', 'NULL', null],
            ['cun.used_user_guid', 'IN', $db_user->getChildUserGuidArray()],
        ];
        $this->model = $db->alias('cun')->order(['cun.create_time' => 'DESC'])->join($join)->where($map);

        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'count(1)'                      => 'count',
                'IFNULL(sum(cun.used_num),0)'   => 'sum_used_num',
                'IFNULL(sum(cun.used_value),0)' => 'sum_used_value',
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '总张数 <b style="color: red"> ' . $data['sum_used_num'] . '</b> 张,总金额 <b style="color: red"> ' . number_format($data['sum_used_value'], 2) . '</b> 元';
            success($total_text);
        }
        $this->model = $this->model->field($field);
        result($this->_list());
    }

    public function build_export_query()
    {
        $db          = new CouponUsedNoteModel();
        $bid         = $this->get_bid();
        $join        = [
            ['coupon_send_note csn', 'cun.coupon_send_guid = csn.guid AND cun.bid = csn.bid'],
            ['coupon c', 'cun.coupon_guid = c.guid AND cun.bid = c.bid'],
            ['member m', 'cun.member_guid = m.guid AND cun.bid = m.bid', 'LEFT'],
            ['user u', 'cun.used_user_guid = u.guid AND cun.bid = u.bid', 'LEFT'],
            ['store s', 'u.store_guid = s.guid AND u.bid = s.bid', 'LEFT'],
        ];
        $field       = [
            'cun.way',
            'cun.member_guid',
            'cun.used_num',
            'cun.used_value',
            'cun.create_time',
            'cun.used_user_guid',
            'cun.used_user_id',
            'csn.code',
            'c.name'       => 'coupon_name',
            'm.id'         => 'member_id',
            'm.card_id',
            'u.account'    => 'user_account',
            'u.name'       => 'user_name',
            'u.store_guid',
            's.store_name' => 'store_name',
            's.brand_guid',
            's.industry_guid',
        ];
        $db_user     = new \app\model\User();
        $map         = [
            ['cun.bid', '=', $bid],
            ['csn.delete_time', 'NULL', null],
            ['cun.used_user_guid', 'IN', $db_user->getChildUserGuidArray()],
        ];
        $this->model = $db->alias('cun')->order(['cun.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        return $this->model;
    }

    public function export()
    {
        $data = $this->_select($this->build_export_query());
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $header = [
            'member_id'    => '会员编号',
            'coupon_name'  => '卡券',
            'code'         => '券号',
            'card_id'      => '卡号',
            'user_account' => '操作工号',
            'user_name'    => '操作用户',
            'store_name'   => '操作门店',
            'used_num'     => '核销张数',
            'used_value'   => '核销金额',
            'create_time'  => '操作时间',

        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *充值记录统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function report()
    {
        $db          = new CouponUsedNoteModel();
        $join        = [
            ['user u', 'cun.operator_user_guid = u.guid AND cun.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'u.id',
            'u.account',
            'u.name',
            'SUM(cun.money)' => 'total_money',
        ];
        $map         = [
            ['cun.bid', '=', $this->get_bid()],
        ];
        $group       = ['u.account'];
        $this->model = $db->alias('cun')->join($join)->field($field)->where($map)->group($group)->order(['total_money']);
        result($this->_list());
    }
}
