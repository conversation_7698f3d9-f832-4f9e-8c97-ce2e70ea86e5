<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\CropWechatAuth;
use Exception;


class WorkWeixin extends BasicAdminApi
{

    /**
     *获取配置信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db                = new CropWechatAuth();
        $bid               = $this->get_bid();
        $config            = get_config_by_bid($bid);
        $crop_wechat_appid = $config['crop_wechat_appid'];
        $url               = (string)url('gateway/work_weixin_auth/auth', ['bid' => $bid], true, true);
        if (!$crop_wechat_appid) {
            //return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $map  = [
            ['auth_corpid', '=', $crop_wechat_appid],
        ];
        $info = $db->where($map)->findOrEmpty();
        $data = [
            'info'     => $info,
            'status'   => !(int)$info->isEmpty(),
            'auth_url' => $url,
        ];
        result($data);
    }
}