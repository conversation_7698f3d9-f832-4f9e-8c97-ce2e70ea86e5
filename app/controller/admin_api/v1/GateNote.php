<?php

namespace app\controller\admin_api\v1;

use app\model\GateNote as GateNoteModel;
use Exception;

class GateNote extends BasicAdminApi
{
    /**
     * 门禁记录列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map = [
            ['bid', '=', $this->get_bid()]
        ];

        $this->model = $this->model->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 门禁记录详情
     * @access public
     * @return void
     * @throws Exception
     */
    public function detail()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        $guid   = $params['guid'];

        $db_gate_note = new GateNoteModel();
        $map          = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];

        $detail = $db_gate_note->where($map)->find();

        if (!$detail) {
            error('记录不存在');
        }

        result($detail);
    }

    /**
     * 添加门禁记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params = $this->params;

        $data = [
            'bid'             => $this->get_bid(),
            'guid'            => create_guid(),
            'cam_id'          => $params['cam_id'] ?? '',
            'plate_num'       => $params['plate_num'] ?? '',
            'card_id'         => $params['card_id'] ?? '',
            'yky_member_guid' => $params['yky_member_guid'] ?? '',
            'display_content' => $params['display_content'] ?? '',
            'speech_content'  => $params['speech_content'] ?? '',
        ];

        $db_gate_note = new GateNoteModel();
        $result       = $db_gate_note->save($data);

        if ($result) {
            success('添加成功');
        } else {
            error('添加失败');
        }
    }

    /**
     * 编辑门禁记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $guid   = $params['guid'];

        $data = [
            'cam_id'          => $params['cam_id'] ?? '',
            'plate_num'       => $params['plate_num'] ?? '',
            'card_id'         => $params['card_id'] ?? '',
            'yky_member_guid' => $params['yky_member_guid'] ?? '',
            'display_content' => $params['display_content'] ?? '',
            'speech_content'  => $params['speech_content'] ?? '',
        ];

        $db_gate_note = new GateNoteModel();
        $map          = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];

        $result = $db_gate_note->where($map)->update($data);

        if ($result) {
            success('编辑成功');
        } else {
            error('编辑失败');
        }
    }

    /**
     * 删除门禁记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params = $this->params;
        $guid   = $params['guid'];

        $db_gate_note = new GateNoteModel();
        $map          = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
        ];

        $result = $db_gate_note->where($map)->delete();

        if ($result) {
            success('删除成功');
        } else {
            error('删除失败');
        }
    }
}
