<?php

namespace app\controller\admin_api\v1;

use app\model\JobsExecuteNote as JobsExecuteNoteModel;

class JobsExecuteNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new JobsExecuteNoteModel();
        $map         = [];
        if (empty($this->params['create_time'])) {
            $begin_date = date('Y-m-d H:i:s', strtotime('-1 days'));
            $map        = [['create_time', '>', $begin_date]];
        }
        $this->model = $this->model->where($map)->append(['job_payload_text']);
        result($this->_list());
    }

    public function re_execute()
    {
        $params      = $this->params;
        $id          = $params['id'];
        $db          = new JobsExecuteNoteModel();
        $map         = [['id', '=', $id]];
        $info        = $db->where($map)->findOrFail();
        $job_payload = $info['job_payload'];
        job()->set_job_name($job_payload['job'])->push_job(json_decode($job_payload['data'], true));
        success('重新执行成功');
    }
}