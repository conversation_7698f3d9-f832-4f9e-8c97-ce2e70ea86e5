<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\PayParameter as PayParameterModel;
use app\model\UserBindNote;
use app\model\WechatActivity;
use app\model\WechatCard;
use app\model\WechatConfig;
use app\model\WechatRedPacketOrder;
use app\model\WechatTemplate;
use app\model\WechatTransfersOrder;
use app\common\tools\Visitor;
use Exception;
use think\facade\Db;
use Throwable;

class Wechat extends BasicAdminApi
{

    protected function set_activate_user_form()
    {
        $params  = $this->params;
        $bid     = $this->get_bid();
        $card_id = $params['card_id'];
        //       $wechat_config = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        //       $appid         = $wechat_config['app_id'];
        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];
        $wechat = weixin($appid)::WeChatCard();
        $data   = [
            'card_id'       => $card_id,
            'required_form' => [
                'can_modify'           => false,
                'common_field_id_list' => [
                    'USER_FORM_INFO_FLAG_MOBILE'
                ],
            ]
        ];
        $result = [];
        try {
            $result = $wechat->setActivateMemberCardUser($data);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        //result($result);
    }

    public function get_url()
    {
        $this->set_activate_user_form();
        $params  = $this->params;
        $bid     = $this->get_bid();
        $card_id = $params['card_id'];
        //       $wechat_config  = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        //       $appid          = $wechat_config['app_id'];

        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];

        $wechat         = weixin($appid)::WeChatCard();
        $outer_str      = (string)url('member/code/index', ['bid' => $bid, 'outer_str' => tools()::get_bill_number()], false, true);
        $outer_str      = tools()::replace_readonly_to_www($outer_str);
        $center_url     = (string)url('member/code/index', ['bid' => $bid, 'card_id' => $card_id], false, true);
        $center_url     = tools()::replace_readonly_to_www($center_url);
        $map            = [
            ['bid', '=', $bid],
            ['card_id', '=', $card_id],
            ['appid', '=', $appid],
        ];
        $db_wechat_card = new WechatCard();
        $card_info      = $db_wechat_card->where($map)->find();
        // center_url
        $before_info = $card_info['card_info']['card']['member_card'];

        //       参数名	是否提审	类型	示例值	描述
        //title	是	string(27)	微信会员卡	会员卡标题，字数上限为9个汉字
        //logo_url	否	string(128)	http://mmbiz .qpic.cn/	卡券的商户logo，建议像素为300*300。
        //notice	否	string（48）	请出示二维 码核销卡券。	使用提醒，字数上限为16个汉字。
        //description	是	string（3072）	不可与其 他优惠同享	使用说明。
        //service_phone	否	string（24）	40012234	客服电话。
        //color	否	string（3072）	Color010	卡券颜色。
        //location_id_list	否	string（3072）	1234,2314	支持更新适用门店列表。
        //use_all_locations	否	bool	true	支持全部门店，填入后卡券门店跟随商户门店更新而更新
        //center_title	否	string(18)	立即使用	会员卡中部的跳转按钮名称 ，建议用作使用用途
        //center_sub_title	否	string(24)	到店后使用	会员卡中部按钮解释wording
        //center_url	否	string(128)	www.qq.com	会员卡中部按钮对应跳转的url
        $before_info['base_info']['center_title']     = '立即提货';
        $before_info['base_info']['center_sub_title'] = '三秒快速提货';
        $before_info['base_info']['center_url']       = $center_url;
        unset($before_info['base_info']['brand_name']);
        unset($before_info['base_info']['sku']);
        //       unset($before_info['base_info']['logo_url']);
        //
        //       unset($before_info['wx_activate_after_submit_url']);
        //       unset($before_info['wx_activate_after_submit']);
        //       unset($before_info['prerogative']);
        //       result($before_info);
        $update = $wechat->updateCard($card_id, $before_info);
        //       $card_info['card_info']['card']['member_card'] = $before_info;
        //       $update_data                                   = [
        //           'card_info' => $before_info
        //       ];
        // $db_wechat_card::update($update_data, $map);

        $data   = [
            'card_id'   => $card_id,
            'outer_str' => urlencode($outer_str)
        ];
        $result = [];
        try {
            $result = $wechat->getActivateUrl($data);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        result($result);
    }

    /**
     *创建卡券
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function create_card()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $card_guid = create_guid();
        //       $wechat_config = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        //       $appid         = $wechat_config['app_id'];
        //       $mch_id        = $wechat_config['mch_id'];
        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];

        $wechat = weixin($appid)::WeChatCard();
        //       $params['least_cost']         = tools()::ncPriceYuan2fen($params['least_cost']);
        //       $params['reduce_cost']        = tools()::ncPriceYuan2fen($params['reduce_cost']);
        $wx_activate_after_submit_url = (string)url('member/wechat/card', ['bid' => $bid, 'card_guid' => $card_guid], false, true);
        $center_url                   = (string)url('member/code/index', ['bid' => $bid, 'card_guid' => $card_guid], false, true);

        $data = [
            'card' => [
                'card_type'   => 'MEMBER_CARD',
                'member_card' => [
                    //   prerogative	是	strin g(3072)	会员卡特权说明,限制1024汉字。
                    //                       supply_bonus	是	bool	显示积分，填写true或false，如填写true，积分相关字段均为必 填 若设置为true则后续不可以被关闭。
                    //                       supply_balance	是	bool	是否支持储值，填写true或false。如填写true，储值相关字段均为必 填 若设置为true则后续不可以被关闭。该字段须开通储值功能后方可使用， 详情见： 获取特殊权限

                    'supply_balance'               => false,
                    'supply_bonus'                 => false,
                    'wx_activate'                  => true,
                    'wx_activate_after_submit'     => true,
                    'wx_activate_after_submit_url' => $wx_activate_after_submit_url,
                    'prerogative'                  => $params['prerogative'],
                    'base_info'                    => [
                        'logo_url'     => $params['logo_url'],
                        'code_type'    => 'CODE_TYPE_NONE',
                        //                       code_type	是	string(16)	Code展示类型， "CODE_TYPE_TEXT" 文本 "CODE_TYPE_BARCODE" 一维码 "CODE_TYPE_QRCODE" 二维码 "CODE_TYPE_ONLY_QRCODE" 仅显示二维码 "CODE_TYPE_ONLY_BARCODE" 仅显示一维码 "CODE_TYPE_NONE" 不显示任何码型\
                        'brand_name'   => $params['brand_name'],
                        'title'        => $params['title'],
                        'color'        => $params['color'],
                        'notice'       => $params['notice'],
                        'description'  => $params['description'],
                        'center_title' => '立即提货',
                        //                       'center_sub_title' => '三秒极速提货',
                        'center_url'   => $center_url,
                        'sku'          => ['quantity' => intval($params['quantity'] ?? 100000000)],
                        'date_info'    => [
                            'type' => 'DATE_TYPE_PERMANENT'
                        ],
                        //brand_name	是	string	商户名字,字数上限为12个汉字。
                        //title	是	string	卡券名，字数上限为9个汉字 (建议涵盖卡券属性、服务及金额)。
                        //color	是	string	券颜色。按色彩规范标注填写Color010-Color100
                        //notice	是	string	卡券使用提醒，字数上限为16个汉字。
                        //description	是	string	卡券使用说明，字数上限为1024个汉字。
                        //sku	是	JSON	商品信息。
                        //quantity	是	int	卡券库存的数量，不支持填写0，上限为100000000。
                        //date_info	是	JSON	使用日期，有效期的信息。
                        //type	是	string	使用时间的类型 支持固定时长有效类型 固定日期有效类型 永久有效类型( DATE_TYPE_PERMANENT)


                        //                       'pay_info'                   => [
                        //                           'swipe_card' => [
                        //                               'use_mid_list'  => [$mch_id],
                        //                               'create_mid'    => $mch_id,
                        //                               'is_swipe_card' => true,
                        //                           ],
                        //                       ],
                        //                       'brand_name'                 => $params['brand_name'],
                        //                       'title'                      => $params['title'],
                        //                       "color"                      => $params['color'],
                        //                       'sku'                        => ['quantity' => intval($params['quantity'])],
                        //                       'center_title'               => $params['center_title'],//配置代金券跳转小程序
                        //                       'center_app_brand_user_name' => $params['center_app_brand_user_name'] . '@app',//原始ID+@app
                        //                       'center_app_brand_pass'      => $params['center_app_brand_pass'],//小程序的路径path
                    ],
                    //                   'least_cost'                   => $params['least_cost'],
                    //                   'reduce_cost'                  => $params['reduce_cost']
                ],
            ]
        ];
        //       $date_info                    = [];
        //       if ($params['expired_time_type'] == 'DATE_TYPE_FIX_TIME_RANGE') {
        //           $begin_timestamp = max(time() + 60, strtotime($params['begin_timestamp']));
        //           $date_info       = [
        //               'type'            => 'DATE_TYPE_FIX_TIME_RANGE',
        //               'begin_timestamp' => $begin_timestamp,
        //               'end_timestamp'   => strtotime($params['end_timestamp']),
        //           ];
        //       }
        //       if ($params['expired_time_type'] == 'DATE_TYPE_FIX_TERM') {
        //           $date_info = [
        //               'type'             => 'DATE_TYPE_FIX_TERM',
        //               'fixed_begin_term' => intval($params['fixed_begin_term']),
        //               'fixed_term'       => intval($params['fixed_term']),
        //               'end_timestamp'    => strtotime($params['end_timestamp']),
        //           ];
        //       }
        //       $data['card']['cash']['base_info']['date_info'] = $date_info;
        try {
            $result = $wechat->create($data);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        $card_id        = $result['card_id'];
        $db_wechat_card = new WechatCard();
        $data           = [
            'guid'      => $card_guid,
            'bid'       => $this->get_bid(),
            'appid'     => $appid,
            'card_id'   => $card_id,
            'card_info' => $data
        ];
        $db_wechat_card->save($data);
        send_qy_wechat('card_id:' . $card_id);
        success($card_id);
    }

    /**
     *创建卡券
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function create_cash_card()
    {
        $params                = $this->params;
        $params['least_cost']  = tools()::nc_price_yuan2fen($params['least_cost']);
        $params['reduce_cost'] = tools()::nc_price_yuan2fen($params['reduce_cost']);
        $wechat_config         = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        $appid                 = $wechat_config['app_id'];
        $mch_id                = $wechat_config['mch_id'];
        $wechat                = weixin($appid)::WeChatCard();
        $data                  = [
            'card' => [
                'card_type' => 'CASH',
                'cash'      => [
                    'base_info'   => [
                        "logo_url"                   => $params['logo_url'],
                        'pay_info'                   => [
                            'swipe_card' => [
                                'use_mid_list'  => [$mch_id],
                                'create_mid'    => $mch_id,
                                'is_swipe_card' => true,
                            ],
                        ],
                        'brand_name'                 => $params['brand_name'],
                        'title'                      => $params['title'],
                        "color"                      => $params['color'],
                        'sku'                        => ['quantity' => intval($params['quantity'])],
                        'center_title'               => $params['center_title'],//配置代金券跳转小程序
                        'center_app_brand_user_name' => $params['center_app_brand_user_name'] . '@app',//原始ID+@app
                        'center_app_brand_pass'      => $params['center_app_brand_pass'],//小程序的路径path
                    ],
                    'least_cost'  => $params['least_cost'],
                    'reduce_cost' => $params['reduce_cost']
                ],
            ]
        ];
        $date_info             = [];
        if ($params['expired_time_type'] == 'DATE_TYPE_FIX_TIME_RANGE') {
            $begin_timestamp = max(time() + 60, strtotime($params['begin_timestamp']));
            $date_info       = [
                'type'            => 'DATE_TYPE_FIX_TIME_RANGE',
                'begin_timestamp' => $begin_timestamp,
                'end_timestamp'   => strtotime($params['end_timestamp']),
            ];
        }
        if ($params['expired_time_type'] == 'DATE_TYPE_FIX_TERM') {
            $date_info = [
                'type'             => 'DATE_TYPE_FIX_TERM',
                'fixed_begin_term' => intval($params['fixed_begin_term']),
                'fixed_term'       => intval($params['fixed_term']),
                'end_timestamp'    => strtotime($params['end_timestamp']),
            ];
        }
        $data['card']['cash']['base_info']['date_info'] = $date_info;
        try {
            $result = $wechat->create($data);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        $card_id        = $result['card_id'];
        $db_wechat_card = new WechatCard();
        $data           = [
            'guid'      => create_guid(),
            'bid'       => $this->get_bid(),
            'appid'     => $appid,
            'card_id'   => $card_id,
            'card_info' => $data
        ];
        $db_wechat_card->save($data);
        send_qy_wechat('card_id:' . $card_id);
        success($card_id);
    }

    /**
     *创建活动
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function create_activity()
    {
        $params        = $this->params;
        $card_id       = $params['card_id'];
        $wechat_config = pay($this->get_bid())->driver('wechat')->scene('mp')->get_pay_parameter();
        $appid         = $wechat_config['app_id'];
        $mch_id        = $wechat_config['mch_id'];
        $wechat        = weixin($appid)::WeChatCard();
        $begin_time    = max(time() + 60, strtotime($params['begin_time']));
        $end_time      = strtotime($params['end_time']);
        //       error($end_time - $begin_time);
        $data = [
            'info' => [
                'basic_info'     => [
                    // 'begin_time' => time(),
                    // 'end_time'   => time() + 3600 * 24 * 7,
                    'begin_time'           => $begin_time,
                    'end_time'             => $end_time,
                    'max_partic_times_act' => intval($params['max_partic_times_act']),
                    'gift_num'             => intval($params['gift_num']),
                    'activity_tinyappid'   => $appid,
                    'activity_bg_color'    => $params['activity_bg_color'],
                    'mch_code'             => $mch_id
                ],
                'card_info_list' => [
                    [
                        'card_id'    => $card_id,
                        'min_amt'    => tools()::nc_price_yuan2fen($params['min_amt']),
                        'total_user' => true
                    ]
                ],
                'custom_info'    => [
                    'type' => 'AFTER_PAY_PACKAGE'
                ],
            ]
        ];
        try {
            $result = $wechat->addActivity($data);
        } catch (Exception $e) {
            error($e->getMessage());
        }
        $activity_id        = $result['activity_id'];
        $db_wechat_activity = new WechatActivity();
        $data               = [
            'guid'          => create_guid(),
            'bid'           => $this->get_bid(),
            'card_id'       => $card_id,
            'appid'         => $appid,
            'activity_id'   => $activity_id,
            'activity_info' => $data
        ];
        $db_wechat_activity->save($data);
        send_qy_wechat('activity_id:' . $activity_id);
        success($activity_id);
    }

    /**
     *活动列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_list()
    {
        $this->model = new WechatActivity();
        result($this->_list());
    }

    /**
     *卡列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function card_list()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = new WechatCard();
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *免充值代金券升级
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function sandbox()
    {
        ignore_user_abort(); // run script in background
        set_time_limit(0); // run script forever
        //下载对账单用例
        $pay_instance = pay($this->get_bid())->debug(true)->driver('wechat');
        $pay          = $pay_instance->scene('bill');
        $options      = [
            'bill_date' => '2018-01-01', // 账单时间(日账单yyyy-MM-dd,月账单 yyyy-MM)
            'bill_type' => 'ALL',
        ];
        $result       = $pay->apply($options);
        //刷卡支付+查询用例
        $auth_code    = '151234567890123456';
        $ip           = tools()::get_client_ip();
        $total_fee    = 501; //先501  支付 查询  再502  支付-退款-退款查询
        $out_trade_no = tools()::get_bill_number();
        $options      = [
            'bid'              => $this->get_bid(),
            'out_trade_no'     => $out_trade_no, // 订单号
            'total_fee'        => $total_fee, // 订单金额，**单位：分**
            'body'             => '刷卡支付+查询用例', // 订单描述
            'spbill_create_ip' => $ip, // 支付人的 IP
            'auth_code'        => $auth_code, // 授权码
        ];

        $pay    = $pay_instance->scene('pos');
        $result = $pay->apply($options);
        $result = $pay->find($out_trade_no);

        //刷卡支付+退款+退款查询用例
        $total_fee    = 502; //先501  支付 查询  再502  支付-退款-退款查询
        $out_trade_no = tools()::get_bill_number();
        $options      = [
            'bid'              => $this->get_bid(),
            'out_trade_no'     => $out_trade_no, // 订单号
            'total_fee'        => $total_fee, // 订单金额，**单位：分**
            'body'             => '刷卡支付+退款+退款查询用例', // 订单描述
            'spbill_create_ip' => $ip, // 支付人的 IP
            'auth_code'        => $auth_code, // 授权码
        ];

        $result = $pay->apply($options);
        $result = $pay->find($out_trade_no);
        $order  = [
            'out_trade_no'  => $out_trade_no,
            'out_refund_no' => time(),
            'total_fee'     => $total_fee,
            'refund_fee'    => $total_fee,
            'refund_desc'   => '刷卡支付+退款+退款查询用例-退款',
        ];
        $result = $pay->refund($order);
        $result = $pay->refund_find($out_trade_no);

        //公众号支付+查单 用例
        $total_fee    = 551; //先551  支付 查询  再552  支付-退款-退款查询
        $out_trade_no = tools()::get_bill_number();
        $options      = [
            'bid'              => $this->get_bid(),
            'body'             => '公众号支付+查单 用例',
            'out_trade_no'     => $out_trade_no,
            'total_fee'        => $total_fee,
            'openid'           => 'test',
            'spbill_create_ip' => $ip,
        ];
        $pay          = $pay_instance->scene('mp');
        $result       = $pay->apply($options);
        $result       = $pay->find($out_trade_no);

        //公众号支付+退款+退款查询 用例
        $total_fee    = 552; //先551  支付 查询  再552  支付-退款-退款查询
        $out_trade_no = tools()::get_bill_number();
        $options      = [
            'bid'              => $this->get_bid(),
            'body'             => '公众号支付+退款+退款查询用例',
            'out_trade_no'     => $out_trade_no,
            'total_fee'        => $total_fee,
            'openid'           => 'test',
            'spbill_create_ip' => $ip,
        ];
        $result       = $pay->apply($options);
        $order        = [
            'out_trade_no'  => $out_trade_no,
            'out_refund_no' => time(),
            'total_fee'     => $total_fee,
            'refund_fee'    => $total_fee,
            'refund_desc'   => '公众号支付+退款+退款查询用例-退款',
        ];
        $result       = $pay->find($out_trade_no);
        $result       = $pay->refund($order);
        $result       = $pay->refund_find($out_trade_no);
        success('恭喜您,升级完成');
    }

    /**
     *修改微信配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update()
    {
        $bid         = $this->get_bid();
        $config      = get_config_by_bid($bid);
        $appid       = $config['appid'];
        $db          = new WechatConfig();
        $update_data = $this->params;
        if (isset($update_data['ssl_cer'])) {
            $update_data['ssl_cer'] = tools()::web_to_path($update_data['ssl_cer']);
        }
        if (isset($update_data['ssl_key'])) {
            $update_data['ssl_key'] = tools()::web_to_path($update_data['ssl_key']);
        }
        $update_data['ssl_cer_string'] = $this->request->param('ssl_cer_string', '', null);
        $update_data['ssl_key_string'] = $this->request->param('ssl_key_string', '', null);
        $component_appid               = weixin()::get_component_appid();
        $map                           = [
            ['authorizer_appid', '=', $appid],
            ['component_appid', '=', $component_appid],
        ];
        $update                        = $db->allowField(['ssl_cer', 'ssl_key', 'ssl_cer_string', 'ssl_key_string', 'mch_id', 'partnerkey', 'authorizer_appid', 'authorizer_appsecret'])::update($update_data, $map);
        $channel_id                    = 1;
        $db_pay_parameter              = new PayParameterModel();
        $guid                          = $db_pay_parameter->get_parameter_guid_by_channel_id($bid, $channel_id);
        if (empty($guid)) {
            $guid = $db_pay_parameter->add_parameter($channel_id, $bid);
        }
        $map              = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $update_parameter = [
            'parameter' => [
                'debug'   => false,
                'app_id'  => $appid,
                'mch_id'  => $update_data['mch_id'],
                'mch_key' => $update_data['partnerkey'],
                'ssl_cer' => $update_data['ssl_cer'],
                'ssl_key' => $update_data['ssl_key'],
            ]
        ];
        $db_pay_parameter::update($update_parameter, $map);
        success('修改成功');
    }

    public function auto_set_industry()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];
        if (!$appid) {
            error('请先授权公众号再设置行业');
        }
        $db_wechat_config = new WechatConfig();
        $result           = $db_wechat_config->auto_set_industry($appid);
        if ($result) {
            success('设置成功');
        }
        error($db_wechat_config->getError());
    }

    public function register_weapp()
    {
        $params       = $this->params;
        $appid        = $params['appid'];
        $instance     = weixin()::WeOpenMiniApp();
        $callback_url = (string)url('gateway/wechat_auth/register_weapp', ['appid' => $appid], false, true);
        $url          = $instance->getCopyRegisterMiniUrl($appid, $callback_url);
        $url          = tools()::build_url_with_referer($url);
        result(['url' => $url]);
    }

    /**
     *修改微信配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db     = new WechatConfig();
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $appid  = $config['appid'];
        $url    = (string)url('gateway/wechat_auth/auth', ['bid' => $bid, 'auth_type' => 1], true, true);
        if (!$appid) {
            //return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['authorizer_appid', '=', $appid],
            ['component_appid', '=', $component_appid],
        ];
        $info            = $db->where($map)->findOrEmpty();
        if ($info->isEmpty() && Visitor::is_readonly()) {
            error("未授权公众号,请登录正式环境授权");
        }
        $industry = [];
        $category = [];
        if ($appid) {
            $wechat_template = weixin($appid)::WeChatTemplate();
            try {
                $industry = $wechat_template->getIndustry();
            } catch (Exception|Throwable $e) {
                switch ($e->getCode()) {
                    case 48001:
                        // api unauthorized rid: 62a44519-6131f51b-3f7c4fb9
                        break;
                    case 40102:
                        //没有去申请该功能, 但是可以直接设置权限
                        break;
                    default:
                }
            }
            $wechat_template = weixin($appid)::WeChatNewtmpl();

            try {
                $category = $wechat_template->getCategory();
            } catch (Exception|Throwable $e) {
                switch ($e->getCode()) {
                    case 48001:
                        // api unauthorized rid: 62a44519-6131f51b-3f7c4fb9
                        break;
                    case 40102:
                        //没有去申请该功能, 但是可以直接设置权限
                        break;
                    default:
                }
            }
        }
        $data = [
            'info'     => $info,
            'industry' => $industry,
            'category' => $category,
            'auth_url' => $url,
        ];
        result($data);
    }

    /**
     *发送模板消息
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_tpl()
    {
        $params = $this->params;
        $type   = parse_name($params['type']); //sendNewOrder sendFxsApply sendFxsCash sendMchApply sendMchUploadGoods sendMchUploadGoodsResult
        $data   = $this->request->param('data', '', null);
        $data   = json_decode($data, true);
        //$appid      = '';
        //$touser     = ''; //openid
        $bid = $this->get_bid();
        //$db_user    = new \app\model\User();
        //$admin_user = $db_user->get_admin_user($bid);
        $db_user_bind_note = new UserBindNote();
        $map               = [['bid', '=', $bid]];
        $bind_note         = $db_user_bind_note->field(['appid', 'openid'])->where($map)->select();
        $template_data     = [];
        $template_type     = 'system_notice';
        switch ($type) {
            case 'send_new_order':
                $template_data = [
                    'first'         => [
                        'value' => '您有一笔新订单，请及时处理。',
                        'color' => '#666666',
                    ],
                    'tradeDateTime' => [
                        'value' => $data['time'],
                        'color' => '#000000',
                    ],
                    'orderType'     => [
                        'value' => $data['type'] ? $data['type'] : '',
                        'color' => '#000000',
                    ],
                    'customerInfo'  => [
                        'value' => $data['user'],
                        'color' => '#000000',
                    ],
                    'orderItemName' => [
                        'value' => '商品信息',
                        'color' => '#000000',
                    ],
                    'orderItemData' => [
                        'value' => $data['goods'],
                        'color' => '#000000',
                    ],
                    'remark'        => [
                        'value' => '',
                        'color' => '#000000',
                    ],
                ];
                $template_type = $type; //订单通知单独模板消息发送
                break;
            case 'send_fxs_apply':
                $template_data = [
                    'first'    => [
                        'value' => '有新的用户申请成为分销商，请及时处理。',
                        'color' => '#666666',
                    ],
                    'keyword1' => [
                        'value' => time(),
                        'color' => '#000000',
                    ],
                    'keyword2' => [
                        'value' => '分销商入驻申请',
                        'color' => '#000000',
                    ],
                    'keyword3' => [
                        'value' => $data['time'],
                        'color' => '#000000',
                    ],
                    'keyword4' => [
                        'value' => $data['content'],
                        'color' => '#000000',
                    ],
                    'remark'   => [
                        'value' => '',
                        'color' => '#000000',
                    ],
                ];
                break;
            case 'send_mch_apply':
                $template_data = [
                    'first'    => [
                        'value' => '有新的用户申请成为入驻商，请及时处理。',
                        'color' => '#666666',
                    ],
                    'keyword1' => [
                        'value' => time(),
                        'color' => '#000000',
                    ],
                    'keyword2' => [
                        'value' => '入驻商入驻申请',
                        'color' => '#000000',
                    ],
                    'keyword3' => [
                        'value' => $data['time'],
                        'color' => '#000000',
                    ],
                    'keyword4' => [
                        'value' => $data['content'],
                        'color' => '#000000',
                    ],
                    'remark'   => [
                        'value' => '',
                        'color' => '#000000',
                    ],
                ];
                break;
            case 'send_fxs_cash':
                $template_data = [
                    'first'    => [
                        'value' => '有分销商申请提现，请及时处理。',
                        'color' => '#666666',
                    ],
                    'keyword1' => [
                        'value' => time(),
                        'color' => '#000000',
                    ],
                    'keyword2' => [
                        'value' => '分销商提现申请',
                        'color' => '#000000',
                    ],
                    'keyword3' => [
                        'value' => $data['time'],
                        'color' => '#000000',
                    ],
                    'keyword4' => [
                        'value' => "申请用户：" . $data['user'] . ';金额' . $data['money'],
                        'color' => '#000000',
                    ],
                    'remark'   => [
                        'value' => '',
                        'color' => '#000000',
                    ],
                ];
                break;
            case 'send_mch_upload_goods':
                $template_data = [
                    'first'    => [
                        'value' => '入驻商有新的商品申请上架，请及时处理。',
                        'color' => '#666666',
                    ],
                    'keyword1' => [
                        'value' => time(),
                        'color' => '#000000',
                    ],
                    'keyword2' => [
                        'value' => '商品上架申请',
                        'color' => '#000000',
                    ],
                    'keyword3' => [
                        'value' => format_timestamp(),
                        'color' => '#000000',
                    ],
                    'keyword4' => [
                        'value' => "商品信息：" . $data['goods'],
                        'color' => '#000000',
                    ],
                    'remark'   => [
                        'value' => '',
                        'color' => '#000000',
                    ],
                ];
                break;
            default:
                error('暂时不支持发送' . $type . '类型的消息');
        }
        $appid_template_id = [];
        foreach ($bind_note as $key => $val) {
            if (isset($appid_template_id[$val['appid']])) {
                $template_id = $appid_template_id[$val['appid']];
            } else {
                $db          = new WechatTemplate();
                $template_id = $db->get_template_id_by_type($val['appid'], $template_type);
                if (empty($template_id)) {
                    continue;
                }
                $appid_template_id[$val['appid']] = $template_id; //缓存appid对应的 template_id
            }
            $template = [
                'touser'      => $val['openid'],
                'template_id' => $template_id,
                'data'        => $template_data
            ];
            job()->set_job_name('Weixin@send_template_message')->push_job(['template' => $template, 'appid' => $val['appid']]);
        }
        success('发送成功');
    }

    /**
     *订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function transfers_order()
    {
        $db          = new WechatTransfersOrder();
        $join        = [
            ['member m', 'm.bid = wtfo.bid AND m.guid = wtfo.member_guid', 'LEFT'],
        ];
        $map         = [
            ['wtfo.bid', '=', $this->get_bid()]
        ];
        $this->model = $db->field(
            [
                'wtfo.guid',
                'm.mobile',
                'wtfo.way',
                Db::raw('round(wtfo.amount/100,2) as amount'),
                'wtfo.desc',
                'wtfo.message',
                'wtfo.create_time',
                'wtfo.status',
                'wtfo.memo',
                'wtfo.payment_time',
                'wtfo.update_time',
            ]
        )->alias('wtfo')->join($join)->where($map)->order(['wtfo.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *订单记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function red_packet_order()
    {
        $db   = new WechatRedPacketOrder();
        $join = [
            ['member m', 'm.bid = wrpo.bid AND m.guid = wrpo.member_guid', 'LEFT'],
        ];
        //       $map         = [
        //           ['wrpo.bid', '=', $this->get_bid()]
        //       ];
        $map         = [];
        $this->model = $db->field(
            [
                'wrpo.guid',
                'm.mobile',
                'wrpo.way',
                Db::raw('round(wrpo.total_amount/100,2) as total_amount'),
                'wrpo.reason',
                'wrpo.create_time',
                'wrpo.status',
                'wrpo.memo',
                'wrpo.red_packet_send_time',
                'wrpo.update_time',
            ]
        )->alias('wrpo')->join($join)->where($map)->order(['wrpo.create_time' => 'DESC']);
        result($this->_list());
    }
}