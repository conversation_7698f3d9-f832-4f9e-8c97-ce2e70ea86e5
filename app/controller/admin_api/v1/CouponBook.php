<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use app\model\CouponBook as CouponBookModel;
use app\model\CouponBookItem;

class CouponBook extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new CouponBookModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function coupon_item_list()
    {
        $params                 = $this->params;
        $db                     = new Coupon();
        $bid                    = $this->get_bid();
        $map                    = [
            ['bid', '=', $bid],
            ['type', 'IN', [1, 2]],
        ];
        $list                   = $db->where($map)->field(['name', 'guid' => 'coupon_item_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid                   = $params['guid'] ?? '';
        $coupon_item_guid_array = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_coupon_book_item    = new CouponBookItem();
            $coupon_item_guid_array = $db_coupon_book_item->get_coupon_guid_array($bid, $guid);
        }
        if (!empty($coupon_item_guid_array)) {
            foreach ($list as $key => $val) {
                if (in_array($val['coupon_item_guid'], $coupon_item_guid_array)) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }
}
