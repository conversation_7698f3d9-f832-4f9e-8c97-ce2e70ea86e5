<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use Exception;
use think\facade\Db;

class RecycleBin extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function coupon()
    {
        $params = $this->params;
        $bid    = $params['business_guid'] ?? $this->get_bid();
        unset($this->params['business_guid']);
        $db          = new Coupon();
        $this->model = $db::withTrashed();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'NOT NULL', NULL],
        ];
        $field       = [
            'guid',
            'bid',
            'name',
            'create_time',
            'delete_time',
        ];
        $this->model = $this->model->field($field)->where($map);
        result($this->_list());
    }

    public function goods()
    {
        $db     = new \app\model\Goods();
        $params = $this->params;
        $bid    = $params['business_guid'] ?? $this->get_bid();
        unset($this->params['business_guid']);
        $this->model = $db::withTrashed();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'NOT NULL', NULL],
        ];
        $field       = [
            'guid',
            'bid',
            'name',
            'create_time',
            'delete_time',
        ];
        $this->model = $this->model->field($field)->where($map);
        result($this->_list());
    }

    public function restore()
    {
        $params = $this->params;
        $bid    = $params['bid'] ?? $this->get_bid();
        $id     = $params['id'] ?? null;
        $guid   = $params['guid'] ?? null;
        $table  = $params['table'];
        $map    = [
            ['bid', '=', $bid]
        ];
        if ($id) {
            $map[] = ['id', '=', $id];
        } elseif ($guid) {
            $map[] = ['guid', '=', $guid];
        } else {
            error('还原条件有误');
        }
        $count = Db::table($table)->where($map)->update(['delete_time' => null]);
        success('还原成功' . $count . '条记录');
    }
}