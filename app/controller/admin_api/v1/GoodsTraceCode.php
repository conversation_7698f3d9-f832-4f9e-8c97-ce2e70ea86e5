<?php

namespace app\controller\admin_api\v1;

use app\model\GoodsTraceCode as GoodsTraceCodeModel;

class GoodsTraceCode extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new GoodsTraceCodeModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    public function get_qrcode()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $code                = $params['code'];
        $db_goods_trace_code = new GoodsTraceCodeModel();
        $map                 = [
            ['bid', '=', $bid],
            ['code', '=', $code]
        ];
        $info                = $db_goods_trace_code->where($map)->field(['id'])->findOrFail();
        $url_info            = $db_goods_trace_code->get_goods_trace_url($bid);
        $short_url           = $url_info['short_url'];
        result(['url' => $short_url . '?c=' . $code]);
    }
}
