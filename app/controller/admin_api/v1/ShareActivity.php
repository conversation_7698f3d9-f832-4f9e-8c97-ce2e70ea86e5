<?php

namespace app\controller\admin_api\v1;

use Exception;

class ShareActivity extends BasicAdminApi
{
    /**
     *订单详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        $guid   = $params['guid'];
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $info   = $this->model->where($map)->find();
        result($info);
    }

    /**
     * 日志列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $order       = [
            'status'      => 'DESC',
            'create_time' => 'DESC'
        ];
        $this->model = $this->model->where($map)->order($order);
        result($this->_list());
    }
}
