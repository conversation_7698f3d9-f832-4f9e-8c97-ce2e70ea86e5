<?php

namespace app\controller\admin_api\v1;

use Exception;

class FreightTemplate extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid]
        ];
        $order       = ['create_time' => 'DESC'];
        $this->model = $this->model->where($map)->order($order);
        result($this->_list());
    }

    /**
     *版本授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function area_id_list()
    {
        $db   = new \app\model\FreightTemplate();
        $node = $db->get_rule_node_info($this->params['guid']);
        result($node);
    }
}