<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;


use Exception;

class NotifyTemplateDefault extends BasicAdminApi
{
    /**
     *获取通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->model = $this->model->order(['status' => 'DESC', 'key_name' => 'ASC', 'provider' => 'ASC', 'driver' => 'ASC']);
        result($this->_list());
    }

    /**
     *添加通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params              = $this->params;
        $db                  = new \app\model\NotifyTemplateDefault();
        $form_data           = tools()::_parse_fs_form_data($params, 'variable_list');
        $form_data['status'] = $form_data['status'] ?? 0;
        $update              = $db->save($form_data);
        if ($update) {
            success('添加成功');
        } else {
            error('添加失败');
        }
    }


    /**
     *编辑模板
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params      = $this->params;
        $update_data = isset($params['fsFormData']) ? tools()::_parse_fs_form_data($params, 'variable_list') : $params;
        $id          = intval($update_data['id']);
        //        $update_data['status'] = $update_data['status'] ?? 0;
        if (isset($params['status'])) {
            $update_data['status'] = $params['status'];
        }
        if (isset($params['is_super'])) {
            $update_data['is_super'] = $params['is_super'];
        }
        $db     = new \app\model\NotifyTemplateDefault();
        $map    = [['id', '=', $id]];
        $update = $db::update($update_data, $map);
        if ($update) {
            success('更新成功');
        } else {
            error('更新失败');
        }
    }

    /**
     *获取参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_variable_list()
    {
        $id      = $this->params['id'];
        $db      = new \app\model\NotifyTemplateDefault();
        $map     = [['id', '=', $id]];
        $channel = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($channel['variable_list'] ?? []));
    }

    /**
     *获取参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_param()
    {
        $id      = $this->params['id'];
        $db      = new \app\model\NotifyTemplateDefault();
        $map     = [['id', '=', $id]];
        $channel = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($channel['param'] ?? []));
    }
}