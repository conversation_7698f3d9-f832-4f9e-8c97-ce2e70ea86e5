<?php

namespace app\controller\admin_api\v1;

use app\model\DeviceTradeNoteEveryday;
use app\common\tools\Excel;
use DateTime;
use Exception;
use think\facade\Db;

class YkyDeviceTradeNote extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new DeviceTradeNoteEveryday();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $db->where($map)->order(['trade_date' => 'DESC']);
        result($this->_list());
    }

    /**
     * 导入
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function import()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $file        = new Excel();
        $bid         = $this->get_bid();
        $param       = $this->params;
        $allowField  = ['sn_code' => '设备号', 'agent_account' => '代理账号', 'agent_name' => '代理名称', 'trade_date' => '日期', 'business_account' => '商家账号', 'business_name' => '商家名称', 'day_valid_user_count' => '有效用户数'];
        $arr         = $file->load($param['file'])->excelToArray($allowField);
        $db          = new DeviceTradeNoteEveryday();
        $insert_data = [];
        $dateTime    = new DateTime();
        $import_time = $dateTime->format('Y-m-d H:i:s.u');
        foreach ($arr as $key => $val) {
            $val['bid']         = $bid;
            $val['create_time'] = $import_time;
            $val['update_time'] = $import_time;
            $insert_data[]      = $val;
        }
        $insert_all = Db::name('device_trade_note_everyday')->data($insert_data)->limit(1000)->insertAll();
        $msg        = "成功导入" . count($insert_data) . "条";
        wr_log($msg);
        success($msg);
    }
}