<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\PayOrder as PayOrderModel;
use app\common\tools\Excel;
use Exception;
use Throwable;

class PayOrder extends BasicAdminApi
{
    /**
     *修复订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function repair()
    {
        $params             = $this->params;
        $guid               = $params['guid'];
        $bid                = $params['bid'];
        $pay_parameter_guid = $params['pay_parameter_guid'];
        $out_trade_no       = $params['out_trade_no'];
        if (empty($pay_parameter_guid)) {
            error('仅支持修复2019年6月15日后的订单');
        }
        $query_data = ['order_guid' => $guid, 'bid' => $bid, 'pay_parameter_guid' => $pay_parameter_guid, 'out_trade_no' => $out_trade_no];
        job()->set_job_name('Pay@query_order')->push_job($query_data);
        success('查单任务已提交');
    }

    /**
     *修复订单
     * @access public
     * @return void
     * @throws Exception|Throwable
     */
    public function retry_job()
    {
        $params                       = $this->params;
        $guid                         = $params['guid'];
        $bid                          = $params['bid'];
        $pay_parameter_guid           = $params['pay_parameter_guid'];
        $out_trade_no                 = $params['out_trade_no'];
        $db_pay_order                 = new PayOrderModel();
        $map                          = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['guid', '=', $guid],
        ];
        $order                        = $db_pay_order->where($map)->findOrFail();
        $job_data                     = $order['job_attach'];
        $queue_name                   = $job_data['queue'] ?? '';
        $job_data['data']['pay_time'] = $order['trade_time'];
        job()->set_queue_name($queue_name)->set_job_name($job_data['class'])->push_job($job_data['data']);
        success('已重发异步');
    }

    /**
     *修复订单
     * @access public
     * @return void
     * @throws Exception|Throwable
     */
    public function query()
    {
        $params       = $this->params;
        $guid         = $params['guid'];
        $bid          = $params['bid'];
        $out_trade_no = $params['out_trade_no'];
        $order_info   = [];
        try {
            $config     = get_config_by_bid($bid);
            $appid      = $config['weappid'];
            $weapp      = weixin($appid)::WeMiniShipping();
            $order_info = $weapp->query(['merchant_trade_no' => $out_trade_no]);
        } catch (Throwable|Exception $e) {
            error($e->getMessage());
        }
        result($order_info);
    }

    /**
     *所有订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $bid           = $this->get_bid();
        $params        = $this->params;
        $db_business   = new BusinessModel();
        $business_type = $db_business->get_business_type($bid);
        $map           = [];
        switch ($business_type) {
            case 2:
                //代理商账户看他名下数据
                $map[] = ['b.parent_guid', '=', $bid];
                break;
            case 1:
                //商家只看自己数据
                $map[] = ['po.bid', '=', $bid];
                break;
            default:
                break;
        }
        $join  = [
            ['business b', 'po.bid = b.guid'],
        ];
        $field = [
            'po.*',
            'b.account'                                 => 'business_account',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
        ];
        $db    = new PayOrderModel();
        if (!empty($params['_total'])) {
            //合计
            $field = [
                'count(1)'           => 'count',
                'sum(total_fee)/100' => 'sum',
            ];
            if (empty($map)) {
                $map = true; //如果没任何条件 会查询出来空数据
            }
            $data       = $db->alias('po')->where($map)->join($join)->field($field);
            $data       = $this->_find($data);
            $total_text = '合计金额: <b style="color: red">' . $data['count'] . '</b> 笔,共 <b style="color: red">' . number_format($data['sum'], 2) . '</b> 元';
            success($total_text);
        }
        $this->model = $db->alias('po')->join($join)->field($field)->where($map)->order(['po.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *导出订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export()
    {
        $admin_business_guid = ['f4e1db62-a77e-4f8f-d44d-fbb9eeee9b44', '26810245-d97e-81b6-c1cf-0215fd8f347c'];
        $bid                 = $this->get_bid();
        $map                 = [];
        if (!in_array($bid, $admin_business_guid)) {
            //agent+admin  账号 查看所有数据
            $db_business   = new BusinessModel();
            $business_type = $db_business->get_business_type($bid);
            if ($db_business->is_agent_type($business_type)) {
                //代理商账户看他名下数据
                $bids  = $db_business->where([['parent_guid', '=', $bid]])->column('guid');
                $map[] = [
                    ['po.bid', 'IN', $bids]
                ];
            } else {
                $map[] = [
                    ['po.bid', '=', $bid]
                ];
            }
        }
        $db   = new PayOrderModel();
        $join = [
            ['business b', 'po.bid = b.guid'],
        ];
        $data = $db->field(
            [
                'b.account',
                'b.business_name',
                'scene',
                'bill_number',
                'total_fee',
                'third_trade_no',
                'po.create_time',
                'trade_time',
                'po.status',
            ]
        )->alias('po')->join($join)->where($map)->order('po.create_time', 'DESC')->append(['export_status_text', 'export_total_fee_text']);
        $data = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'account'               => '商家账号',
            'business_name'         => '商家名称',
            'bill_number'           => '订单号',
            'third_trade_no'        => '交易单号',
            'export_total_fee_text' => '金额',
            'create_time'           => '下单时间',
            'trade_time'            => '支付时间',
            'export_status_text'    => '状态',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *商家流水
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function business_order()
    {
        $bid     = $this->get_bid();
        $map     = [['po.bid', '=', $bid]];
        $db_rule = new \app\model\Rule();
        if (!$db_rule->is_admin()) {
            $map[] = ['po.user_guid', '=', $this->get_user_guid()];
        }
        $join = [
            ['business b', 'po.bid = b.guid'],
            ['user u', 'po.user_guid = u.guid', 'LEFT'],
        ];
        $db   = new PayOrderModel();
        if (!empty($params['_total'])) {
            unset($this->params['_total']);
            $field      = [
                'count(1)'                        => 'count',
                'IFNULL(sum(po.total_fee)/100,0)' => 'sum',
            ];
            $data       = $db->alias('po')->join($join)->field($field)->where($map);
            $data       = $this->_find($data);
            $total_text = '合计:' . $data['count'] . '笔,共' . number_format($data['sum'], 2) . '元';
            success($total_text);
        }
        $field       = [
            'po.*',
            'u.account'                        => 'user_account',
            "CONCAT(u.name,'(',u.account,')')" => 'user',
        ];
        $this->model = $db->alias('po')->join($join)->field($field)->where($map)->order(['po.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *代理商后台看的流水
     * @access public
     * @return mixed
     * @throws Exception
     */

    public function agent_order()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $db_business = new BusinessModel();
        //代理商账户看他名下数据
        $bids = $db_business->where([['parent_guid', '=', $bid]])->column('guid');
        $map  = [
            ['po.bid', 'IN', $bids]
        ];
        $join = [
            ['business b', 'po.bid = b.guid'],
            ['user u', 'u.bid = b.parent_guid AND u.guid=b.parent_user_guid', 'LEFT'],
        ];
        $db   = new PayOrderModel();
        if (!empty($params['_total'])) {
            unset($this->params['_total']);
            $field      = [
                'count(1)'                        => 'count',
                'IFNULL(sum(po.total_fee)/100,0)' => 'sum',
            ];
            $data       = $db->alias('po')->join($join)->field($field)->where($map);
            $data       = $this->_find($data);
            $total_text = '合计:' . $data['count'] . '笔,共' . number_format($data['sum'], 2) . '元';
            success($total_text);
        }
        $field       = [
            'po.*',
            'b.account'                                 => 'business_account',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business',
            "CONCAT(u.name,'(',u.account,')')"          => 'service',
        ];
        $this->model = $db->alias('po')->join($join)->field($field)->where($map)->order(['po.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *导出代理订单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_agent_order()
    {
        $bid         = $this->get_bid();
        $db_business = new BusinessModel();
        //代理商账户看他名下数据
        $bids = $db_business->where([['parent_guid', '=', $bid]])->column('guid');
        $map  = [
            ['po.bid', 'IN', $bids]
        ];
        $db   = new PayOrderModel();
        $join = [
            ['business b', 'po.bid = b.guid'],
            ['user u', 'u.bid = b.parent_guid AND u.guid=b.parent_user_guid', 'LEFT'],
        ];
        $data = $db->field(
            [
                'b.account',
                'b.business_name',
                'scene',
                'bill_number',
                'total_fee',
                'third_trade_no',
                'po.create_time',
                'trade_time',
                'po.status',
                'u.account' => 'user_account',
            ]
        )->alias('po')->join($join)->where($map)->order('po.create_time', 'DESC')->append(['export_status_text', 'export_total_fee_text']);
        $data = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'account'               => '商家账号',
            'business_name'         => '商家名称',
            'user_account'          => '工号',
            'bill_number'           => '订单号',
            'third_trade_no'        => '交易单号',
            'export_total_fee_text' => '金额',
            'create_time'           => '下单时间',
            'trade_time'            => '支付时间',
            'export_status_text'    => '状态',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }
}