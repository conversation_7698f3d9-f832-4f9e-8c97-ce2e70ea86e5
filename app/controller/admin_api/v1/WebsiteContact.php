<?php

namespace app\controller\admin_api\v1;

use app\model\WebsiteContact as WebsiteContactModel;
use app\common\tools\Excel;
use DateTime;
use Exception;
use think\facade\Db;

class WebsiteContact extends BasicAdminApi
{
    /**
     *联系方式列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new WebsiteContactModel();
        $map         = [
            ['delete_time', 'NULL', NULL]
        ];
        $this->model = $db->where($map)->order(['id' => 'DESC']);
        result($this->_list());
    }

    /**
     *导出
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function export_note()
    {
        $db   = new WebsiteContactModel();
        $data = $db->whereNotNull('delete_time');
        $data = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'id'          => '序号',
            'url'         => '网址',
            'email'       => '邮箱',
            'facebook'    => 'Facebook',
            'status'      => '状态',
            'memo'        => '结果',
            'create_time' => '创建时间',
            'update_time' => '更新时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    /**
     *导入
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function import()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $file       = new Excel();
        $param      = $this->params;
        $allowField = ['url' => '网址'];
        $arr        = $file->load($param['file'])->excelToArray($allowField);
        $db         = new WebsiteContactModel();
        $url_array  = [];
        foreach ($arr as $key => $val) {
            $url = $val['url'];
            if (!empty($url)) {
                $url_array[] = trim(strtolower($url));
            }
        }
        $all_url_array = array_unique($url_array);//先粗略去重
        $preg          = '/^http(s):\\/\\//';
        $url_preg      = "/^http(s)?:\\/\\/.+/";
        $domain_array  = [];
        foreach ($all_url_array as $key => $url) {
            //不是以http或者https开头的则自动补全
            $url = (!preg_match($preg, $url)) ? 'http://' . $url : $url;
            //自动补全之后,判断是否合法的url
            if (preg_match($url_preg, $url)) {
                $url_info       = parse_url($url);
                $domain_array[] = $url_info['scheme'] . '://' . $url_info['host'];
            }
        }
        $domain_array     = array_unique($domain_array); //域名再次去重
        $output_url_array = [];
        $url_array_chunk  = array_chunk($domain_array, 1000); //使用域名集合
        foreach ($url_array_chunk as $url_array) {
            $map              = [
                ['url', 'IN', $url_array],
                ['delete_time', 'NULL', NULL],
            ];
            $urls             = $db->where($map)->column('url');
            $output_url_array = array_merge($output_url_array, array_diff($url_array, $urls));
        }
        $insert_data = [];
        $dateTime    = new DateTime();
        $import_time = $dateTime->format('Y-m-d H:i:s.u');
        foreach ($output_url_array as $url) {
            $insert_data[] = [
                'url'         => $url,
                'create_time' => $import_time,
                'update_time' => $import_time
            ];
        }
        $insert_all = Db::name('website_contact')->data($insert_data)->limit(1000)->insertAll();
        job()->set_job_name('Tools@push_website_contact_job')->push_job();
        $msg = "成功导入" . count($output_url_array) . "条";
        wr_log($msg);
        success($msg);
    }

    /**
     *清空
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function drop()
    {
        $dateTime    = new DateTime();
        $delete_time = $dateTime->format('Y-m-d H:i:s.u');
        $db          = new WebsiteContactModel();
        $map         = [['delete_time', 'NULL', NULL]];
        $update_data = ['delete_time' => $delete_time];
        $db::update($update_data, $map);
        success('清空成功');
    }
}
 