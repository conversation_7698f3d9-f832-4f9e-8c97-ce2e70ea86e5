<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2018/8/6
 * Time: 20:51
 */

namespace app\controller\admin_api\v1;

use app\model\Area;
use app\common\service\SmsService;
use app\common\service\TokenService;
use app\middleware\BasicMiddleware;
use Exception;

class Common extends BasicAdminApi
{
    public function get_copyright()
    {
        $bid              = $this->params['bid'];
        $basic_middleware = new BasicMiddleware();
        $access_token     = $basic_middleware->get_admin_access_token($this->request);
        if ($access_token) {
            $jwt = TokenService::verify($access_token);
            $bid = $jwt['bid'];
        }
        $config = get_config_by_bid($bid);
        $data   = [
            'copy_right_url'    => $config['copy_right_url'],
            'copy_right_name'   => $config['copy_right_name'],
            'copy_right_footer' => $config['copy_right_footer'],
        ];
        result($data);
    }

    public function ip_to_area_id()
    {
        $db = new Area();
        result($db->ip_to_area_id());
    }

    /**
     *地区
     * @access public
     * @return void
     * @throws Exception
     */
    public function area()
    {
        $pid = input('pid', 1);
        $db  = new Area();
        $map = [['pid', '=', $pid]];
        result($db->field(['id', 'name'])->where($map)->order('id')->select()->toArray());
    }

    /**
     *地区
     * @access public
     * @return void
     * @throws Exception
     */
    public function district()
    {
        $db            = new Area();
        $list          = $db->field(['id', 'pid', 'name', 'level'])->select()->toArray();
        $province_list = $this->build_list($list, 1);
        foreach ($province_list as $i => $province) {
            $city_list = $this->build_list($list, $province['id']);
            foreach ($city_list as $j => $city) {
                $district_list         = $this->build_list($list, $city['id']);
                $city_list[$j]['list'] = $district_list;
            }
            $province_list[$i]['list'] = $city_list;
        }
        result($province_list);
    }

    /**
     *创建列表
     * @access protected
     * @param array $list
     * @param integer $pid
     * @return mixed
     * @throws Exception
     */
    protected function build_list($list, $pid)
    {
        $data = [];
        foreach ($list as $key => $val) {
            if ($val['pid'] == $pid) {
                $data[] = ['id' => $val['id'], 'name' => $val['name']];
            }
        }
        return $data;
    }
}