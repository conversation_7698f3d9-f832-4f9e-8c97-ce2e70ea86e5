<?php

namespace app\controller\admin_api\v1;

use app\model\Jobs;
use app\model\WeappCommitNote;
use app\model\WeappSubmitNote;
use Exception;
use think\facade\Db;

class Queue extends BasicAdminApi
{
    /**
     *队列
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function group()
    {
        $field       = [
            'queue',
            "JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job'))"                                      => 'job_name',
            "substring_index(JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job')),'controller\\\\',-1)" => 'simple_job_name',
            "count(1)"                                                                         => 'count',
        ];
        $this->model = $this->model->field($field)->group(['queue', 'JSON_UNQUOTE(JSON_EXTRACT(payload,\'$.job\'))'])->order(['queue' => 'DESC', 'count' => 'DESC']);
        result($this->_list());
    }

    /**
     *列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $field       = [
            '*',
            "substring_index(JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job')),'@',-1)" => 'simple_job_name',
            "JSON_UNQUOTE(JSON_EXTRACT(payload,'$.job'))"                         => 'job_name',
            "JSON_UNQUOTE(JSON_EXTRACT(payload,'$.data'))"                        => 'payload',
        ];
        $this->model = $this->model->field($field)->order(['created_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *更改队列状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_status()
    {
        $params = $this->params;
        // 验证表单数据
        $scene = (!empty($params['id'])) ? 'single' : 'batch';
        $this->validate($params, '.' . $scene);
        $queue_service = job();
        $action        = $params['action'];
        $action_name   = $params['action_name'];
        switch ($scene) {
            case 'batch':
                $queue_service = $queue_service->set_queue_name($params['queue'])->set_job_name($params['job_name']);
                break;
            case 'single':
                $queue_service = $queue_service->set_queue_id($params['id']);
                break;
            default:
                break;
        }
        $result = $queue_service->$action();
        if ($result) {
            success('成功' . $action_name . '了' . $result . '条任务');
        } else {
            error('没有要' . $action_name . '的条任务');
        }
    }

    /**
     *获取微信商户号状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_wechat_balance_status()
    {
        $key                   = 'wechat_balance_status:' . $this->get_bid();
        $wechat_balance_status = cache($key) ? 1 : 0;
        result(['wechat_balance_status' => $wechat_balance_status]);
    }

    /**
     *重启微信队列
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function restart_wechat_jobs()
    {
        $bid               = $this->get_bid();//coupon_exchange,point_exchange,send_red_packet
        $wechat_queues     = config('app.wechat_queues');
        $wechat_queues_arr = explode(',', $wechat_queues);
        foreach ($wechat_queues_arr as $key => $val) {
            $wechat_queues_arr[$key] = '%' . $val . '%';
        }
        $db    = new Jobs();
        $map   = [
            ['payload->job', 'like', $wechat_queues_arr, 'OR'],
            ['queue', 'in', ['default', 'pause', 'failed']],
        ];
        $map[] = Db::raw("JSON_EXTRACT(payload->>'$.data','$.bid') = '$bid'");
        $key   = 'wechat_balance_status:' . $bid;
        cache($key, null);
        $count = $db->where($map)->whereTime('available_at', '-3 day')->update([
            'queue'        => 'default',
            'attempts'     => 0,
            'reserved'     => 0,
            'reserved_at'  => null,
            'available_at' => time(),
            'created_at'   => time(),
        ]);
        success('成功重启了' . $count . '条微信转账任务');
    }

    /**
     *添加任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function do_job()
    {
        $params = $this->params;
        $job_id = intval($params['job_id']);
        $arg    = isset($params['params']) ? $params['params'] : '';
        // <option value="1">批量发布代码商城小程序</option>
        // <option value="2">批量提交审核商城小程序</option>
        // <option value="3">批量上线审核通过小程序</option>
        // <option value="4">发布大浪小程序至体验版</option>
        // <option value="5">通过APPID重新发布最新版</option>

        switch ($job_id) {
            case 1:
                $db_business    = new \app\model\Business();
                $map            = [
                    ['mall_store_id', '>', 0],
                    ['expired_time', '>', format_timestamp()],
                ];
                $business_array = $db_business->field(['guid'])->where($map)->select()->toArray();
                foreach ($business_array as $key => $val) {
                    $data = ['bid' => $val['guid']];
                    job()->set_job_name('Weapp@commit_to_last_version')->push_job($data);
                }
                break;
            case 2:
                $db_weapp_commit_note = new WeappCommitNote();
                $last_create_time     = date("Y-m-d H:i:s", strtotime("-2 day")); //只处理最近2天数据
                $map                  = [
                    ['status', '=', 1],
                    ['create_time', '>', $last_create_time]
                ]; //代码提交成功 等待提审
                $weapp_array          = $db_weapp_commit_note->field(['guid', 'appid'])->where($map)->select()->toArray();
                foreach ($weapp_array as $key => $val) {
                    $data = ['appid' => $val['appid']];
                    job()->set_job_name('Weapp@submit_to_last_version')->push_job($data);
                }
                break;
            case 3:
                $db_weapp_submit_note = new WeappSubmitNote();
                $map                  = [['status', '=', 0]];
                $weapp_array          = $db_weapp_submit_note->field(['guid', 'appid'])->where($map)->select()->toArray();
                foreach ($weapp_array as $key => $val) {
                    $data = ['appid' => $val['appid']];
                    job()->set_job_name('Weapp@release_to_last_version')->push_job($data);
                }
                break;
            case 4:
                $db_business    = new \app\model\Business();
                $mall_store_id  = $arg ? intval($arg) : 1;
                $map            = [['mall_store_id', 'IN', [$mall_store_id]]];
                $business_array = $db_business->field(['guid'])->where($map)->select()->toArray();
                foreach ($business_array as $key => $val) {
                    $data = ['bid' => $val['guid']];
                    job()->set_job_name('Weapp@commit_to_last_version')->push_job($data);
                }
                break;
            case 5:
                if (empty($arg)) {
                    error('请输入小程序APPID');
                }
                $db_parameter = new \app\model\Parameter();
                $bid          = $db_parameter->get_bid_by_appid($arg);
                if (empty($bid)) {
                    error('当前appid无法查找到商家');
                }
                $data = ['bid' => $bid];
                job()->set_job_name('Weapp@commit_to_last_version')->push_job($data);
                break;
            default:
                error('暂不支持该任务');
        }
        success('操作成功');
    }

    protected function initialize()
    {
        $this->model = new Jobs();
        parent::initialize();
    }
}