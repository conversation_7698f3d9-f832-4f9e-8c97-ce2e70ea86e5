<?php

namespace app\controller\admin_api\v1;


use app\model\YkyPlusMemberOrder as YkyPlusMemberOrderModel;
use app\common\tools\Excel;
use Exception;

class YkyPlusMemberOrder extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyPlusMemberOrderModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $map         = [
            ['bid', '=', $bid],
            ['delete_time', 'null', null]
        ];
        $this->model = $db->where($map)->order(['start_time' => 'DESC', 'end_time' => 'DESC']);
        result($this->_list());
    }

    public function refresh_yky_plus_member_order_statistics()
    {
        $bid                      = $this->get_bid();
        $params                   = $this->params;
        $order_guid               = $params['guid'];
        $db_yky_plus_member_order = new YkyPlusMemberOrderModel();
        $db_yky_plus_member_order->refresh_yky_plus_member_order_statistics($bid, $order_guid);
        success('数据刷新成功!');
    }

    public function export()
    {
        $db    = new YkyPlusMemberOrderModel();
        $bid   = $this->get_bid();
        $map   = [['bid', '=', $bid]];
        $model = $db->where($map)->order(['start_time' => 'DESC']);
        $data  = $this->_select($model);
        if (empty($data)) {
            error('没有要导出的数据~');
        }
        $header = [
            'card_id'         => '卡号',
            'start_time'      => '开始时间',
            'end_time'        => '结束时间',
            'bill_number'     => '单据号',
            'consume_times'   => '消费次数',
            'consume_money'   => '消费金额',
            'add_value_times' => '充值次数',
            'add_value_money' => '充值金额',
            'coupon_send_num' => '累计发券张数',
            'create_time'     => '创建时间',
            'update_time'     => '更新时间',

        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }
}