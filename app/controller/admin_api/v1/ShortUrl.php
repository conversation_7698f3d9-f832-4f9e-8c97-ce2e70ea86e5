<?php

namespace app\controller\admin_api\v1;

use app\common\service\UrlService;
use app\model\ShortUrl as ShortUrlModel;
use Exception;

class ShortUrl extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function long_to_short_url()
    {
        $params    = $this->params;
        $long_url  = $params['url'];
        $short_url = UrlService::long_to_short($long_url);
        $full_url  = UrlService::get_full_url($short_url);
        result(['short_url' => $full_url]);
    }

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->model = new ShortUrlModel();
        result($this->_list());
    }

    /**
     * 添加
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $db          = new ShortUrlModel();
        $this->model = $db;
        $db->add($this->params);
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db     = new ShortUrlModel();
        $params = $this->params;
        $map    = [
            ['code', '=', $params['code']],
        ];
        unset($params['code']);
        $db::update($params, $map);
        success('编辑成功');
    }


}