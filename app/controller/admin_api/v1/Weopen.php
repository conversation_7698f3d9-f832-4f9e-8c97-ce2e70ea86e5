<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\WeappTemplateDraftList;
use app\model\WeappTemplateList;
use Exception;
use think\facade\Db;

class Weopen extends BasicAdminApi

{
    /**
     * 模板库列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function template_list()
    {
        $this->model = new WeappTemplateList();
        //        if (!empty($this->params['value'])) {
        //            $this->model->sync_template_list();
        //        }
        $component_appid = weixin()::get_component_appid();
        $map             = [
            ['component_appid', '=', $component_appid]
        ];
        $this->model     = $this->model->where($map)->order(['template_id' => 'DESC']);
        result($this->_list());
    }

    /**
     * 同步模板库
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function sync_template_list()
    {
        $db     = new WeappTemplateList();
        $result = $db->sync_template_list();
        if ($result) {
            success('模板列表同步成功');
        } else {
            error('模板列表同步失败:' . $db->getError());
        }
    }

    /**
     * 模板列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function template_draft_list()
    {
        //        if (!empty($this->params['value'])) {
        //            $this->sync_template_draft_list();
        //        }
        $this->model     = new WeappTemplateDraftList();
        $component_appid = weixin()::get_component_appid();
        $map             = [['component_appid', '=', $component_appid]];
        $this->model     = $this->model->where($map);
        result($this->_list());
    }

    /**
     * 同步模板列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function sync_template_draft_list()
    {
        $wechat          = weixin()::WeOpenMiniApp();
        $component_appid = weixin()::get_component_appid();
        $result          = $wechat->getTemplateDraftList();
        $draft_list      = $result['draft_list'];
        $db              = new WeappTemplateDraftList();
        // 启动事务
        Db::startTrans();
        try {
            $map = [['component_appid', '=', $component_appid]];
            $db->where($map)->delete();
            foreach ($draft_list as $key => $val) {
                $draft_list[$key]['component_appid'] = $component_appid;
                $draft_list[$key]['create_time']     = date('Y-m-d H:i:s', $val['create_time']); //暂时手动处理
            }
            $save = $db->saveAll($draft_list);
            // 提交事务
            Db::commit();
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            error('草稿箱同步失败:' . $e->getMessage());
        }
        success('草稿箱同步成功');
    }

    /**
     * 删除模板
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del_template()
    {
        $template_id_list = explode(',', $this->params['template_id']);
        $template_id_num  = count($template_id_list);
        if ($template_id_num > 1) {
            $later = 0;
            foreach ($template_id_list as $template_id) {
                $later    += 30;
                $job_data = ['template_id' => $template_id];
                job()->set_job_name('Weopen@del_template')->push_job($job_data, $later);
            }
            job()->set_job_name('Weopen@sync_template_list')->push_job([], $later + 10);
            success($template_id_num . '个模板删除任务提交成功!');
        } else {
            foreach ($template_id_list as $template_id) {
                $wechat = weixin()::WeOpenMiniApp();
                $wechat->deleteTemplate($template_id);
            }
            $db     = new WeappTemplateList();
            $result = $db->sync_template_list();
            success('删除成功' . $template_id_num . '个模板');
        }
    }

    /**
     * 草稿箱添加到模板库
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add_to_template()
    {
        $wechat                   = weixin()::WeOpenMiniApp();
        $params                   = $this->params;
        $draft_id                 = $params['draft_id'];
        $source_miniprogram_appid = $params['source_miniprogram_appid'];
        $result                   = $wechat->addToTemplate($draft_id);
        $db_weapp_template_list   = new WeappTemplateList();
        $db_weapp_template_list->sync_template_list();
        $msg = '添加到模板库成功';
        if (in_array($source_miniprogram_appid, ['wx43cbf6de6c97b2b7', 'wx49310464deda8fe4', 'wx072d5a1354934b21'])) {            //仅当是测试小程序时，自动提交到最后一个版本
            $component_appid = weixin()::get_component_appid();
            $map             = [
                ['component_appid', '=', $component_appid],
                ['source_miniprogram_appid', '=', $source_miniprogram_appid]
            ];
            $template_id     = $db_weapp_template_list->where($map)->max('template_id');
            job()->set_job_name('Weapp@auto_commit_to_last_version')->push_job(['template_id' => $template_id, 'source_miniprogram_appid' => $source_miniprogram_appid]);
        }
        success($msg);
    }
}