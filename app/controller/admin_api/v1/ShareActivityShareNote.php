<?php

namespace app\controller\admin_api\v1;

use app\model\ShareActivityShareNote as ShareActivityShareNoteModel;

class ShareActivityShareNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new ShareActivityShareNoteModel();

        $map = [
            ['sasn.bid', '=', $this->get_bid()]
        ];

        $join = [
            ['share_activity sa', 'sasn.share_activity_guid = sa.guid AND sa.bid = sasn.bid']
        ];

        $field = [
            'sasn.*',
            'sa.title as activity_title',
        ];

        $this->model = $this->model
            ->alias('sasn')
            ->join($join)
            ->where($map)
            ->field($field);

        result($this->_list());
    }
}
