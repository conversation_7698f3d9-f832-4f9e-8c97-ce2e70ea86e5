<?php

namespace app\controller\admin_api\v1;

use app\model\WebsocketUser;
use Exception;

class Push extends BasicAdminApi
{
    /**
     * websocket_user
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function websocket_user()
    {
        $db     = new WebsocketUser();
        $join   = [
            ['business b', 'w.bid = b.guid', 'LEFT'],
            ['user u', 'w.bid = u.bid AND w.user_guid = u.guid ', 'LEFT'],
            ['member m', 'w.bid = m.bid AND w.member_guid = m.guid ', 'LEFT'],
        ];
        $field  = [
            'w.*',
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            'u.account'                                 => 'user_account',
            'u.name'                                    => 'user_name',
            'm.card_id'                                 => 'member_card_id',
            'm.name'                                    => 'member_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
            "CONCAT(u.account,'(',u.name,')')"          => 'user_info',
            "CONCAT(m.card_id,'(',m.name,')')"          => 'member_info',
        ];
        $map    = [];
        $order  = ['w.status' => 'DESC', 'w.last_login_time' => 'DESC'];
        $params = $this->params;
        if (empty($params['w.last_login_time'])) {
            $begin_date = date('Y-m-d', strtotime('-30 days'));
            $map        = [['w.last_login_time', '> time', $begin_date]];
        }
        $this->model = $db->alias('w')->join($join)->field($field)->where($map)->order($order);
        result($this->_list());
    }

    /**
     *push_websocket
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function push_websocket()
    {
        $params  = $this->params;
        $uid     = $params['uid'];
        $channel = $params['channel'];
        $data    = $params['data'];
        $db      = new WebsocketUser();
        $data    = tools()::is_json($data) ? json_decode($data, true) : ['action' => 'alert', 'msg' => $data];
        $result  = $db->push($data, $uid, $channel);
        if ($result === true) {
            success('推送成功!');
        } else {
            error(is_string($result) ? $result : '推送失败!');
        }
    }
}