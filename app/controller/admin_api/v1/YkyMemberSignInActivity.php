<?php

namespace app\controller\admin_api\v1;

use app\model\YkyMemberSignInActivity as YkyMemberSignInActivityModel;

class YkyMemberSignInActivity extends BasicAdminApi
{

    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new YkyMemberSignInActivityModel();
        $map         = [['bid', '=', $this->get_bid()]];
        $this->model = $this->model->where($map);
        result($this->_list());
    }

    /**
     * 列表
     * @access 门店列表
     * @return void
     * @throws \Exception
     */
    public function member_group_list()
    {
        $params                               = $this->params;
        $db                                   = new \app\model\YkyMemberGroup();
        $db_yky_member_sign_in_activity_model = new YkyMemberSignInActivityModel();
        $bid                                  = $this->get_bid();
        $map                                  = [
            ['bid', '=', $bid],
        ];
        $list                                 = $db->where($map)->field(['group_name', 'guid' => 'member_group_guid'])->order(['group_name' => 'ASC'])->select()->toArray();
        $guid                                 = $params['guid'] ?? '';
        if ($guid) {
            //本规则的级别默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_yky_member_sign_in_activity_model->where($map)->field(['member_group_guid'])->find();
            if (is_array($rule['member_group_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['member_group_guid'], $rule['member_group_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的级别禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['member_group_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['member_group_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['member_group_guid'], $val['member_group_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }
}
