<?php

namespace app\controller\admin_api\v1;

use app\model\PayBrokerageNote as PayBrokerageNoteModel;
use Exception;

class PayBrokerageNote extends BasicAdminApi
{
    /**
     *所有分润记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function all_brokerage_note()
    {
        $map         = [
            ['pbn.type', '=', 2]
        ];
        $db_business = new \app\model\Business();
        //$business_type = $db_business->get_business_type($this->get_bid());
        $business_type = 2;
        if ($business_type == 2) {
            $map[] = ['pbn.parent_bid', '=', $this->get_bid()];
        }
        $join        = [
            ['business b', 'pbn.bid = b.guid'],
            ['business b2', 'pbn.parent_bid = b2.guid'],
        ];
        $field       = [
            'pbn.*',
            'b.account'        => 'business_account',
            'b.business_name'  => 'business_name',
            'b2.account'       => 'parent_business_account',
            'b2.business_name' => 'parent_business_name',
        ];
        $db          = new PayBrokerageNoteModel();
        $order       = ['pbn.create_time' => 'DESC'];
        $this->model = $db->alias('pbn')->join($join)->order($order)->field($field)->where($map);
        result($this->_list());
    }

    /**
     *我的分润记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my_brokerage_note()
    {
        $map         = [
            ['pbn.bid', '=', $this->get_bid()],
            ['pbn.type', '=', 2]
        ];
        $join        = [
            ['business b', 'pbn.bid = b.guid'],
            ['business b2', 'pbn.parent_bid = b2.guid'],
        ];
        $field       = [
            'pbn.*',
            'b.account'        => 'business_account',
            'b.business_name'  => 'business_name',
            'b2.account'       => 'parent_business_account',
            'b2.business_name' => 'parent_business_name',
        ];
        $db          = new PayBrokerageNoteModel();
        $order       = ['pbn.create_time' => 'DESC'];
        $this->model = $db->alias('pbn')->join($join)->order($order)->field($field)->where($map);
        result($this->_list());
    }

    /**
     *申请分润
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        $db     = new PayBrokerageNoteModel();
        $params = $this->params;
        $guid   = $params['guid'];
        $map    = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
            ['status', '=', 0]
        ];
        $count  = $db->where($map)->count();
        if (!$count) {
            error('操作失败,请重试');
        }
        $update_data = ['status' => 1];
        $db::update($update_data, $map);
        $result = $db::update($update_data, $map);
        if ($result) {
            success('申请成功');
        } else {
            error('操作失败,请重试');
        }
    }

    /**
     *审核分润
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $db     = new PayBrokerageNoteModel();
        $params = $this->params;
        $guid   = $params['guid'];
        $bid    = $params['bid'];
        $map    = [
            ['bid', '=', $bid],
            ['parent_bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
            ['status', '=', 1]
        ];
        $count  = $db->where($map)->count();
        if (!$count) {
            error('操作失败,请重试');
        }
        $update_data = ['status' => 2];
        $result      = $db::update($update_data, $map);
        if ($result > 0) {
            success('结算成功');
        } else {
            error('操作失败,请重试');
        }
    }
}