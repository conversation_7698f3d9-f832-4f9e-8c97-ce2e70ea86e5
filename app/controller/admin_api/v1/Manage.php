<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use app\model\GoodsOrder;
use app\model\User as UserModel;

class Manage extends BasicAdminApi
{
    public function get_qrcode()
    {
        $bid                    = $this->get_bid();
        $db_coupon              = new Coupon();
        $verify_url_qrcode_list = $db_coupon->get_verify_url_qrcode_list($bid);
        $data                   = ['qrcode_url' => $verify_url_qrcode_list['qrcode_url']];
        result($data);
    }

    public function home_data()
    {
        $bid = $this->get_bid();
        //        $config        = get_config_by_bid($bid);
        $db_business   = new \app\model\Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);

        $db_user       = new UserModel();
        $child_user_id = $db_user->getChildUserIdArray();

        $db_goods_order      = new GoodsOrder();
        $map                 = [
            ['go.bid', '=', $bid],
            ['go.delete_time', 'NULL', null], //过滤已删除订单
            ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
            ['go.status', 'NOT IN', [-1, -2, -3]], // 不展示待支付,已取消,已退款订单
        ];
        $join                = [
            ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'],
        ];
        $total_count         = $db_goods_order->join($join)->alias('go')->where($map)->count();
        $map                 = [
            ['go.bid', '=', $bid],
            ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
            ['go.status', 'NOT IN', [-1, -2, -3]], // 不展示待支付,已取消,已退款订单
            ['go.create_time', '>=', date('Y-m-d')],
            ['go.delete_time', 'NULL', null], //过滤已删除订单
        ];
        $today_count         = $db_goods_order->join($join)->alias('go')->where($map)->count();
        $map                 = [
            ['go.bid', '=', $bid],
            ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $child_user_id], // 只显示自己范围内的订单
            ['go.status', '=', 0],
            ['go.delete_time', 'NULL', null], //过滤已删除订单
        ];
        $wait_send_out_count = $db_goods_order->join($join)->alias('go')->where($map)->count();
        $card_list           = [
            [
                'number' => $today_count,
                'title'  => '今日订单',
                //                'auth_name' => '卡券',
                'url'    => '/pages/order/list',
            ],
            [
                'number' => $wait_send_out_count,
                'title'  => '待发货',
                //                'auth_name' => '余额',
                'url'    => '/pages/order/list?status=0',
            ],
            [
                'number' => $total_count,
                'title'  => '累计订单',
                //                'auth_name' => '余额',
                'url'    => '/pages/order/list',
            ],
            //            [
            //                'number'    => 77,
            //                'title'     => '收藏夹',
            //                'auth_name' => '收藏夹',
            //                'url'       => '/pages/favorite/favorite',
            //            ],
        ];
        $active_code_url     = (string)url('admin/coupon_send_note/active', [], false, true);
        $menu_list           = [
            [
                'type'  => 2, // 1 网页 2 小程序页面
                'name'  => '订单管理',
                //                'auth_path' => '订单管理',
                'image' => '/images/order.png',
                'url'   => '/pages/order/list?status=0',
            ],
            [
                'type'  => 2, // 1 网页 2 小程序页面
                'name'  => '卡券查询',
                //                'auth_path' => '扫码核销',
                'image' => '/images/search.png',
                'url'   => '/pages/info/info',
            ],
            [
                'type'  => 2, // 1 网页 2 小程序页面
                'name'  => '扫码核销',
                //                'auth_path' => '扫码核销',
                'image' => '/images/scan.png',
                'url'   => '/pages/scan/scan',
            ],
            [
                'type'  => 2, // 1 网页 2 小程序页面
                'name'  => '输号核销',
                //                'auth_path' => '扫码核销',
                'image' => '/images/verify.png',
                'url'   => '/pages/verify/index',
            ],
            [
                'type'  => 2, // 1 网页 2 小程序页面
                'name'  => '提货二维码',
                //                'auth_path' => '扫码核销',
                'image' => '/images/qrcode.png',
                'url'   => '/pages/qrcode/qrcode',
            ],
            //            [
            //                'type'  => 1, // 1 网页 2 小程序页面
            //                'name'  => '付费激活',
            //                //                'auth_path' => '扫码核销',
            //                'image' => '/images/verify.png',
            //                'url'   => $active_code_url,
            //            ],
        ];
        $db                  = new \app\model\Notice();
        $notice_list         = $db->get_notice_list();
        $data                = [
            'card_list'       => $card_list,
            'card_list_width' => (1 / count($card_list)) * 100,
            'menu_list'       => $menu_list,
            'notice_list'     => $notice_list,
            'title'           => $business_info['business_name'] . '(后台)'
        ];
        result($data);

    }

}