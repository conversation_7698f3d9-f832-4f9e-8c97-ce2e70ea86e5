<?php


namespace app\controller\admin_api\v1;


use app\model\XmfOrder;
use app\common\tools\Excel;

class XiaoMiFeng extends BasicAdminApi
{
    public function export_order_list()
    {
        $bid   = $this->get_bid();
        $map   = [
            ['xo.bid', '=', $bid],
            ['xo.status', '=', 1]
        ];
        $join  = [
            ['yky_member ym', 'xo.yky_member_guid = ym.guid AND xo.bid=ym.bid'],
        ];
        $field = [
            'xo.*',
            'ym.true_name',
            'ym.card_id',
            'ym.mobile',
        ];
        $db    = new XmfOrder();
        $order = ['xo.create_time' => 'DESC'];
        $data  = $db->alias('xo')->join($join)->field($field)->order($order)->where($map);
        $data  = $this->_select($data);
        if (!$data) {
            error('没有要导出的数据~');
        }
        $header = [
            'order_id'     => '订单号',
            'card_id'      => '卡号',
            'true_name'    => '姓名',
            'mobile'       => '手机号',
            'dec_money'    => '兑换金额',
            'after_amount' => '实际金额',
            'order_amount' => '兑换枚数',
            'result'       => '备注',
            'create_time'  => '创建时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $data);
    }

    public function order_list()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        $db     = new XmfOrder();
        $join   = [
            ['yky_member ym', 'xo.yky_member_guid = ym.guid AND xo.bid=ym.bid'],
        ];
        $map    = [
            ['xo.bid', '=', $bid],
            ['xo.status', '=', 1]
        ];
        if (!empty($params['_total'])) {
            //合计
            $field      = [
                'count(1)'             => 'count',
                'sum(xo.order_amount)' => 'sum',
            ];
            $data       = $db->alias('xo')->join($join)->field($field)->where($map);
            $data       = $this->_find($data);
            $total_text = '合计兑换:<b>' . $data['count'] . '</b>笔,共<b>' . number_format($data['sum'], 2) . '</b>元';
            success($total_text);
        }
        $order       = ['xo.create_time' => 'DESC'];
        $field       = [
            'xo.*',
            'ym.true_name',
            'ym.card_id',
            'ym.mobile',
        ];
        $this->model = $db->alias('xo')->join($join)->field($field)->order($order)->where($map);
        result($this->_list());
    }
}