<?php

namespace app\controller\admin_api\v1;

use app\model\FileUploadNote;
use Exception;
use Storage\Storage;

class File extends BasicAdminApi
{
    /**
     *base64上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function upload_base64()
    {
        $params = $this->params;
        $this->request->__set('_base64', trim($params['editor']));
//        $upload      = Storage::local();
        $upload      = Storage::driver($this->get_storage_type());
        $file        = $upload->upload();
        $file['url'] = tools()::replace_readonly_to_www($file['url']);
        return $upload ? response('<img src="' . $file['url'] . '" alt="" />')->contentType('text/html') : '';
    }

    /**
     *上传
     * @access public
     * @return void
     * @throws Exception
     */
    public function upload()
    {
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        if ($upload !== false) {
            $file['url'] = tools()::replace_readonly_to_www($file['url']);
            result(['filePath' => $file['url']]);
        } else {
            error('上传失败');
        }
    }

    /**
     *获取文件驱动
     * @access protected
     * @return mixed
     * @throws Exception
     */
    protected function get_storage_type()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        if (isset($params['local']) && $params['local'] == 1) {
            return 'local';
        }
        $config = get_config_by_bid($bid);
        return $config['storage_type'];
    }

    /**
     *文件列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db   = new FileUploadNote();
        $map  = [['bid', '=', $this->get_bid()]];
        $list = $db->field(['upload_time' => 'datetime', 'url' => 'filename'])->where($map)->order('upload_time', 'DESC')->limit(100)->select()->toArray();
        foreach ($list as $key => $val) {
            $list[$key]['dir_path'] = '';
            $list[$key]['filesize'] = 1;
            $list[$key]['filetype'] = 'jpg';
            $list[$key]['has_file'] = false;
            $list[$key]['is_dir']   = false;
            $list[$key]['is_photo'] = true;
        }
        return json([
            'current_dir_path' => '',
            'current_url'      => '',
            'moveup_dir_path'  => '',
            'total_count'      => count($list),
            'file_list'        => $list
        ]);
    }

    /**
     *移动端上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function uploadifive()
    {
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        if ($upload !== false) {
            $file['url'] = tools()::replace_readonly_to_www($file['url']);
            $msg         = [
                'error'   => 0,
                'message' => 'success',
                'url'     => $file['url'],
            ];
        } else {
            $msg = [
                'error'   => -1,
                'message' => '上传失败'
            ];
        }
        return json($msg);
    }
}