<?php

namespace app\controller\admin_api\v1;

use app\model\User as UserModel;
use Exception;
use think\facade\Db;

class ExpressConfig extends BasicAdminApi
{

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\ExpressConfig();
        $map         = [
            ['bid', '=', $this->get_bid()],
            ['delete_time', 'null', null],
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     *添加卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (!empty($params['user_guid_json'])) {
            $params['user_guid_json'] = explode(',', $params['user_guid_json']);
        } else {
            unset($params['user_guid_json']);
        }
        $db = new \app\model\ExpressConfig();
        $db->add($params);
        success('添加成功');
    }

    /**
     *编辑卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        if (!empty($params['user_guid_json'])) {
            $params['user_guid_json'] = explode(',', $params['user_guid_json']);
        } else {
            unset($params['user_guid_json']);
        }
        $db = new \app\model\ExpressConfig();
        $db->edit($params);
        success('编辑成功');
    }

    /**
     *编辑卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $guid        = $params['guid'];
        $db          = new \app\model\ExpressConfig();
        $update_data = ['delete_time' => format_timestamp()];
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $db::update($update_data, $map);
        success('删除成功');
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_user_guid_json()
    {
        $params    = $this->params;
        $db        = new UserModel();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $field     = [
            Db::raw("CONCAT(account,'(',name,')') as user_info"),
            'guid' => 'user_guid'
        ];
        $list      = $db->where($map)->field($field)->order(['create_time' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $user_info = [];
        if ($guid) {
            $db_express_config = new \app\model\ExpressConfig();
            $map               = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $user_info         = $db_express_config->where($map)->field(['user_guid_json'])->findOrEmpty();
        }

        if (!empty($user_info) && is_array($user_info['user_guid_json'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['user_guid'], $user_info['user_guid_json'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

}
