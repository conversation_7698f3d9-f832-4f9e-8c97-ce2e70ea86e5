<?php

namespace app\controller\admin_api\v1;

use app\model\AlipayTransfersOrder;
use app\model\MemberBrokerageNote;
use app\model\UserMoneyCashNote as UserMoneyCashNoteModel;
use app\common\service\NotifyService;

class UserMoneyCashNote extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $db          = new UserMoneyCashNoteModel();
        $join        = [
            ['user us', 'umcn.user_guid = us.guid AND umcn.bid = us.bid'],
            ['user u', 'umcn.operator_user_guid = u.guid AND umcn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'umcn.*',
            'u.account',
            "CONCAT(us.account,'-',us.name)" => 'user_info',
        ];
        $db_user     = new \app\model\User();
        $map         = [
            ['umcn.bid', '=', $this->get_bid()],
            ['umcn.user_guid', 'IN', $db_user->getChildUserGuidArray()],
        ];
        $this->model = $db->alias('umcn')->order(['umcn.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        result($this->_list());
    }

    //申请提现
    public function apply()
    {
        $params                   = $this->params;
        $bid                      = $this->get_bid();
        $user_guid                = $this->get_user_guid();
        $db_user_money_cash_note  = new UserMoneyCashNoteModel();
        $pay_type                 = $params['pay_type'] ?? 0;
        $cash_money               = $params['cash_money'];
        $db_user                  = new \app\model\User();
        $map_user                 = [
            ['bid', '=', $bid],
            ['guid', '=', $user_guid],
        ];
        $available_money          = $db_user->where($map_user)->value('money');
        $map_user_money_cash_note = [
            ['bid', '=', $bid],
            ['user_guid', '=', $user_guid],
            ['status', '=', 0]
        ];
        $wait_cash_money          = $db_user_money_cash_note->where($map_user_money_cash_note)->sum('cash_money');
        $available_cash_money     = tools()::nc_price_calculate($available_money, '-', $wait_cash_money);
        if ($cash_money > $available_cash_money) {
            error('提现金额不能超过您的可提现总额:' . $available_cash_money . '(当前余额:' . $available_money . '待审核金额:' . $wait_cash_money);
        }
        //        $db_member_brokerage_note = new MemberBrokerageNote();
        //
        //        $member_brokerage_info          = $db_member_brokerage_note->get_member_brokerage_info($bid, $member_guid);
        //        $member_brokerage               = $member_brokerage_info['member_brokerage'];
        //        $available_cash_brokerage_money = $member_brokerage_info['available_cash_brokerage_money'];
        //
        //        if ($cash_money > $member_brokerage) {
        //            error('提现金额不能超过您的剩余总额:' . $member_brokerage);
        //        }
        //        if ($cash_money > $available_cash_brokerage_money) {
        //            error('提现金额不能超过您的可提现总额:' . $available_cash_brokerage_money);
        //        }
        //todo,一定要做好对cash_money的校验
        $bill_number = tools()::get_bill_number();
        $insert_data = [
            'guid'             => create_guid(),
            'bid'              => $bid,
            'user_guid'        => $user_guid,
            'bill_number'      => $bill_number,
            'cash_money'       => $cash_money,
            'pay_type'         => $pay_type,
            'status'           => 0, //等待审核
            'wechat_true_name' => $params['wechat_true_name'] ?? '',
            'wechat_account'   => $params['wechat_account'] ?? '',
            'alipay_true_name' => $params['alipay_true_name'] ?? '',
            'alipay_account'   => $params['alipay_account'] ?? '',
            'bank_true_name'   => $params['bank_true_name'] ?? '',
            'bank_name'        => $params['bank_name'] ?? '',
            'bank_account'     => $params['bank_account'] ?? '',
        ];

        $db_user_money_cash_note->save($insert_data);

        $data = [
            'url'         => '',
            'title'       => '',
            'name'        => '【资金提现申请】', //流程名称
            'create_time' => format_timestamp(),  //操作时间
            'user'        => '有新的提现申请(' . $pay_type . '),单号:' . $bill_number, //操作人员
            'detail'      => '申请提现金额:' . $cash_money, //流程摘要
            'remark'      => '',
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_business()->set_user_guid($user_guid)->set_data($data)->set_bid($bid)->send();
        success('提交成功,请耐心等待审核');
    }

    public function last_cash_note()
    {
        $bid                      = $this->get_bid();
        $user_guid                = $this->get_user_guid();
        $map_user_money_cash_note = [
            ['user_guid', '=', $user_guid],
            ['bid', '=', $bid]
        ];
        $db_user_money_cash_note  = new UserMoneyCashNoteModel();
        $note                     = $db_user_money_cash_note->where($map_user_money_cash_note)->order(['create_time' => 'DESC'])->findOrEmpty();

        $default_last_cash_note = [
            'pay_type'         => 'alipay',
            'wechat_true_name' => '',
            'wechat_account'   => '',
            'alipay_true_name' => '',
            'alipay_account'   => '',
            'bank_true_name'   => '',
            'bank_name'        => '',
            'bank_account'     => '',
            'available_money'  => 0,
        ];
        if (!$note->isEmpty()) {
            foreach ($default_last_cash_note as $key => $val) {
                $default_last_cash_note[$key] = $note[$key] ?? '';
            }
        }
        $db_user                                        = new \app\model\User();
        $map_user                                       = [
            ['bid', '=', $bid],
            ['guid', '=', $user_guid],
        ];
        $available_money                                = $db_user->where($map_user)->value('money');
        $default_last_cash_note['available_money']      = $available_money;
        $config                                         = get_config_by_bid($this->get_bid());
        $default_last_cash_note['user_money_cash_type'] = $config['user_money_cash_type'];
        result($default_last_cash_note);
    }

    /**
     *审核
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function examine()
    {
        $params                   = $this->params;
        $bid                      = $this->get_bid();
        $guid                     = $params['guid'];
        $db_user_money_cash_note  = new UserMoneyCashNoteModel();
        $map_user_money_cash_note = [
            ['guid', '=', $guid],
            ['bid', '=', $bid]
        ];
        $note                     = $db_user_money_cash_note->where($map_user_money_cash_note)->find()->toArray();
        $db_user_money_note       = new \app\model\UserMoneyNote();
        $bill_number              = $note['bill_number'];
        $unique_code              = $bill_number . '_cash';
        $cash_money               = $note['cash_money'];
        $user_guid                = $note['user_guid'];
        $note_bid                 = $note['bid'];
        $pay_type                 = $note['pay_type'];  //wechat/alipay/bank

        $data          = [
            'guid'          => create_guid(),
            'bid'           => $note_bid,
            'user_guid'     => $user_guid,
            'way'           => 1, //卡密兑换
            'type'          => -1, //充值
            'money'         => $cash_money,
            'unique_code'   => $unique_code,
            'relation_guid' => $guid,
            'memo'          => '[提现支出],关联提现订单号:' . $bill_number,
        ];
        $recharge      = $db_user_money_note->recharge_money($data);
        $update_status = 2;   //-1 已拒绝 0 待审核 1已通过,待打款  2 打款成功
        if ($pay_type == 'alipay') {
            $config                     = get_config_by_bid($note_bid);
            $alipay_cash_auto_transfers = $config['alipay_cash_auto_transfers'];
            if ($alipay_cash_auto_transfers == 1) {
                $update_status = 1; //仅仅当需要自动打款的时候 才赋值为 已通过 待打款
            }
        }
        $update_data = [
            'status'       => $update_status,
            'examine_time' => format_timestamp()
        ];
        $db_user_money_cash_note::update($update_data, $map_user_money_cash_note);

        $data = [
            'url'         => '',
            'title'       => '',
            'name'        => '【资金提现申请通过】', //流程名称
            'create_time' => format_timestamp(),  //操作时间
            'user'        => '您发起的资金提现已经审核通过,请留意到账', //操作人员
            'detail'      => '提现金额:' . $cash_money, //流程摘要
            'remark'      => '',
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_business()->set_user_guid([$user_guid, $this->get_user_guid()])->set_data($data)->set_bid($bid)->send();
        $msg = '审核通过';

        // 根据当前是否自动支付宝并且是否自动转账 同步进行转账逻辑
        if ($pay_type == 'alipay' && $update_status == 1) {
            $db_alipay_transfers_order = new AlipayTransfersOrder();
            $transfers_guid            = create_guid();
            $out_biz_no                = tools()::get_bill_number();
            $alipay_true_name          = $note['alipay_true_name'];
            $payee_account             = $note['alipay_account'];
            $payee_type                = 'ALIPAY_LOGONID';
            $unique_code               = $bill_number;
            $payer_show_name           = '资金提现';
            $remark                    = '订单号:' . $bill_number;
            $data                      = [
                'unique_code'          => $unique_code,
                'guid'                 => $transfers_guid,
                'way'                  => 1,
                'bid'                  => $note_bid,
                'user_guid'            => $user_guid,
                'relation_guid'        => $guid,
                'relation_bill_number' => $bill_number,
                'out_biz_no'           => $out_biz_no,
                'payee_type'           => $payee_type,
                'payee_account'        => $payee_account,
                'amount'               => $cash_money,
                'payer_show_name'      => $payer_show_name,
                'status'               => 0,
                'payee_real_name'      => $alipay_true_name,
                'remark'               => $remark,
            ];
            $db_alipay_transfers_order->save($data);

            $result = $db_alipay_transfers_order->transfer($bid, $transfers_guid);
            if ($result) {
                $update_data_user_money_cash_note = ['status' => 2];
                $db_user_money_cash_note::update($update_data_user_money_cash_note, $map_user_money_cash_note);
                $msg .= '并自动支付宝打款成功' . $cash_money . '元';
            } else {
                $msg .= '但是支付宝打款失败:' . $db_alipay_transfers_order->getError();
            }
        }
        success($msg);
    }
}
