<?php

namespace app\controller\admin_api\v1;

use app\model\YkyCouponSendRuleItem as YkyCouponSendRuleItemModel;
use Exception;

class YkyCouponSendRuleItem extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new YkyCouponSendRuleItemModel();
        $this->model = $db;
        $bid         = $this->get_bid();
        $params      = $this->params;
        $rule_guid   = $params['rule_guid'];
        $map         = [
            ['bid', '=', $bid],
            ['rule_guid', '=', $rule_guid],
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 新增
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params      = $this->params;
        $bid         = $this->get_bid();
        $rule_guid   = $params['rule_guid'];
        $num         = (int)$params['num'];
        $coupon_guid = $params['coupon_guid'];
        $db          = new YkyCouponSendRuleItemModel();
        $data        = [
            'bid'         => $bid,
            'rule_guid'   => $rule_guid,
            'num'         => $num,
            'coupon_guid' => $coupon_guid,
        ];
        $db->add($data);
        success('添加成功');
    }


    /**
     * 删除
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $bid         = $this->get_bid();
        $params      = $this->params;
        $db          = new YkyCouponSendRuleItemModel();
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $params['guid']],
        ];
        $update_data = ['delete_time' => format_timestamp()];
        $db::update($update_data, $map);
        success('删除成功');
    }
}