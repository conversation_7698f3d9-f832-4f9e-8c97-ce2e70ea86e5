<?php

namespace app\controller\admin_api\v1;

use app\model\SmsSendNote;
use app\common\tools\Excel;
use Exception;
use think\facade\Db;

class Sms extends BasicAdminApi
{
    /**
     *所有短信发送记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function all()
    {
        $params      = $this->params;
        $db          = new SmsSendNote();
        $map         = [];
        $join        = [
            ['business b', 'sms.bid = b.guid', 'LEFT'],
        ];
        $field       = [
            'sms.*',
            Db::raw("(CHAR_LENGTH(sms.content)+CHAR_LENGTH(sms.sign_name)+2) as sms_length"),
            'b.account'                                 => 'business_account',
            'b.business_name'                           => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')" => 'business_info',
        ];
        $this->model = $db->alias('sms')->join($join)->where($map);
        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'sum(sms.fee)' => 'sum_fee',
            ];
            $this->model = $this->model->field($field)->order(['sum_fee']);
            $data        = $this->_find($this->model);
            $total_text  = '共 <b style="color: red"> ' . $data['sum_fee'] . '</b> 条';
            success($total_text);
        }
        $this->model = $this->model->field($field);
        result($this->_list());
    }

    /**
     *短信发送记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $this->model = new SmsSendNote();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $field       = [
            '*',
            Db::raw("(CHAR_LENGTH(content)+CHAR_LENGTH(sign_name)+2) as sms_length")
        ];
        $this->model = $this->model->where($map)->field($field);
        $params      = $this->params;
        if (!empty($params['_total'])) {
            //合计
            $field       = [
                'sum(fee)' => 'sum_fee',
            ];
            $this->model = $this->model->field($field);
            $data        = $this->_find($this->model);
            $total_text  = '共 <b style="color: red"> ' . $data['sum_fee'] . '</b> 条';
            success($total_text);
        }
        result($this->_list());
    }

    /**
     *重发短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function resend()
    {
        $params    = $this->params;
        $send_guid = $params['guid'];
        $bid       = $params['bid'] ?? $this->get_bid();
        $db        = new SmsSendNote();
        $map       = [
            ['guid', '=', $send_guid],
            ['bid', '=', $bid],
        ];
        $note      = $db->field(['content', 'mobile'])->where($map)->findOrFail();
        $content   = $note['content'];
        $mobile    = $note['mobile'];
        send_sms($content, $mobile, $bid);
        success('重发成功,请留意发送记录');
    }

    /**
     *发送优惠
     * @access public
     * @return void
     * @throws Exception
     */
    public function send()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $param            = $this->params;
        $file             = new Excel();
        $operator_user_id = $this->get_user_id();
        $bid              = $this->get_bid();
        $file_url         = $param['file'] ?? '';
        $mobile           = $param['mobile'] ?? '';
        $send_content     = $param['content'] ?? '';
        $send_bid         = $param['send_bid'];
        $success_num      = 0;
        if ($mobile) {
            //单发
            if (!tools()::is_mobile($mobile)) {
                error('您输入的手机号格式不正确');
            }
            if (empty($send_content)) {
                error('请输入短信内容');
            }
            send_sms($send_content, $mobile, $send_bid);
            $success_num++;
        } else {
            //群发
            if (empty($file_url)) {
                error('请上传文件后再发送');
            }
            if (!tools()::is_url($file_url)) {
                error('文件路径不正确,请重新上传excel文件');
            }
            $allowField = [
                'mobile'       => '手机号',
                'send_content' => '短信内容',
            ];
            $arr        = $file->load($file_url)->excelToArray($allowField);
            //循环校验合法数据 得到要导入的数组
            $errmsg = '';
            foreach ($arr as $key => $val) {
                // 验证excel数据
                $mobile              = $val['mobile'];
                $mobile_send_content = $val['send_content'];
                if (!tools()::is_mobile($mobile)) {
                    $errmsg .= '第' . ($key + 2) . '行:(手机号:' . $mobile . ')格式不正确' . "<br/>";
                }
                $success_num++;
            }
            if ($errmsg) {
                error('数据校验不通过:' . "<br/>" . $errmsg);
            }
            $jobs_data = [];
            foreach ($arr as $key => $val) {
                $mobile_send_content = $val['send_content'] ?: $send_content;
                $jobs_data[]         = [
                    'guid'    => create_guid(),
                    'bid'     => $send_bid,
                    'mobile'  => $val['mobile'],
                    'content' => $mobile_send_content
                ];
            }
            job()->set_job_name('Sms@send_sms')->push_job($jobs_data);
        }
        success('提交成功' . $success_num . '条, 请在【短信】查看发送结果');
    }

    public function get_online_recharge_list()
    {
        $recharge_bid         = $this->get_bid();
        $config               = get_config_by_bid($recharge_bid);
        $sms_settlement_price = (int)$config['sms_settlement_price'];
        if ($sms_settlement_price < 5) {
            error('短信结算价格过低,获取失败');
        }
        $allow_recharge_amount = [1000, 2000, 3000, 5000, 10000];
        $list                  = [];
        foreach ($allow_recharge_amount as $amount) {
            $settlement_price_fen  = $amount * $sms_settlement_price;
            $settlement_price_yuan = tools()::nc_price_fen2yuan($settlement_price_fen);
//            $list[]                = [
//                'amount'               => $amount,
//                'sms_settlement_price' => $settlement_price_fen,
//                'text'                 => $settlement_price_yuan . '元 ' . $amount . '条',
//            ];
            $list[$amount] = ((int)$settlement_price_yuan) . '元 ' . $amount . '条';
        }
        $db_business   = new \app\model\Business();
        $business_info = $db_business->find($recharge_bid);
        result(['list' => $list, 'business_info' => $business_info]);
    }

    /**
     *在线充值短信
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function online_recharge()
    {
        if (!get_system_config('enable_online_recharge_sms')) {
            error('暂不支持在线充值短信,请联系客服');
        }
        $params               = $this->params;
        $recharge_bid         = $this->get_bid();
        $amount               = (int)$params['amount']; //充值条数
        $config               = get_config_by_bid($recharge_bid);
        $sms_settlement_price = (int)$config['sms_settlement_price'];
        if ($sms_settlement_price < 5) {
            error('短信结算价格过低,获取失败');
        }
        $bid           = config('app.recharge_pay_parameter_business_guid');
        $out_trade_no  = tools()::get_bill_number();
        $int_total_fee = (int)($amount * $sms_settlement_price);
        $order_guid    = create_guid();
        $recharge_info = [
            'out_trade_no' => $out_trade_no,
            'recharge_bid' => $recharge_bid,
            'total_fee'    => $int_total_fee,
            'amount'       => $amount,
        ];
        cache($order_guid, $recharge_info, 3600);
        $pay_url = (string)url('member/pay/submit', ['bid' => $bid, 'order_guid' => $order_guid, 'type' => 'recharge_business_sms'], false, true);
        $pay_url = tools()::replace_readonly_to_www($pay_url);
        $data    = [
            'url'        => $pay_url,
            'socket_uid' => $out_trade_no
        ];
        result($data);
    }
}