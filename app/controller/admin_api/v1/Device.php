<?php

namespace app\controller\admin_api\v1;

use app\model\Device as DeviceModel;
use app\model\DeviceAd;
use Exception;

class Device extends BasicAdminApi
{
    /**
     *设备列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new DeviceModel();
        $join        = [
            ['business b', 'd.bid = b.guid', 'LEFT'],
            ['business b2', 'd.last_login_bid = b2.guid', 'LEFT'],
        ];
        $field       = [
            'd.*',
            'b.account'                                   => 'agent_account',
            "CONCAT(b.business_name,'(',b.account,')')"   => 'agent',
            "CONCAT(b2.business_name,'(',b2.account,')')" => 'last_login_business',
        ];
        $this->model = $db->alias('d')->join($join)->field($field)->order(['d.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *我的设备
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function my_device()
    {
        $db          = new DeviceModel();
        $join        = [
            ['business b', 'd.bid = b.guid', 'LEFT'],
        ];
        $field       = [
            'd.*',
            'b.account'                                 => 'agent_account',
            "CONCAT(b.business_name,'(',b.account,')')" => 'agent',
        ];
        $map         = [
            ['bid', '=', $this->get_bid()],
        ];
        $this->model = $db->alias('d')->join($join)->field($field)->where($map)->order(['d.create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     *设备配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function config()
    {
        $params      = $this->params;
        $sn          = $params['device_id'];
        $db          = new DeviceModel();
        $map         = [['sn', '=', $sn]];
        $device_info = $db->where($map)->find();
        if (empty($device_info)) {
            error('设备号:' . $sn . '不存在');
        }
        if (empty($device_info['bid'])) {
            error('设备号:' . $sn . '未分配代理商');
        }
        $system_config                = get_config_by_bid($device_info['bid']); //获取商家配置
        $app_name                     = $system_config['device_app_name'];
        $app_logo                     = $system_config['device_app_logo'];
        $qrcode                       = $system_config['device_qrcode'];
        $qrcode_title                 = $system_config['device_qrcode_title'];
        $device_copyright             = $system_config['device_copyright'];
        $device_pay_before_background = $system_config['device_pay_before_background'];
        $result                       = [
            'app_name'                     => $app_name,
            'app_logo'                     => $app_logo,
            'qrcode'                       => $qrcode,
            'qrcode_title'                 => $qrcode_title,
            'device_copyright'             => $device_copyright,
            'device_pay_before_background' => $device_pay_before_background
        ];
        result($result);
    }

    /**
     *上传广告
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function upload_ad()
    {
        $params       = $this->params;
        $sn           = $params['sn'];
        $src          = $params['src'];
        $sort         = (int)$params['sort'];
        $ad_type      = (int)$params['ad_type'];
        $db_device_ad = new DeviceAd();
        // pathinfo( parse_url($src)['path'] )['extension'];
        $ext         = pathinfo(parse_url($src, PHP_URL_PATH), PATHINFO_EXTENSION);
        $type        = strtolower($ext) == 'mp4' ? 2 : 1; //mp4后缀是视频,其他认为图片
        $insert_data = [
            'type'      => $type,
            'ad_type'   => $ad_type,
            'sort'      => $sort,
            'device_sn' => $sn,
            'src'       => $src
        ];
        $result      = $db_device_ad->save($insert_data);
        success('添加成功');
    }

    /**
     *编辑广告
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function ad_edit()
    {
        $params       = $this->params;
        $id           = $params['id'];
        $src          = $params['src'];
        $ad_type      = (int)$params['ad_type'];
        $db_device_ad = new DeviceAd();
        $map          = [
            ['id', '=', $id],
        ];
        $update_data  = ['src' => $src, 'ad_type' => $ad_type];
        $result       = $db_device_ad::update($update_data, $map);
        success('编辑成功');
    }

    /**
     *删除广告
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del_ad()
    {
        $params       = $this->params;
        $id           = $params['id'];
        $db_device_ad = new DeviceAd();
        $map          = [
            ['id', '=', $id],
        ];
        $update_data  = ['status' => -1];
        $result       = $db_device_ad::update($update_data, $map);
        success('广告删除成功');
    }

    /**
     *分配
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change()
    {
        $params      = $this->params;
        $id          = $params['id'];
        $sn          = $params['sn'];
        $bid         = $params['business_guid'];
        $db_device   = new DeviceModel();
        $map         = [
            ['id', '=', $id],
            ['sn', '=', $sn],
        ];
        $update_data = ['bid' => $bid];
        $result      = $db_device::update($update_data, $map);
        success('分配成功');
    }

    /**
     *广告列表PC版
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function ad_lists()
    {
        $params       = $this->params;
        $sn           = $params['sn'];
        $this->params = [];
        $bid          = $this->get_bid();
        $map          = [
            //['bid', '=', $bid],
            ['device_sn', '=', $sn],
            ['status', '=', 1],
        ];
        $db_device_ad = new DeviceAd();
        $this->model  = $db_device_ad->where($map)->order(['ad_type' => 'ASC', 'type' => 'ASC', 'sort' => 'ASC']);
        result($this->_list());
    }

    /**
     *广告列表APP使用
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function ad_list()
    {
        $params     = $this->params;
        $sn         = $params['device_id'];
        $ad_type    = isset($params['ad_type']) ? $params['ad_type'] : 1; //默认获取支付后广告
        $bid        = $this->get_bid();
        $order      = ['type' => 'DESC', 'sort' => 'ASC'];
        $field      = ['sort', 'src'];
        $type_array = [1, 2];
        $result     = [];
        foreach ($type_array as $type) {
            $db_device_ad = new DeviceAd();
            $map          = [
                //['bid', '=', $bid],
                ['ad_type', '=', $ad_type],
                ['device_sn', '=', $sn],
                ['status', '=', 1],
                ['type', '=', $type],
            ];
            $list         = $db_device_ad->where($map)->field($field)->order($order)->select()->toArray();
            $result[]     = [
                'type' => $type,
                'list' => $list
            ];
        }
        result($result);
    }
}