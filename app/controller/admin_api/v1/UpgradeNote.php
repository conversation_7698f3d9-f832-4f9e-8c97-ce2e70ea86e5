<?php

namespace app\controller\admin_api\v1;


use Exception;

class UpgradeNote extends BasicAdminApi
{
    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        result($this->_list());
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function version_item_list()
    {
        $params = $this->params;
        $db     = new \app\model\Version();
        $map    = [
            ['type', '=', 1]
        ];
        $list   = $db->where($map)->field(['name', 'guid' => 'version_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid   = $params['guid'] ?? '';
        $rule   = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_goods = new \app\model\UpgradeNote();
            $map      = [
                ['guid', '=', $guid],
            ];
            $rule     = $db_goods->where($map)->field(['version_guid_json'])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule['version_guid_json'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['version_guid'], $rule['version_guid_json'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }
}
