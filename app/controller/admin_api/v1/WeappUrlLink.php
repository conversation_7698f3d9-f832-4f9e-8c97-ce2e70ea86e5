<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;


use Exception;

class WeappUrlLink extends BasicAdminApi
{
    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        //        $db_fast_register_weapp = new \app\model\FastRegisterWeapp();
        //        $map                    = [];
        //        $join                   = [
        //            ['business b', 'frw.bid = b.guid'],
        //            ['business b2', 'frw.create_bid = b2.guid'],
        //        ];
        //        $field                  = [
        //            'frw.*',
        //            'b.account'                                   => 'business_account',
        //            'b.business_name'                             => 'business_name',
        //            "CONCAT(b.business_name,'(',b.account,')')"   => 'business_info',
        //            'b2.account'                                  => 'agent_account',
        //            'b2.business_name'                            => 'agent_name',
        //            "CONCAT(b2.business_name,'(',b2.account,')')" => 'agent_info',
        //        ];
        //        $db_business            = new \app\model\Business();
        //        $business_type          = $db_business->get_business_type($this->get_bid());
        //        if ($db_business->is_admin_type($business_type)) {
        //
        //        } elseif ($db_business->is_business_type($business_type)) {
        //            $map[] = ['b.guid', '=', $this->get_bid()];
        //        } elseif ($db_business->is_agent_type($business_type)) {
        //            $map[] = ['b.parent_guid', '=', $this->get_bid()];
        //        } else {
        //            error('暂时不支持查询');
        //        }
        //        $this->model = $db_fast_register_weapp->alias('frw')->join($join)->field($field)->where($map);
        result($this->_list());
    }


    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params      = $this->params;
        $db          = new \app\model\WeappUrlLink();
        $path        = $params['path'];
        $query       = $params['query'];
        $env_version = $params['env_version'];
        $appid       = $params['appid'];
        $guid        = create_guid();
        $data        = [
            'guid'        => $guid,
            'bid'         => $this->get_bid(),
            'appid'       => $appid,
            'path'        => $path,
            'query'       => $query,
            'env_version' => $env_version,
        ];
        $db->save($data);
        $instance                = weixin($appid)::WeMiniScheme();
        $post_data               = [
            'path'        => $path,
            'query'       => $query,
            'env_version' => $env_version,
        ];
        $update_data             = [];
        $result                  = $instance->urlLink($post_data);
        $update_data['url_link'] = $result['url_link'];
        $map                     = [['guid', '=', $guid]];
        $db::update($update_data, $map);
        success('添加成功');
    }
}