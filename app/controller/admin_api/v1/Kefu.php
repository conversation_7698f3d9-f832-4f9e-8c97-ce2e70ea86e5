<?php

namespace app\controller\admin_api\v1;

use app\model\CustomerMessage;
use app\model\CustomerSession;
use app\model\CustomerSessionTransfer;
use OpenApi\DuoKeFu;
use think\Exception;

class Kefu extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $bid               = $this->get_bid();
        $db_business       = new \app\model\Business();
        $business_info     = $db_business->get_business_info_by_account_or_guid($bid);
        $db_user           = new \app\model\User();
        $default_user_guid = $db_user->get_default_user_guid($bid);
        $duo_ke_fu         = new DuoKeFu();
        $data              = [
            'seller_code' => $bid,
            'seller_name' => $business_info['account'],
            'kefu_code'   => $default_user_guid
        ];
        $result            = $duo_ke_fu->auto_add_seller_and_kefu($data);
        $url               = request()->scheme() . '://chat.yikayi.net/service/login/login_by_seller_code_and_kefu_code?seller_code=' . $bid . '&kefu_code=' . $default_user_guid;
        result(['url' => $url]);
    }

    /**
     * 消息列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function message_list()
    {
        $session_guid = $this->request->param('session_guid');
        $params       = $this->params;
        $page         = max(1, intval($params['page'] ?? 1));
        $limit        = max(1, intval($params['limit'] ?? 20));
        $bid          = $this->get_bid();

        // 1. 先查询基本消息数据
        $map = [
            ['session_guid', '=', $session_guid],
            ['is_recall', '=', 0]
        ];

        $this->model = CustomerMessage::where($map)->order(['create_time' => 'asc']);
        $list        = $this->_list($page, $limit);

        if (!empty($list['data'])) {
            // 2. 收集需要查询的用户GUID和会员GUID
            $user_guids   = [];
            $member_guids = [];

            foreach ($list['data'] as $msg) {
                if ($msg['from_type'] == 2) {
                    $user_guids[] = $msg['from_guid'];
                } else {
                    $member_guids[] = $msg['from_guid'];
                }
            }

            // 3. 去重并批量查询用户信息
            $user_info_map = [];
            if (!empty($user_guids)) {
                $user_guids = array_unique($user_guids);
                $users      = \app\model\User::where([
                    ['bid', '=', $bid],
                    ['guid', 'in', $user_guids]
                ])->field(['guid', 'name', 'head'])->select()->toArray();

                foreach ($users as $user) {
                    $user_info_map[$user['guid']] = [
                        'name'   => $user['name'] ?: \app\model\User::DEFAULT_NAME,
                        'avatar' => $user['head'] ?: \app\model\User::DEFAULT_AVATAR
                    ];
                }
            }

            // 4. 去重并批量查询会员信息
            $member_info_map = [];
            if (!empty($member_guids)) {
                $member_guids = array_unique($member_guids);
                $members      = \app\model\Member::where([
                    ['bid', '=', $bid],
                    ['guid', 'in', $member_guids]
                ])->field(['guid', 'name', 'head_img'])->select()->toArray();

                foreach ($members as $member) {
                    $member_info_map[$member['guid']] = [
                        'name'   => $member['name'] ?: \app\model\Member::DEFAULT_NAME,
                        'avatar' => $member['head_img'] ?: \app\model\Member::DEFAULT_AVATAR
                    ];
                }
            }

            // 5. 为每条消息补充头像昵称信息
            foreach ($list['data'] as &$msg) {
                if ($msg['from_type'] == 2) {
                    // 客服消息
                    $user_info          = $user_info_map[$msg['from_guid']] ?? [
                        'name'   => \app\model\User::DEFAULT_NAME,
                        'avatar' => \app\model\User::DEFAULT_AVATAR
                    ];
                    $msg['from_name']   = $user_info['name'];
                    $msg['from_avatar'] = $user_info['avatar'];
                } else {
                    // 会员消息
                    $member_info        = $member_info_map[$msg['from_guid']] ?? [
                        'name'   => \app\model\Member::DEFAULT_NAME,
                        'avatar' => \app\model\Member::DEFAULT_AVATAR
                    ];
                    $msg['from_name']   = $member_info['name'];
                    $msg['from_avatar'] = $member_info['avatar'];
                }
            }
        }

        result($list);
    }

    /**
     * 极简会话总览
     * @access public
     * @return void
     * @throws Exception
     */
    public function chat_list()
    {
        $bid       = $this->get_bid();
        $user_guid = $this->get_user_guid();

        // 1. 查询已接入会话基本数据
        $session_map  = [
            ['bid', '=', $bid],
            ['user_guid', '=', $user_guid],
            ['status', '=', 1], // 进行中
        ];
        $session_list = CustomerSession::where($session_map)
            ->order('update_time desc')
            ->limit(100)
            ->select()
            ->toArray();

        // 2. 查询待接入会话基本数据
        $queue_map  = [
            ['bid', '=', $bid],
            ['status', '=', 0], // 待接入
        ];
        $queue_list = CustomerSession::where($queue_map)
            ->order('update_time desc')
            ->limit(100)
            ->select()
            ->toArray();

        // 3. 收集需要查询的用户GUID和会员GUID
        $user_guids   = [];
        $member_guids = [];

        // 从已接入会话收集
        foreach ($session_list as $session) {
            if ($session['user_guid']) {
                $user_guids[] = $session['user_guid'];
            }
            if ($session['member_guid']) {
                $member_guids[] = $session['member_guid'];
            }
        }

        // 从待接入会话收集
        foreach ($queue_list as $session) {
            if ($session['member_guid']) {
                $member_guids[] = $session['member_guid'];
            }
        }

        // 4. 去重并批量查询用户信息
        $user_info_map = [];
        if (!empty($user_guids)) {
            $user_guids = array_unique($user_guids);
            $users      = \app\model\User::where([
                ['bid', '=', $bid],
                ['guid', 'in', $user_guids]
            ])->field(['guid', 'name', 'head'])->select()->toArray();

            foreach ($users as $user) {
                $user_info_map[$user['guid']] = [
                    'name'   => $user['name'] ?: \app\model\User::DEFAULT_NAME,
                    'avatar' => $user['head'] ?: \app\model\User::DEFAULT_AVATAR
                ];
            }
        }

        // 5. 去重并批量查询会员信息
        $member_info_map = [];
        if (!empty($member_guids)) {
            $member_guids = array_unique($member_guids);
            $members      = \app\model\Member::where([
                ['bid', '=', $bid],
                ['guid', 'in', $member_guids]
            ])->field(['guid', 'name', 'head_img'])->select()->toArray();

            foreach ($members as $member) {
                $member_info_map[$member['guid']] = [
                    'name'   => $member['name'] ?: \app\model\Member::DEFAULT_NAME,
                    'avatar' => $member['head_img'] ?: \app\model\Member::DEFAULT_AVATAR
                ];
            }
        }

        // 6. 为已接入会话补充头像昵称信息
        foreach ($session_list as &$session) {
            // 会员信息
            $member_info              = $member_info_map[$session['member_guid']] ?? [
                'name'   => \app\model\Member::DEFAULT_NAME,
                'avatar' => \app\model\Member::DEFAULT_AVATAR
            ];
            $session['member_name']   = $member_info['name'];
            $session['member_avatar'] = $member_info['avatar'];

            // 客服信息
            $user_info              = $user_info_map[$session['user_guid']] ?? [
                'name'   => \app\model\User::DEFAULT_NAME,
                'avatar' => \app\model\User::DEFAULT_AVATAR
            ];
            $session['kefu_name']   = $user_info['name'];
            $session['kefu_avatar'] = $user_info['avatar'];
        }

        // 7. 为待接入会话补充头像昵称信息
        foreach ($queue_list as &$session) {
            // 会员信息
            $member_info              = $member_info_map[$session['member_guid']] ?? [
                'name'   => \app\model\Member::DEFAULT_NAME,
                'avatar' => \app\model\Member::DEFAULT_AVATAR
            ];
            $session['member_name']   = $member_info['name'];
            $session['member_avatar'] = $member_info['avatar'];

            // 待接入时没有客服
            $session['kefu_name']   = null;
            $session['kefu_avatar'] = null;
        }

        result([
            'session_list' => $session_list,
            'queue_list'   => $queue_list,
        ]);
    }

    /**
     * 获取可转接的客服列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function transfer_kefu_list()
    {
        $bid               = $this->get_bid();
        $current_user_guid = $this->get_user_guid();

        // 获取在线且可接收转接的客服
        $kefu_list = \app\model\User::where([
            ['bid', '=', $bid],
            ['service_status', '=', 1], // 在线状态
            ['guid', '<>', $current_user_guid] // 排除自己
        ])->field([
            'guid',
            'name',
            'head',
            'service_status',
            'last_login_time'
        ])->select()->toArray();

        // 统计每个客服的当前会话数
        foreach ($kefu_list as &$kefu) {
            $session_count = \app\model\CustomerSession::where([
                ['user_guid', '=', $kefu['guid']],
                ['status', '=', 1] // 进行中
            ])->count();

            $kefu['session_count'] = $session_count;
            $kefu['can_transfer']  = $session_count < 10; // 假设最大10个会话

            // 统一字段名，便于前端使用
            $kefu['nickname']         = $kefu['name']; // 使用name作为nickname
            $kefu['avatar']           = $kefu['head']; // 使用head作为avatar
            $kefu['last_active_time'] = $kefu['last_login_time']; // 使用last_login_time作为活跃时间
        }

        result($kefu_list);
    }

    /**
     * 获取转接历史记录
     * @access public
     * @return void
     * @throws Exception
     */
    public function transfer_history()
    {
        $params       = $this->params;
        $session_guid = $params['session_guid'] ?? '';

        if (!$session_guid) {
            error('会话GUID不能为空');
        }

        $transfer_list = CustomerSessionTransfer::where([
            ['session_guid', '=', $session_guid]
        ])->with(['fromUser', 'toUser'])
            ->order('transfer_time desc')
            ->select()
            ->toArray();

        result($transfer_list);
    }

    /**
     * 获取待处理的转接请求
     * @access public
     * @return void
     * @throws Exception
     */
    public function pending_transfers()
    {
        $user_guid = $this->get_user_guid();

        $transfer_list = CustomerSessionTransfer::where([
            ['to_user_guid', '=', $user_guid],
            ['status', '=', CustomerSessionTransfer::STATUS_PENDING]
        ])->with(['fromUser', 'session'])
            ->order('transfer_time desc')
            ->select()
            ->toArray();

        result($transfer_list);
    }

    /**
     * 获取访客信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function visitor_info()
    {
        $params      = $this->params;
        $member_guid = $params['member_guid'] ?? '';
        $bid         = $this->get_bid();

        if (!$member_guid) {
            error('会员GUID不能为空');
        }

        // 查询会员基本信息
        $member = \app\model\Member::where([
            ['bid', '=', $bid],
            ['guid', '=', $member_guid]
        ])->field([
            'guid',
            'name',
            'head_img',
            'mobile',
            'card_id',
            'from',
            'sex',
            'create_time',
            'room_number' // 暂时用room_number作为备注字段
        ])->findOrEmpty();

        if ($member->isEmpty()) {
            error('访客信息不存在');
        }

        // 格式化返回数据
        $visitor_info = [
            'guid'          => $member['guid'],
            'name'          => $member['name'] ?: \app\model\Member::DEFAULT_NAME,
            'avatar'        => $member['head_img'] ?: \app\model\Member::DEFAULT_AVATAR,
            'mobile'        => $member['mobile'] ?: '',
            'card_id'       => $member['card_id'] ?: '',
            'sex'           => $member['sex'] ?? 0,
            'from'          => $member['from'] ?? 0,
            'register_time' => $member['create_time'] ?: '',
            'remark'        => $member['room_number'] ?: '', // 暂时用room_number作为备注
            'ip'            => '', // 暂时为空，后续可以从session表获取
            'address'       => '', // 暂时为空，后续可以根据IP获取
            'device'        => '', // 暂时为空，后续可以从session表获取
        ];

        result($visitor_info);
    }

    /**
     * 更新访客备注
     * @access public
     * @return void
     * @throws Exception
     */
    public function update_visitor_remark()
    {
        $params      = $this->params;
        $member_guid = $params['member_guid'] ?? '';
        $remark      = $params['remark'] ?? '';
        $bid         = $this->get_bid();

        if (!$member_guid) {
            error('会员GUID不能为空');
        }

        // 更新会员备注信息（暂时使用room_number字段）
        $result = \app\model\Member::where([
            ['bid', '=', $bid],
            ['guid', '=', $member_guid]
        ])->update([
            'room_number' => $remark,
            'update_time' => date('Y-m-d H:i:s.v')
        ]);

        if ($result === false) {
            error('更新备注失败');
        }

        result(['success' => true], 0, '备注更新成功');
    }

    /**
     * 获取客服端初始化配置
     */
    public function config()
    {
        // 假设有登录态或token，实际项目可根据实际情况获取当前客服guid
        $user_guid = $this->request->param('user_guid') ?: $this->get_user_guid();
        $db_user   = new \app\model\User();
        $user_info = $db_user->where(['guid' => $user_guid])->findOrEmpty();
        if (!$user_info) {
            result(['user' => null, 'style' => ['box_color' => '#009688']], 0, '客服信息不存在');
        }
        $data = [
            'user'  => [
                'bid'    => $user_info['bid'],
                'name'   => $user_info['name'] ? ($user_info['name'] . '-' . $user_info['account']) : \app\model\User::DEFAULT_NAME,
                'guid'   => $user_info['guid'] ?? '',
                'avatar' => $user_info['head'] ?: \app\model\User::DEFAULT_AVATAR,
            ],
            'style' => ['box_color' => '#009688'],
            // 可扩展其它配置
        ];
        result($data, 0, 'ok');
    }

    /**
     * 客服心跳接口
     * @access public
     * @return void
     * @throws Exception
     */
    public function heartbeat()
    {
        $kefu_guid = $this->params['kefu_guid'] ?? '';
        $bid       = $this->params['bid'] ?? '';

        if (!$kefu_guid || !$bid) {
            error('参数缺失');
        }

        // 验证客服身份
        $current_user_guid = $this->get_user_guid();
        if ($current_user_guid !== $kefu_guid) {
            error('身份验证失败');
        }

        try {
            // 更新客服最后活跃时间和在线状态
            $update_result = \app\model\User::where([
                ['guid', '=', $kefu_guid],
                ['bid', '=', $bid]
            ])->update([
                'service_status' => 1, // 确保在线状态
                //                'last_login_time' => date('Y-m-d H:i:s'),
                //                'update_time'    => date('Y-m-d H:i:s')
            ]);

            if ($update_result !== false) {
                success('心跳更新成功');
            } else {
                error('心跳更新失败');
            }
        } catch (\Exception $e) {
            debug_log('[KEFU_HEARTBEAT] 心跳更新异常: ' . $e->getMessage());
            error('心跳更新异常: ' . $e->getMessage());
        }
    }
}
