<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use Exception;

class Banner extends BasicAdminApi
{
    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function coupon_item_list()
    {
        $params    = $this->params;
        $db        = new Coupon();
        $bid       = $this->get_bid();
        $map       = [
            ['bid', '=', $bid],
        ];
        $list      = $db->where($map)->field(['name', 'guid' => 'coupon_item_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $db_banner = new \app\model\Banner();
        if ($guid) {
            //本规则的商品默认选中
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db_banner->where($map)->field(['coupon_item_guid'])->find();
            if (is_array($rule['coupon_item_guid'])) {
                foreach ($list as $key => $val) {
                    if (in_array($val['coupon_item_guid'], $rule['coupon_item_guid'])) {
                        $list[$key]['selected'] = true;
                    }
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    public function filter_params($params)
    {

        if (isset($params['coupon_item_guid'])) {
            $params['coupon_item_guid'] = explode(',', $params['coupon_item_guid']);
        } else {
            unset($params['coupon_item_guid']);
        }
        if (!empty($params['path'])) {
            //处理小程序路径 先替换 .html .
            $params['path'] = str_replace('.html', '', $params['path']);
            //去除 $params['path'] 左边的 /
            $params['path'] = '/' . ltrim($params['path'], '/');
        }
        return $params;
    }

    /**
     *添加
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params    = $this->filter_params($this->params);
        $db_banner = new \app\model\Banner();
        $db_banner->add($params);
        success('添加成功');
    }

    /**
     *编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $params    = $this->filter_params($this->params);
        $db_banner = new \app\model\Banner();
        $db_banner->edit($params);
        success('编辑成功');
    }

    /**
     * 轮播图列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [['bid', '=', $this->get_bid()]];
        $order       = [
            'status'      => 'DESC',
            'type'        => 'ASC',
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        $this->model = $this->model->where($map)->order($order)->append(['img_url_mini']);
        result($this->_list());
    }
}