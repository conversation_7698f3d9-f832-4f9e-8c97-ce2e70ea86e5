<?php

namespace app\controller\admin_api\v1;

use app\model\Coupon;
use Exception;
use think\facade\Db;

class DateRule extends BasicAdminApi
{

    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\DateRule();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     *添加卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $rule_type = $params['rule_type'];
        if (!empty($params['date'])) {
            $params['date'] = explode(',', $params['date']);
            sort($params['date']);
        } else {
            unset($params['date']);
        }
        switch ($rule_type) {
            case 4:
                $start_month_day = $params['start_month_day'];
                $end_month_day   = $params['end_month_day'];
                if (!tools()::validate_month_day($start_month_day)) {
                    error('开始月日输入不正确,请输入月-日,例如输入08-01代表8月1日');
                }
                if (!tools()::validate_month_day($end_month_day)) {
                    error('截止月日输入不正确,请输入月-日,例如输入12-31代表12月31日');
                }
                if (strtotime(date('Y') . '-' . $start_month_day) >= strtotime(date('Y') . '-' . $end_month_day)) {
                    error('开始月日需要小于截止月日');
                }
                break;
            case 5:
                // month_days 必须为 1-31 的数组
                if (empty($params['month_days'])) {
                    error('请选择每月几日');
                }
                $month_days_array = explode(',', $params['month_days']);
                foreach ($month_days_array as $day) {
                    if (!is_numeric($day) || $day < 1 || $day > 31) {
                        error('每月几日必须为1-31之间的数字');
                    }
                }
                break;
        }
        $db                   = new \app\model\DateRule();
        $params['start_date'] = $params['start_date'] ?: null;
        $params['end_date']   = $params['end_date'] ?: null;
        $db->add($params);
        success('添加成功');
    }

    public function change_status()
    {
        $params = $this->params;
        $bid    = $this->get_bid();
        //        $guid   = $params['guid'];
        //        $status = $params['status'];
        $db = new \app\model\DateRule();
        $db->edit($params);
        success('编辑成功');
    }

    /**
     *编辑卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $rule_type = $params['rule_type'];
        if (!empty($params['date'])) {
            $params['date'] = explode(',', $params['date']);
            sort($params['date']);
        } else {
            unset($params['date']);
        }
        switch ($rule_type) {
            case 4:
                $start_month_day = $params['start_month_day'];
                $end_month_day   = $params['end_month_day'];
                if (!tools()::validate_month_day($start_month_day)) {
                    error('开始月日输入不正确,请输入月-日,例如输入08-01代表8月1日');
                }
                if (!tools()::validate_month_day($end_month_day)) {
                    error('截止月日输入不正确,请输入月-日,例如输入12-31代表12月31日');
                }
                if (strtotime(date('Y') . '-' . $start_month_day) >= strtotime(date('Y') . '-' . $end_month_day)) {
                    error('开始月日需要小于截止月日');
                }
                break;
            case 5:
                // month_days 必须为 1-31 的数组
                if (empty($params['month_days'])) {
                    error('请选择每月几日');
                }
                $month_days_array = explode(',', $params['month_days']);
                foreach ($month_days_array as $day) {
                    if (!is_numeric($day) || $day < 1 || $day > 31) {
                        error('每月几日必须为1-31之间的数字');
                    }
                }
                break;
        }
        $params['start_date'] = $params['start_date'] ?: null;
        $params['end_date']   = $params['end_date'] ?: null;
        $db                   = new \app\model\DateRule();
        if (isset($params['start_date'])) {
            $params['start_date'] = $params['start_date'] ?: null;
        }
        if (isset($params['end_date'])) {
            $params['end_date'] = $params['end_date'] ?: null;
        }
        $db->edit($params);
        success('编辑成功');
    }

    /**
     *编辑卡券_NEW
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del()
    {
        $params              = $this->params;
        $bid                 = $this->get_bid();
        $guid                = $params['guid'];
        $db_coupon           = new Coupon();
        $db_date_rule        = new \app\model\DateRule();
        $date_rule_guid_json = json_encode([$guid]);
        $empty_guid          = '';
        $empty_guid_json     = json_encode([$empty_guid]);
        $map                 = [['bid', '=', $bid]];
        $map[]               = Db::raw("JSON_CONTAINS(date_rule_guid,'$date_rule_guid_json')>0");
        // 查询不限制级别或者限定了当前会员级别可参与的规则
        //$map[] = Db::raw("JSON_CONTAINS(member_group_guid,'$member_group_guid_json')>0 OR JSON_CONTAINS(member_group_guid,'$empty_guid_json')>0 OR member_group_guid IS NULL");
        $coupon_name_array = $db_coupon->where($map)->column('name');
        if (empty($coupon_name_array)) {
            $map = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $db_date_rule->where($map)->delete();
            success('删除成功');
        }
        error('删除失败:' . join(',', $coupon_name_array) . '等' . count($coupon_name_array) . '种卡券有关联当前日期规则');
    }

    public function goods_item_list()
    {
        $params    = $this->params;
        $db        = new Coupon();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $json_key  = 'goods_item_guid';
        $field_key = 'goods_item_guid';
        $list      = $db->where($map)->field(['name', 'guid' => $field_key])->order(['name' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $rule      = [];
        if ($guid) {
            // 本规则的商品默认选中
            $db   = new \app\model\DateRule();
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db->where($map)->field([$json_key])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule[$json_key])) {
            foreach ($list as $key => $val) {
                if (in_array($val[$field_key], $rule[$json_key])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function coupon_item_list()
    {
        $params    = $this->params;
        $db        = new Coupon();
        $bid       = $this->get_bid();
        $map       = [['bid', '=', $bid]];
        $json_key  = 'coupon_item_guid';
        $field_key = 'coupon_item_guid';
        $list      = $db->where($map)->field(['name', 'guid' => $field_key])->order(['name' => 'ASC'])->select()->toArray();
        $guid      = $params['guid'] ?? '';
        $rule      = [];
        if ($guid) {
            // 本规则的卡券默认选中
            $db   = new \app\model\DateRule();
            $map  = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule = $db->where($map)->field([$json_key])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule[$json_key])) {
            foreach ($list as $key => $val) {
                if (in_array($val[$field_key], $rule[$json_key])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        result($list);
    }
}
