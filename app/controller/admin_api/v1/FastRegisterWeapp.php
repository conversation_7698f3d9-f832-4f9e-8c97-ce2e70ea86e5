<?php

/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;


use app\common\exceptions\NotNotifyException;
use Exception;
use Throwable;
use xieyongfa\companyquery\CompanyQuery;

class FastRegisterWeapp extends BasicAdminApi
{
    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_fast_register_weapp = new \app\model\FastRegisterWeapp();
        $map                    = [];
        $join                   = [
            ['business b', 'frw.bid = b.guid'],
            ['business b2', 'frw.create_bid = b2.guid'],
        ];
        $field                  = [
            'frw.*',
            'b.account'                                   => 'business_account',
            'b.business_name'                             => 'business_name',
            "CONCAT(b.business_name,'(',b.account,')')"   => "business_info",
            'b2.account'                                  => 'agent_account',
            'b2.business_name'                            => 'agent_name',
            "CONCAT(b2.business_name,'(',b2.account,')')" => 'agent_info',
        ];
        $db_business            = new \app\model\Business();
        $business_type          = $db_business->get_business_type($this->get_bid());
        if ($db_business->is_admin_type($business_type)) {
        } elseif ($db_business->is_business_type($business_type)) {
            $map[] = ['b.guid', '=', $this->get_bid()];
        } elseif ($db_business->is_agent_type($business_type)) {
            $map[] = ['b.parent_guid', '=', $this->get_bid()];
        } else {
            error('暂时不支持查询');
        }
        $this->model = $db_fast_register_weapp->alias('frw')->join($join)->field($field)->where($map);
        result($this->_list());
    }

    protected function get_code_type($code)
    {
        if (preg_match("/[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/", $code)) {
            return 1; // 18位统一社会信用代码
        }
        if (preg_match("/[a-zA-Z0-9]{8}-[a-zA-Z0-9]/", $code)) {
            return 2; //12345678-1 组织机构代码（9 位 xxxxxxxx-x）
        }
        if (preg_match("/[1-9]\d{15}/", $code)) {
            return 3; // 15位社会信用代码
        }
        throw new NotNotifyException('公司代码可能有误');
    }

    public function query()
    {
        $db_fast_register_weapp = new \app\model\FastRegisterWeapp();
        $params                 = $this->params;
        $guid                   = $params['guid'];
        $map                    = [['guid', '=', $guid]];
        $note                   = $db_fast_register_weapp->where($map)->findOrFail();
        $examine_status         = $note['examine_status'];
        if ($examine_status == 0) {
            error('仅支持未成功记录查询');
        }
        $name                 = $note['name'];
        $legal_persona_wechat = $note['legal_persona_wechat'];
        $legal_persona_name   = $note['legal_persona_name'];
        $instance             = weixin()::WeOpenMiniApp();
        $post_data            = [
            'name'                 => $name, //企业名
            'legal_persona_wechat' => $legal_persona_wechat, //法人微信
            'legal_persona_name'   => $legal_persona_name, //法人姓名
        ];
        $update_data          = [];
        try {
            $result                        = $instance->searchRegisterMiniprogram($post_data);
            $update_data['status']         = 1;
            $update_data['examine_result'] = $result['errmsg'];
        } catch (Exception|Throwable $e) {
            $update_data['status']         = -1;
            $update_data['examine_status'] = $e->getCode();
            $msg_array                     = [
                '89247' => '内部错误',
                '89250' => '未找到该任务',
                '89251' => '模板消息已下发，待法人人脸核身校验',
                '89252' => '法人&企业信息一致性校验中',
                '89253' => 'lack of some params',
            ];
            $update_data['examine_result'] = $msg_array[$e->getCode()] ?? $e->getMessage();
        }
        $db_fast_register_weapp::update($update_data, $map);
        success('查询成功');
    }

    /**
     *获取列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params                 = $this->params;
        $db_fast_register_weapp = new \app\model\FastRegisterWeapp();
        $guid                   = $params['guid'] ?? null;
        if ($guid) {
            //说明是重发,数据从数据库中取
            $map                  = [['guid', '=', $guid]];
            $note                 = $db_fast_register_weapp->where($map)->findOrFail();
            $code                 = $note['code'];
            $code_type            = $note['code_type'];
            $name                 = $note['name'];
            $legal_persona_wechat = $note['legal_persona_wechat'];
            $legal_persona_name   = $note['legal_persona_name'];
            $bid                  = $note['bid'];
        } else {
            //新创建
            $code                 = $params['code'];
            $code_type            = $this->get_code_type($code);
            $name                 = $params['name'];
            $legal_persona_wechat = $params['legal_persona_wechat'];
            $legal_persona_name   = $params['legal_persona_name'];
            $bid                  = $params['bid'];
        }
        $component_phone = '18603047034';
        $guid            = create_guid();
        $data            = [
            'guid'                 => $guid,
            'bid'                  => $bid,
            'create_bid'           => $this->get_bid(),
            'name'                 => $name,
            'code'                 => $code,
            'code_type'            => $code_type,
            'legal_persona_wechat' => $legal_persona_wechat,
            'legal_persona_name'   => $legal_persona_name,
            'component_phone'      => $component_phone,
            'status'               => 0,
        ];
        $db_fast_register_weapp->save($data);
        $instance    = weixin()::WeOpenMiniApp();
        $post_data   = [
            'name'                 => $name, //企业名
            'code'                 => $code, //企业代码
            'code_type'            => $code_type, // 企业代码类型（1：统一社会信用代码， 2：组织机构代码，3：营业执照注册号）
            'legal_persona_wechat' => $legal_persona_wechat, //法人微信
            'legal_persona_name'   => $legal_persona_name, //法人姓名
            'component_phone'      => $component_phone, //第三方联系电话
        ];
        $update_data = [];
        try {
            $result                        = $instance->registerMiniprogram($post_data);
            $update_data['status']         = 1;
            $update_data['examine_result'] = $result['errmsg'];
        } catch (Exception|Throwable $e) {
            $update_data['status']         = -1;
            $update_data['examine_status'] = $e->getCode();
            $update_data['examine_result'] = $e->getMessage();
        }

        $map = [['guid', '=', $guid]];
        $db_fast_register_weapp::update($update_data, $map);
        success('添加成功');
    }


    /**
     * 新版企业信息查询，使用riskbird接口
     * @return void
     */
    public function get_company_detail()
    {
        $params       = $this->params;
        $company_name = $params['name'];
        $result       = CompanyQuery::default()->getDetail($company_name);
        result($result);
    }
}
