<?php

namespace app\controller\admin_api\v1;

use app\model\BusinessVersionSettlementPrice as BusinessVersionSettlementPriceModel;
use Exception;

class BusinessVersionSettlementPrice extends BasicAdminApi
{
    /**
     *编辑卡券折扣
     * @access public
     * @return void
     * @throws Exception
     */
    public function edit()
    {
        $params           = $this->params;
        $bid              = $this->get_bid();
        $db               = new BusinessVersionSettlementPriceModel();
        $version_guid     = $params['version_guid'];
        $settlement_price = $params['settlement_price'];
        $bid              = $params['bid'];
        if ($settlement_price < 0) {
            error('系数需要大于0');
        }

        $map  = [
            'bid'          => $bid,
            'version_guid' => $version_guid,
        ];
        $note = $db->where($map)->findOrEmpty();
        if (!$note->isEmpty()) {
            $update_data = ['settlement_price' => $settlement_price];
            $db::update($update_data, $map);
            success('修改成功!');
        } else {
            $data = [
                'guid'             => create_guid(),
                'bid'              => $bid,
                'settlement_price' => $settlement_price,
                'version_guid'     => $version_guid,
            ];
            $db->save($data);
            success('修改成功');
        }
    }

    /**
     *折扣
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $referer = $this->request->header('referer');
        $bid     = $this->get_bid();
        $arr     = tools()::parse_url_params($referer);//将url解析成参数数组
        $arr     = $this->params;
        if (empty($arr['bid'])) {
            error('缺少bid参数');
        }
        $db  = new \app\model\Version();
        $bid = $arr['bid'];
        unset($this->params['bid']);
        $join        = [
            ['business_version_settlement_price bvsp', "bvsp.version_guid = v.guid  AND  bvsp.bid='$bid'", 'LEFT'],
        ];
        $map         = [
            ['v.status', '=', 1],
        ];
        $field       = [
            'v.guid'                            => 'version_guid',
            'v.name'                            => 'version_name',
            "IFNULL(bvsp.bid, '$bid')"          => 'bid',
            'IFNULL(bvsp.settlement_price, "")' => 'settlement_price',
        ];
        $this->model = $db->alias('v')->field($field)->join($join)->where($map)->order(['v.create_time' => 'DESC']);
        result($this->_list());
    }
}