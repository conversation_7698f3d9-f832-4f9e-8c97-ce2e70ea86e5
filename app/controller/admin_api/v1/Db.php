<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use Exception;
use tp5er\Backup\Backup;

class Db extends BasicAdminApi
{
    /**
     *数据库
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db = new Backup();
        result($db->dataList());
    }

    /**
     *文件备份列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function backup_file_list()
    {
        $db = new Backup();
        result($db->fileList());
    }

    /**
     *删除备份
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function del_backup_file()
    {
        $db   = new Backup();
        $time = $this->params['time'];
        $del  = $db->delFile($time);
        if ($del) {
            success('备份删除成功');
        } else {
            error('备份删除失败');
        }
    }

    /**
     *还原
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function import()
    {
        ignore_user_abort(true);
        set_time_limit(0);
        $params     = $this->params;
        $table_name = $params['table_name'];
        $part       = $params['part'];
        $time       = $params['time'];
        $db         = new Backup();
        $file       = ['name' => $table_name, 'part' => $part];
        $start      = 0;
        $size       = 0;
        while ($start == 0 || $start < $size) {
            $info = $db->setFile($file)->import($start, $time);
            if (!$info) {
                error('还原失败');
            }
            $start = $info[0];
            $size  = $info[1];
        }
        success('还原成功');
    }

    /**
     *备份
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function backup()
    {
        $table_name = $this->params['table_name'];
        $db         = new Backup();
        $file       = ['name' => date('Ymd-His'), 'part' => 1];
        $backup     = $db->setFile($file)->backup($table_name, 0);
        success('备份成功');
    }
}