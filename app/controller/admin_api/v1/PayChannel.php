<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2016/12/17
 * Time: 19:38
 */

namespace app\controller\admin_api\v1;

use app\model\PayChannel as PayChannelModel;
use app\model\PayChannelProvider;
use Exception;

class PayChannel extends BasicAdminApi
{
    /**
     *获取通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get()
    {
        result($this->_list());
    }

    /**
     *添加通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $params    = $this->params;
        $form_data = tools()::_parse_fs_form_data($params, 'channel_parameter');
        $key_name  = $form_data['key'];
        $db        = new PayChannelModel();
        $map       = [['key', '=', $key_name]];
        $count     = $db->where($map)->count();
        if ($count > 0) {
            error('您输入的key已经存在');
        }
        $form_data['status'] = isset($form_data['status']) ? $form_data['status'] : 0;
        $update              = $db->save($form_data);
        if ($update) {
            success('更新成功');
        } else {
            error('更新失败');
        }
    }

    /**
     *快速添加
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function quick_add()
    {
        $params = $this->params;
        $bid    = $params['bid'];
        if (!empty($bid)) {
            $db  = new \app\model\Business();
            $map = [['account', '=', $bid]];
            $bid = $db->where($map)->value('guid');
            if (empty($bid)) {
                error('您输入的代理商账号不存在');
            }
        }
        $insert_data['bid']                 = $bid;
        $insert_data['name']                = $params['name'];
        $channel_provider_id                = $params['channel_provider_id'];
        $insert_data['channel_provider_id'] = $channel_provider_id;
        $scene                              = $this->request->param('scene/a');
        $scene_id                           = 0;
        foreach ($scene[$channel_provider_id] as $key => $val) {
            $scene_id += intval($key);
        }
        $insert_data['scene_id']          = $scene_id;
        $db_pay_channel_provider          = new PayChannelProvider();
        $domain                           = $this->request->domain();
        $insert_data['channel_parameter'] = $db_pay_channel_provider->build_parameter($channel_provider_id, $params, $domain, 'init_channel_parameter');
        $db_pay_channel                   = new PayChannelModel();
        $insert                           = $db_pay_channel->save($insert_data);
        if ($insert) {
            success('添加成功');
        } else {
            error('添加失败');
        }
    }

    /**
     *编辑通道
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        if (!empty($params['id'])) {
            //单纯编辑支付场景
            $scene    = $this->request->param('scene/a');
            $scene_id = 0;
            foreach ($scene as $key => $val) {
                $scene_id += intval($key);
            }
            $update_data['id']       = $params['id'];
            $update_data['scene_id'] = $scene_id;
        } else {
            //正常编辑支付参数
            $update_data           = tools()::_parse_fs_form_data($params, 'channel_parameter');
            $update_data['status'] = isset($update_data['status']) ? $update_data['status'] : 0;
        }
        $id     = intval($update_data['id']);
        $db     = new PayChannelModel();
        $map    = [['id', '=', $id]];
        $update = $db::update($update_data, $map);
        if ($update) {
            success('更新成功');
        } else {
            error('更新失败');
        }
    }

    /**
     *获取参数
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_parameter()
    {
        $id      = $this->params['id'];
        $db      = new PayChannelModel();
        $map     = [['id', '=', $id]];
        $channel = $db->where($map)->find();
        result(tools()::_array_to_fs_table_data($channel['channel_parameter']));
    }
}