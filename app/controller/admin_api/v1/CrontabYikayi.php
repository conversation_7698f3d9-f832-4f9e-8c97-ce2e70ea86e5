<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\Crontab as CrontabModel;
use app\model\CrontabClass as CrontabClassModel;
use Exception;
use think\facade\Db;

class CrontabYikayi extends BasicAdminApi
{
    /**
     *定时任务列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_crontab_class = new CrontabClassModel();
        $params           = $this->params;
        $bid              = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        unset($this->params['bid']); //bid无需自动到$map中
        $join        = [
            ['crontab c', "c.class = cc.class AND c.bid='$bid'", 'LEFT'],
        ];
        $map         = [];
        $field       = [
            'cc.*',
            Db::raw("IFNULL(c.`crontab_string`,cc.`default_crontab_string`) AS crontab_string"),
            Db::raw("IFNULL(c.`interval_sec`,cc.`default_interval_sec`) AS interval_sec"),
            Db::raw("IFNULL(c.`interval_unit`,cc.`default_interval_unit`) AS interval_unit"),
            Db::raw("IFNULL(c.`status`,-1) AS status"),
            Db::raw("IFNULL(c.`last_execute_time`,'') AS last_execute_time"),
            Db::raw("IFNULL(c.`connections`,'') AS connections"),
        ];
        $this->model = $db_crontab_class->alias('cc')->where($map)->join($join)->field($field)->order(['status' => 'DESC', 'cc.name' => 'ASC']);
        result($this->_list());
    }

    /**
     *启动任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function start()
    {
        $db_crontab_class = new CrontabClassModel();
        $params           = $this->params;
        $guid             = $params['guid'];
        $map              = [['guid', '=', $guid]];
        $crontab_template = $db_crontab_class->where($map)->findOrFail();
        $class            = $crontab_template['class'];
        $bid              = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $map              = [
            ['bid', '=', $bid],
            ['class', '=', $class]
        ];
        $db_crontab       = new CrontabModel();
        $crontab_info     = $db_crontab->where($map)->findOrEmpty();
        if ($crontab_info->isEmpty()) {
            //todo 如果没有则创建任务
            $crontab_type = $crontab_template['default_crontab_string'] ? 'input' : 'select';
            $data         = [
                'guid'              => create_guid(),
                'bid'               => $bid,
                'name'              => $crontab_template['name'],
                'class'             => $class,
                'next_execute_time' => format_timestamp(),
                'status'            => 1,
                'payload'           => [],
                'crontab_type'      => $crontab_type,
                'interval_sec'      => $crontab_template['default_interval_sec'],
                'interval_unit'     => $crontab_template['default_interval_unit'],
                'crontab_string'    => $crontab_template['default_crontab_string']
            ];
            $result       = $db_crontab->save($data);
            success('任务创建成功');
        } else {
            $update_data = ['status' => 1];
            $db_crontab::update($update_data, $map);
            success('任务启动成功');
        }
    }

    /**
     *停止任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function stop()
    {
        $db_crontab_class = new CrontabClassModel();
        $params           = $this->params;
        $guid             = $params['guid'];
        $map              = [['guid', '=', $guid]];
        $crontab_template = $db_crontab_class->where($map)->findOrFail();
        $class            = $crontab_template['class'];
        $bid              = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $map              = [
            ['bid', '=', $bid],
            ['class', '=', $class]
        ];
        $update_data      = ['status' => 0];
        $db_crontab       = new CrontabModel();
        $db_crontab::update($update_data, $map);
        success('任务停止成功');
    }


    /**
     *立即执行某个定时任务
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function execute()
    {
        $db_crontab_class = new CrontabClassModel();
        $params           = $this->params;
        $guid             = $params['guid'];
        $map              = [['guid', '=', $guid]];
        $crontab_template = $db_crontab_class->where($map)->findOrFail();
        $class            = $crontab_template['class'];
        $bid              = !empty($params['bid']) ? $params['bid'] : $this->get_bid();
        $map              = [
            ['bid', '=', $bid],
            ['class', '=', $class]
        ];
        $db_crontab       = new CrontabModel();
        $job              = $db_crontab->where($map)->findOrFail();
        $db_crontab->resolve($job);
        success('任务执行成功');
    }
}