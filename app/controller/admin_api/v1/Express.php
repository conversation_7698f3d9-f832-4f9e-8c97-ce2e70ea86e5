<?php

namespace app\controller\admin_api\v1;

use Exception;

class Express extends BasicAdminApi
{
    /**
     * 快递公司管理
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [];
        $this->model = $this->model->where($map)->order(['status' => 'DESC', 'sort' => 'ASC']);
        result($this->_list());
    }

    /**
     *查单
     * @access public
     * @return void
     * @throws Exception
     */
    public function query()
    {
        $params        = $this->params;
        $db_express    = new \app\model\Express();
        $params['way'] = 2;
        $result        = $db_express->query_route($params);
        result($result);
    }
}