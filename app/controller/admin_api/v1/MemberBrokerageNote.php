<?php

namespace app\controller\admin_api\v1;

use Exception;

class MemberBrokerageNote extends BasicAdminApi
{

    /**
     *充值
     * @access public
     * @return void
     * @throws Exception
     */
    public function recharge()
    {
        $params                 = $this->params;
        $bid                    = $this->get_bid();
        $db_user_brokerage_note = new \app\model\MemberBrokerageNote();
        $type                   = $params['type'];
        $user_guid              = $params['user_guid'];
        $brokerage              = $params['brokerage'];
        $data                   = [
            'bid'       => $bid,
            'user_guid' => $user_guid,
            'way'       => 1,
            'type'      => $type,
            'brokerage' => $brokerage,
            'memo'      => '[后台操作]',
        ];
        $db_user_brokerage_note->recharge_brokerage($data);
        success('操作成功');
    }

    /**
     *储值记录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new \app\model\MemberBrokerageNote();
        $join        = [
            ['member m', 'mbn.member_guid = m.guid AND mbn.bid = m.bid'],
            ['user u', 'mbn.operator_user_guid = u.guid AND mbn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'mbn.*',
            'u.account',
            "CONCAT(m.id,'-',m.name)" => 'member_info',
            "m.id"                    => 'member_id',
            "m.mobile"                => 'mobile',
        ];
        $map         = [
            ['mbn.bid', '=', $this->get_bid()],
        ];
        $this->model = $db->alias('mbn')->order(['mbn.create_time' => 'DESC'])->join($join)->field($field)->where($map);
        result($this->_list());
    }

    /**
     *充值记录统计
     * @access public
     * @return void
     * @throws Exception
     */
    public function brokerage_note_report()
    {
        $db          = new \app\model\User();
        $join        = [
            ['user u', 'umn.operator_user_guid = u.guid AND umn.bid = u.bid', 'LEFT'],
        ];
        $field       = [
            'u.id',
            'u.account',
            'u.name',
            'SUM(umn.brokerage)' => 'total_brokerage',
        ];
        $map         = [
            ['umn.bid', '=', $this->get_bid()],
        ];
        $group       = ['u.account'];
        $this->model = $db->alias('umn')->join($join)->field($field)->where($map)->group($group)->order(['total_brokerage']);
        result($this->_list());
    }
}
