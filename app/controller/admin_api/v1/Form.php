<?php

namespace app\controller\admin_api\v1;

use Exception;

class Form extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $map         = [];
        $this->model = $this->model->where($map);
        result($this->_list());
    }


    /**
     *订单详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $params    = $this->params;
        $bid       = $this->get_bid();
        $form_guid = $params['guid'];
        $map       = [
            ['guid', '=', $form_guid],
        ];
        $db_form   = new \app\model\Form();
        $info      = $db_form->where($map)->find();
        result($info);
    }
}