<?php

namespace app\controller\admin_api\v1;

use app\model\Business as BusinessModel;
use app\model\BusinessRenewNote;
use app\model\User;
use app\model\UserBindNote;
use app\model\WebsocketUser;
use app\model\XcxmallStore;
use app\common\service\TokenService;
use app\common\service\UrlService;
use app\common\tools\Visitor;
use Exception;
use think\facade\Route;
use think\facade\Db;
use think\Model;

class Business extends BasicAdminApi
{
    public function register()
    {
        $params           = $this->params;
        $company          = $params['company'];
        $parent_guid      = $params['parent_guid'];
        $parent_user_guid = $params['parent_user_guid'] ?? '';
        $db_user          = new User();
        if (empty($parent_user_guid)) {
            $parent_user_guid = $db_user->get_default_user_guid($parent_guid);
        }
        $mobile         = $params['mobile'];
        $true_name      = $params['true_name'];
        $account        = $params['account'];
        $version_guid   = $params['version_guid'];
        $token          = $params['token'];//包含appid+openid
        $way            = $params['way'] ?? 0;//注册来源
        $login_password = $params['login_password'];
        $repassword     = $params['repassword'];
        if (empty($login_password) || empty($repassword)) {
            error('请设置登录密码');
        }
        if ($login_password !== $repassword) {
            error('密码不一致,请重新输入');
        }
        //        $ip     = tools()::get_client_ip();
        //        $region = tools()::get_client_ip_region_info($ip);

        $db_business = new BusinessModel();
        //开始注册
        $map           = [['account', '=', $account]];
        $note_business = $db_business->where($map)->findOrEmpty();
        if (!$note_business->isEmpty()) {
            error('账号【' . $account . '】已存在,请换一个');
        }
        $data   = [
            'account'          => $account,
            'mobile'           => $mobile,
            'type'             => 1,
            'business_name'    => $company,
            'true_name'        => $true_name,
            'version_guid'     => $version_guid,
            'login_password'   => $login_password,
            'parent_guid'      => $parent_guid,
            'parent_user_guid' => $parent_user_guid,
            'memo'             => '在线申请试用',
            'source'           => 2, //自助申请
        ];
        $result = $note_business->add($data);
        if (!$result) {
            error($note_business->getError());
        }
        $token_data = TokenService::decode($token);
        //自动绑定
        $business_info     = $db_business->get_business_info_by_account_or_guid($account);
        $bind_bid          = $business_info['guid'];
        $bind_user_guid    = $db_user->get_default_user_guid($bind_bid);
        $db_user_bind_note = new UserBindNote();
        $bind_data         = [
            'bind_bid'       => $bind_bid,
            'bind_user_guid' => $bind_user_guid,
            'appid'          => $token_data['appid'],
            'openid'         => $token_data['openid'],
            'way'            => $token_data['way'], //1 公众号 2 小程序
            'from'           => $token_data['from'],//来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程
        ];
        $result            = $db_user_bind_note->bind_user($bind_data);
        success('开通成功');
    }


    /**
     * 商家列表
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        // 构建子查询
        // 获取当前商家的唯一标识
        $bid = $this->get_bid();
        // 定义关联查询的表和关联条件
        $join = [
            // 关联 user 表，条件为用户工号为 10000，且用户信息与 WebSocket 用户信息匹配
            ['user u', "wu.user_guid = u.guid AND u.account = 10000 AND wu.bid=u.bid AND wu.type='user'"],
        ];
        // 定义需要查询的字段
        $field = [
            'wu.bid'                 => 'bid',
            'wu.status'              => 'status',
            'wu.last_login_out_time' => 'last_login_out_time',
        ];
        // 实例化 WebsocketUser 模型
        $db_websocket_user = new WebsocketUser();
        // 构建子查询 SQL 语句
        $subQuery = $db_websocket_user->alias('wu')->join($join)->field($field)->buildSql();

        // 定义排序规则，先按在线状态降序排序，再按最后登录时间降序排序
        $order = ['online_status' => 'DESC', 'last_login_time' => 'DESC'];
        // 定义主查询的关联表和关联条件
        $join = [
            // 左关联 business 表，用于获取代理商信息
            ['business b1', 'b.parent_guid = b1.guid', 'LEFT'],
            // 左关联子查询结果，用于获取 WebSocket 用户信息
            [[$subQuery => 'wu'], 'wu.bid= b.guid', 'LEFT'],
        ];
        // 定义主查询需要查询的字段
        $field = [
            'DISTINCT b.*',
            'b.account',
            'b1.account'                                  => 'agent_account',
            // 拼接代理商名称和账号
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
            "wu.status"                                   => 'online_status',
            "wu.last_login_out_time"                      => 'last_login_out_time',
        ];
        // 定义查询条件，过滤掉已删除的商家
        $map = [
            ['b.delete_time', 'null', null]
        ];

        $params = $this->params;
        if (!empty($params['key']) && strpos($params['key'], 'id&') !== false) {
            //输入200--2000 这种方式 则说明是通过id精确查询
            $value = $params['value'] ?? '';
            if (is_numeric($value) && $value > 200 && $value < 2000) {
                $map[] = ['b.id', '=', $value];
                unset($this->params['key']);
            }
        }
        // 实例化 BusinessModel 模型
        $db_business = new BusinessModel();
        // 判断当前用户是否为代理商
        if ($db_business->is_agent()) {
            // 如果是代理商，只查询该代理商下的商家
            $map[] = ['b.parent_guid', '=', $bid];
        } // 判断当前用户是否为普通商家
        elseif ($db_business->is_business($bid)) {
            // 如果是普通商家，提示无权限查看该页面
            error('商家类型无权限查看该页面');
        }
        // 构建查询对象，设置表别名、追加字段、查询条件、关联表、查询字段和排序规则
        $this->model = $this->model->alias('b')->append(['is_expired'])->where($map)->join($join)->field($field)->order($order);
        // 调用 _list 方法获取列表数据并返回结果
        result($this->_list());
    }


    /**
     * 转移商家
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change()
    {
        $params        = $this->params;
        $business_guid = $params['guid'];
        $parent_guid   = $params['parent_guid'];
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_account_or_guid($business_guid);
        if ($business_info['parent_guid'] == $parent_guid) {
            success('修改成功,无修改');
        }
        $parent_business_info = $db_business->get_business_info_by_account_or_guid($parent_guid);
        $db_user              = new User();
        $parent_admin_user    = $db_user->get_admin_user($parent_guid);
        $parent_user_guid     = $parent_admin_user['guid'];
        $map                  = [['guid', '=', $business_guid]];
        $update_data          = [
            'parent_guid'      => $parent_guid,
            'parent_user_guid' => $parent_user_guid,
        ];
        $db_business::update($update_data, $map);
        wr_log('商家:' . $business_info['account'] . '成功转移给代理商' . $parent_business_info['account']);
        $db_business->remove_cache($business_guid);
        success('转移成功');
    }

    /**
     * 转移商家
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function change_bak()
    {
        $params        = $this->params;
        $business_guid = $params['guid'];
        $parent_guid   = $params['parent_guid'];
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_account_or_guid($business_guid);
        if ($business_info['parent_guid'] == $parent_guid) {
            success('修改成功,无修改');
        }
        $parent_business_info = $db_business->get_business_info_by_account_or_guid($parent_guid);
        $wxapp_admin_id       = $parent_business_info['wxapp_admin_id'];
        if ($wxapp_admin_id == 0) {
            error('转移失败,所选代理商不是小程序代理');
        }
        $db_user           = new User();
        $parent_admin_user = $db_user->get_admin_user($parent_guid);
        $parent_user_guid  = $parent_admin_user['guid'];
        $map               = [['guid', '=', $business_guid]];
        $update_data       = [
            'parent_guid'      => $parent_guid,
            'parent_user_guid' => $parent_user_guid,
        ];
        $db_business::update($update_data, $map);
        $mall_store_id = $business_info['mall_store_id'];
        if ($mall_store_id > 0) {
            //修改小程序数据库
            $db_store    = new  XcxmallStore();
            $map         = [
                ['id', '=', $mall_store_id]
            ];
            $update_data = [
                'admin_id' => $wxapp_admin_id,
            ];
            $db_store::update($update_data, $map);
        }
        wr_log('商家:' . $business_info['account'] . '成功转移给代理商' . $parent_business_info['account']);
        $db_business->remove_cache($business_guid);
        success('转移成功');
    }

    /**
     * 所有小程序商家
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function all_weapp_business()
    {
        $db    = new BusinessModel();
        $join  = [
            ['business b1', 'b.parent_guid = b1.guid'],
        ];
        $field = [
            'b.guid',
            'b.mall_store_id',
            'b.account',
            'b.business_name',
            'b.mobile',
            'b.create_time',
            'b.expired_time',
            'b.license_status',
            'b.renewal_amount',
            'b.memo',
            'b1.account'                                  => 'agent_account',
            "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
        ];
        //默认查看自己的子集
        $version_guid = 'a43f4869-5d32-2989-fcf7-195fac110046';
        $map          = [
            ['b.version_guid', '=', $version_guid],
            ['b.mall_store_id', '>', 0]
        ];
        $this->model  = $db->alias('b')->join($join);
        $order        = ['b.mall_store_id' => 'DESC'];
        $this->model  = $this->model->order($order)->append(['is_expired'])->field($field)->where($map);
        result($this->_list());
    }

    /**
     * 小程序商家
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function weapp_business()
    {
        $db           = new BusinessModel();
        $join         = [
            ['user u', "b.parent_user_guid=u.guid AND b.parent_guid=u.bid"],
        ];
        $version_guid = 'a43f4869-5d32-2989-fcf7-195fac110046';
        $map          = [
            ['b.version_guid', '=', $version_guid],
            ['b.mall_store_id', '>', 0],
            ['b.parent_guid', '=', $this->get_bid()]
        ];
        $db_rule      = new \app\model\Rule();
        if ($db_rule->is_admin() === false) {
            $map[] = ['b.parent_user_guid', '=', $this->get_user_guid()];
        }
        $field       = [
            'b.guid',
            'b.mall_store_id',
            'b.account',
            'b.business_name',
            'b.mobile',
            'b.create_time',
            'b.expired_time',
            'b.license_status',
            'b.renewal_amount',
            'b.memo',
            //"CONCAT(u.business_name,'(',b1.account,')')" => 'agent',
            "CONCAT(u.name)" => 'service',
        ];
        $order       = ['b.mall_store_id' => 'DESC'];
        $this->model = $db->alias('b')->join($join)->order($order)->append(['is_expired'])->field($field)->where($map);
        result($this->_list());
    }

    /**
     * 跳转到admin后台
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function goto_admin()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $goto_bid = $params['goto_bid'] ?? null;
        $goto_sid = $params['goto_sid'] ?? null;
        if (!$goto_bid && !$goto_sid) {
            error('商家信息不能为空');
        }
        $goto_user_guid = $params['goto_user_guid'] ?? null;
        $db_business    = new BusinessModel();
        if ($goto_sid && empty($goto_bid)) {
            $goto_bid = $db_business->where([['id', '=', $goto_sid]])->value('guid');
            if (!$goto_bid) {
                error('商家ID不存');
            }
        }
        $business_info      = $db_business->get_business_info_by_account_or_guid($bid);
        $goto_business_info = $db_business->get_business_info_by_account_or_guid($goto_bid);
        if (empty($goto_business_info)) {
            error('商家账号不存');
        }
        $type = $business_info['type'];
        switch ($type) {
            case 1:
                error('商户无权限模拟登陆商户后台');
                break;
            case 2:
                if ($goto_business_info['parent_guid'] !== $business_info['guid']) {
                    error('只能模拟登陆自己名下的商户');
                }
        }
        //校验商家是否过期等
        $business_info = $db_business->login_verify($goto_business_info['account'], false);
        if ($business_info === false) {
            error($db_business->getError());
        }
        //查找10000工号信息
        $db_user   = new User();
        $user_info = $goto_user_guid ? $db_user->get_user_info($goto_user_guid, $goto_bid) : $db_user->get_admin_user($goto_business_info['guid']);
        if ($user_info === false) {
            error($db_user->getError());
        }
        $data = $db_user->get_access_token($user_info, 'client_credential', true);
        //       $url  = (string)Route::buildUrl('admin/common/redirect', ['access_token' => $data['access_token'], 'expires_in' => $data['expires_in']])->suffix(true)->https(true)->domain(true);
        $url = (string)Route::buildUrl('admin/common/quick_login', ['business_user_token' => $data['access_token'], 'expires_in' => $data['expires_in'], 'goto_bid' => $goto_bid])->suffix(true)->https(false)->domain(true);
//        $url = (string)url('admin/common/redirect', ['access_token' => $data['access_token'], 'expires_in' => $data['expires_in']], true, true);
        $url = tools()::replace_www_to_readonly($url);
        $url = str_replace('https', 'http', $url); //替换成http
        result(['url' => $url]);
    }

    public function reset_password()
    {
        $params         = $this->params;
        $reset_bid      = $params['reset_bid'];
        $login_password = get_system_config('default_login_password');
        $userModel      = new User();
        $update_data    = [
            'password'           => $login_password,
            'login_failed_times' => 0,
        ];
        $map            = [
            ['bid', '=', $reset_bid],
            ['account', '=', 10000],
        ];
        $userModel::update($update_data, $map);
        success('重置成功!');
    }

    /**
     * 访问商城
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function goto_mall()
    {
        $params        = $this->params;
        $store_id      = $params['store_id'];
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_account_or_guid($this->get_bid());
        if (empty($business_info['wxapp_admin_id'])) {
            error('暂无权限');
        }
        $data = [
            'store_id' => $store_id,
            'r'        => 'admin/passport/entry',
            'admin_id' => $business_info['wxapp_admin_id']
        ];
        $url  = UrlService::build_date_time_sign_url('https://kxsc.kuxiang168.cn/web/index.php', $data);
        result(['url' => $url]);
    }

    /**
     * //续费 单次代表1年 扣除一个授权
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function renew()
    {
        $params                 = $this->params;
        $db_business_renew_note = new BusinessRenewNote();
        $renew                  = $db_business_renew_note->renew($params);
        if ($renew) {
            success('续费成功');
        } else {
            error('续费失败:' . $db_business_renew_note->getError());
        }
    }

    public function renew_or_buy()
    {
        $params                 = $this->params;
        $data                   = [
            'renew_type' => 4, //1年
            'guid'       => $params['business_guid'] //被延期的商家guid
        ];
        $db_business_renew_note = new BusinessRenewNote();
        $renew                  = $db_business_renew_note->renew($data);
        if ($renew) {
            success('操作成功');
        } else {
            error('操作失败:' . $db_business_renew_note->getError());
        }
    }

    /**
     * //续费 单次代表1年 扣除一个授权
     * @access public
     * @return mixed
     * @throws Exception
     */

    public function renew_old()
    {
        $params        = $this->params;
        $business_guid = $params['business_guid'];
        $db_business   = new BusinessModel();
        $renew         = $db_business->renew_old($business_guid, $this->get_bid());
        if ($renew) {
            success('续费成功');
        } else {
            error('续费失败:' . $db_business->getError());
        }
    }

    /**
     *获取配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_config()
    {
        $bid    = $this->get_bid();
        $config = get_config_by_bid($bid);
        $params = $this->params;
        if (!empty($params['device_id'])) {
            $db              = new BusinessModel();
            $map             = [['guid', '=', $bid]];
            $parent_business = $db->where($map)->field(['parent_guid'])->find();
            if (!empty($parent_business['parent_guid'])) {
                $map                        = [['guid', '=', $parent_business['parent_guid']]];
                $parent_business_info       = $db->where($map)->field(['business_name', 'mobile'])->find();
                $config['device_copyright'] = $parent_business_info['business_name'] . '/服务电话：' . $parent_business_info['mobile'];
            } else {
                $config['device_copyright'] = get_system_config('device_copyright'); //无挂靠代理商的设备,直接取全局参数
            }
        }
        result($config);
    }

    /**
     *商家信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function info()
    {
        $db_business   = new BusinessModel();
        $params        = $this->params;
        $business_guid = !empty($params['guid']) ? $params['guid'] : $this->get_bid();
        $data          = $db_business->find($business_guid);
        //        $data          = $db_business->cache('info:' . $business_guid, 60)->find($business_guid);
        result($data);
    }

    /**
     *退出登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function login_out()
    {
        $bid           = $this->get_bid();
        $user_guid     = $this->get_user_guid();
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $db_user       = new User();
        $user_info     = $db_user->get_user_info($user_guid, $bid);
        wr_log('商家:' . $business_info['business_name'] . ',工号:' . $user_info['account'] . ' 退出了系统');
        $key = $this->get_bid() . '_business_user_token';
        cookie($key, null);
        success('退出成功');
    }

    /**
     *登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function scan_login()
    {
        $params            = $this->params;
        $uuid              = $params['uuid'];
        $bind_note_guid    = $params['bind_note_guid'];
        $openid            = $this->get_openid();
        $bid               = $this->get_bid();
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['guid', '=', $bind_note_guid],
        ];
        $bind_note         = $db_user_bind_note->where($map)->find();
        $db_user           = new User();
        $map               = [
            ['bid', '=', $bind_note['bid']],
            ['guid', '=', $bind_note['user_guid']],
        ];
        $user_info         = $db_user->where($map)->findOrEmpty();
        if ($user_info->isEmpty()) {
            error('用户不存在');
        }
        cache($uuid, tools()::object2array($user_info), 60);
        $socket_data = ['code' => 0, 'msg' => '登录成功', 'data' => ['status' => 1, 'uuid' => $uuid]];
        publish_websocket($socket_data, $uuid);
        result($socket_data);
    }

    /**
     *获取用户绑定列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_bind_user_list()
    {
        $params = $this->params;
        if (empty($this->get_appid()) || empty($this->get_openid())) {
            error('appid或openid为空');
        }
        $uuid        = $params['uuid'];
        $socket_data = ['code' => 0, 'msg' => '扫码成功', 'data' => ['status' => 0, 'uuid' => $uuid]];
        publish_websocket($socket_data, $uuid); //推送扫码成功的提示
        $db_user_bind_note = new UserBindNote();
        $map               = [
            ['ubn.appid', '=', $this->get_appid()],
            ['ubn.openid', '=', $this->get_openid()],
            ['ubn.status', '=', 1],
            ['b.delete_time', 'NULL', null] //过滤已删除的商家
        ];
        $join              = [
            ['business b', 'ubn.bid = b.guid'],
            ['user u', 'ubn.bid=u.bid AND ubn.user_guid=u.guid'],
        ];
        $field             = [
            'ubn.bid',
            'ubn.guid'  => 'bind_note_guid',
            'b.account' => 'business_account',
            'b.business_name',
            'u.account' => 'user_account',
            'u.name'    => 'user_name',
        ];
        $order             = ['u.bid' => 'DESC', 'u.account' => 'ASC', 'ubn.create_time' => 'DESC']; //按照商家/工号/绑定时间排序
        $list              = $db_user_bind_note->alias('ubn')->where($map)->join($join)->field($field)->order($order)->select();
        result($list);
    }

    /**
     *获取token
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_token()
    {
        $params     = $this->params;
        $grant_type = $params['grant_type'];
        if (Visitor::is_standby_domain()) {
            error('请使用主域名' . config('app.app_host_domain') . '/admin 登录后台!');
        }
        switch ($grant_type) {
            case 'password':
                return $this->login_by_password();
            case 'code':
                return $this->login_by_code();
            case 'client_credential':
                return $this->login_by_appid();
            default:
                throw new Exception('不支持的登录方式');
        }
    }

    /**
     *通过密码登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function login_by_password()
    {
        if (Visitor::is_readonly()) {
            error('当前网址不允许登录,请联系管理员');
        }
        $params = $this->params;
        //校验次数过多锁定
        $business_account = $params['business_account'];
        $business_account = tools()::remove_empty_string($business_account);
        //校验商家信息
        $db_business   = new BusinessModel();
        $business_info = $db_business->login_verify($business_account);
        if ($business_info === false) {
            error($db_business->getError());
        }
        //校验工号信息
        $db_user                 = new User();
        $params['business_guid'] = $business_info['guid'];
        $user_info               = $db_user->login_verify($params);
        if ($user_info === false) {
            // 登录失败要记录在日志里
            error($db_user->getError());
        }
        $data = $db_user->get_access_token($user_info, 'password');
        if (!empty($params['device_id'])) {
            $device_id   = $params['device_id'];
            $db_device   = new \app\model\Device();
            $map         = [['sn', '=', $device_id]];
            $update_data = [
                'last_login_bid'       => $business_info['guid'],
                'last_login_user_guid' => $user_info['guid'],
                'last_login_time'      => microsecond()
            ];
            $db_device::update($update_data, $map);
        }
        // data 是当前登录用户对象实例
        result($data);
    }

    /**
     *通过code登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function login_by_code()
    {
        $params = $this->params;
        //校验次数过多锁定
        $code      = $params['code'];
        $user_info = cache($code);
        if (!$user_info) {
            error('code已失效');
        }
        $db_user = new User();
        //需要校验商家是否过期 是否锁定等
        $verify = $db_user->verify_user_info($user_info);
        if ($verify === false) {
            error($db_user->getError());
        }
        $data = $db_user->get_access_token($user_info, 'code');
        cache($code, null);//清空凭证,确保扫码一次有效
        result($data);
    }

    /**
     *通过appid登录
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function login_by_appid()
    {
        $params = $this->params;
        $appid  = $params['appid'];
        $secret = $params['secret'];
        //通过appid+secret查找商家信息
        $db_business   = new BusinessModel();
        $business_info = $db_business->get_business_info_by_appid_secret($appid, $secret);
        if ($business_info === false) {
            error($db_business->getError());
        }
        //校验商家是否过期等
        $business_info = $db_business->login_verify($business_info['account']);
        if ($business_info === false) {
            error($db_business->getError());
        }
        //查找10000工号信息
        $db_user   = new User();
        $user_info = $db_user->get_admin_user($business_info['guid']);
        if ($user_info === false) {
            error($db_user->getError());
        }
        $data = $db_user->get_access_token($user_info, 'client_credential');
        result($data);
    }

    /**
     *充值续费
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function recharge()
    {
        $param                 = $this->params;
        $db_business           = new BusinessModel();
        $param['recharge_bid'] = $this->get_bid();
        $param['source']       = 1; //来源 后台充值 或者移动支付等
        $recharge              = $db_business->recharge($param);
        if ($recharge) {
            success('充值成功');
        } else {
            error('充值失败:' . $db_business->getError());
        }
    }

    public function do_job()
    {
        $params        = $this->params;
        $job_id        = intval($params['job_id']);
        $business_guid = $params['business_guid'];
        $params        = $params['params'] ?? '';
        // <option value="1">回退订单</option>
        // <option value="2">解密制卡批次</option>
        switch ($job_id) {
            case 1:
                //回退订单
                $db_goods_order = new \app\model\GoodsOrder();
                $db_goods_order->del(['bid' => $business_guid, 'bill_number' => $params]);
                break;
            case 2:
                if (empty($params)) {
                    error('请输入具体批次码');
                }
                $db_coupon_generate_note = new \app\model\CouponGenerateNote();
                $map                     = [
                    ['bid', '=', $business_guid],
                ];
                if (strtoupper($params) == 'ALL') {
                    $map[] = ['safe_level', '=', 1];
                } else {
                    $map[] = ['batch', '=', $params];
                }
                $db_coupon_generate_note::update(['safe_level' => 0], $map);
                $count = $db_coupon_generate_note::getNumRows();
                success('共处理' . $count . '个批次码');
                break;
            case 3:
                $db_business = new BusinessModel();
                $result      = $db_business->change_account(['guid' => $business_guid, 'new_account' => $params]);
                if ($result === false) {
                    error($db_business->getError());
                }
                break;

            case 4:
                $db_goods_order = new \app\model\GoodsOrder();
                $update_data    = [
                    'status'                    => 0,
                    'send_type'                 => 0,
                    'express_code'              => '',
                    'express_no'                => '',
                    'send_out_goods_remark'     => '',
                    'send_or_pick_up_time'      => NULL,
                    'send_or_pick_up_user_id'   => 0,
                    'send_or_pick_up_user_guid' => ''
                ];
                $params         = explode(',', $params);
                $map            = [
                    ['bid', '=', $business_guid],
                    ['bill_number', 'IN', $params],
                ];
                $db_goods_order::update($update_data, $map);
                break;
            case 5:
                //回退卡号(用于直接已卡号维度回退,解决拆单问题)
                $db_coupon_send_note = new \app\model\CouponSendNote();
                $db_coupon_send_note->revoke($business_guid, $params);
                break;
            case 6:
                //用于解决卡号或密码导入时前面0未成功导入,进行自动补全
                if (empty($params)) {
                    error('请输入具体批次码');
                }
                $db_coupon_send_note = new \app\model\CouponSendNote();
                $map                 = [
                    ['bid', '=', $business_guid],
                    ['batch', '=', $params],
                ];
                $field               = [
                    Db::raw("LENGTH(code) AS max_code_length"),
                    Db::raw("LENGTH(password) AS max_password_length"),
                ];
                $max_length          = $db_coupon_send_note::where($map)->field($field)->find();
                $max_code_length     = $max_length['max_code_length'];
                $max_password_length = $max_length['max_password_length'];
                $update_data         = [
                    'code'     => Db::raw("LPAD(code, $max_code_length, '0')"),
                    'password' => Db::raw("LPAD(password, $max_password_length, '0')"),
                ];
                $where_or            = [
                    [Db::raw("LENGTH(code)"), '<', $max_code_length],
                    [Db::raw("LENGTH(password)"), '<', $max_password_length]
                ];
                $update              = $db_coupon_send_note->where($map)->where(function ($query) use ($where_or) {
                    /* @var $query Model */
                    $query->whereOr($where_or);
                })->update($update_data);
                $num                 = $db_coupon_send_note::getNumRows();
                if ($num) {
                    success('卡号' . $max_code_length . ';密码:' . $max_password_length . '共修改:' . $num . '张');
                } else {
                    error('所有卡号长度一致,密码长度一致,无需修改');
                }
                break;
            default:
                error('暂不支持该任务');
        }
        success('操作成功');
    }
}
