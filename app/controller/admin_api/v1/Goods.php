<?php

namespace app\controller\admin_api\v1;

use app\model\GoodsAttr;
use app\model\GoodsAttrGroup as GoodsAttrGroupModel;
use app\model\GoodsCategory;
use app\model\GoodsSku;
use app\model\GoodsSkuItem;
use app\model\User;
use app\common\tools\Excel;
use Exception;

class Goods extends BasicAdminApi
{
    public function import_bak()
    {
        $goods_id    = '10000168749868';
        $url         = 'https://store.weixin.qq.com/shop-faas/mmchannelstradeproductcore/cgi/goods/getEditProductV2?token=&lang=zh_CN&productKey=%7B%22productId%22:' . $goods_id . '%7D&needRealStock=true&preview=false';
        $header      = [
            'biz_magic' => 'e93433268c19f511df7efe579b9c830f3f3b0f5b9b2f7d29c7238dec57d30c3a',
        ];
        $cookie      = 'pgv_pvid=6478503536; RK=OauxA9sHvQ; ptcz=4fc5f76eb7723869319b7929e6e9bffa3bdda972b23a8de74200bfdcd7218004; pac_uid=0_MdsBPJDjGcREN; suid=user_0_MdsBPJDjGcREN; _qimei_uuid42=18a1c0b3402100de65a8745725f5c933144255a4bb; _qimei_fingerprint=dbf04d64864947092b07cb3f5ade496e; _qimei_h38=7de166f865a8745725f5c93302000003f18a1c; _qimei_q32=aa21ede8fc823dfeef986a7f4cb6b34b; _qimei_q36=b8905c0e4d678a88dea329c1300012a18a12; qq_domain_video_guid_verify=f161cf4c6e54c13a; ETCI=5ab1789003d640b9a69d4e47350d0161; _hp2_id.1405110977=%7B%22userId%22%3A%224657858075813607%22%2C%22pageviewId%22%3A%222974631345053995%22%2C%22sessionId%22%3A%224146941745819947%22%2C%22identity%22%3Anull%2C%22trackerVersion%22%3A%224.0%22%7D; _clck=3091271558|1|fs1|0; biz_token=b_00000194_1ab13d2b_c365180b_d0536f47_ba88ebfa; biz_ticket=; biz_magic=e93433268c19f511df7efe579b9c830f3f3b0f5b9b2f7d29c7238dec57d30c3a; biz_rand=CAESIMpMxsXjT72PRh7711FjS5uK7iIGb7x+tvesAoJ8ujrA; pgv_info=ssid=s6399857232; verifysession=h01788af68d852af8cc9b950f1b67df4a6b7508af5cdf2b64bb6f9b6e1551e9d885d76f9995741cf153; mm_lang=zh_CN';
        $result      = curl()->set_header($header)->set_cookies($cookie)->get($url)->get_body();
        $info        = $result['product']['info'];
        $headImg     = $info['headImg'];
        $title       = $info['title'];
        $detailImg   = $info['detail']['detailImg'];
        $insert_data = [
            'name' => $title,
        ];
        if (isset($headImg[0])) {
            $insert_data['pic'] = tools()::web_image_url_to_oss($info['headImg'][0]);
        }
        if (isset($headImg[1])) {
            $insert_data['pic1'] = tools()::web_image_url_to_oss($info['headImg'][1]);
        }
        if (isset($headImg[2])) {
            $insert_data['pic2'] = tools()::web_image_url_to_oss($info['headImg'][2]);
        }
        if (isset($headImg[3])) {
            $insert_data['pic3'] = tools()::web_image_url_to_oss($info['headImg'][3]);
        }
        $description = '';
        foreach ($detailImg as $img) {
            $oss_img     = tools()::web_image_url_to_oss($img);
            $description .= "<img src='{$oss_img}'>";
        }
        $insert_data['description'] = $description;

        $db_goods = new \app\model\Goods();
        $db_goods->add($insert_data);
        success('添加成功:' . $goods_id);
    }

    public function import()
    {
        $file                        = new Excel();
        $param                       = $this->params;
        $bid                         = $this->get_bid();
        $file_url                    = $param['file'];
        $required_field              = [
            'sort'          => '序号',
            'name'          => '名称',
            'unit'          => '单位',
            'specs'         => '规格',
            'price'         => '价格',
            'category_name' => '类别',
            'pic'           => '主图链接',
        ];
        $optional_field_array        = [
            'original_price' => '原价',
        ];
        $arr                         = $file->load($file_url)->excelToArray($required_field, $optional_field_array);
        $err_msg                     = '';
        $success_num                 = 0;
        $total_num                   = 0;
        $goods_category_name_mapping = [];
        foreach ($arr as $goods) {
            foreach ($goods as $k => $v) {
                $goods[$k] = trim($v);
            }

            $total_num++;
            $sort           = $goods['sort'] ?: 1;
            $name           = $goods['name'];
            $name           = tools()::remove_empty_string($name);
            $unit           = $goods['unit'];
            $unit           = tools()::remove_empty_string($unit);
            $specs          = $goods['specs'];
            $specs          = tools()::remove_empty_string($specs);
            $price          = $goods['price'];
            $category_name  = $goods['category_name'];
            $pic            = $goods['pic'];
            $original_price = $goods['original_price'] ?? 0;
            $category_name  = tools()::remove_empty_string($category_name);
            $db_goods       = new \app\model\Goods();
            $map            = [
                ['bid', '=', $bid],
                ['name', '=', $name],
            ];
            $count          = $db_goods->where($map)->value('id');
            if ($count) {
                $err_msg .= '商品:' . $name . '已存在';
                continue;
            }

            $insert_data = [
                'name'           => $name,
                'unit'           => $unit,
                'specs'          => $specs,
                'price'          => $price,
                'original_price' => $original_price,
                'sort'           => $sort,
                'pic'            => $pic,
            ];

            if ($category_name) {
                //允许类别名称为空
                if (!isset($goods_category_name_mapping[$name])) {
                    $db_goods_category   = new GoodsCategory(); //注意此时可能是二级类别
                    $map                 = [
                        ['name', '=', $category_name],
                        ['bid', '=', $bid],
                    ];
                    $goods_category_guid = $db_goods_category->where($map)->order(['parent_guid' => 'DESC'])->value('guid');
                    //无论是否能查询到 类别guid  都赋值到数组中 避免全部都是名称不存在的类别名称的时候 每一次都无效查询
                    $goods_category_name_mapping[$category_name] = (string)$goods_category_guid;
                }
                //如果快递名称缓存是空的 则不再执行
                if (empty($goods_category_name_mapping[$category_name])) {
                    $err_msg .= '类别' . $category_name . '不存在,请检查名称,';
                    continue;
                }
                $insert_data['category_guid'] = $goods_category_name_mapping[$category_name];
            }
            $db_goods->add($insert_data);
            $success_num++;
        }
        $fail_num = $total_num - $success_num;
        $msg      = '共导入【' . $total_num . '】条数据 ,成功【' . $success_num . '】条';
        if ($fail_num > 0) {
            $msg .= ',失败【' . $fail_num . '】条';
            $msg .= ';原因:' . $err_msg;
        }
        wr_log($msg);
        if ($fail_num > 0) {
            error($msg);
        } else {
            success($msg);
        }
    }

    public function sync_image()
    {
        $params       = $this->params;
        $bid          = $this->get_bid();
        $db_goods     = new \app\model\Goods();
        $guid         = $params['guid'];
        $map_goods    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $goods        = $db_goods->where($map_goods)->findOrFail();
        $description  = $goods['description'];
        $image_list   = tools()::get_image_urls_from_html($description);
        $files_domain = config('app.files_domain');
        $map_list     = [];
        foreach ($image_list as $image) {
            if (strpos($image, $files_domain) === false) {
                $map_list[$image] = tools()::web_image_url_to_oss($image);
                $description      = str_replace($image, $map_list[$image], $description);
            }
        }
        if (!empty($map_list)) {
            $db_goods::update(['description' => $description], $map_goods);
            success('同步成功' . count($map_list) . '张图片!');
        } else {
            error('没有需要同步的图片');
        }
    }


    public function update_sort()
    {
        $params   = $this->params;
        $bid      = $this->get_bid();
        $db_goods = new \app\model\Goods();
        $list     = $params['goods_guid_list'];
        foreach ($list as $key => $value) {
            $map         = [
                ['bid', '=', $bid],
                ['guid', '=', $value],
            ];
            $update_data = [
                'sort' => (int)$key + 1,
            ];
            $db_goods::update($update_data, $map);
        }
        result([], '更新成功');
    }

    public function all()
    {
        $params              = $this->params;
        $goods_category_guid = $params['guid'] ?? '';
        $bid                 = $this->get_bid();
        $db_goods_category   = new GoodsCategory();
        $map_goods_category  = [
            ['bid', '=', $bid],
        ];
        if ($goods_category_guid) {
            $map_goods_category[] = ['guid', '=', $goods_category_guid];
        } else {
            $map_goods_category[] = ['parent_guid', 'IN', [tools()::get_empty_guid(), '']];
        }
        $field_goods_category = [
            'bid',
            'guid',
            'parent_guid',
            'name',
            'pic',
            'mini_pic'
        ];
        $order_goods_category = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        $list                 = $db_goods_category->where($map_goods_category)->field($field_goods_category)->order($order_goods_category)->append(['all_goods_list'])->select()->toArray();

        $db_goods                       = new \app\model\Goods();
        $map_goods                      = [
            ['bid', '=', $bid],
            ['category_guid', '=', ''],
        ];
        $field_goods                    = [
            'guid',
            'category_guid',
            'name',
            'pic',
        ];
        $order_goods                    = [
            'sort'        => 'ASC',
            'create_time' => 'DESC'
        ];
        $empty_category_guid_goods_list = $db_goods->where($map_goods)->field($field_goods)->order($order_goods)->select();
        if (!empty($empty_category_guid_goods_list)) {
            $list[] = [
                'bid'            => $bid,
                'guid'           => '',
                'parent_guid'    => '',
                'name'           => '商品列表',
                'pic'            => '',
                'mini_pic'       => '',
                'all_goods_list' => $empty_category_guid_goods_list
            ];
        }
        foreach ($list as $key => $val) {
            $val['all_goods_list'] = tools()::object2array($val['all_goods_list']);
            if (empty($val['all_goods_list'])) {
                unset($list[$key]);
                continue;
            }
            foreach ($val['all_goods_list'] as $k => $v) {
                $list[$key]['all_goods_list'][$k]['pic'] = tools()::add_thumbnail_mini($v['pic'], true);
            }
        }
        result($list);
    }

    public function get_type_attr_v2()
    {
        $bid                   = $this->get_bid();
        $params                = $this->params;
        $goods_guid            = $params['product_id'] ?? '';
        $product_type_id       = $params['product_type_id'] ?? '';  //1 只包含新建 2 包含历史记录
        $db_goods_attr         = new GoodsAttr();
        $db_goods_attr_group   = new GoodsAttrGroupModel();
        $attr_list             = [];
        $map                   = [['bid', '=', $bid]];
        $goods_attr_group_list = $db_goods_attr_group->where($map)->select()->toArray();
        $goods_attr_list       = $db_goods_attr->where($map)->select()->toArray();
        foreach ($goods_attr_group_list as $key => $val) {
            $push = $product_type_id == 2 ? [
                'id'      => $val['guid'],
                'title'   => $val['name'],
                'options' => [],
                'value'   => []
            ] : [];
            foreach ($goods_attr_list as $k => $v) {
                if ($v['goods_attr_group_guid'] == $val['guid']) {
                    if ($goods_guid) {
                        $db_goods_sku        = new GoodsSku();
                        $map                 = [
                            ['bid', '=', $bid],
                            ['goods_guid', '=', $goods_guid],
                            ['delete_time', 'null', null]
                        ];
                        $sku_item_guid_array = $db_goods_sku->where($map)->column('guid');
                        if ($sku_item_guid_array) {
                            $db_goods_sku_item = new GoodsSkuItem();
                            $map               = [
                                ['bid', '=', $bid],
                                ['goods_sku_guid', 'IN', $sku_item_guid_array],
                                ['goods_attr_guid', '=', $v['guid']],
                            ];
                            $count             = $db_goods_sku_item->where($map)->count();
                            if ($count) {
                                $push['value'][] = $v['guid'];
                            }
                        }
                    }

                    if (!empty($push['value']) || $product_type_id == 2) {
                        $push['id']        = $val['guid'];
                        $push['title']     = $val['name'];
                        $push['options'][] = [
                            'id'    => $v['guid'],
                            'title' => $v['goods_attr_name'],
                        ];
                    }
                }
            }
            if (!empty($push)) {
                $attr_list[] = $push;
            }
        }
        result(['attribute' => [], 'spec' => $attr_list]);
    }

    public function get_type_v2()
    {
        $data = [
            [
                'id'    => 1,
                'title' => '新建'
            ],
            [
                'id'    => 2,
                'title' => '历史记录'
            ],
        ];
        result($data);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_freight_template_guid_list()
    {
        $params = $this->params;
        $db     = new \app\model\FreightTemplate();
        $bid    = $this->get_bid();
        $map    = [['bid', '=', $bid]];
        $list   = $db->where($map)->field(['name', 'guid' => 'freight_template_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid   = $params['guid'] ?? '';
        $rule   = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_goods = new \app\model\Goods();
            $map      = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule     = $db_goods->where($map)->field(['freight_template_guid_list'])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule['freight_template_guid_list'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['freight_template_guid'], $rule['freight_template_guid_list'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function tag_item_list()
    {
        $params = $this->params;
        $db     = new \app\model\Tag();
        $bid    = $this->get_bid();
        $map    = [['bid', '=', $bid]];
        $list   = $db->where($map)->field(['name', 'guid' => 'tag_guid'])->order(['name' => 'ASC'])->select()->toArray();
        $guid   = $params['guid'] ?? '';
        $rule   = [];
        if ($guid) {
            //本规则的商品默认选中
            $db_goods = new \app\model\Goods();
            $map      = [
                ['bid', '=', $bid],
                ['guid', '=', $guid],
            ];
            $rule     = $db_goods->where($map)->field(['tag_guid'])->findOrEmpty();
        }
        if (!empty($rule) && is_array($rule['tag_guid'])) {
            foreach ($list as $key => $val) {
                if (in_array($val['tag_guid'], $rule['tag_guid'])) {
                    $list[$key]['selected'] = true;
                }
            }
        }
        //不在本规则的商品禁用
        //       $map        = [
        //           ['bid', '=', $bid],
        //           ['guid', '<>', $guid],
        //       ];
        //       $other_rule = $db_yky_coupon_send_rule->where($map)->field(['goods_item_guid'])->select();
        //       foreach ($other_rule as $key => $val) {
        //           if (is_array($val['goods_item_guid'])) {
        //               foreach ($list as $k => $v) {
        //                   if (in_array($v['goods_item_guid'], $val['goods_item_guid'])) {
        //                       $list[$k]['disabled'] = true;
        //                   }
        //               }
        //           }
        //       }
        result($list);
    }

    public function change_stock()
    {
        $bid        = $this->get_bid();
        $params     = $this->params;
        $type       = $params['type'];  //1 增加 2 减少  3 直接设置为值
        $goods_guid = $params['guid'];
        $stock      = $params['stock'];
        $map        = [
            ['bid', '=', $bid],
            ['guid', '=', $goods_guid]
        ];
        $db_goods   = new \app\model\Goods();
        if ($type == 1) {
            $db_goods->where($map)->setInc('stock', $stock);
        } elseif ($type == 2) {
            $db_goods->where($map)->setDec('stock', $stock);
        } elseif ($type == 3) {
            $db_goods::update(['stock' => $stock], $map);
        }
        success('更新成功');
    }

    /**
     *商品列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db_user  = new User();
        $bid      = $this->get_bid();
        $map      = [
            ['g.bid', '=', $bid],
            ['g.create_user_id|g.owner_user_id', 'IN', $db_user->getChildUserIdArray()]
        ];
        $db_goods = new \app\model\Goods();
        $join     = [
            ['goods_category gc', "g.category_guid = gc.guid AND  gc.bid=g.bid", 'LEFT'],
        ];
        $params   = $this->params;
        if (!empty($params['category_guid'])) {
            $map[] = ['gc.guid|gc.parent_guid', '=', $params['category_guid']];
            unset($this->params['category_guid']);
        }
        $field       = [
            'g.*',
            'gc.parent_guid' => 'parent_category_guid',
        ];
        $this->model = $db_goods->alias('g')->where($map)->join($join)->field($field)->order(['g.sort' => 'ASC', 'g.create_time' => 'DESC'])->append(['pic_mini']);
        result($this->_list());
    }
}