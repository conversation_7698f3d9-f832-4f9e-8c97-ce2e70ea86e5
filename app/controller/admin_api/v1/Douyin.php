<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/23
 * Time: 9:44
 */

namespace app\controller\admin_api\v1;

use app\model\DouyinConfig;
use Exception;


class Douyin extends BasicAdminApi
{

    /**
     *获取配置信息
     * @access public
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db             = new DouyinConfig();
        $bid            = $this->get_bid();
        $config         = get_config_by_bid($bid);
        $douyin_open_id = $config['douyin_open_id'];
        $url            = (string)url('gateway/douyin_auth/auth', ['bid' => $bid], true, true);
        if (!$douyin_open_id) {
            //return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
        }
        $map  = [
            ['open_id', '=', $douyin_open_id],
        ];
        $info = $db->where($map)->findOrEmpty();
        $data = [
            'info'     => $info,
            'status'   => !(int)$info->isEmpty(),
            'auth_url' => $url,
        ];
        result($data);
    }
}