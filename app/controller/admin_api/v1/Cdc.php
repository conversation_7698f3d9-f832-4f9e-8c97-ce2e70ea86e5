<?php

namespace app\controller\admin_api\v1;

use app\model\Cdc as CdcModel;
use think\facade\Db;

class Cdc extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $this->model = new CdcModel();
        $map         = [];
        $map         = [];
        if (empty($this->params['execute_time'])) {
            $begin_date = date('Y-m-d', strtotime('-3 days'));
            $map        = [['execute_time', '>', $begin_date]];
        }
        $this->model = $this->model->where($map)->append(['before_value_text', 'after_value_text']);
        result($this->_list());
    }

    public function detail()
    {
        $params              = $this->params;
        $id                  = $params['id'];
        $db_cdc              = new CdcModel();
        $map                 = [['id', '=', $id]];
        $info                = $db_cdc->where($map)->append(['change_list'])->find()->toArray();
        $table               = $info['table_name'];
        $schema              = $info['schema_name'];
        $sql                 = "select COLUMN_NAME,COLUMN_COMMENT from information_schema.COLUMNS where table_name = '$table' and table_schema = '$schema'";
        $column_list         = Db::query($sql);
        $column_comment_list = array_column($column_list, 'COLUMN_COMMENT', 'COLUMN_NAME');
        foreach ($info['change_list'] as $key => $val) {
            $info['change_list'][$key]['comment'] = $column_comment_list[$val['key_name']];
        }
        if ($info['bid_value']) {
            $db_business      = new \app\model\Business();
            $info['business'] = $db_business->get_business_info_by_account_or_guid($info['bid_value']);
        }
        result($info);
    }
}
