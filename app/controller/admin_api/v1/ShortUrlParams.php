<?php

namespace app\controller\admin_api\v1;

use app\model\ShortUrlParams as ShortUrlParamsModel;
use Exception;

class ShortUrlParams extends BasicAdminApi
{
    /**
     * 列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $db          = new ShortUrlParamsModel();
        $map         = [
            ['bid', '=', $this->get_bid()]
        ];
        $this->model = $db->where($map);
        result($this->_list());
    }

    /**
     * 添加
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function add()
    {
        $db          = new ShortUrlParamsModel();
        $this->model = $db;
        $db->add($this->params);
        success('添加成功');
    }

    /**
     * 编辑
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $db          = new ShortUrlParamsModel();
        $this->model = $db;
        $db->edit($this->params);
        success('编辑成功');
    }

    /**
     * 详情
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function detail()
    {
        $bid    = $this->get_bid();
        $params = $this->params;
        $guid   = $params['guid'];
        $db     = new ShortUrlParamsModel();
        $map    = [
            ['bid', '=', $bid],
            ['guid', '=', $guid],
        ];
        $note   = $db->where($map)->find()->toArray();
        $data   = array_merge($note, $note['params']);
        unset($data['params']);
        result($data);
    }
}