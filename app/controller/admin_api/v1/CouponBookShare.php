<?php

namespace app\controller\admin_api\v1;

use app\model\CouponBookShare as CouponBookShareModel;
use app\common\tools\Excel;

class CouponBookShare extends BasicAdminApi
{
    /**
     * 获取列表
     */
    public function index()
    {
        $db          = new CouponBookShareModel();
        $join        = [
            ['coupon_book cb', 'cbs.coupon_book_guid = cb.guid AND cbs.bid=cb.bid'],
        ];
        $field       = [
            'cbs.*',
            'cb.name' => 'coupon_book_name',
        ];
        $map         = [
            ['cbs.bid', '=', $this->get_bid()]
        ];
        $this->model = $db->alias('cbs')->where($map)->join($join)->field($field)->order(['cbs.create_time' => 'DESC']);

        result($this->_list());
    }

    public function export()
    {
        $bid         = $this->get_bid();
        $db          = new CouponBookShareModel();
        $join        = [
            ['coupon_book cb', 'cbs.coupon_book_guid = cb.guid AND cbs.bid=cb.bid'],
        ];
        $field       = [
            'cbs.*',
            'cb.name' => 'coupon_book_name',
        ];
        $map         = [
            ['cbs.bid', '=', $bid]
        ];
        $this->model = $db->alias('cbs')->where($map)->join($join)->field($field)->order(['cbs.create_time' => 'DESC']);
        $export_data = $this->_select($this->model);
        if ($export_data === false) {
            error($this->model->getError());
        }
        $header = [
            'coupon_book_name' => '卡册名称',
            'true_name'        => '姓名',
            'mobile'           => '手机号',
            'company'          => '公司名称',
            'create_time'      => '生成时间',
        ];
        $file   = new Excel();
        $file->arrayToExcel($header, $export_data);
    }
}
