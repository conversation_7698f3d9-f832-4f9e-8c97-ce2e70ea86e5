<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/1/26
 * Time: 20:31
 */

namespace app\controller\index;

use app\model\Business;
use app\model\MobileIdMapping;
use app\model\Network;
use app\model\ShortUrl;
use app\model\UrlClickNote;
use app\model\UrlIdMapping;
use app\model\User;
use app\model\WeappUrlLink;
use app\model\WechatConfig;
use app\common\service\NotifyService;
use app\common\service\TokenService;
use app\common\service\UrlService;
use app\common\tools\Visitor;
use Cyokup\EasyAiChat\EasyAiChat;
use DateTime;
use DateTimeZone;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;
use Endroid\QrCode\Writer\PngWriter;
use Exception;
use GuzzleHttp\Psr7\MimeType;
use Ip2Region;
use OpenApi\ChangAnCar;
use OpenApi\FengChao;
use OpenApi\Zentao;
use think\facade\Log;
use think\Response;

// 引入 Grafika 库相关类
use Grafika\Grafika;
use Grafika\Color as GrafikaColor;

class Plugins extends BasicIndex
{
    public function redirect_link()
    {
        $params               = $this->params;
        $url_id               = (int)$params['url_id'];
        $mobile_id            = (int)$params['mobile_id'];
        $db_url_id_mapping    = new UrlIdMapping();
        $map                  = [['id', '=', $url_id]];
        $url_info             = $db_url_id_mapping->where($map)->findOrFail();
        $url                  = $url_info['url'];
        $title                = $url_info['title'];
        $db_mobile_id_mapping = new MobileIdMapping();
        $map                  = [
            ['id', '=', $mobile_id]
        ];
        $mobile               = $db_mobile_id_mapping->where($map)->value('mobile');
        $ip                   = tools()::get_client_ip();
        $region               = tools()::get_client_ip_region_info($ip);
        $subject              = '手机号:' . $mobile . ',点击了' . $title . ',IP信息:' . $region;
        $db_url_click_note    = new UrlClickNote();
        $request              = $this->request;
        $user_agent           = $request->header('user_agent');
        $data                 = [
            'url_id'     => $url_id,
            'mobile'     => $mobile,
            'ip'         => $ip,
            'region'     => $region,
            'user_agent' => $user_agent,
        ];
        $db_url_click_note->save($data);
        wr_log($subject, 1);
        $redirect_link_copy_to_email = get_system_config('redirect_link_copy_to_email');
        $email_address               = explode(',', $redirect_link_copy_to_email);
        $email_data                  = [
            'account' => 'xyfqq',
            'send_to' => '<EMAIL>',
            'title'   => '【短链接被点击了】',
            'subject' => $subject,
            'copy_to' => $email_address
        ];
        $send_result                 = send_email($email_data);
        redirect($url);
    }

    public function redirect()
    {
        $params = $this->params;
        $url    = urldecode($params['url']);
        $ip     = tools()::get_client_ip();
        $region = tools()::get_client_ip_region_info($ip);
        $data   = [
            'url'         => '',
            'title'       => '链接【' . $url . '】正在被点击',
            'detail'      => 'ip:' . $ip,
            'user'        => '【链接点击】',
            'name'        => '(' . $region . ')',
            'remark'      => '系统通知',
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_system()->set_data($data)->send();
        redirect($url);
    }

    public function redirect_url()
    {
        $params = $this->params;
        $url    = urldecode($params['url']);
        return '<script>window.location.href="' . $url . '";</script>';//这种形式的referer才是真正的当前页面
    }

    public function test()
    {
        $db   = new \app\model\Log();
        $info = $db->where([['id', '>', 1]])->find();
        result($info);
    }

    public function chat()
    {
        $params = $this->params;
        $url    = 'http://*************:3001/api/v1/chat/completions';
        $token  = 'fastgpt-XUjXjGXzj3ZimmEnVDs39u1gckD4NX3vjB5RlsYK4PQDOzL1PXgWIf27wWETMnu';
        $text   = $params['text'];
        $header = [
            'Authorization' => "Bearer $token",
        ];
        $data   = [
            "chatId"    => "abcd",
            "stream"    => false,
            "detail"    => false,
            "variables" => [
                "uid"  => "",
                "name" => ""
            ],
            "messages"  => [
                [
                    "content" => $text,
                    "role"    => "user"
                ]
            ]
        ];
        $result = curl()->set_headers($header)->json()->post($url, $data)->get_body();
        success($result['choices'][0]['message']['content']);
    }

    public function deepseek()
    {
        $text = $this->params['text'] ?? '';
        if (empty($text)) {
            return ['response' => '内容为空'];
        }
        $aiModel  = new EasyAiChat();
        $messages = [
            ['role' => 'user', 'content' => $text]
        ];
        $return   = $aiModel::spark()->chat($messages);
        return json(['response' => $return]);
    }

    public function gpt()
    {
//        $text = $this->params['text'] ?? '';
//        if (empty($text)) {
//            return ['response' => '内容为空'];
//        }
//        $aiModel  = new EasyAiChat();
//        $messages = [
//            ['role' => 'user', 'content' => $text]
//        ];
//        $return   = $aiModel::spark()->chat($messages);
        wr_log($this->params, 1);

// 获取 messages 数组
        $params   = $this->params;
        $messages = $params['messages'];

// 获取最后一条消息
        $lastMessage = end($messages);

// 获取最后一条消息的内容
        $lastMessageContent = $lastMessage['content'];
        $response           = [
            'id'      => 'chatcmpl-123',
            'object'  => 'chat.completion',
            'created' => 1677652288,
            'model'   => 'gpt-3.5-turbo',
            'choices' => [
                [
                    'index'         => 0,
                    'message'       => [
                        'role'    => 'assistant',
                        'content' => '你刚刚发送的消息是：' . $lastMessageContent . ''
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage'   => [
                'prompt_tokens'     => 56,
                'completion_tokens' => 20,
                'total_tokens'      => 76
            ]
        ];
        return json($response);
    }

    protected function get_cookies()
    {
        $url       = 'https://m.kuaidi100.com/app/query/?coname=www&nu=';
        $cache_key = 'kuaidi100';
        if (!$cookies = cache($cache_key)) {
            $cookies = curl()->get($url)->get_cookies();
            cache($cache_key, $cookies, 60);
        }
        return $cookies;
    }

    public function open_the_door()
    {
        job()->set_job_name('Tools@open_the_door')->push_job();
        success("调用成功");
    }

    public function request_kuaidi()
    {
        $params = $this->params;
        $path   = $params['path'];
        unset($params['path']);
        $url    = 'https://m.kuaidi100.com' . $path;
        $cookie = $this->get_cookies();
        $result = curl()->set_cookies($cookie)->form_params()->post($url, $params)->get_body();
        if ($path == '/query') {
            if ($result['status'] != '200' && empty($result['data'])) {
                $referer = $this->request->header('referer');
                $arr     = tools()::parse_url_params($referer);//将url解析成参数数组
                if (!empty($arr['bid'])) {
                    error('暂时不支持该接口查询物流');
                }
            }
        }
        return json($result);
    }

    /**
     * 清空cookies
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function clear()
    {
        tools()::clear_all_cookie_and_session();
        success('缓存清除成功');
    }

    /**
     * 获取禅道当天的日志
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function get_chandao_log()
    {
        $account   = 'xyf';
        $password  = 'xyf1155';
        $zentao    = new Zentao($account, $password);
        $toady     = $zentao->get_log(strtotime(date('Y-m-d')));
        $yesterday = $zentao->get_log(strtotime(date('Y-m-d', strtotime('-1 day'))));
        return ($toady . $yesterday);
    }

    public function redirect_to_weapp()
    {
        $db     = new WeappUrlLink();
        $params = $this->params;
        $guid   = $params['guid'];
        $map    = [
            ['guid', '=', $guid]
        ];
        $note   = $db->where($map)->findOrFail();
        $appid  = $note['appid'];
        unset($params['guid']);
        $query = $note['query'];
        if (empty($note['query']) && $params) {
            if (!empty($params['_code'])) {
                $params['code'] = $params['_code'];
                unset($params['_code']);
            }
            $query = http_build_query($params);
        }
        $post_data = [
            'path'        => $note['path'],
            'query'       => $query,
            'env_version' => $note['env_version'],
        ];
        $instance  = weixin($appid)::WeMiniScheme();
        $result    = $instance->urlLink($post_data);
        redirect($result['url_link']);
    }

    /**
     * 跳转到admin后台
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function redirect_to_admin()
    {
        $params = $this->params;
        UrlService::check_date_time_url_sign();
        $store_id      = $params['store_id'];
        $db_business   = new Business();
        $map           = [['mall_store_id', '=', $store_id]];
        $business_info = $db_business->where($map)->findOrFail();
        //校验商家是否过期等
        $business_info = $db_business->login_verify($business_info['account']);
        if ($business_info === false) {
            error($db_business->getError());
        }
        //查找10000工号信息
        $db_user   = new User();
        $user_info = $db_user->get_admin_user($business_info['guid']);
        if ($user_info === false) {
            error($db_user->getError());
        }
        $data = $db_user->get_access_token($user_info, 'client_credential');
        $url  = (string)url('admin/common/redirect', ['access_token' => $data['access_token'], 'expires_in' => $data['expires_in']], true, true);
        redirect($url);
    }

    /**
     * 跳转URL
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function redirect_to_admin_url()
    {
        $params    = $this->params;
        $token_key = 'business_user_token';
        // 定义表单验证规则
        $rules = [
            $token_key => 'require',
            'url'      => 'require',
        ];
        // 验证表单数据
        $this->validate($params, $rules);
        $url                    = $this->request->param('url', '', null);
        $business_user_token    = $this->request->param($token_key, '', null);
        $jwt_data               = TokenService::decode($business_user_token);
        $bid                    = $jwt_data['bid'];
        $access_token_cache_key = $bid . '_' . $token_key;
        cookie($access_token_cache_key, $business_user_token, 3600 * 24 * 30);
        redirect(urldecode($url));
    }

    /**
     * 跳转URL
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function redirect_to_url()
    {
        $params    = $this->params;
        $token_key = 'access_token';
        // 定义表单验证规则
        $rules = [
            $token_key => 'require',
            'url'      => 'require',
        ];
        // 验证表单数据
        $this->validate($params, $rules);
        $url                    = $this->request->param('url', '', null);
        $access_token           = $this->request->param($token_key, '', null);
        $jwt_data               = TokenService::decode($access_token);
        $bid                    = $jwt_data['bid'];
        $access_token_cache_key = $bid . '_' . $token_key;
        cookie($access_token_cache_key, $access_token, 3600 * 24 * 30);
        redirect(urldecode($url));
    }

    public function download_file()
    {
        $params   = $this->params;
        $token    = $params['token'];
        $jwt_data = TokenService::decode($token);
        if ($jwt_data['exp'] < time()) {
            $msg = '文件下载链接已过期,请重新下载!';
            error($msg);
        }
        $local_file_name    = $jwt_data['local_file_name'];
        $download_file_name = $jwt_data['download_file_name'];
        $local_file_name    = tools()::get_absolute_path_disk($local_file_name);
        if (!is_file($local_file_name)) {
            error('文件不存在!');
        }
        /* @var $response Response */
        $response = Response::create($local_file_name, 'file')->name($download_file_name);
        if (Visitor::is_swoole()) {
            $download_file_name = urlencode($download_file_name);
            $header             = [
                'Content-Disposition' => 'attachment; ' . 'filename="' . $download_file_name . '";' . "filename* = UTF-8''{$download_file_name}"
            ];
            $response->header($header)->contentType(MimeType::fromFilename($local_file_name));
        }
        return $response;
    }

    /**
     * 下载第三方图片
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function download_img()
    {
        $file_path     = urldecode(urldecode(input('file_path')));
        $file_name     = urldecode(urldecode(input('file_name')));
        $absolute_path = tools()::get_absolute_path($file_path);
        $file_name     = $file_name ?: pathinfo($absolute_path, PATHINFO_BASENAME);
        $extension     = strtolower(pathinfo($absolute_path, PATHINFO_EXTENSION));
        if (!in_array($extension, ['png', 'jpg', 'jpeg', 'gif', 'bmp'])) {
            error('仅支持下载图片');
        }
        return download($absolute_path, urlencode($file_name))->force(true);
    }

    /**
     * 输出第三方图片
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function output_img()
    {
        $url = $this->params['url'];
        $url = urldecode(urldecode($url));
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new Exception('output_img url不合法');
        }
        return curl()->get($url)->show_image();
    }

    /**
     * 下载第三方图片
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function download_image()
    {
        $url       = $this->params['url'];
        $file_name = $this->params['file_name'] ?? 'download.png';
        $image_url = urldecode(urldecode($url));
        $file_name = urldecode(urldecode($file_name));
        return curl()->get($image_url)->download_image($file_name);
    }

    /**
     * 上报
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function web_monitor()
    {
        $array              = $this->params;
        $user_agent         = $this->request->header('user_agent');
        $ip                 = tools()::get_client_ip();
        $array['userAgent'] = $user_agent;
        $array['ip']        = $ip;
        $timestamp          = DateTime::createFromFormat('0.u00 U', microtime())->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('c');
        if (!empty($array['type']) && $array['type'] == 3) {
            //js错误则微信通知
            try {
                $notify_json         = json_encode($array, JSON_UNESCAPED_UNICODE);
                $ip2region           = new Ip2Region();
                $region_info         = $ip2region->btreeSearch($ip);
                $array['ipRegion']   = $region_info['region'];
                $array['cityId']     = $region_info['city_id'];
                $array['@timestamp'] = $timestamp;
                unset($array['type']);
                wr_log($notify_json);
                Log::channel('files')->js_error($array);
            } catch (Exception $e) {
                wr_log(tools()::exception_to_string($e));
            }
        } else {
            $db          = new Network();
            $insert_data = [];
            foreach ($array as $key => $val) {
                $insert_data[parse_name($key)] = is_null($val) ? 0 : $val;
            }
            try {
                $ip2region              = new Ip2Region();
                $region_info            = $ip2region->btreeSearch($ip);
                $insert_data['region']  = $region_info['region'];
                $insert_data['city_id'] = $region_info['city_id'];
                $db->save($insert_data);
                Log::channel('files')->net_work($insert_data);
            } catch (Exception $e) {
            }
        }
        success('success');
    }

    /**
     * 跳转对应长链接
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function redirect_to_long_url()
    {
        $params   = $this->params;
        $db       = new ShortUrl();
        $long_url = $db->get_long_url($this->request->route('code'));
        if (!$long_url) {
            abort(404, $db->getError() ?: '您访问的链接不存在或已经失效');
        }
        unset($params['code']);
        if (!empty($params)) {
            $long_url .= strpos($long_url, '?') === false ? '?' : '&';
            $long_url .= http_build_query($params);
        }
        redirect($long_url);
    }

    /**
     * 测试生成半透明图片
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function test_transparent_image()
    {
        phpinfo();
        halt('1');
        if (extension_loaded('imagick')) {
            echo 'Imagick 扩展已安装。';
        } else {
            echo 'Imagick 扩展未安装。';
        }
        halt('123');
        // 在调用Grafika前设置编辑器优先级
        try {
// 创建编辑器
            $editor = Grafika::createEditor();

// 创建透明背景图片（关键修改）
            $image = Grafika::createBlankImage(400, 200, null); // 使用 null 创建透明背景

// 定义文字内容、字体大小、字体路径和颜色
            $text      = '中文123abc';
            $fontSize  = 24;
            $font      = root_path() . 'public/static/css/fonts/KAISHU.ttf';
            $textColor = new GrafikaColor('#000000');

// 计算文字位置（居中）
            $textBox    = imagettfbbox($fontSize, 0, $font, $text);
            $textWidth  = $textBox[2] - $textBox[0];
            $textHeight = abs($textBox[7] - $textBox[1]); // 修正高度计算
            $x          = (400 - $textWidth) / 2;

// 修正 Y 坐标计算（关键修改）
            $y = (200 - $textHeight) / 2 + abs($textBox[7]);

// 定义内边距
            $padding = 20;

// 计算背景条参数（修正位置计算）
            $bgWidth  = $textWidth + (2 * $padding);
            $bgHeight = $textHeight + (2 * $padding);
            $bgX      = ($x - $padding);
            $bgY      = ($y - $textHeight - $padding + abs($textBox[7])); // 修正 Y 偏移

// 创建半透明背景（RGBA 格式）
            $bgColor = new GrafikaColor('#FFFFFF', 0.1); // 50% 透明度

// 创建背景矩形（使用正确的坐标参数）
            $rectangle = Grafika::createDrawingObject('Rectangle',
                $bgWidth,
                $bgHeight,
                [$bgX, $bgY], // 正确的位置数组
                0,            // 边框宽度
                null,         // 边框颜色
                $bgColor      // 填充颜色
            );

// 绘制背景矩形
            $editor->draw($image, $rectangle);

// 添加文字
            $editor->text($image, $text, $fontSize, $x, $y, $textColor, $font, 0);

// 保存为 PNG 格式（确保保留透明度）
            $name     = 'test_transparent_image' . time() . '.png';
            $filePath = root_path() . 'public/' . $name;
            $editor->save($image, $filePath, 'png', 100); // 明确指定 PNG 格式

// 返回图片 URL
            $imageUrl = tools()::path_to_web($name);
            return "<img src='{$imageUrl}' alt='Test Transparent Image' />";
        } catch (Exception $e) {
            error('生成半透明图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 文本转二维码
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function create_qrcode()
    {
        $data     = $this->request->param('data', '', null);
        $size     = $this->request->param('size', 300, null);
        $to_short = $this->request->param('to_short', 0, null);
        $writer   = new PngWriter();
        if ($to_short) {
            $data = UrlService::get_full_url(UrlService::long_to_short($data));
        }
        // Create QR code
        $qrCode = QrCode::create($data)
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh())
            ->setSize($size)
            ->setMargin(10)
            ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        $result = $writer->write($qrCode);
        return response($result->getString())->contentType($result->getMimeType());
    }

    /**
     * 升级通知推送
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function notify()
    {
        wr_log($this->params, 1);
        //        wr_log('收到消息推送', 1);
        success('ok');
    }

    public function bark()
    {
        wr_log($this->params, 1);
        //        wr_log('收到消息推送', 1);
        success('ok');
    }

    public function ha_cmd_service()
    {
        $params      = $this->params;
        $to          = $params['to'] ?? ''; //触发的实体   qzl_iphone 时间触发则为空
        $cmd         = $params['cmd'] ?? '';
        $bark_url    = '';
        $bark_title  = '';
        $bark_icon   = '';
        $bark_body   = $params['msg'] ?? '';
        $to_user     = '';
        $sound       = '';
        $notify_bark = true;
        switch ($cmd) {
            case 'feng_chao':
                if (empty($to)) {
                    //如果不是定位触发 则不发送bark通知(因为bark通知没有缓存)
                    $notify_bark = false;
                }
                $bark_url   = 'com.fcbox.hiveconsumer://';
                $bark_title = '【取件通知】';
                $bark_icon  = 'https://' . config('app.app_host_domain') . '/static/img/app_icon/feng_chao.jpg';

                //  message_param_name : msg
                //  title_param_name : cmd  //后续扩展根据cmd不同 执行不同任务
                //  target_param_name : to

                $feng_chao = new FengChao();
                $result    = $feng_chao->get_express_list();
                if ($result === false) {
                    $msg = '丰巢模拟登录失败:' . $feng_chao->getMsg();
                    wr_log($msg, 1);
                    error($msg);
                }
                if ($to == 'person.admin') {
                    $to_user = 'xyf';
                } elseif ($to == 'person.wang_wen_juan') {
                    $to_user = 'wwj';
                }
                $data          = $result['data'];
                $to_pick_total = $data['toPickTotal']; //待取件
                $total         = $data['total']; //合计多少快递
                if ($to_pick_total > 0) {
                    $sound     = 'qujian.caf';
                    $bark_body = '共【' . $to_pick_total . '】个快递待取件:' . "\n";

                    // 获取当前时间并转换为 HHmm 格式的整数
                    $time = intval(date('Hi'));

                    foreach ($result['list'] as $list) {
                        $company_name        = $list['express_name']; //快递公司
                        $send_tm             = $list['send_time']; //入柜时间
                        $pick_up_code        = $list['pick_up_code']; //取件码
                        $postId              = $list['post_id']; //加密单号
                        $clientMobile        = $list['client_mobile']; //手机号
                        $express_id          = $list['express_no']; //快递单号
                        $company_name_simple = $list['company_name_simple'];//快递公司
                        $express_id_suffix   = $list['express_id_suffix'];//单号尾号
                        $address             = $list['pick_up_address'];//取件地址

                        $cache_key = 'feng_chao_notify:' . $express_id;
                        $bark_body .= $address . '-' . $company_name_simple . '-' . $express_id_suffix;
                        $bark_body .= "\n";
                        $is_notify = cache($cache_key);
                        if (empty($is_notify)) {
                            // 无论是时间触发  还是 离家在家 都进行检测
//                            if (empty($to) && empty($is_notify)) {
                            $bid       = config('app.super_admin_business_guid'); //admin
                            $url       = 'https://external.fcbox.com/staticResource/wechat/program/wechat_pick/pick-card.html#/pickupPromotion?bizType=0&mobile=' . $clientMobile . '&postId=' . $postId;
                            $url       = (string)url('index/tools/feng_chao', ['to' => $to], false, true);
                            $pick_data = [
                                'url'             => $url,
                                'pick_up_code'    => $pick_up_code,
                                'pick_up_address' => $address,
                                'express_name'    => $company_name,
                                'express_no'      => $express_id,
                                'create_time'     => $send_tm,
                            ];
                            $result    = notify()->set_key_name(NotifyService::PickUpExpress)->set_data($pick_data)->set_bid($bid)->send();
                            cache($cache_key, 1, 7 * 24 * 60 * 40); //缓存7天
                        } elseif ($time >= 2015 && $time <= 2115) {
                            // 判断当前时间是否在晚上 8 点 15 分到 9 点 15 分之间
                            ios_push($bark_body);
                        }
                    }
                    $bark_body = rtrim($bark_body, "\n");
                } else {
//                    $bark_body = '到家了但是没有快递要取';
                    $bark_body = '';
                }
                break;
            case '360_watch':
                $bark_url   = 'QihooChildSafe://';
                $bark_title = '【360手表】';
                $bark_icon  = 'https://' . config('app.app_host_domain') . '/static/img/app_icon/360_watch.jpg';
                break;
            default:
                break;

        }
        $bark_data = [
            'title' => $bark_title,
            'url'   => $bark_url,
            'icon'  => $bark_icon,
            'body'  => $bark_body
        ];
        if ($sound) {
            $bark_data['sound'] = $sound;
        }
        if ($bark_body && $notify_bark) {
            ios_push($bark_data, $to_user);
        }
        success($bark_body);
    }

    public function ha_notify()
    {
        $msg = $this->params['msg'] ?? 'ha_notify空消息';
        $to  = $this->params['to'] ?? '';
        if ($to == 'wwj') {
            ios_push($msg, $to);
        }
        ios_push($msg);
//        wr_log($this->params, 1);
        //        wr_log('收到消息推送', 1);
        success('ok');
    }

    public function ha_change_state()
    {
        $params        = $this->params;
        $entity        = $params['entity']; //'switch.boot_pc';
        $friendly_name = $params['friendly_name']; //'boot_pc';
        $state         = $params['state']; //'off';
        $from          = $params['from'] ?? 1; //'1 家里电脑 2 公司电脑';
        $domain        = '';
        $authorization = '';
        if ($from == 1) {
            $domain        = 'http://i.ecarde.cn:8123';
            $authorization = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI4Yjg1YTFjMDZjNjY0ZmIzYTZjYzllMDBjNGNiNzIwZiIsImlhdCI6MTcxNDM1OTYzNywiZXhwIjoyMDI5NzE5NjM3fQ.epUODqRUxiqAqzbW5ZMoFkgFUccEjwa5qnnyGmvEOWc";
        } elseif ($from == 2) {
            $domain        = 'http://onecloud-ha.i.ecarde.cn:7001';
            $authorization = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI3MWU2MDI4YmVhYWQ0NGMwYTFjMDNmN2ZmOGFmOGU1NiIsImlhdCI6MTcxNzQ1ODY5MiwiZXhwIjoyMDMyODE4NjkyfQ.xu9TKyVHpYinaDfwItZOZVBtVdHvTJa2LZGppb1lTd8";
        } else {
            error('from参数错误');
        }

        $url     = $domain . "/api/states/" . $entity;
        $headers = [
            "Accept"          => "*/*",
            "Accept-Language" => "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection"      => "keep-alive",
            "Content-Type"    => "application/json;charset=UTF-8",
            "Origin"          => $domain,
            "Referer"         => $domain . "/developer-tools/state",
            "User-Agent"      => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.0",
            "Authorization"   => $authorization
        ];

        $data   = [
            "state"      => $state,
            "attributes" => [
                "assumed_state" => true,
                "friendly_name" => $friendly_name
            ]
        ];
        $result = curl()->set_http_errors(false)->set_header($headers)->json()->post($url, $data)->get_body();
        if (!is_array($result)) {
            wr_log($result);
            error('更新状态失败:返回结果不是数组');
        }
        $msg = $friendly_name . '状态更新为:' . $state . '成功,来源:' . $from;
        success($msg);
    }

    public function get_weapp_url_scheme()
    {
        //  "currentTime": 1693551553702,
//	"path": "123",
//	"originId": "gh_e509cf76bdf6",
//	"query": "fid=zn1Tknj01nHfdnWc&pageId=123456789&bd_vid=zn1Tknj01nHfdnWc&fix=yingxiaotongceshi",
//	"sign": "29482e6f3c4eb4c1029c10ba4ce044f3"
        $params = $this->params;
        if (!$this->request->isPost()) {
            error('当前访问方式错误,请不要通过浏览器访问此接口');
        }
        $currentTime = $params['currentTime'];
        $path        = $params['path'];
        $originId    = $params['originId'];
        $query       = $params['query'];
        if (empty($path)) {
            $query = '';
        }
        $sign       = $params['sign'];
        $local_sign = md5(strtolower('currentTime=' . $currentTime . '&originid=' . $originId));
        if ($sign !== $local_sign) {
            error('签名错误');
        }
        $db    = new WechatConfig();
        $map   = [
            ['user_name', '=', $originId]
        ];
        $appid = $db->where($map)->value('authorizer_appid');
        if (empty($appid)) {
            error('当前小程序尚未授权');
        }
        $instance  = weixin($appid)::WeMiniScheme();
        $jump_wxa  = [
            'path'        => $path,
            'query'       => $query,
            'env_version' => 'release',
        ];
        $post_data = [
            'jump_wxa'        => $jump_wxa,
            //            'expire_time' => '', //到期失效的 scheme 码的失效时间，为 Unix 时间戳。生成的到期失效 scheme 码在该时间前有效。最长有效期为30天。is_expire 为 true 且 expire_type 为 0 时必填
            'expire_type'     => 1, //默认值0，到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
            'expire_interval' => 30,
        ];
        $result    = $instance->create($post_data);
        $openlink  = $result['openlink'];
        wr_log('获取成功:' . $openlink);
        return json([
            'code'   => 0,
            'msg'    => '获取成功',
            'result' => ['scheme' => $openlink]
        ]);
    }

    /**
     * 升级通知推送
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function upgrade_push()
    {
        logToFile($this->params);
        $changes = input('changes');
        $branch  = input('branch');
        wr_log($changes, 1);
        $notice = '';
        if ($branch) {
            $notice .= "\n【分支】:【" . $branch . "】\n";
        }
        $regex = "/^(feat|refactor|fix)(\((story|task|bug)#([1-9]\d*)\))/";
        if ($changes) {
            $changes = nl2br($changes);
            $changes = strtolower($changes);
            $changes = explode("<br />", trim($changes));
            $notice  = '';
            foreach ($changes as $key => $val) {
                $val = trim(str_replace("<br />", "", $val));
                if (!$val) {
                    continue;
                }
                if (preg_match($regex, $val)) {
                    $id   = tools()::search_str('#', ')', $val);
                    $type = tools()::search_str('(', '#', $val);
                    $data = [
                        'id'      => $id,
                        'type'    => $type,
                        'comment' => '【当前状态未升级】' . $val . "--于" . date("Y-m-d H:i:s", time()) . "-开发完毕,等待产品部测试"
                    ];
                    wr_log($data);
                }
                $notice .= $val . "\n";
            }
            $notice = rtrim("\n");
            send_qy_wechat($notice);
            success();
        }
        $developmentType = input('developmentType');
        if ($developmentType) {
            $development = ['1' => '预发环境', '2' => '正式环境',];
            $notice      .= "\n【环境】:【" . $development[$developmentType] . "】\n";
        }
        $upgrade = input('upgrade');
        $notice  .= $upgrade;
        $redis   = get_redis_instance();
        $keys    = 'upgrade_log';
        $result  = $redis->zAdd($keys, time(), md5(json_encode($notice)));
        if ($result === 1) {
            //优先处理数据
            $upgrade     = nl2br($upgrade);
            $upgrade     = strtolower($upgrade);
            $upgrade_arr = explode("<br />", trim($upgrade));
            foreach ($upgrade_arr as $key => $val) {
                $val     = strtolower($val);
                $replace = ["\r\n", "\n", "\r", "<br />", "<br/>"];
                $val     = str_replace($replace, '', trim($val));
                if (!$val) {
                    continue;
                }
                //如果是升级到正式环境,则自动关闭禅道
                if ($developmentType == 2) {
                    if (preg_match($regex, $val)) {
                        $val  = trim(str_replace("<br />", "", $val));
                        $id   = tools()::search_str('#', ')', $val);
                        $type = tools()::search_str('(', '#', $val);
                        wr_log('发现符合条件内容:' . $val . $id . $type, 1);
                        $data = [
                            'id'      => $id,
                            'type'    => $type,
                            'comment' => "本单于" . date("Y-m-d H:i:s", time()) . "-正式升级"
                        ];
                        wr_log($data);
                    }
                }
            }
            //清空过期的缓存
            $redis->zRemRangeByScore($keys, 0, time() - 3600);
        }
        success();
    }

    public function redirect_changan_user_center_url()
    {
        $unionid = 'o7sY81F3-5Lb3PSCMSDpHBJbjykY';
        $openid  = 'ol7KdjvN1XrUhuCq8TLu0VPDKdUg';
        $config  = ['unionid' => $unionid, 'openid' => $openid];
        $changan = new ChangAnCar($config);
        //$url     = $changan->get_order_list_url();
        $url = $changan->get_user_center_url();
        redirect($url);
    }

    public function redirect_qichezhijia_url()
    {
        $url = get_system_config('qichezhijia_link');
        wr_log('qichezhijia_link 正在被点击');
        redirect($url);
    }

    public function crontab_time()
    {
        $cron_txt  = $this->params['crontxt'];
        $msg       = '';
        $next_time = time();
        for ($i = 0; $i <= 10; $i++) {
            $next_time = tools()::parse_crontab($cron_txt, $next_time + 60);
            $msg       .= format_timestamp($next_time) . "<br/>";
        }
        success($msg);
    }
}