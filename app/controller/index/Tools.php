<?php

namespace app\controller\index;

use Darabonba\GatewaySpi\Models\InterceptorContext\request;

class Tools extends BasicIndex
{
    public function sleep()
    {
        sleep($this->params['sleep'] ?? 1);
        success(microsecond());
    }

    public function bang_ding()
    {
        error('当前页面已下线,请前往公众号查看官方数据~');
    }

    public function jielong()
    {
        $all_list = [
            '谢佳明',
            '刘萧仪',
            '李以沫',
            '何雨宣',
            '陈思敏',
            '陈芊宇',
            '贺雅彤',
            '张宏毅',
            '张书雅',
            '张瑾琦',
            '黄苇航',
            '钱妧玥',
            '曾思漫',
            '曾思涵',
            '王睿婕',
            '肖沐宸'
        ];

        // 输出HTML文档的开始部分
        echo '<!DOCTYPE html>';
        echo '<html lang="en">';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">'; // 确保页面响应式缩放
        echo '<meta http-equiv="X-UA-Compatible" content="ie=edge">'; // 告诉IE使用最新的文档模式
        echo '<title>接龙小助手</title>';
        echo '</head>';

        // 获取当前时间
        $currentTimestamp = time();
// 设置17:00的时间戳
        $eveningTimestamp = strtotime('17:00');

        // 获取今天、昨天和前天的日期
        $today              = date('Y-m-d');
        $yesterday          = date('Y-m-d', strtotime('-1 day'));
        $dayBeforeYesterday = date('Y-m-d', strtotime('-2 days'));

// 获取当前URL
        $currentUrl = request()->url();
        parse_str(parse_url($currentUrl, PHP_URL_QUERY), $queryParams);

// 移除现有的date参数并重新构建URL
        unset($queryParams['date']);
        $urlBase               = strtok($currentUrl, '?');
        $urlToday              = sprintf('%s?%s', $urlBase, http_build_query(array_merge($queryParams, ['date' => $today])));
        $urlYesterday          = sprintf('%s?%s', $urlBase, http_build_query(array_merge($queryParams, ['date' => $yesterday])));
        $urlDayBeforeYesterday = sprintf('%s?%s', $urlBase, http_build_query(array_merge($queryParams, ['date' => $dayBeforeYesterday])));

// 检查哪个日期是选中的
        $selectedDate = isset($_GET['date']) ? $_GET['date'] : ($currentTimestamp < $eveningTimestamp ? $yesterday : $today);
        echo '<body>';
        // 输出横向的底部列表
        echo '<div style="display: flex; justify-content: space-around;">';
        echo '<a href="' . $urlToday . '" style="color: ' . ($today === $selectedDate ? 'orange' : 'gray') . '">今天</a>';
        echo '<a href="' . $urlYesterday . '" style="color: ' . ($yesterday === $selectedDate ? 'orange' : 'gray') . '">昨天</a>';
        echo '<a href="' . $urlDayBeforeYesterday . '" style="color: ' . ($dayBeforeYesterday === $selectedDate ? 'orange' : 'gray') . '">前天</a>';
        echo '</div>';

        $url = "https://h-api.jielong.co/api/CheckIn/Records?pageIndex=1&pageSize=20&threadId=131491548&dateTarget=" . $selectedDate . "&sortBy=0&isShowCommentAndAttitude=true&isGetAll=true";

        $header = [
            "Authorization" => get_system_config('jie_long_guan_jia_token'),
        ];
        $result = curl()->set_header($header)->get($url)->get_body();
//        dump($result);
        $top5       = [];
        $aggressive = [];
        foreach ($result['Data'] as $data) {
            $timeString = $data['DateCreated'];
            $true_name  = $data['Signature'];
            if (count($top5) < 5) {
                $top5[] = $true_name;
            }
            // 去掉日期部分，只保留时间
            $timeOnly = substr($timeString, strpos($timeString, ' ') + 1);

// 去掉冒号，得到一个纯数字字符串
            $timeNumeric = str_replace(':', '', $timeOnly);

// 设置晚上21:00的纯数字字符串
            $eveningNumeric = '2100';

// 比较时间
            if ($timeNumeric < $eveningNumeric) {
                $aggressive[] = $true_name;
            }

        }
        echo('日期:' . $selectedDate . "</br>");
//        echo('前5名单:' . "</br>");
//        foreach ($top5 as $list) {
//            echo($list . "</br>");
//        }
//
//        echo("</br>");
//        echo("</br>");
//        echo("</br>");

        $aggressive_list = [];
        $i               = 0;
        foreach ($all_list as $user) {
            $i++;
            if (in_array($user, $aggressive)) {
                $aggressive_list[] = $i . '_' . $user;
            }
        }
        echo('9点前名单:' . "</br>");
        foreach ($aggressive_list as $list) {
            echo($list . "</br>");
        }
// 您的页面内容
// 在页面底部输出HTML文档的结束部分
        echo '</body>';
        echo '</html>';
        return '';
    }
}