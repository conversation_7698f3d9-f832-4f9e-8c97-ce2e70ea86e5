<?php

namespace app\controller\index;

use app\model\StatisticTask;
use app\middleware\CropWeixinAuth;
use OpenApi\SubMerchant;
use think\facade\Db;

class Test extends BasicIndex
{
    protected array $middleware = [
        CropWeixinAuth::class => ['except' => ['index', 'api']],
    ];

    public function index()
    {
        $config = ['merchant_code' => '**********'];
        //$config = [];
        $sub    = new SubMerchant($config);
        $result = $sub->get_account_list();
        dump($result);
        $sub    = new SubMerchant();
        $result = $sub->get_account_list();
        dump($result);
        $result = Db::connect('management')->name('user')->where('1=1')->find();
        dump($result);
    }

    public function yikayi()
    {
        $db     = new StatisticTask();
        $result = $db->get_result_by_id(1);
        $this->assign([
            'header' => array_keys(current($result)),
            'list'   => $result,
            'date'   => date('Y-m-d H:i'),
        ]);
        return $this->fetch();
    }

    public function wechat()
    {
        return $this->fetch();
    }

    public function test()
    {
        foreach ([10, 20, 30, 40, 50, 60] as $key => $value) {
            $job_data = ['time' => format_timestamp(), 'msg' => $value];
            job()->set_job_name('Tools@wr_log')->push_job($job_data, $value); //同步执行一次
        }
    }

    public function api()
    {
        $params = $this->params;

        //      {
        //          "at_list": [],
        //  "content": "6",
        //  "content_type": 2,
        //  "conversation_id": "R:10696051012013676",
        //  "is_pc": 0,
        //  "local_id": "29410",
        //  "receiver": "R:10696051012013676",
        //  "send_time": "1609055094",
        //  "sender": "1688851757289348",
        //  "sender_name": "谢永发",
        //  "server_id": "1332747"
        //}
        wr_log(json_encode($params, JSON_UNESCAPED_UNICODE), 1);
        $reply         = 0;
        $at_list       = $params['at_list'];
        $content       = $params['content'];
        $reply_content = $params['sender_name'] . '您好!' . "\n您说的话:" . $content;
        if (!empty($at_list)) {
            $nickname_array = array_column($at_list, 'nickname');
            if (in_array('谢永发', $nickname_array)) {
                $reply_content .= "\n您@了:" . join(',', $nickname_array);
                $reply         = 1;
                if (strpos($content, '进件') !== false) {
                    $reply_content .= "\n匹配结果: 进件流程请查看 http://sz1card1.udesk.cn/hc/articles/90271";
                } elseif (strpos($content, '11') !== false) {

                } else {
                    $reply_content .= "\n没有匹配到答案哦";

                }

            }
        }

        $data = [
            'reply'   => $reply,
            'content' => $reply_content
        ];
        result($data);
    }
}