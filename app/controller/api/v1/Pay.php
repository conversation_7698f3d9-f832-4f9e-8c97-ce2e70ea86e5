<?php

namespace app\controller\api\v1;

use app\model\Business;
use Exception;

class Pay extends BasicApi
{
    protected $appid;
    protected $secret;
    protected $bid;
    protected $user_guid;
    protected $data = [];

    /**
     *退款
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function refund()
    {
        $receive_data = $this->data;
        $data         = [];
        try {
            $options = pay($this->get_bid())->driver('wechat')->scene('miniapp')->refund($receive_data);
            if ($options['return_code'] == 'SUCCESS' && $options['result_code'] == 'SUCCESS') {
                $data         = [
                    'return_code' => 'SUCCESS',
                    'return_msg'  => '退款成功',
                    'result_code' => 'SUCCESS',
                ];
                $data['sign'] = tools()::get_sign($data, $this->secret);
            } else {
                throw new Exception('退款失败');
            }
        } catch (Exception $e) {
            return $this->error_return($e->getMessage(), $e->getCode());
        }
        return tools()::arr2xml($data);
    }

    /**
     *退款查询
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function refundquery()
    {
        $receive_data = $this->data;
        $data         = [];
        try {
            $options = pay($this->get_bid())->driver('wechat')->scene('miniapp')->refund_find($receive_data['out_trade_no']);
            if ($options) {
                $data         = [
                    'return_code' => 'SUCCESS',
                    'return_msg'  => '查询成功',
                    'result_code' => 'SUCCESS',
                    'order_data'  => $options
                ];
                $data['sign'] = tools()::get_sign($data, $this->secret);
            } else {
                throw new Exception('查询失败');
            }
        } catch (Exception $e) {
            return $this->error_return($e->getMessage(), $e->getCode());
        }
        return tools()::arr2xml($data);
    }

    /**
     *查单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query()
    {
        $receive_data = $this->data;
        $data         = [];
        try {
            $options = pay($this->get_bid())->driver('wechat')->scene('miniapp')->find($receive_data['out_trade_no']);
            if ($options) {
                $data         = [
                    'return_code' => 'SUCCESS',
                    'return_msg'  => '查询成功',
                    'result_code' => 'SUCCESS',
                    'order_data'  => $options
                ];
                $data['sign'] = tools()::get_sign($data, $this->secret);
            } else {
                throw new Exception('查询失败');
            }
        } catch (Exception $e) {
            return $this->error_return($e->getMessage(), $e->getCode());
        }
        return tools()::arr2xml($data);
    }

    /**
     *下单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        $receive_data = $this->data;
        $out_trade_no = $receive_data['out_trade_no'];
        $options      = [
            'bid'          => $this->get_bid(),
            'user_guid'    => $this->get_user_guid(),
            'body'         => $receive_data['body'],
            'out_trade_no' => $out_trade_no,
            'total_fee'    => $receive_data['total_fee'],
            'openid'       => $receive_data['openid'],
            'appid'        => $receive_data['appid'],
            'attach'       => isset($receive_data['attach']) ? $receive_data['attach'] : '',
            'job_attach'   => [
                'queue' => '', //暂时本地处理
                'class' => 'Pay@notify',
                'data'  => [
                    'bid'         => $this->get_bid(),
                    'bill_number' => $out_trade_no,
                    'notify_url'  => $receive_data['notify_url'],
                ]]
        ];
        try {
            $options = pay($this->get_bid())->driver('wechat')->scene('miniapp')->apply($options);
            if (isset($options['appId']) && isset($options['nonceStr']) && isset($options['package'])) {
                //下单成功构建针对商城小程序的消息体
                $options = [
                    'return_code' => 'SUCCESS',
                    'return_msg'  => 'OK',
                    'result_code' => 'SUCCESS',
                    'appid'       => $options['appId'],
                    'mch_id'      => $this->get_appid(),
                    'nonce_str'   => $options['nonceStr'],
                    'prepay_id'   => str_replace('prepay_id=', '', $options['package']),
                    'trade_type'  => $receive_data['trade_type'],
                    'pay_data'    => $options,
                ];
            }
            $options['sign'] = tools()::get_sign($options, $this->secret);
        } catch (Exception $e) {
            return $this->error_return($e->getMessage(), $e->getCode());
        }
        return tools()::arr2xml($options);
    }

    /**
     * [__construct description]
     * <AUTHOR>
     * @dateTime 2018-05-21 20:47:02
     */
    protected function initialize()
    {
        if (!empty($this->request->__get('_bid'))) {
            $this->get_bid() = $this->request->__get('_bid');
        }
        if (!empty($this->request->__get('_user_guid'))) {
            $this->get_user_guid() = $this->request->__get('_user_guid');
        }
        $this->init();
    }

    /**
     * @access public
     * @return mixed
     * @throws Exception
     */
    protected function init()
    {
        $receive_data = tools()::xml2arr(file_get_contents("php://input"));
        $this->data   = $receive_data;
        if (empty($receive_data)) {
            return $this->error_return('未接收到数据');
        }
        logToFile($receive_data);
        if (empty($receive_data['appid']) && empty($receive_data['mch_id'])) {
            return $this->error_return('缺少 appid 和 mch_id参数');
        }
        $db = new Business();
        //wx开头则取 mch_id 反之取appid参数
        $is_really_appid = tools()::start_with($receive_data['appid'], 'wx');
        $appid           = $is_really_appid ? $receive_data['mch_id'] : $receive_data['appid'];
        $business_info   = $db->get_business_info_by_appid_secret($appid);
        if (!$business_info) {
            return $this->error_return($db->getError());
        }
        $this->get_appid() = $appid;
        $this->secret      = $business_info['secret'];
        $this->get_bid()   = $business_info['guid'];
        if ($this->verify($receive_data) === false) {
            wr_log('签名不合法', 1);
            return $this->error_return('签名不合法');
        }
        if (!$is_really_appid) {
            $config              = get_config_by_bid($this->get_bid());
            $this->data['appid'] = $config['weappid']; //重新赋值小程序appid
        }
    }

    /**
     *返回
     * @access protected
     * @param string $msg 错误信息
     * @param string $code 错误码
     * @return mixed
     * @throws Exception
     */
    protected function error_return($msg = 'FAIL', $code = 'FAIL')
    {
        $data         = [
            'return_code'  => 'SUCCESS',
            'return_msg'   => 'OK',
            'result_code'  => 'FAIL',
            'err_code'     => $code,
            'err_code_des' => $msg,
        ];
        $data['sign'] = tools()::get_sign($data, $this->secret);
        return tools()::arr2xml($data);
    }

    /**
     * 签名验证
     * @access protected
     * @param array $data
     * @param string|null $sign
     * @return  bool|array
     * @throws Exception
     */
    protected function verify($data, $sign = null)
    {
        $sign = is_null($sign) ? $data['sign'] : $sign;
        return tools()::get_sign($data, $this->secret) === $sign ? $data : false;
    }
}