<?php
declare (strict_types=1);

namespace app\controller\api\v1;

use app\BaseController;
use app\middleware\YkyManagement;
use app\model\DevicePayRewardNote;
use app\model\DeviceSubmitNote;
use app\model\DeviceTradeNote;
use app\model\PayScorePaymentsModel;
use app\model\SubMerchant;
use app\model\SubMerchantRateApplyNote;
use app\model\YkyActivitySubmit;
use Exception;
use Intervention\Image\AbstractFont;
use Intervention\Image\ImageManagerStatic;
use OpenApi\TencentWenJuan;

class Merchant extends BaseController
{
    protected array $middleware = [
        YkyManagement::class => ['except' => ['report_cookies', 'report_login_url']],
    ];

    /**
     * 上报URL
     * @access public
     * @return void
     * @throws Exception
     */
    public function report_login_url()
    {
        $time = intval(date('Hi'));
        if ($time < 700) {
            error('当前非工作时间');
        }
        $login_url     = input('login_url', null, null);
        $cookies       = input('cookies', null, null);
        $cookies_array = tools()::cookies_to_array(join($cookies, ';'));
        $merchant_code = $cookies_array['merchant_code'] ?? '';
        $text_card     = [
            'url'         => $login_url,
            'title'       => '微信商户平台重新登陆提醒',
            'description' => '商户号: ' . $merchant_code,
        ];
        qy_weixin_msg()->textcard($text_card)->send();
        //$msg = '一卡易微信通道模拟登陆失效,请点击链接重新登陆 ' . $login_url . ' 参数商户号:' . $merchant_code;
        //send_qy_wechat($msg, 'xieyongfa|admin');
        //send_qy_wechat($msg, get_system_config('pay_notice'), 3);
        success();
    }

    /**
     * 上报cookie
     * @access public
     * @return void
     * @throws Exception
     */
    public function report_cookies()
    {
        $cookies       = input('cookies', null, null);
        $cookies_array = tools()::cookies_to_array($cookies);
        if (empty($cookies_array['merchant_code'])) {
            error('cookies不包含merchant_code信息');
        }
        $merchant_code = $cookies_array['merchant_code'];
        $config        = [
            'merchant_code' => $merchant_code,
            'cookies'       => $cookies,
        ];
        $sub           = new \OpenApi\SubMerchant($config);
        $result        = $sub->check_cookie();
        if ($result === true) {
            cache('sub_merchant_cookies:' . $merchant_code, $cookies);
            cache('sub_merchant_cookies_update_time:' . $merchant_code, format_timestamp());
        } else {
            error('cookies无效');
        }
        $url        = input('url', null, null);
        $user_agent = input('user_agent', null, null);
        $data       = [
            'time'       => time(),
            'url'        => $url,
            'cookies'    => $cookies,
            'user_agent' => $user_agent
        ];
        result($data);
    }

    /**
     * 刷脸活动奖励
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function device_pay_reward_note()
    {
        $db_pay_reward_note = new DevicePayRewardNote();
        $order              = ['month' => 'DESC', 'id' => 'DESC'];
        $submit_list        = $db_pay_reward_note->order($order);
        result($this->_paginate($submit_list));
    }

    /**
     * 商户预报名情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function device_submit_list()
    {
        $db_device_submit_note = new DeviceSubmitNote();
        $order                 = ['month' => 'DESC', 'id' => 'DESC'];
        $submit_list           = $db_device_submit_note->order($order);
        result($this->_paginate($submit_list));
    }

    /**
     * 商户名情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function activity_submit()
    {
        $db_activity_submit = new YkyActivitySubmit();
        $params             = $this->params;
        $map                = [];
        if (!empty($params['value'])) {
            $map[] = ['account', 'LIKE', '%' . trim($params['value']) . '%'];
        }
        $order       = [
            'as.id'          => 'DESC',
            'as.create_time' => 'DESC',
        ];
        $field       = [
            'as.*',
            'yam.agent_account' => 'agent_account',
        ];
        $submit_list = $db_activity_submit->alias('as')->where($map)->field($field)->leftJoin('yky_agent_map yam', 'as.apply_appid=yam.appid AND as.apply_openid=yam.openid')->order($order);
        result($this->_paginate($submit_list));
    }

    /**
     * 批量审核通过
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function batch_examine_activity_submit()
    {
        $db_activity_submit = new YkyActivitySubmit();
        $map                = [
            ['status', '=', 0]
        ];
        $update_data        = [
            'status'       => 1,
            'examine_time' => format_timestamp()
        ];
        $db_activity_submit::update($update_data, $map);
        success('审核通过');
    }

    /**
     * 商户交易情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function device_trade_list()
    {
        $db_device_trade_note = new DeviceTradeNote();
        $field                = [
            'dtn.*',
            'darn.month'             => 'reward_month',
            'darn.money'             => 'reward_money',
            'darn.camera_code'       => 'camera_code',
            'darn.real_reward_month' => 'real_reward_month',
            //            Db::raw('DATE_FORMAT(dsn.month,"%Y-%m-%d") as submit_month'),
        ];
        $order                = ['dtn.begin_date' => 'DESC', 'dtn.end_date' => 'DESC', 'dtn.id' => 'DESC'];
        $trade_note_list      = $db_device_trade_note->alias('dtn')->field($field)->leftJoin('device_access_reward_note darn', 'darn.sn_code=dtn.sn_code')->order($order);
        //     $trade_note_list        = $db_device_trade_note->alias('dtn')->field($field)->leftJoin('device_access_reward_note darn', 'darn.sn_code=dtn.sn_code')->leftJoin('device_submit_note dsn', 'dtn.sn_code=dsn.sn_code')->order($order);
        result($this->_paginate($trade_note_list));
    }

    /**
     * 费率修改申请列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function rate_apply_list()
    {
        $params          = $this->params;
        $map             = [];
        $db_sub_merchant = new SubMerchantRateApplyNote();
        if (!empty($params['value'])) {
            $map[] = ['agent_account|merchant_id|merchant_name|merchant_short_name', 'LIKE', '%' . trim($params['value']) . '%'];
        }
        $order         = [
            'm.status'      => 'ASC',
            'm.create_time' => 'DESC',
        ];
        $field         = [
            'm.*',
            'yam.agent_account' => 'agent_account',
        ];
        $merchant_list = $db_sub_merchant->alias('m')->where($map)->field($field)->leftJoin('yky_agent_map yam', 'm.apply_appid=yam.appid AND m.apply_openid=yam.openid')->order($order);
        result($this->_paginate($merchant_list));
    }

    /**
     * 商户申请列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function merchant_list()
    {
        $db_sub_merchant = new SubMerchant();
        $params          = $this->params;
        $map             = [
            ['merchant_status', '=', 1]
        ];
        if (!empty($params['value'])) {
            $map[] = ['agent_account|merchant_id|business_account|wechat_appid|merchant_name', 'LIKE', '%' . trim($params['value']) . '%'];
        }
        $order         = [
            'm.status'      => 'ASC',
            'm.create_time' => 'DESC',
        ];
        $field         = [
            'm.*',
            'yam.agent_account' => 'agent_account',
        ];
        $merchant_list = $db_sub_merchant->alias('m')->where($map)->field($field)->leftJoin('yky_agent_map yam', 'm.apply_appid=yam.appid AND m.apply_openid=yam.openid')->order($order);
        result($this->_paginate($merchant_list));
    }

    /**
     * 编辑商户资料
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $update_data     = $this->params;
        $db_sub_merchant = new SubMerchant();
        $map             = [
            ['id', '=', $update_data['id']],
            ['merchant_id', '=', $update_data['merchant_id']],
        ];
        $allow_field     = [
            'merchant_name',
            'industry',
            'business_account',
            'wechat_appid',
        ];
        $db_sub_merchant->allowField($allow_field)::update($update_data, $map);
        success('修改成功');
    }

    /**
     * 编辑配置
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function dev_config()
    {
        $params                   = $this->params;
        $merchant_id              = $params['merchant_id'];
        $value                    = $params['value'];
        $wechat_appid_pattern     = "/^wx[A-Za-z0-9]{16}$/"; //公众号APPID
        $merchant_pattern         = "/^1\d{9}$/"; //微信子商户正则
        $business_account_pattern = "/^[A-Za-z0-9]+$/"; //商家账号正则
        $db                       = new SubMerchant();
        $map                      = [['merchant_id', '=', $merchant_id]];
        $merchant_info            = $db->where($map)->find();
        if (preg_match($wechat_appid_pattern, $value)) {
            //填写的APPID
            $value  = strtolower($value);
            $result = $db->add_subscribe_appid($merchant_id, $value);
            if ($result !== true) {
                error('关注APPID配置失败:' . $result);
            }
            $result = $db->add_pay_appid($merchant_id, $value);
            if ($result !== true) {
                error('关注APPID配置成功,但是支付APPID配置失败:' . $result);
            }
            $this->query_sub_dev_config($merchant_id);
            $update_data = ['wechat_appid' => $value];
            $db::update($update_data, $map);
            success('支付APPID和关注APPID:' . $value . '配置成功');
        } elseif (preg_match($business_account_pattern, $value)) {
            //填写的商家账号
            $business_account = strtolower($value);
            if ($merchant_info['wechat_appid'] == get_system_config('platform_sub_merchant_appid')) {
                //如果走平台子商户通道,则支付目录配置成统一的
                $jsapi_path = 'https://m.yunhuiyuan.cn/Pay/';
            } else {
                $jsapi_path = 'https://' . $business_account . '.m.yunhuiyuan.cn/Pay/';
            }
            $result = $db->add_jsapi_path($merchant_id, $jsapi_path);
            if ($result !== true) {
                error('支付目录配置失败:' . $result);
            }
            $update_data = ['business_account' => $business_account, 'jsapi_path' => $jsapi_path];
            $db::update($update_data, $map);
            $this->query_sub_dev_config($merchant_id);
            $result = $db->add_pay_channel($business_account);
            if ($result !== true) {
                error('支付通道添加失败:' . $result);
            }
            success('支付目录:' . $jsapi_path . '以及[一卡易微信通道]配置成功成功');
        } elseif (tools()::is_url($value)) {
            //填写的URL
            $result = $db->add_jsapi_path($merchant_id, $value);
            if ($result !== true) {
                error('支付目录配置失败:' . $result);
            }
            $update_data = ['jsapi_path' => $value];
            $db::update($update_data, $map);
            $this->query_sub_dev_config($merchant_id);
            success('支付目录' . $value . '配置成功!');
        } else {
            error('请填写appid或url或商家账号');
        }
    }

    /**
     * 查询配置
     * @access public
     * @param string $merchant_id 商户号
     * @return mixed
     * @throws Exception
     */
    protected function query_sub_dev_config($merchant_id)
    {
        job()->set_job_name('Pay@query_sub_dev_config')->push_job(['merchant_id' => $merchant_id]); //批量查询当前支付配置,以更新merchant_status 字段, 否则不会被监控起来
        return true;
    }

    /**
     * 查询
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query()
    {
        $params      = $this->params;
        $merchant_id = $params['merchant_id'];
        $db          = new SubMerchant();
        $result      = $db->get_face_pay_result($merchant_id);
        if ($result !== true) {
            $msg = '抱歉,尚未通过,' . $merchant_id . '最新状态:' . $result;
            wr_log($msg, 1);
            error($msg);
        } else {
            $msg = '恭喜,商户号' . $merchant_id . '刷脸权限[已通过]';
            wr_log($msg);
            success($msg);
        }
        //       job()->set_job_name('Pay@add_pay_appid')->push_job(['merchant_id' => $merchant_id]); //配置支付APPID
        //       job()->set_job_name('Pay@add_subscribe_appid')->push_job(['merchant_id' => $merchant_id]); //配置关注APPID
        //       job()->set_job_name('Pay@add_jsapi_path')->push_job(['merchant_id' => $merchant_id]); //配置支付目录
        //job()->set_job_name('Pay@query_sub_dev_config')->push_job(['merchant_id' => $merchant_id]); //批量查询当前支付配置,以更新merchant_status 字段, 否则不会被监控起来
        job()->set_job_name('Pay@get_face_pay_examine_result')->push_job(['merchant_id' => $merchant_id]); //批量查询当前支付配置,以更新merchant_status 字段, 否则不会被监控起来
        $this->query_sub_dev_config($merchant_id);
        success('请刷新页面重新查看配置');
    }

    /**
     * 更新费率状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_rate_list()
    {
        $cache_key = __FUNCTION__;
        if (cache($cache_key)) {
            error('1分钟内只允许操作1次,请稍后重试');
        }
        $data   = [];
        $params = $this->params;
        if (!empty($params['merchant_id'])) {
            $data['merchant_code'] = $params['merchant_id'];
        }
        job()->set_job_name('Merchant@get_sub_merchant_rate_apply_list')->push_job(); //自动更新状态
        cache($cache_key, format_timestamp(), 60);
        success('任务提交成功,稍后刷新查看');
    }

    /**
     * 更新授权状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_auth_status()
    {
        $cache_key = __FUNCTION__;
        if (cache($cache_key)) {
            error('10分钟内只允许操作1次,请稍后重试');
        }
        $db    = new SubMerchant();
        $map   = [
            ['status', '=', -1],
            ['merchant_status', '=', 1],
        ];
        $list  = $db->field(['merchant_id'])->where($map)->select();
        $count = 0;
        foreach ($list as $merchant) {
            $merchant_id = $merchant['merchant_id'];
            job()->set_job_name('Merchant@authorize_pay_and_refund')->push_job(['merchant_id' => $merchant_id]); //自动发起邀请
            ++$count;
        }
        cache($cache_key, format_timestamp(), 600);
        success('共提交' . $count . '个商户更新,请稍后刷新页面重新查看!');
    }

    /**
     * 更新支付APPID状态
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function update_pay_appid_status()
    {
        $cache_key = __FUNCTION__;
        if (cache($cache_key)) {
            error('10分钟内只允许操作1次,请稍后重试');
        }
        $db    = new SubMerchant();
        $map   = [
            ['pay_appid_status', '=', 0],
            ['merchant_status', '=', 1],
        ];
        $list  = $db->field(['merchant_id'])->where($map)->select();
        $count = 0;
        foreach ($list as $merchant) {
            $merchant_id = $merchant['merchant_id'];
            job()->set_job_name('Pay@query_pay_appid_status')->push_job(['merchant_id' => $merchant_id]);
            ++$count;
        }
        cache($cache_key, format_timestamp(), 600);
        success('共提交' . $count . '个商户更新,请稍后刷新页面重新查看!');
    }

    /**
     * 查找appid
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function find_appid_by_business_account()
    {
        $cache_key = __FUNCTION__;
        if (cache($cache_key)) {
            error('10分钟内只允许操作1次,请稍后重试');
        }
        $db               = new SubMerchant();
        $last_create_time = date("Y-m-d H:i:s", strtotime("-30 day")); //只处理最近30天数据
        $map              = [
            ['business_account', '<>', ''],
            //           ['pay_appid_status', '=', 0], //只查看有效商户
            ['wechat_appid', '=', ''], //只查看有效商户
            ['merchant_status', '=', 1], //只查看有效商户
            ['create_time', '>', $last_create_time]
        ];
        $merchant_list    = $db->field(['merchant_id'])->where($map)->order(['update_time' => 'ASC'])->select();
        $count            = 0;
        foreach ($merchant_list as $merchant) {
            $merchant_id = $merchant['merchant_id'];
            job()->set_job_name('Pay@find_appid_by_business_account')->push_job(['merchant_id' => $merchant_id]); //通过商家账号查找appid
            ++$count;
        }
        cache($cache_key, format_timestamp(), 600);
        success('共提交' . $count . '个商户更新,请稍后刷新页面重新查看!');
    }

    /**
     * 批量邀约
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function send_examine_result_notify()
    {
        $cache_key = __FUNCTION__;
        if (cache($cache_key)) {
            error('10分钟内只允许操作1次,请稍后重试');
        }
        $db               = new SubMerchant();
        $last_create_time = date("Y-m-d H:i:s", strtotime("-30 day")); //只处理最近30天数据
        $map              = [
            ['status', '=', -1], //只查等待邀请
            ['merchant_status', '=', 1], //只查看有效商户
            ['create_time', '>', $last_create_time]
        ];
        $merchant_list    = $db->field(['merchant_id'])->where($map)->order(['update_time' => 'ASC'])->limit(1000)->select();
        $count            = 0;
        foreach ($merchant_list as $merchant) {
            $merchant_id = $merchant['merchant_id'];
            job()->set_job_name('Pay@send_examine_result_notify')->push_job(['merchant_id' => $merchant_id]); //发送邀请授权成功通知
            ++$count;
        }
        cache($cache_key, format_timestamp(), 600);
        if ($count == 0) {
            error('当前没有需要通知的商户');
        }
        $update_data = ['status' => 0, 'submit_time' => microsecond()];
        $db::update($update_data, $map);
        success('共提交' . $count . '个商户更新,请稍后刷新页面重新查看!');
    }

    /**
     * 查询刷脸立减活动参与情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_coupon_used_record()
    {
        $params                 = $this->params;
        $merchant_id            = $params['merchant_id'];
        $db_yky_activity_submit = new YkyActivitySubmit();
        return $db_yky_activity_submit->query_coupon_used_record($merchant_id);
    }

    /**
     * 查询刷脸立减活动参与情况
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function device_info()
    {
        $params  = $this->params;
        $sn_code = $params['sn_code'];
        //todo
        success('功能待完善');
        success($sn_code);
    }

    /**
     * 模拟提交收收银员活动报名
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_checker_active()
    {
        $params                 = $this->params;
        $merchant_id            = $params['merchant_id'];
        $tencent_wen_juan       = new TencentWenJuan();
        $answer_result          = $tencent_wen_juan->submit_checker_active($merchant_id);
        $data                   = [
            'type'          => 3,//报名类型 1商家 2代理商 3商户号
            'activity_type' => 3,//活动类型 1 到店红包 2 刷脸立减 3 收银员奖励
            'account'       => $merchant_id,
            'status'        => 1,
            'remark'        => '手动提交',
            'examine_time'  => format_timestamp(),
        ];
        $db_yky_activity_submit = new YkyActivitySubmit();
        $result                 = $db_yky_activity_submit->save($data);
        success('收银员奖励报名成功');
    }

    /**
     * 模拟提交刷脸活动报名
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_face_pay_active()
    {
        $params                 = $this->params;
        $merchant_id            = $params['merchant_id'];
        $db_yky_activity_submit = new YkyActivitySubmit();
        $db_yky_activity_submit->submit_face_pay_active($merchant_id);
        success('刷脸活动报名成功');
    }

    /**
     * 模拟提交刷脸活动报名
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function submit_face_pay_active_wenjuan()
    {
        $params                 = $this->params;
        $merchant_id            = $params['merchant_id'];
        $tencent_wen_juan       = new TencentWenJuan();
        $answer_result          = $tencent_wen_juan->submit_face_pay_active($merchant_id);
        $data                   = [
            'type'          => 3,//报名类型 1商家 2代理商 3商户号
            'activity_type' => 2,//活动类型 1 到店红包 2 刷脸立减 3 收银员奖励
            'account'       => $merchant_id,
            'status'        => 1,
            'remark'        => '手动提交',
            'examine_time'  => format_timestamp(),
        ];
        $db_yky_activity_submit = new YkyActivitySubmit();
        $result                 = $db_yky_activity_submit->save($data);
        success('刷脸活动报名成功');
    }

    /**
     * 先享机规则列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function pay_score()
    {
        $db    = new PayScorePaymentsModel();
        $join  = [
            ['pay_score_goods psg', 'psg.guid=pspm.pay_score_goods_guid'],
            ['agent ag', "ag.guid = pspm.agent_guid", 'LEFT'],
        ];
        $field = [
            'pspm.id'                   => 'id',
            'pspm.guid'                 => 'rule_guid',
            'pspm.pay_score_goods_guid' => 'goods_guid',
            'pspm.payments_model_json'  => 'payments_model_json',
            'pspm.is_off_line'          => 'status',
            'psg.title'                 => 'goods_title',
            'pspm.agent_guid'           => 'agent_guid',
            'ag.account'                => 'agent_account',
            'ag.company'                => 'agent_company',
        ];
        $list  = $db->alias('pspm')->join($join)->field($field)->order(['pspm.id' => 'DESC']);
        result($this->_paginate($list));
    }

    /**
     * 先享机二维码
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function get_pay_score_qrcode()
    {
        $params        = $this->params;
        $rule_guid     = $params['rule_guid'];
        $goods_guid    = $params['goods_guid'];
        $agent_account = $params['agent_account'];
        $goods_title   = $params['goods_title'];
        $appid         = 'wxa94b0f7f896c666b';
        $base_path     = 'pages/Detail/index?';
        $path          = $base_path . http_build_query(['rule' => $rule_guid, 'guid' => $goods_guid]);
        $file_path     = '/temp/images/' . md5($appid . $path) . '.png';
        $absolute_path = tools()::get_absolute_path($file_path);
        $file_name     = $agent_account . '-' . $goods_title;
        if (!file_exists($absolute_path)) {
            $wechat       = weixin($appid)::WeMiniQrcode();
            $qrcodeBinary = $wechat->createMiniPath($path, 600, false, ['r' => '0', 'g' => '0', 'b' => '0'], false);
            // 直接用二进制内容生成Image对象
            $driver_name = extension_loaded('imagick') ? 'imagick' : 'gd';
            ImageManagerStatic::configure(['driver' => $driver_name]);
            $image = ImageManagerStatic::make($qrcodeBinary);
            $font  = tools()::get_absolute_path('/static/css/fonts/MSYH.TTC');
            $image->text($file_name, 10, 10, function (AbstractFont $font_draw) use ($font) {
                $font_draw->file($font);
                $font_draw->size(12);
                $font_draw->color('#EEEEEE');
                $font_draw->align('left');
                $font_draw->valign('top');
            });
            $image->save($absolute_path);
        }
        $data = ['src' => $file_path, 'file_name' => $file_name];
        result($data);
    }
}
