<?php

namespace app\controller\api\v1;

use app\BaseController;
use Exception;
use Storage\Storage;

class File extends BaseController
{
    /**
     *base64上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function upload_base64()
    {
        $params = $this->params;
        $this->request->__set('_base64', trim($params['editor']));
//        $upload = Storage::local();
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        return $upload ? response('<img src="' . $file['url'] . '" alt="" />')->contentType('text/html') : '';
    }

    /**
     *上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function upload()
    {
        //$storage_type = !empty($this->params['storage']) ? $this->params['storage'] : $config['storage_type'];
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        if ($upload !== false) {
            result(['filePath' => $file['url']]);
        } else {
            error($upload->getMsg());
        }
    }

    /**
     *上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function upload_layui()
    {
        //$storage_type = !empty($this->params['storage']) ? $this->params['storage'] : $config['storage_type'];
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        if ($upload !== false) {
            result(['file' => $file['url']]);
        } else {
            error($upload->getMsg());
        }
    }

    /**
     *获取驱动
     * @access protected
     * @return string
     * @throws Exception
     */
    protected function get_storage_type()
    {
        $bid    = config('app.super_admin_business_guid');
        $config = get_config_by_bid($bid);
        return $config['storage_type'];
    }

    /**
     *移动端上传
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function uploadifive()
    {
        $upload = Storage::driver($this->get_storage_type());
        $file   = $upload->upload();
        if ($upload !== false) {
            $msg = [
                'error'   => 0,
                'message' => 'success',
                'url'     => $file['url'],
            ];
        } else {
            $msg = [
                'error'   => -1,
                'message' => $upload->getMsg()
            ];
        }
        return json($msg);
    }
}