<?php


namespace app\controller\api\v1;


use app\BaseController;
use app\common\service\CropWeixinService;
use OpenApi\Yky;
use Stoneworld\Wechat\Token;

class CropWechat extends BaseController
{
    //    protected $corpid = 'wwd8cd62fcd5f38121';
    //    protected $agentid = '1000034';
    protected $corpid = 'wx3b5649a58bff1e0b';
    protected $agentid = '0';

    public function get_member_info()
    {
        $params  = $this->params;
        $user_id = $params['user_id'];
        $corpid  = $this->corpid;
        $agentid = $this->agentid;
        $obj     = CropWeixinService::get_instance($corpid, $agentid);
        $auth    = $obj->Auth();
        $result  = $auth->toExternalOpenid($user_id);
        //       $bid         = '26810245-d97e-81b6-c1cf-0215fd8f347c';
        //       $config      = get_config_by_bid($bid);
//        wr_log($result, 1);
        $config = [
            'openid' => 'E7256EB83BC111E9BC5E001018640E28',
            'secret' => 'JEKT92',
        ];
        $config = [
            'openid' => 'D5A508D48F6C4BB5BE3D144301B1DABD',
            'secret' => 'W70UMZ',
        ];

        $yky         = new Yky($config);
        $member_info = $yky->get_member_info_by_openid($result['openid']);
        result($member_info);
    }

    public function get_config()
    {
        $params      = $this->params;
        $url         = $params['href'];
        $corpid      = $this->corpid;
        $agentid     = $this->agentid;
        $obj         = CropWeixinService::get_instance($corpid, $agentid);
        $js          = $obj->Js();
        $config      = $js->getConfig($url);
        $agentConfig = $js->getAgentConfig($this->agentid, $url);
        result([
            'config'       => $config,
            'agent_config' => $agentConfig,
        ]);
    }

    public function get_config_v2()
    {
        $params = $this->params;
        $url    = $params['href'];
        $bid    = $params['bid'];
//        $suite_id     = 'ww45d16a562d76f34f';
//        $auth_corp_id = 'wp3zzMDwAANe97kpiMi8i8yDD-TjIfIg';
//        $token        = new Token($suite_id, $auth_corp_id);
        $js          = work_weixin($bid)::Js();
        $config      = $js->getConfig($url);
        $agentConfig = $js->getAgentConfig($url);
        result([
            'config'       => $config,
            'agent_config' => $agentConfig,
        ]);
    }

    public function get_member_info_v2()
    {
        $params  = $this->params;
        $user_id = $params['user_id'];
        error(json_encode($params, JSON_UNESCAPED_UNICODE));
    }
}