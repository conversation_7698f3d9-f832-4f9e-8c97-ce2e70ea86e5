<?php
declare (strict_types=1);

namespace app\controller\api\v1;

use app\BaseController;
use app\common\service\TokenService;
use Exception;

class Management extends BaseController
{
    protected $cookie_key = 'management_access_token';

    /**
     * 登陆API
     * @access public
     * @return void
     * @throws Exception
     */
    public function login()
    {
        $params    = $this->params;
        $account   = $params['account'];
        $password  = $params['password'];
        $login_url = 'https://mps.1card1.cn/admin/index/api/basic/login';
        $data      = [
            'account'     => $account,
            'password'    => $password,
            'accountType' => 0,
        ];
        //       $this->validate($params, [
        //           'captcha|验证码' => 'require|captcha'
        //       ]);
        $post_data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $result    = curl()->post($login_url, $post_data)->get_body();
        if (!is_array($result)) {
            error('登录异常');
        }
        if (isset($result['success']) && $result['success'] === true) {
            $exp   = 3600 * 24 * 7;
            $token = ['account' => $account, 'exp' => $exp];
            $jwt   = TokenService::encode($token);
            cookie($this->cookie_key, $jwt, $exp);
            success('登录成功');
        } else {
            error('账号或密码错误');
        }
    }

    /**
     * 退出API
     * @access public
     * @return void
     * @throws Exception
     */
    public function logout()
    {
        cookie($this->cookie_key, null);
        success('退出成功');
    }
}
