<?php


namespace app\controller\api\v1;


use app\BaseController;
use app\model\ChanganOrder;
use app\model\Mobile;
use app\model\SinglePage;
use app\model\XiaoQuHouse;
use app\common\tools\Excel;
use Exception;
use OpenApi\FengChao;
use OpenApi\ZhuJianJu;
use think\facade\Db;
use xieyongfa\yikayi\Yikayi;
use Cyokup\EasyAiChat\EasyAiChat;

class Tools extends BaseController
{
    /**
     * 统一AI聊天接口
     * @return mixed
     */
    public function chat()
    {
        $params  = $this->params;
        $message = $params['message'] ?? '';
        $model   = $params['model'] ?? 'glm4'; // 默认使用GLM-4
        $stream  = $params['stream'] ?? true; // 默认流式输出
        $images  = $params['images'] ?? []; // 图片数据
        // 验证消息内容
        if (empty($message)) {
            if ($stream) {
                http_response_code(200);
                header('Content-Type: text/plain; charset=utf-8');
                echo "请输入消息内容";
                return;
            } else {
                error('消息内容不能为空');
            }
        }

        // 获取支持的模型配置
        $modelConfigs = $this->getAiModelConfigs();

        // 验证模型是否支持
        if (!isset($modelConfigs[$model])) {
            $errorMsg = '不支持的AI模型: ' . $model . '，支持的模型: ' . implode(', ', array_keys($modelConfigs));
            if ($stream) {
                http_response_code(200);
                header('Content-Type: text/plain; charset=utf-8');
                echo $errorMsg;
                return;
            } else {
                error($errorMsg);
            }
        }

        $config = $modelConfigs[$model];
        try {
            $aiModel  = new EasyAiChat();
            $messages = [
                ['role' => 'user', 'content' => $message]
            ];

            // 直接传递完整配置，让Gateway内部自己决定需要什么
            $apiConfig = $config;

            // 构建消息数组
            $messages = [];
            // 如果有图片且模型支持视觉功能，构建多模态消息
            if (!empty($images) && isset($config['supports_vision']) && $config['supports_vision']) {
                $content = [];
                // 添加文本内容
                if (!empty($message)) {
                    $content[] = [
                        'type' => 'text',
                        'text' => $message
                    ];
                }

                // 添加图片内容
                foreach ($images as $image) {
                    $content[] = [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => $image['data'] // Base64数据
                        ]
                    ];
                }

                $messages[] = [
                    'role'    => 'user',
                    'content' => $content
                ];
            } else {
                // 纯文本消息
                $messages[] = [
                    'role'    => 'user',
                    'content' => $message
                ];
            }
            $modelInstance = $aiModel->gateway($model, $apiConfig);
            if ($stream) {
                // 设置流式响应头
                http_response_code(200);
                header('Content-Type: text/plain; charset=utf-8');
                header('Cache-Control: no-cache');
                header('Connection: keep-alive');
                header('X-Accel-Buffering: no');

                // 关闭所有输出缓冲
                while (ob_get_level()) {
                    ob_end_flush();
                }

                // 启用隐式刷新
                if (function_exists('ob_implicit_flush')) {
                    ob_implicit_flush(1);
                }

                // 发送初始数据确保连接建立
                echo '';
                flush();

                // 流式输出
                $parameters = [
                    'model'       => $config['default_model'] ?? $model,
                    'temperature' => $config['default_params']['temperature'] ?? 0.7,
                    'max_tokens'  => $config['default_params']['max_tokens'] ?? 1024,
                    'top_p'       => $config['default_params']['top_p'] ?? 0.9,
                ];
                $modelInstance->chat($messages, $parameters, true);
                return;
            } else {
                // 非流式输出
                $parameters = [
                    'model'       => $config['default_model'] ?? $model,
                    'temperature' => $config['default_params']['temperature'] ?? 0.7,
                    'max_tokens'  => $config['default_params']['max_tokens'] ?? 1024,
                    'top_p'       => $config['default_params']['top_p'] ?? 0.9,
                ];
                $result     = $modelInstance->chat($messages, $parameters, false);
                result([
                    'content'    => $result[0],
                    'tokens'     => $result[1] ?? 0,
                    'model'      => $model,
                    'model_name' => $config['name']
                ]);
            }
        } catch (Exception $e) {
            $errorMsg = 'AI调用失败: ' . tools()::exception_to_string($e);
            if ($stream) {
                // 流式输出时，确保返回HTTP 200状态码
                http_response_code(200);
                header('Content-Type: text/plain; charset=utf-8');
                echo $errorMsg;
                return;
            } else {
                // 非流式输出时，使用error方法返回JSON格式错误
                error($errorMsg);
            }
        }
    }

    /**
     * 获取AI模型配置
     * @return array
     */
    private function getAiModelConfigs()
    {
        return config('ai.list');
    }


    /**
     * 获取支持的模型列表 - API接口
     * @return mixed
     */
    public function getModels()
    {
        $modelConfigs = $this->getAiModelConfigs();
        result($modelConfigs);
    }

    public function feng_chao()
    {
        $params    = $this->params;
        $all       = (bool)$params['status'];
        $feng_chao = new FengChao();
        $all       = 1;
        $result    = $feng_chao->get_express_list($all);
        result($result);
    }

    public function work_weixin_apply()
    {
        $params = $this->params;
        wr_log($params, 1);
        result(['msg' => '申请成功']);
    }

    public function zhu_jian_ju_table()
    {
        $db    = new XiaoQuHouse();
        $order = [
            'building_index' => 'ASC',
            'building'       => 'ASC',
            'floor'          => 'ASC',
            'room_number'    => 'ASC',
        ];
        $field = [
            Db::raw("CONCAT(building,room_number) AS index_no"),
            'building',
            'vote_date_time',
            'room_number',
            Db::raw("CASE 
            WHEN bind_status = 0 THEN '否'
            WHEN bind_status = 1 THEN '是'
            ELSE '未知' END as bind_status_text"),
            'last_bind_time',
        ];
        $list  = $db->field($field)->order($order)->select()->toArray();
        $map   = [['bind_status', '=', 1]];
        $count = $db->where($map)->count();
        $count .= '条,更新于' . format_timestamp();
        echo '<table border="1">'; // 边框为1，更容易在网页上看到表格结构
        echo '<thead><tr>
              <th>唯一识别码</th>
              <th>栋</th>
              <th>投正式票时间</th>
              <th>房间号</th>
              <th>绑定状态</th>
              <th>绑定时间</th>
              <th>统计</th>
              <th>已投票数据</th>
              <th>临时票户数</th>
              <th>正式票人数</th>
              <th>实际已投面积</th>
               </tr></thead>';
        echo '<tbody>';
        $i = 0;
        foreach ($list as $val) {
            echo '<tr>';
            foreach ($val as $k => $v) {
                echo '<td>' . htmlspecialchars($v) . '</td>';
            }
            if ($i == 0) {
                $zzj              = new ZhuJianJu();
                $total_voted_info = $zzj->get_total_voted_info_v2();
                //                $official_voted_info = $zzj->get_official_voted_info();

                $total_voted_house_num  = $total_voted_info['total_voted_house_num'];
                $total_voted_house_area = $total_voted_info['total_voted_house_area'];

                //                $official_voted_house_num  = $official_voted_info['official_voted_house_num'];
                //                $official_voted_person_num = $official_voted_info['official_voted_person_num'];

                $official_voted_house_num  = 0;
                $official_voted_person_num = 0;

                $str = '<td>' . $count . '</td>';
                $str .= '<td>' . $total_voted_house_num . '</td>';
                $str .= '<td>' . $official_voted_house_num . '</td>';
                $str .= '<td>' . $official_voted_person_num . '</td>';
                $str .= '<td>' . $total_voted_house_area . '</td>';
                $str .= '</tr>';
                echo $str;
            } else {
                echo '<td></td><td></td><td></td><td></td><td></td></tr>';
            }
            $i++;
        }

        echo '</tbody>';
        echo '</table>';
        return '';
    }

    public function xuanhao()
    {
        $params  = $this->params;
        $keyword = $params['keyword'];
        if (empty($keyword)) {
            error('不能为空');
        }
        $url      = get_system_config('xuanhao_url') . $keyword;
        $response = curl()->form_params()->get($url)->get_body();
        $response = tools()::jsonp_decode($response);
        $array    = $response['numArray'];
        foreach ($array as $key => $val) {
            if (!tools()::is_mobile($val)) {
                unset($array[$key]);
            }
        }
        sort($array);
        if (empty($array)) {
            error('查询不到号码');
        } else {
            $list = [];
            foreach ($array as $key => $val) {
                if (strpos($val, 4) === false) {
                    $list[] = $val;
                    unset($array[$key]);
                }
            }
            $array            = array_merge($list, $array);
            $db               = new Mobile();
            $insert_item_data = [];
            $msg              = '';
            foreach ($array as $key => $val) {
                $insert_item_data[] = [
                    'mobile'   => $val,
                    'key_word' => $keyword
                ];
                if (strpos($val, 4) === false) {
                    $msg .= $val . "\r\n";
                }
            }
            $db->saveAll($insert_item_data, false);
            if ($msg) {
                //                 wr_log('发现没有4的号码' . $msg, 1);
            }
        }
        result($array);
    }

    public function get_feiyan_city_list()
    {
        $url         = 'https://api.look.360.cn/events/feiyanGradeCity?sv=&version=&market=&device=2&net=4&stype=&scene=&sub_scene=&refer_scene=&refer_subscene=&f=jsonp&location=true&_=' . time() . '&callback=jsonp6';
        $result      = curl()->get($url)->get_body();
        $data        = (tools()::jsonp_decode($result)['data'] ?? []);
        $return_data = [];
        foreach ($data as $key => $val) {
            foreach ($val as $k => $v) {
                if (!is_array($v)) {
                    $return_data[$key][strtolower($k)] = str_replace('市', '', $v);
                }
            }
        }
        return json($return_data);
    }

    public function get_single_page_detail()
    {
        $params = $this->params;
        $guid   = $params['guid'];
        $db     = new SinglePage();
        $map    = [['guid', '=', $guid]];
        $result = $db->where($map)->findOrFail();
        if ($result['expire_type'] == 2 && tools()::is_expired($result['expire_time'])) {
            error('当前页面已过期,请联系客服');
        }
        $result['content'] = tools()::add_rich_img_class($result['content']);
        result($result);
    }

    /**
     * 获取订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function changan_v3()
    {
        $params = $this->params;
        $link   = $params['link'] ?? '';
        if (strpos($link, 'listInfo') === false) {
            error("请先关公众号【长安汽车服务号】,点击'我的长安'-'个人中心'-'购车订单'-点击订单进度详情,右上角'复制链接'后提交查询");
        }
        $result = tools()::search_str('=', '', $link);
        $data   = json_decode(urldecode($result), true);
        if (empty($data)) {
            wr_log($result);
            //$msg = '您复制的链接可能不完整,建议您重新复制再试';
            $msg = '由于长安系统升级,小工具暂时无法查询了,短期内暂时无法恢复,感谢您的使用,祝您早日提车~';
            wr_log($msg);
            error($msg);
        }
        //       $result = curl()->get($url)->setMaxRetries(2)->setRetryDecider(function ($result) {
        //           return (is_array($result) && isset($result['code']) && $result['code'] === '0') === false;
        //       })->get_body();

        //       if ($result['code'] == '1003') {
        //           $msg = '未查询到购车订单,请确认在"长安fan"APP中能查询到订单再来!';
        //           error($msg);
        //       }
        //       if ($result['code'] !== '0') {
        //           $msg = '订单查询错误,token可能已失效,请重新复制链接!';
        //           error($msg);
        //       }
        //       if (empty($result['data'])) {
        //           error('没有查询到购车订单!');
        //       }
        $status_info = '';
        $db          = new ChanganOrder();
        $order_no    = $data['orderNo'];
        $insert_data = ['order_no' => $order_no, 'data' => $data];
        $db->save($insert_data);
        $orderidCreateDate = $data['orderidCreateDate']; //下单时间
        $orderidCreateDate = date('Y-m-d', strtotime($orderidCreateDate));
        $vinNo             = $data['vinNo'] ?? '';
        $promiseDeliyDate  = $data['promiseDeliyDate']; //预计交期
        if (tools()::is_time($promiseDeliyDate)) {
            $promiseDeliyDate = date('Y-m-d', strtotime($promiseDeliyDate));
        }
        $expDlryDate = $data['expDlryDate']; //要车日期
        if (tools()::is_time($expDlryDate)) {
            $expDlryDate = date('Y-m-d H:i:s', strtotime($expDlryDate));
            $expDlryDate = str_replace(' 00:00:00', '', $expDlryDate);
        }
        $statusNode     = $data['nowNode']['statusNode'];
        $nodeDate       = $data['nowNode']['nodeDate'];
        $high_date      = '2021-10-07';
        $last_wait_day  = floor((time() - strtotime($nodeDate)) / 86400) + 1;
        $order_wait_day = floor((time() - strtotime($orderidCreateDate)) / 86400) + 1;

        $booking_date = '2021-09-07';
        if ((strtotime($booking_date) > strtotime($orderidCreateDate))) {
            //预售前下单
            $high_money_day = floor((time() - strtotime($high_date)) / 86400); //高保从 2021-10-07 开始算
            $low_money_day  = floor((strtotime($high_date) - strtotime($orderidCreateDate)) / 86400) + 1;
        } else {
            //预售后用等车日期减去30,小于0则没有高保
            $high_money_day = $order_wait_day - 30;
            $high_money_day = ($high_money_day < 0) ? 0 : $high_money_day;

            $low_money_day = floor((time() - strtotime($orderidCreateDate)) / 86400) + 1;
            $low_money_day = min($low_money_day, 30);
        }

        $low_money  = $low_money_day * 50;
        $high_money = $high_money_day * 120;

        $status_info .= '订单状态: 【' . $statusNode . "】(第" . $last_wait_day . "天)<br/>";
        $status_info .= "下单日期: " . $orderidCreateDate . " (第" . $order_wait_day . "天)<br/>";
        $status_info .= "预估补贴: " . $low_money_day . '*50+' . $high_money_day . '*120=' . ($low_money + $high_money) . "元<br/>";

        $status_info .= "要车日期: " . $expDlryDate . "<br/>";
        $status_info .= "预计交期: " . $promiseDeliyDate . "<br/>";
        $status_info .= "车架号: " . $vinNo . '<br/>';

        //       $dcsSalesOrderNo = $data['dcsSalesOrderNo'];//物流单号
        //       $erpSalesOrderNo = $data['erpSalesOrderNo'];//ERP单号
        //       if (!empty($dcsSalesOrderNo)) {
        //           $status_info .= '物流单号: ' . $dcsSalesOrderNo . "<br/>";
        //       }
        //       if (!empty($erpSalesOrderNo)) {
        //           $status_info .= 'ERP单号: ' . $erpSalesOrderNo . "<br/>";
        //       }
        $vin_suffix = (int)substr($vinNo, -6); //只关心车架号稍大于谢永发的 260000
        if ($vinNo && in_array($statusNode, ['配送中', '在途', '已到店', '等待用户提车'])) {
            $cache_key = 'changan:' . $vinNo;
            $cache     = cache($cache_key);
            if (!$cache) {
                wr_log($status_info);
                cache($cache_key, $status_info, 3600 * 12);
            }
        }
        $status_info = rtrim($status_info, '<br/>');
        wr_log($status_info);
        success($status_info);
    }

    public function distance()
    {
        $params       = $this->params;
        $address_from = $params['address_from'];
        $address_to   = $params['address_to'];
        $tk           = '85b6a8bbda93f82a27ec1d6199a1c224';
        $url          = 'https://api.tianditu.gov.cn/geocoder?ds={%22keyWord%22:' . urlencode($address_from) . '}&tk=85b6a8bbda93f82a27ec1d6199a1c224';
        $url          = 'https://api.tianditu.gov.cn/geocoder';
        $data         = ['ds' => json_encode(['keyWord' => $address_from], JSON_UNESCAPED_UNICODE), 'tk' => $tk];
        $from_result  = curl()->get($url, $data)->get_body();
        $data         = ['ds' => json_encode(['keyWord' => $address_to], JSON_UNESCAPED_UNICODE), 'tk' => $tk];
        $to_result    = curl()->get($url, $data)->get_body();
        $distance     = tools()::calculate_distance($from_result['location']['lat'], $from_result['location']['lon'], $to_result['location']['lat'], $to_result['location']['lon']);
        $distance     = tools()::nc_price_calculate($distance, '/', '1000', 2);
        success('两地大约距离' . $distance . '公里!');
    }

    /**
     * 获取订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function changan_v2()
    {
        error('因长安系统升级,小工具需要调整升级更新中,预计10月8日18:00恢复');
        $params = $this->params;
        $link   = $params['link'] ?? '';
        if (empty($link)) {
            error("请先关公众号【长安汽车服务号】,点击'我的长按'-'个人中心',右上角'复制链接'后提交查询");
        }
        if (strpos($link, 'token=') === false) {
            error('因长安升级更新,部分用户暂时无法查询,升级时间待定~');
        }
        $token        = tools()::search_str('token=', '', $link);
        $url          = 'https://scrm.changan.com.cn/scrm-app-web/tmjc/generateSignature';
        $data         = ['sys' => 'cafan_app', 'token' => $token];
        $token_result = curl()->form_params()->post($url, $data)->get_body();
        if ($token_result['result'] !== 0) {
            $msg = '查询失败,token可能已失效,请重新复制链接!';
            error($msg);
        }
        wr_log('新增有效token:' . $token);
        $cache_key = 'changan_token:' . $token;
        $result    = cache($cache_key);
        if (!$result) {
            $data     = [
                'sys'            => 'cafan_app',
                'sign'           => $token_result['data']['sign'],
                'customerMobile' => $token_result['data']['customerMobile'],
                'echo'           => $token_result['data']['echo'],
                'time'           => $token_result['data']['time'],
            ];
            $signInfo = urlencode(json_encode($data));
            $url      = 'http://cvsses.changan.com.cn/tmjc/orderList?customerMobile=' . $token_result['data']['customerMobile'] . '&isHistory=false&keyWord=&searchType=customerMobile&pageSize=2&signInfo=' . $signInfo;
            $url      = 'http://47.115.7.168/curl.php?url=' . urlencode($url);

            $result = curl()->get($url)->setMaxRetries(2)->setRetryDecider(function ($result) {
                return (is_array($result) && isset($result['code']) && $result['code'] === '0') === false;
            })->get_body();
            cache($cache_key, $result, 3600);
        }
        if ($result['code'] == '1003') {
            $msg = '未查询到购车订单,请确认在"长安fan"APP中能查询到订单再来!';
            error($msg);
        }
        if ($result['code'] !== '0') {
            cache($cache_key, null);
            $msg = '订单查询错误,token可能已失效,请重新复制链接!';
            error($msg);
        }
        $status_info = '';
        foreach ($result['data'] as $data) {
            $db          = new ChanganOrder();
            $order_no    = $data['orderNo'];
            $insert_data = ['order_no' => $order_no, 'data' => $data];
            $db->save($insert_data);
            $orderidCreateDate = $data['orderidCreateDate']; //下单时间
            $orderidCreateDate = date('Y-m-d', strtotime($orderidCreateDate));
            $vinNo             = $data['vinNo'] ?? '';
            $promiseDeliyDate  = $data['promiseDeliyDate']; //预计交期
            if (tools()::is_time($promiseDeliyDate)) {
                $promiseDeliyDate = date('Y-m-d', strtotime($promiseDeliyDate));
            }
            $expDlryDate = $data['expDlryDate']; //要车日期
            if (tools()::is_time($expDlryDate)) {
                $expDlryDate = date('Y-m-d H:i:s', strtotime($expDlryDate));
                $expDlryDate = str_replace(' 00:00:00', '', $expDlryDate);
            }
            $statusNode     = $data['nowNode']['statusNode'];
            $nodeDate       = $data['nowNode']['nodeDate'];
            $high_date      = '2021-10-07';
            $last_wait_day  = floor((time() - strtotime($nodeDate)) / 86400) + 1;
            $order_wait_day = floor((time() - strtotime($orderidCreateDate)) / 86400) + 1;
            $low_money_day  = floor((strtotime($high_date) - strtotime($orderidCreateDate)) / 86400) + 1;

            $high_money_day = floor((time() - strtotime($high_date)) / 86400);
            $low_money      = $low_money_day * 50;
            $high_money     = $high_money_day * 120;

            $status_info .= '订单状态: 【' . $statusNode . "】(第" . $last_wait_day . "天)<br/>";
            $status_info .= "下单日期: " . $orderidCreateDate . " (第" . $order_wait_day . "天)<br/>";
            $status_info .= "预估补贴: " . $low_money_day . '*50+' . $high_money_day . '*120=' . ($low_money + $high_money) . "元<br/>";

            $status_info .= "要车日期: " . $expDlryDate . "<br/>";
            $status_info .= "预计交期: " . $promiseDeliyDate . "<br/>";
            $status_info .= "车架号: " . $vinNo . '<br/>';

            $dcsSalesOrderNo = $data['dcsSalesOrderNo']; //物流单号
            $erpSalesOrderNo = $data['erpSalesOrderNo']; //ERP单号
            if (!empty($dcsSalesOrderNo)) {
                $status_info .= '物流单号: ' . $dcsSalesOrderNo . "<br/>";
            }
            if (!empty($erpSalesOrderNo)) {
                $status_info .= 'ERP单号: ' . $erpSalesOrderNo . "<br/>";
            }
            $vin_suffix = (int)substr($vinNo, -6); //只关心车架号稍大于谢永发的 260000
            if ($vinNo && in_array($statusNode, ['配送中', '在途', '已到店', '等待用户提车'])) {
                $cache_key = 'changan:' . $vinNo;
                $cache     = cache($cache_key);
                if (!$cache) {
                    wr_log($status_info);
                    cache($cache_key, $status_info, 3600 * 12);
                }
            }
        }
        $status_info = rtrim($status_info, '<br/>');
        wr_log($status_info);
        success($status_info);
    }


    /**
     * 获取订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function changan()
    {
        //     error('因长安接口调整,小工具升级维护中,预计9月27日22点恢复,感谢您的使用~');
        $params = $this->params;
        $link   = $params['link'] ?? '';
        if (empty($link)) {
            error("请先关公众号【长安汽车服务号】,点击'我的长按'-'个人中心',右上角'复制链接'后提交查询");
        }
        if (strpos($link, 'token=') === false) {
            error('您复制的链接不正确,未包含token关键词');
        }
        $token  = tools()::search_str('token=', '', $link);
        $url    = 'https://scrm.changan.com.cn/scrm-app-web/tmjc/generateSignature';
        $data   = ['sys' => 'cafan_app', 'token' => $token];
        $result = curl()->form_params()->post($url, $data)->get_body();
        if ($result['result'] !== 0) {
            $msg = '查询失败,token可能已失效,请重新复制链接!';
            error($msg);
        }
        wr_log('新增有效token:' . $token);
        $data     = [
            'sys'            => 'cafan_app',
            'sign'           => $result['data']['sign'],
            'customerMobile' => $result['data']['customerMobile'],
            'echo'           => $result['data']['echo'],
            'time'           => $result['data']['time'],
        ];
        $signInfo = urlencode(json_encode($data));
        $url      = 'http://cvsses.changan.com.cn/tmjc/orderList?customerMobile=' . $result['data']['customerMobile'] . '&isHistory=false&keyWord=&searchType=customerMobile&pageSize=2&signInfo=' . $signInfo;
        $url      = 'http://47.115.7.168/curl.php?url=' . urlencode($url);
        result(['url' => $url]);
    }

    /**
     * 获取订单列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function query_changan_order()
    {
        error('当前接口已下线');
        $params = $this->params;
        $result = $params['result'];
        $result = json_decode($result, true);
        //       $result = curl()->get($url)->setMaxRetries(2)->setRetryDecider(function ($result) {
        //           return (is_array($result) && isset($result['code']) && $result['code'] === '0') === false;
        //       })->get_body();

        if ($result['code'] == '1003') {
            $msg = '未查询到购车订单,请确认在"长安fan"APP中能查询到订单再来!';
            error($msg);
        }
        if ($result['code'] !== '0') {
            $msg = '订单查询错误,token可能已失效,请重新复制链接!';
            error($msg);
        }
        if (empty($result['data'])) {
            error('没有查询到购车订单!');
        }
        $status_info = '';
        foreach ($result['data'] as $data) {
            $db          = new ChanganOrder();
            $order_no    = $data['orderNo'];
            $insert_data = ['order_no' => $order_no, 'data' => $data];
            $db->save($insert_data);
            $orderidCreateDate = $data['orderidCreateDate']; //下单时间
            $orderidCreateDate = date('Y-m-d', strtotime($orderidCreateDate));
            $vinNo             = $data['vinNo'] ?? '';
            $promiseDeliyDate  = $data['promiseDeliyDate']; //预计交期
            if (tools()::is_time($promiseDeliyDate)) {
                $promiseDeliyDate = date('Y-m-d', strtotime($promiseDeliyDate));
            }
            $expDlryDate = $data['expDlryDate']; //要车日期
            if (tools()::is_time($expDlryDate)) {
                $expDlryDate = date('Y-m-d H:i:s', strtotime($expDlryDate));
                $expDlryDate = str_replace(' 00:00:00', '', $expDlryDate);
            }
            $statusNode     = $data['nowNode']['statusNode'];
            $nodeDate       = $data['nowNode']['nodeDate'];
            $high_date      = '2021-10-07';
            $last_wait_day  = floor((time() - strtotime($nodeDate)) / 86400) + 1;
            $order_wait_day = floor((time() - strtotime($orderidCreateDate)) / 86400) + 1;
            $low_money_day  = floor((strtotime($high_date) - strtotime($orderidCreateDate)) / 86400) + 1;

            $high_money_day = floor((time() - strtotime($high_date)) / 86400);
            $low_money      = $low_money_day * 50;
            $high_money     = $high_money_day * 120;

            $status_info .= '订单状态: 【' . $statusNode . "】(第" . $last_wait_day . "天)<br/>";
            $status_info .= "下单日期: " . $orderidCreateDate . " (第" . $order_wait_day . "天)<br/>";
            $status_info .= "预估补贴: " . $low_money_day . '*50+' . $high_money_day . '*120=' . ($low_money + $high_money) . "元<br/>";

            $status_info .= "要车日期: " . $expDlryDate . "<br/>";
            $status_info .= "预计交期: " . $promiseDeliyDate . "<br/>";
            $status_info .= "车架号: " . $vinNo . '<br/>';

            $dcsSalesOrderNo = $data['dcsSalesOrderNo']; //物流单号
            $erpSalesOrderNo = $data['erpSalesOrderNo']; //ERP单号
            if (!empty($dcsSalesOrderNo)) {
                $status_info .= '物流单号: ' . $dcsSalesOrderNo . "<br/>";
            }
            if (!empty($erpSalesOrderNo)) {
                $status_info .= 'ERP单号: ' . $erpSalesOrderNo . "<br/>";
            }
            $vin_suffix = (int)substr($vinNo, -6); //只关心车架号稍大于谢永发的 260000
            if ($vinNo && in_array($statusNode, ['配送中', '在途', '已到店', '等待用户提车'])) {
                $cache_key = 'changan:' . $vinNo;
                $cache     = cache($cache_key);
                if (!$cache) {
                    wr_log($status_info);
                    cache($cache_key, $status_info, 3600 * 12);
                }
            }
        }
        $status_info = rtrim($status_info, '<br/>');
        wr_log($status_info);
        success($status_info);
    }

    public function payroll()
    {
        $params    = $this->params;
        $file_path = $params['file_path'];
        if (empty($file_path)) {
            error('请上传文件!');
        }
        $year               = $params['year'];
        $output_column_name = $params['output_column_name'];
        $file               = new Excel();
        $absolute_file_path = tools()::get_absolute_path(tools()::web_to_path($file_path));
        $excel_data         = $file->load($absolute_file_path)->excelToArray();
        if (empty($excel_data)) {
            error('没有要提取的数据');
        }
        $file              = new Excel();
        $data              = [];
        $out_merchant_list = [];
        foreach ($excel_data as $payroll) {
        }
        $out_merchant_list = array_unique($out_merchant_list);
        foreach ($out_merchant_list as $merchant_id) {
            $data[] = ['merchant_id' => $merchant_id];
        }
        success('处理成功');
    }

    public function excel()
    {
        $params    = $this->params;
        $file_path = $params['file_path'];
        if (empty($file_path)) {
            error('请上传文件!');
        }
        $input_column_name  = $params['input_column_name'];
        $output_column_name = $params['output_column_name'];
        $file               = new Excel();
        $allowField         = ['merchant_id' => $input_column_name];
        $absolute_file_path = tools()::get_absolute_path(tools()::web_to_path($file_path));
        $merchant_list      = $file->load($absolute_file_path)->excelToArray($allowField);
        if (empty($merchant_list)) {
            error('没有要提取的数据');
        }
        $file              = new Excel();
        $data              = [];
        $out_merchant_list = [];
        foreach ($merchant_list as $merchant) {
            $merchant_id       = tools()::sbc2_dbc($merchant['merchant_id']);
            $merchant_id_array = explode(';', $merchant_id);
            foreach ($merchant_id_array as $merchant_id) {
                //这里可以做一些校验 比如只能数字
                if (!empty($merchant_id)) {
                    $out_merchant_list[] = $merchant_id;
                }
            }
        }
        $out_merchant_list = array_unique($out_merchant_list);
        foreach ($out_merchant_list as $merchant_id) {
            $data[] = ['merchant_id' => $merchant_id];
        }
        $header = ['merchant_id' => $output_column_name];
        $file->arrayToExcel($header, $data);
    }

    public function delete_yky_member()
    {
        $params              = $this->params;
        $yikayi_account      = $params['account'];
        $yikayi_user_account = $params['user_account'];
        $yikayi_password     = $params['password'];
        $yikayi_config       = ['account' => $yikayi_account, 'user_account' => $yikayi_user_account, 'password' => $yikayi_password];
        $recycle             = Yikayi::Recycle($yikayi_config);
        $result              = $recycle->DelData(['dataTypeId' => 1]);
        if ($result['success'] === false) {
            error($result['message'] ?? '注销失败');
        }
        success($result['message'] ?? '删除成功');
    }
}
