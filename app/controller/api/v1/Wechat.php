<?php
declare (strict_types=1);

namespace app\controller\api\v1;

use app\BaseController;
use app\model\YkyWechatOfficialAccounts;
use app\middleware\YkyManagement;
use Exception;
use think\facade\Db;

class Wechat extends BaseController
{
    protected array $middleware = [
        YkyManagement::class => ['except' => []],
    ];

    /**
     * 公众号列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        $db     = new YkyWechatOfficialAccounts();
        $params = $this->params;
        $map    = [];
        if (!empty($params['value'])) {
            $map[] = ['appid|nick_name|admin_user_name|principal_name|principal_short_name', 'LIKE', '%' . trim($params['value']) . '%'];
        }
        $field = [
            'woa.*',
        ];
        $order = ['status' => 'DESC', 'woa.last_renew_time' => 'ASC'];
        $list  = $db->alias('woa')->where($map)->field($field)->order($order);
        result($this->_paginate($list));
    }

    /**
     * 编辑公众号
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function edit()
    {
        $params = $this->params;
        $db     = new YkyWechatOfficialAccounts();
        $map    = [
            ['id', '=', $params['id']]
        ];
        $db::update($this->params, $map);
        success('修改成功');
    }

    /**
     * 年审公众号
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function renew()
    {
        $params      = $this->params;
        $db          = new YkyWechatOfficialAccounts();
        $map         = [
            ['id', '=', $params['id']]
        ];
        $update_data = [
            'last_renew_time' => Db::raw('DATE_ADD(last_renew_time, INTERVAL 1 YEAR)')
        ];
        $db::update($update_data, $map);
        success('年审成功');
    }
}