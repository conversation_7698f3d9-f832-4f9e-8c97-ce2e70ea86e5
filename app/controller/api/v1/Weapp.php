<?php
declare (strict_types=1);

namespace app\controller\api\v1;

use app\BaseController;
use Exception;

class Weapp extends BaseController
{
    //   protected $middleware = [
    //       YkyManagement::class => ['except' => []],
    //   ];
    /**
     * 扫码登陆小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function login()
    {
        if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
            error('当前服务器繁忙,请联系管理员');
        }
        $login_url = '/login';
        $data      = [
            'qr-format'     => 'base64',
            'result-output' => ('D:/login_result.json'),
        ];
        $result    = $this->request($login_url, $data);
        if ($result === false) {
            error('系统繁忙');
        }
        result($result);
    }

    protected function request($url_path, $data = [])
    {
        $file_path = 'C:\Users\<USER>\AppData\Local\微信开发者工具\User Data\Default\.ide';
        if (!file_exists($file_path)) {
            throw new Exception('项目文件异常!');
        }
        $port     = (int)file_get_contents($file_path);//将整个文件内容读入到一个字符串中
        $base_url = 'http://127.0.0.1:' . $port . '/v2' . $url_path . '?' . $this->build_data($data);
        $result   = curl()->get($base_url . $url_path)->get_body();
        return tools()::is_json($result) ? json_decode($result, true) : $result;
    }

    protected function build_data($data)
    {
        $str = '';
        foreach ($data as $key => $val) {
            $str .= $key . '=' . $val . '&';
        }
        return $str;
    }

    /**
     * 扫码预览小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function preview()
    {
        $params = $this->params;
        if (empty($params['appid'])) {
            error('缺少appid参数');
        }
        $demo_config = 'D:\app\demo\project.config_demo.json';
        $config      = 'D:\app\demo\project.config.json';
        if (!file_exists($demo_config) || !file_exists($config)) {
            error('项目文件异常');
        }
        $result = file_get_contents($demo_config);//读取标准化配置
        $new    = str_replace('{{APPID}}', $params['appid'], $result);
        file_put_contents($config, $new);//写入到配置文件
        $login_url = '/preview';
        $data      = [
            'qr-format' => 'base64',
            // 'project'   => 'D:/Git/zhibo_wechat_app',
            'project'   => 'D:/app/demo',
            //'qr-output' => urlencode('D:/preview_result.json'),
        ];
        $result    = $this->request($login_url, $data);
        if (is_string($result)) {
            result(['qrcode' => 'data:image/png;base64,' . $result]);
        } else {
            if (isset($result['message']) && strpos($result['message'], '需要重新登录') !== false) {
                error('请先扫码登陆后再预览小程序');
            } else {
                error($result['message'] ?? '系统繁忙');
            }
        }
    }

    /**
     * 扫码上传小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function upload()
    {
        $login_url = '/upload';
        $data      = [
            // 'project'     => urlencode('D:/Git/zhibo_wechat_app'),
            'project'     => ('D:/app/demo'),
            'version'     => '1.0.3',
            'desc'        => 'auto',
            'info-output' => urlencode('D:/upload_result.json'),
        ];
        $result    = $this->request($login_url, $data);
        if ($result === false) {
            error('上传失败');
        }
        result($result);
    }
}