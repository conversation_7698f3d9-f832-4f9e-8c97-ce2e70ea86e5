<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\model\JuheRechargeOrder;
use app\middleware\YkyMember;

class PointExchange extends BasicMember
{
    protected array $middleware = [
        YkyMember::class,
    ];

    public function oil_card()
    {
        $db         = new JuheRechargeOrder();
        $member     = $this->request->__get('_member');
        $map        = [['mobile', '=', $member['Mobile']]];
        $oil_cardid = $db->where($map)->value('game_userid');
        $this->assign([
            'oil_cardid' => $oil_cardid ?: '',
            'title'      => '兑换油卡',
        ]);
        return $this->fetch();
    }
}