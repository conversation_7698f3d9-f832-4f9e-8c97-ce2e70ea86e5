<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\middleware\YkyMember;

class Pay extends BasicMember
{
    protected array $middleware = [
        YkyMember::class => ['only' => ['barter']]
    ];

    public function demo()
    {
        // 创建SDK实例
        $script  = weixin($this->get_appid())::WeChatScript();
        $request = request();
        $url     = $request->url(true);
        // 获取JsApi使用签名，通常这里只需要传 $url参数
        $options = $script->getJsSign($url);
        // 处理执行结果
        if ($options === false) {
            error($script->errMsg);
        }
        $this->assign([
            'options' => $options,
            'title'   => 'demo',
        ]);
        return $this->fetch();
    }

    public function barter()
    {
        $this->assign([
            'title' => '自助买单'
        ]);
        return $this->fetch();
    }

    public function index()
    {
        $this->assign([
            'title' => '在线支付'
        ]);
        return $this->fetch();
    }

    public function pay_success()
    {
        $this->assign([
            'title' => '支付成功'
        ]);
        return $this->fetch();
    }
}