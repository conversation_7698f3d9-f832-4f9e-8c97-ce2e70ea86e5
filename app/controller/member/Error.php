<?php

namespace app\controller\member;

use app\BaseController;
use Exception;

class Error extends BaseController
{
    /**
     * 魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return string
     * @throws Exception
     */
    public function __call(string $name, array $arguments)
    {
        return $this->fetch(strtolower($this->request->action()));
    }
}