<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\middleware\WeappOpenidMappingWechatOpenid;

class Tools extends BasicMember
{
    protected array $middleware = [
        WeappOpenidMappingWechatOpenid::class => ['only' => ['kuaidi', 'auth']],
    ];

    public function info()
    {
        success('您的会员编号:' . request()->__get('_member_id') . ',请截图保存~');
    }
}