<?php

namespace app\controller\member;

use app\BaseController;
use Exception;

class BasicMember extends BaseController
{
    /**
     * 魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return string
     * @throws Exception
     */
    public function __call($name, $arguments)
    {
        return $this->fetch(strtolower($this->request->action()));
    }

    /**
     * [__construct description]
     * <AUTHOR>
     * @dateTime 2018-05-21 20:47:02
     */
    protected function initialize()
    {
    }
}
