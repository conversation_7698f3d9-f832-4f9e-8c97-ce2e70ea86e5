<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\model\Member;

class MoneyExchange extends BasicMember
{
    public function index()
    {
        $db_member = new Member();
        $map       = [
            'bid'    => $this->get_bid(),
            'openid' => $this->get_openid(),
        ];
        $member    = $db_member->get_member_info($map);
        $this->assign([
            'title'  => '兑换油卡',
            'member' => $member,
        ]);
        return $this->fetch();
    }
}