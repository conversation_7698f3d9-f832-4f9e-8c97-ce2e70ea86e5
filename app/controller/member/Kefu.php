<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\middleware\MemberWechatUserInfo;
use app\model\Business;
use app\model\WechatUserInfo;
use OpenApi\DuoKeFu;

class Kefu extends BasicMember
{
    protected array $middleware = [
        MemberWechatUserInfo::class,
    ];

    public function redirect()
    {
        $super_admin_business_guid = config('app.super_admin_business_guid');
        $request                   = request();
        $member_id                 = $request->__get('_member_id');
        $sid                       = $request->__get('_sid');
        $url                       = (string)url('member/kefu/index', ['bid' => $super_admin_business_guid, 'uid' => $this->get_openid(), 'member_id_and_sid' => $member_id . '-' . $sid], false, true);
//        https://www.yikayi.net/member/kefu/index?bid=26810245-d97e-81b6-c1cf-0215fd8f347c
        redirect($url);
    }

    public function index()
    {
        $openid = $this->get_openid();
        $appid  = $this->get_appid();
        $params = $this->params;
        $bid    = $this->get_bid();
        $u      = $params['u'] ?? '';
        if (empty($u)) {
//            $seller_code   = 'f37eb109-70ac-9e6e-a089-ecefebd3d396';
//            $kefu_code     = 'f3c757e1-451c-2cba-13b9-fe550835d477';
//            $seller_name   = 'tihuo';
            $db_business   = new Business();
            $business_info = $db_business->get_business_info_by_account_or_guid($bid);

            $db_user           = new \app\model\User();
            $default_user_guid = $db_user->get_default_user_guid($bid);
            $duo_ke_fu         = new DuoKeFu();
            $data              = [
                'seller_code' => $bid,
                'seller_name' => $business_info['account'],
                'kefu_code'   => $default_user_guid
            ];
            $result            = $duo_ke_fu->auto_add_seller_and_kefu($data);
            $u                 = $bid;
        }
        $db_wechat_user_info = new WechatUserInfo();
        $user_info           = $db_wechat_user_info->get_user_info($appid, $openid);
        $member_id_and_sid   = $params['member_id_and_sid'] ?? request()->__get('_member_id');
        $uid                 = $params['uid'] ?? $this->get_openid();
        $url                 = 'https://chat.yikayi.net/index/index/kefu?u=' . $u . '&uid=' . $uid . '&name=' . $user_info['nickname'] . '(' . $member_id_and_sid . ')' . '&avatar=' . urlencode($user_info['headimgurl']);
        redirect($url);
    }

    public function index_bak()
    {
        $openid    = $this->get_openid();
        $appid     = $this->get_appid();
        $params    = $this->params;
        $u         = $params['u'];
        $cache_key = 'user_info:' . $this->get_openid();
        $user_info = cache($cache_key);
        if (!is_array($user_info)) {
            //如果缓存没有则看是否关注
            $wechat    = weixin($appid)::WeChatUser();
            $user_info = $wechat->getUserInfo($openid);
            if ($user_info['subscribe'] != 1) {
                //没有关注则强制授权
                $url = (string)url('member/kefu/index', ['u' => $u, 'scope' => 'snsapi_userinfo', 'bid' => $this->get_bid()], true, true);
                redirect($url);
            }
        }
        wr_log($user_info);
        $url = 'https://chat.yikayi.net/index/index/kefu?u=' . $u . '&uid=' . $this->get_openid() . '&name=' . $user_info['nickname'] . '&avatar=' . $user_info['headimgurl'];
        redirect($url);
    }
}
