<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/11/8
 * Time: 15:26
 */

namespace app\controller\member;

use app\model\Goods as GoodsModel;
use app\model\Member;
use app\model\MemberGroup;
use app\middleware\WechatJsSdk;
use Exception;

class Goods extends BasicMember
{
    protected array $middleware = [
        WechatJsSdk::class => ['only' => ['detail']],
    ];

    /**
     *商品
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $params = $this->params;
        if ($this->get_config()['mall_status'] == 0) {
            error('暂未上线,敬请期待');
        }
        $guid       = $params['guid'];
        $db         = new GoodsModel();
        $map        = [
            ['guid', '=', $guid],
            ['bid', '=', $this->get_bid()]
        ];
        $goods_info = $db->where($map)->find();
        if (!$goods_info) {
            error('商品不存在');
        }
        $goods_category_guid = $goods_info['category_guid'];
        $member              = new Member();
        $map                 = [['bid', '=', $this->get_bid()], ['openid', '=', $this->get_openid()]];
        $member_info         = $member->where($map)->find();
        if (!$member_info) {
            error('抱歉,您无权限查看此商品');
        }
        $member_group_info = $member_info->profile;
        $level             = $member_group_info['level'];
        $member_group      = new MemberGroup();
        $join              = [
            ['goods_category_discount mcd', "mg.guid = mcd.member_group_guid  AND mcd.goods_category_guid ='$goods_category_guid'and mg.bid=mcd.bid", 'LEFT'],
        ];
        $map               = [
            ['level', '>=', $level],
            ['mg.bid', '=', $this->get_bid()]
        ];
        $list              = $member_group->alias('mg')->field(
            [
                'mg.guid',
                'mg.level',
                'IFNULL(mcd.discount,1)' => 'discount'
            ]
        )->where($map)->join($join)->select();
        $map               = [
            ['bid', '=', $this->get_bid()],
            ['level', '>=', $level]
        ];
        //$list = $member_group->where($map)->column('discount', 'level');
        $member_group_list = [];
        foreach ($list as $key => $val) {
            $member_group_list[(string)$val['level']] = $val['discount'];
        }
        $this->assign([
            'goods_info'        => $goods_info,
            'level'             => $level,
            'member_group_list' => json_encode($member_group_list),
        ]);
        return $this->fetch();
    }
}