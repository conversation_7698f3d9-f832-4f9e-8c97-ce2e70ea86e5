<?php

namespace app\controller\push;

use app\model\WebsocketUser;
use app\common\tools\Visitor;
use Exception;
use think\facade\Log;
use app\service\WorkerServer;
use Workerman\Lib\Timer;

class WorkerSocket extends WorkerServer
{
    public $uidConnections = [];
    protected $socket = 'websocket://0.0.0.0:2010';
    protected $protocol = 'websocket';
    protected $host = '0.0.0.0';
    protected $port = '2010';
    protected $context = [];
    protected $option = [
        'count'      => 1,
        'name'       => 'websocket',
        'pidFile'    => '',
        'stdoutFile' => '',
        'logFile'    => '',
    ];

    public function __construct()
    {
        $runtime_path = app()->getRuntimePath();
        // 避免pid混乱
        $this->option['stdoutFile'] = $runtime_path . $this->port . 'stdoutFile.log';
        $this->option['logFile']    = $runtime_path . $this->port . 'logFile.log';
        $this->option['pidFile']    = $runtime_path . $this->port . 'pidfile.pid';
        parent::__construct();
    }

    public function onWorkerStart($worker)
    {
        echo __FUNCTION__ . PHP_EOL;
        // 开启一个内部端口，方便内部系统推送数据，Text协议格式 文本+换行符
        $inner_text_worker            = new \Workerman\Worker('text://0.0.0.0:2011');
        $inner_text_worker->onMessage = function ($connection, $buffer) {
            // 使用uid判断需要向哪个页面推送数据
            // $data数组格式，里面有uid，表示向那个uid的页面推送数据
            $data = json_decode($buffer, true);
            if (empty($data)) {
                return $connection->send('FAIL');
            }
            if (isset($data['client_id'])) {
                $uid = $data['client_id'];
                // 通过workerman，向uid的页面推送数据
                $res = $this->sendMessageByUid($uid, $data);
                if ($res === false) {
                    $this->update_uid(['status' => 0], $uid);
                }
            } else {
                $res = $this->broadCast($data);
            }
            return $connection->send($res ? 'SUCCESS' : 'FAIL');
        };
        $inner_text_worker->listen();
        Timer::add(15, function () use ($worker) {
            foreach ($worker->connections as $connection) {
                $this->sendMessageByConnection($connection, ['type' => 'ping']);
            }
        });
    }

    protected function sendMessageByUid($uid, array $message)
    {
        if (isset($this->uidConnections[$uid])) {
            foreach ($this->uidConnections[$uid] as $connection) {
                $this->sendMessageByConnection($connection, $message);
            }
            return true;
        }
        return false;
    }

    protected function sendMessageByConnection($connection, array $message)
    {
        $message['time']          = $message['time'] ?? microsecond();
        $message['worker_id']     = $this->worker->id;
        $message['connection_id'] = $connection->id;
        $message                  = (string)json_encode($message);
        $connection->send($message);
        return true;
    }

    /**
     *更新客户端连接记录
     * @access public
     * @param array $data 更新数据
     * @param string $uid 客户端ID
     * @return bool
     * @throws Exception
     */
    protected function update_uid(array $data, string $uid)
    {
        try {
            $db_websocket_user = new WebsocketUser();
            $channel           = 'socket';
            $map               = [
                ['uid', '=', $uid],
                ['channel', '=', $channel],
            ];
            $user_id           = $db_websocket_user->where($map)->value('id');
            if (empty($user_id)) {
                return false;
            }
            //存在做更新
            $map[] = ['id', '=', $user_id];
            $db_websocket_user::update($data, $map);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    // 客户端连接上来时，即完成TCP三次握手后的回调

    protected function broadCast(array $message)
    {
        foreach ($this->uidConnections as $connections) {
            foreach ($connections as $connection) {
                $this->sendMessageByConnection($connection, $message);
            }
        }
        return true;
    }

    public function onMessage($connection, $data)
    {
        echo __FUNCTION__ . PHP_EOL;
        echo ($data) . PHP_EOL;
        $data                  = json_decode($data, true);
        $file_data             = $data;
        $file_data['log_time'] = date('Y-m-d H:i:s');
        $type                  = $data['type'] ?? 'send';
        $default_return_type   = 'push';
        try {
            //file_put_contents(date('Y-m-d') . '.txt', json_encode($file_data) . PHP_EOL, FILE_APPEND);
            Log::record(json_encode($file_data));
        } catch (Exception $e) {
        }
        switch ($type) {
            case 'login':
                if (!isset($connection->uid)) {
                    // 没验证的话把第一个包当做uid（这里为了方便演示，没做真正的验证）
                    $connection->uid = $data['client_id'] ?? time();
                    //todo 后续需要考虑多worker的时候 $connection->id 可能会重复 考虑增加 $this->worker->id
                    $this->uidConnections[$connection->uid][$connection->id] = $connection;
                    //记录数据库
                    $uid     = $connection->uid;
                    $channel = 'socket';
//                    if (strpos($uid, 'web') !== 0) {
//                        return;
//                    }
                    $date_time = date('Y-m-d H:i:s');
                    try {
                        $db_websocket_user = new WebsocketUser();
                        $map               = [['uid', '=', $uid], ['channel', '=', $channel]];
                        $user_id           = $db_websocket_user->where($map)->value('id');
                        if ($user_id) {
                            //存在做更新
                            $data  = ['status' => 1, 'last_login_time' => $date_time];
                            $map[] = ['id', '=', $user_id];
                            $db_websocket_user::update($data, $map);
                            echo $uid . "于" . $date_time . "上线了,更新成功\n";
                        } else {
                            $array = explode('_', $uid);
                            //不存在做新增
                            $data = [
                                'uid'              => $uid,
                                'channel'          => $channel,
                                'platform'         => $array[0] ?? '',
                                'type'             => $array[1] ?? '',
                                'bid'              => $array[2] ?? '',
                                'status'           => 1,
                                'first_login_time' => $date_time,
                                'last_login_time'  => $date_time,
                            ];
                            switch ($data['type']) {
                                case 'user':
                                    $data['user_guid'] = $array[3] ?? '';
                                    break;
                                case 'member':
                                    $data['member_guid'] = $array[3] ?? '';
                                    break;
                                default:
                            }
                            $db_websocket_user->save($data);
                            echo $uid . "于" . $date_time . "上线了,插入成功\n";
                        }
                    } catch (Exception $e) {
                    }
                    return;
                }
                break;
            case 'ping': //收到心跳请求,回应pong,表示服务器当前正常状态
                $this->sendMessageByConnection($connection, ['type' => 'pong']); //sendMessageByUid 会发给所有client_id相同的链接
                break;
            case 'pong': //收到心跳回应,表示当前客户端还在线,暂时不做处理
                //$this->sendMessageByConnection($connection, ['type' => 'msg', 'msg' => 'success']); //sendMessageByUid 会发给所有client_id相同的链接
                break;
            case 'send':
                $data['type'] = $default_return_type;
                $data['ip']   = $connection->clientIp;
                $this->sendMessageByConnection($connection, $data);
                break;
            default:
                $this->sendMessageByConnection($connection, ['type' => $default_return_type, 'msg' => 'not support type: ' . $type]);
        }
    }

    public function onWorkerReload($worker)
    {
        echo __FUNCTION__ . PHP_EOL;
    }

    public function onConnect($connection)
    {
        //$connection->getRemoteIp(); //获取到的是 127.0.0.1
        echo __FUNCTION__ . PHP_EOL;
    }

    public function onBufferFull($connection)
    {
        echo __FUNCTION__ . PHP_EOL;
    }

    public function onBufferDrain($connection)
    {
        echo __FUNCTION__ . PHP_EOL;
    }

    // 群发

    public function onWebSocketConnect($connection, $http_header)
    {
        $connection->clientIp = Visitor::get_server_header('HTTP_X_REAL_IP', '0.0.0.0'); //可获取到公网IP
        echo __FUNCTION__ . PHP_EOL;
    }

    // 向客户端某一个uid推送数据

    public function onClose($connection)
    {
        if (isset($connection->uid)) {
            $uid = $connection->uid;
            // 连接断开时删除映射
            unset($this->uidConnections[$connection->uid][$connection->id]);
            //          if (strpos($uid, 'web') !== 0) {
            //              return;
            //          }
            $data = ['status' => 0, 'last_login_out_time' => date('Y-m-d H:i:s')];
            $this->update_uid($data, $uid);
        }
    }

    // 向某个连接推送数据

    public function onError($connection, $code, $msg)
    {
        echo __FUNCTION__ . PHP_EOL;
        echo "error [ $code ] $msg\n";
    }
}