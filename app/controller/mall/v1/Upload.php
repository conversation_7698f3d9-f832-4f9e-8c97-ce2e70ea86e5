<?php

namespace app\controller\mall\v1;

use Exception;

class Upload extends BasicMall
{
    /**
     * 扫码登陆小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function login()
    {
        if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
            error('您的系统尚未开通一键扫码上传功能!');
        }
        $login_url = '/login';
        $data      = [
            'qr-format' => 'base64',
            //'result-output' => ('C:/login_result.json'),
        ];
        $result    = $this->request($login_url, $data);
        if ($result === false) {
            error('系统繁忙');
        }
        result($result);
    }

    /**
     * 请求方法
     * @access protected
     * @param string $url_path 请求路径
     * @param array $data 数据包
     * @return string
     * @throws Exception
     */
    protected function request($url_path, $data = [])
    {
        $ide_file = $this->get_ide_file();
        $port     = (int)file_get_contents($ide_file);//将整个文件内容读入到一个字符串中
        $base_url = 'http://127.0.0.1:' . $port . '/v2' . $url_path . '?' . $this->build_data($data);
        $result   = curl()->get($base_url . $url_path)->get_body();
        return tools()::is_json($result) ? json_decode($result, true) : $result;
    }

    /**
     * 获取ide的端口号文件
     * @access protected
     * @return string
     * @throws Exception
     */
    protected function get_ide_file()
    {
        $user_path = 'C:\Users';
        if (!is_dir($user_path)) {
            throw new Exception('文件异常!');
        }
        $user_array = tools()::get_sub_dirs($user_path);
        foreach ($user_array as $user) {
            $file_path = 'C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\Default\\.ide';
            if (file_exists($file_path)) {
                return $file_path;
            }
        }
        throw new Exception('服务器繁忙(-1)!');
    }

    /**
     * 构造请求数据
     * @access protected
     * @param array $data 数据包
     * @return string
     */
    protected function build_data($data)
    {
        $str = '';
        foreach ($data as $key => $val) {
            $str .= $key . '=' . $val . '&';
        }
        return $str;
    }

    /**
     * 扫码预览小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function preview()
    {
        //
        //    {
        //    "api_root": "/web/index.php?_mall_id=1",
        //   "appid": "wx49310464deda8fe4",
        //   "token": "-R51q2jIZo4TIIG3Tq-2KUqcnQ0sNEIb",
        //   "version": "v1",
        //   "protocol": "https",
        //   "branch": "",
        //   "options": "{\"plugins\":[]}",
        //   "controller": "upload",
        //   "function": "preview"
        //}
        $params       = $this->params;
        $version      = $this->request->header('X_VERSION');
        $domain       = $this->request->header('X_DOMAIN');
        $project_path = 'C:\\app\\' . $version . '\\';

        //确定是否包含直播插件
        $options          = json_decode($params['options'], true);
        $plugins          = $options['plugins'];
        $app_json_string  = '';
        $live_json        = $project_path . 'app.json';
        $live_app_json    = $project_path . 'app_live.json';
        $no_live_app_json = $project_path . 'app_no_live.json';
        if (in_array('live-player-plugin', $plugins)) {
            $app_json_string = file_get_contents($live_app_json);
        } else {
            $app_json_string = file_get_contents($no_live_app_json);
        }
        //更新跳转APPID
        $jump_appid_list = $params['jump_appid_list'] ?? [];
        $new             = str_replace('{{JUMP_APPID_LIST}}', json_encode($jump_appid_list), $app_json_string);
        file_put_contents($live_json, $new);

        //更新project.config.json
        $project_config_demo = $project_path . 'project.config.demo.json';
        $project_config      = $project_path . 'project.config.json';
        if (!file_exists($project_config_demo)) {
            error('project.config.json文件异常');
        }
        $result = file_get_contents($project_config_demo);//读取标准化配置
        $new    = str_replace('{{APPID}}', $params['appid'], $result);
        file_put_contents($project_config, $new);//写入到配置文件
        //更新siteinfo.js
        $siteinfo_js_demo = $project_path . 'siteinfo.demo.js';
        $siteinfo_js      = $project_path . 'siteinfo.js';
        if (!file_exists($siteinfo_js_demo)) {
            error('siteinfo.js文件异常');
        }
        $result = file_get_contents($siteinfo_js_demo);//读取标准化配置
        $new    = str_replace('{{DOMAIN}}', $params['protocol'] . '://' . $domain, $result);
        $new    = str_replace('{{PATH}}', $params['api_root'], $new);
        file_put_contents($siteinfo_js, $new);//写入到配置文件
        //调用预览接口
        $login_url = '/preview';
        $data      = [
            'qr-format' => 'base64',
            'project'   => $project_path,
            //'qr-output' => urlencode('C:/preview_result.json'),
        ];
        $result    = $this->request($login_url, $data);
        if (is_string($result)) {
            result(['qrcode' => 'data:image/png;base64,' . $result]);
        } else {
            if (isset($result['message']) && strpos($result['message'], '需要重新登录') !== false) {
                $data = ['retry' => 1];
                result($data);
                //error('请先扫码登陆后再预览小程序');
            } else {
                error($result['message'] ?? '系统繁忙');
            }
        }
    }

    /**
     * 扫码上传小程序
     * @access  public
     * @return mixed
     * @throws Exception
     */
    public function upload()
    {
        $version      = $this->request->header('X_VERSION');
        $domain       = $this->request->header('X_DOMAIN');
        $project_path = 'C:\\app\\' . $version . '\\';
        $desc         = '优化用户体验';
        $login_url    = '/upload';
        $data         = [
            'project' => $project_path,
            'version' => $version,
            'desc'    => urlencode($desc),
            //'info-output' => urlencode('C:/upload_result.json'),
        ];
        $result       = $this->request($login_url, $data);
        if ($result === false) {
            error('上传失败');
        }
        $data = [
            'version' => $version,
            'desc'    => $desc,
            'result'  => $result
        ];
        wr_log($domain . ' 小程序上传成功', 1);
        result($data);
    }
}