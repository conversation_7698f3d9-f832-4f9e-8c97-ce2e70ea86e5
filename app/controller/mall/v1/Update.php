<?php


namespace app\controller\mall\v1;


use app\model\UpgradeNote;
use Exception;

class Update extends BasicMall
{
    /**
     * 获取更新列表
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function index()
    {
        $host_version         = $this->request->header('x-version');
        $db                   = new UpgradeNote();
        $data                 = [];
        $list                 = $db->field(['version_number' => 'version', 'content', 'src_file'])->order(['date' => 'ASC'])->select()->toArray();
        $data['host_version'] = $host_version;
        foreach ($list as $upgrade_note) {
            if (version_compare($upgrade_note['version'], $host_version) > 0) {
                $data['next_version'] = [
                    'version'  => $upgrade_note['version'],
                    'src_file' => $upgrade_note['src_file'],
                    'content'  => $upgrade_note['content']
                ];
                break;
            }
        }
        $data['list'] = $db->field(['version_number' => 'version', 'content'])->order(['date' => 'DESC'])->limit(10)->select()->toArray();
        result($data);
    }
}