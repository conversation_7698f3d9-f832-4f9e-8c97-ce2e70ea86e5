<?php

namespace app\controller\mall\v1;

class Plugin extends BasicMall
{
    public function index()
    {
        $data = [
            'list' => []
            //               [
            //                   'name'         => 'name',
            //                   'display_name' => 'display_name',
            //                   'pic_url'      => 'pic_url',
            //               ]
        ];
        result($data);
    }

    public function detail()
    {
        $data = [
            'id' => 1,
            //           'new_version' =>
            //               [
            //                   'version' => $this->params['plugin_version']
            //               ]
        ];
        result($data);
    }
}