<?php

namespace app\controller\mall\v1;

use app\BaseController;
use Exception;

class BasicMall extends BaseController
{
    /**
     * 魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return void
     * @throws Exception
     */
    public function __call($name, $arguments)
    {
        wr_log('接口不存在:' . $name, 1);
        error('接口不存在:' . $name);
    }
}