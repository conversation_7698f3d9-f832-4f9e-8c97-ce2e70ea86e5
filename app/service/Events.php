<?php

namespace app\service;

use app\model\CustomerAutoReply;
use app\model\CustomerMessage;
use app\model\CustomerQueue;
use app\model\CustomerSession;
use app\model\User;
use GatewayWorker\Lib\Gateway;

class Events
{
    /**
     * 机器人头像路径常量
     */
    const ROBOT_AVATAR = '/static/img/robot.svg';

    /**
     * 公共：推送消息到指定客户端
     */
    protected static function sendToClient($client_id, $cmd, $data = [])
    {
        \GatewayWorker\Lib\Gateway::sendToClient($client_id, json_encode([
            'cmd'  => $cmd,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 公共：推送消息到指定 UID
     */
    protected static function sendToUid($uid, $cmd, $data = [])
    {
        \GatewayWorker\Lib\Gateway::sendToUid($uid, json_encode([
            'cmd'  => $cmd,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 公共：推送消息到所有客户端
     */
    protected static function sendToAll($cmd, $data = [])
    {
        \GatewayWorker\Lib\Gateway::sendToAll(json_encode([
            'cmd'  => $cmd,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 有新客户端连接时
     * @param int|string $client_id
     */
    public static function onConnect($client_id)
    {
        debug_log("[WebSocket] 客户端连接: {$client_id}");
    }

    /**
     * 收到客户端消息时
     * @param int|string $client_id
     * @param mixed $message
     */
    public static function onMessage($client_id, $message)
    {
        debug_log("[WebSocket] 收到消息: 客户端 {$client_id}, 内容: {$message}");
        try {
            $msg = json_decode($message, true);
            if (!$msg || !isset($msg['cmd'])) {
                debug_log("[WebSocket] 非法消息格式: {$message}");
                return;
            }

            debug_log("[WebSocket] 解析消息成功 - CMD: {$msg['cmd']}, 客户端: {$client_id}");
            switch ($msg['cmd']) {
                case 'chatMessage':
                    self::handleChatMessage($client_id, $msg['data'] ?? []);
                    break;
                case 'userInit':
                    self::handleUserInit($client_id, $msg['data'] ?? []);
                    break;
                case 'kefuInit':
                    self::handleKefuInit($client_id, $msg['data'] ?? []);
                    break;
                case 'readMessage':
                    self::handleReadMessage($client_id, $msg['data'] ?? []);
                    break;
                case 'ping':
                    self::sendToClient($client_id, 'pong');
                    break;
                case 'heartbeat':
                    self::handleHeartbeat($client_id, $msg['data'] ?? []);
                    break;
                case 'transferSession':
                    self::handleTransferSession($client_id, $msg['data'] ?? []);
                    break;
                case 'respondTransfer':
                    self::handleRespondTransfer($client_id, $msg['data'] ?? []);
                    break;
                case 'rollBackMessage':
                    self::handleRollBackMessage($client_id, $msg['data'] ?? []);
                    break;
                case 'changeGroup':
                    // TODO: 实现会话转接
                    break;
                case 'closeUser':
                    self::handleCloseUser($client_id, $msg['data'] ?? []);
                    break;
                case 'acceptSession':
                    self::handleAcceptSession($client_id, $msg['data'] ?? []);
                    break;
                case 'rejectSession':
                    self::handleRejectSession($client_id, $msg['data'] ?? []);
                    break;
                case 'closeSession':
                    self::handleCloseSession($client_id, $msg['data'] ?? []);
                    break;
                case 'robotQuickReply':
                    self::handleRobotQuickReply($client_id, $msg['data'] ?? []);
                    break;
                case 'typing':
                    self::handleTyping($client_id, $msg['data'] ?? []);
                    break;
                default:
                    debug_log("[WebSocket] 未知cmd: {$msg['cmd']}");
                    break;
            }
        } catch (\Throwable $e) {
            debug_log("[WebSocket] 处理消息异常: " . $e->getMessage());
        }
    }

    /**
     * 公共：存表并推送聊天消息
     */
    protected static function sendChatMessage($msg_data, $client_id = null)
    {
        $now = date('Y-m-d H:i:s');
        $msg = [
            'guid'         => $msg_data['guid'] ?? create_guid(),
            'bid'          => $msg_data['bid'] ?? '',
            'session_guid' => $msg_data['session_guid'] ?? '',
            'from_type'    => $msg_data['from_type'] ?? 0,
            'from_guid'    => $msg_data['from_guid'] ?? '',
            'to_guid'      => $msg_data['to_guid'] ?? '',
            'msg_type'     => $msg_data['msg_type'] ?? 1,
            'content'      => $msg_data['content'] ?? '',
            'is_read'      => $msg_data['is_read'] ?? 0,
            'send_time'    => $msg_data['send_time'] ?? $now,
            'extra'        => $msg_data['extra'] ?? [],
        ];

        // 动态获取发送者信息
        $from_name   = $msg_data['from_name'] ?? '';
        $from_avatar = $msg_data['from_avatar'] ?? '';

        // 如果没有提供头像昵称，则从数据库获取
        if (empty($from_name) || empty($from_avatar)) {
            if ($msg['from_type'] == 2) { // 客服
                $user        = \app\model\User::where('guid', $msg['from_guid'])->find();
                $from_name   = $from_name ?: ($user['name'] ?? \app\model\User::DEFAULT_NAME);
                $from_avatar = $from_avatar ?: ($user['head'] ?? \app\model\User::DEFAULT_AVATAR);
            } elseif ($msg['from_type'] == 3) { // 机器人
                $from_name   = $from_name ?: '智能助手';
                $from_avatar = $from_avatar ?: self::ROBOT_AVATAR;
            } else { // 会员
                $member      = \app\model\Member::where('guid', $msg['from_guid'])->find();
                $from_name   = $from_name ?: ($member['name'] ?? \app\model\Member::DEFAULT_NAME);
                $from_avatar = $from_avatar ?: ($member['head_img'] ?? \app\model\Member::DEFAULT_AVATAR);
            }
        }

        $msgModel = new CustomerMessage();
        $msgModel->save($msg);
        $chatMessage = [
            'name'         => $from_name,
            'avatar'       => $from_avatar,
            'id'           => $msg['from_guid'],
            'time'         => $msg['send_time'],
            'content'      => $msg['content'],
            'protocol'     => 'ws',
            'chat_log_id'  => $msg['guid'],
            'session_guid' => $msg['session_guid'],
            // 添加完整的消息字段，确保与历史消息数据结构一致
            'guid'         => $msg['guid'],
            'from_guid'    => $msg['from_guid'],
            'to_guid'      => $msg['to_guid'],
            'from_type'    => $msg['from_type'],
            'from_name'    => $from_name,
            'from_avatar'  => $from_avatar,
            'msg_type'     => $msg['msg_type'],
            'is_read'      => $msg['is_read'],
            'is_recall'    => 0, // 新消息默认未撤回
            'send_time'    => $msg['send_time'],
            'extra'        => $msg['extra'],
        ];

        // 推送给接收方
        if (!empty($msg['to_guid']) && Gateway::isUidOnline($msg['to_guid'])) {
            // 根据消息类型选择不同的推送事件
            if ($msg['from_type'] == 3) { // 机器人消息
                if ($msg['msg_type'] == 10) { // 快捷回复类型
                    self::sendToUid($msg['to_guid'], 'robotMessage', array_merge($chatMessage, [
                        'title'         => $msg['extra']['message_type'] == 'robot_quick_replies' ? $msg['content'] : '',
                        'quick_replies' => $msg['extra']['quick_replies'] ?? []
                    ]));
                } else { // 普通机器人回复
                    self::sendToUid($msg['to_guid'], 'robotReply', array_merge($chatMessage, [
                        'reply_to' => $msg['extra']['reply_to'] ?? ''
                    ]));
                }
            } else { // 普通聊天消息
                self::sendToUid($msg['to_guid'], 'chatMessage', $chatMessage);
            }
        }

        // 推送 afterSend 给发送方
        if ($client_id) {
            self::sendToClient($client_id, 'afterSend', [
                'code'     => 0,
                'data'     => $msg,
                'msg'      => $msg['content'],
                'guid'     => $msg['guid'],
                'local_id' => $msg_data['local_id'] ?? null,
            ]);
        }
    }

    /**
     * 处理 chatMessage
     */
    protected static function handleChatMessage($client_id, $data)
    {
        // 参数校验可按需补充
        self::sendChatMessage($data, $client_id);
    }

    /**
     * 处理 userInit
     */
    protected static function handleUserInit($client_id, $data)
    {
        $member_guid = $data['member_guid'] ?? '';
        $bid         = $data['bid'] ?? '';
        $now         = date('Y-m-d H:i:s');
        if (!$member_guid || !$bid) {
            self::sendToClient($client_id, 'userInit', [
                'code' => 400,
                'msg'  => '参数错误',
            ]);
            return;
        }
        // 1. 检查未结束会话（status=0 或 1，优先进行中）
        $session = CustomerSession::where([
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['status', 'in', [0, 1]]
        ])->order(['status' => 'desc', 'create_time' => 'DESC'])->find();
        if ($session) {
            Gateway::bindUid($client_id, $member_guid);
            self::sendToClient($client_id, 'userInit', [
                'code'    => 0,
                'session' => $session
            ]);
            // 新增：自动发送系统欢迎消息（仅首次排队时发送，进行中不重复发）
            if ($session['status'] == 0) {
                $msg_data = [
                    'bid'          => $session['bid'],
                    'session_guid' => $session['guid'],
                    'from_type'    => 2, // 客服
                    'from_guid'    => $session['user_guid'],
                    'to_guid'      => $session['member_guid'],
                    'msg_type'     => 99, // 系统消息
                    'content'      => '接入成功，客服为您服务！',
                    'is_read'      => 0,
                ];
                self::sendChatMessage($msg_data);
            }
            return;
        }
        // 2. 检查是否已在队列
        $queue = CustomerQueue::where([
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['status', '=', 0]
        ])->find();
        if (!$queue) {
            CustomerQueue::create([
                'guid'        => create_guid(),
                'member_guid' => $member_guid,
                'bid'         => $bid,
                'queue_time'  => $now,
                'status'      => 0
            ]);
        }
        // 3. 查询在线客服列表
        $onlineKefuList = User::where([
            ['bid', '=', $bid],
            ['service_status', '=', 1]
        ])->select();

        debug_log([
            '[USER_INIT] 查询在线客服',
            'bid'               => $bid,
            'member_guid'       => $member_guid,
            'online_kefu_count' => count($onlineKefuList),
            'online_kefu_list'  => $onlineKefuList ? $onlineKefuList->toArray() : []
        ]);

        if (empty($onlineKefuList) || count($onlineKefuList) == 0) {
            // 没有客服在线
            debug_log('[USER_INIT] 没有客服在线，返回code:201');
            Gateway::bindUid($client_id, $member_guid);

            // 检查是否应该触发自动回复（检查是否有启用的自动回复项目）
            $autoReplyItems         = CustomerAutoReply::getItemsByBid($bid, 1, 1);
            $shouldTriggerAutoReply = !empty($autoReplyItems);

            $response = [
                'code'               => 201,
                'msg'                => '暂无客服在线，请稍后再来',
                'auto_reply_enabled' => $shouldTriggerAutoReply
            ];

            self::sendToClient($client_id, 'userInit', $response);

            // 如果启用了自动回复，发送机器人欢迎消息
            if ($shouldTriggerAutoReply) {
                self::sendRobotWelcomeMessage($client_id, $bid, $member_guid, 1);
            }

            return;
        }

        // 4. 查找有空闲容量的客服
        $availableKefu    = null;
        $kefuCapacityInfo = [];

        foreach ($onlineKefuList as $kefu) {
            // 查询该客服当前接待的会话数
            $currentSessionCount = CustomerSession::where([
                ['user_guid', '=', $kefu['guid']],
                ['status', '=', 1] // 进行中的会话
            ])->count();

            // 检查是否有空闲容量
            $maxServiceNum = $kefu['max_service_num'] ?? 10; // 默认10个
            $hasCapacity   = $currentSessionCount < $maxServiceNum;

            $kefuCapacityInfo[] = [
                'name'             => $kefu['name'],
                'guid'             => $kefu['guid'],
                'current_sessions' => $currentSessionCount,
                'max_service_num'  => $maxServiceNum,
                'has_capacity'     => $hasCapacity
            ];

            if ($hasCapacity) {
                $availableKefu = $kefu;
                break;
            }
        }

        debug_log([
            '[USER_INIT] 客服容量检查',
            'kefu_capacity_info' => $kefuCapacityInfo,
            'available_kefu'     => $availableKefu ? $availableKefu['name'] : 'none'
        ]);

        if ($availableKefu) {
            // 5. 分配客服，生成会话
            $session = CustomerSession::create([
                'guid'        => create_guid(),
                'bid'         => $bid,
                'member_guid' => $member_guid,
                'user_guid'   => $availableKefu['guid'],
                'status'      => 1, // 修复：访客分配客服时直接设为进行中状态
                'start_time'  => $now
            ]);
            CustomerQueue::where([
                ['bid', '=', $bid],
                ['member_guid', '=', $member_guid],
                ['status', '=', 0]
            ])->update(['status' => 1]);
            Gateway::bindUid($client_id, $member_guid);
            self::sendToClient($client_id, 'userInit', [
                'code'    => 0,
                'session' => $session
            ]);
            Gateway::sendToUid($availableKefu['guid'], json_encode([
                'cmd'  => 'newSession',
                'data' => [
                    'session' => $session
                ]
            ], JSON_UNESCAPED_UNICODE));
        } else {
            // 有客服在线但都忙
            debug_log('[USER_INIT] 有客服在线但都忙，返回code:202');
            Gateway::bindUid($client_id, $member_guid);

            // 检查是否应该触发自动回复（检查是否有启用的自动回复项目）
            $autoReplyItems         = CustomerAutoReply::getItemsByBid($bid, 2, 1);
            $shouldTriggerAutoReply = !empty($autoReplyItems);

            $response = [
                'code'               => 202,
                'msg'                => '客服全忙，排队中请等待',
                'auto_reply_enabled' => $shouldTriggerAutoReply
            ];

            self::sendToClient($client_id, 'userInit', $response);

            // 如果启用了自动回复，发送机器人欢迎消息
            if ($shouldTriggerAutoReply) {
                self::sendRobotWelcomeMessage($client_id, $bid, $member_guid, 2);
            }
        }
    }

    /**
     * 客服初始化 kefuInit
     */
    protected static function handleKefuInit($client_id, $data)
    {
        $kefu_guid = $data['kefu_guid'] ?? '';
        $bid       = $data['bid'] ?? '';
        $now       = date('Y-m-d H:i:s');
        if (!$kefu_guid || !$bid) {
            self::sendToClient($client_id, 'kefuInit', [
                'data_msg' => $data,
                'code'     => 400,
                'msg'      => '参数错误',
            ]);
            return;
        }
        // 1. 设置客服在线
        $map       = [
            ['guid', '=', $kefu_guid],
            ['bid', '=', $bid]
        ];
        $updateRes = User::where($map)->update([
            'service_status'  => 1,
            'last_login_time' => $now,
            'update_time'     => $now
        ]);
        debug_log([
            '[KEFU_INIT] 客服上线状态更新',
            '条件'      => $map,
            '结果'      => $updateRes,
            'kefu_guid' => $kefu_guid,
            'bid'       => $bid,
            'client_id' => $client_id
        ]);

        // 绑定客服UID到WebSocket连接
        debug_log("[KEFU_INIT] 开始绑定客服UID: {$kefu_guid} 到客户端: {$client_id}");
        Gateway::bindUid($client_id, $kefu_guid);
        debug_log("[KEFU_INIT] 客服UID绑定完成");

        // 缓存客户端ID和UID的映射关系，用于断开连接时识别
        $cache_key = "ws_client_uid_{$client_id}";
        cache($cache_key, $kefu_guid, 3600); // 缓存1小时
        debug_log("[KEFU_INIT] 已缓存客户端UID映射: {$cache_key} => {$kefu_guid}");

        // 广播客服上线状态
        $user = User::where($map)->find();
        if ($user) {
            debug_log("[KEFU_INIT] 开始广播客服上线状态");
            self::broadcastKefuStatusChange($kefu_guid, 1, $user['name']);
            debug_log("[KEFU_INIT] 广播客服上线状态完成");
        }
        // 2. 遍历队列，尝试分配
        $queueList = CustomerQueue::where([
            ['bid', '=', $bid],
            ['status', '=', 0]
        ])->order('queue_time', 'asc')->select();
        foreach ($queueList as $queue) {
            // 检查该访客是否已分配
            $session = CustomerSession::where([
                ['bid', '=', $bid],
                ['member_guid', '=', $queue['member_guid']],
                ['status', '=', 0]
            ])->find();
            if ($session) continue;
            // 分配会话
            $session = CustomerSession::create([
                'guid'        => create_guid(),
                'bid'         => $bid,
                'member_guid' => $queue['member_guid'],
                'user_guid'   => $kefu_guid,
                'status'      => 1, // 修复：客服自动接入时直接设为进行中状态
                'start_time'  => $now
            ]);
            CustomerQueue::where('id', $queue['id'])->update(['status' => 1]);
            Gateway::sendToUid($queue['member_guid'], json_encode([
                'cmd'  => 'userInit',
                'data' => [
                    'code'    => 0,
                    'session' => $session,
                    // 兼容前端所需字段
                    // 'kefu_code'   => $kefu_guid,
                    // 'kefu_name'   => $session['kefu_name'] ?? '',
                    // 'kefu_avatar' => $session['kefu_avatar'] ?? '',
                    // 'session_guid' => $session['guid'],
                ]
            ], JSON_UNESCAPED_UNICODE));
            // 新增：自动发送欢迎消息
            $welcome  = '接入成功，客服为您服务！';
            $msg_data = [
                'bid'          => $bid,
                'session_guid' => $session['guid'],
                'from_type'    => 2, // 客服
                'from_guid'    => $kefu_guid,
                'to_guid'      => $queue['member_guid'],
                'msg_type'     => 1, // 文本
                'content'      => $welcome,
                'is_read'      => 0,
                'from_name'    => $session['kefu_name'] ?? '',
                'from_avatar'  => $session['kefu_avatar'] ?? '',
            ];
            self::sendChatMessage($msg_data);
            Gateway::sendToUid($kefu_guid, json_encode([
                'cmd'  => 'newSession',
                'data' => [
                    'session' => $session
                ]
            ], JSON_UNESCAPED_UNICODE));
            break; // 这里只分配一个
        }
        self::sendToClient($client_id, 'kefuInit', [
            'code' => 0,
            'msg'  => '客服初始化成功'
        ]);
    }

    /**
     * 处理消息已读回执
     */
    protected static function handleReadMessage($client_id, $data)
    {
        // $data: [ 'session_guid' => '', 'from_guid' => '', 'to_guid' => '' ]
        $session_guid = $data['session_guid'] ?? '';
        $from_guid    = $data['from_guid'] ?? '';  // 读消息的人
        $to_guid      = $data['to_guid'] ?? '';    // 消息发送方

        if (!$session_guid || !$from_guid || !$to_guid) {
            debug_log('[readMessage] 参数不全', $data);
            return;
        }

        try {
            $msgModel = new CustomerMessage();
            // 查找指定会话中，发送方发给读消息人的未读消息
            $map = [
                ['session_guid', '=', $session_guid],
                ['from_guid', '=', $to_guid],      // 消息发送方
                ['to_guid', '=', $from_guid],      // 消息接收方（读消息的人）
                ['is_read', '=', 0],
            ];

            // 查找所有未读消息guid
            $msg_ids = $msgModel->where($map)->column('guid');

            // 批量更新为已读
            if ($msg_ids) {
                $msgModel->where('guid', 'in', $msg_ids)->update(['is_read' => 1]);
                debug_log(['readMessage成功', '会话' => $session_guid, '标记已读消息数' => count($msg_ids)]);
            }
        } catch (\Throwable $e) {
            debug_log(['readMessage异常', $e->getMessage(), $data]);
            return;
        }

        // 推送回执给消息发送方（即使没有未读消息也要发送回执）
        if (!empty($to_guid)) {
            self::sendToUid($to_guid, 'readMessage', [
                'session_guid' => $session_guid,
                'msg_ids'      => $msg_ids ?? [], // 确保总是有数组，即使为空
                'reader_guid'  => $from_guid,
            ]);
            debug_log(['已读回执已发送', '接收方' => $to_guid, '会话' => $session_guid, '消息数' => count($msg_ids ?? [])]);
        }
    }

    /**
     * 处理消息撤回
     */
    protected static function handleRollBackMessage($client_id, $data)
    {
        // 参数校验
        $guid      = $data['guid'] ?? '';
        $from_guid = $data['from_guid'] ?? '';
        if (!$guid || !$from_guid) {
            self::sendToClient($client_id, 'rollBackMessageReply', [
                'code' => 1,
                'msg'  => '参数错误'
            ]);
            return;
        }
        // 查找消息
        $msgModel = new \app\model\CustomerMessage();
        $msg      = $msgModel->where(['guid' => $guid])->find();
        if (!$msg) {
            self::sendToClient($client_id, 'rollBackMessageReply', [
                'code' => 2,
                'msg'  => '消息不存在'
            ]);
            return;
        }
        // 只能撤回自己发送的消息
        if ($msg['from_guid'] !== $from_guid) {
            self::sendToClient($client_id, 'rollBackMessageReply', [
                'code' => 3,
                'msg'  => '无权撤回该消息'
            ]);
            return;
        }
        // 已撤回不重复操作
        if (isset($msg['is_recall']) && $msg['is_recall'] == 1) {
            self::sendToClient($client_id, 'rollBackMessageReply', [
                'code' => 4,
                'msg'  => '消息已撤回'
            ]);
            return;
        }
        // 更新为撤回
        $msg->is_recall = 1;
        $msg->save();

        // 首先向发起者发送撤回成功回执
        self::sendToClient($client_id, 'rollBackMessageReply', [
            'code' => 0,
            'msg'  => '撤回成功',
            'data' => [
                'guid'         => $guid,
                'from_guid'    => $from_guid,
                'session_guid' => $msg['session_guid'] ?? '',
            ]
        ]);

        // 推送撤回事件给会话双方
        debug_log(['撤回推送给发送者', $msg['from_guid']]);
        self::sendToUid($msg['from_guid'], 'rollBackMessage', [
            'guid'         => $guid,
            'from_guid'    => $from_guid,
            'session_guid' => $msg['session_guid'] ?? '',
        ]);
        if (!empty($msg['to_guid'])) {
            debug_log(['撤回推送给接收者', $msg['to_guid']]);
            self::sendToUid($msg['to_guid'], 'rollBackMessage', [
                'guid'         => $guid,
                'from_guid'    => $from_guid,
                'session_guid' => $msg['session_guid'] ?? '',
            ]);
        }
    }

    /**
     * 处理会话接入（客服接入待接入会话）
     */
    protected static function handleAcceptSession($client_id, $data)
    {
        $session_guid = $data['session_guid'] ?? '';
        $kefu_guid    = $data['kefu_guid'] ?? '';
        $bid          = $data['bid'] ?? '';
        if (!$session_guid || !$kefu_guid || !$bid) {
            self::sendToClient($client_id, 'acceptSession', [
                'code' => 400,
                'msg'  => '参数缺失'
            ]);
            return;
        }
        // 校验会话是否待接入
        $session = CustomerSession::where([
            ['guid', '=', $session_guid],
            ['bid', '=', $bid],
            ['status', '=', 0]
        ])->find();
        if (!$session) {
            self::sendToClient($client_id, 'acceptSession', [
                'code' => 404,
                'msg'  => '会话不存在或已被接入'
            ]);
            return;
        }
        // 分配会话
        $session->user_guid   = $kefu_guid;
        $session->status      = 1; // 进行中
        $session->update_time = date('Y-m-d H:i:s.v');
        $session->save();
        // 推送会话变更给相关客户端
        $sessionUpdateData = [
            'session_guid' => $session_guid,
            'status'       => 1,
            'user_guid'    => $kefu_guid,
        ];

        // 通知接入的客服
        self::sendToUid($kefu_guid, 'sessionUpdate', $sessionUpdateData);

        // 通知会员
        self::sendToUid($session['member_guid'], 'sessionUpdate', $sessionUpdateData);
        // 推送系统消息给访客
        $msg_data = [
            'bid'          => $bid,
            'session_guid' => $session_guid,
            'from_type'    => 2,
            'from_guid'    => $kefu_guid,
            'to_guid'      => $session['member_guid'],
            'msg_type'     => 99,
            'content'      => '客服已接入会话',
            'is_read'      => 0,
        ];
        self::sendChatMessage($msg_data);
        // 返回分配结果
        self::sendToClient($client_id, 'acceptSession', [
            'code'    => 0,
            'msg'     => '接入成功',
            'session' => $session
        ]);
    }

    /**
     * 处理会话拒绝（客服拒绝待接入会话）
     */
    protected static function handleRejectSession($client_id, $data)
    {
        $session_guid = $data['session_guid'] ?? '';
        $kefu_guid    = $data['kefu_guid'] ?? '';
        $bid          = $data['bid'] ?? '';
        if (!$session_guid || !$kefu_guid || !$bid) {
            self::sendToClient($client_id, 'rejectSession', [
                'code' => 400,
                'msg'  => '参数缺失'
            ]);
            return;
        }

        // 校验会话是否待接入
        $session = CustomerSession::where([
            ['guid', '=', $session_guid],
            ['bid', '=', $bid],
            ['status', '=', 0]
        ])->find();
        if (!$session) {
            self::sendToClient($client_id, 'rejectSession', [
                'code' => 404,
                'msg'  => '会话不存在或已被处理'
            ]);
            return;
        }

        // 标记会话为已拒绝（可以设置为特殊状态或删除）
        $session->status      = -1; // -1表示已拒绝
        $session->update_time = date('Y-m-d H:i:s.v');
        $session->save();

        // 通知会员会话被拒绝
        self::sendToUid($session['member_guid'], 'sessionUpdate', [
            'session_guid' => $session_guid,
            'status'       => -1,
            'msg'          => '客服暂时无法接入，请稍后再试'
        ]);

        // 推送系统消息给访客
        $msg_data = [
            'bid'          => $bid,
            'session_guid' => $session_guid,
            'from_type'    => 2,
            'from_guid'    => 'system',
            'to_guid'      => $session['member_guid'],
            'msg_type'     => 99,
            'content'      => '客服暂时无法接入，请稍后再试或联系其他客服',
            'is_read'      => 0,
        ];
        self::sendChatMessage($msg_data);

        // 返回拒绝结果
        self::sendToClient($client_id, 'rejectSession', [
            'code'         => 0,
            'msg'          => '已拒绝会话',
            'session_guid' => $session_guid
        ]);
    }

    /**
     * 处理会话结束（客服主动结束会话）
     */
    protected static function handleCloseSession($client_id, $data)
    {
        $session_guid = $data['session_guid'] ?? '';
        $kefu_guid    = $data['kefu_guid'] ?? '';
        $bid          = $data['bid'] ?? '';
        if (!$session_guid || !$kefu_guid || !$bid) {
            self::sendToClient($client_id, 'closeSession', [
                'code' => 400,
                'msg'  => '参数缺失'
            ]);
            return;
        }
        // 校验会话是否进行中
        $session = CustomerSession::where([
            ['guid', '=', $session_guid],
            ['bid', '=', $bid],
            ['status', '=', 1]
        ])->find();
        if (!$session) {
            self::sendToClient($client_id, 'closeSession', [
                'code' => 404,
                'msg'  => '会话不存在或已结束'
            ]);
            return;
        }
        // 结束会话
        $session->status   = 2; // 已结束
        $session->end_time = date('Y-m-d H:i:s.v');
        $session->save();
        // 推送会话变更给相关客户端
        $sessionUpdateData = [
            'session_guid' => $session_guid,
            'status'       => 2,
            'user_guid'    => $kefu_guid,
        ];

        // 通知客服
        self::sendToUid($kefu_guid, 'sessionUpdate', $sessionUpdateData);

        // 通知会员
        self::sendToUid($session['member_guid'], 'sessionUpdate', $sessionUpdateData);
        // 推送系统消息给访客
        $msg_data = [
            'bid'          => $bid,
            'session_guid' => $session_guid,
            'from_type'    => 2,
            'from_guid'    => $kefu_guid,
            'to_guid'      => $session['member_guid'],
            'msg_type'     => 99,
            'content'      => '本次会话已结束，感谢您的咨询！',
            'is_read'      => 0,
        ];
        self::sendChatMessage($msg_data);
        // 返回结果
        self::sendToClient($client_id, 'closeSession', [
            'code' => 0,
            'msg'  => '会话已结束'
        ]);
    }

    /**
     * 处理心跳请求
     * @param int|string $client_id
     * @param array $data
     */
    protected static function handleHeartbeat($client_id, $data)
    {
        $timestamp    = $data['timestamp'] ?? 0;
        $client_type  = $data['client_type'] ?? 'unknown';
        $missed_count = $data['missed_count'] ?? 0;

        debug_log("[HEARTBEAT] 收到心跳 - 客户端: {$client_id}, 类型: {$client_type}, 丢失次数: {$missed_count}");

        // 更新客户端最后活跃时间
        self::updateClientLastActiveTime($client_id, $client_type);

        // 响应心跳，包含服务器时间戳
        $response = [
            'server_timestamp' => time() * 1000, // 毫秒时间戳
            'client_timestamp' => $timestamp,
            'status'           => 'ok',
            'server_time'      => date('Y-m-d H:i:s')
        ];

        self::sendToClient($client_id, 'heartbeat', $response);

        // 如果客户端报告丢失心跳，记录日志
        if ($missed_count > 0) {
            debug_log("[HEARTBEAT] 客户端报告丢失心跳: {$missed_count} 次");
        }
    }

    /**
     * 更新客户端最后活跃时间
     * @param int|string $client_id
     * @param string $client_type
     */
    protected static function updateClientLastActiveTime($client_id, $client_type)
    {
        // 可以在这里更新数据库中的客户端活跃时间
        // 或者使用缓存记录客户端状态
        $cache_key   = "ws_client_active_{$client_id}";
        $active_data = [
            'last_active'     => time(),
            'client_type'     => $client_type,
            'heartbeat_count' => cache($cache_key)['heartbeat_count'] ?? 0 + 1
        ];

        cache($cache_key, $active_data, 300); // 缓存5分钟
    }

    /**
     * 处理会话转接请求
     * @param int|string $client_id
     * @param array $data
     */
    protected static function handleTransferSession($client_id, $data)
    {
        $sessionGuid    = $data['session_guid'] ?? '';
        $toUserGuid     = $data['to_user_guid'] ?? '';
        $transferReason = $data['transfer_reason'] ?? '';

        if (!$sessionGuid || !$toUserGuid) {
            self::sendToClient($client_id, 'transferSessionReply', [
                'code' => -1,
                'msg'  => '参数不完整'
            ]);
            return;
        }

        try {
            // 获取当前客服信息
            $fromUserGuid = Gateway::getUidByClientId($client_id);
            if (!$fromUserGuid) {
                throw new \Exception('客服身份验证失败');
            }

            // 验证会话是否存在且属于当前客服
            $session = \app\model\CustomerSession::where([
                ['guid', '=', $sessionGuid],
                ['user_guid', '=', $fromUserGuid],
                ['status', '=', 1] // 进行中
            ])->find();

            if (!$session) {
                throw new \Exception('会话不存在或无权限转接');
            }

            // 验证目标客服是否存在且在线
            $toUser = \app\model\User::where([
                ['guid', '=', $toUserGuid],
                ['service_status', '=', 1] // 在线状态
            ])->find();

            if (!$toUser) {
                throw new \Exception('目标客服不存在或不在线');
            }

            // 检查是否有未处理的转接请求
            $existingTransfer = \app\model\CustomerSessionTransfer::where([
                ['session_guid', '=', $sessionGuid],
                ['status', '=', \app\model\CustomerSessionTransfer::STATUS_PENDING]
            ])->find();

            if ($existingTransfer) {
                throw new \Exception('该会话已有待处理的转接请求');
            }

            // 创建转接记录
            $transfer = \app\model\CustomerSessionTransfer::createTransfer(
                $sessionGuid,
                $fromUserGuid,
                $toUserGuid,
                $transferReason,
                $session['bid']
            );

            // 获取转出客服信息
            $fromUser = \app\model\User::where('guid', $fromUserGuid)->find();

            // 推送转接请求给目标客服
            self::sendToUid($toUserGuid, 'transferRequest', [
                'transfer_guid'   => $transfer['guid'],
                'session_guid'    => $sessionGuid,
                'from_user_guid'  => $fromUserGuid,
                'from_user_name'  => $fromUser['name'] ?? '客服',
                'transfer_reason' => $transferReason,
                'timeout_time'    => $transfer['timeout_time'],
                'session_info'    => [
                    'member_guid'   => $session['member_guid'],
                    'start_time'    => $session['start_time'],
                    'last_msg_time' => $session['last_msg_time']
                ]
            ]);

            // 回复转接发起者
            self::sendToClient($client_id, 'transferSessionReply', [
                'code'          => 0,
                'msg'           => '转接请求已发送',
                'transfer_guid' => $transfer['guid']
            ]);

            debug_log("[TRANSFER] 转接请求已发送: {$sessionGuid} -> {$toUserGuid}");
        } catch (\Exception $e) {
            self::sendToClient($client_id, 'transferSessionReply', [
                'code' => -1,
                'msg'  => $e->getMessage()
            ]);
            debug_log("[TRANSFER] 转接请求失败: " . $e->getMessage());
        }
    }

    /**
     * 处理转接响应
     * @param int|string $client_id
     * @param array $data
     */
    protected static function handleRespondTransfer($client_id, $data)
    {
        $transferGuid = $data['transfer_guid'] ?? '';
        $action       = $data['action'] ?? ''; // accept 或 reject
        $rejectReason = $data['reject_reason'] ?? '';

        if (!$transferGuid || !in_array($action, ['accept', 'reject'])) {
            self::sendToClient($client_id, 'respondTransferReply', [
                'code' => -1,
                'msg'  => '参数不完整或无效'
            ]);
            return;
        }

        try {
            // 获取当前客服信息
            $toUserGuid = Gateway::getUidByClientId($client_id);
            if (!$toUserGuid) {
                throw new \Exception('客服身份验证失败');
            }

            // 获取转接记录
            $transfer = \app\model\CustomerSessionTransfer::where([
                ['guid', '=', $transferGuid],
                ['to_user_guid', '=', $toUserGuid]
            ])->find();

            if (!$transfer) {
                throw new \Exception('转接记录不存在或无权限操作');
            }

            // 响应转接请求
            $transfer->respondTransfer($action, $rejectReason);

            if ($action === 'accept') {
                // 获取接受转接的客服信息
                $toUser = \app\model\User::where('guid', $toUserGuid)->find();

                // 接受转接，更新会话归属
                $session = \app\model\CustomerSession::where('guid', $transfer['session_guid'])->find();
                if ($session) {
                    // 更新会话信息
                    $session->user_guid          = $toUserGuid;
                    $session->transfer_count     = ($session->transfer_count ?? 0) + 1;
                    $session->last_transfer_time = date('Y-m-d H:i:s.v');
                    if (!$session->original_user_guid) {
                        $session->original_user_guid = $transfer['from_user_guid'];
                    }
                    $session->save();

                    // 获取会员信息
                    $member = \app\model\Member::where('guid', $session['member_guid'])->find();

                    // 通知接受转接的客服，推送完整会话信息
                    self::sendToUid($toUserGuid, 'sessionTransferred', [
                        'session_guid'    => $transfer['session_guid'],
                        'member_guid'     => $session['member_guid'],
                        'member_name'     => $member['name'] ?? \app\model\Member::DEFAULT_NAME,
                        'member_avatar'   => $member['head_img'] ?? \app\model\Member::DEFAULT_AVATAR,
                        'start_time'      => $session['start_time'],
                        'last_msg_time'   => $session['last_msg_time'],
                        'status'          => $session['status'],
                        'transfer_notice' => true,
                        'from_user_name'  => $fromUser['name'] ?? '客服'
                    ]);

                    // 通知转出客服
                    self::sendToUid($transfer['from_user_guid'], 'transferComplete', [
                        'transfer_guid' => $transferGuid,
                        'session_guid'  => $transfer['session_guid'],
                        'action'        => 'accepted',
                        'new_user_guid' => $toUserGuid
                    ]);

                    // 通知会员客服变更
                    self::sendToUid($session['member_guid'], 'sessionUpdate', [
                        'session_guid'    => $transfer['session_guid'],
                        'user_guid'       => $toUserGuid,
                        'kefu_name'       => $toUser['name'] ?? \app\model\User::DEFAULT_NAME,
                        'kefu_avatar'     => $toUser['head'] ?? \app\model\User::DEFAULT_AVATAR,
                        'transfer_notice' => true
                    ]);

                    // 发送系统消息
                    $fromUser = \app\model\User::where('guid', $transfer['from_user_guid'])->find();
                    // 使用之前已经查询的 $toUser 结果

                    $msg_data = [
                        'bid'          => $session['bid'],
                        'session_guid' => $transfer['session_guid'],
                        'from_type'    => 2,
                        'from_guid'    => $toUserGuid,
                        'to_guid'      => $session['member_guid'],
                        'msg_type'     => 99,
                        'content'      => sprintf(
                            '会话已从 %s 转接给 %s',
                            $fromUser['name'] ?? '客服',
                            $toUser['name'] ?? '客服'
                        ),
                        'is_read'      => 0,
                    ];
                    self::sendChatMessage($msg_data);
                }
            } else {
                // 拒绝转接，通知转出客服
                self::sendToUid($transfer['from_user_guid'], 'transferComplete', [
                    'transfer_guid' => $transferGuid,
                    'session_guid'  => $transfer['session_guid'],
                    'action'        => 'rejected',
                    'reject_reason' => $rejectReason
                ]);
            }

            // 回复响应者
            self::sendToClient($client_id, 'respondTransferReply', [
                'code' => 0,
                'msg'  => $action === 'accept' ? '转接已接受' : '转接已拒绝'
            ]);

            debug_log("[TRANSFER] 转接响应: {$transferGuid} - {$action}");
        } catch (\Exception $e) {
            self::sendToClient($client_id, 'respondTransferReply', [
                'code' => -1,
                'msg'  => $e->getMessage()
            ]);
            debug_log("[TRANSFER] 转接响应失败: " . $e->getMessage());
        }
    }

    /**
     * 客户端断开连接时
     * @param int|string $client_id
     */
    public static function onClose($client_id)
    {
        // 第一时间记录日志，确保方法被调用
        debug_log("=== [WebSocket] onClose事件开始 === 客户端ID: {$client_id}");
        debug_log("[WebSocket] onClose事件触发 - 客户端ID: {$client_id}");

        try {
            // 先尝试从Gateway获取UID（可能已被清理）
            $uid = Gateway::getUidByClientId($client_id);
            debug_log("[WebSocket] Gateway获取到的UID: " . ($uid ? $uid : 'null'));

            // 如果Gateway中获取不到，尝试从缓存中获取
            if (!$uid) {
                $cache_key = "ws_client_uid_{$client_id}";
                $uid       = cache($cache_key);
                debug_log("[WebSocket] 缓存获取到的UID: " . ($uid ? $uid : 'null'));
            }

            if ($uid) {
                debug_log("[WebSocket] 开始处理用户断开连接: {$uid}");

                // 检查是否为客服用户（通过查询user表确认）
                $user = \app\model\User::where('guid', $uid)->find();
                debug_log("[WebSocket] 查询用户信息结果: " . ($user ? "找到用户" : "未找到用户"));

                if ($user) {
                    debug_log([
                        "[WebSocket] 用户详细信息",
                        'guid'                   => $user['guid'],
                        'name'                   => $user['name'],
                        'current_service_status' => $user['service_status'],
                        'bid'                    => $user['bid']
                    ]);

                    // 这是一个客服用户，更新其离线状态
                    $updateResult = \app\model\User::where('guid', $uid)->update([
                        'service_status' => 0, // 设置为离线
                        'update_time'    => date('Y-m-d H:i:s')
                    ]);

                    debug_log([
                        "[WebSocket] 客服离线状态更新完成",
                        'kefu_guid'       => $uid,
                        'update_result'   => $updateResult,
                        'previous_status' => $user['service_status'],
                        'new_status'      => 0
                    ]);

                    // 通知其他在线客服该客服已离线
                    debug_log("[WebSocket] 开始广播客服离线状态");
                    self::broadcastKefuStatusChange($uid, 0, $user['name']);
                    debug_log("[WebSocket] 广播客服离线状态完成");
                } else {
                    // 可能是访客断开连接
                    debug_log("[WebSocket] 访客断开连接: {$uid}");
                }
            } else {
                debug_log("[WebSocket] 无法获取断开连接的用户ID，可能是匿名连接");
            }
        } catch (\Exception $e) {
            debug_log([
                "[WebSocket] 处理断开连接异常",
                'error'     => $e->getMessage(),
                'file'      => $e->getFile(),
                'line'      => $e->getLine(),
                'client_id' => $client_id
            ]);
        }

        // 清理客户端活跃状态缓存
        $cache_key_active = "ws_client_active_{$client_id}";
        cache($cache_key_active, null);
        debug_log("[WebSocket] 已清理客户端活跃状态缓存: {$cache_key_active}");

        // 清理客户端UID映射缓存
        $cache_key_uid = "ws_client_uid_{$client_id}";
        cache($cache_key_uid, null);
        debug_log("[WebSocket] 已清理客户端UID映射缓存: {$cache_key_uid}");

        // 方法结束日志
        debug_log("=== [WebSocket] onClose事件结束 === 客户端ID: {$client_id}");
    }

    /**
     * 广播客服状态变化
     * @param string $kefu_guid 客服GUID
     * @param int $status 状态 0-离线 1-在线
     * @param string $kefu_name 客服名称
     */
    protected static function broadcastKefuStatusChange($kefu_guid, $status, $kefu_name = '')
    {
        try {
            debug_log("[BROADCAST] 开始广播客服状态变化: {$kefu_guid}, 状态: {$status}");

            // 获取该客服所属的bid
            $user = \app\model\User::where('guid', $kefu_guid)->find();
            if (!$user) {
                debug_log("[BROADCAST] 未找到客服用户信息: {$kefu_guid}");
                return;
            }

            $bid         = $user['bid'];
            $status_text = $status == 1 ? '上线' : '离线';
            debug_log("[BROADCAST] 客服所属bid: {$bid}, 状态文本: {$status_text}");

            // 获取同一bid下的所有在线客服
            $online_kefus = \app\model\User::where([
                ['bid', '=', $bid],
                ['service_status', '=', 1],
                ['guid', '<>', $kefu_guid] // 排除自己
            ])->column('guid');

            debug_log("[BROADCAST] 找到在线客服数量: " . count($online_kefus) . ", 列表: " . implode(',', $online_kefus));

            // 向所有在线客服广播状态变化
            $notified_count = 0;
            foreach ($online_kefus as $online_kefu_guid) {
                $is_online = Gateway::isUidOnline($online_kefu_guid);
                debug_log("[BROADCAST] 检查客服 {$online_kefu_guid} 是否在线: " . ($is_online ? '是' : '否'));

                if ($is_online) {
                    self::sendToUid($online_kefu_guid, 'kefuStatusChange', [
                        'kefu_guid'   => $kefu_guid,
                        'kefu_name'   => $kefu_name ?: $user['name'],
                        'status'      => $status,
                        'status_text' => $status_text,
                        'timestamp'   => date('Y-m-d H:i:s')
                    ]);
                    $notified_count++;
                    debug_log("[BROADCAST] 已向客服 {$online_kefu_guid} 发送状态变化通知");
                }
            }

            debug_log([
                "[BROADCAST] 客服状态变化广播完成",
                'kefu_guid'          => $kefu_guid,
                'status'             => $status,
                'total_online_kefus' => count($online_kefus),
                'notified_count'     => $notified_count
            ]);
        } catch (\Exception $e) {
            debug_log([
                "[BROADCAST] 广播客服状态变化失败",
                'error'     => $e->getMessage(),
                'kefu_guid' => $kefu_guid,
                'status'    => $status
            ]);
        }
    }

    /**
     * 处理关闭用户连接（客服主动断开访客连接）
     */
    protected static function handleCloseUser($client_id, $data)
    {
        $member_guid = $data['member_guid'] ?? '';
        $kefu_guid   = $data['kefu_guid'] ?? '';
        $bid         = $data['bid'] ?? '';

        if (!$member_guid || !$kefu_guid || !$bid) {
            self::sendToClient($client_id, 'closeUser', [
                'code' => 400,
                'msg'  => '参数缺失'
            ]);
            return;
        }

        // 查找进行中的会话
        $session = CustomerSession::where([
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['user_guid', '=', $kefu_guid],
            ['status', '=', 1]
        ])->find();

        if ($session) {
            // 结束会话
            $session->status   = 2;
            $session->end_time = date('Y-m-d H:i:s.v');
            $session->save();

            // 推送会话结束通知
            $sessionUpdateData = [
                'session_guid' => $session['guid'],
                'status'       => 2,
                'user_guid'    => $kefu_guid,
            ];

            // 通知客服
            self::sendToUid($kefu_guid, 'sessionUpdate', $sessionUpdateData);

            // 通知会员
            self::sendToUid($member_guid, 'sessionUpdate', $sessionUpdateData);

            // 发送系统消息给访客
            $msg_data = [
                'bid'          => $bid,
                'session_guid' => $session['guid'],
                'from_type'    => 2,
                'from_guid'    => $kefu_guid,
                'to_guid'      => $member_guid,
                'msg_type'     => 99,
                'content'      => '客服已断开连接，本次会话结束。',
                'is_read'      => 0,
            ];
            self::sendChatMessage($msg_data);
        }

        // 强制断开访客连接
        if (Gateway::isUidOnline($member_guid)) {
            Gateway::closeClient(Gateway::getClientIdByUid($member_guid));
        }

        // 返回结果
        self::sendToClient($client_id, 'closeUser', [
            'code' => 0,
            'msg'  => '用户连接已关闭'
        ]);
    }

    /**
     * 发送机器人欢迎消息
     */
    protected static function sendRobotWelcomeMessage($client_id, $bid, $member_guid, $trigger_condition = 1)
    {
        debug_log("[机器人] 发送欢迎消息: 客户端 {$client_id}, 商户 {$bid}, 触发条件 {$trigger_condition}");

        // 根据触发条件生成欢迎语
        $welcomeMessages = [
            1 => "您好！当前客服暂时不在线，我是智能助手，很高兴为您服务！",
            2 => "您好！当前客服繁忙中，我是智能助手，可以先为您提供帮助！"
        ];

        $welcomeMessage = $welcomeMessages[$trigger_condition] ?? $welcomeMessages[1];

        // 使用通用方法保存机器人欢迎消息到数据库
        $msg_data = [
            'bid'          => $bid,
            'session_guid' => '', // 机器人消息暂时没有会话
            'from_type'    => 3, // 3表示机器人
            'from_guid'    => 'robot_system',
            'to_guid'      => $member_guid,
            'msg_type'     => 1, // 文本消息
            'content'      => $welcomeMessage,
            'is_read'      => 0,
            'from_name'    => '智能助手',
            'from_avatar'  => self::ROBOT_AVATAR,
            'extra'        => [
                'trigger_condition' => $trigger_condition,
                'message_type'      => 'robot_welcome'
            ]
        ];

        // 保存到数据库并发送
        self::sendChatMessage($msg_data, $client_id);

        // 延迟发送快捷回复选项
        $quickReplies = self::getRobotQuickReplies($bid);
        if (!empty($quickReplies)) {
            // 快捷回复选项也保存到数据库
            $quickReplyMsg = [
                'bid'          => $bid,
                'session_guid' => '',
                'from_type'    => 3, // 机器人
                'from_guid'    => 'robot_system',
                'to_guid'      => $member_guid,
                'msg_type'     => 10, // 快捷回复类型
                'content'      => '💡 猜您想问：',
                'is_read'      => 0,
                'from_name'    => '智能助手',
                'from_avatar'  => self::ROBOT_AVATAR,
                'extra'        => [
                    'message_type'  => 'robot_quick_replies',
                    'quick_replies' => $quickReplies
                ]
            ];

            self::sendChatMessage($quickReplyMsg, $client_id);
        }
    }

    /**
     * 获取机器人快捷回复选项
     */
    protected static function getRobotQuickReplies($bid)
    {
        try {
            // 从数据库获取自动回复项目
            $autoReplyItems = CustomerAutoReply::getItemsByBid($bid, 1, 5);

            $quickReplies = [];
            foreach ($autoReplyItems as $item) {
                $quickReplies[] = [
                    'id'   => $item['guid'],
                    'text' => $item['question']
                ];
            }
            return $quickReplies;
        } catch (\Exception $e) {
            debug_log("[机器人] 获取快捷回复选项失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 处理机器人快捷回复
     */
    protected static function handleRobotQuickReply($client_id, $data)
    {
        $reply_id      = $data['reply_id'] ?? '';
        $question_text = $data['question_text'] ?? '';
        $session_guid  = $data['session_guid'] ?? '';
        $member_guid   = $data['member_guid'] ?? '';
        $bid           = $data['bid'] ?? '';

        debug_log("[机器人] 处理快捷回复: {$reply_id} - {$question_text}");

        // 获取回复内容
        $replyContent = self::getRobotReplyContent($reply_id, $bid);

        if ($replyContent) {
            // 使用通用方法保存机器人回复到数据库
            $msg_data = [
                'bid'          => $bid,
                'session_guid' => $session_guid,
                'from_type'    => 3, // 机器人
                'from_guid'    => 'robot_system',
                'to_guid'      => $member_guid,
                'msg_type'     => 1, // 文本消息
                'content'      => $replyContent,
                'is_read'      => 0,
                'from_name'    => '智能助手',
                'from_avatar'  => self::ROBOT_AVATAR,
                'extra'        => [
                    'message_type' => 'robot_reply',
                    'reply_to'     => $question_text,
                    'reply_id'     => $reply_id
                ]
            ];

            self::sendChatMessage($msg_data, $client_id);

            // 延迟发送继续提示和新的快捷回复选项
            $quickReplies = self::getRobotQuickReplies($bid);
            if (!empty($quickReplies)) {
                // 发送新的快捷回复选项，也保存到数据库
                $quickReplyMsg = [
                    'bid'          => $bid,
                    'session_guid' => $session_guid,
                    'from_type'    => 3, // 机器人
                    'from_guid'    => 'robot_system',
                    'to_guid'      => $member_guid,
                    'msg_type'     => 10, // 快捷回复类型
                    'content'      => '💡 您是否还想问：',
                    'is_read'      => 0,
                    'from_name'    => '智能助手',
                    'from_avatar'  => self::ROBOT_AVATAR,
                    'extra'        => [
                        'message_type'  => 'robot_quick_replies',
                        'quick_replies' => $quickReplies
                    ]
                ];

                self::sendChatMessage($quickReplyMsg, $client_id);
            }
        } else {
            // 发送默认回复，也保存到数据库
            $defaultMsg = [
                'bid'          => $bid,
                'session_guid' => $session_guid,
                'from_type'    => 3, // 机器人
                'from_guid'    => 'robot_system',
                'to_guid'      => $member_guid,
                'msg_type'     => 1, // 文本消息
                'content'      => '抱歉，我暂时无法回答这个问题。客服上线后会为您详细解答！',
                'is_read'      => 0,
                'from_name'    => '智能助手',
                'from_avatar'  => self::ROBOT_AVATAR,
                'extra'        => [
                    'message_type' => 'robot_default',
                    'reply_to'     => $question_text
                ]
            ];

            self::sendChatMessage($defaultMsg, $client_id);
        }
    }

    /**
     * 获取机器人回复内容
     */
    protected static function getRobotReplyContent($reply_id, $bid)
    {
        try {
            // 从数据库获取回复内容
            $item = CustomerAutoReply::getAnswerByGuid($bid, $reply_id);

            if ($item) {
                // 增加点击次数
                debug_log('获取成功');
                CustomerAutoReply::incrementClickCount($bid, $reply_id);
                return $item['answer'];
            } else {
                debug_log('没有获取到内容');
            }
            return null;
        } catch (\Exception $e) {
            debug_log("[机器人] 获取回复内容失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 处理访客输入状态（实时预览）
     */
    protected static function handleTyping($client_id, $data)
    {
        $session_guid = $data['session_guid'] ?? '';
        $member_guid  = $data['member_guid'] ?? '';
        $user_guid    = $data['user_guid'] ?? '';
        $content      = $data['content'] ?? '';
        $status       = $data['status'] ?? 'typing'; // start/typing/stop
        $bid          = $data['bid'] ?? '';

        debug_log("[输入预览] 会话: {$session_guid}, 会员: {$member_guid}, 客服: {$user_guid}, 状态: {$status}");

        // 内容长度限制（防止过长内容影响性能）
        if (strlen($content) > 100) {
            $content = mb_substr($content, 0, 100, 'UTF-8') . '...';
        }

        // 获取会员信息
        $member     = \app\model\Member::where('guid', $member_guid)->find();
        $memberName = $member['name'] ?? '访客';

        // 转发给对应的客服
        if (!empty($user_guid) && Gateway::isUidOnline($user_guid)) {
            self::sendToUid($user_guid, 'typingStatus', [
                'session_guid' => $session_guid,
                'member_guid'  => $member_guid,
                'member_name'  => $memberName,
                'content'      => $content,
                'status'       => $status,
                'timestamp'    => time(),
                'bid'          => $bid
            ]);

            debug_log("[输入预览] 已转发给客服: {$user_guid}, 内容长度: " . strlen($content));
        } else {
            debug_log("[输入预览] 客服不在线或未找到: {$user_guid}");
        }

        // 如果是会员端发送的，也可以转发给其他相关客服（如果有多客服场景）
        // 这里暂时只转发给当前会话的客服
    }
}
