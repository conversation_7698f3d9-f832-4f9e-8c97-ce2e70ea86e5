<?php

namespace app\model;

use Exception;
use Throwable;

class WeappSubmitNote extends ModelBase
{
    protected $pk = 'guid';
    protected $type = [
        'screenshot_image_list' => 'json',
    ];
    protected $jsonAssoc = true;

    public function is_has_auditing_note($appid)
    {
        if (empty($appid)) {
            return false;
        }
        $db_weapp_submit_note = new WeappSubmitNote();
        $last_submit_note     = $db_weapp_submit_note->get_last_submit_note($appid);
        if ($last_submit_note->isEmpty()) {
            return false;
        }
        return $last_submit_note['status'] == 2;
    }

    public function get_last_submit_note($appid)
    {
        $db_weapp_submit_note = new WeappSubmitNote();
        $component_appid      = weixin()::get_component_appid($appid);
        $map                  = [
            ['appid', '=', $appid],
            ['component_appid', '=', $component_appid],
            ['auditid', '>', 0],
        ];
        return $db_weapp_submit_note->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
    }

    public function submit_app($appid)
    {
        $wechat          = weixin($appid)::WeMiniCode();
        $component_appid = weixin()::get_component_appid($appid);

        //自动设置隐私协议
        $db_weapp_commit_note = new WeappCommitNote();
        $db_weapp_commit_note->set_privacy($appid);

        //自动检测接口权限
        $db_weapp_commit_note->set_privacy_interface($appid);

        //自动获取小程序类目
        $result = $wechat->getCategory();
        if (empty($result['category_list'])) {
            $this->error = '提审失败:请先到微信后台设置类目再发布';
            return false;
        }
        $default_item = [
            'address' => 'pages/index/index',
            'tag'     => '商城',
            'title'   => '在线商城'
        ];
        $item         = array_merge($default_item, $result['category_list'][0]);
        $page         = $wechat->getPage();
        if (!empty($page['page_list'])) {
            $first_page      = reset($page['page_list']);
            $item['address'] = $first_page;
        }
        $item_list         = [$item];
        $weapp_submit_note = new WeappSubmitNote();
        $submit_guid       = create_guid();
        $insert_data       = [
            'guid'            => $submit_guid,
            'component_appid' => $component_appid,
            'appid'           => $appid,
            'submit_data'     => json_encode($item_list, JSON_UNESCAPED_UNICODE),
        ];
        $weapp_submit_note->save($insert_data);
        $map = [['guid', '=', $submit_guid]];
        try {
            //自动设置隐私协议
            $db_weapp_commit_note = new WeappCommitNote();
            $db_weapp_commit_note->set_privacy($appid);


            //自动判断是否有接口权限
            $result       = $db_weapp_commit_note->set_privacy_interface($appid);
            $code_service = weixin($appid)::WeMiniCode();
            $submit       = $code_service->submitAudit($item_list, '', '', '/pages/order/order');
            $auditid      = $submit['auditid'];
            $update_data  = [
                'status'  => 2,
                'reason'  => '提审成功',
                'auditid' => $auditid,
            ];
            $weapp_submit_note::update($update_data, $map);
        } catch (Exception|Throwable $e) {
            $msg         = '提审失败:' . $e->getMessage();
            $update_data = [
                'status' => -1,
                'reason' => $msg,
            ];
            $weapp_submit_note::update($update_data, $map);
            $this->error = $msg;
            return false;
        }
        try {
            $db_weapp_commit_note = new WeappCommitNote();
            $map                  = [
                ['appid', '=', $appid],
                ['component_appid', '=', $component_appid],
            ];
            $update_data          = ['status' => 2, 'reason' => '提审成功'];
            $db_weapp_commit_note::update($update_data, $map);
        } catch (Exception|Throwable $e) {
        }
        wr_log('小程序:' . $appid . '提交审核成功');
        return true;
    }

    public function publish_release($appid)
    {
        $db_wechat_config = new WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['principal_name', 'nick_name']);
        $principal_name   = $info['principal_name'];
        $nick_name        = $info['nick_name'];
        try {
            $code_service = weixin($appid)::WeMiniCode();
            $code_service->publishRelease();
            $db          = new WeappSubmitNote();
            $update_data = ['status' => 3, 'reason' => '发布成功'];
            $db->update_last_submit_note($appid, $update_data);
            $msg = '小程序已通过并发布上线成功:,' . $principal_name . '-' . $nick_name . '(Appid:' . $appid . ')';
            wr_log($msg, 1);
            return true;
        } catch (Exception|Throwable $e) {
            $this->error = $e->getMessage();
            $this->code  = $e->getCode();
            $msg         = '小程序发布失败:,' . $principal_name . '-' . $nick_name . '(Appid:' . $appid . ');原因:' . $this->error;
            wr_log($msg, 1);
            return false;
        }
    }

    public function update_last_submit_note($appid, $update_data)
    {
        $db              = new WeappSubmitNote();
        $component_appid = weixin()::get_component_appid($appid);
        $map             = [
            ['component_appid', '=', $component_appid],
            ['appid', '=', $appid],
            ['status', 'IN', [0, 2]] //只允许更新审核中和审核通过的记录
        ];
        $guid            = $db->where($map)->order(['create_time' => 'DESC'])->value('guid');
        if (!empty($guid)) {
            $map[] = ['guid', '=', $guid];
            return $db::update($update_data, $map);
        }
        return false;
    }
}