<?php

namespace app\model;

use app\common\exceptions\NotNotifyException;
use app\common\service\NotifyService;
use Exception;
use think\facade\Db;
use think\model\concern\SoftDelete;

/**
 *
 */
class Coupon extends ModelBase
{
    use SoftDelete;

    protected $pk = 'guid';

    protected $type = [
        'goods_item_guid' => 'json',
        'date_rule_guid'  => 'json',
        'user_guid_json'  => 'json',
    ];

    protected $jsonAssoc = true;
    public $log_field = [
        'title_field_key' => 'name',
        'field'           => [
            'name'                             => '卡券名称',
            'sort'                             => '序号',
            'active_price'                     => '激活价格',
            'selling_price'                    => '售价',
            'cash_money'                       => '提现金额',
            'confirm_reward_user_money'        => '完单奖励',
            'single_openid_max_exchange_times' => '单人限制次数',
            'active_reward_user_money'         => '激活奖励金额',
            'expire_days'                      => '过期天数',
            'coupon_used_reward_money'         => '核销奖励金额',
            'expire_time'                      => '过期时间',
            'online_status'                    => '上架状态',
            'status'                           => '状态',
            'everyday_exchange_limit_num'      => '每日限兑',
            'default_cycle_delivery_interval'  => '周期间隔',
            'pic'                              => '主图',
            'send_num'                         => '提货次数',
        ]
    ];

    public function getExpireTimeAttr($value, $data)
    {
        return !empty($data['expire_time']) ? date('Y-m-d', strtotime($data['expire_time'])) : $data['expire_time'];
    }

    public function get_verify_url_qrcode_list($bid, $coupon_guid = null)
    {
        $data = ['bid' => $bid];
        if ($coupon_guid) {
            $data['coupon_guid'] = $coupon_guid;
        }
        $path         = 'member/code/index';
        $db_short_url = new ShortUrl();
        $db_business  = new Business();
        $info         = $db_business->get_business_info_by_account_or_guid($bid);
        return $db_short_url->get_url_with_qrcode($path, $data, [], $info['business_name'] . '-通用提货码');
    }

    public function is_enable_user_guid_json($bid)
    {
        $db_coupon = new Coupon();
        $map       = [
            ['bid', '=', $bid],
            ['user_guid_json', 'NOT NULL', null],
        ];
        return $db_coupon->where($map)->value('id');
    }

    protected function get_dates_from_today_to_month_end($startOffset)
    {
        $dates = [];
        // 获取当前日期
        $currentDate = date('Y-m-d');
        // 获取当前月的最后一天
        $lastDayOfMonth = date('Y-m-t', strtotime($currentDate));
        // 计算从今天到月底的天数
        $daysUntilEndOfMonth = (strtotime($lastDayOfMonth) - strtotime($currentDate)) / 86400;

        // 计算起始日期的偏移量
        $startDateOffset = $startOffset + 1; // 因为从0开始计数，所以需要减1
        // 确保起始偏移量不会超过月底的天数
        if ($startDateOffset < 0) {
            $startDateOffset = 0;
        }

        // 使用for循环添加日期到数组
        for ($i = $startDateOffset; $i <= $daysUntilEndOfMonth; $i++) {
            $dates[] = date('Y-n-j', strtotime("+$i day", strtotime($currentDate)));
        }

        return $dates;
    }

    public function get_disable_date_list_by_coupon_guid($bid, $coupon_guid, $coupon_send_note_guid = null, $choose_goods_guid_list = null)
    {
        $disable_date_array     = [];
        $disable_date_list_mark = [];
        $map                    = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $db_coupon              = new Coupon();
        $coupon_info            = $db_coupon->where($map)->findOrFail();
        $date_rule_guid         = $coupon_info['date_rule_guid'];
        $date_rule_guid         = []; //暂时重新赋值 不使用 卡券上的日期规则 list
        $db_date_rule           = new DateRule();
        $date_rule_list         = $db_date_rule->get_rule_list($bid, $coupon_guid, $choose_goods_guid_list);
        foreach ($date_rule_list as $date_rule) {
            $date_rule_guid[] = $date_rule['guid'];
        }
        $everyday_exchange_limit_type = $coupon_info['everyday_exchange_limit_type']; // 0 不限制 1限制
        $everyday_exchange_limit_num  = $coupon_info['everyday_exchange_limit_num']; // 0 每日限制份数
        if (!empty($date_rule_guid) && is_array($date_rule_guid)) {
            foreach ($date_rule_guid as $key => $val) {
                if (empty($val)) {
                    unset($date_rule_guid[$key]);
                }
            }
        }
        $unavailable_date_array = [];
        $available_date_array   = [];
        $all_date_array         = [];
        $config                 = get_config_by_bid($bid);
        $max_day                = $config['max_request_send_or_pick_up_days']; //最大预约天数
        if (!empty($date_rule_guid)) {
            //如果存在日期规则 则根据日期规则来决定禁用什么日期
            $db_date_rule         = new DateRule();
            $map                  = [
                ['bid', '=', $bid],
                ['status', '=', 1],
                ['guid', 'IN', $date_rule_guid],
            ];
            $available_date_list  = $db_date_rule->where($map)->field(['date', 'weekday', 'type', 'rule_type', 'start_date', 'end_date', 'start_month_day', 'end_month_day', 'month_days'])->select()->toArray();
            $unavailable_rule_num = 0; //不可用规则数
            $available_rule_num   = 0; //可用规则数
            for ($i = 0; $i <= $max_day; $i++) {
                $now_day          = date("Y-m-d", strtotime("+$i day"));
                $layui_day        = date("Y-n-j", strtotime("+$i day"));
                $all_date_array[] = $layui_day;
                foreach ($available_date_list as $available_date) {
                    $type      = $available_date['type']; // 0 不可选  1 可选
                    $rule_type = $available_date['rule_type'];  //日期规则 1 连续日期 2 不规则日期  3 周几 后续扩展其他
                    $is_match  = null;
                    $type == 0 && $unavailable_rule_num++;
                    $type == 1 && $available_rule_num++;
                    //0 不可选  1 可选
                    switch ($rule_type) {
                        case 1: //连续日期
                            $start_date = $available_date['start_date'];
                            $end_date   = $available_date['end_date'];
                            //如果设置了某个区间不可选 则日期大于等于start_date 且 小于等于 end_date 就说明不可选
                            $is_match = $start_date && $end_date && strtotime($now_day) >= strtotime($start_date) && strtotime($now_day) <= strtotime($end_date);
                            break;
                        case 2: //不规则日期
                            $rule_date_array = $available_date['date'];
                            //设置日期不可选,当前日期如果在数组,说明不可选
                            $is_match = $rule_date_array && in_array($now_day, $rule_date_array);
                            break;
                        case 3: // 周几
                            $weekday       = $available_date['weekday'];
                            $weekday_array = explode(',', $weekday);
                            //$weekday_array   = array_filter($weekday);  // 这样会把周日自动剔除掉
                            //如果设置的周几 则当前日期包含 周几 就周几
                            $is_match = in_array(date('w', strtotime($now_day)), $weekday_array);
                            break;
                        case 4: //每年 月-日
                            $start_month_day = date('Y', strtotime($now_day)) . '-' . $available_date['start_month_day'];
                            $end_month_day   = date('Y', strtotime($now_day)) . '-' . $available_date['end_month_day'];
                            //如果设置了某个区间不可选 则日期大于等于 start_month_date 且 小于等于 end_month_date 就说明不可选
                            $is_match = $start_month_day && $end_month_day && strtotime($now_day) >= strtotime($start_month_day) && strtotime($now_day) <= strtotime($end_month_day);
                            break;
                        case 5: //每月几日
                            $month_days = $available_date['month_days'];
                            $month_days = explode(',', $month_days);

                            $day_of_month = (int)date('j', strtotime($now_day));
                            //设置日期不可选,当前日期如果在数组,说明不可选
                            $is_match = $month_days && in_array($day_of_month, $month_days);
                            break;
                        default:
                            break;
                    }
                    if ($is_match) {
                        $type == 0 && ($unavailable_date_array[] = $layui_day);
                        $type == 1 && ($available_date_array[] = $layui_day);
                    }
                }
            }
            $unavailable_date_array = array_unique($unavailable_date_array);
            $available_date_array   = array_unique($available_date_array);
            if ($available_rule_num > 0 && $unavailable_rule_num == 0) {
                //全部都是可用, 则直接禁用可用以外的日期
                $disable_date_array = array_diff($all_date_array, $available_date_array);
            } elseif ($unavailable_rule_num > 0 && $available_rule_num == 0) {
                //全部都是不可用, 则直接禁用不可用的日期
                $disable_date_array = $unavailable_date_array;
            } elseif ($unavailable_rule_num > 0 && $available_date_array > 0) {
                //先考虑某个日期既在可用  又在不可用  这种要先从可用日期中剔除
                $available_date_array = array_diff($available_date_array, $unavailable_date_array);
                //然后可用日期之外的日期 都设置为不可用
                $disable_date_array = array_diff($all_date_array, $available_date_array);
            }
            $disable_date_array = array_values($disable_date_array);
        } else {
            //如果没任何日期规则  至少要把当月可预约天数之后的日期 禁用掉
            $disable_date_array = $this->get_dates_from_today_to_month_end($max_day);
        }
        //        foreach ($disable_date_array as $date) {
        //            $disable_date_list_mark[$date] = '未开';
        //        }
        //开始判断每日限兑次数
        if ($everyday_exchange_limit_type == 1) {
            $db_goods_order                    = new GoodsOrder();
            $map_goods_order                   = [
                ['bid', '=', $bid],
                ['way', '=', 1],
                ['coupon_guid', '=', $coupon_guid],
                ['request_send_or_pick_up_time', '>=', date('Y-m-d')],
            ];
            $field                             = [
                //                Db::raw("date_format(request_send_or_pick_up_time,'%Y-%n-%j') as request_send_or_pick_up_time"),
                'request_send_or_pick_up_time'
            ];
            $request_send_or_pick_up_time_list = $db_goods_order->where($map_goods_order)->field($field)->group('request_send_or_pick_up_time')->having("count(1)>=$everyday_exchange_limit_num")->select()->toArray();
            foreach ($request_send_or_pick_up_time_list as $key => $val) {
                $request_send_or_pick_up_time_date                          = date("Y-n-j", strtotime($val['request_send_or_pick_up_time']));
                $disable_date_array[]                                       = $request_send_or_pick_up_time_date;
                $disable_date_list_mark[$request_send_or_pick_up_time_date] = '约满';
            }
            if (!empty($request_send_or_pick_up_time_list)) {
                $disable_date_array = array_unique($disable_date_array);
                $disable_date_array = array_values($disable_date_array);
            }
        }
        return ['disable_date_list' => $disable_date_array, 'disable_date_list_mark' => $disable_date_list_mark,];
    }

    public function notify_refresh_home_data($bid)
    {
        notify()->set_key_name(NotifyService::RefreshHomeData)->set_data(['type' => 'notify_refresh_home_data'])->set_bid($bid)->send();
    }

    public function get_sell_code($map_sell_code = [], $find = false)
    {
        $db_coupon   = new Coupon();
        $bid         = $this->get_bid_from_request();
        $member_guid = $this->get_member_guid_from_request();
        $join        = [
            ['member m', "c.bid=m.bid AND m.guid='$member_guid'", 'LEFT'],
            ['member_group_discount mgd', "m.member_group_guid = mgd.member_group_guid  AND mgd.object_guid =c.guid and c.bid=mgd.bid AND mgd.type=1", 'LEFT'],
        ];
        $map         = [
            ['c.bid', '=', $bid],
            //            ['c.selling_price', '>', 0],
            ['c.status', '=', 1],
            ['c.online_status', '=', 1],
        ];
        foreach ($map_sell_code as $key => $val) {
            $map[] = ['c.' . $key, '=', $val];
        }
        $field     = [
            'c.name',
            'c.bid',
            'c.guid',
            'c.pic',
            'c.pic1',
            'c.pic2',
            'c.pic3',
            'c.send_type',
            'c.video_url',
            'c.description',
            'c.selling_price',
            'c.selling_original_price',
            Db::raw("ROUND(IFNULL(mgd.discount,1),2) as discount"),
            Db::raw("ROUND(c.selling_price * IFNULL(mgd.discount,1),2) as discount_selling_price"),
        ];
        $db_coupon = $db_coupon->alias('c')->field($field)->where($map)->join($join)->order(['sort' => 'ASC', 'selling_price' => 'ASC']);
        return $find ? $db_coupon->findOrFail() : $db_coupon->select();
    }


    public function get_coupon_list($field = [], $map = [])
    {
        $default_field = ['guid', 'name', 'type', 'value'];
        $merge_field   = array_merge($default_field, $field);
        $map[]         = ['bid', '=', $this->get_bid_from_request()];
        $map[]         = ['status', '=', 1];
        $db            = new Coupon();
        $order         = [
            'type'  => 'ASC',
            'value' => 'ASC',
        ];
        $list          = $db->field($merge_field)->where($map)->order($order)->select();
        $list          = $list->append(['type_text'])->toArray();
        return $list;
    }

    public function get_coupon_list_width_num($map = [])
    {
        $bid                 = $this->get_bid_from_request();
        $where               = [
            ['bid', '=', $bid],
            ['status', '=', 0],
            ['send_time', 'NULL', null],
            ['revoke_time', 'NULL', null],
        ];
        $db_coupon_send_note = new CouponSendNote();
        $sub_sql             = $db_coupon_send_note->where($where)->field(['bid', 'coupon_guid', 'count(1)' => 'available_num'])->group('coupon_guid')->buildSql();
        $map[]               = ['c.bid', '=', $bid];
        $map[]               = ['c.status', '=', 1];
        $db_user             = new User();
        $map[]               = ['c.operator_user_id', 'IN', $db_user->getChildUserIdArray()]; //只可以发送查看范围内的券
        $db_coupon           = new Coupon();
        $order               = [
            'type'          => 'ASC',
            'value'         => 'ASC',
            'available_num' => 'DESC',
        ];
        return $db_coupon->alias('c')->field(['c.guid', 'c.name', 'c.value', 'c.type', 'IFNULL(available_num,0)' => 'available_num'])->join([$sub_sql => 'csn'], 'csn.bid = c.bid and csn.coupon_guid=c.guid', 'LEFT')->where($map)->order($order)->select()->append(['type_text'])->toArray();
    }

    /**
     * 通过用户guid获取用户GUID
     * @access public
     * @return integer
     */
    protected function get_user_id()
    {
        return $this->get_user_id_from_request();
    }

    public function getPicAttr($value, $data)
    {
        $host        = config('app.app_host_domain');
        $data['pic'] = empty($data['pic']) ? '/static/img/empty_img.png' : $data['pic'];
        //处理主图
        if (!filter_var($data['pic'], FILTER_VALIDATE_URL)) {
            $data['pic'] = 'http://' . $host . $data['pic'];
        }
        return $data['pic'];
    }

    public function getPicMiniAttr($value, $data)
    {
        return tools()::add_thumbnail_mini($data['pic']);
    }

    public function getPicSmallAttr($value, $data)
    {
        return tools()::add_thumbnail_small($data['pic']);
    }

    public function get_coupon_info_by_data($bid, $data, $auto_create = true)
    {
        $db_coupon = new Coupon();
        if (empty($data['name'])) {
            throw new Exception('请传入卡券名称');
        }
        $coupon_name = $data['name'];
        $map_coupon  = [
            ['bid', '=', $bid],
            ['name', '=', $coupon_name]
        ];
        $coupon_info = $db_coupon->where($map_coupon)->order(['create_time' => 'DESC'])->findOrEmpty();
        if ($coupon_info->isEmpty()) {
            if ($auto_create === false) {
                return false;
            }
            // 没有同名卡券, 则先查看是否有同名商品 没有则先创建同名商品 再自动创建卡券

            $db_goods    = new Goods();
            $goods_data  = [
                'name'  => $data['name'],
                'specs' => $data['specs'] ?? '',
                'price' => $data['price'] ?? 0,
            ];
            $goods_info  = $db_goods->get_goods_info_by_data($bid, $goods_data);
            $goods_guid  = $goods_info['guid'];
            $data        = [
                'bid'                => $bid,
                'name'               => $coupon_name,
                'exchange_goods_num' => 1,
                'type'               => 1,
                'goods_item_guid'    => [$goods_guid],
                'specs'              => $data['specs'] ?? '',
                'price'              => $data['price'] ?? 0,
            ];
            $coupon_info = $db_coupon->add($data);
        }
        return $coupon_info;
    }

    public function add(array $data)
    {
        if (empty($data['bid'])) {
            $data['bid'] = $this->get_bid_from_request();
        }
        if (empty($data['guid'])) {
            $data['guid'] = create_guid();
        }
        if (empty($data['operator_user_id'])) {
            $data['operator_user_id'] = $this->get_user_id_from_request();
        }
        if (empty($data['expire_time'])) {
            $data['expire_time'] = '2099-12-31 23:59:59';
        }
        if (!empty($data['user_guid_json'])) {
            $data['user_guid_json'] = explode(',', $data['user_guid_json']);
        } else {
            unset($data['user_guid_json']);
        }
        if (!empty($data['value'])) {
            //先判断是否有同面值/类型/名称的券
            $map   = [
                ['bid', '=', $data['bid']],
                ['name', '=', $data['name']],
                ['value', '=', $data['value']],
                ['type', '=', $data['type']],
            ];
            $db    = new Coupon();
            $count = $db->where($map)->count();
            if ($count) {
                $this->error = '创建失败,已经存在' . $data['value'] . '元面值的券!';
                return false;
            }
        }

        $this->save($data);
        // 把 goods_item_guid 写入 coupon_goods_item
        $this->save_coupon_goods_item($data);
        // 把 goods_category_guid 写入 coupon_goods_category_item (赠礼卡类型)
        if ($data['type'] == 4) {
            $this->save_coupon_goods_category_item($data);
        }
        $db_coupon = new Coupon();
        $db_coupon->notify_refresh_home_data($data['bid']);
        return $data;
    }

    public function save_coupon_goods_item($data, $delete_before_data_map = [])
    {
        if (!empty($data['goods_item_guid'])) {
            $db_coupon_goods_item    = new CouponGoodsItem();
            $before_goods_guid_array = [];
            $coupon_goods_item_data  = [];
            if (!empty($delete_before_data_map)) {
                $delete_before_data_map1   = $delete_before_data_map;
                $delete_before_data_map1[] = ['goods_guid', 'NOT IN', $data['goods_item_guid']];
                $db_coupon_goods_item->where($delete_before_data_map1)->delete();
                $before_goods_guid_array = $db_coupon_goods_item->where($delete_before_data_map)->column('goods_guid');
            }
            foreach ($data['goods_item_guid'] as $goods_item_guid) {
                if (!in_array($goods_item_guid, $before_goods_guid_array)) {
                    $coupon_goods_item_data[] = [
                        'guid'        => create_guid(),
                        'status'      => 1,
                        'bid'         => $data['bid'],
                        'coupon_guid' => $data['guid'],
                        'goods_guid'  => $goods_item_guid,
                    ];
                }
            }
            if (!empty($coupon_goods_item_data)) {
                $db_coupon_goods_item->saveAll($coupon_goods_item_data, false);
            }
        }
    }

    /**
     * 保存卡券商品类别关联
     * @param array $data 卡券数据
     * @param array $delete_before_data_map 删除条件
     */
    public function save_coupon_goods_category_item($data, $delete_before_data_map = [])
    {
        if (!empty($data['goods_category_guid'])) {
            $db_coupon_goods_category_item   = new CouponGoodsCategoryItem();
            $before_category_guid_array      = [];
            $coupon_goods_category_item_data = [];

            if (!empty($delete_before_data_map)) {
                $delete_before_data_map1   = $delete_before_data_map;
                $delete_before_data_map1[] = ['goods_category_guid', 'NOT IN', $data['goods_category_guid']];
                $db_coupon_goods_category_item->where($delete_before_data_map1)->delete();
                $before_category_guid_array = $db_coupon_goods_category_item->where($delete_before_data_map)->column('goods_category_guid');
            }

            foreach ($data['goods_category_guid'] as $goods_category_guid) {
                if (!in_array($goods_category_guid, $before_category_guid_array)) {
                    $coupon_goods_category_item_data[] = [
                        'guid'                => create_guid(),
                        'status'              => 1,
                        'bid'                 => $data['bid'],
                        'coupon_guid'         => $data['guid'],
                        'goods_category_guid' => $goods_category_guid,
                        'sort'                => 0,
                        'create_time'         => format_timestamp(),
                        'update_time'         => format_timestamp(),
                    ];
                }
            }

            if (!empty($coupon_goods_category_item_data)) {
                $db_coupon_goods_category_item->saveAll($coupon_goods_category_item_data, false);
            }
        }
    }

    public function edit(array $data)
    {
        $pk = $this->getPk();
        if (!isset($data[$pk])) {
            $this->error = '缺少主键';
            return false;
        }
        if (!empty($data['user_guid_json'])) {
            $data['user_guid_json'] = explode(',', $data['user_guid_json']);
        } else {
            unset($data['user_guid_json']);
        }
        $map                   = [['bid', '=', $this->get_bid_from_request()], [$pk, '=', $data[$pk]]];
        $update_result         = $this::update($data, $map);
        $db_coupon_goods_item  = new CouponGoodsItem();
        $map_coupon_goods_item = [
            ['bid', '=', $this->get_bid_from_request()],
            ['coupon_guid', '=', $data[$pk]],
        ];
        $data['bid']           = $this->get_bid_from_request();
        if (!empty($data['goods_item_guid'])) {
            //            $db_coupon_goods_item->where($map_coupon_goods_item)->delete();
            $this->save_coupon_goods_item($data, $map_coupon_goods_item);
        }
        // 处理赠礼卡的商品类别关联
        if (isset($data['type']) && $data['type'] == 4) {
            $map_coupon_goods_category_item = [
                ['bid', '=', $this->get_bid_from_request()],
                ['coupon_guid', '=', $data[$pk]],
            ];
            if (!empty($data['goods_category_guid'])) {
                $this->save_coupon_goods_category_item($data, $map_coupon_goods_category_item);
            } else {
                // 如果没有选择类别，删除所有关联
                $db_coupon_goods_category_item = new CouponGoodsCategoryItem();
                $db_coupon_goods_category_item->where($map_coupon_goods_category_item)->delete();
            }
        }
        return $update_result;
    }

    public function del(array $data)
    {
        $soft_delete = $this->soft_delete($data);
        $db_coupon   = new Coupon();
        $db_coupon->notify_refresh_home_data($data['bid'] ?? $this->get_bid_from_request());
        return $soft_delete;
    }

    public function send_coupon(array $data)
    {
        $lock_instance = get_distributed_instance();
        $bid           = $data['bid'];
        $start_time    = microtime(true);
        $task_id       = $data_arr['task_id'] ?? tools()::get_bill_number();
        $batch_task_id = $data_arr['batch_task_id'] ?? tools()::get_bill_number();
        logToFile('model进程:' . getmypid() . '$task_id=' . $task_id . ';$batch_task_id=' . $batch_task_id . ';' . __LINE__ . '行;耗时:' . get_diff_time($start_time));
        $lock                  = ''; //不是所有情况都需要加锁
        $way                   = $data['way'] ?? 1;
        $send_remark           = $data['send_remark'] ?? '';
        $member_guid           = $data['member_guid'] ?? null;
        $mobile                = $data['mobile'] ?? null;
        $code                  = !empty($data['code']) ? $data['code'] : ''; //批量发券支持先生成卡号 便于并发执行
        $operator_user_id      = $data['operator_user_id'] ?? 0;
        $coupon_guid           = $data['coupon_guid'];
        $coupon_send_note_guid = !empty($data['coupon_send_note_guid']) ? $data['coupon_send_note_guid'] : create_guid();
        $send_num              = $data['num'] ?? 1;
        if ($send_num <= 0) {
            $this->error = '优惠券发送数量需要大于等于0:' . $coupon_guid;
            return false;
        }
        $send_value          = $data['value'] ?? 0; //金额如果未指定 则一律通过数量乘以卡券面值来算,
        $db_coupon           = new Coupon();
        $db_coupon_send_note = new CouponSendNote();
        $map                 = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid],
        ];
        $coupon_info         = $db_coupon->where($map)->findOrEmpty();
        if ($coupon_info->isEmpty()) {
            $this->error = '优惠券可能被删除:' . $coupon_guid;
            return false;
        }
        if ($way == 5) {
            //免费领取途径判断
            if ($coupon_info['send_type'] != 2) {
                $this->error = '当前优惠券不支持免费领取:' . $coupon_guid;
                return false;
            }
            if (empty($member_guid)) {
                $this->error = '会员身份为空';
                return false;
            }
            //免费领券 做看是否有超过限制
            if ($coupon_info['receive_rate_limit_type'] != 0 && $coupon_info['receive_rate_limit_num'] > 0) {
                $receive_rate_limit_type = $coupon_info['receive_rate_limit_type'];
                $receive_rate_limit_num  = $coupon_info['receive_rate_limit_num'];
                $send_map                = [
                    ['bid', '=', $bid],
                    ['coupon_guid', '=', $coupon_guid],
                    ['member_guid', '=', $member_guid]
                ];
                $begin_time              = '';
                $end_time                = '';
                $send_rate_limit_array   = [
                    1 => '每天',
                    2 => '每周',
                    3 => '每月',
                    4 => '每季度',
                    5 => '每年',
                    6 => '累计',
                ];
                switch ($receive_rate_limit_type) {
                    //0 不限制 1 每天 2每周 3每月 4每季度 5每年 6 累计
                    case 1: //每天限制
                        $begin_time = date('Y-m-d 00:00:00');
                        $end_time   = date('Y-m-d 00:00:00', strtotime('+1 day'));
                        break;
                    case 2: //每周限制
                        $begin_time = date('Y-m-d 00:00:00', strtotime('this week Monday'));
                        $end_time   = date('Y-m-d 00:00:00', strtotime('+1 week', strtotime($begin_time)));
                        break;
                    case 3: //每月限制
                        $begin_time = date('Y-m-01 00:00:00');
                        $end_time   = date('Y-m-d 00:00:00', strtotime('+1 month', strtotime($begin_time)));
                        break;
                    case 4: //每季度限制
                        $season     = ceil((date('n')) / 3); //当月是第几季度
                        $start_time = date('Y-01-01 00:00:00', mktime(0, 0, 0, $season * 3 - 3 + 1, 1, date('Y')));
                        $end_time   = (date("Y-m-d 00:00:00", strtotime('+3 month', strtotime($start_time))));
                        break;
                    case 5: //每年限制
                        $begin_time = date('Y-01-01 00:00:00');
                        $end_time   = date('Y-m-d 00:00:00', strtotime('+1 year', strtotime($begin_time)));
                        break;
                    case 6:
                        //累计限制 无需加时间条件
                        break;
                    default:
                        $this->error = '暂时不支持的时间限制';
                        return false;
                }
                if ($begin_time && $end_time) {
                    $send_map[] = ['create_time', '>=', $begin_time];
                    $send_map[] = ['create_time', '<', $end_time];
                }
                $lock_send_map = $lock_instance->get_lock(md5(json_encode($send_map, JSON_UNESCAPED_UNICODE)));
                $is_send_num   = $db_coupon_send_note->where($send_map)->sum('send_num');
                $lock_instance->unlock($lock_send_map);
                if ($is_send_num >= $receive_rate_limit_num) {
                    $this->error = '领取失败:每人' . $send_rate_limit_array[$receive_rate_limit_type] . '限制领取' . $receive_rate_limit_num . '张,您已经领取' . $is_send_num . '张';
                    return false;
                }
            }
        }
        if (empty($code)) {
            $lock_key = __FUNCTION__ . $bid;
            $lock     = $lock_instance->get_lock($lock_key);
            do {
                //先查找是否有可使用的券
                $code     = mt_rand(10000000, 99999999);
                $map      = [
                    ['bid', '=', $bid],
                    ['code', '=', $code],
                ];
                $is_exist = $db_coupon_send_note->where($map)->value('id');
            } while ($is_exist);
        }

        $password         = mt_rand(100000, 999999);
        $db_user          = new User();
        $operator_user_id = $operator_user_id ?: $db_user->get_default_user_id($bid);
        $owner_user_id    = !empty($data['owner_user_id']) ? $data['owner_user_id'] : $operator_user_id;
        $coupon_type      = $coupon_info['type']; // 1数量卡 2金额卡 3充值卡
        if ($send_value == 0 && in_array($coupon_type, [2, 3])) {
            //如果没有指定发送金额, 且卡券是金额卡或者充值卡 则用优惠券的面值乘以数量作为发送金额
            $send_value = tools()::nc_price_calculate($coupon_info['value'], '*', $send_num, 2);
        }
        if (empty($data['expire_time'])) {
            $expire_time = $this->get_after_expire_time($coupon_info);
        } else {
            $expire_time = $data['expire_time'];
        }
        $availability_time = $data['availability_time'] ?? date('Y-m-d');
        $insert_data       = [
            'guid'                 => $coupon_send_note_guid,
            'way'                  => $way, //线上购买
            'bid'                  => $bid,
            'coupon_guid'          => $coupon_guid,
            'generate_guid'        => null,
            'batch'                => 0,
            'send_time'            => format_timestamp(),
            'operator_user_id'     => $operator_user_id, //生成人 或者发送人
            'code'                 => $code,
            'password'             => $password,
            'owner_user_id'        => $owner_user_id, //支持传入归属者 用于电子卡密限制用户后 默认归属第一个用户
            'availability_time'    => $availability_time,
            'expire_time'          => $expire_time,
            'send_num'             => $send_num,
            'send_value'           => $send_value,
            'used_rate_limit_type' => $coupon_info['used_rate_limit_type'],
            'used_rate_limit_num'  => $coupon_info['used_rate_limit_num'],
            'status'               => 0, //待使用
            'send_remark'          => $send_remark,
        ];
        if ($member_guid) {
            $insert_data['member_guid'] = $member_guid;
        }
        if ($mobile) {
            $insert_data['mobile'] = $mobile;
        }
        $result = $db_coupon_send_note->save($insert_data);
        $lock && $lock_instance->unlock($lock);
        $map                              = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_note_guid],
        ];
        $insert_data['coupon_info_array'] = $coupon_info;
        $insert_data['send_num']          = $send_num;
        $insert_data['send_value']        = $send_value;
        $insert_data['coupon_name']       = $coupon_info['name'];
        logToFile('model进程:' . getmypid() . '$task_id=' . $task_id . ';$batch_task_id=' . $batch_task_id . ';' . __LINE__ . '行;耗时:' . get_diff_time($start_time));
        notify()->set_key_name(NotifyService::CodeSendSuccessfully)->set_member_mobile($mobile)->set_member_guid($member_guid)->set_data($insert_data)->set_map($map)->set_bid($bid)->send();
        logToFile('model进程:' . getmypid() . '$task_id=' . $task_id . ';$batch_task_id=' . $batch_task_id . ';' . __LINE__ . '行;耗时:' . get_diff_time($start_time));
        return $insert_data;
    }

    /**
     * @throws Exception
     */
    public function get_after_expire_time($coupon_info, $map_coupon_send_note = [], $always_return_expire_time = false)
    {
        if (!empty($map_coupon_send_note)) {
            //如果有条件必需所有的卡券 expire_time 都是null 才往下, 如果全是有值 就返回false 不做修改, 有有值 有空则报错
            $db_coupon_send_note             = new CouponSendNote();
            $map_coupon_send_note_null       = $map_coupon_send_note;
            $map_coupon_send_note_null[]     = ['expire_time', 'NULL', null];
            $has_null_expire_time            = $db_coupon_send_note->where($map_coupon_send_note_null)->value('id');
            $map_coupon_send_note_not_null   = $map_coupon_send_note;
            $map_coupon_send_note_not_null[] = ['expire_time', 'NOT NULL', null];
            $has_not_null_expire_time        = $db_coupon_send_note->where($map_coupon_send_note_not_null)->value('id');
            if ($has_null_expire_time && $has_not_null_expire_time && !$always_return_expire_time) {
                throw new Exception('有部分卡券的存在过期时间,部分不存在过期时间,无法统一处理');
            }
            if (!$has_null_expire_time && $has_not_null_expire_time && !$always_return_expire_time) {
                return false;
            }
        }
        $expire_time_type = $coupon_info['expire_time_type'];
        switch ($expire_time_type) {
            case 0: //系统默认 当年年底
                $expire_time = date('Y-12-31 23:59:59');
                break;
            case 1: //N天后有效
                $expire_days = $coupon_info['expire_days'];
                $expire_time = date('Y-m-d 23:59:59', strtotime("+$expire_days day"));
                break;
            case 2: //固定日期到期
                $expire_time = $coupon_info['expire_time'];
                break;
            default:
                $expire_time = date('Y-12-31 23:59:59');
                break;
        }
        return $expire_time;
    }

    /**
     * 还原卡券
     * @access public
     * @param array $data
     * @return mixed
     */
    public function undo(array $data)
    {
        $coupon_send_guid = $data['coupon_send_guid'];
        $bid              = $this->get_bid_from_request();
        $map              = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_guid]
        ];
        $db               = new CouponSendNote();
        $coupon_send_note = $db->where($map)->findOrFail();
        if (!$coupon_send_note) {
            $this->error = '发送记录不存在';
            return false;
        }
        if (!is_null($coupon_send_note['revoke_time'])) {
            $this->error = '还原失败:当前记录已被撤销过';
            return false;
        }
        if ($coupon_send_note['used_way'] != 3) {
            $this->error = '还原失败:暂时仅支持还原商城储值的卡券';
            return false;
        }
        if ($coupon_send_note['status'] != 1) {
            $this->error = '还原失败:只能还原已使用过的卡券';
            return false;
        }
        if (!$coupon_send_note['used_time']) {
            $this->error = '还原失败:该券号未使用过,无需还原';
            return false;
        }
        $relation_guid        = $coupon_send_note['relation_guid'];
        $db_member_money_note = new MemberMoneyNote();
        $db_member_money_note->undo($bid, $relation_guid); //执行储值撤销
        //还原卡券发送记录
        $update_data = [
            'used_way'      => 0,
            'status'        => 0,
            'used_mobile'   => '',
            'member_guid'   => '',
            'relation_guid' => '',
            'used_time'     => null,
        ];
        return $db::update($update_data, $map);
    }

    /**
     * 作废卡券
     * @access public
     * @param array $data
     * @return mixed
     */
    public function revoke(array $data)
    {
        $coupon_send_guid = $data['coupon_send_guid'];
        $bid              = $this->get_bid_from_request();
        $map              = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_guid]
        ];
        $db               = new CouponSendNote();
        $coupon_send_note = $db->where($map)->find();
        if (!$coupon_send_note) {
            $this->error = '发送记录不存在';
            return false;
        }
        if (!is_null($coupon_send_note['revoke_time'])) {
            $this->error = '撤销失败:当前记录已被撤销过';
            return false;
        }
        if ($coupon_send_note['used_time']) {
            $this->error = '撤销失败:当前记录已被使用过';
            return false;
        }
        if ($coupon_send_note['status'] != 0) {
            $this->error = '撤销失败:只能撤销未使用记录';
            return false;
        }
        $update_data = [
            'revoke_time' => format_timestamp()
        ];
        return $db::update($update_data, $map);
    }


    public function getValueAttr($value, $data)
    {
        return floatval($data['value']);
    }

    public function getTypeTextAttr($value, $data)
    {
        $type = [
            1 => '实体卡',
            2 => '电子卡'
        ];
        return $type[$data['type']];
    }
}
