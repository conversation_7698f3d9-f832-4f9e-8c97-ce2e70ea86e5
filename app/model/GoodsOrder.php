<?php

namespace app\model;


use app\common\exceptions\NotNotifyException;
use app\model\Cart as CartModel;
use app\model\ExtendField as ExtendFieldModel;
use app\model\Goods as GoodsModel;
use app\model\GoodsOrder as GoodsOrderModel;
use app\common\service\ExpressService;
use app\common\service\NotifyService;
use app\common\tools\AES_256_CBC;
use app\common\tools\Excel;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;

class GoodsOrder extends ModelBase
{
    protected $pk = 'guid';
    protected $type = [
        'status'            => 'integer',
        'goods_info'        => 'json',
        'remark_image_list' => 'json',
        'goods_item_guid'   => 'json',
    ];
    protected $jsonAssoc = true;

    /**
     * 获取状态
     *
     * @param array $data
     * @return mixed
     * <AUTHOR>
     * @dateTime 2016-04-19T16:00:40+0800
     */
    public function getOrderDetailAttr($value, $data)
    {
        $bid  = $data['bid'];
        $guid = $data['guid'];
        return $this->get_order_detail($bid, $guid);
    }

    /**
     * 获取商品详情
     *
     * @param array $data
     * @return mixed
     * <AUTHOR>
     * @dateTime 2016-04-19T16:00:40+0800
     */
    public function getGoodsInfoTextAttr($value, $data)
    {
        $goods_info = json_decode($data['goods_info'], true);
        return $this->get_goods_list_text($goods_info);
    }

    public function create_express_order_and_send_out_goods($bid, $order_guid, $express_config_guid)
    {
        //后续优化成异步并行处理
        $db_goods_order = new GoodsOrderModel();
        $map            = [
            ['bid', '=', $bid],
            ['status', '=', 0],
            ['guid', '=', $order_guid],
        ];
        $order_info     = $db_goods_order->where($map)->findOrEmpty();
        if ($order_info->isEmpty()) {
            return ['success' => false, 'msg' => '订单不存在或者状态不是待发货'];
        }
        $deduct_result = $db_goods_order->deduct_send_out_goods_paid_user_money($bid, $order_guid, $order_info);
        if ($deduct_result !== true) {
            return ['success' => false, 'msg' => '订单号:' . $order_info['bill_number'] . ',原因:' . $db_goods_order->getError()];
        }
        try {
            $instance = ExpressService::get_instance($bid);
            $result   = $instance->create_order($order_guid, $express_config_guid);
            if ($result === false) {
                return ['success' => false, 'msg' => '订单号:' . $order_info['bill_number'] . ',原因:' . $instance->message];
            }
        } catch (Exception | Throwable $e) {
            return ['success' => false, 'msg' => '订单号:' . $order_info['bill_number'] . ',原因:' . $e->getMessage()];
        }
        //如果成功,进行发货逻辑
        $express_data = ['express_code' => $result['express_code'], 'express_no' => $result['express_no']];
        $result       = $db_goods_order->send_out_goods($bid, $order_guid, $express_data, 3);
        return ['success' => true, 'msg' => '电子面单发货成功'];
    }

    public function get_goods_list_text($goods_info)
    {
        $text = '';
        if (!empty($goods_info)) {
            foreach ($goods_info as $info) {
                $text .= '【' . $info['name'];
                if (!empty($info['attr_list'])) {
                    $text .= '/';
                    foreach ($info['attr_list'] as $attr_list) {
                        $text .= '[' . $attr_list['attr_name'] . ']';
                    }
                }
                if (!empty($info['specs'])) {
                    $text .= '[' . $info['specs'] . ']';
                }
                $text .= ' * ' . $info['amount'] . '】';
            }
        }
        return $text;
    }

    public function getStatusTextAttr($value, $data)
    {
        $status = [
            -3 => '已退款',
            -2 => '已取消',
            -1 => '待付款',
            0  => '待发货',
            1  => '待收货',
            2  => '已完成',
        ];
        return $status[$data['status']];
    }

    protected function append_extra_field($bid, $header)
    {
        //处理扩展字段
        $config                         = get_config_by_bid($bid);
        $goods_order_export_field       = $config['goods_order_export_field'];
        $goods_order_export_field_array = explode(',', $goods_order_export_field);
        $extra_field_mapping            = [
            'province_name'    => '省',
            'city_name'        => '市',
            'area_name'        => '区',
            'address'          => '详细地址',
            'total_amount'     => '数量',
            'code_send_remark' => '卡号备注',
            'code_mobile'      => '卡号手机号',
        ];
        foreach ($goods_order_export_field_array as $field) {
            foreach ($extra_field_mapping as $key => $val) {
                if ($field == $val) {
                    $header[$key] = $val;
                }
            }
        }
        return $header;
    }

    protected function append_extend_field($bid, $header)
    {
        //处理扩展字段
        $db_extend_field         = new ExtendFieldModel();
        $map_extend_field        = [
            ['bid', '=', $bid],
            ['status', '=', 1],
        ];
        $show_extend_field_array = $db_extend_field->where($map_extend_field)->order(['key_name' => 'ASC'])->select();
        foreach ($show_extend_field_array as $key => $val) {
            $key_name          = $val['key_name'];
            $name              = $val['name'];
            $header[$key_name] = $name;
        }
        return $header;
    }

    protected function has_auth_gift_card($bid = null, $user_guid = null)
    {
        //判断是否有多包裹发货权限
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::GIFT_CARD);
    }

    protected function has_auth_multiple_express_send_out_goods($bid = null, $user_guid = null)
    {
        //判断是否有赠礼卡权限
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::MULTIPLE_EXPRESS_SEND_OUT_GOODS);
    }

    protected function has_auth_cost_price($bid = null, $user_guid = null)
    {
        //判断是否有成本价权限
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::COST_PRICE);
    }

    protected function has_auth_member_brokerage_note_index($bid = null, $user_guid = null)
    {
        //判断是否批量更新物流状态的权限
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::MEMBER_BROKERAGE_NOTE_INDEX);
    }

    protected function has_auth_batch_update_express_route($bid = null, $user_guid = null)
    {
        //判断是否批量更新物流状态的权限
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $db_rule   = new Rule();
        return $db_rule->check_rule($bid, $user_guid, Rule::BATCH_UPDATE_EXPRESS_ROUTE);
    }

    public function build_item_query($bid = null, $user_guid = null, $params = [])
    {
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $order     = ['go.id' => 'DESC'];
        $join      = [
            ['goods_order_item goi', 'goi.bid = go.bid AND goi.order_guid = go.guid'],
            ['express e', 'go.express_code = e.code', 'LEFT'],
            ['goods g', 'goi.bid = g.bid AND goi.goods_guid= g.guid'],
            ['user u', 'u.bid = g.bid AND u.guid= g.owner_user_guid', 'LEFT'],
            ['area a1', 'go.province_id = a1.id', 'LEFT'],
            ['area a2', 'go.city_id = a2.id', 'LEFT'],
            ['area a3', 'go.area_id = a3.id', 'LEFT'],
        ];

        $field = [
            'go.bill_number'                                        => 'bill_number',
            'go.create_time'                                        => 'create_time',
            'go.status'                                             => 'status',
            'goi.goods_name'                                        => 'goods_name',
            'g.specs'                                               => 'goods_specs',
            'goi.amount'                                            => 'amount',
            'goi.goods_price'                                       => 'price',
            'go.mobile'                                             => 'mobile',
            'go.true_name'                                          => 'true_name',
            'go.remark'                                             => 'remark',
            "IFNULL(CONCAT(a1.name,a2.name,a3.name,go.address),'')" => 'address_info',
            'a1.name'                                               => 'province_name',
            'a2.name'                                               => 'city_name',
            'a3.name'                                               => 'area_name',
            'go.address'                                            => 'address',
            "CONCAT(u.account,'(',u.name,')')"                      => 'owner_user_info',
            'go.extend_field_1'                                     => 'extend_field_1',
            'go.extend_field_2'                                     => 'extend_field_2',
            'go.extend_field_3'                                     => 'extend_field_3',
            'go.express_no'                                         => 'express_no',
            'e.name'                                                => 'express_name',
        ];
        if (!empty($params['user_id']) || !empty($params['key']) && $params['key'] == 'code') {
            $join[]                     = ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'];
            $field['csn.owner_user_id'] = 'user_id';
            $field['csn.code']          = 'code';
            $field['csn.send_remark']   = 'code_send_remark';
            $field['csn.mobile']        = 'code_mobile';
        } else {
            $config                         = get_config_by_bid($bid);
            $goods_order_export_field       = $config['goods_order_export_field'];
            $goods_order_export_field_array = explode(',', $goods_order_export_field);
            if (!empty(array_intersect($goods_order_export_field_array, ['卡号备注', '卡号手机号']))) {
                $join[]                   = ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'];
                $field['csn.send_remark'] = 'code_send_remark';
                $field['csn.mobile']      = 'code_mobile';
            }
        }
        $db_business = new Business();
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(go.mobile,4,4,'****') as mobile");
        }
        if ($this->has_auth_cost_price()) {
            $field['g.cost_price'] = 'cost_price';
        }
        $db_user = new User();
        $map     = [
            ['go.bid', '=', $bid],
            ['go.delete_time', 'NULL', NULL],
            ['go.status', 'NOT IN', [-1, -2]], //不展示已取消订单
            ['go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildUserIdArray()], // 只显示自己范围内的订单
        ];
        if (!empty($params['key']) && $params['key'] == 'bill_number_or_code' && !empty($params['value'])) {
            $bill_number_or_code = $params['value'];
            $bill_number_reg     = '/^20\d{18}(-\d+)?$/';
            if (strpos($bill_number_or_code, ',') !== false) {
                //多个用,隔开
                $condition             = 'IN';
                $bill_number_or_code   = explode(',', $bill_number_or_code);
                $is_bill_number_search = preg_match($bill_number_reg, current($bill_number_or_code)) === 1;
            } else {
                $condition             = '=';
                $is_bill_number_search = preg_match($bill_number_reg, $bill_number_or_code) === 1;
            }
            if ($is_bill_number_search) {
                $map[] = ['go.bill_number', $condition, $bill_number_or_code];
            } else {
                $map[]                      = ['csn.code', $condition, $bill_number_or_code];
                $join[]                     = ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'];
                $field['csn.owner_user_id'] = 'user_id';
                $field['csn.code']          = 'code';
                $field['csn.send_remark']   = 'code_send_remark';
                $field['csn.mobile']        = 'code_mobile';
            }

            unset($params['key']);
            unset($params['value']);
        }
        $db_goods_order = new GoodsOrderModel();
        $model          = $db_goods_order->alias('go')->where($map)->join($join)->field($field)->order($order)->append(['status_text']);
        return ['params' => $params, 'model' => $model];
    }


    public function build_query($bid = null, $user_guid = null, $params = [])
    {
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $config    = get_config_by_bid($bid);
        //判断是否有多包裹发货权限
        $has_auth_multiple_express_send_out_goods = $this->has_auth_multiple_express_send_out_goods($bid, $user_guid);
        //判断是否批量更新物流状态的权限
        $has_auth_batch_update_express_route  = $this->has_auth_batch_update_express_route($bid, $user_guid);
        $has_auth_member_brokerage_note_index = $this->has_auth_member_brokerage_note_index($bid, $user_guid);

        $order = ['id' => 'DESC'];
        $join  = [
            ['member m', 'go.member_guid = m.guid and go.bid = m.bid', 'LEFT'],
            ['coupon c', 'go.bid = c.bid and go.coupon_guid = c.guid', 'LEFT'],
            ['coupon_send_note csn', 'go.bid = csn.bid and go.coupon_send_note_guid = csn.guid', 'LEFT'],
            ['user u', 'csn.bid = u.bid and csn.owner_user_id = u.id', 'LEFT'],
            ['express e', 'go.express_code = e.code', 'LEFT'],
            ['area a1', 'go.province_id = a1.id', 'LEFT'],
            ['area a2', 'go.city_id = a2.id', 'LEFT'],
            ['area a3', 'go.area_id = a3.id', 'LEFT'],
            //            ['member m2', 'm.share_member_guid = m2.guid and m.bid = m2.bid', 'LEFT'],
            //            ['member_brokerage_note mbn', 'go.guid = mbn.relation_guid and go.bid = mbn.bid', 'LEFT'],
            //   ['store s','go.request_send_or_pick_up_store_guid = s.guid and go.bid = s.bid','LEFT']
        ];

        $field = [
            'c.name' => 'coupon_name',
            'go.id',
            'go.guid',
            'go.way',
            'go.bill_number',
            'go.parent_bill_number',
            'go.cycle_times',
            'go.type',
            'go.owner_user_id',
            'go.request_send_or_pick_up_store_guid',
            'go.total_money',
            'go.discount_preferential_money',
            'go.goods_money',
            'go.paid_wechat',
            'go.paid_money',
            'go.paid_point',
            'go.freight_money',
            'go.extra_charges_money',
            'go.true_name',
            'go.mobile',
            'go.id_card_number',
            'go.remark',
            'go.status',
            'go.create_time',
            'go.send_or_pick_up_time',
            'go.send_out_goods_remark',
            'go.request_send_or_pick_up_time',
            'go.print_times',
            'go.send_type',
            'go.send_or_pick_up_user_guid',
            'go.express_no',
            'go.extend_field_1',
            'go.extend_field_2',
            'go.extend_field_3',
            'go.goods_info',
            'go.total_amount',

            'e.name'                                                => 'express_name',
            //          'm.name'                                     =>'share_member_name',
            "CONCAT(m.id,'-', IFNULL(m.name,''))"                   => 'member_name',
            //            "CONCAT(m2.id,'-',IFNULL(m2.name,''))"                  => 'share_member_name',
            //            "IFNULL(mbn.brokerage,0)"                               => 'brokerage_money',
            'csn.code',
            'csn.send_remark'                                       => 'code_send_remark',
            'csn.mobile'                                            => 'code_mobile',
            'csn.owner_user_id'                                     => 'user_id',
            'go.owner_user_id'                                      => 'order_owner_user_id',
            "CONCAT(u.name,'-',u.account)"                          => 'owner_user_info',
            'go.send_or_pick_up_user_id'                            => 'send_or_pick_up_user_id',
            'a1.name'                                               => 'province_name',
            'a2.name'                                               => 'city_name',
            'a3.name'                                               => 'area_name',
            'go.address'                                            => 'address',
            "IFNULL(CONCAT(a1.name,a2.name,a3.name,go.address),'')" => 'address_info',
        ];
        if ($has_auth_member_brokerage_note_index) {
            $join[]                                        = ['member m2', 'm.share_member_guid = m2.guid and m.bid = m2.bid', 'LEFT'];
            $join[]                                        = ['member_brokerage_note mbn', 'go.guid = mbn.relation_guid and go.bid = mbn.bid AND mbn.level=1', 'LEFT'];
            $field["CONCAT(m2.id,'-',IFNULL(m2.name,''))"] = 'share_member_name';
            $field["IFNULL(mbn.brokerage,0)"]              = 'brokerage_money';
            //            "CONCAT(m2.id,'-',IFNULL(m2.name,''))"                  => 'share_member_name',
            //            "IFNULL(mbn.brokerage,0)"                               => 'brokerage_money',
        }
        if ($config['is_show_request_send_or_pick_up_store'] == 1) {
            $join[]                = ['store s', 'go.request_send_or_pick_up_store_guid = s.guid and go.bid = s.bid', 'LEFT'];
            $field["s.store_name"] = 'request_send_or_pick_up_store_name';
        }
        if ($has_auth_multiple_express_send_out_goods) {
            $join[]                              = ['express e2', 'go.express_code_2 = e2.code', 'LEFT'];
            $field['e2.name']                    = 'express_name_2';
            $field['go.express_no_2']            = 'express_no_2';
            $field['go.send_out_goods_remark_2'] = 'send_out_goods_remark_2';
        }
        if ($has_auth_batch_update_express_route) {
            $field['express_no_route_status']           = 'express_no_route_status';
            $field['express_no_route_last_update_time'] = 'express_no_route_last_update_time';
            if ($has_auth_multiple_express_send_out_goods) {
                $field['express_no_2_route_status']           = 'express_no_2_route_status';
                $field['express_no_2_route_last_update_time'] = 'express_no_2_route_last_update_time';
            }
        }
        $db_business = new Business();
        if ($db_business->is_examples_bid($bid)) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(go.mobile,4,4,'****') as mobile");
        }
        $db_user = new User();
        $map     = [
            ['go.bid', '=', $bid],
            ['go.status', 'NOT IN', [-1, -2]], //不展示已取消订单
            ['go.delete_time', 'null', null],
            ['csn.owner_user_id|go.owner_user_id|go.send_or_pick_up_user_id', 'IN', $db_user->getChildUserIdArray($bid, $user_guid)], // 只显示自己范围内的订单
        ];
        if (isset($params['print_status'])) {
            switch ($params['print_status']) {
                case 0:
                    $map[] = ['print_times', '=', 0];
                    break;
                case 1:
                    $map[] = ['print_times', '>', 0];
                    break;
                default:
            }
            unset($params['print_status']);
        }
        if (!empty($params['key']) && $params['key'] == 'bill_number_or_code' && !empty($params['value'])) {
            $bill_number_or_code = $params['value'];
            $bill_number_reg     = '/^20\d{18}(-\d+)?$/';
            if (strpos($bill_number_or_code, ',') !== false) {
                //多个用,隔开
                $bill_number_or_code_array = explode(',', $bill_number_or_code);
                if (preg_match($bill_number_reg, current($bill_number_or_code_array)) === 1) {
                    $map[] = ['go.bill_number', 'IN', $bill_number_or_code_array];
                } else {
                    $map[] = ['csn.code', 'IN', $bill_number_or_code_array];
                }
            } else {
                if (preg_match($bill_number_reg, $bill_number_or_code) === 1) {
                    $map[] = ['go.bill_number', '=', $bill_number_or_code];
                } else {
                    $map[] = ['csn.code', '=', $bill_number_or_code];
                }
            }

            unset($params['key']);
            unset($params['value']);
        }

        $db_goods_order = new GoodsOrderModel();
        $model          = $db_goods_order->alias('go')->where($map)->join($join)->field($field)->order($order)->append(['goods_info_text', 'status_text']);
        if (isset($params['append_order_detail'])) {
            unset($params['append_order_detail']);
            //            $this->model = $this->model->append(['order_detail']);
        }
        return ['params' => $params, 'model' => $model];
    }


    public function export_data($data, $bid = null, $user_guid = null)
    {
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $config    = get_config_by_bid($bid);
        if (empty($user_guid)) {
            $db_user   = new User();
            $user_guid = $db_user->get_default_user_guid($bid);
        }
        $header                                   = [
            'bill_number'           => '订单号',
            'status_text'           => '状态',
            'goods_info_text'       => '商品信息',
            //            'total_amount'          => '数量',
            'create_time'           => '下单时间',
            'send_or_pick_up_time'  => '发货时间',
            'express_name'          => '快递公司',
            'express_no'            => '快递单号',
            'send_out_goods_remark' => '发货备注',
        ];
        $has_auth_multiple_express_send_out_goods = $this->has_auth_multiple_express_send_out_goods($bid, $user_guid);
        if ($has_auth_multiple_express_send_out_goods) {
            $header['express_name_2']          = '快递公司2';
            $header['express_no_2']            = '快递单号2';
            $header['send_out_goods_remark_2'] = '发货备注2';
        }
        //处理扩展字段
        $header = $this->append_extra_field($bid, $header);
        $header = $this->append_extend_field($bid, $header);
        //自动增加支付金额列
        $is_not_empty_or_zero_filed           = [
            'owner_user_info'              => '归属者',
            'coupon_name'                  => '卡券名称',
            'mobile'                       => '手机号',
            'true_name'                    => '姓名',
            'address_info'                 => '地址',
            'id_card_number'               => '身份证号',
            'request_send_or_pick_up_time' => '预约时间',
            'code'                         => '券号',
            'remark'                       => '备注',
            //            'share_member_name'            => '分销者',
            //            'brokerage_money'              => '佣金金额',
            'total_money'                  => '订单金额',
            'freight_money'                => '运费',
            'extra_charges_money'          => '附加费',
            'paid_money'                   => '储值支付',
            'paid_point'                   => '积分支付',
            'paid_wechat'                  => '微信支付',
            'code_send_remark'             => '卡券备注',
            'code_mobile'                  => '卡券手机号',
        ];
        $has_auth_member_brokerage_note_index = $this->has_auth_member_brokerage_note_index($bid, $user_guid);
        if ($has_auth_member_brokerage_note_index) {
            $is_not_empty_or_zero_filed['share_member_name'] = '分销者';
            $is_not_empty_or_zero_filed['brokerage_money']   = '佣金金额';
        }
        if ($config['is_show_request_send_or_pick_up_store'] == 1) {
            $is_not_empty_or_zero_filed['request_send_or_pick_up_store_name'] = '自提门店';
        }
        foreach ($is_not_empty_or_zero_filed as $key => $value) {
            foreach ($data as $k => $v) {
                if (!tools()::is_empty_or_zero($v[$key])) {
                    $header[$key] = $value;
                    break;
                }
            }
        }
        $file = new Excel();
        return $file->arrayToExcel($header, $data, '商品订单_' . date('Y-m-d-H-i-s'));
    }


    public function export_order_item_data($data, $bid = null, $user_guid = null)
    {
        $bid       = $bid ?: $this->get_bid_from_request();
        $user_guid = $user_guid ?: $this->get_user_guid_from_request();
        $header    = [
            'bill_number'     => '订单号',
            'goods_name'      => '商品名',
            'owner_user_info' => '供应商',
            'goods_specs'     => '规格',
            'amount'          => '件数',
            'price'           => '单价',
            'mobile'          => '手机号',
            'true_name'       => '姓名',
            'address_info'    => '地址',
            'express_no'      => '快递单号',
            'express_name'    => '快递公司',
            'remark'          => '备注',
            'create_time'     => '下单时间',
            'status_text'     => '状态',
        ];
        if ($this->has_auth_cost_price($bid, $user_guid)) {
            tools()::insert_array_key_value($header, 'cost_price', '成本价', 'price');
        }
        //处理扩展字段
        $header = $this->append_extra_field($bid, $header);
        $header = $this->append_extend_field($bid, $header);
        $file   = new Excel();
        return $file->arrayToExcel($header, $data, '商品订单明细_' . date('Y-m-d-H-i-s'));
    }

    public function get_notify_user_guid_array($bid, $order_guid)
    {
        $user_guid_array = [];
        $db_goods_order  = new GoodsOrder();
        $db_user         = new User();
        $map_goods_order = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $order_info      = $db_goods_order->where($map_goods_order)->findOrEmpty();
        if (!empty($order_info['user_guid'])) {
            //user_guid   订单创建用户guid
            $user_guid_array[] = $order_info['user_guid'];
        }
        if (!empty($order_info['owner_user_guid'])) {
            // owner_user_guid   订单归属用户 guid, 用于指派发货
            $user_guid_array[] = $order_info['owner_user_guid'];
        }
        if (!empty($order_info['coupon_send_note_guid'])) {
            //coupon_send_note_guid 卡券发送记录的归属用户id 用于分销商逻辑
            $db_coupon_send_note            = new CouponSendNote();
            $map_coupon_send_note           = [
                ['bid', '=', $bid],
                ['guid', '=', $order_info['coupon_send_note_guid']],
            ];
            $coupon_send_note_owner_user_id = $db_coupon_send_note->where($map_coupon_send_note)->value('owner_user_id');
            if ($coupon_send_note_owner_user_id) {
                $coupon_send_note_owner_user_guid = $db_user->get_user_guid_by_id($coupon_send_note_owner_user_id, $bid);
                $user_guid_array[]                = $coupon_send_note_owner_user_guid;
            }
        }
        $auth_path = 'goods_order/index';
        return $db_user->get_data_range_user_guid_list($bid, $user_guid_array, $auth_path);
    }

    public function filter_express_no($express_no)
    {
        $express_no = str_replace(' ', '', trim($express_no));
        $express_no = tools()::remove_empty_string($express_no); //替换空格 换行符 等特殊字符
        //提取第一个和最后一个字符串的符号,根据 数据库中记录经常容易复制到符号
        return trim($express_no, ';；,，:：.。&?');
    }

    /**
     * @param string $bid
     * @param string $order_guid_or_bill_number
     * @param array $express_data //物流数据
     * @param int $send_type //发货类型 1 输入单号发货 2 导入excel  3 电子面单 4到店核销
     * @return bool
     * @throws Exception|Throwable
     */
    public function send_out_goods(string $bid, string $order_guid_or_bill_number, array $express_data, int $send_type, $user_guid = null, $user_id = 0)
    {
        $user_guid    = $user_guid ?: $this->get_user_guid_from_request();
        $user_id      = $user_id ?: $this->get_user_id_from_request();
        $express_code = $express_data['express_code'] ?? null;
        $express_no   = $express_data['express_no'] ?? null;
        $return_msg   = $express_data['return_msg'] ?? false;
        $replace      = $express_data['replace'] ?? false; //是否强行覆盖

        $express_no = $this->filter_express_no($express_no);
        if ($express_code) {
            //删除前后和中间空格
            $express_code = str_replace(' ', '', trim($express_code));
        }
        $express_index         = $express_data['express_index'] ?? 1;
        $send_out_goods_remark = $express_data['send_out_goods_remark'] ?? '';

        $field_suffix                         = $express_index > 1 ? '_' . $express_index : '';
        $express_code_field_name              = 'express_code' . $field_suffix;
        $express_no_field_name                = 'express_no' . $field_suffix;
        $send_out_goods_remark_field_name     = 'send_out_goods_remark' . $field_suffix;
        $send_or_pick_up_time_field_name      = 'send_or_pick_up_time' . $field_suffix;
        $send_or_pick_up_user_id_field_name   = 'send_or_pick_up_user_id' . $field_suffix;
        $send_or_pick_up_user_guid_field_name = 'send_or_pick_up_user_guid' . $field_suffix;
        $db_goods_order                       = new GoodsOrder();
        $map                                  = [
            ['bid', '=', $bid],
            //            ['status', '=', 0],
        ];
        if (tools()::is_guid($order_guid_or_bill_number)) {
            $map[] = ['guid', '=', $order_guid_or_bill_number];
        } else {
            $map[] = ['bill_number', '=', $order_guid_or_bill_number];
        }
        $order_info = $db_goods_order->field(['guid', 'coupon_guid', 'coupon_send_note_guid', 'status', 'send_type', $express_no_field_name])->where($map)->findOrEmpty();
        if ($order_info->isEmpty()) {
            $this->error = '订单不存在';
            return $return_msg ? '单号:' . $order_guid_or_bill_number . ':' . $this->error : false;
        }
        $order_status = $order_info['status'];
        $order_guid   = $order_info['guid'];

        //        if ($order_status != 0 && $express_index == 1) {
        //            //如果是第一个单号 必须是待发货状态才能发送
        //            $this->error = '订单不是待发货状态';
        //            return false;
        //        }
        //        if ($order_status == 0 && $express_index > 1) {
        //            //如果是第一个单号 必须是待发货状态才能发送
        //            $this->error = '请先发货第一个单号';
        //            return false;
        //        }
        if (!$replace && $order_status != 0 && !in_array($order_info['send_type'], [1, 2]) && $express_index == 1) {
            $this->error = '订单不是待发货状态!';
            return $return_msg ? '单号:' . $order_guid_or_bill_number . ':' . $this->error : false;
        }
        $update_data = [
            $send_or_pick_up_user_id_field_name   => $user_id,
            $send_or_pick_up_user_guid_field_name => $user_guid,
            $send_or_pick_up_time_field_name      => format_timestamp(),
        ];
        if ($order_status != 1) {
            $update_data['status'] = 1;
        }
        if ($order_info['send_type'] == 0) {
            $update_data['send_type'] = $send_type;
        }
        $map[] = ['status', '=', $order_status];
        if (!tools()::is_guid($order_guid_or_bill_number)) {
            $map[] = ['guid', '=', $order_guid];
        }
        if ($express_no) {
            if (!empty($order_info[$express_no_field_name]) && !$replace) {
                $this->error = '该订单已经发货过包裹' . $express_index;
                return $return_msg ? '单号:' . $order_guid_or_bill_number . ':' . $this->error : false;
            }
            $update_data[$express_no_field_name] = $express_no;
        }
        if ($express_code) {
            $update_data[$express_code_field_name] = $express_code;
        }
        if ($send_out_goods_remark) {
            $update_data[$send_out_goods_remark_field_name] = $send_out_goods_remark;
        }
        if ($send_type != 3) {
            //电子面单会单独调用扣费
            $result = $this->deduct_send_out_goods_paid_user_money($bid, $order_guid, $order_info);
            if ($result !== true) {
                return $return_msg ? '单号:' . $order_guid_or_bill_number . ':' . $this->error : false;
            }
        }
        $db_goods_order::update($update_data, $map);
        if (!$replace) {
            $order_data = ['bid' => $bid, 'guid' => $order_info['guid'], 'express_index' => $express_index];
            job()->set_job_name('GoodsOrder@after_send_out_goods')->push_job(['order_data' => $order_data]);
        }
        return true;
    }

    public function deduct_send_out_goods_paid_user_money($bid, $order_guid, $order_info)
    {
        //如果有coupon_guid则查询coupon表 send_out_goods_paid_user_money 是否>0
        $coupon_send_note_guid = $order_info['coupon_send_note_guid'] ?? '';
        $coupon_guid           = $order_info['coupon_guid'] ?? '';
        if (!empty($coupon_guid)) {
            $db_coupon                      = new Coupon();
            $map_coupon                     = [
                ['bid', '=', $bid],
                ['guid', '=', $coupon_guid]
            ];
            $send_out_goods_paid_user_money = $db_coupon->where($map_coupon)->value('send_out_goods_paid_user_money');
            if ($send_out_goods_paid_user_money > 0) {
                //大于0 则 扣除用户余额
                $db_coupon_send_note = new CouponSendNote();
                $map_send_note_guid  = [
                    ['bid', '=', $bid],
                    ['guid', '=', $coupon_send_note_guid]
                ];
                $owner_user_id       = $db_coupon_send_note->where($map_send_note_guid)->value('owner_user_id');
                if ($owner_user_id > 0) {
                    $db_user            = new User();
                    $owner_user_guid    = $db_user->get_user_guid_by_id($owner_user_id, $bid);
                    $db_user_money_note = new UserMoneyNote();
                    $data               = [
                        'bid'           => $bid,
                        'user_guid'     => $owner_user_guid,
                        'way'           => 4, // 途径 1 后台充值 2后台扣除 3 付费激活卡券扣除 4 发货扣除资金
                        'type'          => -1, //扣除
                        'money'         => $send_out_goods_paid_user_money,
                        'memo'          => '[发货扣除]',
                        'relation_guid' => $order_guid,
                    ];
                    try {
                        $db_user_money_note->recharge_money($data);
                        return true;
                    } catch (Throwable | Exception $e) {
                        $this->error = '发货扣除用户资金失败:' . $e->getMessage();
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * @param $bid
     * @param $order_guid_or_bill_number
     * @param $express_code
     * @param $express_no
     * @param $send_type //发货类型 1 输入单号发货 2 导入excel  3 电子面单 4到店核销
     * @param $express_index //第几个快递单号
     * @return bool
     * @throws Exception|Throwable
     */
    public function send_out_goods_old($bid, $order_guid_or_bill_number, $express_code, $express_no, $send_type, $express_index = 1)
    {
        $db_user        = new User();
        $user_guid      = $this->get_user_guid_from_request();
        $user_id        = $this->get_user_id_from_request();
        $db_goods_order = new GoodsOrder();
        $map            = [
            ['bid', '=', $bid],
            //            ['status', '=', 0],
        ];
        if (tools()::is_guid($order_guid_or_bill_number)) {
            $map[] = ['guid', '=', $order_guid_or_bill_number];
        } else {
            $map[] = ['bill_number', '=', $order_guid_or_bill_number];
        }
        $order_info = $db_goods_order->field(['guid'])->where($map)->findOrEmpty();
        if ($order_info->isEmpty()) {
            $this->error = '订单不存在';
            return false;
        }
        if ($order_info['status'] != 0 && $express_index == 1) {
            //如果是第一个单号 必须是待发货状态才能发送
            $this->error = '订单不是待发货状态';
            return false;
        }

        $update_data             = [
            'status'                    => 1,
            'send_or_pick_up_user_id'   => $user_id,
            'send_or_pick_up_user_guid' => $user_guid,
            'send_or_pick_up_time'      => format_timestamp(),
            'send_type'                 => $send_type
        ];
        $express_code_field_name = 'express_code' . ($express_index > 1 ? '_' . $express_index : '');
        $express_no_field_name   = 'express_no' . ($express_index > 1 ? '_' . $express_index : '');
        if ($express_code) {
            $update_data[$express_code_field_name] = $express_code;
        }
        if ($express_no) {
            $update_data[$express_no_field_name] = $express_no;
        }
        $db_goods_order::update($update_data, $map);
        $order_data = ['bid' => $bid, 'guid' => $order_info['guid']];
        job()->set_job_name('GoodsOrder@after_send_out_goods')->push_job(['order_data' => $order_data]);
        return true;
    }

    public function get_web_express_query_url($order_data, $express_index = 1)
    {
        $bid          = $order_data['bid'];
        $field_suffix = $express_index > 1 ? '_' . $express_index : '';
        $express_no   = $order_data['express_no' . $field_suffix] ?? '';
        $express_code = $order_data['express_code' . $field_suffix] ?? '';
        if (empty($express_no)) {
            //单号为空则返回
            return '';
        }
        //获取电脑端网页查询链接
        $db_express = new Express();
        return $db_express->get_web_express_query_url($express_no, $express_code);
    }

    public function build_goods_info_pic($goods_info)
    {
        if (empty($goods_info)) {
            return $goods_info;
        }
        foreach ($goods_info as $key => $val) {
            $goods_info[$key]['pic'] = tools()::add_thumbnail_mini($val['pic']);
        }
        return $goods_info;
    }

    public function get_express_query_url($order_data, $express_index = 1)
    {
        $request          = request();
        $params           = $request->param();
        $from             = $params['from'] ?? ''; // 目前只有 H5
        $from             = strtoupper($from);
        $bid              = $order_data['bid'];
        $field_suffix     = $express_index > 1 ? '_' . $express_index : '';
        $express_no       = $order_data['express_no' . $field_suffix];
        $express_code     = $order_data['express_code' . $field_suffix];
        $express_number   = AES_256_CBC::encrypt($express_no);
        $mobile           = $order_data['mobile'] ?? '';
        $guid             = $order_data['guid'];
        $query_route_path = $this->get_member_guid_from_request() ? 'member/tools/express' : 'admin/mobile/express';
        //        $query_route_url              = (string)url('member/tools/kuaidi', ['bid' => $bid, 'coname' => 'www', 'nu' => $express_no], false, true); // 模拟请求快递100 不稳定, 暂时弃用
        $query_route_url = (string)url($query_route_path, ['bid' => $bid, 'order_guid' => $guid, 'coname' => 'www', 'nu' => $express_no, 'mobile' => $mobile, 'express_number' => $express_number, 'express_code' => $express_code], false, true);
        $callback_url    = (string)url('member/code/order_detail', ['bid' => $bid, 'order_type' => 1, 'order_guid' => $guid, 'from' => 'kuaidi100'], false, true);

        $query_route_url_h5_kuaidi100 = 'https://m.kuaidi100.com/app/query/?coname=www&nu=' . $express_no . '&com=';

        if ($this->get_member_guid_from_request()) {
            //如果是会员访问快递100 则构造回跳链接
            $query_route_url_h5_kuaidi100 .= '&callbackurl=' . urlencode($callback_url);
        }
        $query_route_url_guoguo = 'https://page.cainiao.com/guoguo/app-myexpress-taobao/express-detail.html?mailNo=' . $express_no;
        if ($from == 'H5') {
            //顺丰开头单号  走快递100
            // $query_route_url = tools()::start_with(strtoupper($express_no), 'SF') ? $query_route_url_h5_kuaidi100 : $query_route_url_guoguo;
        }
        return $query_route_url;
    }

    /**
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws NotNotifyException
     */
    public function get_order_detail($bid, $guid, $hide_mobile = true)
    {
        $db_goods_order                            = new GoodsOrder();
        $config                                    = get_config_by_bid($bid);
        $allow_member_modify_order_advance_min_day = $config['allow_member_modify_order_advance_min_day'];
        $join                                      = [
            ['coupon c', 'go.bid = c.bid AND go.coupon_guid= c.guid', 'LEFT'],
            ['business b', 'go.bid = b.guid', 'INNER'],
            ['coupon_send_note csn', 'go.bid = csn.bid AND go.coupon_send_note_guid= csn.guid', 'LEFT'],
            ['area a1', 'go.province_id = a1.id', 'LEFT'],
            ['area a2', 'go.city_id = a2.id', 'LEFT'],
            ['area a3', 'go.area_id = a3.id', 'LEFT'],

            ['store s', 'go.request_send_or_pick_up_store_guid=s.guid AND go.bid=s.bid', 'LEFT'],
            ['area a4', 's.province_id = a4.id', 'LEFT'],
            ['area a5', 's.city_id = a5.id', 'LEFT'],
            ['area a6', 's.area_id = a6.id', 'LEFT'],

            ['express e', 'go.express_code = e.code', 'LEFT'],
            ['express e2', 'go.express_code_2 = e2.code', 'LEFT'],

            ['member_brokerage_note mbn', 'go.guid = mbn.relation_guid AND go.bid=mbn.bid', 'LEFT'],

        ];
        $map                                       = [
            ['go.bid', '=', $bid],
            ['go.guid', '=', $guid]
        ];
        $field                                     = [
            'c.name'                                     => 'coupon_name',
            'b.account'                                  => 'business_account',
            'b.business_name',
            'e.name'                                     => 'express_name',
            'e2.name'                                    => 'express_name_2',
            'go.*',
            'csn.code',
            'a1.name'                                    => 'province_name',
            'a2.name'                                    => 'city_name',
            'a3.name'                                    => 'area_name',
            'csn.code',
            's.store_name',
            's.mobile'                                   => 'store_mobile',
            's.latitude'                                 => 'store_latitude',
            's.longitude'                                => 'store_longitude',
            "CONCAT(a1.name,a2.name,a3.name,go.address)" => 'address_info',
            "CONCAT(a4.name,a5.name,a6.name,s.address)"  => 'store_address',
            "IFNULL(mbn.brokerage,0)"                    => 'brokerage_money',
        ];
        $db_business                               = new Business();
        if ($db_business->is_examples_bid($bid) && $hide_mobile) {
            //演示账号隐藏中间四位数
            $field[] = Db::raw("INSERT(go.mobile,4,4,'****') as mobile");
        }
        $order_info = $db_goods_order->alias('go')->join($join)->field($field)->where($map)->findOrEmpty();
        if ($order_info->isEmpty()) {
            throw new NotNotifyException('订单信息不存在');
        }
        if ($order_info['delete_time']) {
            throw new NotNotifyException('订单信息不存在!');
        }
        //查询商品信息
        $db_goods_order_item  = new GoodsOrderItem();
        $map_goods_order_item = [
            ['goi.bid', '=', $bid],
            ['goi.order_guid', 'IN', $guid]
        ];
        $join                 = [
            ['goods g', 'goi.bid = g.bid AND goi.goods_guid= g.guid', 'LEFT'],
        ];
        $field                = [
            'g.*',
            'goi.*',
        ];
        $goods_list           = $db_goods_order_item->alias('goi')->join($join)->field($field)->where($map_goods_order_item)->select();
        foreach ($goods_list as $key => $val) {
            $goods_list[$key]['num'] = $goods_list[$key]['amount'];
            $goods_list[$key]['pic'] = tools()::add_thumbnail_mini($goods_list[$key]['pic']);
            //            $goods_list[$key]['attr_list'] = json_decode($val['attr_list'], true);
        }

        //旧版从订单表查询商品明细,暂时废弃
        //       $goods_item_guid = array_keys($order_info['goods_item_guid']);
        //       $db_goods        = new Goods();
        //       $map             = [
        //           ['bid', '=', $bid],
        //           ['guid', 'IN', $goods_item_guid]
        //       ];
        //       $goods_list      = $db_goods->where($map)->selectOrFail();
        //       $total_amount    = 0;
        //
        //       foreach ($goods_list as $key => $val) {
        //           $goods_list[$key]['num'] = $order_info['goods_item_guid'][$val['guid']];
        //           $total_amount            += tools()::ncPriceCalculate($val['price'], '*', $goods_list[$key]['num']);
        //       }

        $order_info['goods_list'] = $goods_list; //后续考虑去掉
        $order_info['goods_info'] = $this->build_goods_info_pic($order_info['goods_info']);
        //       $order_info['total_amount']   = array_sum($order_info['goods_item_guid']); //商品总件数
        $request_send_or_pick_up_time                    = $order_info['request_send_or_pick_up_time'];
        $days                                            = round((strtotime($request_send_or_pick_up_time) - strtotime(date('Y-m-d'))) / 3600 / 24);
        $order_info['allow_modify']                      = ($order_info['status'] == 0 && $days >= $allow_member_modify_order_advance_min_day && $allow_member_modify_order_advance_min_day >= 0);
        $order_info['request_send_or_pick_up_time_text'] = $config['request_send_or_pick_up_time_text'];

        $allow_submit_after_sale_note_day           = $config['allow_submit_after_sale_note_day'];
        $order_create_date                          = date('Y-m-d', strtotime($order_info['create_time'])); // 获取订单创建日期
        $current_date                               = date('Y-m-d'); // 获取当前日期
        $days                                       = (strtotime($current_date) - strtotime($order_create_date)) / 3600 / 24; // 计算日期差
        $days                                       = max(1, $days); // 确保当天订单整天计算结果为 1
        $order_info['allow_submit_after_sale_note'] = ($days <= $allow_submit_after_sale_note_day);

        //兼容处理
        $order_info['send_time'] = $order_info['send_or_pick_up_time'];
        $order_info['content']   = $order_info['remark'];

        //单号自动转大写
        $order_info['express_no']   = strtoupper($order_info['express_no']);
        $order_info['express_no_2'] = strtoupper($order_info['express_no_2']);


        $order_info['goods_list_text']     = $this->get_goods_list_text($order_info['goods_info']);
        $order_info['query_route_url']     = $this->get_express_query_url($order_info);
        $order_info['web_query_route_url'] = $this->get_web_express_query_url($order_info);

        if (!empty($order_info['express_no_2'])) {
            $order_info['query_route_url_2']     = $this->get_express_query_url($order_info, 2);
            $order_info['web_query_route_url_2'] = $this->get_web_express_query_url($order_info, 2);
        }
        // 查询移动 支付退款状态
        $pay_order_status = '';
        $pay_order_remark = '';
        if (!empty($order_info['third_order_number'])) {
            $db_pay_order  = new PayOrder();
            $map_pay_order = [
                ['bid', '=', $bid],
                ['bill_number', '=', $order_info['third_order_number']],
            ];
            $pay_order     = $db_pay_order->field(['status', 'remark'])->where($map_pay_order)->findOrEmpty();
            if (!$pay_order->isEmpty()) {
                $pay_order_status = $pay_order['status'];
                $pay_order_remark = $pay_order['remark'];
            }
        }
        $order_info['pay_order_status'] = $pay_order_status;
        $order_info['pay_order_remark'] = $pay_order_remark;

        //完善扩展字段name
        if (!empty($order_info['extend_field_1']) || !empty($order_info['extend_field_2']) || !empty($order_info['extend_field_3'])) {
            $db_extend_field         = new ExtendField();
            $show_extend_field_array = $db_extend_field->get_extend_field_array($bid, true);
            foreach ($show_extend_field_array as $key => $val) {
                $order_info[$val['key_name'] . '_name'] = $val['name'];
            }
        }
        //判断是否允许退款
        $order_info['enable_refund'] = $this->is_enable_refund_by_order_status($bid, $order_info['status']);
        return $order_info;
    }

    public function is_enable_refund_by_order_status($bid, $order_status)
    {
        $config                              = get_config_by_bid($bid);
        $enable_refund_order_status_list     = explode(',', $config['enable_refund_order_status_list']);
        $enable_refund_order_status_list_map = [
            '0' => '待发货',
            '1' => '已发货',
            '2' => '已完成',
        ];
        foreach ($enable_refund_order_status_list_map as $key => $val) {
            if (!in_array($val, $enable_refund_order_status_list)) {
                unset($enable_refund_order_status_list_map[$key]);
            }
        }
        return (int)isset($enable_refund_order_status_list_map[$order_status]);
    }

    public function confirm_order($bid, $order_guid)
    {
        $map         = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
            ['status', '=', 1],
        ];
        $db          = new GoodsOrderModel();
        $update_data = ['status' => 2, 'confirm_time' => format_timestamp()];
        $db::update($update_data, $map);
        $data = ['bid' => $bid, 'order_guid' => $order_guid];
        job()->set_job_name('GoodsOrder@after_confirm')->push_job($data);
        $db->notify_refresh_home_data($bid, $order_guid);
        return true;
    }


    /**
     * @throws DataNotFoundException
     * @throws NotNotifyException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public function get_order_submit_info($params)
    {
        $bid                = $this->get_bid_from_request();
        $member_guid        = $this->get_member_guid_from_request();
        $token              = $params['token'] ?? null; // 卡密提货通过token提升安全性
        $way                = $params['way'] ?? ($token ? 1 : 2);
        $choose_goods_list  = $params['choose_goods_list'] ?? null;
        $goods_info         = $params['goods_info'] ?? null; //商品信息
        $cart_guid_list     = $params['cart_guid_list'] ?? null; //商品信息
        $exchange_times     = $params['exchange_times'] ?? 1; //提货次数 用于周期提货 >1
        $pick_up_times_type = $params['pick_up_times_type'] ?? ''; //提货模式  once 一次  cycle 周期
        if ($pick_up_times_type == 'once') {
            $exchange_times = 1;
        }
        $config       = get_config_by_bid($bid);
        $default_type = 1;  //1 快递发货 2到店自提
        if ($config['is_show_address'] == 0 && $config['is_show_request_send_or_pick_up_store']) {
            $default_type = 2;  //1 快递发货 2到店自提
        }
        $exchange_times              = (int)$exchange_times;
        $type                        = $params['type'] ?? $default_type; //1 快递发货 2到店自提
        $type                        = $type ?: $default_type; //如果type=0 则默认是1
        $coupon_send_note_guid       = '';
        $coupon_guid                 = '';
        $discount_preferential_money = 0;
        if ($way == 1 || $token) {
            //todo 提货业务强制校验token 因为下面的逻辑提货业务直接不计算产品费用, (后续需要通过  coupon_send_note_guid 来实现自动免除产品费用
            if (empty($token)) {
                throw new NotNotifyException('token不能为空');
            }
            $token_info = cache($token);
            if (empty($token_info)) {
                throw new NotNotifyException('token校验失败');
            }
            $coupon_send_note_guid = $token_info['coupon_send_note_guid'];
            $coupon_guid           = $token_info['coupon_guid'];
            $bid                   = $token_info['bid'];
        } elseif ($way == 3) {
            $coupon_send_note_guid = $params['coupon_send_note_guid'];
        }
        $goods_info_list       = []; //商品信息列表,用于展示预览用
        $goods_guid_array      = []; //商品guid数组 ['guid1`,'guid2'];
        $goods_item_guid_array = []; //商品guid数组 ['guid1`=>1,'guid2'=>2];
        $goods_list_array      = []; //从商品详情页进入商品数量
        $cart_guid_list_array  = []; //购物车数组详情
        $db_goods              = new Goods();
        $db_member             = new Member();
        $member_discount_ratio = (in_array($way, [1, 3])) ? 1 : $db_member->get_member_discount_ratio($bid, $member_guid);
        if (!empty($choose_goods_list)) {
            //兼容老的
            $goods_info = [];
            foreach ($choose_goods_list as $key => $val) {
                $goods_info[] = [
                    'guid'   => $key,
                    'amount' => $val,
                ];
            }
        }

        if ($goods_info) {
            //从产品详情页进入
            $goods_list_array = is_string($goods_info) ? json_decode($goods_info, true) : $goods_info;
            if (empty($goods_list_array)) {
                error('请选择商品!');
            }
            foreach ($goods_list_array as $goods) {
                $goods_guid_array[]                    = $goods['guid'];
                $goods_item_guid_array[$goods['guid']] = $goods['amount'];
            }
            $field           = [
                'g.guid' => 'goods_guid',
                'owner_user_id',
                'owner_user_guid',
                'name',
                'name'   => 'goods_name',
                'stock_mode',
                'stock',
                //                'price',
                Db::raw("ROUND(price * $member_discount_ratio ,2) as price"),
                'pic',
                'pic'    => 'goods_pic',
                'specs'
            ];
            $map             = [
                ['g.bid', '=', $bid],
                ['g.guid', 'IN', $goods_guid_array]
            ];
            $goods_info_list = $db_goods->alias('g')->field($field)->where($map)->selectOrFail()->toArray();
            foreach ($goods_info_list as $key => $val) {
                foreach ($goods_list_array as $goods) {
                    if ($goods['guid'] == $val['goods_guid']) {
                        $goods_info_list[$key]['num'] = $goods['amount'];
                        if (!empty($goods['attr'])) {
                            $db_goods                          = new GoodsModel();
                            $sku_info                          = $db_goods->get_goods_sku_info_by_sku_json($bid, $goods['attr'], $val['goods_guid']);
                            $goods_info_list[$key]['price']    = tools()::nc_price_calculate($sku_info['price'], '*', $member_discount_ratio);
                            $goods_info_list[$key]['sku_guid'] = $sku_info['guid'];
                            $attr                              = is_array($goods['attr']) ? $goods['attr'] : json_decode($goods['attr'], true);
                            $goods_info_list[$key]['attr']     = $attr;
                        }
                        //     goods_info: "[{"guid":"e811ac9a-7a88-e5a6-308a-8c30b8e491f0","attr":[{"attr_group_id":"f602044b-4ab9-d8c9-88ee-929a746ed167","attr_group_name":"内存","attr_id":"83bc12ce-e38d-e454-d4ba-72b0e0b7e2e9","attr_name":"64G"},{"attr_group_id":"ee5df39f-8463-4f58-7105-b05ef74a69aa","attr_group_name":"颜色","attr_id":"260bb6de-579b-10c6-98ea-c113f5f6e370","attr_name":"黑色"}],"amount":1}]"
                    }
                }
            }
        } else if ($cart_guid_list) {
            $cart_guid_list_array = is_string($cart_guid_list) ? json_decode($cart_guid_list, true) : $cart_guid_list;
            if (empty($cart_guid_list_array)) {
                error('cart_guid_list_array 为空');
            }
            $db_cart          = new CartModel();
            $map              = [
                ['bid', '=', $bid],
                ['guid', 'IN', $cart_guid_list_array]
            ];
            $goods_list_array = $db_cart->field(['goods_guid' => 'guid', 'amount'])->where($map)->select()->toArray();
            if (empty($goods_list_array)) {
                error('当前购物车商品已失效,请退出小程序重新访问');
            }
            foreach ($goods_list_array as $goods) {
                $goods_guid_array[] = $goods['guid'];
            }
            $field           = [
                'g.guid'   => 'goods_guid',
                'owner_user_id',
                'owner_user_guid',
                'name',
                'specs',
                'g.stock_mode',
                'g.stock',
                'name'     => 'goods_name',
                //                'price',
                Db::raw("ROUND(price * $member_discount_ratio ,2) as price"),
                'pic',
                'pic'      => 'goods_pic',
                'c.amount' => 'num',
                'c.attr'   => 'attr',
                'specs'
            ];
            $join            = [
                ['cart c', 'c.goods_guid = g.guid AND c.bid=g.bid'],
            ];
            $map             = [
                ['g.bid', '=', $bid],
                ['g.guid', 'in', $goods_guid_array],
                ['c.guid', 'in', $cart_guid_list_array]
            ];
            $goods_info_list = $db_goods->alias('g')->join($join)->field($field)->where($map)->select()->toArray();
            foreach ($goods_info_list as $key => $val) {
                $goods_info_list[$key]['pic']       = tools()::add_thumbnail_mini($val['pic']);
                $goods_info_list[$key]['goods_pic'] = tools()::add_thumbnail_mini($val['goods_pic']);

                $attr = is_array($val['attr']) ? $val['attr'] : json_decode($val['attr'], true);
                if (!empty($attr)) {
                    $db_goods = new GoodsModel();
                    $sku_info = $db_goods->get_goods_sku_info_by_sku_json($bid, $val['attr'], $val['goods_guid']);
                    if (empty($sku_info)) {
                        error('购物车商品信息已失效,请清空购物车重新下单');
                    }
                    $goods_info_list[$key]['price']    = tools()::nc_price_calculate($sku_info['price'], '*', $member_discount_ratio);
                    $goods_info_list[$key]['sku_guid'] = $sku_info['guid'];
                    $goods_info_list[$key]['attr']     = $attr;
                } else {
                    $goods_info_list[$key]['attr'] = [];
                }
            }
        } else {
            error('商品信息为空');
        }
        if (empty($goods_list_array)) {
            error('商品不能为空!');
        }
        $map = [
            ['bid', '=', $bid],
            ['guid', 'IN', $goods_guid_array]
        ];
        //校验价格
        $goods_price_array = $db_goods->where($map)->column('price', 'guid');
        if (empty($goods_price_array)) {
            error('商品已被删除');
        }
        //校验库存
        foreach ($goods_info_list as $key => $val) {
            $stock_mode = $val['stock_mode'];
            $stock      = $val['stock'];
            if ($stock_mode == 1 && $stock <= 0) {
                $goods_name = $val['name'];
                error('商品' . $goods_name . '库存已经不足,请选择其他商品');
            }
        }

        $extra_charges_name  = '';
        $extra_charges_price = 0;
        if ($member_guid) {
            $extra_charges_price = $db_goods->where($map)->sum('extra_charges_price'); //附加费用
        }
        if ($extra_charges_price > 0) {
            $map_extra_charges_name   = [
                ['bid', '=', $bid],
                ['extra_charges_name', '<>', ''],
                ['guid', 'IN', $goods_guid_array]
            ];
            $extra_charges_name_array = $db_goods->where($map_extra_charges_name)->column('extra_charges_name');
            $extra_charges_name_array = array_unique(array_filter($extra_charges_name_array));
            if (!empty($extra_charges_name_array)) {
                usort($extra_charges_name_array, function ($a, $b) {
                    if (strlen($a) == strlen($b)) {
                        return 0;
                    }
                    return (strlen($a) < strlen($b)) ? -1 : 1;
                });
                $extra_charges_name = join('+', $extra_charges_name_array);
            } else {
                $extra_charges_name = $config['extra_charges_name'];
            }
        }
        $total_goods_price = 0;
        //        foreach ($goods_list_array as $goods) {
        //            //先乘以折扣系数
        //            $goods_price       = tools()::ncPriceCalculate($goods_price_array[$goods['guid']], '*', $member_discount_ratio);
        //            $goods_price       = tools()::ncPriceCalculate($goods_price, '*', $goods['amount']);
        //            $total_goods_price = tools()::ncPriceCalculate($total_goods_price, '+', $goods_price);
        //        }
        foreach ($goods_info_list as $key => $val) {
            //先乘以折扣系数
            //            $goods_price       = tools()::ncPriceCalculate($goods_price_array[$goods['guid']], '*', $member_discount_ratio);
            $goods_price                  = tools()::nc_price_calculate($val['price'], '*', $val['num']);
            $total_goods_price            = tools()::nc_price_calculate($total_goods_price, '+', $goods_price);
            $goods_info_list[$key]['pic'] = tools()::add_thumbnail_mini($val['pic']);
        }

        $total_goods_num = 0;
        foreach ($goods_list_array as $goods) {
            $total_goods_num += $goods['amount'];
        }

        //计算运费
        $choose_goods_list                       = [];
        $choose_goods_list_for_get_freight_price = [];
        foreach ($goods_list_array as $goods) {
            $choose_goods_list[$goods['guid']]                       = $goods['amount'];
            $choose_goods_list_for_get_freight_price[$goods['guid']] = ($choose_goods_list_for_get_freight_price[$goods['guid']] ?? 0) + $goods['amount'];
        }
        $db_member_address = new MemberAddress();
        $address_guid      = !empty($params['address_guid']) ? $params['address_guid'] : null;
        //产品运费, 由于没地址的时候 也要展示价格, 所以都计算
        //       if ($address_guid) {
        //           $db_goods      = new Goods();
        //           $express_price = $db_goods->get_freight_price($bid, $choose_goods_list);
        //       }
        if (empty($address_guid) && $way == 1) {
            //如果地址是空 地址guid也是空 取 参数中传的这些信息
            $address['true_name'] = $params['true_name'] ?? '';
            $address['mobile']    = $params['mobile'] ?? '';
            $address['mobile']    = tools()::find_string_number($address['mobile']);
            $province_id          = $params['province_id'] ?? 0;
            $city_id              = $params['city_id'] ?? 0;
            $area_id              = $params['area_id'] ?? 0;
            if ($province_id == 0 && $city_id == 0 && $area_id == 0) {
                $db_area_rule  = new AreaRule();
                $map_area_rule = [
                    ['bid', '=', $bid],
                    ['status', '=', 1],
                ];
                $count         = $db_area_rule->where($map_area_rule)->count();
                $db_area       = new Area();
                $auto_ip_info  = $db_area->ip_to_area_id();
                $province_id   = $auto_ip_info['province_id'];
                $city_id       = $auto_ip_info['city_id'];
                $area_id       = $auto_ip_info['area_id'];
                if ($count > 0) {
                    //存在地区规则 则继续判断省市是否处于不可选择的规则 如果是不采用自动识别到的省市区
                    $check_area = $db_area_rule->check_area($bid, $auto_ip_info, $token);
                    if ($check_area !== true) {
                        $province_id = 0;
                        $city_id     = 0;
                        $area_id     = 0;
                    }
                }
            }
            $address['province_id'] = $province_id;
            $address['city_id']     = $city_id;
            $address['area_id']     = $area_id;
            $address['address']     = $params['address'] ?? '';
        } elseif (empty($address_guid) && $way == 2 && $type == 2) {
            //商城到店自提
            $address['true_name'] = $params['true_name'] ?? '';
            $address['mobile']    = $params['mobile'] ?? '';
            $address['mobile']    = tools()::find_string_number($address['mobile']);
        } else {
            $address = $db_member_address->get_address_detail($bid, $address_guid);
        }
        $address_guid = $address['guid'] ?? null;
        //判断地址是否合法
        $db_area_rule = new AreaRule();
        $check_area   = $db_area_rule->check_area($bid, $address, $token);
        if ($check_area === false) {
            throw new NotNotifyException($db_area_rule->getError());
        }
        $db_goods      = new Goods();
        $express_price = 0;
        if ($member_guid && $type == 1 && $way != 3) {
            // way=3 送礼订单免运费
            $express_price = $db_goods->get_freight_price($bid, $choose_goods_list_for_get_freight_price, $total_goods_price, $address);
        }

        $sum_price = tools()::nc_price_calculate($total_goods_price, '+', $express_price);
        $sum_price = tools()::nc_price_calculate($extra_charges_price, '+', $sum_price);

        //折后价格 除以折扣系数 减去 折后价格
        $discount_preferential_money = tools()::nc_price_calculate($total_goods_price, '/', $member_discount_ratio) - $total_goods_price;

        // 开始进行订单校验
        $money_unit     = $config['money_unit'];
        $coupon_use_num = 0;
        if ($coupon_send_note_guid && $way == 1) {
            //多次提货处理
            $db_coupon_send_note  = new CouponSendNote();
            $code_info            = $db_coupon_send_note->get_code_info($bid, $coupon_send_note_guid);
            $exchange_goods_type  = $code_info['exchange_goods_type']; //提货类型 1 按数量 2 按金额
            $exchange_goods_value = $code_info['exchange_goods_value']; //可提产品金额
            $exchange_goods_num   = $code_info['exchange_goods_num']; //可选产品数量
            if ($exchange_goods_num <= 0) {
                $msg = '可提货产品数量为:' . $exchange_goods_num;
                throw new Exception($msg);
            }
            $exchange_num = $total_goods_num / $exchange_goods_num;  //所选商品需要消耗N次卡券
            if ($exchange_times > 1 && $total_goods_num > 1) {
                $msg = '周期提货暂时仅支持1个商品'; // 例如选了3个商品
                throw new NotNotifyException($msg);
            }
            if ($exchange_times > 1 && $exchange_num > 1) {
                $msg = '周期提货暂时仅支持1倍提货'; // 例如选了3个商品
                throw new NotNotifyException($msg);
            }
            if ($total_goods_num > 1 || $exchange_num > 1) {
                if (floor($exchange_num) != $exchange_num) {
                    $msg = '商品选择总数必须是' . $exchange_goods_num . '的倍数'; // 例如选了3个商品
                    throw new NotNotifyException($msg);
                }
                //            if ($exchange_times == 1) {
                //                $exchange_times = $exchange_num;
                //            }
            }
            if ($exchange_goods_type == 2) {
                $coupon_use_num = 1;
            } else {
                $coupon_use_num = $coupon_send_note_guid ? max($exchange_times * $exchange_num, 1) : 0;
            }
        } elseif ($way == 3 && $coupon_send_note_guid) {
            // 送礼订单处理 - 送礼券本身是付费购买的，无需验证面值
            $coupon_use_num = 1; // 送礼券使用1次
        }

        $data   = [
            'way'                   => $way, // 1 提货业务 2 产品商城
            'type'                  => $type, // 1 快递配送 2 到店自提
            'cart_guid_list_array'  => $cart_guid_list_array, //购物车列表
            'goods_info_list'       => $goods_info_list, //商品信息列表
            'goods_list_array'      => $goods_list_array, //商品信息数组
            'goods_guid_array'      => $goods_guid_array, //商品guid数组 ['guid1','guid2']
            'goods_item_guid_array' => $goods_item_guid_array, //商品guid数组 ['guid1'=>1,'guid2'=>2]

            'discount_preferential_money' => $discount_preferential_money,
            'total_goods_num'             => $total_goods_num, //商品总件数
            'total_goods_price'           => $total_goods_price, //商品小计
            'extra_charges_price'         => $extra_charges_price, //附加费
            'extra_charges_name'          => $extra_charges_name, //附加费名称
            'money_unit'                  => $money_unit, //金额单位,例如 元 点
            'express_price'               => $express_price, //运费
            'sum_price'                   => $sum_price, //总价
            'address'                     => $address, //地址信息数组
            'address_guid'                => $address_guid, //地址guid

            'coupon_send_note_guid' => $coupon_send_note_guid, //优惠券发送记录id 提货使用
            'coupon_guid'           => $coupon_guid, //优惠券id 提货使用
            'coupon_use_num'        => $coupon_use_num, //默认优惠券使用张数是1
            'exchange_times'        => $exchange_times, //提货次数 用于周期提货
            'token'                 => $token,
        ];
        $result = $this->check($data);
        if ($result === false) {
            throw new Exception($this->getError());
        }
        return $result;
    }

    public function notify_refresh_home_data($bid, $order_guid = null)
    {
        $user_guid_array = [];
        if ($order_guid) {
            $db              = new GoodsOrderModel();
            $user_guid_array = $db->get_notify_user_guid_array($bid, $order_guid);
        }
        notify()->set_key_name(NotifyService::RefreshHomeData)->set_user_guid($user_guid_array)->set_data(['type' => 'notify_refresh_home_data'])->set_bid($bid)->send();
    }

    public function submit_preview(array $params)
    {
        $bid                     = $this->get_bid_from_request();
        $member_guid             = $this->get_member_guid_from_request();
        $config                  = get_config_by_bid($bid);
        $order_submit_info       = $this->get_order_submit_info($params);
        $way                     = $order_submit_info['way']; // 1 提货业务 2 产品商城
        $type                    = $order_submit_info['type']; // 1 快递配送 2 到店自提
        $total_goods_price       = $order_submit_info['total_goods_price']; //商品总价
        $extra_charges_price     = $order_submit_info['extra_charges_price']; //附加费合计
        $express_price           = $order_submit_info['express_price']; //运费合计
        $sum_price               = $order_submit_info['sum_price']; //总价(共支付金额)
        $deficit                 = $order_submit_info['deficit'] ?? 0; //差额 金额卡涉及补差价,仅允许微信支付
        $address                 = $order_submit_info['address']; //地址信息
        $address_guid            = $order_submit_info['address_guid']; //地址guid
        $goods_list_array        = $order_submit_info['goods_list_array']; //商品列表
        $goods_info_list         = (array)$order_submit_info['goods_info_list']; //商品列表
        $choose_goods_show_price = (int)$config['choose_goods_show_price']; //选择商品是否展示价格

        if ($way == 1 && $choose_goods_show_price == 0) {
            foreach ($goods_info_list as $key => $val) {
                $goods_info_list[$key]['price'] = 0;
            }
        }

        $cart_guid_list_array = $order_submit_info['cart_guid_list_array']; //购物车商品guid数组
        $extra_charges_name   = $order_submit_info['extra_charges_name']; //附加费名称
        $money_unit           = $order_submit_info['money_unit']; //储值单位
        $paid_money           = 0; //储值支付金额
        $paid_point           = 0; //积分支付金额
        if ($way == 1) {
            //提货业务 支付金额为 总价- 商品价格
            $sum_price = tools()::nc_price_calculate($sum_price, '-', $total_goods_price, 2);
        }
        $paid_wechat    = tools()::nc_price_calculate($sum_price, '+', $deficit); //先默认微信支付金额即产品总价
        $use_money_paid = $params['use_money_paid'] ?? false; // 是否允许用余额支付
        $use_point_paid = $params['use_point_paid'] ?? true; //是否允许用积分支付

        $db_member              = new Member();
        $map                    = ['bid' => $bid, 'guid' => $member_guid];
        $member_info            = $db_member->get_member_info($map);
        $member_available_money = $member_info['money']; //会员可用余额
        $member_available_point = $member_info['point']; //会员可用积分

        $point_per_yuan                 = (int)$config['point_per_yuan'];  //X积分当一元花
        $point_to_value_max_ratio       = $config['point_to_value_max_ratio'];  //积分抵现最大比例 0.5 代表最多抵扣一半费用
        $money_can_deduct_express_price = (int)$config['money_can_deduct_express_price']; // 储值是否抵扣运费
        $max_use_point_num              = 0; //最大可用积分数量,例如 10积分
        $max_use_point_money            = 0; //最大可用积分数量所抵扣的金额 例如 1元

        if ($point_per_yuan > 0 && $member_available_point) {
            $point_cost_money = tools()::nc_price_calculate($member_available_point, '/', $point_per_yuan, 2); //积分价值多少元
            //使用积分支付, 则先计算 积分可抵用的金额
            $point_to_value_max  = tools()::nc_price_calculate($total_goods_price, '*', $point_to_value_max_ratio, 2); //积分价值多少元
            $max_use_point_money = min($point_to_value_max, $point_cost_money); //最多可以抵用商品金额和积分价值
            $max_use_point_num   = tools()::nc_price_calculate($max_use_point_money, '*', $point_per_yuan, 2); //最多消耗积分个数
        }
        $max_deduct_money = $total_goods_price;
        if ($money_can_deduct_express_price && $express_price > 0) {
            //储值可以抵扣运费切运费>0
            $max_deduct_money += $express_price;
        }
        $max_use_money = min($max_deduct_money, $member_available_money); //最多可以抵用商品金额和会员余额最小值
        if ($way == 2 || $way == 3) {
            // 产品商城和送礼订单都可以使用储值或者积分
            if ($use_point_paid) {
                $paid_point = $max_use_point_money; // 最大积分使抵扣金额(上面逻辑已经限定了 不可能超过总价)  赋值给积分抵扣
                //微信支付金额随之递减
                $paid_wechat = tools()::nc_price_calculate($paid_wechat, '-', $paid_point, 2);
            }
            //仅当还需要微信支付时 才去抵扣储值
            if ($paid_wechat > 0 && $use_money_paid && $max_use_money > 0) {
                $paid_money = min($paid_wechat, $max_use_money); //看微信支付和最大可抵用余额 取最小值
                $paid_money = floatval($paid_money);
                //继续递减
                $paid_wechat = tools()::nc_price_calculate($paid_wechat, '-', $paid_money, 2);
            }
            if ($paid_money <= 0) {
                //如果储值抵扣金额小于等于0 则不允许储值支付 (小程序前端才是未勾选状态)
                $use_money_paid = false;
            }
            if ($paid_point <= 0) {
                //如果积分抵扣金额小于等于0 则不允许积分支付 (小程序前端才是未勾选状态)
                $use_point_paid = false;
            }
        }
        $cost_desc       = '';
        $cost_desc_array = [];
        if ($express_price > 0) {
            $cost_desc_array[] = '运费';
        }
        if ($extra_charges_price > 0) {
            $cost_desc_array[] = $extra_charges_name;
        }
        if (!empty($cost_desc_array)) {
            $cost_desc = join('+', $cost_desc_array);
        }
        return [
            'goods_info'             => $goods_list_array, //产品信息 原样返回
            'cart_guid_list'         => $cart_guid_list_array, //购物车guid列表
            'coupon_list'            => [],
            'way'                    => $way,   // 1 提货业务 2 产品商城
            'type'                   => (int)$type,   // 1 快递配送 2 到店自提
            'goods_price'            => (float)$total_goods_price, //商品总额
            'extra_charges_price'    => (float)$extra_charges_price, //附加费
            'extra_charges_name'     => $extra_charges_name, //附加费名称
            'cost_desc'              => $cost_desc, //费用名称
            'money_unit'             => $money_unit, //金额单位 例如 元/点
            'express_price'          => (float)$express_price, //运费
            'total_price'            => (float)$sum_price, //总金额
            'form'                   => ['is_form' => 0],
            'list'                   => $goods_info_list, //商品列表 (用于预览) 商城提货用的字段
            'goods_list'             => $goods_info_list, //商品列表 (用于预览) 提货系统用的字段
            'address'                => $address, //地址信息 (数组)
            'address_guid'           => $address_guid, //地址guid
            'paid_point'             => (float)$paid_point, //积分支付金额
            'paid_money'             => (float)$paid_money, //储值支付金额
            'paid_wechat'            => (float)$paid_wechat, //微信支付金额
            'use_money_paid'         => $use_money_paid, //是否要求使用储值支付
            'use_point_paid'         => $use_point_paid, //是否要求使用积分支付
            'max_use_money'          => $max_use_money, //本单最大可抵用储值
            'member_available_money' => $member_available_money, //会员可用储值
            'member_available_point' => $member_available_point, //会员可用积分
            'can_use_money_pay'      => true,
            'can_use_point_pay'      => $point_per_yuan > 0, //是否允许使用积分支付 ,X积分抵现1元则说明允许使用积分支付
            'max_use_point_num'      => $max_use_point_num, //最大可用积分数量,例如 10积分
            'max_use_point_money'    => $max_use_point_money, //最大可用积分数量所抵扣的金额 例如 1元
        ];
    }

    // 写入数据库之前检测 目前支持优惠券是否可使用的检测
    public function check($order_submit_info)
    {
        $bid                 = $this->get_bid_from_request();
        $db_coupon_send_note = new CouponSendNote();
        $way                 = $order_submit_info['way'];
        $goods_guid_array    = $order_submit_info['goods_guid_array'];

        if ($way == 2) {
            // 商城订单校验商品和分类状态
            $db_goods                 = new GoodsModel();
            $offline_goods_name_array = $db_goods->get_goods_and_category_status($bid, $goods_guid_array);
            if (!empty($offline_goods_name_array)) {
                $this->error = '商品' . implode(',', $offline_goods_name_array) . '已下架，请重新选择';
                return false;
            }

            return $order_submit_info;
        }

        if ($way == 3) {
            // 送礼订单只需要校验商品状态，不需要校验卡券可提产品
            $db_goods                 = new GoodsModel();
            $offline_goods_name_array = $db_goods->get_goods_and_category_status($bid, $goods_guid_array);
            if (!empty($offline_goods_name_array)) {
                $this->error = '商品' . implode(',', $offline_goods_name_array) . '已下架，请重新选择';
                return false;
            }

            return $order_submit_info;
        }
        $coupon_send_note_guid = $order_submit_info['coupon_send_note_guid'];
        $coupon_guid           = $order_submit_info['coupon_guid'];

        $total_goods_num   = $order_submit_info['total_goods_num'];
        $total_goods_price = $order_submit_info['total_goods_price'];
        $exchange_times    = $order_submit_info['exchange_times'];

        $lock_instance        = get_distributed_instance();
        $lock_key             = $bid . $coupon_send_note_guid;
        $lock                 = $lock_instance->get_lock($lock_key);
        $code_info            = $db_coupon_send_note->get_code_info($bid, $coupon_send_note_guid);
        $exchange_goods_type  = $code_info['exchange_goods_type']; //提货类型 1 按数量 2 按金额
        $exchange_goods_value = $code_info['exchange_goods_value']; //可提产品金额
        $exchange_goods_num   = $code_info['exchange_goods_num']; //可选产品数量
        $goods_list           = $code_info['goods_list']; //可选的商品列表
        $available_num        = $code_info['available_num']; //可用次数
        $available_value      = $code_info['available_value']; //可用金额


        //校验传入的商品必须是合法的商品
        foreach ($goods_guid_array as $key => $val) {
            if (!in_array($val, array_column($goods_list, 'guid'))) {
                $lock_instance->unlock($lock);
                $this->error = '卡券可提产品可能有更新,请您重新操作提货';
                return false;
            }
        }

        if ($exchange_goods_type == 1) {
            //按照数量提取
            if ($exchange_times > $available_num) {
                $lock_instance->unlock($lock);
                $this->error = '该卡号最多提货' . $available_num . '次哦';
                return false;
            }
            if ($exchange_goods_num == 0) {
                $lock_instance->unlock($lock);
                $this->error = '该卡号的可提商品数为0!';
                return false;
            }
            $exchange_num = $total_goods_num / $exchange_goods_num;  // 例如 10个商品 , 一次可选2个商品
            if (floor($exchange_num) != $exchange_num) {
                $this->error = '商品选择总数必须是' . $exchange_goods_num . '的倍数'; // 例如选了3个商品
                $lock_instance->unlock($lock);
                return false;
            }
            if ($exchange_times > 1 && $exchange_num > 1) {
                $lock_instance->unlock($lock);
                $this->error = '多次提货暂不支持周期提货';
                return false;
            }
            //            if ($exchange_times < $exchange_num) {
            //                //使用优惠券 2张 不能只提一次
            //                $this->error = '提货次数' . $exchange_times . '不能小于商品所消耗的次数' . $exchange_num;
            //                $lock_instance->unlock($lock);
            //                return false;
            //            }
            $coupon_use_num = intval($exchange_times / $exchange_num); //4个周期 每个周期
            //            $order_submit_info['coupon_use_num'] = $coupon_use_num;
            //           if ($choose_goods_list_num != $exchange_goods_num && $exchange_goods_num > 0) {
            //               $lock_instance->unlock($lock);
            //               $this->error = '商品数量不一致';
            //               return false;
            //           }
        }

        if ($exchange_goods_type == 2) {
            //按金额量提取
            $config                        = get_config_by_bid($bid);
            $value_code_pick_up_limit_type = $config['value_code_pick_up_limit_type']; //金额卡下单模式 0 不限制 可分次提货 1 必须满额才能提交订单 2 支持补差价
            if (in_array($value_code_pick_up_limit_type, [0, 1]) && $total_goods_price > $available_value) {
                //多次提货 满额才能提货 均不可超过订单金额
                $lock_instance->unlock($lock);
                $this->error = '该卡号最多提货' . $available_value . '元哦';
                return false;
            }
            if ($value_code_pick_up_limit_type == 1 && $total_goods_price != $available_value) {
                //多次提货 满额才能提货 均不可超过订单金额
                $lock_instance->unlock($lock);
                $this->error = '该卡必须一次性提完' . $available_value . '元商品哦,请继续选择商品';
                return false;
            }
            if ($value_code_pick_up_limit_type == 2 && $total_goods_price > $available_value) {
                //允许补差价
                $lock_instance->unlock($lock);
                $order_submit_info['deficit'] = tools()::nc_price_calculate($total_goods_price, '-', $available_value, 2);
            }

            //            if ($exchange_goods_value < $total_goods_price) {
            //                $lock_instance->unlock($lock);
            //                $this->error = '系统繁忙,请稍候再试';
            //                $msg         = '$exchange_goods_value:' . $exchange_goods_value . '$total_goods_price:' . $total_goods_price;
            //                wr_log($msg, 1);
            //                wr_log($code_info);
            //                return false;
            //            }
            if ($exchange_times > 1) {
                $lock_instance->unlock($lock);
                $this->error = '金额卡暂时不支持周期提货';
                return false;
            }
            $config = get_config_by_bid($bid);
            if ($config['value_code_pick_up_limit_type'] == 1) {
                if ($total_goods_price < $available_value) {
                    $lock_instance->unlock($lock);
                    $this->error = '该卡暂不支持多次提货,请一次性使用完毕';
                    return false;
                }
            }
        }
        $lock_instance->unlock($lock);
        return $order_submit_info;
    }

    protected function get_pick_up_code()
    {
        do {
            //先查找是否有可使用的提货码
            $pick_up_code   = '1' . mt_rand(10000000, 99999999);
            $map            = [
                ['bid', '=', $this->get_bid_from_request()],
                ['pick_up_code', '=', $pick_up_code],
            ];
            $db_goods_order = new GoodsOrder();
            $is_exist       = $db_goods_order->where($map)->value('id');
        } while ($is_exist);
        return $pick_up_code;
    }

    public function calculate_sub_order_money($total_order_num, $index, $total_goods_money, $sub_goods_money, $money, $before_money)
    {
        if ($money == 0) {
            return 0;
        }
        if ($total_order_num == $index) {
            //最后一个订单 用减法
            return tools()::nc_price_calculate($money, '-', $before_money, 2);
        }
        //其余情况算权重
        return round($money * ($sub_goods_money / $total_goods_money), 2);
    }
    // 统一下单

    /**
     * @throws NotNotifyException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception|Throwable
     */
    public function submit_beta(array $params)
    {
        $bid         = $this->get_bid_from_request();
        $member_guid = (string)$this->get_member_guid_from_request();
        $user_guid   = (string)$this->get_user_guid_from_request();
        $openid      = (string)$this->get_openid_from_request();
        $appid       = (string)$this->get_appid_from_request();

        $share_member_guid = $params['share_member_guid'] ?? null;
        $share_user_guid   = $params['share_user_guid'] ?? null;

        $id_card_number    = isset($params['id_card_number']) ? strtoupper($params['id_card_number']) : null;
        $remark            = $params['remark'] ?? null;
        $remark_image_list = $params['remark_image_list'] ?? null;

        $request_send_or_pick_up_time       = $params['request_send_or_pick_up_time'] ?? null; //预约时间
        $request_send_or_pick_up_store_guid = $params['request_send_or_pick_up_store_guid'] ?? null; //自提门店
        $cycle_delivery_interval_days       = $params['cycle_delivery_interval_days'] ?? 0; //周期发货间隔天数
        if ($cycle_delivery_interval_days > 100) {
            error('间隔天数不能超过100天');
        }
        $cycle_delivery_min_interval_days = $params['cycle_delivery_min_interval_days'] ?? null; //周期发货最小间隔天数
        $exchange_times                   = $params['exchange_times'] ?? 1; //提货次数
        $pick_up_times_type               = $params['pick_up_times_type'] ?? ''; //提货模式 once cycle
        if ($pick_up_times_type == 'once') {
            $exchange_times = 1; //如果是一次提货 次数强制设置为 1
        }
        $type         = $params['type'] ?? ($request_send_or_pick_up_store_guid ? 2 : 1); // 1 快递发货  2 到店自提
        $pick_up_code = 0; //核销码默认0 type=2 的时候生成核销码

        $db_cart           = new CartModel();
        $order_submit_info = $this->get_order_submit_info($params);


        //todo 做参数校验

        if ($id_card_number && !tools()::is_id_card_number($id_card_number)) {
            error('身份证号不正确');
        }


        $total_amount                = $order_submit_info['total_goods_num']; //商品总件数
        $total_goods_price           = $order_submit_info['total_goods_price']; //商品总金额
        $discount_preferential_money = $order_submit_info['discount_preferential_money']; //优惠总金额
        $extra_charges_price         = $order_submit_info['extra_charges_price']; //附加费
        $express_price               = $order_submit_info['express_price']; //运费
        $extra_charges_name          = $order_submit_info['extra_charges_name']; //附加费名称

        $sum_price = $order_submit_info['sum_price']; //订单总价
        $deficit   = $order_submit_info['deficit'] ?? 0; //差额 金额卡涉及补差价,仅允许微信支付

        $address_info = $order_submit_info['address'];

        $goods_list_array      = $order_submit_info['goods_list_array'];
        $goods_item_guid_array = $order_submit_info['goods_item_guid_array'];


        $goods_info_list      = $order_submit_info['goods_info_list'];
        $cart_guid_list_array = $order_submit_info['cart_guid_list_array'];
        $way                  = $order_submit_info['way']; // 1 提货业务 2 产品商城
        $coupon_guid          = $order_submit_info['coupon_guid'];

        $coupon_send_note_guid = $order_submit_info['coupon_send_note_guid'];
        $coupon_use_num        = $order_submit_info['coupon_use_num'];
        $token                 = $order_submit_info['token'];

        //地址信息
        $true_name = $address_info['true_name'] ?? ''; //姓名
        $last_name = $params['last_name'] ?? ''; //姓氏
        $sex       = $params['sex'] ?? 0; //性别 0 未知 1 男 2 女
        if (empty($true_name) && $last_name && $sex) {
            $true_name = $last_name . ($sex == 1 ? '先生' : '女士');
        }
        $mobile      = $address_info['mobile'] ?? '';
        $province_id = $address_info['province_id'] ?? 0;
        $city_id     = $address_info['city_id'] ?? 0;
        $area_id     = $address_info['area_id'] ?? 0;
        $address     = $address_info['address'] ?? '';
        if ($request_send_or_pick_up_store_guid && empty($province_id) && empty($city_id) && empty($area_id)) {
            $type = 2;  // 1 快递发货  2 到店自提 如果有自提门店 且 省市区为空, 则说明是到店自提 临时修复 20221205
        }

        //扩展信息

        $extend_field_1 = $params['extend_field_1'] ?? '';
        $extend_field_2 = $params['extend_field_2'] ?? '';
        $extend_field_3 = $params['extend_field_3'] ?? '';

        $config                               = get_config_by_bid($bid, 'coupon', $coupon_guid);
        $is_show_request_send_or_pick_up_time = $config['is_show_request_send_or_pick_up_time']; // 是否展示发货日期
        $is_show_upload_image                 = $config['is_show_upload_image']; //是否展示图片 0 不展示 1 必填 2 选填
        $is_show_true_name                    = $config['is_show_true_name']; // 是否展示姓名
        $is_show_last_name                    = $config['is_show_last_name']; // 是否展示姓氏
        $is_show_sex                          = $config['is_show_sex']; // 是否展示性别

        $allow_submit_order                  = $config['allow_submit_order'];
        $auto_modify_owner_user_submit_order = $config['auto_modify_owner_user_submit_order']; //下单是否自动拆单

        if ($way == 1 && $allow_submit_order == 0 && empty($user_guid)) {
            error($config['forbid_submit_order_tips']);
        }
        if ($way == 1 && $member_guid && $is_show_request_send_or_pick_up_time && empty($params['request_send_or_pick_up_time'])) {
            error('请选择预约发货日期');
        }
        if ($way == 1 && $is_show_upload_image == 1 && empty($remark_image_list)) {
            error('请上传图片');
        }
        if ($way == 1 && empty($request_send_or_pick_up_store_guid) && $member_guid) {
            // 没有自提门店又没有收货信息
            if ($is_show_true_name == 1 && empty($params['true_name'])) {
                error('请输入姓名');
            }
            if (empty($params['mobile'])) {
                error('请输入手机号');
            }
            $params['mobile'] = tools()::find_string_number($params['mobile']);
            if (!tools()::is_mobile($params['mobile'])) {
                error('手机号格式不正确,请输入1开头11位手机号');
            }
            $is_show_address_area = $config['is_show_address_area']; // 是否展示地区信息
            $is_show_address      = $config['is_show_address']; //是否开启快递配送
            if ($is_show_address_area == 1 && $is_show_address == 1) {
                if (empty($params['province_id']) || empty($params['city_id']) || empty($params['area_id'])) {
                    error('请选择省市区');
                }
                if (empty($params['address'])) {
                    error('请输入地址');
                }
            }
        }
        if ($request_send_or_pick_up_time && $way == 1) {
            //不能早于今天
            if (strtotime($request_send_or_pick_up_time) < strtotime(date('Y-m-d'))) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能早于今天,请重新选择');
            }
            //不能早于备货日期
            $prepare_goods_days = (int)$config['prepare_goods_days'];
            $max_day            = (int)$config['max_request_send_or_pick_up_days'];
            $max_date           = date('Y-m-d', strtotime("+$max_day day"));
            $prepare_goods_date = date('Y-m-d', strtotime("+$prepare_goods_days day"));
            if (strtotime($request_send_or_pick_up_time) < strtotime($prepare_goods_date)) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能早于' . $prepare_goods_date . ',请重新选择');
            }
            if (strtotime($request_send_or_pick_up_time) > strtotime($max_date)) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能晚于' . $max_date . ',请重新选择');
            }
            //校验预约日期是否合法
            if ($coupon_guid) {
                $db_coupon         = new Coupon();
                $disable_date_list = $db_coupon->get_disable_date_list_by_coupon_guid($bid, $coupon_guid);
                $disable_date_list = $disable_date_list['disable_date_list'];
                foreach ($disable_date_list as $key => $val) {
                    if (strtotime($request_send_or_pick_up_time) == strtotime($val)) {
                        error('您预约的日期:' . $request_send_or_pick_up_time . '不在可选范围内,请重新选择');
                    }
                }
            }
        }

        $paid_money    = isset($params['paid_money']) ? floatval($params['paid_money']) : 0;
        $paid_point    = isset($params['paid_point']) ? floatval($params['paid_point']) : 0;
        $paid_wechat   = isset($params['paid_wechat']) ? floatval($params['paid_wechat']) : 0;
        $paid_coupon   = 0; //优惠券支付金额
        $point_use_num = 0; //积分使用数值
        if ($paid_point > 0) {
            //积分支付金额大于0 计算积分应该扣除数量
            $point_per_yuan = (int)$config['point_per_yuan'];  //X积分当一元花
            $point_use_num  = tools()::nc_price_calculate($paid_point, '*', $point_per_yuan, 2);
        }
        if (!empty($coupon_send_note_guid) && $way == 1) {
            // 只有提货业务才需要这个逻辑，送礼订单不需要
            $paid_coupon = tools()::nc_price_calculate($total_goods_price, '-', $deficit);
            $paid_wechat = tools()::nc_price_calculate($sum_price, '-', $paid_coupon);
        }

        //校验金额是否合法
        $total_paid = tools()::nc_price_calculate($paid_wechat, '+', $paid_money);
        $total_paid = tools()::nc_price_calculate($total_paid, '+', $paid_coupon);
        $total_paid = tools()::nc_price_calculate($total_paid, '+', $paid_point);

        if ($total_paid != $sum_price) {
            error('支付金额校验不通过,应付:' . $sum_price . ',实付:' . $total_paid . json_encode($order_submit_info, JSON_UNESCAPED_UNICODE));
        }
        if ($way == 2 && $sum_price <= 0) {
            error('订单总金额为0不允许下单');
        }

        //校验库存是否充足
        $not_enough_stock_goods_name_array = [];
        $db_goods                          = new GoodsModel();
        $map_goods                         = [
            ['bid', '=', $bid],
            ['stock_mode', '=', 1], // 开启了库存限制
            ['guid', 'IN', array_keys($goods_item_guid_array)] // 商品限制
        ];
        $stock_goods_array                 = $db_goods->where($map_goods)->field(['guid', 'name', 'stock'])->select();
        foreach ($goods_info_list as $goods) {
            $goods_num  = $goods['num'];
            $goods_guid = $goods['goods_guid'];
            foreach ($stock_goods_array as $stock_goods) {
                if ($stock_goods['guid'] == $goods_guid && $stock_goods['stock'] < $goods_num) {
                    $not_enough_stock_goods_name_array[] = $stock_goods['name'];
                }
            }
        }

        if (!empty($not_enough_stock_goods_name_array)) {
            error('产品【' . join('、', $not_enough_stock_goods_name_array) . '】库存不足!');
        }

        //校验通过后创建订单
        $pay_type = $paid_wechat > 0 ? 1 : 2; // 1 还需移动支付 2 会员全额支付

        $ip = tools()::get_client_ip();

        if ($type == 2) {
            //自提订单生成1开头 9位数 不重复的提货核销码
            $pick_up_code = $this->get_pick_up_code();
        }

        $db_user   = new User();
        $user_guid = $user_guid ?: $db_user->get_default_user_guid();
        $user_id   = $db_user->get_user_id_by_guid($user_guid, $bid);

        $all_goods_list    = [];
        $modify_owner_user = in_array($auto_modify_owner_user_submit_order, [1, 2]) &&
            $way == 1 && //提货业务
            $exchange_times == 1 && //兑换次数等于1
            $coupon_use_num == 1 && //优惠券使用了1张
            $paid_wechat == 0 && //微信支付金额 = 0
            $paid_money == 0 && //余额支付 = 0
            $paid_point == 0 && //积分支付 = 0
            $total_goods_price == $sum_price; //整单的商品金额就是订单金额
        if (!$modify_owner_user) {
            //如果不是 继续查看 是否测试BID
            $db_business     = new Business();
            $is_examples_bid = $db_business->is_examples_bid($bid);
            if ($is_examples_bid) {
                $modify_owner_user = in_array($auto_modify_owner_user_submit_order, [1, 2]) &&
                    $way == 2   //商城业务
                ;
            }
        }
        if ($modify_owner_user) {
            //进行拆单 $db_goods_order 要分次插入

            switch ($auto_modify_owner_user_submit_order) {
                case 1: //按供应商拆单
                    $db_goods            = new GoodsModel();
                    $map_goods           = [
                        ['bid', '=', $bid],
                        ['guid', 'IN', array_keys($goods_item_guid_array)]
                    ];
                    $owner_user_id_array = $db_goods->where($map_goods)->column('owner_user_id');
                    $owner_user_id_array = array_unique($owner_user_id_array);
                    foreach ($owner_user_id_array as $owner_user_id) {
                        //遍历每一个产品 分配到 $all_goods_list 数组
                        $temp_goods            = [];
                        $goods_owner_user_guid = '';
                        foreach ($goods_info_list as $goods) {
                            $goods_owner_user_id = $goods['owner_user_id'];
                            if ($goods_owner_user_id == $owner_user_id) {
                                $goods_owner_user_guid = $goods['owner_user_guid'];
                                $temp_goods[]          = $goods;
                            }
                        }
                        $all_goods_list[] = [
                            'user_guid'       => $goods_owner_user_guid,
                            'user_id'         => $owner_user_id,
                            'goods_info_list' => $temp_goods,
                        ];
                    }
                    break;
                case 2: //按供应商+商品拆单
                    foreach ($goods_info_list as $goods) {
                        $goods_owner_user_id   = $goods['owner_user_id'];
                        $goods_owner_user_guid = $goods['owner_user_guid'];
                        $all_goods_list[]      = [
                            'user_guid'       => $goods_owner_user_guid,
                            'user_id'         => $goods_owner_user_id,
                            'goods_info_list' => [$goods],
                        ];
                    }
                    break;
                default:
                    throw new Exception('暂时不支持的订单模式');
            }
        } else {
            //不拆单则归属用户为订单创建用户,所有商品不拆分
            $all_goods_list[] = [
                'user_guid'       => $user_guid,
                'user_id'         => $user_id,
                'goods_info_list' => $goods_info_list,
            ];
        }
        $main_order_guid_array = [];

        $current_discount_preferential_money = 0;
        $current_total_money                 = 0;
        $current_goods_money                 = 0;
        $current_extra_charges_money         = 0;
        $current_freight_money               = 0;

        $current_paid_money    = 0;
        $current_paid_point    = 0;
        $current_point_use_num = 0;
        $current_paid_wechat   = 0;
        $current_paid_coupon   = 0;

        $index           = 1;
        $bill_number     = $parent_bill_number = tools()::get_bill_number();
        $total_order_num = count($all_goods_list);
        foreach ($all_goods_list as $all_goods) {

            $parent_order_guid       = create_guid();
            $out_trade_no            = tools()::get_bill_number();
            $main_order_guid_array[] = $parent_order_guid;

            if ($exchange_times > 1 && $cycle_delivery_interval_days > 0) {
                $parent_bill_number = $bill_number . '-1';
            }

            $goods_info_list = $all_goods['goods_info_list'];
            $owner_user_id   = $all_goods['user_id'];
            $owner_user_guid = $all_goods['user_guid'];
            $item            = [];
            $goods_info      = [];
            $goods_amount    = 0;
            $goods_sum_price = 0;
            foreach ($goods_info_list as $goods) {
                //构造商品明细
                $goods_price             = $goods['price'];
                $goods_name              = $goods['name'];
                $goods_specs             = $goods['specs'];
                $pic                     = $goods['pic'];
                $goods_guid              = $goods['goods_guid'];
                $num                     = $goods['num'];
                $current_goods_sum_price = tools()::nc_price_calculate($goods_price, '*', $num, 2);
                $goods_sum_price         = tools()::nc_price_calculate($goods_sum_price, '+', $current_goods_sum_price, 2);
                $goods_amount            += $num;
                if (!empty($goods['attr']) && is_array($goods['attr'])) {
                    $attr_list = $goods['attr'];
                } elseif (!empty($goods['attr']) && tools()::is_json($goods['attr'])) {
                    $attr_list = json_decode($goods['attr'], true);
                } else {
                    $attr_list = [];
                }
                $sku_guid     = $goods['sku_guid'] ?? '';
                $goods_info[] = [
                    'guid'      => $goods_guid,
                    'amount'    => $num,
                    'name'      => $goods_name,
                    'specs'     => $goods_specs,
                    'pic'       => $pic,
                    'price'     => $goods_price,
                    'sku_guid'  => $sku_guid,
                    'attr_list' => $attr_list,
                ];
                $item[]       = [
                    'guid'        => create_guid(),
                    'bid'         => $bid,
                    'order_type'  => $way,
                    'order_guid'  => $parent_order_guid,
                    'goods_price' => $goods_price,
                    'goods_guid'  => $goods_guid,
                    'goods_name'  => $goods_name,
                    'sku_guid'    => $sku_guid,
                    'attr_list'   => $attr_list,
                    'amount'      => $num,
                ];
            }
            // $total_amount //商品总件数
            // $total_goods_price  //商品总金额
            // $discount_preferential_money  //优惠总金额
            // $extra_charges_price   //附加费
            // $express_price   //运费
            // $sum_price  //订单总价
            // $total_order_num 订单总数
            // $goods_amount 当前订单商品总数
            // $goods_sum_price 当前订单商品总金额

            // $paid_money //储值支付
            // $paid_point //积分支付
            // $point_use_num //积分扣除数量
            // $paid_wechat //微信支付金额
            // $paid_coupon //卡券支付金额


            $sub_order_used_num       = $coupon_use_num;
            $sub_order_coupon_use_num = $coupon_use_num;

            $sub_order_discount_preferential_money = $discount_preferential_money;

            $sub_order_total_amount = $modify_owner_user ? $goods_amount : $total_amount;
            $sub_order_total_money  = $modify_owner_user ? $goods_sum_price : $sum_price;
            $sub_order_goods_money  = $modify_owner_user ? $goods_sum_price : $total_goods_price;

            $sub_order_extra_charges_money = $extra_charges_price; //附加费
            $sub_order_freight_money       = $express_price; //运费

            $sub_order_paid_money    = $paid_money; //储值支付
            $sub_order_paid_point    = $paid_point; //积分支付
            $sub_order_point_use_num = $point_use_num; //积分扣除数量
            $sub_order_paid_wechat   = $paid_wechat; //微信支付金额
            $sub_order_paid_coupon   = $paid_coupon; //卡券支付金额

            //            $current_discount_preferential_money = 0;
            //            $current_total_money                 = 0;
            //            $current_goods_money                 = 0;
            //            $current_extra_charges_money         = 0;
            //            $current_freight_money               = 0;
            //            $current_paid_point                  = 0;
            //            $current_point_use_num               = 0;
            //            $current_paid_wechat                 = 0;
            //            $current_paid_coupon                 = 0;
            //            $current_paid_money                  = 0;


            if ($total_order_num > 1) {
                $parent_bill_number = $bill_number . '-' . $index;

                //拆单逻辑需要 分配金额 以便于退单和统计
                //                'used_num'              => $coupon_use_num, //优惠券使用张数,即将废弃
                //                'coupon_use_num'        => $coupon_use_num, //优惠券使用张数
                //                'total_amount'                => $modify_owner_user ? $goods_amount : $total_amount, //拆单则取单独统计的商品数量
                //                'discount_preferential_money' => $discount_preferential_money,
                //                'total_money'                 => $modify_owner_user ? $goods_sum_price : $sum_price,
                //                'goods_money'                 => $modify_owner_user ? $goods_sum_price : $total_goods_price,
                //                'freight_money'               => $express_price, // 运费金额
                //                'extra_charges_money'         => $extra_charges_price, //附加费

                if ($index > 1) {
                    //如果是第二个订单 这些金额都为0 因为算到主订单了
                    $sub_order_discount_preferential_money = 0; //优惠金额
                    $sub_order_extra_charges_money         = 0; //附加费
                    $sub_order_freight_money               = 0; //运费
                    $sub_order_paid_money                  = 0; //储值支付
                    $sub_order_paid_point                  = 0; //积分支付
                    $sub_order_point_use_num               = 0; //积分扣除数量
                    $sub_order_paid_wechat                 = 0; //微信支付金额
                    $sub_order_paid_coupon                 = 0; //卡券支付金额
                }
                //下面拆单后  支付扣款有问题
                //                $sub_order_discount_preferential_money = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $discount_preferential_money, $current_discount_preferential_money);
                //                $current_discount_preferential_money   = tools()::ncPriceCalculate($current_discount_preferential_money, '+', $sub_order_discount_preferential_money);
                //
                //                $sub_order_total_money = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $sum_price, $current_total_money);
                //                $current_total_money   = tools()::ncPriceCalculate($current_total_money, '+', $sub_order_total_money);
                //
                //
                //                $sub_order_extra_charges_money = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $extra_charges_price, $current_extra_charges_money);
                //                $current_extra_charges_money   = tools()::ncPriceCalculate($current_extra_charges_money, '+', $sub_order_extra_charges_money);
                //
                //
                //                $sub_order_freight_money = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $express_price, $current_freight_money);
                //                $current_freight_money   = tools()::ncPriceCalculate($current_freight_money, '+', $sub_order_freight_money);
                //
                //
                //                $sub_order_paid_point = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $paid_point, $current_paid_point);
                //                $current_paid_point   = tools()::ncPriceCalculate($current_paid_point, '+', $sub_order_paid_point);
                //
                //                $sub_order_point_use_num = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $point_use_num, $current_point_use_num);
                //                $current_point_use_num   = tools()::ncPriceCalculate($current_point_use_num, '+', $sub_order_point_use_num);
                //
                //                $sub_order_paid_wechat = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $paid_wechat, $current_paid_wechat);
                //                $current_paid_wechat   = tools()::ncPriceCalculate($current_paid_wechat, '+', $sub_order_paid_wechat);
                //
                //                $sub_order_paid_coupon = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $paid_coupon, $current_paid_coupon);
                //                $current_paid_coupon   = tools()::ncPriceCalculate($current_paid_coupon, '+', $sub_order_paid_coupon);
                //
                //                $sub_order_paid_money = $this->calculate_sub_order_money($total_order_num, $index, $total_goods_price, $goods_sum_price, $paid_money, $current_paid_money);
                //                $current_paid_money   = tools()::ncPriceCalculate($current_paid_money, '+', $sub_order_paid_money);
            }

            $index++;
            //创建订单
            $order_data = [
                'guid'        => $parent_order_guid,
                'bid'         => $bid,
                'openid'      => $openid,
                'appid'       => $appid,
                'member_guid' => $member_guid,

                'cycle_times'           => 1, //第一期
                'bill_number'           => $parent_bill_number,
                'coupon_guid'           => $coupon_guid,
                'coupon_send_note_guid' => $coupon_send_note_guid,
                'used_num'              => $coupon_use_num, //优惠券使用张数,即将废弃
                'coupon_use_num'        => $coupon_use_num, //优惠券使用张数

                'goods_item_guid' => $goods_item_guid_array,
                'goods_info'      => $goods_info,
                'user_id'         => $user_id,
                'user_guid'       => $user_guid,

                'owner_user_id'   => $owner_user_id,
                'owner_user_guid' => $owner_user_guid,

                'way'    => $way,  //1 提货订单  2 商城订单
                'type'   => $type,
                // 'type'   => $request_send_or_pick_up_store_guid ? 2 : 1, //1 快递发货  2 到店自提
                'status' => -1, //待支付

                'third_order_number' => $pay_type == 1 ? $out_trade_no : '',

                'total_amount' => $modify_owner_user ? $goods_amount : $total_amount, //拆单则取单独统计的商品数量
                'goods_money'  => $modify_owner_user ? $goods_sum_price : $total_goods_price,

                'discount_preferential_money' => $sub_order_discount_preferential_money,
                'total_money'                 => $sub_order_total_money,
                'freight_money'               => $sub_order_freight_money, // 运费金额
                'extra_charges_money'         => $sub_order_extra_charges_money, //附加费


                'paid_money'    => $sub_order_paid_money, //储值支付
                'paid_point'    => $sub_order_paid_point, //积分支付
                'point_use_num' => $sub_order_point_use_num, //积分扣除数量
                'paid_wechat'   => $sub_order_paid_wechat, //微信支付金额
                'paid_coupon'   => $sub_order_paid_coupon, //卡券支付金额

                'pay_type' => $pay_type, //1 移动支付 2 纯会员支付


                'extra_charges_name' => $extra_charges_name, //附加费名称
                'province_id'        => $province_id,
                'city_id'            => $city_id,
                'area_id'            => $area_id,
                'address'            => $address,
                'true_name'          => $true_name,
                'last_name'          => $last_name,
                'sex'                => $sex,
                'mobile'             => $mobile,

                'id_card_number'    => $id_card_number,
                'remark'            => $remark, //备注
                'remark_image_list' => $remark_image_list,
                'ip'                => $ip,

                'request_send_or_pick_up_time'       => $request_send_or_pick_up_time,
                'request_send_or_pick_up_store_guid' => $request_send_or_pick_up_store_guid,
                'pick_up_code'                       => $pick_up_code,


                'extend_field_1' => $extend_field_1,
                'extend_field_2' => $extend_field_2,
                'extend_field_3' => $extend_field_3,

                'share_member_guid' => $share_member_guid,
                'share_user_guid'   => $share_user_guid,

            ];
            //先插入订单表
            $db_goods_order    = new GoodsOrderModel();
            $parent_order_data = $order_data;
            $db_goods_order->save($order_data);
            //插入订单明细表(主订单)
            $db_goods_order_item = new GoodsOrderItem();
            $db_goods_order_item->saveAll($item, false);

            if ($exchange_times > 1 && $cycle_delivery_interval_days > 0 && $coupon_use_num > 1) {
                //周期配送继续创建子订单
                $sub_order_data = [];
                for ($i = 1; $i < $exchange_times; $i++) {
                    $now_cycle_delivery_interval_days           = $cycle_delivery_interval_days * $i;
                    $sub_order_guid                             = create_guid();
                    $order_data['guid']                         = $sub_order_guid;
                    $order_data['bill_number']                  = $bill_number . '-' . ($i + 1);
                    $order_data['cycle_times']                  = $i + 1;
                    $order_data['parent_order_guid']            = $parent_order_guid;
                    $order_data['parent_bill_number']           = $parent_bill_number;
                    $order_data['request_send_or_pick_up_time'] = date('Y-m-d', strtotime("$request_send_or_pick_up_time + $now_cycle_delivery_interval_days day"));
                    $sub_order_data[]                           = $order_data;
                    foreach ($item as $key => $val) {
                        $item[$key]['guid']       = create_guid();
                        $item[$key]['order_guid'] = $sub_order_guid;
                    }
                    //插入订单明细表
                    $db_goods_order_item->saveAll($item, false);
                }
                $db_goods_order->saveAll($sub_order_data, false);
            }
        }

        if (!empty($cart_guid_list_array)) {
            //清空购物车
            $map = [
                ['bid', '=', $bid],
                ['guid', 'in', $cart_guid_list_array]
            ];
            $db_cart->where($map)->delete();
        }
        if ($token && !is_debug()) {
            //清空token
            cache($token, null);
        }
        //完成支付
        $parent_order_data['order_guid_array'] = $main_order_guid_array;

        $first_order_guid                = reset($main_order_guid_array);
        $parent_order_data['order_guid'] = $first_order_guid;

        if ($paid_wechat == 0) {
            return $this->pay(['order_guid' => $first_order_guid, 'order_guid_array' => $main_order_guid_array]);
        }
        // return ['order_guid' => $order_guid, 'pay_type' => $pay_type];
        return $parent_order_data;
    }

    // 统一下单V2

    /**
     * @throws NotNotifyException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception|Throwable
     */
    public function submit(array $params)
    {
        $bid = $this->get_bid_from_request();
//        $db_business = new Business();
//        $is_examples_bid = $db_business->is_examples_bid($bid);
        //        if ($is_examples_bid) {
        //            return $this->submit_beta($params);
        //        }
        $member_guid = (string)$this->get_member_guid_from_request();
        $user_guid   = (string)$this->get_user_guid_from_request();
        $openid      = (string)$this->get_openid_from_request();
        $appid       = (string)$this->get_appid_from_request();

        $share_member_guid = $params['share_member_guid'] ?? null;
        $share_user_guid   = $params['share_user_guid'] ?? null;

        $id_card_number    = isset($params['id_card_number']) ? strtoupper($params['id_card_number']) : null;
        $remark            = $params['remark'] ?? null;
        $remark_image_list = $params['remark_image_list'] ?? null;

        $request_send_or_pick_up_time       = $params['request_send_or_pick_up_time'] ?? null; //预约时间
        $request_send_or_pick_up_store_guid = $params['request_send_or_pick_up_store_guid'] ?? null; //自提门店
        $cycle_delivery_interval_days       = $params['cycle_delivery_interval_days'] ?? 0; //周期发货间隔天数
        if ($cycle_delivery_interval_days > 100) {
            error('间隔天数不能超过100天');
        }
        $cycle_delivery_min_interval_days = $params['cycle_delivery_min_interval_days'] ?? null; //周期发货最小间隔天数
        $exchange_times                   = $params['exchange_times'] ?? 1; //提货次数
        $pick_up_times_type               = $params['pick_up_times_type'] ?? ''; //提货模式 once cycle
        if ($pick_up_times_type == 'once') {
            $exchange_times = 1; //如果是一次提货 次数强制设置为 1
        }
        $type         = $params['type'] ?? ($request_send_or_pick_up_store_guid ? 2 : 1); // 1 快递发货  2 到店自提
        $pick_up_code = 0; //核销码默认0 type=2 的时候生成核销码

        $db_cart           = new CartModel();
        $order_submit_info = $this->get_order_submit_info($params);

        //todo 做参数校验

        if ($id_card_number && !tools()::is_id_card_number($id_card_number)) {
            error('身份证号不正确');
        }


        $total_amount                = $order_submit_info['total_goods_num'];
        $total_goods_price           = $order_submit_info['total_goods_price'];
        $discount_preferential_money = $order_submit_info['discount_preferential_money'];
        $extra_charges_price         = $order_submit_info['extra_charges_price'];
        $express_price               = $order_submit_info['express_price'];
        $extra_charges_name          = $order_submit_info['extra_charges_name'];
        $deficit                     = $order_submit_info['deficit'] ?? 0; //差额 金额卡涉及补差价,仅允许微信支付

        $sum_price    = $order_submit_info['sum_price'];
        $address_info = $order_submit_info['address'];

        $goods_list_array      = $order_submit_info['goods_list_array'];
        $goods_item_guid_array = $order_submit_info['goods_item_guid_array'];


        $goods_info_list      = $order_submit_info['goods_info_list'];
        $cart_guid_list_array = $order_submit_info['cart_guid_list_array'];
        $way                  = $order_submit_info['way']; // 1 提货业务 2 产品商城
        $coupon_guid          = $order_submit_info['coupon_guid'];

        $coupon_send_note_guid = $order_submit_info['coupon_send_note_guid'];
        $coupon_use_num        = $order_submit_info['coupon_use_num'];
        $token                 = $order_submit_info['token'];

        //地址信息
        $true_name = $address_info['true_name'] ?? ''; //姓名
        $last_name = $params['last_name'] ?? ''; //姓氏
        $sex       = $params['sex'] ?? 0; //性别 0 未知 1 男 2 女
        if (empty($true_name) && $last_name && $sex) {
            $true_name = $last_name . ($sex == 1 ? '先生' : '女士');
        }
        $mobile      = $address_info['mobile'] ?? '';
        $province_id = $address_info['province_id'] ?? 0;
        $city_id     = $address_info['city_id'] ?? 0;
        $area_id     = $address_info['area_id'] ?? 0;
        $address     = $address_info['address'] ?? '';
        if ($request_send_or_pick_up_store_guid && empty($province_id) && empty($city_id) && empty($area_id)) {
            $type = 2;  // 1 快递发货  2 到店自提 如果有自提门店 且 省市区为空, 则说明是到店自提 临时修复 20221205
        }

        //扩展信息

        $extend_field_1 = $params['extend_field_1'] ?? '';
        $extend_field_2 = $params['extend_field_2'] ?? '';
        $extend_field_3 = $params['extend_field_3'] ?? '';
        if ($way == 1) {
            $db_extend_field_model = new ExtendFieldModel();
            $check                 = $db_extend_field_model->check_extend_field_array($bid, $params);
            if ($check === false) {
                error($db_extend_field_model->getError());
            }
        }
        $config                               = get_config_by_bid($bid, 'coupon', $coupon_guid);
        $is_show_request_send_or_pick_up_time = $config['is_show_request_send_or_pick_up_time']; // 是否展示发货日期
        $is_show_upload_image                 = $config['is_show_upload_image']; //是否展示图片
        $is_show_true_name                    = $config['is_show_true_name']; // 是否展示姓名
        $is_show_last_name                    = $config['is_show_last_name']; // 是否展示姓氏
        $is_show_sex                          = $config['is_show_sex']; // 是否展示性别

        $allow_submit_order                  = $config['allow_submit_order'];
        $auto_modify_owner_user_submit_order = $config['auto_modify_owner_user_submit_order']; //下单是否自动拆单

        if ($way == 1 && $allow_submit_order == 0 && empty($user_guid)) {
            error($config['forbid_submit_order_tips']);
        }
        if ($way == 1 && $member_guid && $is_show_request_send_or_pick_up_time && empty($params['request_send_or_pick_up_time'])) {
            error('请选择预约发货日期');
        }
        if ($way == 1 && $is_show_upload_image == 1 && empty($remark_image_list)) {
            error('请上传图片');
        }

        if ($way == 1 && empty($request_send_or_pick_up_store_guid) && $member_guid) {
            // 没有自提门店又没有收货信息
            if ($is_show_true_name == 1 && empty($params['true_name'])) {
                error('请输入姓名');
            }
            if (empty($params['mobile'])) {
                error('请输入手机号');
            }
            $params['mobile'] = tools()::find_string_number($params['mobile']);
            if (!tools()::is_mobile($params['mobile'])) {
                error('手机号格式不正确,请输入1开头11位手机号');
            }
            $is_show_address_area = $config['is_show_address_area']; // 是否展示地区信息
            $is_show_address      = $config['is_show_address']; //是否开启快递配送
            if ($is_show_address_area == 1 && $is_show_address == 1) {
                if (empty($params['province_id']) || empty($params['city_id']) || empty($params['area_id'])) {
                    error('请选择省市区');
                }
                if (empty($params['address'])) {
                    error('请输入地址');
                }
            }
        }

        if ($way == 2 && $type == 2) {
            if (empty($order_submit_info['address']['true_name'] ?? '')) {
                error('请填写联系人姓名');
            }
            if (!tools()::is_mobile($order_submit_info['address']['mobile'] ?? '')) {
                error('手机号格式不正确');
            }
        }

        if ($request_send_or_pick_up_time && $way == 1) {
            //不能早于今天
            if (strtotime($request_send_or_pick_up_time) < strtotime(date('Y-m-d'))) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能早于今天,请重新选择');
            }
            //不能早于备货日期
            $prepare_goods_days = (int)$config['prepare_goods_days'];
            $max_day            = (int)$config['max_request_send_or_pick_up_days'];
            $max_date           = date('Y-m-d', strtotime("+$max_day day"));
            $prepare_goods_date = date('Y-m-d', strtotime("+$prepare_goods_days day"));
            if (strtotime($request_send_or_pick_up_time) < strtotime($prepare_goods_date)) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能早于' . $prepare_goods_date . ',请重新选择');
            }
            if (strtotime($request_send_or_pick_up_time) > strtotime($max_date)) {
                error('您预约的日期:' . $request_send_or_pick_up_time . '不能晚于' . $max_date . ',请重新选择');
            }
            //校验预约日期是否合法
            if ($coupon_guid) {
                $db_coupon         = new Coupon();
                $disable_date_list = $db_coupon->get_disable_date_list_by_coupon_guid($bid, $coupon_guid);
                $disable_date_list = $disable_date_list['disable_date_list'];
                foreach ($disable_date_list as $key => $val) {
                    if (strtotime($request_send_or_pick_up_time) == strtotime($val)) {
                        error('您预约的日期:' . $request_send_or_pick_up_time . '不在可选范围内,请重新选择');
                    }
                }
            }
        }

        $paid_money  = isset($params['paid_money']) ? floatval($params['paid_money']) : 0;
        $paid_point  = isset($params['paid_point']) ? floatval($params['paid_point']) : 0;
        $paid_wechat = isset($params['paid_wechat']) ? floatval($params['paid_wechat']) : 0;

        $paid_coupon   = 0; //优惠券支付金额
        $point_use_num = 0; //优惠券使用张数
        if ($paid_point > 0) {
            //积分支付金额大于0 计算积分应该扣除数量
            $point_per_yuan = (int)$config['point_per_yuan'];  //X积分当一元花
            $point_use_num  = tools()::nc_price_calculate($paid_point, '*', $point_per_yuan, 2);
        }
        if (!empty($coupon_send_note_guid) && $way == 1) {
            // 只有提货业务才需要这个逻辑，送礼订单不需要
            $paid_coupon = tools()::nc_price_calculate($total_goods_price, '-', $deficit);
            $paid_wechat = tools()::nc_price_calculate($sum_price, '-', $paid_coupon);
        }

        //校验金额是否合法
        $total_paid = tools()::nc_price_calculate($paid_wechat, '+', $paid_money);
        $total_paid = tools()::nc_price_calculate($total_paid, '+', $paid_coupon);
        $total_paid = tools()::nc_price_calculate($total_paid, '+', $paid_point);

        if ($total_paid != $sum_price) {
            error('支付金额校验不通过,应付:' . $sum_price . ',实付:' . $total_paid . json_encode($order_submit_info, JSON_UNESCAPED_UNICODE));
        }
        if ($way == 2 && $sum_price <= 0) {
            error('订单总金额为0不允许下单');
        }

        //校验库存是否充足
        $not_enough_stock_goods_name_array = [];
        $db_goods                          = new GoodsModel();
        $map_goods                         = [
            ['bid', '=', $bid],
            ['stock_mode', '=', 1], // 开启了库存限制
            ['guid', 'IN', array_keys($goods_item_guid_array)] // 商品限制
        ];
        $stock_goods_array                 = $db_goods->where($map_goods)->field(['guid', 'name', 'stock'])->select();
        foreach ($goods_info_list as $goods) {
            $goods_num  = $goods['num'];
            $goods_guid = $goods['goods_guid'];
            foreach ($stock_goods_array as $stock_goods) {
                if ($stock_goods['guid'] == $goods_guid && $stock_goods['stock'] < $goods_num) {
                    $not_enough_stock_goods_name_array[] = $stock_goods['name'];
                }
            }
        }

        if (!empty($not_enough_stock_goods_name_array)) {
            error('产品【' . join('、', $not_enough_stock_goods_name_array) . '】库存不足!');
        }

        //校验通过后创建订单
        $pay_type = $paid_wechat > 0 ? 1 : 2; // 1 还需移动支付 2 会员全额支付

        $ip = tools()::get_client_ip();

        if ($type == 2) {
            //自提订单生成1开头 9位数 不重复的提货核销码
            $pick_up_code = $this->get_pick_up_code();
        }

        $db_user   = new User();
        $user_guid = $user_guid ?: $db_user->get_default_user_guid();
        $user_id   = $db_user->get_user_id_by_guid($user_guid, $bid);

        $all_goods_list    = [];
        $modify_owner_user = in_array($auto_modify_owner_user_submit_order, [1, 2]) &&
            $way == 1 && //提货业务
            $exchange_times == 1 && //兑换次数等于1
            $coupon_use_num == 1 && //优惠券使用了1张
            $paid_wechat == 0 && //微信支付金额 = 0
            $paid_money == 0 && //余额支付 = 0
            $paid_point == 0 && //积分支付 = 0
            $total_goods_price == $sum_price; //整单的商品金额就是订单金额
        if ($modify_owner_user) {
            //进行拆单 $db_goods_order 要分次插入

            switch ($auto_modify_owner_user_submit_order) {
                case 1: //按供应商拆单
                    $db_goods            = new GoodsModel();
                    $map_goods           = [
                        ['bid', '=', $bid],
                        ['guid', 'IN', array_keys($goods_item_guid_array)]
                    ];
                    $owner_user_id_array = $db_goods->where($map_goods)->column('owner_user_id');
                    $owner_user_id_array = array_unique($owner_user_id_array);
                    foreach ($owner_user_id_array as $owner_user_id) {
                        //遍历每一个产品 分配到 $all_goods_list 数组
                        $temp_goods            = [];
                        $goods_owner_user_guid = '';
                        foreach ($goods_info_list as $goods) {
                            $goods_owner_user_id = $goods['owner_user_id'];
                            if ($goods_owner_user_id == $owner_user_id) {
                                $goods_owner_user_guid = $goods['owner_user_guid'];
                                $temp_goods[]          = $goods;
                            }
                        }
                        $all_goods_list[] = [
                            'user_guid'       => $goods_owner_user_guid,
                            'user_id'         => $owner_user_id,
                            'goods_info_list' => $temp_goods,
                        ];
                    }
                    break;
                case 2: //按供应商+商品拆单
                    foreach ($goods_info_list as $goods) {
                        $goods_owner_user_id   = $goods['owner_user_id'];
                        $goods_owner_user_guid = $goods['owner_user_guid'];
                        $all_goods_list[]      = [
                            'user_guid'       => $goods_owner_user_guid,
                            'user_id'         => $goods_owner_user_id,
                            'goods_info_list' => [$goods],
                        ];
                    }
                    break;
                default:
                    throw new Exception('暂时不支持的订单模式');
            }
        } else {
            //不拆单则归属用户为订单创建用户,所有商品不拆分
            $all_goods_list[] = [
                'user_guid'       => $user_guid,
                'user_id'         => $user_id,
                'goods_info_list' => $goods_info_list,
            ];
        }
        $main_order_guid_array = [];
        $index                 = 1;
        $bill_number           = $parent_bill_number = tools()::get_bill_number();

        if (empty($member_guid) && $coupon_send_note_guid) {
            $db_coupon_send_note  = new CouponSendNote();
            $map_coupon_send_note = [
                ['bid', '=', $bid],
                ['guid', '=', $coupon_send_note_guid],
            ];
            $member_guid          = (string)($db_coupon_send_note->where($map_coupon_send_note)->value('member_guid'));
        }
        foreach ($all_goods_list as $all_goods) {
            if (count($all_goods_list) > 1) {
                $parent_bill_number = $bill_number . '-' . $index;
            }
            $index++;
            $parent_order_guid       = create_guid();
            $out_trade_no            = tools()::get_bill_number();
            $main_order_guid_array[] = $parent_order_guid;

            if ($exchange_times > 1 && $cycle_delivery_interval_days > 0) {
                $parent_bill_number = $bill_number . '-1';
            }

            $goods_info_list = $all_goods['goods_info_list'];
            $owner_user_id   = $all_goods['user_id'];
            $owner_user_guid = $all_goods['user_guid'];
            $item            = [];
            $goods_info      = [];
            $goods_amount    = 0;
            $goods_sum_price = 0;
            foreach ($goods_info_list as $goods) {
                //构造商品明细
                $goods_price             = $goods['price'];
                $goods_name              = $goods['name'];
                $goods_specs             = $goods['specs'];
                $pic                     = $goods['pic'];
                $goods_guid              = $goods['goods_guid'];
                $num                     = $goods['num'];
                $current_goods_sum_price = tools()::nc_price_calculate($goods_price, '*', $num, 2);
                $goods_sum_price         = tools()::nc_price_calculate($goods_sum_price, '+', $current_goods_sum_price, 2);
                $goods_amount            += $num;
                if (!empty($goods['attr']) && is_array($goods['attr'])) {
                    $attr_list = $goods['attr'];
                } elseif (!empty($goods['attr']) && tools()::is_json($goods['attr'])) {
                    $attr_list = json_decode($goods['attr'], true);
                } else {
                    $attr_list = [];
                }
                $sku_guid     = $goods['sku_guid'] ?? '';
                $goods_info[] = [
                    'guid'      => $goods_guid,
                    'amount'    => $num,
                    'name'      => $goods_name,
                    'specs'     => $goods_specs,
                    'pic'       => $pic,
                    'price'     => $goods_price,
                    'sku_guid'  => $sku_guid,
                    'attr_list' => $attr_list,
                ];
                $item[]       = [
                    'guid'        => create_guid(),
                    'bid'         => $bid,
                    'order_type'  => $way,
                    'order_guid'  => $parent_order_guid,
                    'goods_price' => $goods_price,
                    'goods_guid'  => $goods_guid,
                    'goods_name'  => $goods_name,
                    'sku_guid'    => $sku_guid,
                    'attr_list'   => $attr_list,
                    'amount'      => $num,
                ];
            }

            //创建订单
            $order_data = [
                'guid'        => $parent_order_guid,
                'bid'         => $bid,
                'openid'      => $openid,
                'appid'       => $appid,
                'member_guid' => $member_guid,

                'cycle_times'           => 1, //第一期
                'bill_number'           => $parent_bill_number,
                'coupon_guid'           => $coupon_guid,
                'coupon_send_note_guid' => $coupon_send_note_guid,
                'used_num'              => $coupon_use_num, //优惠券使用张数,即将废弃
                'coupon_use_num'        => $coupon_use_num, //优惠券使用张数

                'goods_item_guid' => $goods_item_guid_array,
                'goods_info'      => $goods_info,
                'user_id'         => $user_id,
                'user_guid'       => $user_guid,

                'owner_user_id'   => $owner_user_id,
                'owner_user_guid' => $owner_user_guid,

                'way'    => $way,  //1 提货订单  2 商城订单
                'type'   => $type,
                // 'type'   => $request_send_or_pick_up_store_guid ? 2 : 1, //1 快递发货  2 到店自提
                'status' => -1, //待支付

                'third_order_number' => $pay_type == 1 ? $out_trade_no : '',

                'total_amount'                => $modify_owner_user ? $goods_amount : $total_amount, //拆单则取单独统计的商品数量
                'discount_preferential_money' => $discount_preferential_money,
                'total_money'                 => $modify_owner_user ? $goods_sum_price : $sum_price,
                'goods_money'                 => $modify_owner_user ? $goods_sum_price : $total_goods_price,
                'freight_money'               => $express_price, //运费金额
                'extra_charges_money'         => $extra_charges_price, //附加费
                'extra_charges_name'          => $extra_charges_name, //附加费名称

                'province_id' => $province_id,
                'city_id'     => $city_id,
                'area_id'     => $area_id,
                'address'     => $address,
                'true_name'   => $true_name,
                'last_name'   => $last_name,
                'sex'         => $sex,
                'mobile'      => $mobile,

                'id_card_number'    => $id_card_number,
                'remark'            => $remark, //备注
                'remark_image_list' => $remark_image_list,
                'ip'                => $ip,

                'request_send_or_pick_up_time'       => $request_send_or_pick_up_time,
                'request_send_or_pick_up_store_guid' => $request_send_or_pick_up_store_guid,
                'pick_up_code'                       => $pick_up_code,

                'paid_money'    => $paid_money, //储值支付
                'paid_point'    => $paid_point, //积分支付
                'point_use_num' => $point_use_num, //积分扣除数量
                'paid_wechat'   => $paid_wechat, //微信支付金额
                'paid_coupon'   => $paid_coupon, //微信支付金额
                'pay_type'      => $pay_type, //1 移动支付 2 纯会员支付

                'extend_field_1' => $extend_field_1,
                'extend_field_2' => $extend_field_2,
                'extend_field_3' => $extend_field_3,

                'share_member_guid' => $share_member_guid,
                'share_user_guid'   => $share_user_guid,

            ];
            //先插入订单表
            $db_goods_order      = new GoodsOrderModel();
            $db_goods_order_item = new GoodsOrderItem();

            $parent_order_data = $order_data;
            $db_goods_order->save($order_data);
            //插入订单明细表(主订单)
            $db_goods_order_item->saveAll($item, false);

            if ($exchange_times > 1 && $cycle_delivery_interval_days > 0 && $coupon_use_num > 1) {
                //周期配送继续创建子订单
                $sub_order_data = [];
                for ($i = 1; $i < $exchange_times; $i++) {
                    $now_cycle_delivery_interval_days           = $cycle_delivery_interval_days * $i;
                    $sub_order_guid                             = create_guid();
                    $order_data['guid']                         = $sub_order_guid;
                    $order_data['bill_number']                  = $bill_number . '-' . ($i + 1);
                    $order_data['cycle_times']                  = $i + 1;
                    $order_data['parent_order_guid']            = $parent_order_guid;
                    $order_data['parent_bill_number']           = $parent_bill_number;
                    $order_data['request_send_or_pick_up_time'] = date('Y-m-d', strtotime("$request_send_or_pick_up_time + $now_cycle_delivery_interval_days day")); //只处理最近30天数据
                    $sub_order_data[]                           = $order_data;
                    foreach ($item as $key => $val) {
                        $item[$key]['guid']       = create_guid();
                        $item[$key]['order_guid'] = $sub_order_guid;
                    }
                    //插入订单明细表
                    $db_goods_order_item->saveAll($item, false);
                }
                $db_goods_order->saveAll($sub_order_data, false);
            }
        }

        if (!empty($cart_guid_list_array)) {
            //清空购物车
            $map = [
                ['bid', '=', $bid],
                ['guid', 'in', $cart_guid_list_array]
            ];
            $db_cart->where($map)->delete();
        }

        if ($token && !is_debug()) {
            //清空token
            cache($token, null);
        }
        //完成支付
        $parent_order_data['order_guid_array'] = $main_order_guid_array;

        $first_order_guid                = reset($main_order_guid_array);
        $parent_order_data['order_guid'] = $first_order_guid;


        //判断如果way=3 则写赠礼记录
        if ($way == 3) {
            $gift_expire_time      = '';
            $gift_expire_time_type = $params['gift_expire_time_type'] ?? '';
            if ($gift_expire_time_type) {
                $new_timestamp    = strtotime($gift_expire_time_type, time());
                $gift_expire_time = date('Y-m-d H:i:s', $new_timestamp);
            }
            $coupon_gift_order_data    = [
                'bid'                   => $bid,
                'coupon_send_note_guid' => $coupon_send_note_guid,
                'order_guid'            => $first_order_guid,
                'sender_member_guid'    => $member_guid,
                'sender_openid'         => $openid,
                'gift_message'          => $params['gift_message'] ?? '',
                'gift_password'         => $params['gift_password'] ?? '',
                'expire_time'           => $gift_expire_time
            ];
            $db_coupon_gift_order_note = new CouponGiftOrderNote();
            $db_coupon_gift_order_note->create_gift_record($coupon_gift_order_data);
        }


        if ($paid_wechat == 0) {
            return $this->pay(['order_guid' => $first_order_guid, 'order_guid_array' => $main_order_guid_array]);
        }
        // return ['order_guid' => $order_guid, 'pay_type' => $pay_type];
        return $parent_order_data;
    }

    public function refund_third_paid_money($bid, $order_guid)
    {
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $db_goods_order = new GoodsOrderModel();
        $order_info     = $db_goods_order->where($map)->findOrFail();
        $order_status   = $order_info['status'];
        if (!in_array($order_status, [-3])) {
            error('重新退款仅支持订单状态为【已退款】的订单');
        }
        $pay_type             = $order_info['pay_type'];
        $total_money          = $order_info['total_money'];
        $bill_number          = $order_info['bill_number'];
        $paid_point           = $order_info['paid_point'];
        $paid_wechat          = $order_info['paid_wechat'];
        $out_trade_no         = $order_info['third_order_number'];
        $member_guid          = $order_info['member_guid'];
        $before_refund_result = $order_info['refund_result'];
        if ($paid_wechat <= 0) {
            error('微信支付金额为0,无需退款');
        }
        $refund_result = '';
        $order         = [
            'out_trade_no'  => $out_trade_no,
            'out_refund_no' => tools()::get_bill_number(),
            'refund_fee'    => tools()::nc_price_yuan2fen($paid_wechat),
            'refund_desc'   => '商城退款',
        ];
        try {
            $pay                  = pay($bid)->driver('wechat')->scene('mp');
            $result               = $pay->refund($order);
            $append_refund_result = '退款成功';
        } catch (Exception | Throwable $e) {
            $append_refund_result = $refund_result .= '微信退款失败:' . $e->getMessage();
            wr_log('微信退款失败:订单号' . $bill_number . $e->getMessage());
        }
        $update_data = ['refund_result' => $before_refund_result . $append_refund_result];
        $db_goods_order::update($update_data, $map);
        return $refund_result;
    }

    /**
     *退单
     * @access public
     * @return mixed
     * @throws Exception
     */
    public function refund($params)
    {
        $bid            = $this->get_bid_from_request();
        $order_guid     = $params['order_guid'];
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $db_goods_order = new GoodsOrderModel();
        $order_info     = $db_goods_order->where($map)->findOrFail();
        $order_status   = $order_info['status'];
        if (!in_array($order_status, [0, 1, 2])) {
            error('仅允许状态为待发货/已发货/已完成订单进行退款');
        }
        $order_way = $order_info['way'];
        if (!in_array($order_way, [1, 2])) {
            // 1提货 2商城
            error('仅允商城或提货订单进行退款');
        }
        $pay_type              = $order_info['pay_type'];
        $total_money           = $order_info['total_money'];
        $bill_number           = $order_info['bill_number'];
        $paid_point            = $order_info['paid_point'];
        $paid_wechat           = $order_info['paid_wechat'];
        $paid_coupon           = $order_info['paid_coupon'];
        $coupon_guid           = $order_info['coupon_guid'];
        $coupon_send_note_guid = $order_info['coupon_send_note_guid'];
        $out_trade_no          = $order_info['third_order_number'];
        $member_guid           = $order_info['member_guid'];
        $refund_result         = ''; //退款结果
        if ($order_way == 1 && $paid_wechat == 0) {
            $this->del($params, 2);
            $refund_result = '卡券提货订单退款完成';
            //            error('提货订单仅允许使用微信支付的订单退款');
        }
        if ($order_way == 2 && $paid_coupon > 0) {
            error('使用优惠券支付,暂不支持退单');
        }
        // 1 更新状态为已退款
        $refund_user_id   = $this->get_user_id_from_request();
        $refund_user_guid = $this->get_user_guid_from_request();
        $update_data      = [
            'refund_time'      => format_timestamp(),
            'status'           => -3, //已退款
            'refund_user_id'   => $refund_user_id,
            'refund_user_guid' => $refund_user_guid,
        ];
        $db_goods_order::update($update_data, $map);

        // 2 退佣金
        $map_member_brokerage_note = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['relation_guid', '=', $order_guid],
        ];
        $db_member_brokerage_note  = new MemberBrokerageNote();
        $brokerage_note_guid_array = $db_member_brokerage_note->where($map_member_brokerage_note)->order(['brokerage' => 'ASC'])->column('guid');
        if (!empty($brokerage_note_guid_array)) {
            foreach ($brokerage_note_guid_array as $brokerage_note_guid) {
                try {
                    $db_member_brokerage_note->undo($bid, $brokerage_note_guid);
                } catch (Exception | Throwable $e) {
                    $refund_result .= '佣金撤销失败:' . $e->getMessage();
                    wr_log('佣金撤销失败:订单号' . $bill_number . $e->getMessage(), 1);
                }
            }
        }

        // 3 退余额
        $db_member_money_note  = new MemberMoneyNote();
        $map_member_money_note = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['relation_guid', '=', $order_guid],
        ];
        $money_note_guid_array = $db_member_money_note->where($map_member_money_note)->order(['money' => 'ASC'])->column('guid');
        if (!empty($money_note_guid_array)) {
            foreach ($money_note_guid_array as $money_note_guid) {
                try {
                    $db_member_money_note->undo($bid, $money_note_guid);
                } catch (Exception | Throwable $e) {
                    $refund_result .= '余额撤销失败:' . $e->getMessage();
                    wr_log('余额撤销失败:订单号' . $bill_number . $e->getMessage(), 1);
                }
            }
        }
        // 4 撤销积分记录 由于可能消费赠送积分,所以不能判断积分支付大于0 才执行
        $db_member_point_note  = new MemberPointNote();
        $map_member_point_note = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['relation_guid', '=', $order_guid],
        ];
        $point_note_guid_array = $db_member_point_note->where($map_member_point_note)->order(['point' => 'ASC'])->column('guid');
        if (!empty($point_note_guid_array)) {
            foreach ($point_note_guid_array as $point_note_guid) {
                try {
                    $db_member_point_note->undo($bid, $point_note_guid);
                } catch (Exception | Throwable $e) {
                    $refund_result .= '积分撤销失败:' . $e->getMessage();
                    wr_log('积分撤销失败:订单号' . $bill_number . $e->getMessage(), 1);
                }
            }
        }
        // 5 再进行退资金
        if ($paid_wechat > 0) {
            $order = [
                'out_trade_no'  => $out_trade_no,
                'out_refund_no' => tools()::get_bill_number(),
                'refund_fee'    => tools()::nc_price_yuan2fen($paid_wechat),
                'refund_desc'   => '商城退款',
            ];
            try {
                $pay    = pay($bid)->driver('wechat')->scene('mp');
                $result = $pay->refund($order);
            } catch (Exception | Throwable $e) {
                $refund_result .= '微信退款失败:' . $e->getMessage();
                wr_log('微信退款失败:订单号' . $bill_number . $e->getMessage(), 1);
            }
        }
        if ($refund_result) {
            $update_data = ['refund_result' => $refund_result];
            $db_goods_order::update($update_data, $map);
        }
        return $refund_result;
    }

    public function member_pay($bid, $order_guid)
    {
        $db_goods_order = new GoodsOrderModel();
        $map            = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid]
        ];
        $order          = $db_goods_order->where($map)->find()->toArray();
        if ($order['status'] == 0) {
            return true;
        }
        $paid_money            = $order['paid_money'];
        $paid_point            = $order['paid_point'];
        $point_use_num         = $order['point_use_num'];   // 积分抵扣数量
        $paid_coupon           = $order['paid_coupon'];
        $coupon_send_note_guid = $order['coupon_send_note_guid'];
        $paid_wechat           = $order['paid_wechat'];
        $memo_suffix           = $paid_wechat > 0 ? '混合支付' : '';
        //扣除成功后 修改订单状态
        $update_data = [
            'status'   => 0,
            'pay_time' => format_timestamp()
        ];
        if ($paid_money > 0) {
            //完成扣除储值
            $db_member_money_note = new MemberMoneyNote();
            $data                 = [
                'bid'           => $bid,
                'member_guid'   => $order['member_guid'],
                'money'         => $order['paid_money'],
                'way'           => 5, //商城购物
                'type'          => -1, //扣费
                'relation_guid' => $order_guid,
                'memo'          => '[商城购物' . $memo_suffix . ']', //
            ];
            $used_result          = $db_member_money_note->recharge_money($data);
        }
        if ($paid_point > 0 || $point_use_num > 0) {
            $db_member_point_note = new MemberPointNote();
            $data                 = [
                'bid'           => $bid,
                'member_guid'   => $order['member_guid'],
                'point'         => $point_use_num,
                'relation_guid' => $order_guid,
                'way'           => 2, //商城购物
                'type'          => -1, //扣费
                'memo'          => '[商城购物积分抵扣' . $memo_suffix . ']', //
            ];
            $used_result          = $db_member_point_note->recharge_point($data);
        }
        if (!empty($coupon_send_note_guid)) {
            //开始核销优惠券
            $db_coupon_send_note = new CouponSendNote();
            $data                = [
                'bid'                   => $bid,
                'coupon_send_note_guid' => $order['coupon_send_note_guid'],
                'member_guid'           => $order['member_guid'],
                'relation_guid'         => $order_guid,
                'used_num'              => $order['coupon_use_num'] ?? 1,
                'used_value'            => $order['paid_coupon'],
                'way'                   => $order['way'],
            ];
            $used_result         = $db_coupon_send_note->use_coupon($data);
            if ($used_result === false) {
                throw new Exception($db_coupon_send_note->getError());
            }
            $update_data['coupon_used_note_guid'] = $used_result['guid'];
        }
        $db_goods_order::update($update_data, $map);
        return array_merge($order, $update_data);
    }

    public function update_order_delete_time($params)
    {
        $guid_array      = explode(',', $params['guid']);
        $map_goods_order = [
            ['bid', '=', $this->get_bid_from_request()],
            ['guid', 'IN', $guid_array],
        ];
        $db_goods_order  = new GoodsOrderModel();
        $db_goods_order::update(['delete_time' => format_timestamp()], $map_goods_order);
        success('删除成功' . count($guid_array) . '个订单');
    }

    /**
     * @param array $data
     * @param int $type //删除类型 1 标记删除 2 退款状态
     * @return mixed
     * @throws Exception|Throwable
     */
    public function del($data, $type = 1)
    {
        $bid             = $data['bid'] ?? $this->get_bid_from_request();
        $order_guid      = $data['order_guid'] ?? '';
        $bill_number     = $data['bill_number'] ?? '';
        $map_goods_order = [['bid', '=', $bid]];
        if ($order_guid) {
            $map_goods_order[] = ['guid', '=', $order_guid];
        } elseif ($bill_number) {
            $map_goods_order[] = ['bill_number', '=', $bill_number];
        } else {
            throw new Exception('订单号或订单guid不能为空');
        }
        $db_goods_order = new GoodsOrderModel();
        $order_info     = $db_goods_order->where($map_goods_order)->findOrFail();
        $order_guid     = $order_guid ?: $order_info['guid'];
        if ($order_info['delete_time']) {
            throw new Exception('订单已退单');
        }
        if (in_array($order_info['status'], [-3, -2, -1])) {
            throw new Exception('订单状态不允许退单');
        }
        if (strpos($order_info['bill_number'], '-') !== false) {
            throw new Exception('暂不支持退单拆单订单');
        }
        $coupon_guid           = $order_info['coupon_guid'];
        $coupon_send_note_guid = $order_info['coupon_send_note_guid'];
        if (!$coupon_guid || !$coupon_send_note_guid) {
            throw new Exception('优惠券使用标识不存在');
        }
        $db_coupon   = new Coupon();
        $map_coupon  = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_guid]
        ];
        $coupon_info = $db_coupon->where($map_coupon)->findOrFail();

        $db_coupon_used_note   = new CouponUsedNote();
        $map_coupon_send_note  = [
            ['bid', '=', $bid],
            ['coupon_guid', '=', $coupon_guid],
            ['coupon_send_guid', '=', $coupon_send_note_guid],
            ['relation_guid', '=', $order_guid]
        ];
        $coupon_used_note_info = $db_coupon_used_note->where($map_coupon_send_note)->findOrFail();
        $used_num              = $coupon_used_note_info['used_num'];
        $used_value            = $coupon_used_note_info['used_value'];
        //根据卡券类型决定是否允许删除
        $coupon_type = $coupon_info['type'];
        if ($coupon_type != 1 && $coupon_type != 2) {
            throw new Exception('仅支持退单数量卡和金额卡类型的订单');
        }
        //先删除订单,避免重复退回
        $update_data = $type == 1 ? ['delete_time' => format_timestamp()] : ['status' => -3, 'refund_time' => format_timestamp()];
        $db_goods_order::update($update_data, $map_goods_order);
        $db_coupon_send_note  = new CouponSendNote();
        $map_coupon_send_note = [
            ['bid', '=', $bid],
            ['guid', '=', $coupon_send_note_guid]
        ];
        switch ($coupon_type) {
            case 1:
                //数量卡 退回可用数量
                $db_coupon_send_note->where($map_coupon_send_note)->setDec('used_num', $used_num);
                break;
            case 2:
                //金额卡 退回可用数量
                $db_coupon_send_note->where($map_coupon_send_note)->setDec('used_value', $used_value);
                $db_coupon_send_note->where($map_coupon_send_note)->setDec('used_num', $used_num);
                break;
            default:
                break;
        }
        $coupon_send_note_info = $db_coupon_send_note->where($map_coupon_send_note)->findOrFail();
        if ($coupon_send_note_info['status'] == 1) {
            //如果卡券已经标记为已使用 则判断当前回退后 是否应该标记为未使用
            $need_update = ($coupon_type == 1 && $coupon_send_note_info['used_num'] == 0) || ($coupon_type == 2 && $coupon_send_note_info['used_value'] == 0);
            if ($need_update) {
                $db_coupon_send_note::update(['status' => 0, 'used_time' => null], $map_coupon_send_note);
            }
        }
        return true;
    }

    /**
     *支付下单
     * @access public
     * @return array|GoodsOrder
     * @throws Exception|Throwable
     */
    public function pay($params)
    {
        $bid              = $this->get_bid_from_request();
        $order_guid       = $params['order_guid'];
        $order_guid_array = !empty($params['order_guid_array']) ? $params['order_guid_array'] : [$order_guid];
        //       $pay_type              = $params['pay_type'];
        $scene                 = $params['scene'] ?? 'miniapp';
        $map                   = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $db_goods_order        = new GoodsOrderModel();
        $order_info            = $db_goods_order->where($map)->find();
        $total_money           = $order_info['total_money'];
        $paid_money            = $order_info['paid_money'];
        $paid_point            = $order_info['paid_point'];   // 积分抵扣金额
        $point_use_num         = $order_info['point_use_num']; // 积分抵扣数量
        $paid_wechat           = $order_info['paid_wechat'];
        $paid_coupon           = $order_info['paid_coupon'];
        $coupon_guid           = $order_info['coupon_guid'];
        $coupon_send_note_guid = $order_info['coupon_send_note_guid'];
        $out_trade_no          = $order_info['third_order_number'];
        $member_guid           = $order_info['member_guid'];
        $pay_type              = $order_info['pay_type'];
        $way                   = $order_info['way'];

        switch ($pay_type) {
            case 1:
                //存在微信支付,
                //先检查是否重复单据号
                $db_pay_order  = new PayOrder();
                $map_pay_order = [
                    ['bid', '=', $bid],
                    ['bill_number', '=', $out_trade_no],
                ];

                $pay_order_info = $db_pay_order->where($map_pay_order)->field(['status'])->findOrEmpty();

                if (!$pay_order_info->isEmpty()) {
                    $status = $pay_order_info['status'];
                    switch ($status) {
                        case 1:
                            error('该订单已经支付成功,请勿重复支付');
                            break;
                        case 0:
                            //待支付的则 重新生成单据号, 并且更新 商品订单表 $db_goods_order
                            $out_trade_no = tools()::get_bill_number();
                            $update_data  = ['third_order_number' => $out_trade_no];
                            $db_goods_order::update($update_data, $map);
                            break;
                        default:
                            error('支付订单状态' . $status . '不允许重复支付');
                    }
                }
                $int_total_fee = tools()::nc_price_yuan2fen($paid_wechat);
                $options       = [
                    'bid'          => $bid,
                    'body'         => '[商城购物]',
                    'out_trade_no' => $out_trade_no,
                    'total_fee'    => $int_total_fee,
                    'openid'       => $this->get_openid_from_request(),
                    'appid'        => $this->get_appid_from_request(),
                    'job_attach'   => [
                        'class' => 'GoodsOrder@after_pay_success',
                        'data'  => [
                            'bid'         => $bid,
                            'order_guid'  => $order_guid,
                            'bill_number' => $out_trade_no,
                        ]
                    ]
                ];
                $options       = pay($bid)->driver('wechat')->scene($scene)->apply($options);
                //$options                         = json_encode($options, JSON_UNESCAPED_UNICODE);
                $result['pay_options']           = $options;
                $result['third_pay_bill_number'] = $out_trade_no;
                $result['status']                = $order_info['status'];
                $result['order_guid']            = $order_info['guid'];
                result($result, '等待支付');
                //               $map                             = [
                //                   ['bid', '=', $bid],
                //                   ['guid', '=', $order_guid]
                //               ];
                //               $update_data                     = ['pay_type' => 1];
                //               $db_goods_order::update($update_data, $map);

                break;
            case  2:
                //纯会员支付
                $db_goods_order = new GoodsOrderModel();
                $order          = $db_goods_order->member_pay($bid, $order_guid);
                if (!$order) {
                    throw new Exception('支付失败');
                }
                //更新订单状态为支付成功状态
                if (count($order_guid_array) > 1) {
                    $map = [
                        ['bid', '=', $bid],
                        ['guid|parent_order_guid', 'IN', $order_guid_array]
                    ];
                } else {
                    $map = [
                        ['bid', '=', $bid],
                        ['guid|parent_order_guid', '=', $order_guid]
                    ];
                }
                $update_data = [
                    'status'   => 0, // 支付成功 待发货
                    'pay_type' => 2,
                    'pay_time' => format_timestamp()
                ];
                $db_goods_order::update($update_data, $map);
                break;
            default:
        }
        //异步通知
        foreach ($order_guid_array as $order_guid) {
            $map                      = [
                ['bid', '=', $bid],
                ['guid', '=', $order_guid],
            ];
            $order_info               = $db_goods_order->where($map)->find();
            $order_info['order_guid'] = $order_guid;
            //            job()->set_job_name('GoodsOrder@after_submit_order')->push_job(['order_data' => $order_info]);
            $order_info['order_guid_array'] = $order_guid_array;
            job()->set_job_name('GoodsOrder@after_submit_order')->push_job(['order_data' => [
                'bid'  => $bid,
                'guid' => $order_guid,
            ]]);
        }
        return $order_info;
    }
}
