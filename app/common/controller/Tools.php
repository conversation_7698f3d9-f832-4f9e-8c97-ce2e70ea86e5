<?php

namespace app\common\controller;

use OpenApi\SubMerchant;
use Symfony\Component\DomCrawler\Crawler;

class Tools
{
    public function filter_email()
    {
        $html     = cache('20230105');
        $html     = tools()::array_iconv($html, 'GB18030');
        $crawler  = new Crawler($html);
        $result   = $crawler->filterXPath('//table[@border="1"]/*')->each(function (Crawler $node, $i) {
            return $node->filterXPath('//tr/*')->each(function (Crawler $node, $i) {
                return $node->text();
            });
        });
        $result   = array_filter($result);
        $header   = [
            '日期'     => 'date',
            '星期'     => 'week',
            '交易笔数' => 'num',
            '交易金额' => 'money',
        ];
        $all_data = tools()::table_array_to_data($result[0], $header);
        halt($all_data);
    }

    public function download_bill()
    {
        $merchant_code = '1263122701';
        $config        = ['merchant_code' => $merchant_code];
        $sub           = new SubMerchant($config);
        $start         = '2018-04-01';
        for ($i = 0; $i < 70; $i++) {
            $ii         = $i + 1;
            $start_date = strtotime("$start +$i month");
            $end_date   = strtotime("$start +$ii month -1 days");
            $start_date = date('Y-m-d', $start_date);
            $end_date   = date('Y-m-d', $end_date);
            $name       = '2023批量下载' . $merchant_code . '基础账户汇总' . $start_date . '_' . $end_date;
            if (cache($name)) {
                dump('下载好了');
                continue;
            }
            $result = $sub->down_summary_bill($start_date, $end_date, 1);
            //1263122701运营账户汇总2022-12-30_2023-01-05
            // 打开文件资源，不存在则创建
            $fp = fopen($name . '.csv', 'a');
            // 写入并关闭资源
            fwrite($fp, $result);
            fclose($fp);
            cache($name, 'ok', 3600);
        }
    }
}