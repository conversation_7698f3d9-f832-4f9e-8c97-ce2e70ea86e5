# Curl.php 使用说明

## 一、简介

`Curl` 是本项目对 HTTP 请求的高级封装，基于 GuzzleHttp 实现，支持 GET、POST、PUT、DELETE、OPTIONS、HEAD 等多种请求方式，支持链式调用、请求参数灵活设置、自动重试、日志记录、并发请求、文件上传/下载、cookie/session 管理等功能。

---

## 二、常用方法与链式调用

### 1. 基本请求

- **GET 请求**

  ```php
  $result = curl()->get('https://api.example.com/user', ['id' => 123])->get_body();
  ```

- **POST 请求（JSON）**

  ```php
  $data = ['name' => '张三', 'age' => 18];
  $result = curl()->json()->post('https://api.example.com/user', $data)->get_body();
  ```

- **POST 请求（表单）**

  ```php
  $data = ['username' => 'test', 'password' => '123456'];
  $result = curl()->form_params()->post('https://api.example.com/login', $data)->get_body();
  ```

- **PUT/DELETE/OPTIONS/HEAD**
  ```php
  $result = curl()->put('https://api.example.com/user/1', ['name' => '李四'])->get_body();
  $result = curl()->delete('https://api.example.com/user/1')->get_body();
  ```

### 2. 设置请求头、Cookie

- **自定义 Header**

  ```php
  $header = ['Authorization' => 'Bearer token_xxx'];
  $result = curl()->set_header($header)->get('https://api.example.com/user')->get_body();
  ```

- **设置 Cookie**
  ```php
  $cookie = 'PHPSESSID=xxxxxx; path=/';
  $result = curl()->set_cookies($cookie)->get('https://api.example.com/profile')->get_body();
  ```

### 3. 文件上传（multipart）

```php
$file = new \CURLFile('/path/to/file.jpg');
$data = ['file' => $file, 'desc' => '图片描述'];
$result = curl()->multipart()->post('https://api.example.com/upload', $data)->get_body();
```

### 4. 并发请求

```php
$url_data_array = [
    ['url' => 'https://api.example.com/user/1', 'data' => []],
    ['url' => 'https://api.example.com/user/2', 'data' => []],
];
$responses = curl()->multi_get($url_data_array);
```

### 5. 下载文件/图片

```php
curl()->get('https://api.example.com/file/123')->download('/tmp', 'file123.pdf');
curl()->get('https://api.example.com/image/1')->download_image('pic.jpg');
```

---

## 三、常用链式方法

- `json()`：设置请求体为 JSON 格式
- `form_params()`：设置请求体为表单格式
- `multipart()`：设置为 multipart/form-data（用于文件上传）
- `set_header(array $header)`：设置请求头
- `set_cookies($cookies)`：设置 Cookie
- `set_timeout($seconds)`：设置超时时间
- `set_proxy($proxy)`：设置代理
- `set_cert($file)`/`set_ssl_key($file)`：设置 SSL 证书
- `setMaxRetries($n)`：设置最大重试次数
- `setRetryDecider($closure)`：自定义重试逻辑
- `cache($exp, $key)`：设置缓存

---

## 四、获取响应内容

- `get_body($json_decode = true)`：获取响应内容（默认自动 JSON 解析）
- `get_status_code()`：获取 HTTP 状态码
- `get_header($name = null)`：获取响应头
- `get_cookies()`：获取响应 Set-Cookie
- `get_base64()`：获取图片 base64 编码

---

## 五、项目内常见用法举例

- **业务控制器调用**

  ```php
  // POST JSON
  $result = curl()->json()->post($url, $data)->get_body();
  // GET 并获取 cookies
  $cookies = curl()->get($url)->get_cookies();
  // 表单 POST
  $result = curl()->form_params()->post($url, $params)->get_body();
  ```

- **队列任务中调用**

  ```php
  $result = curl()->set_header($header)->$method($url, $data)->get_body();
  ```

- **并发请求**
  ```php
  $responses = curl()->multi_post($url_data_array);
  ```

---

## 六、注意事项

- 默认自动记录请求日志，便于排查问题。
- 支持自动重试、超时、代理、SSL、缓存等高级功能。
- 支持链式调用，推荐每次请求都用 `->get_body()` 获取最终结果。
- 文件上传需用 `CURLFile` 对象，且需 `multipart()`。
- 并发请求返回数组，需自行处理每个响应。

---

## 七、参考代码片段

```php
// 1. GET 请求
$result = curl()->get('https://api.example.com/info', ['id' => 1])->get_body();

// 2. POST JSON
$result = curl()->json()->post('https://api.example.com/create', ['name' => 'test'])->get_body();

// 3. 设置 header 和 cookie
$result = curl()->set_header(['Authorization' => 'Bearer xxx'])->set_cookies('PHPSESSID=xxx')->get('https://api.example.com')->get_body();

// 4. 文件上传
$file = new \CURLFile('/tmp/a.jpg');
$result = curl()->multipart()->post('https://api.example.com/upload', ['file' => $file])->get_body();

// 5. 并发请求
$responses = curl()->multi_get([
    ['url' => 'https://api.example.com/a', 'data' => []],
    ['url' => 'https://api.example.com/b', 'data' => []],
]);
```

---

## 八、更多高级用法

详见 `app/common/service/Curl.php` 注释与方法定义，或参考项目内如 `app/controller/index/Plugins.php`、`app/queue/controller/Curl.php` 等实际业务调用。

如需特殊用法（如自定义重试、日志、缓存、下载、图片处理等），可查阅源码或联系维护者。
