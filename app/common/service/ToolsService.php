<?php

namespace app\common\service;

use app\common\exceptions\NotNotifyException;
use app\common\tools\Visitor;
use CURLFile;
use DateTime;
use Exception;
use Ip2Region;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Storage\Storage;
use think\facade\App;
use Throwable;
use ZIPARCHIVE;

class ToolsService
{
    protected static $instance = null;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     */
    public static function get_instance()
    {
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 字符串命名风格转换
     * type 0  驼峰转下划线  1 下划线转驼峰
     * @param string $name 字符串
     * @param integer $type 转换类型
     * @param bool $ucfirst 首字母是否大写（驼峰规则）
     * @return string
     */
    public static function parse_name($name, $type = 0, $ucfirst = true)
    {
        if ($type) {
            $name = preg_replace_callback('/_([a-zA-Z])/', function ($match) {
                return strtoupper($match[1]);
            }, $name);

            return $ucfirst ? ucfirst($name) : lcfirst($name);
        } else {
            return strtolower(trim(preg_replace("/[A-Z]/", "_\\0", $name), "_"));
        }
    }

    /**
     * @param $string
     * @return string|boolean
     */
    public static function check_password($string)
    {
        //return preg_match('/^(?![^a-zA-Z]+$)(?!\D+$).{6,}$/', $string);
        $msg = '密码必须6位以上包含字母和数字';
        if (!preg_match("/[A-Za-z]/", $string) || !preg_match("/\d/", $string)) {
            return $msg;
        }
        if (strlen($string) < 6) {
            return $msg;
        }
        //        $simple_password       = get_system_config('simple_password');
        //        $simple_password_array = explode(',', $simple_password);
        //        if (in_array($string, $simple_password_array)) {
        //            return '您的密码过于简单,请重新输入!';
        //        }
        return true;
    }

    /**
     * 产生随机字符串
     * @param int $length 指定字符长度
     * @param string $str 字符串前缀
     * @return string
     */
    public static function create_noncestr($length = 32, $str = "")
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    public static function get_random_string($len, $chars = null)
    {
        if (is_null($chars)) {
            $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        }
        mt_srand(10000000 * ( double )microtime());
        for ($i = 0, $str = '', $lc = strlen($chars) - 1; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $lc)];
        }
        return $str;
    }

    /**
     * 创建CURL文件对象
     * @param $filename
     * @param string $mimetype
     * @param string $postname
     * @return CURLFile|string
     * @throws Exception
     */
    public static function create_curl_file($filename, $mimetype = null, $postname = null)
    {
        is_null($postname) && $postname = basename($filename);
        is_null($mimetype) && $mimetype = self::get_ext_mine(pathinfo($filename, 4));
        if (function_exists('curl_file_create')) {
            return curl_file_create($filename, $mimetype, $postname);
        }
        return "@{$filename};filename={$postname};type={$mimetype}";
    }

    /**
     * 根据文件后缀获取文件MINE
     * @param array $ext 文件后缀
     * @param array $mine 文件后缀MINE信息
     * @return string
     * @throws Exception
     */
    public static function get_ext_mine($ext, $mine = [])
    {
        $mines = self::get_mines();
        foreach (is_string($ext) ? explode(',', $ext) : $ext as $e) {
            $mine[] = $mines[strtolower($e)] ?? 'application/octet-stream';
        }
        return join(',', array_unique($mine));
    }

    /**
     * 获取所有文件扩展的mine
     * @return array
     * @throws Exception
     */
    private static function get_mines()
    {
        $mines = cache('all_ext_mine');
        if (empty($mines)) {
            $content = file_get_contents('http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types');
            preg_match_all('#^([^\s]{2,}?)\s+(.+?)$#ism', $content, $matches, PREG_SET_ORDER);
            foreach ($matches as $match) {
                foreach (explode(" ", $match[2]) as $ext) {
                    $mines[$ext] = $match[1];
                }
            }
            cache('all_ext_mine', $mines);
        }
        return $mines;
    }

    /**
     * 获取域名SSL到期时间
     * @param string $domain 域名
     * @return array|false
     * @throws Exception
     */
    public static function get_certificate_info($domain)
    {
        $handle = curl_init();
        curl_setopt($handle, CURLOPT_URL, "https://" . $domain);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($handle, CURLOPT_CERTINFO, true);
        $response = curl_exec($handle);
        if ($response === false) {
            return false;
        }
        $certInfo = curl_getinfo($handle, CURLINFO_CERTINFO);
        curl_close($handle);
        if (is_array($certInfo) && count($certInfo) > 0) {
            $certData = $certInfo[0];
            if (isset($certData['Cert'])) {
                $certDetails = openssl_x509_parse($certData['Cert']);
                return [
                    'validTo' => date("Y-m-d H:i:s", $certDetails['validTo_time_t']),
                    // 可以添加更多的证书信息字段
                ];
            }
        }
        return false;
    }

    /**
     * 获取本机局域网IP 方便分布式部署代码
     * @return string 局域网IP
     */
    public static function get_lan_ip()
    {
        if (Visitor::is_swoole()) {
            // 在 Swoole 环境中，使用协程
            return go(function () {
                return gethostbyname(gethostname());
            });
        } else {
            // 在非 Swoole 环境中，直接调用
            return gethostbyname(gethostname());
        }
    }


    /**
     * @throws NotNotifyException
     */
    public static function update_app()
    {
        $lan_ip = tools()::get_lan_ip();
        //master分支才git pull 并重启,否则其他分支也是在master的目录下git pull
        $cmd = 'sudo -u root -S git pull';
        exec($cmd, $out, $code);
        if ($code !== 0) {
            $msg = ' git pull 失败,状态码:' . $code . ';输出:' . json_encode($out, JSON_UNESCAPED_UNICODE);
            wr_log($msg, 1);
            throw new NotNotifyException($msg);
        }
        //更新php缓存 防止代码部分最新 部分旧代码
        $cmd = 'sudo -u root -S  /usr/local/php/bin/php /mnt/sh/cachetool-8.6.1.phar opcache:reset --fcgi=/dev/shm/php-cgi.sock';
        exec($cmd, $out, $code);
        if ($code !== 0) {
            $msg = 'opcache reset 失败,状态码:' . $code . ';输出:' . json_encode($out, JSON_UNESCAPED_UNICODE);
            wr_log($msg, 1);
            throw new NotNotifyException($msg);
        }
        //更新当前应用最后更新时间
        $app_name = $lan_ip . ':' . config('app.app_name');
        cache($app_name . ':last_update_time', time());
        logToFile('服务器IP:' . $lan_ip . ' 应用更新成功!');
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        return true;
    }

    /**
     * 获取客户端IP地址
     * @param string $ip IP地址
     * @return string
     */
    public static function get_client_ip_region_info($ip = null)
    {
        $region   = '';
        $ip       = $ip ?? self::get_client_ip();
        $local_ip = ['0.0.0.0', '127.0.0.1'];
        if (!in_array($ip, $local_ip)) {
            try {
                $ip2region   = new Ip2Region();
                $region_info = $ip2region->btreeSearch($ip);
                $region      = $region_info['region'];
            } catch (Exception | Throwable $e) {
                $region = '获取出错';
            }
        } else {
            $region = '本机/局域网';
        }
        return str_replace('中国|0|', '', $region);
    }

    /**
     * 获取客户端IP地址
     *
     * @param integer $type 返回类型 0 返回IP地址 1 返回IPV4地址数字
     * @return string
     */
    public static function get_client_ip($type = 0)
    {
        $type = $type ? 1 : 0;
        static $ip = NULL;
        if ($ip !== NULL)
            return $ip[$type];
        if (Visitor::get_server_header('HTTP_X_FORWARDED_FOR')) {
            $arr = explode(',', Visitor::get_server_header('HTTP_X_FORWARDED_FOR'));
            $pos = array_search('unknown', $arr);
            if (false !== $pos)
                unset ($arr[$pos]);
            $ip = trim($arr[0]);
        } elseif (Visitor::get_server_header('HTTP_CLIENT_IP')) {
            $ip = Visitor::get_server_header('HTTP_CLIENT_IP');
        } elseif (Visitor::get_server_header('REMOTE_ADDR')) {
            $ip = Visitor::get_server_header('REMOTE_ADDR');
        }
        if (empty($ip) && request()->isCli()) {
            // 如果IP是空则获取局域网ip
            $ip = self::get_lan_ip();
        }
        // IP地址合法验证
        $long = sprintf("%u", ip2long($ip));
        $ip   = $long ? [$ip, $long] : ['0.0.0.0', 0];
        return $ip[$type];
    }

    /**
     * 判断时间是否到了可用时间
     * @param string $datetime
     * @return bool
     **@throws Exception
     */
    public static function is_availability($datetime)
    {
        //时间大于2038年 不能直接用strtotime //http://www.jb51.net/article/117320.htm
        $datetime     = new DateTime($datetime);
        $expired_time = $datetime->format('U');
        return (time() <= $expired_time);
    }

    /**
     * 判断时间是否过期
     * @param string $datetime
     * @return bool
     **@throws Exception
     */
    public static function is_expired($datetime)
    {
        //时间大于2038年 不能直接用strtotime //http://www.jb51.net/article/117320.htm
        $datetime     = new DateTime($datetime);
        $expired_time = $datetime->format('U');
        return (time() > $expired_time);
    }

    /**
     * 数组转XML内容
     * @param array $data
     * @return string
     */
    public static function arr2xml($data)
    {
        return "<xml>" . self::_arr2xml($data) . "</xml>";
    }

    /**
     * XML内容生成
     * @param array $data 数据
     * @param string $content
     * @return string
     */
    private static function _arr2xml($data, $content = '')
    {
        foreach ($data as $key => $val) {
            is_numeric($key) && $key = 'item';
            $content .= "<{$key}>";
            if (is_array($val) || is_object($val)) {
                $content .= self::_arr2xml($val);
            } elseif (is_string($val)) {
                $content .= '<![CDATA[' . preg_replace("/[\\x00-\\x08\\x0b-\\x0c\\x0e-\\x1f]/", '', $val) . ']]>';
            } else {
                $content .= $val;
            }
            $content .= "</{$key}>";
        }
        return $content;
    }

    /**
     * 解析XML内容到数组
     * @param string $xml
     * @return array
     */
    public static function xml2arr($xml)
    {
        try {
            if (!self::xml_parser($xml)) {
                return [];
            }
            if (PHP_VERSION_ID < 80000) {
                libxml_disable_entity_loader(true);
            }
            return json_decode(self::arr2json(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 解析XML格式的字符串
     * @param string|array $str
     * @return bool|array 解析正确就返回解析结果,否则返回false,说明字符串不是XML格式
     */
    public static function xml_parser($str)
    {
        try {
            if (!is_string($str)) {
                return false;
            }
            $xml_parser = xml_parser_create();
            if (!xml_parse($xml_parser, $str, true)) {
                xml_parser_free($xml_parser);
                return false;
            } else {
                return (json_decode(json_encode(simplexml_load_string($str)), true));
            }
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 数组转xml内容
     * @param array $data
     * @return null|string|string
     */
    public static function arr2json($data)
    {
        return preg_replace_callback('/\\\\u([0-9a-f]{4})/i', function ($matches) {
            return mb_convert_encoding(pack("H*", $matches[1]), "UTF-8", "UCS-2BE");
        }, ($jsonData = json_encode($data)) == '[]' ? '{}' : $jsonData);
    }

    /**
     * 替换字符串中间位置字符为星号
     * @param string $str 待加密字符
     * @return string [加密后字符]
     */
    public static function replace_to_star(string $str)
    {
        $len = strlen($str);
        // 1 - 2 位数原样返回
        if ($len <= 2) {
            return $str;
        }
        // 11 位时固定替换成前三后四显示
        if ($len === 11) {
            $prefix    = substr($str, 0, 3);
            $suffix    = substr($str, -4);
            $starCount = 4;
            return $prefix . str_repeat('*', $starCount) . $suffix;
        }
        // 其他长度按原逻辑处理
        $prefixLength = ceil($len / 4);
        $suffixLength = floor($len / 4);
        $prefix       = substr($str, 0, $prefixLength);
        $suffix       = substr($str, -$suffixLength);
        $starCount    = $len - $prefixLength - $suffixLength;
        return $prefix . str_repeat('*', $starCount) . $suffix;
    }

    /**
     * 多维数组交集
     * @param $array1
     * @param $array2
     * @return array
     */
    public static function many_array_intersect(array $array1, array $array2)
    {
        $out_arr = [];
        foreach ($array1 as $key => $val) {
            if (in_array($val, $array2)) {
                $out_arr[] = $val;
            }
        }
        return $out_arr;
    }

    /**
     * 合并数据
     * @param $ary
     * @return mixed
     */
    public static function merge_arr($ary)
    {
        // 先删除key
        $ary = array_values($ary);
        for ($x = 0; $x < count($ary) - 1; $x++) {
            foreach ($ary[$x] as $k => $v) {
                if (key_exists($k, $ary[$x + 1])) {
                    $ary[$x + 1][$k] += $v;
                } else {
                    $ary[$x + 1][$k] = $v;
                }
            }
        }
        return end($ary);
    }

    /**
     * 多维数组排序
     * @param $multi_array
     * @param $sort_key
     * @param int $sort
     * @return array|bool
     */
    public static function multi_array_sort($multi_array, $sort_key, $sort = SORT_ASC)
    {
        if (is_array($multi_array)) {
            foreach ($multi_array as $row_array) {
                if (is_array($row_array)) {
                    $key_array[] = $row_array[$sort_key];
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
        array_multisort($key_array, $sort, $multi_array);
        return $multi_array;
    }

    /**
     * 是否多维数组
     * @access protected
     * @param array $data 数据
     * @return bool
     * @throws Exception
     */
    public static function is_multi_dimensional_array(array $data)
    {
        if (empty($data)) {
            return false;
        }
        foreach ($data as $key => $val) {
            if (!is_numeric($key) || !is_array($val)) {
                return false; //如果key是字符串或者val不是数组则说明是一维数组
            }
        }
        return true;
    }

    /**
     * 切割字符串
     * @param string $string 字符
     * @param integer $len 长度
     * @return array
     */
    public static function mb_str_split($string, $len = 1)
    {
        $start   = 0;
        $str_len = strlen($string);
        $array   = [];
        while ($str_len) {
            $array[] = mb_strcut($string, $start, $len, "utf8");
            $string  = mb_strcut($string, $len, $str_len, "utf8");
            $str_len = strlen($string);
        }
        return $array;
    }

    /**
     * unicode_to_utf8
     * @param string $name 字符
     * @return string
     */
    public static function unicode_to_utf8($name)
    {
        $json = '{"str":"' . $name . '"}';
        $arr  = json_decode($json, true);
        return empty($arr) ? $name : $arr['str'];
    }

    /**
     * @param string $zipName 需要解压的文件路径加文件名
     * @param string $dir 解压后的文件夹路径
     * @param array $replace 需要替换的名称
     * @return bool|array
     */
    public static function extract_zip_to_file($zipName, $dir, $replace = [])
    {
        $zip = new ZipArchive;
        if ($zip->open($zipName) === true) {
            $unzip_files = [];
            if (!is_dir($dir)) mkdir($dir, 0775, true);
            $file_num = $zip->numFiles;
            for ($i = 0; $i < $file_num; $i++) {
                $statInfo  = $zip->statIndex($i, ZipArchive::FL_ENC_RAW);
                $file_name = self::transcoding($statInfo['name']);
                if ($statInfo['crc'] == 0) {
                    //新建目录
                    if (!is_dir($dir . '/' . substr($file_name, 0, -1))) mkdir($dir . '/' . substr($file_name, 0, -1), 0775, true);
                } else {
                    //拷贝文件
                    $file_name = tools()::array_iconv($file_name);
                    foreach ($replace as $key => $val) {
                        $file_name = str_replace($key, $val, $file_name);
                    }
                    copy('zip://' . $zipName . '#' . $zip->getNameIndex($i), $dir . '/' . $file_name);
                    $unzip_files[] = $dir . '/' . $file_name;
                }
            }
            $zip->close();
            return $unzip_files;
        }
        return false;
    }

    /**
     * 转换文件名
     * @param string $file_name
     * @return string
     */
    public static function transcoding($file_name)
    {
        $encoding    = mb_detect_encoding($file_name, ['UTF-8', 'GBK', 'BIG5', 'CP936']);
        $out_charset = (DIRECTORY_SEPARATOR == '/') ? 'UTF-8' : 'GBK'; // linux 下 UTF-8 win 下 GBK
        return iconv($encoding, $out_charset, $file_name);
    }

    /**
     * 字符编码转换
     * @param array|string $data 字符
     * @param string $output 输出字符编码
     * @return array|string
     */
    public static function array_iconv($data, $output = 'utf-8')
    {
        try {
            $encode_arr = ['UTF-8', 'ASCII', 'GBK', 'GB2312', 'BIG5', 'JIS', 'eucjp-win', 'sjis-win', 'EUC-JP'];
            $encoded    = mb_detect_encoding($data, $encode_arr);
            if (!is_array($data)) {
                if (self::get_file_type($data) != -1) {
                    //如果字符串是二进制流文件则不转换,以免损坏数据
                    return $data;
                }
                return mb_convert_encoding($data, $output, $encoded);
            } else {
                foreach ($data as $key => $val) {
                    $key = tools()::array_iconv($key, $output);
                    if (is_array($val)) {
                        $data[$key] = tools()::array_iconv($val, $output);
                    } else {
                        $data[$key] = mb_convert_encoding($data, $output, $encoded);
                    }
                }
                return $data;
            }
        } catch (Exception | Throwable $e) {
            return $data;
        }
    }

    /**
     * 根据二进制流获取文件类型
     * @param string $file 字符
     * @return integer
     */
    public static function get_file_type($file)
    {
        // 文件头标识 (2 bytes)
        $bin      = substr($file, 0, 2);
        $strInfo  = @unpack("C2chars", $bin);
        $typeCode = intval($strInfo['chars1'] . $strInfo['chars2']);
        $fileType = "";
        switch ($typeCode) {
            case 255216 :
                $fileType = "image/jpeg";
                $ret      = 0;
                break;
            case 7173 :
                $fileType = "image/gif";
                $ret      = 0;
                break;
            case 13780 :
                $fileType = "image/png";
                $ret      = 0;
                break;
            case 6677:
                $fileType = 'image/bmp';
                $ret      = 0;
                break;
            case 7790:
                $fileType = 'exe';
                $ret      = 0;
                break;
            case 7784:
                $fileType = 'midi';
                $ret      = 0;
                break;
            case 8297:
                $fileType = 'rar';
                $ret      = 0;
                break;
            default :
                $fileType = "unknow";
                $ret      = -1;
                break;
        }
        return $ret;
    }

    /**
     * 读取csv文件
     * @param string $file_name
     * @return array|bool
     * @throws Exception
     */
    public static function read_csv($file_name)
    {
        if (!file_exists($file_name)) {
            throw new Exception('文件不存在:' . $file_name);
        }
        $file = fopen($file_name, "r");
        while (!feof($file)) {
            $data[] = fgetcsv($file);
        }
        fclose($file);
        $data = eval('return ' . iconv('gbk', 'utf-8', var_export($data, true)) . ';');
        foreach ($data as $key => $value) {
            if (!$value) {
                unset($data[$key]);
            }
        }
        return $data;
    }

    /**
     * 字符串处理函数
     * @param string $str 所要查找的字符串
     * @return string
     */
    public static function replace_www_to_readonly($str)
    {
        return str_replace(['www.', 'admin.'], 'readonly.', $str);
    }

    /**
     * 字符串处理函数
     * @param string $str 所要查找的字符串
     * @return string
     */
    public static function replace_readonly_to_www($str)
    {
        return str_replace(['readonly.', 'admin.'], 'www.', $str);
    }

    /**
     * 字符串处理函数
     * @param string $start 开始寻找的字符串
     * @param string $end 结束寻找的字符串
     * @param string $str 所要查找的字符串
     * @return string
     */
    public static function search_str($start, $end, $str)
    {
        $strLen = strlen($str);
        if (empty ($str))
            return false;
        if (empty ($start) && empty ($end)) {
            return false;
        }
        if (empty ($start)) {
            $endPosition = strpos($str, $end);
            $endLen      = strlen($end);
            return trim(substr($str, 0, $endPosition));
        }
        if (empty ($end)) {
            $startPosition = strpos($str, $start);
            $startLen      = strlen($start);
            return trim(substr($str, $startPosition + $startLen));
        }
        $strarr = explode($start, $str);
        if (!isset($strarr[1])) {
            return false;
        } else {
            $str    = $strarr[1];
            $strarr = explode($end, $str);
            return trim($strarr[0]);
        }
    }

    /**
     * 获取guid
     * @return string
     */
    public static function create_guid()
    {
        mt_srand(( double )microtime() * 10000); // optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid   = substr($charid, 0, 8) . $hyphen . substr($charid, 8, 4) . $hyphen . substr($charid, 12, 4) . $hyphen . substr($charid, 16, 4) . $hyphen . substr($charid, 20, 12);
        return strtolower($uuid);
    }

    /**
     * object转array
     * @param array|object $array 对象或者数组
     * @return array
     */
    public static function object2array($array)
    {
        return json_decode(json_encode($array), true);
    }

    /**
     * object转array
     * @param array $array 对象或者数组
     * @return array
     */
    public static function object_array($array)
    {
        if (is_object($array)) {
            $array = (array)$array;
        }
        if (is_array($array)) {
            foreach ($array as $key => $value) {
                $array[$key] = self::object_array($value);
            }
        }
        return $array;
    }

    public static function create_password($pw_length = 6)
    {
        $rand_pwd = '';
        for ($i = 0; $i < $pw_length; $i++) {
            $rand_pwd .= chr(mt_rand(33, 126));
        }
        return $rand_pwd;
    }

    /**
     * 判断字符串是否URL
     * @param string $str 字符
     * @return bool
     */
    public static function is_url($str)
    {
        return preg_match("/^http(s)?:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/", $str) ? true : false;
    }

    public static function get_age_by_id_card_number($id_card_number)
    {
        // 验证身份证号码长度
        if (strlen($id_card_number) == 15) {
            // 对于15位身份证号码，将前6位作为年份的后两位，加上"19"
            $year = '19' . substr($id_card_number, 6, 2);
            // 月份和日期
            $month = substr($id_card_number, 8, 2);
            $day   = substr($id_card_number, 10, 2);
        } elseif (strlen($id_card_number) == 18) {
            // 对于18位身份证号码，直接获取年份、月份和日期
            $year  = substr($id_card_number, 6, 4);
            $month = substr($id_card_number, 10, 2);
            $day   = substr($id_card_number, 12, 2);
        } else {
            // 非法身份证号码长度
            return '号码不合法';
        }

        // 计算年龄
        $today       = date('Y-m-d'); // 获取当前日期
        $today_year  = date('Y'); // 获取当前年份
        $today_month = date('m'); // 获取当前月份
        $today_day   = date('d'); // 获取当前日期

        $age = $today_year - $year; // 计算年份差
        // 如果当前月份小于出生月份，或者月份相等但当前日期小于出生日期，则年龄减1
        if ($today_month < $month || ($today_month == $month && $today_day < $day)) {
            $age--;
        }

        return $age;
    }

    /**
     * 判断是否为合法的身份证号码
     * @param $id_card_number
     * @return int
     */
    public static function get_sex_by_id_card_number($id_card_number)
    {
        // 验证身份证号码长度
        if (strlen($id_card_number) == 15) {
            // 对于15位身份证号码，获取第15位
            $genderDigit = substr($id_card_number, 14, 1);
        } elseif (strlen($id_card_number) == 18) {
            // 对于18位身份证号码，获取第17位
            $genderDigit = substr($id_card_number, 16, 1);
        } else {
            // 非法身份证号码长度
            return '号码不合法';
        }

        // 判断性别
        if ($genderDigit % 2 == 0) {
            return '女';
        } else {
            return '男';
        }
    }

    /**
     * 判断是否为合法的身份证号码
     * @param $id_card_number
     * @return int
     */
    public static function is_id_card_number($vStr)
    {
        $vCity = array(
            '11', '12', '13', '14', '15', '21', '22',
            '23', '31', '32', '33', '34', '35', '36',
            '37', '41', '42', '43', '44', '45', '46',
            '50', '51', '52', '53', '54', '61', '62',
            '63', '64', '65', '71', '81', '82', '91'
        );
        if (!preg_match('/^([\d]{17}[xX\d]|[\d]{15})$/', $vStr)) return false;
        if (!in_array(substr($vStr, 0, 2), $vCity)) return false;
        $vStr    = preg_replace('/[xX]$/i', 'a', $vStr);
        $vLength = strlen($vStr);
        if ($vLength == 18) {
            $vBirthday = substr($vStr, 6, 4) . '-' . substr($vStr, 10, 2) . '-' . substr($vStr, 12, 2);
        } else {
            $vBirthday = '19' . substr($vStr, 6, 2) . '-' . substr($vStr, 8, 2) . '-' . substr($vStr, 10, 2);
        }
        if (date('Y-m-d', strtotime($vBirthday)) != $vBirthday) return false;
        if ($vLength == 18) {
            $vSum = 0;
            for ($i = 17; $i >= 0; $i--) {
                $vSubStr = substr($vStr, 17 - $i, 1);
                $vSum    += (pow(2, $i) % 11) * (($vSubStr == 'a') ? 10 : intval($vSubStr, 11));
            }
            if ($vSum % 11 != 1) return false;
        }
        return true;
    }

    /**
     * @param string $IDCard 身份证
     * @param integer $format 通过身份证获取生日
     * @return mixed
     */
    public static function get_i_d_card_info($IDCard, $format = 1)
    {
        $result['error'] = 0;//0：未知错误，1：身份证格式错误，2：无错误
        $result['flag']  = '';//0标示成年，1标示未成年
        $result['tdate'] = '';//生日，格式如：2012-11-15
        if (!preg_match("/^(\d{15}$|^\d{18}$|^\d{17}(\d|X|x))$/", $IDCard)) {
            $result['error'] = 1;
            return $result;
        } else {
            if (strlen($IDCard) == 18) {
                $tyear  = intval(substr($IDCard, 6, 4));
                $tmonth = intval(substr($IDCard, 10, 2));
                $tday   = intval(substr($IDCard, 12, 2));
            } elseif (strlen($IDCard) == 15) {
                $tyear  = intval("19" . substr($IDCard, 6, 2));
                $tmonth = intval(substr($IDCard, 8, 2));
                $tday   = intval(substr($IDCard, 10, 2));
            }

            if ($tyear > date("Y") || $tyear < (date("Y") - 100)) {
                $flag = 0;
            } elseif ($tmonth < 0 || $tmonth > 12) {
                $flag = 0;
            } elseif ($tday < 0 || $tday > 31) {
                $flag = 0;
            } else {
                if ($format) {
                    $tdate = $tyear . "-" . $tmonth . "-" . $tday;
                } else {
                    $tdate = $tmonth . "-" . $tday;
                }

                if ((time() - mktime(0, 0, 0, $tmonth, $tday, $tyear)) > 18 * 365 * 24 * 60 * 60) {
                    $flag = 0;
                } else {
                    $flag = 1;
                }
            }
        }
        $result['error']    = 2;//0：未知错误，1：身份证格式错误，2：无错误
        $result['isAdult']  = $flag;//0标示成年，1标示未成年
        $result['birthday'] = $tdate;//生日日期
        return $result;
    }

    /**
     * 数组中有json字符串递归转成数组
     * @param mixed $array 字符串
     * @return mixed 成功返回true，失败返回false
     */
    public static function json_string_to_array($array)
    {
        if (is_array($array)) {
            foreach ($array as $key => $val) {
                if (is_array($val)) {
                    $array[$key] = self::json_string_to_array($val);
                } elseif (tools()::is_json($val)) {
                    $array[$key] = json_decode($val, true);
                }
            }
        }
        return $array;
    }

    /**
     * 判断字符串是否为 Json 格式
     * @param string $json_str 字符串
     * @return bool 成功返回true，失败返回false
     */
    public static function is_json($json_str)
    {
        try {
            if (empty($json_str) || !is_string($json_str)) {
                return false;
            }
            return is_array(json_decode($json_str, true));
        } catch (Exception $e) {
            return false;
        }
    }

    public static function calculate_time_difference_in_seconds($beginTime, $endTime)
    {
        if (empty($beginTime) || empty($endTime)) {
            return 0;
        }
        // 创建DateTime对象
        $dateTimeBegin = new DateTime($beginTime);
        $dateTimeEnd   = new DateTime($endTime);

        // 计算两个时间的差值
        $interval = $dateTimeBegin->diff($dateTimeEnd);

        // 获取差值的总秒数
        $seconds = $interval->days * 86400; // 将天数转换为秒
        $seconds += $interval->h * 3600;    // 将小时数转换为秒
        $seconds += $interval->i * 60;      // 将分钟数转换为秒
        $seconds += $interval->s;           // 加上秒数

        // 微秒部分
        $microseconds = (($dateTimeEnd->format('Uu') - $dateTimeBegin->format('Uu')) % 1000000);

        // 最终秒数需要加上微秒部分（转换为秒）
        return $seconds + $microseconds / 1000000;
    }

    /**
     * 获取订单号
     * @return string 订单号
     */
    public static function get_bill_number()
    {
        $dateTime = new DateTime();
        return $dateTime->format('YmdHisu');
    }

    public static function clear_all_cookie_and_session()
    {
        foreach ($_COOKIE as $key => $value) {
            cookie($key, null);
        }
        session(null);
    }

    /**
     * Decode a string with URL-safe Base64.
     *
     * @param string $string A Base64 encoded string
     * @return string A decoded string
     */
    public static function urlsafe_base64_encode($string)
    {
        $data = base64_encode($string);
        return str_replace(array('+', '/', '='), array('-', '_', ''), $data);
    }

    public static function urlsafe_base64_decode($string)
    {
        $data = str_replace(array('-', '_'), array('+', '/'), $string);
        $mod4 = strlen($data) % 4;
        if ($mod4) {
            $data .= substr('====', $mod4);
        }
        return base64_decode($data);
    }

    public static function get_number($string)
    {
        $str = trim($string);
        if (empty($str)) {
            return '';
        }
        $result = '';
        for ($i = 0; $i < strlen($str); $i++) {
            if (is_numeric($str[$i])) {
                $result .= $str[$i];
            }
        }
        return $result;
    }

    public static function remove_taobao_jd_class($string)
    {
        $string = preg_replace('/(<img[^>]*?)style="[^"]*"/i', '$1', $string);
        $string = str_replace("descV8-singleImage-image lazyload", '', $string);
        $string = preg_replace('/class=""\s*/', '', $string);
        
        if (strpos($string, 'img-ks-lazyload') === false && strpos($string, 'shop-editor-zone-insert') === false) {
            //如果不包含淘宝京东图片特征, 原样返回 避免出错
            return $string;
        }
        $search = ['description', 'J_DetailSection', 'tshop-psm-bdetaildes', 'tshop-psm', 'content', 'ke-post', 'img-ks-lazyload', 'absmiddle', 'J_DcBottomRightWrap', 'class=""'];
        $string = str_replace($search, '', $string);
        $search = ['shop-editor-zone-insert', 'class=""'];

        return str_replace($search, '', $string);
    }

    public static function add_rich_img_class($string)
    {
        $string = str_replace('<img class=""', '<img ', $string);
        $string = str_replace('<img', '<img class="rich_img"', $string);
        return self::add_thumbnail_in_html($string);
    }

    public static function get_image_urls_from_html($html)
    {
        $pattern = "/<img.*?src=['\"](.*?(?:\.(jpg|png|jpeg|bmp|gif|avif|JPG|PNG|JPEG|BMP|GIF|AVIF)))['\"].*?>/i";
        $urls    = [];
        preg_match_all($pattern, $html, $matches);
        foreach ($matches[1] as $imageUrl) {
            $urls[] = $imageUrl;
        }
        return $urls;
    }

    public static function add_thumbnail_in_html($html)
    {
        $pattern = "/[img|IMG].*?src=['|\"](.*?(?:[.jpg|.png|.jpeg|.bmp|.gif|.avif|.JPG|.PNG|.JPEG|.BMP|.GIF|.AVIF]))['|\"].*?[\/]?>/";
        return preg_replace_callback($pattern,
            function ($match) {
                $html_string = $match[0]; // 整个img开头的html字符串
                $image_url   = $match[1]; // 图片url
                return str_replace($image_url, self::add_thumbnail($image_url), $html_string);
            },
            $html);
    }

    public static function add_thumbnail($url, $force = false)
    {
        if (empty($url)) {
            return $url;
        }
        $files_domain = config('app.files_domain');
        if (strpos($url, $files_domain) === false) {
            return $url;
        }
        $queryStringPos = strpos($url, '?');
        if ($force && $queryStringPos !== false) {
            $url = substr($url, 0, $queryStringPos);
        }
        if (strpos($url, '?') !== false) {
            return $url;
        }
        return $url . '?x-oss-process=style/thumbnail';
    }

    public static function add_thumbnail_small($url, $force = false)
    {
        if (empty($url)) {
            return $url;
        }
        $files_domain = config('app.files_domain');
        if (strpos($url, $files_domain) === false) {
            return $url;
        }
        $queryStringPos = strpos($url, '?');
        if ($force && $queryStringPos !== false) {
            $url = substr($url, 0, $queryStringPos);
        }
        if (strpos($url, '?') !== false) {
            return $url;
        }
        return $url . '?x-oss-process=style/thumbnail_small';
    }

    public static function add_thumbnail_mini($url, $force = false)
    {
        if (empty($url)) {
            return $url;
        }
        $files_domain = config('app.files_domain');
        if (strpos($url, $files_domain) === false) {
            return $url;
        }
        $queryStringPos = strpos($url, '?');
        if ($force && $queryStringPos !== false) {
            $url = substr($url, 0, $queryStringPos);
        }
        if (strpos($url, '?') !== false) {
            return $url;
        }
        return $url . '?x-oss-process=style/thumbnail_mini';
    }

    public static function is_empty_or_zero($string)
    {
        if (is_numeric($string) && $string == 0) {
            return true;
        }
        if (empty($string)) {
            return true;
        }
        return false;
    }

    /**
     * 一般用于html解析table后需要转换成插入数据库的二维数组
     * @param array $array 数据
     * @param array $header 表头
     * @return  array
     * @throws Exception
     */
    public static function table_array_to_data($array, $header)
    {
        $length   = count($header);
        $all_data = [];
        foreach ($array as $key => $val) {
            $fmod     = $key % $length; //求模 以确认这个数据是对应哪个key
            $index    = intval($key / $length); //确认当前value是放到第几个二维数组中
            $key_name = array_values($header)[$fmod]; //确认当前的value对应哪个key_name
            if ($key >= $length) {
                //前 $length 个数据是表头,不需要汇总
                $all_data[$index][$key_name] = $val;
            }
        }
        return $all_data;
    }

    /**
     * 返回两个字符串公共部分(从前)
     * @param string $str1 第一个数
     * @param string $str2 第二个数
     * @return  string
     */
    public static function get_common_string($str1, $str2)
    {
        $len1 = strlen($str1);
        $len2 = strlen($str2);
        for ($i = 0; $i < min($len1, $len2); $i++) {
            if (substr($str1, 0, $i + 1) !== substr($str2, 0, $i + 1)) {
                break;
            }
        }
        return substr($str1, 0, $i);
    }

    /**
     * @throws Exception
     */
    public static function add_string_and_number($str, $number)
    {
        // 验证字符串是否只包含数字和前导零
        if (!is_numeric($str)) {
            throw new Exception($str . '无法递增' . $number . '由于不是数字');
        }
        // 将字符串转换为数字并执行加法, 必须用 bcadd 不可以直接用+ 否则20250205234603179070 这种位数超长出会变成科学计数
        $result = bcadd($str, $number);
        // 将结果转换回字符串，并保留前导零
        $result_str = str_pad($result, strlen($str), '0', STR_PAD_LEFT);

        // 检查结果位数是否超过了原始字符串的位数
        if (strlen($result_str) > strlen($str)) {
            throw new Exception($str . '无法递增' . $number . '由于末尾连续数字递增后超出最大值');
        }

        return $result_str;
    }

    public static function find_last_continuous_numbers($str)
    {
        $lastNumbers  = '';
        $remainingStr = $str;

        // 从字符串末尾开始查找连续的数字
        for ($i = strlen($str) - 1; $i >= 0; $i--) {
            if (ctype_digit($str[$i])) {
                $lastNumbers = $str[$i] . $lastNumbers;
            } else {
                // 遇到非数字字符时停止查找
                break;
            }
        }

        // 如果找到连续数字，则截取剩余部分
        if ($lastNumbers !== '') {
            $remainingStr = substr($str, 0, -strlen($lastNumbers));
        }
        // 返回结果数组
        return [$remainingStr, $lastNumbers];
    }


    public static function increment_string($str, $num = 1)
    {
        $str_array = self::find_last_continuous_numbers($str);
        if ($str_array[1] === '') {
            throw new Exception($str . '无法递增' . $num . '由于末尾不存在连续数字');
        }
        $last_str = self::add_string_and_number($str_array[1], $num);
        return $str_array[0] . $last_str;
    }


    public static function removeEmptyArrays($array)
    {
        return array_filter($array, function ($subarray) {
            // 检查子数组是否包含非空字符串
            foreach ($subarray as $value) {
                if ($value !== '') {
                    // 如果找到非空字符串，则返回true，保留子数组
                    return true;
                }
            }
            // 如果循环结束都没有返回true，说明子数组只包含空字符串
            return false;
        });
    }

    /*
     * @param string $key
     * @param string $rate   10/s   100/d 最近多少秒内最多允许多少个请求
     * return bool|int
     */
    /**
     * @throws \RedisException
     * @throws Exception
     */
    public static function check_throttle($key, $rate)
    {
        $key            = 'throttle:' . $key;
        $redis_instance = get_redis_instance();
        [$num, $period] = explode("/", $rate);
        $duration    = [
            's' => 1,
            'm' => 60,
            'h' => 3600,
            'd' => 86400,
        ];
        $duration    = $duration[$period] ?? (int)$period;
        $now_time    = time();
        $start_score = $now_time - $duration;
        $redis_instance->zRemRangeByScore($key, 0, $start_score);
        $count     = $redis_instance->zCount($key, $start_score, $now_time);
        $remainder = $num - $count;
        if ($remainder <= 0) {
            return false;
        }
        $redis_instance->zAdd($key, $now_time, microsecond());
        $redis_instance->expire($key, $duration);
        return $remainder;
    }

    /**
     * 用于将数字的所有元素相加
     * @param array $array 数字数组
     * @param string $scale 精度 默认为小数点后两位
     * @return  float
     * @throws Exception
     */
    public static function bcadd_array($array, $scale = '2')
    {
        $res = 0;
        foreach ($array as $item) {
            if (!is_numeric($item)) {
                throw new Exception('【' . $item . '】不是有效数字!');
            }
            $res = bcadd($res, $item, $scale);
        }
        return floatval($res);
    }

    /**
     * PHP精确计算  主要用于货币的计算用
     * @param string $n1 第一个数
     * @param string $symbol 计算符号 + - * / %
     * @param string $n2 第二个数
     * @param string $scale 精度 默认为小数点后两位
     * @return  string
     * @throws Exception
     */
    public static function nc_price_calculate($n1, $symbol, $n2, $scale = '2')
    {
        $res = null;
        switch ($symbol) {
            case "+"://加法
                $res = bcadd($n1, $n2, $scale);
                break;
            case "-"://减法
                $res = bcsub($n1, $n2, $scale);
                break;
            case "*"://乘法
                $res = bcmul($n1, $n2, $scale);
                break;
            case "/"://除法
                $res = bcdiv($n1, $n2, $scale);
                break;
            case "%"://求余、取模
                $res = bcmod($n1, $n2, $scale);
                break;
            default:
                throw new Exception('非法的操作符');
        }
        return $res;
    }

    /**
     * GUID转MD5
     * @param string $str 字符串
     * @return string GUID
     */
    public static function guid_md5($str)
    {
        return str_replace('-', '', $str);
    }

    /**
     * MD5转GUID
     * @param string $str 字符串
     * @return string GUID
     */
    public static function md5_guid($str)
    {
        return substr($str, 0, 8) . '-' . substr($str, 8, 4) . '-' . substr($str, 12, 4) . '-' . substr($str, 16, 4) . '-' . substr($str, 20, 12);
    }

    /**
     * 判断字符串是否为 GUID 格式
     * @param string $str 字符串
     * @return bool 成功返回true，失败返回false
     */
    public static function is_guid($str)
    {
        return preg_match("/^\w{8}-(\w{4}-){3}\w{12}$/", $str);
    }

    /**
     * 判断字符串是否为 手机 格式
     * @param string $str 字符串
     * @return bool 成功返回true，失败返回false
     */
    public static function is_mobile($str)
    {
        ///^1[23456789]\d{9}$/
        return preg_match("/^1\d{10}$/", $str);
    }

    /**
     * 判断字符串是否为 油卡 格式
     * @param string $str 字符串
     * @return bool 成功返回true，失败返回false
     */
    public static function is_oil_card_id($str)
    {
        return preg_match("/^1\d{18}$/", $str) || preg_match("/^9\d{15}$/", $str);
    }

    /**
     * 判断字符串是否为 邮箱 格式
     * @param string $str 字符串
     * @return bool 成功返回true，失败返回false
     */
    public static function is_email($str)
    {
        if (strstr($str, '-')) {
            return false;
        }
        $pattern = "/^([0-9A-Za-z\\-_\\.]+)@([0-9a-z]+\\.[a-z]{2,3}(\\.[a-z]{2})?)$/i";
        return preg_match($pattern, $str);
    }

    /**
     * 文本中提取数字
     * @param string $str 字符串
     * @return string
     */
    public static function find_string_number($str)
    {
        preg_match_all('/\d+/', $str, $arr);
        return join('', $arr[0]);
    }

    /**
     * 文本中订单号,周期发货支持 - 故不能用 find_string_number
     * @param string $str 字符串
     * @return string
     */
    public static function find_string_bill_number($str)
    {
        preg_match_all('/[-0-9]/', $str, $arr);
        return join('', $arr[0]);
    }

    /**
     * 生成唯一数字
     * @param integer $length 长度
     * @param integer $num 数量
     * @param array $old_arr 原数据
     * @return array|string
     */
    public static function unique_rand($length, $num = 1, array $old_arr = [])
    {
        $count = 0;
        $min   = (int)('1' . str_repeat(0, $length - 1));
        $max   = (int)str_repeat(9, $length);
        $arr   = [];
        while ($count < $num) {
            for ($x = 0; $x <= $num; $x++) {
                $rand  = mt_rand($min, $max);
                $arr[] = $rand;
            }
            $arr = array_flip(array_flip($arr));
            if (!empty($old_arr)) {
                $arr = array_diff($arr, $old_arr);
            }
            $count = count($arr);
        }
        $return = array_slice($arr, 0, $num);
        return (count($return) == 1) ? reset($return) : $return;
    }

    /**
     * 判断是否以特定的字符串结束
     * @param string $haystack
     * @param string $needle
     * @return boolean $result
     *
     */
    public static function end_with($haystack, $needle)
    {
        $haystack = (string)$haystack; //转换成字符串
        $needles  = is_array($needle) ? $needle : [$needle];
        foreach ($needles as $key => $val) {
            $val    = (string)$val; //转换成字符串
            $length = strlen($val);
            if ($length == 0) {
                return true;
            }
            if (substr($haystack, -$length) === $val) {
                return true;
            }
        }
        return false;
    }

    /**
     * 仅仅提取中文
     * @param string $string 需要处理的字符串
     * @param string $replace_space 是否替换空格
     * @return string
     *
     */
    public static function remove_empty_string($string, $replace_space = true)
    {
        $string = (string)$string;
        $string = str_replace(array("\t", "\0", "\v", "\e", "\r", "\n", "\r\n", PHP_EOL), '', $string); //去掉换行符 去掉制表符
        if ($replace_space) {
            $string = str_replace(' ', '', $string);
        }
        $string = preg_replace('/[[:cntrl:]]/', '', $string);
        $string = preg_replace('/[\x00-\x1F\x7F]/', '', $string);
//        $string = preg_replace('/[^\x20-\x7E]/', '', $string);// 移除所有非打印字符, 范围太大
        return trim($string);
    }

    /**
     * 仅仅提取中文+英文
     * @param string $string
     * @return string
     *
     */
    public static function find_string_cn_and_en($string)
    {
        preg_match_all('/[a-zA-Z\x{4e00}-\x{9fff}]+/u', $string, $content);
        return join('', $content[0]);
    }

    /**
     * 判断是否以特定的字符串开头
     * @param string $haystack
     * @param string $needle
     * @return boolean $result
     *
     */
    public static function start_with($haystack, $needle)
    {
        $haystack = (string)$haystack; //转换成字符串
        $needles  = (is_array($needle) ? $needle : [$needle]);
        foreach ($needles as $key => $val) {
            $val    = (string)$val; //转换成字符串
            $length = strlen($val);
            if ($length == 0) {
                return true;
            }
            if (substr($haystack, 0, strlen($val)) === $val) {
                return true;
            }
        }
        return false;
    }

    /**
     * 价格由元转分
     * @param string $price 金额
     * @return int 数字
     * @throws Exception
     */
    public static function nc_price_yuan2fen($price)
    {
        return (int)self::nc_price_calculate(100, "*", self::nc_price_format($price));
    }

    /**
     * 价格格式化
     * @param int $price
     * @return string  $price_format
     */
    public static function nc_price_format($price)
    {
        return number_format($price, 2, '.', '');
    }

    /**
     * 数组重新排序
     * @param array $array1 数组1
     * @param array $array2 数组2
     * @return array $new_array1 重新排的数组1
     */
    public static function re_sort_array($array1, $array2)
    {
        //$array1=['首页'=>[],'商城'=>[], '提货'=>[], '我的'=>[], '购物车'=>[],'分销'=>[]];
        //$array2=['商城', '分销', '购物车'];
        //对 $array1 重新排序  排序原则是  如果元素没有在$array2 出现  就按照原有顺序不变 但是如果有$array2出现  必须维持按照$array2的先后顺序展示
        //最终期望$array1 变成  ['首页'=>[],'商城'=>[], '提货'=>[], '我的'=>[],'分销'=>[], '购物车'=>[]]
        $index      = 0;
        $new_array1 = [];
        foreach ($array1 as $key => $val) {
            if (in_array($key, $array2)) {
                $new_key              = $array2[$index++];
                $new_array1[$new_key] = $array1[$new_key];
            } else {
                $new_array1[$key] = $val;
            }
        }
        return $new_array1;
    }

    /**
     * 价格由分转元
     * @param string $price 金额
     * @return int 数字
     * @throws Exception
     */
    public static function nc_price_fen2yuan($price)
    {
        return self::nc_price_calculate($price, "/", 100);
    }

    public static function to_tree($arr, $keyNodeId = 'guid', $keyParentId = 'parent_guid', $keyChildrens = 'sub', &$refs = null)
    {
        $refs = [];
        foreach ($arr as $offset => $row) {
            $arr[$offset][$keyChildrens] = [];
            $refs[$row[$keyNodeId]]      = &$arr[$offset];
        }

        $tree = [];
        foreach ($arr as $offset => $row) {
            $parentId = $row[$keyParentId];
            if ($parentId) {
                if (!isset($refs[$parentId])) {
                    $tree[] = &$arr[$offset];
                    continue;
                }
                $parent                  = &$refs[$parentId];
                $parent[$keyChildrens][] = &$arr[$offset];
            } else {
                $tree[] = &$arr[$offset];
            }
        }
        return $tree;
    }

    public static function tree_sort(array $arr, $parent_guid = null, $id = 'guid', $parentId = 'parent_guid')
    {
        $parent_guid = $parent_guid ?: self::get_empty_guid();
        if (empty($arr)) {
            return $arr;
        }
        //   if (count($arr) == 1) {
        //       return $arr;
        //   }
        $newArr = [];
        foreach ($arr as $key => $item) {
            if ($parent_guid == $item[$parentId]) {
                in_array($item, $newArr) || $newArr[] = $item;
                unset($arr[$key]);
                $sub    = tools()::tree_sort($arr, $item[$id]);
                $newArr = array_merge($newArr, $sub);
            }
        }
        return $newArr;
    }

    public static function get_empty_guid()
    {
        return '00000000-0000-0000-0000-000000000000';
    }

    /**
     * 获取顶级域名
     * @param string $url URL
     * @return string
     */
    public static function get_top_host($url)
    {
        try {
            $list = [
                '127.0.0.1',
                'chrome-extension://',
                'chrome://',
                'localhost',
            ];
            if (empty($url) || tools()::match_keyword_in_array($url, $list)) {
                return '';
            }
            $url   = strtolower($url);  //首先转成小写
            $hosts = parse_url($url);
            if ($hosts === false) {
                return '';
            }
            $host = $hosts['host'];
            //查看是几级域名
            $data = explode('.', $host);
            $n    = count($data);
            //判断是否是双后缀
            $preg = '/[\w].+\.(com|net|org|gov|edu)\.cn$/';
            if (($n > 2) && preg_match($preg, $host)) {
                //双后缀取后3位
                $host = $data[$n - 3] . '.' . $data[$n - 2] . '.' . $data[$n - 1];
            } else {
                //非双后缀取后两位
                $host = $data[$n - 2] . '.' . $data[$n - 1];
            }
            return strtolower($host);
        } catch (Exception | Throwable $e) {
            wr_log('url:' . $url . '获取顶级域名失败:' . $e->getMessage(), 1);
            return '';
        }

    }

    public static function formatTree(array $array, $parent_guid = null, $id = 'guid', $parentId = 'parent_guid', $keyChildrens = 'sub')
    {
        $parent_guid = $parent_guid ?: self::get_empty_guid();
        $arr         = [];
        $temp        = [];
        foreach ($array as $v) {
            $v['open'] = true;
            if ($v[$parentId] == $parent_guid) {
                $temp = self::formatTree($array, $v[$id], $id, $parentId, $keyChildrens);
                //判断是否存在子数组
                $temp && $v[$keyChildrens] = $temp;
                $arr[] = $v;
            }
        }
        return $arr;
    }

    public static function tree_to_html($tree)
    {
        $html = '';
        foreach ($tree as $t) {
            if (empty($t['sub'])) {
                $html .= "<li>{$t['title']}";
                // $html .= " <a class='btn btn-xs btn-primary'>编辑</a>";
                // $html .= " <a class='btn btn-xs btn-danger delete'>删除</a>";
                $html .= "</li>";
            } else {
                $html .= "<li>" . $t['title'];
                // $html .= " <a class='btn btn-xs btn-primary'>编辑</a>";
                // $html .= " <a class='btn btn-xs btn-danger delete'>删除</a>";
                $html .= self::tree_to_html($t['sub']);
                $html .= "</li>";
            }
        }
        return $html ? '<ul>' . $html . '</ul>' : $html;
    }

    /**
     * 移除HTML标签
     * @param string $str 字符
     * @return string
     */
    public static function remove_html_tag($str)
    {
        //清除HTML代码、空格、回车换行符
        //trim 去掉字串两端的空格
        //strip_tags 删除HTML元素
        $str = trim($str);
        $str = str_replace('/<script[^>]*?>(.*?)<\/script>/si', '', $str);
        $str = str_replace('/<style[^>]*?>(.*?)<\/style>/si', '', $str);
        $str = strip_tags($str, "");
        $str = str_replace("\t", "", $str);
        $str = str_replace("\r\n", "", $str);
        $str = str_replace("\r", "", $str);
        $str = str_replace("\n", "", $str);
        $str = str_replace(" ", "", $str);
        $str = str_replace("&nbsp;", "", $str);
        return trim($str);
    }

    public static function to_one_level_array($arr)
    {
        foreach ($arr as $key => $val) {
            if (!empty($val['children'])) {
                //发现有子元素 全部压入新数组,然后unset掉
                foreach ($val['children'] as $k => $v) {
                    $arr[] = $v;
                }
                unset($arr[$key]['children']);
                return self::to_one_level_array($arr);
            }
        }
        return $arr;
    }

    public static function create_parent_node($arr, $pid = 0)
    {
        foreach ($arr as $key => $val) {
            $arr[$key]['pid'] = $pid;
            if (!empty($val['children'])) {
                $arr[$key]['children'] = self::create_parent_node($val['children'], $val['id']);
            }
        }
        return $arr;
    }

    /**
     * Emoji原形转换为String
     * @param string $content 要处理的表情内容
     * @param boolean $replace_to_empty 是否替换成空
     * @return string
     */
    public static function emoji_encode($content, $replace_to_empty = false)
    {
        return json_decode(preg_replace_callback("/(\\\u[ed][0-9a-f]{3})/i", function ($str) use ($replace_to_empty) {
            return $replace_to_empty ? '' : addslashes($str[0]);
        }, json_encode($content)));
    }

    /**
     * Emoji字符串转换为原形
     * @param string $content
     * @return string
     */
    public static function emoji_decode($content)
    {
        return json_decode(preg_replace_callback('/\\\\\\\\/i', function () {
            return '\\';
        }, json_encode($content)));
    }

    /**
     * 安全URL编码
     * @param array|string $data
     * @return string
     */
    public static function encode_url($data)
    {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(serialize($data)));
    }

    /**
     * 安全URL解码
     * @param string $string
     * @return string
     */
    public static function decode_url($string)
    {
        $data = str_replace(['-', '_'], ['+', '/'], $string);
        $mod4 = strlen($data) % 4;
        !!$mod4 && $data .= substr('====', $mod4);
        return unserialize(base64_decode($data));
    }

    /**
     * 一维数据数组生成数据树
     * @param array $list 数据列表
     * @param string $id ID Key
     * @param string $pid 父ID Key
     * @param string $path
     * @return array
     */
    public static function arr2table($list, $id = 'id', $pid = 'pid', $path = 'path', $ppath = '')
    {
        $_array_tree = self::arr2tree($list, $id, $pid);
        $tree        = [];
        foreach ($_array_tree as $_tree) {
            $_tree[$path] = $ppath . '-' . $_tree[$id];
            $_tree['spl'] = str_repeat("&nbsp;&nbsp;&nbsp;├&nbsp;&nbsp;", substr_count($ppath, '-'));
            if (!isset($_tree['sub'])) {
                $_tree['sub'] = [];
            }
            $sub = $_tree['sub'];
            unset($_tree['sub']);
            $tree[] = $_tree;
            if (!empty($sub)) {
                $sub_array = self::arr2table($sub, $id, $pid, $path, $_tree[$path]);
                $tree      = array_merge($tree, (array)$sub_array);
            }
        }
        return $tree;
    }

    /**
     * @param int $num //密码数量
     * @param int $length //密码长度
     * @param int $type //密码类型 1 纯数字 2 纯字母 3 数字加字母
     * @return array
     */
    public static function generate_random_password_batch($num, $length, $type = 1)
    {
        $password = [];
        for ($i = 0; $i < $num; $i++) {
            $password[] = self::generate_random_password($length, $type);
        }
        return $password;
    }

    /**
     * @param int $length //密码长度
     * @param int $type //密码类型 1 纯数字 2 纯字母 3 数字加字母
     * @return string
     */
    public static function generate_random_password($length, $type = 1)
    {
        $password = '';
        switch ($type) {
            case 1: // 数字
                $characters = '0123456789'; // 删除易混淆的数字0和1
                break;
            case 2: // 字母
                $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // 删除易混淆的字母0和1以及I、i、O、o
                break;
            case 3: // 数字加字母
                $characters = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'; // 删除易混淆的数字0和1以及字母I、i、O、o
                break;
            default:
                $characters = '';
                break;
        }
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
    }

    /**
     * 一维数据数组生成数据树
     * @param array $list 数据列表
     * @param string $id 父ID Key
     * @param string $pid ID Key
     * @param string $son 定义子数据Key
     * @return array
     */
    public static function arr2tree($list, $id = 'id', $pid = 'pid', $son = 'sub')
    {
        $tree = $map = [];
        foreach ($list as $item) {
            $map[$item[$id]] = $item;
        }
        foreach ($list as $item) {
            if (isset($item[$pid]) && isset($map[$item[$pid]])) {
                $map[$item[$pid]][$son][] = &$map[$item[$id]];
            } else {
                $tree[] = &$map[$item[$id]];
            }
        }
        unset($map);
        return $tree;
    }

    /**
     * 获取数据树子ID
     * @param array $list 数据列表
     * @param int $id 起始ID
     * @param string $key 子Key
     * @param string $pkey 父Key
     * @return array
     */
    public static function get_arr_sub_ids($list, $id = 0, $key = 'id', $pkey = 'pid')
    {
        $ids = [intval($id)];
        foreach ($list as $vo) {
            if (intval($vo[$pkey]) > 0 && intval($vo[$pkey]) == intval($id)) {
                $ids = array_merge($ids, self::get_arr_sub_ids($list, intval($vo[$key]), $key, $pkey));
            }
        }
        return $ids;
    }

    public static function array_to_xml($arr, $root = '')
    {
        $xml = '';
        if ($root) {
            $xml .= '<' . $root . '>';
        }
        foreach ($arr as $key => $val) {
            if (is_array($val)) {
                $xml .= "<" . $key . ">" . self::array_to_xml($val, $root) . "</" . $key . ">";
            } else {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";
            }
        }
        if ($root) {
            $xml .= '</' . $root . '>';
        }
        return $xml;
    }

    public static function excel_string_to_date($string)
    {
        //excel 导入可能把日期变成了数字 46387 例如这样  实际上是 2026-12-31
        // 1900年1月1日的UNIX时间戳
        $baseTimestamp = strtotime('1900-01-01');
        $timestamp     = $baseTimestamp + ($string - 1) * 86400;
        return date('Y-m-d', $timestamp);
    }

    public static function get_memory_get_usage()
    {
        return round(memory_get_usage() / 1024 / 1024, 4);
    }

    public static function get_excel_max_column_width(array $array)
    {
        // 初始化一个数组，用于存储每列的最大宽度
        $widths    = [];
        $max_width = 50; //防止某些列过宽
        // 遍历二维数组
        foreach ($array as $row) {
            foreach ($row as $index => $value) {
                // 确保宽度数组中有对应的索引
                if (!isset($widths[$index])) {
                    $widths[$index] = 0;
                }
                // 计算当前元素的长度
                $length = (strlen($value) + mb_strlen($value, 'UTF8')) / 2;
                $length = min($max_width, $length);
                // 更新最大宽度
                if ($length > $widths[$index]) {
                    $widths[$index] = $length;
                }
            }
        }

        // 生成新的数组格式
        $headerMap   = array_keys($widths);
        $finalWidths = [];
        foreach ($widths as $index => $width) {
            // 计算所需的字母对数量
            $numPairs = (int)($index / 26) + 1;
            // 生成字母对
            $letterPair = '';
            for ($i = 0; $i < $numPairs; $i++) {
                $letter     = chr(65 + ($index % 26));
                $letterPair = $letter . $letterPair; // 将字母前置，以便在超过'Z'时正确排序
                $index      -= 26;
            }
            $finalWidths[$letterPair . ':' . $letterPair] = $width;
        }
        return $finalWidths;
    }

    public static function trim_array($array)
    {
        return (!is_array($array)) ? trim($array) : array_map([self::class, __FUNCTION__], $array);
    }

    public static function md5_16($str)
    {
        return substr(md5($str), 8, 16);
    }

    public static function insert_array_key_value(&$array, $key, $value, $positionKey)
    {
        // 检查位置键是否存在
        if (!array_key_exists($positionKey, $array)) {
            throw new Exception("Position key '$positionKey' does not exist in the array.");
        }

        // 分割数组为两部分：在位置键之前的部分和之后的部分
        $before = array_slice($array, 0, array_search($positionKey, array_keys($array)) + 1, true);
        $after  = array_slice($array, array_search($positionKey, array_keys($array)) + 1, null, true);

        // 合并数组，插入新键值对
        $array = $before + [$key => $value] + $after;
    }

    public static function parse_url_params($url)
    {
        $query = parse_url($url, PHP_URL_QUERY);
        if (is_null($query)) {
            return [];
        }
        parse_str($query, $params);
        return $params;
    }

    public static function get_current_dir($dir)
    {
        $files = [];
        if (is_dir($dir)) {
            if ($handle = opendir($dir)) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != "." && $file != "..") {
                        if (is_dir($dir . "/" . $file)) {
                            $files[] = $file;
                        }
                    }
                }
                closedir($handle);
                return $files;
            }
        }
    }

    //获取文件目录列表,该方法返回数组

    public static function scan_dir($dir)
    {
        $files = [];
        if (is_dir($dir)) {
            if ($handle = opendir($dir)) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != "." && $file != "..") {
                        if (is_dir($dir . "/" . $file)) {
                            $files[$file] = self::scan_dir($dir . "/" . $file);
                        } else {
                            $files[] = self::array_iconv($dir . "/" . $file);
                        }
                    }
                }
                closedir($handle);
                return $files;
            }
        }
    }

    public static function get_array_value_mb_strlen($array)
    {
        $length = 0;
        foreach ($array as $key => $val) {
            $length += mb_strlen($val);
        }
        return $length;
    }

    /**
     * 将unicode转换成字符
     * @param int $unicode
     * @return string UTF-8字符
     **/
    public static function unicode2_char($unicode)
    {
        if ($unicode < 128) return chr($unicode);
        if ($unicode < 2048) return chr(($unicode >> 6) + 192) .
            chr(($unicode & 63) + 128);
        if ($unicode < 65536) return chr(($unicode >> 12) + 224) .
            chr((($unicode >> 6) & 63) + 128) .
            chr(($unicode & 63) + 128);
        if ($unicode < 2097152) return chr(($unicode >> 18) + 240) .
            chr((($unicode >> 12) & 63) + 128) .
            chr((($unicode >> 6) & 63) + 128) .
            chr(($unicode & 63) + 128);
        return false;
    }

    /**
     * 将字符转换成unicode
     * @param string $char 必须是UTF-8字符
     * @return int
     **/
    public static function char2_unicode($char)
    {
        switch (strlen($char)) {
            case 1 :
                return ord($char);
            case 2 :
                return (ord($char[1]) & 63) |
                    ((ord($char[0]) & 31) << 6);
            case 3 :
                return (ord($char[2]) & 63) |
                    ((ord($char[1]) & 63) << 6) |
                    ((ord($char[0]) & 15) << 12);
            case 4 :
                return (ord($char[3]) & 63) |
                    ((ord($char[2]) & 63) << 6) |
                    ((ord($char[1]) & 63) << 12) |
                    ((ord($char[0]) & 7) << 18);
            default :
                trigger_error('Character is not UTF-8!', E_USER_WARNING);
                return false;
        }
    }

    /**
     * 全角转半角
     * @param string $str
     * @return string
     **/
    public static function sbc2_dbc($str)
    {
        return preg_replace_callback(
            '/[\x{3000}\x{ff01}-\x{ff5f}]/u',
            function ($matches) {
                $unicode = self::char2_unicode($matches[0]);
                if ($unicode == 0x3000) {
                    return " ";
                }
                $code = $unicode - 0xfee0;
                if ($code > 256) {
                    return self::unicode2_char($code);
                } else {
                    return chr($code);
                }
            },
            $str);
    }

    /**
     * 半角转全角
     * @param string $str
     * @return string
     **/
    public static function dbc2_sbc($str)
    {
        return preg_replace_callback(
        // 半角字符
            '/[\x{0020}\x{0020}-\x{7e}]/u',
            // 编码转换
            // 0x0020是空格，特殊处理，其他半角字符编码+0xfee0即可以转为全角
            function ($matches) {
                $unicode = self::char2_unicode($matches[0]);
                if ($unicode == 0x0020) {
                    return self::unicode2_char(0x3000);
                }

                $code = $unicode + 0xfee0;
                if ($code > 256) {
                    return self::unicode2_char($code);

                } else {
                    return chr($code);
                }
            },
            $str);
    }

    /**
     * 判断时间是否空guid
     * @param string $str
     * @return boolean
     **/
    public static function is_empty_guid($str)
    {
        return $str == self::get_empty_guid();
    }

    public static function is_date_time($dateTime)
    {

        $ret = strtotime($dateTime);

        return $ret !== FALSE && $ret != -1;

    }

    /**
     * 付完成时间，格式为yyyyMMddHHmmss，如2009年12月25日9点10分10秒表示为20091225091010。格式化返回datetime格式
     * @param string $str
     * @return string
     **/
    public static function format_time($str)
    {
        if (strpos($str, '-') !== false) {
            return $str;
        }
        // 创建一个DateTime对象，指定原始时间的格式
        $date = DateTime::createFromFormat('YmdHis', $str);
        // 检查是否成功创建DateTime对象
        if ($date) {
            // 转换为新的格式
            return $date->format('Y-m-d H:i:s');  // 输出：2025-04-08 16:04:02
        } else {
            throw new Exception('时间格式不正确');
        }
//        if (!self::is_date_time($str)) {
//            $str = substr($str, 0, 4) . '-' . substr($str, 4, 2) . '-' . substr($str, 6, 2) . ' ' . substr($str, 8, 2) . ':' . substr($str, 10, 2) . ':' . substr($str, 12, 2);  // bcd
//        }
//        return $str;
    }

    public static function _array_to_fs_table_data($array)
    {
        $data = [];
        foreach ($array as $key => $val) {
            $data[] = ['key_name' => $key, 'value' => $val];
        }
        return $data;
    }

    public static function _parse_fs_form_data_attr($data, $json_key)
    {
        $form_data = self::_get_fs_form_data($data, $json_key);
        $json_data = [];
        foreach ($form_data[$json_key] as $key => $val) {
            if (!empty($val['name'])) {
                $json_data[] = [
                    'guid' => $val['guid'] ?? '',
                    'name' => $val['name']
                ];
            }
        }
        $form_data[$json_key] = $json_data;
        return $form_data;
    }

    public static function remove_after_code_url($url)
    {
        if (strpos($url, '?code=') !== false) {
            $url = substr($url, 0, strrpos($url, '?code'));
        } elseif (strpos($url, '&code=') !== false) {
            $url = substr($url, 0, strrpos($url, '&code'));
        }
        return $url;
    }

    public static function _get_fs_form_data($data, $json_key)
    {
        $form_data            = urldecode($data['fsFormData']);
        $form_data            = json_decode($form_data, true);
        $table_data           = urldecode($data['fsTableData']);
        $table_data           = json_decode($table_data, true);
        $form_data[$json_key] = $table_data;
        return $form_data;
    }

    public static function _parse_fs_form_data($data, $json_key)
    {
        $form_data = self::_get_fs_form_data($data, $json_key);
        $json_data = [];
        foreach ($form_data[$json_key] as $key => $val) {
            if (!empty($val['key_name'])) {
                if (!isset($val['value'])) {
                    $val['value'] = '';
                }
                if ($val['value'] == 'true') {
                    $val['value'] = true;
                }
                if ($val['value'] == 'false') {
                    $val['value'] = false;
                }
                $json_data[$val['key_name']] = $val['value'];
            }
        }
        $form_data[$json_key] = $json_data;
        return $form_data;
    }

    /**自动下载文件
     * @param string $local_file_name
     * @param string $download_file_name
     * @throws Exception
     */
    public static function auto_download(string $local_file_name, string $download_file_name)
    {
        $exp          = 600;
        $token_data   = [
            'exp'                => $exp,
            'local_file_name'    => $local_file_name,
            'download_file_name' => $download_file_name,
        ];
        $jwt          = TokenService::encode($token_data);
        $web_file_url = (string)url('/index/plugins/download_file', ['token' => $jwt], false, true);
//        $web_file_url = UrlService::long_to_short($web_file_url, $exp, '文件下载');
        if (request()->isPost()) {
            result(['file_url' => $web_file_url]);
        } elseif (request()->isCli()) {
            return $web_file_url;
        } else {
            redirect($web_file_url);
        }
    }

    /**压缩文件夹成压缩文件
     * @param string $source
     * @param string $destination
     * @return  bool
     * @throws Exception
     */
    public static function zip($source, $destination)
    {
        if (!extension_loaded('zip') || !file_exists($source)) {
            return false;
        }
        $path = pathinfo($destination, PATHINFO_DIRNAME);
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $zip = new ZipArchive();
        if (!$zip->open($destination, ZIPARCHIVE::CREATE)) {
            return false;
        }
        $source = str_replace('\\', DIRECTORY_SEPARATOR, realpath($source));
        if (is_dir($source) === true) {
            $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($source), RecursiveIteratorIterator::SELF_FIRST);
            foreach ($files as $file) {
                $file = str_replace('\\', '/', $file);
                // Ignore "." and ".." folders
                if (in_array(substr($file, strrpos($file, '/') + 1), ['.', '..'])) {
                    continue;
                }
                $file = realpath($file);
                if (is_dir($file) === true) {
                    $zip->addEmptyDir(str_replace($source . DIRECTORY_SEPARATOR, '', $file . '/'));
                } else if (is_file($file) === true) {
                    $zip->addFromString(str_replace($source . DIRECTORY_SEPARATOR, '', $file), file_get_contents($file));
                }
            }
        } else if (is_file($source) === true) {
            $zip->addFromString(basename($source), file_get_contents($source));
        }
        return $zip->close();
    }

    public static function long_to_short($long_url, $expires = 0, $title = '')
    {
        return UrlService::get_full_url(UrlService::long_to_short($long_url, $expires, $title));
    }

    /**
     * 外部url网址返OSS网址
     * @param string $image_url
     * @return string
     */
    public static function web_image_url_to_oss($image_url)
    {
        if (empty($image_url)) {
            return $image_url;
        }
        $files_domain    = config('app.files_domain');
        $app_host_domain = config('app.app_host_domain');
        $upload          = Storage::driver('aliyun');
        if (strpos($image_url, $files_domain) === false && strpos($image_url, $app_host_domain) === false) {
            request()->__set('_base64', curl()->get($image_url)->get_base64());
            $file = $upload->upload();
            return tools()::replace_readonly_to_www($file['url']);
        }
        return $image_url;
    }

    /**
     * 外部url网址返回本地路径
     * @param string $url
     * @return string
     */
    public static function web_url_to_local_absolute_path($url)
    {
        $local_path = tools()::get_absolute_path(tools()::web_to_path($url));
        if (!file_exists($local_path)) {
            $url_path           = parse_url($url, PHP_URL_PATH);
            $pos                = strrpos($url_path, '/');
            $path               = substr($url_path, 0, $pos);
            $path_absolute_path = tools()::get_absolute_path($path);
            !is_dir($path_absolute_path) && mkdir($path_absolute_path, 0755, true);
            $local_path      = tools()::get_absolute_path($url_path);
            $files_domain    = config('app.files_domain');
            $app_host_domain = config('app.app_host_domain');
            if (strpos($url, $app_host_domain) === false && strpos($url, $files_domain) === false) {
                $file_path  = '/temp/images/' . tools()::md5_guid(md5($url)) . '.' . pathinfo($url)['extension'];
                $local_path = tools()::get_absolute_path($file_path);
            }

            $result = curl()->set_sink($local_path)->get($url);
        }
        return $local_path;
    }

    /**
     * 带referer跳转
     * @param string $url
     * @return string
     */
    public static function build_url_with_referer($url)
    {
        return (string)url('index/plugins/redirect_url', ['url' => urlencode($url)], false, true);
    }

    /**
     * 获取绝对目录
     * @param string $file_name
     * @param string $sub_path
     * @return string
     */
    public static function get_absolute_path_disk($file_name = '', $sub_path = '')
    {
        $root_path = App::getRootPath();
        $full_path = $root_path . 'disk/';
        if ($sub_path) {
            $full_path = $full_path . $sub_path . '/';
        }
        tools()::mkdir($full_path);
        $path = $full_path . $file_name;
        return str_replace('\\', '/', $path);
    }

    /**
     * @param data 日期
     */
    public static function date_to_week($date)
    {
        //强制转换日期格式
        $date_str = date('Y-m-d', strtotime($date));

        //封装成数组
        $arr = explode("-", $date_str);

        //参数赋值
        //年
        $year = $arr[0];

        //月，输出2位整型，不够2位右对齐
        $month = sprintf('%02d', $arr[1]);

        //日，输出2位整型，不够2位右对齐
        $day = sprintf('%02d', $arr[2]);

        //时分秒默认赋值为0；
        $hour = $minute = $second = 0;

        //转换成时间戳
        $strap = mktime($hour, $minute, $second, $month, $day, $year);

        //获取数字型星期几
        $number_wk = date("w", $strap);

        //自定义星期数组
        $weekArr = array("周日", "周一", "周二", "周三", "周四", "周五", "周六");

        //获取数字对应的星期
        return $weekArr[$number_wk];
    }

    public static function absolute_path_disk_to_relative_path($path)
    {
        return root_path('disk');
    }

    public static function get_pathinfo_download_path($file)
    {
        if (strpos($file, str_replace('\\', '/', root_path())) === false) {
            $file = public_path() . ltrim($file, '/');
        }
        $full_path = str_replace('\\', '/', $file);
        return pathinfo($full_path);
    }

    /**
     * 获取绝对目录
     * @param string $path
     * @return string
     */
    public static function get_absolute_path($path)
    {
        $path = public_path() . ltrim($path, '/');
        return str_replace('\\', '/', $path);
    }

    /**
     * @param string $path
     * @return bool
     */
    public static function mkdir($path)
    {
        if (!is_dir($path) && $path) {
            return mkdir($path, 0755, true);
        }
        return true;
    }

    public static function get_sub_dirs($dir)
    {
        $sub_dirs = [];
        if (!$dh = opendir($dir))
            return $sub_dirs;
        $i = 0;
        while ($f = readdir($dh)) {
            if ($f == '.' || $f == '..') {
                continue;
            }
            //如果只要子目录名, path = $f;
            //$path = $dir.'/'.$f;
            $path         = $f;
            $sub_dirs[$i] = $path;
            $i++;
        }
        return $sub_dirs;
    }

    public static function download($path, $name, $message)
    {
        if (!is_dir($path) && $path) {
            mkdir($path, 0755, true);
        }
        $fp = fopen($path . $name, "w");
        fwrite($fp, $message);
        return fclose($fp);
    }

    /**
     * @param string $path
     * @return string
     */
    public static function path_to_web($path)
    {
        $request = request();
        $domain  = $request->domain();
        return $domain . '/' . ltrim($path, '/');
    }

    /**
     * @param string $url
     * @return string
     */
    public static function web_to_path($url)
    {
        $request = request();
        $host    = $request->host();
        return str_replace([$host, 'http://', 'https://'], '', $url);
    }

    /**
     * @param String $endTime
     * @return String time
     * @example
     * $endTime = '2014-07-13 8:15:00';
     * echo countDown($endTime);
     */
    public static function countDown($endTime)
    {
        $endTime        = strtotime($endTime);
        $beiginTime     = strtotime(date('Y-m-d H:i:s'));
        $timeDifference = $endTime - $beiginTime;
        switch ($timeDifference) {
            case $timeDifference < 0 :
                $timeDifference = '已经结束！';
                break;
            case $timeDifference < 60 :
                $timeDifference = $timeDifference . '秒';
                break;
            case $timeDifference < 3600 :
                $minutes        = floor($timeDifference / 60);
                $seconds        = floor($timeDifference - ($minutes * 60));
                $timeDifference = $minutes . '分' . $seconds . '秒';
                break;
            case $timeDifference < 86400 :
                $hours          = floor($timeDifference / 3600);
                $minutes        = floor(($timeDifference - ($hours * 3600)) / 60);
                $seconds        = floor($timeDifference - ($hours * 3600) - ($minutes * 60));
                $timeDifference = $hours . '小时' . $minutes . '分' . $seconds . '秒';
                break;
            default:
                $days           = floor(($timeDifference / 86400));
                $hours          = floor(($timeDifference - ($days * 86400)) / 3600);
                $minutes        = floor(($timeDifference - ($days * 86400) - ($hours * 3600)) / 60);
                $seconds        = floor($timeDifference - ($days * 86400) - ($hours * 3600) - ($minutes * 60));
                $timeDifference = $days . '天' . $hours . '小时' . $minutes . '分' . $seconds . '秒';
                break;
        }
        return $timeDifference;
    }

    /**
     * 把jsonp转为php数组
     * @param string $jsonp jsonp字符串
     * @param boolean $assoc 当该参数为true时，将返回array而非object
     * @return array|object
     */
    public static function jsonp_decode($jsonp, $assoc = true)
    {
        $jsonp = trim($jsonp);
        if (isset($jsonp[0]) && $jsonp[0] !== '[' && $jsonp[0] !== '{') {
            $begin = strpos($jsonp, '(');
            if (false !== $begin) {
                $end = strrpos($jsonp, ')');
                if (false !== $end) {
                    $jsonp = substr($jsonp, $begin + 1, $end - $begin - 1);
                }
            }
        }
        return json_decode($jsonp, $assoc);
    }

    /**
     * 生成内容签名
     * @param array $data
     * @param string $key
     * @return string
     * @throws Exception
     */
    public static function get_sign($data, $key)
    {
        if (is_null($key)) {
            throw new Exception('getSign Failed Missing Parameter -- [key]');
        }
        ksort($data);
        $string = md5(self::get_sign_content($data) . '&key=' . $key);
        return strtoupper($string);
    }

    /**
     * 生成签名内容
     * @param $data
     * @return string
     */
    public static function get_sign_content($data)
    {
        $buff = '';
        foreach ($data as $k => $v) {
            $buff .= ($k != 'sign' && $k != 'signature' && $v !== '' && !is_array($v)) ? $k . '=' . $v . '&' : '';
        }
        return trim($buff, '&');
    }

    /**
     * 生成hash密码
     * @param string $password
     * @param string|mixed $cost
     * @return string
     * @throws Exception
     */
    public static function generatePasswordHash($password, $cost = null)
    {
        if ($cost === null) {
            $cost = 13;
        }
        return password_hash($password, PASSWORD_DEFAULT, ['cost' => $cost]);
    }

    /**
     * 验证hash密码
     * @param string $password
     * @param string $hash
     * @return bool
     * @throws Exception
     */
    public static function validate_password_hash($password, $hash)
    {
        if (!is_string($password) || $password === '') {
            return false;
        }
        if (!preg_match('/^\$2[axy]\$(\d\d)\$[\.\/0-9A-Za-z]{22}/', $hash, $matches)
            || $matches[1] < 4
            || $matches[1] > 30
        ) {
            return false;
        }
        return password_verify($password, $hash);
    }

    /**
     * 验证月-日
     * @param string $date_str
     * @return bool
     * @throws Exception
     */
    public static function validate_month_day($date_str)
    {
        // 正则规则
        $pattern = '/^(0?[1-9]|1[0-2])-(0?[1-9]|[12]\d|3[01])$/';
        return preg_match($pattern, $date_str);
    }

    /**
     * @param $lat1
     * @param $lng1
     * @param $lat2
     * @param $lng2
     * @param float $radius 星球半径
     * @return int
     */
    public static function get_distance($lat1, $lng1, $lat2, $lng2, $radius = 6378.137)
    {
        //将角度转为狐度
        $radLat1 = deg2rad($lat1);//deg2rad()函数将角度转换为弧度
        $radLat2 = deg2rad($lat2);
        $radLng1 = deg2rad($lng1);
        $radLng2 = deg2rad($lng2);
        $a       = $radLat1 - $radLat2;
        $b       = $radLng1 - $radLng2;
        $s       = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2))) * $radius;
        return $s;
    }

    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @param float $radius 星球半径
     * @return float 距离千米
     */
    public static function distance($lat1, $lon1, $lat2, $lon2, $radius = 6378.137)
    {
        $rad   = floatval(M_PI / 180.0);
        $lat1  = floatval($lat1) * $rad;
        $lon1  = floatval($lon1) * $rad;
        $lat2  = floatval($lat2) * $rad;
        $lon2  = floatval($lon2) * $rad;
        $theta = $lon2 - $lon1;
        $dist  = acos(sin($lat1) * sin($lat2) + cos($lat1) * cos($lat2) * cos($theta));
        if ($dist < 0) {
            $dist += M_PI;
        }
        return $dist = $dist * $radius;
    }

    /**
     * @desc 仅仅获取这个类的方法，不要父类的
     * @param string $class 类名
     * @return array 本类的所有方法构成的一个数组
     */
    public static function get_this_class_methods($class)
    {
        $array1 = get_class_methods($class);
        if ($parent_class = get_parent_class($class)) {
            $array2 = get_class_methods($parent_class);
            $array3 = array_diff($array1, $array2);
        } else {
            $array3 = $array1;
        }
        return $array3;
    }

    /**
     * RSA数据加密解密
     * @param string $data
     * @param string $public_key
     * @return string
     * @throws Exception
     */
    public static function rsa_encode($data, $public_key)
    {
        if (empty($data)) {
            throw new Exception('data参数不能为空');
        }
        //公钥加密
        $public_key = openssl_pkey_get_public($public_key);
        if (!$public_key) {
            throw new Exception('公钥不可用');
        }
        $return_en = openssl_public_encrypt($data, $crypted, $public_key);
        if (!$return_en) {
            throw new Exception('加密失败,请检查RSA秘钥');
        }
        return base64_encode($crypted);
    }

    /**
     * RSA数据加密解密
     * @param string $data
     * @param string $private_key
     * @return string
     * @throws Exception
     */
    public static function rsa_decode($data, $private_key)
    {
        if (empty($data)) {
            throw new Exception('data参数不能为空');
        }
        //私钥解密
        $private_key = openssl_pkey_get_private($private_key);
        if (!$private_key) {
            throw new Exception('私钥不可用');
        }
        $return_de = openssl_private_decrypt(base64_decode($data), $decrypted, $private_key);
        if (!$return_de) {
            throw new Exception('解密失败,请检查RSA秘钥');
        }
        return $decrypted;
    }

    /**
     * COOKIES转换成ARRAY数组
     * @param string|array $array
     * @return array
     */
    public static function remove_key_by_value($array, $value)
    {
        foreach ($array as $key => $val) {
            if ($val == $value) {
                unset($array[$key]);
            }
        }
        return $array;
    }

    /*
     <AUTHOR> <<EMAIL>> v1.01
     @Param string $str
     @Param int $start 起始位置 从0开始计数  负数倒转替换
     @Param (int or string) $length 当 $length=string 替换 $key
     @Param (int or string) $key 填充的隐藏的字符 默认 *
     @Param string $charset 可废弃 删除 ($key && $charset = $key) 和 ($charset='utf8') 语句;

     @Param int $split 可拓展
     $split_card = implode(' ',str_split($str_hide,4));  // 分割卡号
     */
    public static function str_hide($str, $start, $length = 0, $key = '', $charset = 'utf8')
    {
        // 使用支持补0，当 $length=string 替换 $key
        if (strlen($length) && gettype($length) != "integer") {
            $key && $charset = $key;
            $key    = $length;
            $length = 0;
        }
        $Par = $length ? [$start, $length] : [$start]; //array_filter([$start,$length]);
        // use $charset;
        //$e or $e = mb_strlen($str);
        //$Par     = [$start,$length,$charset];
        $rep_str = mb_substr($str, ...$Par);
        strlen($key) or $key = '*';
        strlen($key) == 1 && $key = str_pad('', mb_strlen($rep_str), $key);
        $start = strlen(mb_substr($str, 0, $start));
        $count = strlen($rep_str);
        return substr_replace($str, $key, $start, $count);
    }


    //将用户名进行处理，中间用星号表示
    public static function substr_cut_user_name($user_name)
    {
        //获取字符串长度
        $strlen = mb_strlen($user_name, 'utf-8');
        //如果字符创长度小于2，不做任何处理
        if ($strlen < 2) {
            return $user_name;
        } else {
            //mb_substr — 获取字符串的部分
            $firstStr = mb_substr($user_name, 0, 1, 'utf-8');
            $lastStr  = mb_substr($user_name, -1, 1, 'utf-8');
            //str_repeat — 重复一个字符串
            return $strlen == 2 ? $firstStr . str_repeat('*', mb_strlen($user_name, 'utf-8') - 1) : $firstStr . str_repeat("*", $strlen - 2) . $lastStr;
        }
    }


    /**
     * COOKIES转换成ARRAY数组
     * @param string|array $cookies
     * @return array
     */
    public static function cookies_to_array($cookies)
    {
        $cookies_array = [];
        $cookies       = is_array($cookies) ? implode(';', $cookies) : $cookies;
        $array         = explode(';', $cookies);
        foreach ($array as $key => $val) {
            $str = explode('=', $val);
            if (isset($str[0]) && isset($str[1])) {
                $cookies_array[trim($str[0])] = trim($str[1]);
            }
        }
        return $cookies_array;
    }

    /**
     * 通过字符获取临时文件路径
     * @param string $content
     * @return string
     */
    public static function get_tmp_path_by_content($content)
    {
        static $tmpFile = null;
        $tmpFile = tmpfile();
        fwrite($tmpFile, $content);
        $tempPemPath = stream_get_meta_data($tmpFile);
        return $tempPemPath['uri'];
    }

    /**
     * 通过描述转换成时间文本
     * @param integer $time 秒数
     * @return string
     */
    public static function sec2time(int $time)
    {
        $text  = '';
        $value = [
            "years"   => 0,
            "days"    => 0,
            "hours"   => 0,
            "minutes" => 0,
            "seconds" => 0,
        ];
        if ($time >= 31556926) {
            $value["years"] = floor($time / 31556926);
            $time           = ($time % 31556926);
        }
        if ($time >= 86400) {
            $value["days"] = floor($time / 86400);
            $time          = ($time % 86400);
        }
        if ($time >= 3600) {
            $value["hours"] = floor($time / 3600);
            $time           = ($time % 3600);
        }
        if ($time >= 60) {
            $value["minutes"] = floor($time / 60);
            $time             = ($time % 60);
        }
        $value["seconds"] = floor($time);
        //return (array) $value;
        $value["years"] && $text .= $value["years"] . "年";
        $value["days"] && $text .= $value["days"] . "天";
        $value["hours"] && $text .= $value["hours"] . "小时";
        $value["minutes"] && $text .= $value["minutes"] . "分";
        $value["seconds"] && $text .= $value["seconds"] . "秒";
        return $text;
    }

    /**
     *  Finds next execution time(stamp) parsin crontab syntax,
     *  after given starting timestamp (or current time if ommited)
     *
     * @param string $_cron_string :
     *
     * 0     1     2    3    4    5
     * *     *     *    *    *    *
     * -     -     -    -    -    -
     * |     |     |    |    |    |
     * |     |     |    |    |    +----- day of week (0 - 6) (Sunday=0)
     * |     |     |    |    +------- month (1 - 12)
     * |     |     |    +--------- day of month (1 - 31)
     * |     |     +----------- hour (0 - 23)
     * |     +------------- min (0 - 59)
     * +-------------- sec (0 - 59)
     * @param int $_after_timestamp timestamp [default=current timestamp]
     * @return int unix timestamp - next execution time will be greater
     *              than given timestamp (defaults to the current timestamp)
     * @throws Exception
     */
    public static function parse_crontab(string $_cron_string, int $_after_timestamp = null)
    {
        $_cron_string = trim($_cron_string);
        //       $_cron_string = trim(rtrim($_cron_string, '?'));
        $reg = '/^((\*(\/[0-9]+)?)|[0-9\-\,\/]+)\s+((\*(\/[0-9]+)?)|[0-9\-\,\/]+)\s+((\*(\/[0-9]+)?)|[0-9\-\,\/]+)\s+((\*(\/[0-9]+)?)|[0-9\-\,\/,\?]+)\s+((\*(\/[0-9]+)?)|[0-9\-\,\/]+)\s+((\*(\/[0-9]+)?)|[0-9\-\,\/,\?]+)$/i';
        if (!preg_match($reg, $_cron_string)) {
            throw new Exception("Invalid cron string: " . $_cron_string);
        }
        if ($_after_timestamp && !is_numeric($_after_timestamp)) {
            throw new Exception("\$_after_timestamp must be a valid unix timestamp ($_after_timestamp given)");
        }
        $cron  = preg_split("/[\s]+/i", $_cron_string);
        $start = $_after_timestamp ?: time();
        $date  = [
            'seconds' => self::_parse_cron_numbers($cron[0], 0, 59),
            'minutes' => self::_parse_cron_numbers($cron[1], 0, 59),
            'hours'   => self::_parse_cron_numbers($cron[2], 0, 23),
            'dom'     => self::_parse_cron_numbers($cron[3], 1, 31),
            'month'   => self::_parse_cron_numbers($cron[4], 1, 12),
            'dow'     => self::_parse_cron_numbers($cron[5], 0, 6),
        ];
        // limited to time()+366 - no need to check more than 1year ahead
        for ($i = 0; $i <= 60 * 60 * 24 * 366; $i += 60) {
            if (in_array(intval(date('j', $start + $i)), $date['dom']) &&
                in_array(intval(date('n', $start + $i)), $date['month']) &&
                in_array(intval(date('w', $start + $i)), $date['dow']) &&
                in_array(intval(date('G', $start + $i)), $date['hours']) &&
                in_array(intval(date('i', $start + $i)), $date['minutes'])
                // in_array(intval(date('s', $start + $i)), $date['seconds']) // 暂时不需要秒数
            ) {
                return $start + $i;
            }
        }
        return null;
    }

    /**
     * get a single cron style notation and parse it into numeric value
     *
     * @param string $s cron string element
     * @param int $min minimum possible value
     * @param int $max maximum possible value
     * @return array parsed number
     */
    protected static function _parse_cron_numbers(string $s, int $min, int $max)
    {
        $result = [];
        $v      = explode(',', $s);
        foreach ($v as $vv) {
            $vvv  = explode('/', $vv);
            $step = empty($vvv[1]) ? 1 : $vvv[1];
            $vvvv = explode('-', $vvv[0]);
            $_min = count($vvvv) == 2 ? $vvvv[0] : (in_array($vvv[0], ['*', '?']) ? $min : $vvv[0]);
            $_max = count($vvvv) == 2 ? $vvvv[1] : (in_array($vvv[0], ['*', '?']) || count($vvv) == 2 ? $max : $vvv[0]);
            for ($i = $_min; $i <= $_max; $i += $step) {
                $result[$i] = intval($i);
            }
        }
        ksort($result);
        return $result;
    }

    /**
     * 金额转中文大写
     *
     * @param mixed $amount
     * @return string
     */
    public static function rmb_capital($amount)
    {
        $capitalNumbers = [
            '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖',
        ];

        $integerUnits = ['', '拾', '佰', '仟',];

        $placeUnits = ['', '万', '亿', '兆',];

        $decimalUnits = ['角', '分', '厘', '毫',];

        $result = [];

        $arr = explode('.', $amount);

        $integer = trim($arr[0] ?? '', '-');
        $decimal = $arr[1] ?? '';

        if (!((int)$decimal)) {
            $decimal = '';
        }

        // 转换整数部分
        // 从个位开始

        $integerNumbers = $integer ? array_reverse(str_split($integer)) : [];

        $last = null;
        foreach (array_chunk($integerNumbers, 4) as $chunkKey => $chunk) {
            if (!((int)implode('', $chunk))) {
                // 全是 0 则直接跳过
                continue;
            }

            array_unshift($result, $placeUnits[$chunkKey]);

            foreach ($chunk as $key => $number) {
                // 去除重复 零，以及第一位的 零，类似：1002、110
                if (!$number && (!$last || $key === 0)) {
                    $last = $number;
                    continue;
                }
                $last = $number;

                // 类似 1022，中间的 0 是不需要 佰 的
                if ($number) {
                    array_unshift($result, $integerUnits[$key]);
                }

                array_unshift($result, $capitalNumbers[$number]);
            }
        }

        if (!$result) {
            array_push($result, $capitalNumbers[0]);
        }

        array_push($result, '圆');

        if (!$decimal) {
            array_push($result, '整');
        }

        // 转换小数位
        $decimalNumbers = $decimal ? str_split($decimal) : [];
        foreach ($decimalNumbers as $key => $number) {
            array_push($result, $capitalNumbers[$number]);
            array_push($result, $decimalUnits[$key]);
        }

        if (strpos((string)$amount, '-') === 0) {
            array_unshift($result, '负');
        }
        return implode('', $result);
    }

    public static function replace_url_parameters($url, $keys)
    {
        $keys    = is_array($keys) ? $keys : [$keys];
        $pattern = '/(' . implode('|', array_map('preg_quote', $keys)) . ')=([^&]+)/';
        return preg_replace($pattern, '${1}={$${1}}', $url);
    }

    /**
     * @param string $string 关键词
     * @param string|array $array_or_explode_string 数组或者带分隔符的字符串
     * @param string $delimiter 分隔符
     * @return bool
     */
    public static function match_keyword_in_array(string $string, $array_or_explode_string, $delimiter = ',')
    {
        $array = is_array($array_or_explode_string) ? $array_or_explode_string : explode($delimiter, $array_or_explode_string);
        foreach ($array as $keyword) {
            if (strpos($string, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $string
     * @return bool
     */

    public static function is_md5_string($string)
    {
        return (bool)preg_match("/^[a-z0-9]{32}$/", $string);
    }

    /**
     * @param $string
     * @return string
     */
    public static function password($string)
    {
        return md5($string);
    }

    /**
     *风控验证
     * @access public
     * @param mixed $key 键值
     * @param integer $expire 频率
     * @param bool $check_ip 是否验证IP
     * @return bool
     */
    public static function verify_throttle_inc($key, int $expire = 300, $check_ip = true): bool
    {
        $cache_prefix     = 'verify:';
        $cache_key        = $cache_prefix . $key;
        $value_black_time = (int)cache($cache_key);
        cache($cache_key, $value_black_time + 1, $expire);
        if ($check_ip) {
            $ip_key        = $cache_prefix . 'ip:' . tools()::get_client_ip();
            $ip_black_time = (int)cache($ip_key);
            cache($ip_key, $ip_black_time + 1, $expire);
        }
        return true;
    }

    /**
     *风控验证
     * @access public
     * @param mixed $key 键值
     * @param integer $max 最大次数
     * @param bool $check_ip 是否验证IP
     * @return bool
     */
    public static function verify_throttle_check($key, int $max = 3, $check_ip = true): bool
    {
        $cache_prefix     = 'verify:';
        $cache_key        = $cache_prefix . $key;
        $value_black_time = (int)cache($cache_key);
        $ip_result        = false; //默认是校验通过
        if ($check_ip) {
            //仅当需要校验IP的时候 $ip_result 判断看IP黑名单次数是否有超过最大次数
            $ip_key        = $cache_prefix . 'ip:' . tools()::get_client_ip();
            $ip_black_time = (int)cache($ip_key);
            $ip_result     = $ip_black_time >= $max;
        }
        return $value_black_time >= $max || $ip_result;
    }

    /**
     *追加合计行
     * @access public
     * @param array $result 数据
     * @return  array $result
     */
    public static function array_push_total($result)
    {
        foreach ($result as $key => $val) {
            foreach ($val as $k => $v) {
                if (is_null($v)) {
                    $result[$key][$k] = 0;
                }
            }
        }
        $total_array = [];
        foreach (current($result) as $key => $val) {
            if (filter_var($val, FILTER_VALIDATE_FLOAT) !== false) {
                $total = 0;
                foreach ($result as $k => $v) {
                    $total += $v[$key];
                }
                $total_array[] = $total;
            } else {
                $total_array[] = '合计';
            }
        }
        array_push($result, $total_array);
        return $result;
    }

    public static function get_names_and_values($body)
    {
        preg_match_all("/name=\"([^>]*)/i", $body, $arr);
        $val_arr = [];
        foreach ($arr[0] as $key => $val) {
            preg_match("/name=\"([^\"]*)/i", $val, $names);
            preg_match("/value=\"([^\"]*)/i", $val, $values);
            if (isset($names[1])) {
                $input_value        = isset($values[1]) ? $values[1] : '';
                $val_arr[$names[1]] = str_replace(' ', '+', $input_value);
            }
        }
        return $val_arr;
    }

    public static function format_number($num, $count_after_dot = 2)
    {
        $count_after_dot = (int)$count_after_dot;
        $pow             = pow(10, $count_after_dot);
        $tmp             = $num * $pow;
        $tmp             = floor($tmp) / $pow;
        $format          = sprintf('%%.%df', (int)$count_after_dot);
        return sprintf($format, (float)$tmp);
    }

    /**
     *获取$array1中不等于$array2的key
     * @access public
     * @param array $array1 数组1
     * @param array $array2 数组2
     * @return  array $result
     */
    public static function array_diff_assoc2_deep(array $array1, array $array2)
    {
        $ret = [];
        foreach ($array1 as $k => $v) {
            if (!array_key_exists($k, $array2)) {
                $ret[$k] = $v;
            } else if (is_array($v) && is_array($array2[$k])) {
                $value = self:: array_diff_assoc2_deep($v, $array2[$k]);
                if ($value !== []) {
                    $ret[$k] = $value;
                }
            } else if ((string)$v !== (string)$array2[$k]) {
                $ret[$k] = $v;
            }
        }
        return $ret;
    }

    /**
     *判断是否时间格式
     * @access public
     * @param string $time 字符串
     * @return  boolean $result
     */
    public static function is_time($time)
    {
        $preg = '/^([12]\d\d\d)-(0?[1-9]|1[0-2])-(0?[1-9]|[12]\d|3[0-1]) ([0-1]\d|2[0-4]):([0-5]\d)(:[0-5]\d)?$/';
        return preg_match($preg, $time);
    }

    public static function calculate_distance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371000; // 地球半径，单位为米
        // 将经纬度转换为弧度
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);
        // 计算经纬度差
        $latDelta = $lat2 - $lat1;
        $lonDelta = $lon2 - $lon1;
        // 应用哈弗辛公式
        $a = sin($latDelta / 2) * sin($latDelta / 2) +
            cos($lat1) * cos($lat2) *
            sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        // 计算距离
        return $earthRadius * $c;
    }

    /**
     *异常转换成字符串
     * @access public
     * @param Throwable $exception 异常
     * @param boolean $trace 是否记录异常详细信息
     * @param boolean $return_file 是否返回文件路径
     * @return  string
     */
    public static function exception_to_string(Throwable $exception, $trace = false, $return_file = true)
    {
        $data = [
            'code'    => $exception->getCode(),
            'message' => $exception->getMessage(),
        ];
        $return_file && $data['file'] = str_replace(root_path(), '', $exception->getFile());
        $return_file && $data['line'] = $exception->getLine();
        if ($trace) {
            $data['trace'] = $exception->getTraceAsString();
        }
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }

    protected function __clone()
    {
        //disallow clone
    }
}

if (!function_exists('tools')) {
    /**
     * @return ToolsService|null
     */
    function tools()
    {
        return ToolsService::get_instance();
    }
}