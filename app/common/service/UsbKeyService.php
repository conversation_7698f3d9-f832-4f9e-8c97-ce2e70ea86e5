<?php

namespace app\common\service;

class UsbKeyService
{

    function str_dec($InString, $Key) //使用增强算法，解密字符串
    {
        $b[]       = 0;
        $outb[]    = 0;
        $temp[]    = 0;
        $outtemp[] = 0;
        $n         = 0;
        $nlen      = 0;
        $result    = $this->hex_string_to_byte_array($InString, $b);
        $nlen      = $result['len'];
        $b         = $result['b'];
        for ($n = 0; $n < $nlen; $n++) {
            $outb[$n] = $b[$n];
        }
        for ($n = 0; $n <= ($nlen - 8); $n = $n + 8) {
            for ($m = 0; $m < 8; $m++) {
                $temp[$m] = $b[$n + $m];
            }
            $outtemp = $this->decode($temp, $outtemp, $Key);
            for ($m = 0; $m < 8; $m++) {
                $outb[$n + $m] = $outtemp[$m];
            }
        }
        $outstring = "";
        for ($n = 0; $n <= $nlen - 1; $n = $n + 1) {
            if (($outb[$n] <= 127) && ($outb[$n] >= 0)) {
                $outstring = $outstring . chr($outb[$n]);
            } else {
                $temp      = ($outb[$n] << 8) | $outb[$n + 1];
                $outstring = $outstring . chr($temp);
                $n         = $n + 1;
            }
        }
        return $outstring;
    }

    function hex_string_to_byte_array($InString, $b)
    {
        $g_len = 0;
        $nlen  = 0;
        $n     = 0;
        $i     = 0;
        $temp  = "";
        $nlen  = strlen($InString);
        if ($nlen < 16) $g_len = 16;
        $g_len = $nlen / 2;
        for ($n = 0; $n < $nlen; $n = $n + 2) {
            $temp  = substr($InString, $n, 2);
            $temp  = "0x" . $temp;
            $b[$i] = hexdec($temp);
            $i     = $i + 1;
        }
        return ['len' => $g_len, 'b' => $b];
    }

    function decode($b, $outb, $Key) //增强算法--解密
    {
        $keybuf[] = 0;
        $v[]      = 0;
        $k[]      = 0;
        $k        = $this->hex_string_to_long_array($Key, $k);
        $v        = $this->byte_to_long($v, $b);
        $v        = $this->sub_decode($v, $k);
        return $this->long_to_byte($v, $outb);
    }

    function hex_string_to_long_array($Key, $k)
    {
        $nlen  = 0;
        $n     = 0;
        $temp  = "";
        $buf[] = 0;
        $nlen  = strlen($Key);
        $i     = 0;
        for ($n = 0; $n < $nlen; $n = $n + 2) {
            $temp    = substr($Key, $n, 2);
            $buf[$i] = hexdec(("0x" . $temp));
            $i       = $i + 1;
        }
        for ($n = 0; $n <= 3; $n++) {
            $k[$n] = 0;
        }
        for ($n = 0; $n <= 3; $n++) {
            $k[0] = ($buf[$n] << ($n * 8)) | $k[0];
            $k[1] = ($buf[$n + 4] << ($n * 8)) | $k[1];
            $k[2] = ($buf[$n + 4 + 4] << ($n * 8)) | $k[2];
            $k[3] = ($buf[$n + 4 + 4 + 4] << ($n * 8)) | $k[3];
        }
        return $k;
    }

    function byte_to_long($v, $b)
    {
        $n    = 0;
        $v[0] = 0;
        $v[1] = 0;
        for ($n = 0; $n <= 3; $n++) {
            $v[0] = ($b[$n] << ($n * 8)) | $v[0];
            $v[1] = ($b[$n + 4] << ($n * 8)) | $v[1];
        }
        return $v;
    }

    function sub_decode($v, $k) //增强算法--解密
    {
        $Y         = 0;
        $Z         = 0;
        $K1        = 0;
        $K2        = 0;
        $K3        = 0;
        $K4        = 0;
        $L1        = 0;
        $L2        = 0;
        $L3        = 0;
        $L4        = 0;
        $Sum       = 0;
        $i         = 0;
        $Rounds    = 0;
        $mResult[] = array(0, 0);

        $Y  = $v[0];
        $Z  = $v[1];
        $K1 = $k[0];
        $K2 = $k[1];
        $K3 = $k[2];
        $K4 = $k[3];


        $Rounds = 32;
        $Sum    = $this->left_rotate_long(-1640531527, 5);

        for ($i = 1; $i <= $Rounds; $i++) {

            $L1 = $this->left_rotate_long($Y, 4);
            $L1 = $this->add_long($L1, $K3);
            $L2 = $this->add_long($Y, $Sum);
            $L3 = $this->right_rotate_long($Y, 5);
            $L3 = $this->add_long($L3, $K4);
            $L4 = $L1 ^ $L2 ^ $L3;
            $Z  = $this->subtract_long($Z, $L4);

            $L1 = $this->left_rotate_long($Z, 4);
            $L1 = $this->add_long($L1, $K1);
            $L2 = $this->add_long($Z, $Sum);
            $L3 = $this->right_rotate_long($Z, 5);
            $L3 = $this->add_long($L3, $K2);
            $L4 = $L1 ^ $L2 ^ $L3;
            $Y  = $this->subtract_long($Y, $L4);

            $Sum = $this->subtract_long($Sum, -1640531527);

        }

        $v[0] = $Y;
        $v[1] = $Z;
        return $v;
    }

    function left_rotate_long($lValue, $lBits) //按位左移函数
    {
        $lngSign = 0;
        $intI    = 0;
        $mValue  = 0;

        $lBits  = $lBits % 32;
        $mValue = $lValue;
        if ($lBits == 0) return $mValue;

        for ($intI = 1; $intI <= $lBits; $intI++) {
            $lngSign = $mValue & 0x40000000;
            $mValue  = ($mValue & 0x3FFFFFFF) * 2;

            if ($lngSign & 0x40000000)
                $mValue = $mValue | 0x80000000;
        }

        return $mValue;
    }

    function add_long($lX, $lY)
    {
        $lX4     = 0;
        $lY4     = 0;
        $lX8     = 0;
        $lY8     = 0;
        $lResult = 0;

        $lX8 = $lX & 0x80000000;
        $lY8 = $lY & 0x80000000;
        $lX4 = $lX & 0x40000000;
        $lY4 = $lY & 0x40000000;

        $lResult = ($lX & 0x3FFFFFFF) + ($lY & 0x3FFFFFFF);

        if ($lX4 & $lY4) {
            $lResult = $lResult ^ 0x80000000 ^ $lX8 ^ $lY8;
        } elseif ($lX4 | $lY4) {
            $temp = $lResult & 0x40000000;
            if ($lResult & 0x40000000)
                $lResult = $lResult ^ 0xC0000000 ^ $lX8 ^ $lY8;
            else
                $lResult = $lResult ^ 0x40000000 ^ $lX8 ^ $lY8;
        } else {
            $lResult = $lResult ^ $lX8 ^ $lY8;
        }

        return $lResult;
    }

    function right_rotate_long($lValue, $lBits) //按位右移函数
    {
        $lngSign = 0;
        $intI    = 0;
        $mValue  = 0;

        $mValue = $lValue;
        $lBits  = $lBits % 32;

        if ($lBits == 0) return $mValue;

        for ($intI = 1; $intI <= $lBits; $intI++) {
            $lngSign = $mValue & 0x80000000;
            $mValue  = ($mValue & 0x7FFFFFFF) / 2;
            if ($lngSign)
                $mValue = $mValue | 0x40000000;
        }
        return $mValue;
    }

    function subtract_long($lX, $lY) //长整数减法函数
    {
        $lX8     = 0;
        $lY8     = 0;
        $mX      = 0.00;
        $mY      = 0.00;
        $mResult = 0.00;
        $lResult = 0;

        $lX8 = $lX & 0x80000000;
        $lY8 = $lY & 0x80000000;

        $mX = $lX & 0x7FFFFFFF;
        $mY = $lY & 0x7FFFFFFF;

        if ($lX8) {
            if ($lY8)
                $mResult = $mX - $mY;
            else {
                $mX      = $mX + 2147483648;
                $mResult = $mX - $mY;
            }
        } else {
            if ($lY8) {
                $mY      = $lY;
                $mResult = $mX - $mY;
            } else
                $mResult = $mX - $mY;
        }


        if ($mResult < 0)
            $lResult = ((2147483648 + $mResult) | 0x80000000) & 0xFFFFFFFF;
        elseif ($mResult > 0x7fffffff)
            $lResult = (($mResult - 2147483648) | 0x80000000) & 0xFFFFFFFF;
        else
            $lResult = $mResult & 0xFFFFFFFF;

        return $lResult;

    }

    function long_to_byte($v, $b)
    {
        $n    = 0;
        $temp = 0;
        for ($n = 0; $n <= 3; $n++) {
            $b[$n]     = ($v[0] >> ($n * 8)) & 255;
            $b[$n + 4] = ($v[1] >> ($n * 8)) & 255;
        }
        return $b;
    }

    function str_enc($InString, $Key) //使用增强算法，加密字符串
    {
        $b[]       = 0;
        $outb[]    = 0;
        $temp[]    = 0;
        $outtemp[] = 0;
        $n         = 0;
        $nlen      = 0;
        $temp_len  = strlen($InString) + 1;

        if ($temp_len < 8) $temp_len = 8;

        for ($n = 0; $n < $temp_len; $n++) {
            $temp_string = substr($InString, $n, 1);
            $b[$nlen]    = ord($temp_string);
            $nlen++;
        }
        for ($n = 0; $n < $temp_len; $n++) {
            $outb[$n] = $b[$n];
        }

        for ($n = 0; $n <= ($temp_len - 8); $n = $n + 8) {
            for ($m = 0; $m < 8; $m++) {
                $temp[$m] = $b[$n + $m];
            }
            $outtemp = $this->encode($temp, $outtemp, $Key);
            for ($m = 0; $m < 8; $m++) {
                $outb[$n + $m] = $outtemp[$m];
            }
        }
        return $this->byte_array_to_hex_string($outb, $temp_len);
    }

    function encode($b, $outb, $Key) //增强算法--加密
    {
        $keybuf[] = 0;
        $v[]      = 0;
        $k[]      = 0;
        $k        = $this->hex_string_to_long_array($Key, $k);
        $v        = $this->byte_to_long($v, $b);
        $v        = $this->sub_encode($v, $k);
        return $this->long_to_byte($v, $outb);
    }

    function sub_encode($v, $k)
    {
        $Y  = 0;
        $Z  = 0;
        $K1 = 0;
        $K2 = 0;
        $K3 = 0;
        $K4 = 0;
        $L1 = 0;
        $L2 = 0;
        $L3 = 0;
        $L4 = 0;

        $Sum       = 0;
        $i         = 0;
        $Rounds    = 0;
        $mResult[] = array(0, 0);

        $Y  = $v[0];
        $Z  = $v[1];
        $K1 = $k[0];
        $K2 = $k[1];
        $K3 = $k[2];
        $K4 = $k[3];


        $Rounds = 32;

        for ($i = 1; $i <= $Rounds; $i++) {
            //sum += delta ;
            $Sum = $this->add_long($Sum, -1640531527);
            //y += (z<<4)+k[0] ^ z+sum ^ (z>>5)+k[1]
            $L1 = $this->left_rotate_long($Z, 4);
            $L1 = $this->add_long($L1, $K1);
            $L2 = $this->add_long($Z, $Sum);
            $L3 = $this->right_rotate_long($Z, 5);
            $L3 = $this->add_long($L3, $K2);
            $L4 = $L1 ^ $L2;
            $L4 = $L4 ^ $L3;
            $Y  = $this->add_long($Y, $L4);
            $L1 = $this->left_rotate_long($Y, 4);
            $L1 = $this->add_long($L1, $K3);
            $L2 = $this->add_long($Y, $Sum);
            $L3 = $this->right_rotate_long($Y, 5);
            $L3 = $this->add_long($L3, $K4);
            $L4 = $L1 ^ $L2 ^ $L3;
            $Z  = $this->add_long($Z, $L4);
        }

        $v[0] = $Y;
        $v[1] = $Z;
        return $v;
    }

    function byte_array_to_hex_string($b, $nlen)
    {
        $outstring = "";
        $n         = 0;
        $temp      = 0;
        for ($n = 0; $n < $nlen; $n++) {
            $outstring = $outstring . $this->myhex($b[$n]);
        }
        return $outstring;
    }

    function myhex($indata)
    {
        $temp_1 = $indata / 16;
        if ($temp_1 < 10)
            $temp_1 = $temp_1 + 0x30;
        else
            $temp_1 = $temp_1 + 0x41 - 10;

        $temp_2 = $indata % 16;
        if ($temp_2 < 10)
            $temp_2 = $temp_2 + 0x30;
        else
            $temp_2 = $temp_2 + 0x41 - 10;

        return chr($temp_1) . chr($temp_2);

    }
}
