<?php

namespace app\common\service;

use app\common\exceptions\NotNotifyException;
use app\model\Business;
use app\model\Express;
use app\model\ExpressChannel;
use app\model\ExpressConfig;
use app\model\ExpressOrder;
use app\model\ExpressQueryNote;
use app\model\GoodsOrder;
use Exception;
use xieyongfa\express\Contracts\ExpressInterface;
use xieyongfa\express\ExpressManager;

class ExpressService
{
    public static array $express_channel_info = [];
    public static int $express_channel_id;
    public static string $bid;
    protected static array|null $instance = null;
    public int $status = -1;
    public string $message = '';
    protected int $assign_express_channel_id = 0;
    protected bool $use_free_channel = false;

    protected function __construct()
    {
        //disallow new instance
    }

    protected function clear()
    {
        $this->assign_express_channel_id = 0;
        $this->use_free_channel          = true;
    }


    /**
     * 单例化对象
     * @param null|string $bid 商家
     * @return $this
     * @throws Exception
     */
    public static function get_instance($bid)
    {
        self::$bid = $bid;
        $sn        = get_instance_sn(self::$bid);
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }


    /**
     * 取消订单
     * @access public
     * @param string $order_guid 订单guid
     * @return bool
     * @throws Exception
     */
    public function cancel_order(string $order_guid)
    {
        $bid               = self::$bid;
        $map_express_order = [
            ['bid', '=', $bid],
            ['relation_guid', '=', $order_guid],
            ['status', '=', 1],
        ];
        $db_express_order  = new ExpressOrder();
        $order             = $db_express_order->where($map_express_order)->order(['update_time' => 'DESC'])->findOrEmpty();
        if ($order->isEmpty()) {
            $this->message = '当前订单不是通过电子面单下单的,不支持撤单!';
            return false;
        }
        $express_config_guid = $order['express_config_guid'];
        $order_code          = $order['order_code'];
        $express_code        = $order['shipper_code'];
        $express_no          = $order['express_no'];
        $db_express_config   = new ExpressConfig();
        $map_express_config  = [
            ['bid', '=', $bid],
            ['guid', '=', $express_config_guid],
        ];
        $express_config      = $db_express_config->where($map_express_config)->findOrEmpty();
        if ($express_config->isEmpty()) {
            $this->message = '对应面单模板已被删除,撤单失败!';
            return false;
        }
        self::$express_channel_id = $express_config['channel_id']; //赋值 express_channel_id
        $customer_name            = $express_config['customer_name'];
        $customer_pwd             = $express_config['customer_pwd'];
        $options                  = [
            'customer_name' => $customer_name,
            'customer_pwd'  => $customer_pwd,
        ];
        $express                  = self::get_express_instance(self::$express_channel_id);
        $result                   = $express->cancelOrder($express_code, $express_no, $order_code, $options);
        if ($result['status'] !== '1') {
            $this->message = $result['message'] ?? '未知错误';
            return false;
        }
        $update_data = [
            'cancel_time' => format_timestamp(),
            'status'      => -2
        ];
        $db_express_order::update($update_data, $map_express_order);
        return true;
    }

    public function set_assign_express_channel_id($assign_express_channel_id = 0)
    {
        if ($assign_express_channel_id) {
            $this->assign_express_channel_id = $assign_express_channel_id;
        }
        return $this;
    }

    protected function get_query_route_express_channel_id($express_code)
    {
        if ($this->assign_express_channel_id) {
            //当前方法指定通道优先级最高
            return $this->assign_express_channel_id;
        }
        //优先看商家级是否指定走固定的查单通道,废弃
        //        $config                                  = get_config_by_bid(self::$bid);
        //        $business_query_route_express_channel_id = $config['query_route_express_channel_id'];
        //        if ($business_query_route_express_channel_id) {
        //            return $business_query_route_express_channel_id;
        //        }
        $db_express = new Express();
        if ($this->use_free_channel === true) {
            $default_query_route_express_channel_id = get_system_config('default_free_query_route_express_channel_id');
            $map                                    = [['code', '=', $express_code]];
            $query_route_express_channel_id         = $db_express->where($map)->value('free_query_route_express_channel_id');
        } else {
            $default_query_route_express_channel_id = get_system_config('default_query_route_express_channel_id');
            $map                                    = [['code', '=', $express_code]];
            $query_route_express_channel_id         = $db_express->where($map)->value('query_route_express_channel_id');
        }
        $channel_id = $query_route_express_channel_id ?: $default_query_route_express_channel_id;
        if ($channel_id == 8 && get_system_config('cainiao_broken')) {
            $channel_id = 9; //万维
        }
        return $channel_id;
    }


    public function use_free_channel()
    {
        $this->use_free_channel = true;
        return $this;
    }

    protected function is_need_retry($express_no)
    {
        //顺丰单号不做重试
        if (preg_match("/^SF\d{13}$/", $express_no)) {
            return false;
        }
        return true;
    }

    public function get_express_code_by_express_no($express_no)
    {
        if (preg_match("/^SF\d{13}$/", $express_no)) {
            //有特殊规律的返回,不请求第三方
            return 'shunfeng';
        }
        $cache_key = 'express_code:' . $express_no;
        $code      = cache($cache_key);
        if (empty($code)) {
            $url    = 'https://m.kuaidi100.com/apicenter/kdquerytools.do?method=autoComNum&text=' . $express_no;
            $result = curl()->get($url)->get_body();
            if (empty($result['auto'])) {
                return '';
            }
            $first          = current($result['auto']);
            $first_com_code = $first['comCode'];
            $first_com_name = $first['name'];
            $db             = new Express();
            $map            = [
                ['kuaidi_yibai_code', '=', $first_com_code]
            ];
            $code           = $db->where($map)->value('code');
            cache($cache_key, $code, 3600 * 24 * 31);
        }
        return $code;
    }

    /**
     * 查询物流信息(商家私有版本)
     * @access public
     * @param array $query_data 查询数据
     * @return bool|array
     * @throws Exception
     */
    public function query_route(array $query_data)
    {
        $bid                      = $query_data['bid'] ?? null;
        $bid                      = $bid ?: self::$bid;
        $query_data['express_no'] = tools()::remove_empty_string($query_data['express_no']);
        $query_data['express_no'] = strtoupper($query_data['express_no']); //字母转大写
        $expressNo                = $query_data['express_no'];
        $mobile                   = $query_data['mobile'] ?? null;
        $orderId                  = $query_data['order_guid'] ?? null;
        $way                      = $query_data['way'] ?? 0;
        if (empty($expressNo)) {
            throw new Exception('单号不能为空');
        }
        if (preg_match("/^SF\d{13}$/", $query_data['express_no'])) {
            //符合顺丰单号自动纠正
            $query_data['express_code'] = 'shunfeng';
        }
        $expressCode = $query_data['express_code'] ?? null;

        $cache_key                      = 'express_route:' . $expressNo;
        $mobile                         = substr($mobile, -4);// 只需要后四位;
        $query_data['mobile']           = $mobile;// 只需要后四位 重新赋值
        $db_express_query_note          = new ExpressQueryNote();
        $query_route_express_channel_id = $this->get_query_route_express_channel_id($expressCode); //确认用什么通道查询
        $from_cache                     = 1;
        $result                         = cache($cache_key);  //如果存在缓存 则不查询
        if (empty($result) || is_host() || !empty($query_data['is_retry'])) {
            $from_cache = 0;
            $exp        = 600;
            if ($query_data['express_code'] == 'tongcheng') {
                //同城配送不支持快递查询
                $result = [
                    'status'                => 1,
                    'traces'                => [],
                    'message'               => '同城配送订单不支持物流查询',
                    "express_logo"          => "http://file.yikayi.net/static/img/tongcheng.png",
                    'express_status'        => 1,
                    'express_status_name'   => '暂无记录',
                    'express_service_phone' => '',
                ];
            } else {
                $express = self::get_express_instance($query_route_express_channel_id);
                $result  = $express->queryRoute($query_data);
            }
            if (isset($result['message']) && strpos($result['message'], '挤爆啦') !== false) {
                cache($cache_key, $result, $exp); //缓存10分钟
            }
        }
        $express_status = $result['express_status'] ?? -1; //查询失败
        $message        = $result['message'] ?? '未知message';

        //保存记录
        $note_data                       = [];
        $note_data['express_no']         = $expressNo;
        $note_data['express_code']       = $expressCode;
        $note_data['mobile']             = $mobile;
        $note_data['order_guid']         = $orderId;
        $note_data['bid']                = $bid;
        $note_data['way']                = $way;
        $note_data['express_channel_id'] = $query_route_express_channel_id;
        $note_data['result']             = $result ?: [];
        $note_data['from_cache']         = $from_cache;
        $note_data['express_status']     = $express_status;
        $note_data['message']            = $message;
        $db_express_query_note->save_query_note($note_data);
        if ($express_status == 1 && empty($query_data['is_retry']) && $this->is_need_retry($expressNo)) {
            // 当无物流信息的时候 && 当前不是重试 && 需要重试
            $auto_express_code = $this->get_express_code_by_express_no($expressNo);
            if ($auto_express_code && $expressCode != $auto_express_code) {
                //自动识别成功且和原有的快递公司不同
                $query_data['express_code']      = $auto_express_code;
                $query_data['is_retry']          = 1; // 标记是重试 避免死循环
                $this->assign_express_channel_id = 0; // 不强制指定通道
//                debug_log('快递单号:' . $expressNo . '自动识别为【' . $auto_express_code . '】');
                return $this->query_route($query_data);
            }
        }
        if ($express_status == -1 && empty($query_data['is_retry']) && $query_route_express_channel_id == 8 && $this->is_need_retry($expressNo)) {
            // 当查询失败 && 当前不是重试 && 需要重试
            $query_data['is_retry']          = 1; // 标记是重试 避免死循环
            $this->assign_express_channel_id = 0; // 不强制指定通道
            logToFile('快递单号:' . $expressNo . '免费查单失败,正在尝试付费通道');
            return $this->set_assign_express_channel_id(9)->query_route($query_data);
        }

        if ($result['status'] != 1) {
            $this->message = $result['message'];
            return false; // 先保证能正常返回 $result 确保异常信息可以友好展示
        }
        $this->clear(); // 取值完成后 清空,避免污染,赋值仅对本次生效
        return $result;
    }

    /**
     * 获取打单html
     * @access public
     * @param array $order_guid_array 订单号
     * @param string $port_name 打印机名称
     * @return bool
     * @throws Exception
     */
    public function print_order(array $order_guid_array, string $port_name = '电子面单打印机')
    {
        $bid                 = self::$bid;
        $express_channel_id  = self::get_express_channel_id_by_order_guid($order_guid_array, true);
        $express_config_guid = self::get_express_config_guid_by_order_guid($order_guid_array, true);
        $express             = self::get_express_instance($express_channel_id, $express_config_guid);
        $express_map         = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['relation_guid', 'IN', $order_guid_array],
        ];
        $db_express_order    = new ExpressOrder();
        $bill_number_array   = $db_express_order->where($express_map)->column('bill_number');
        $express_no_array    = $db_express_order->where($express_map)->column('express_no');
        if (empty($bill_number_array)) {
            $this->message = '当前订单不是通过电子面单下单的,不支持打印!';
            return false;
        }
        return $express->printOrder($bill_number_array, $express_no_array, $port_name);
    }

    /**
     * 查询运费
     * @access public
     * @param string $order_guid 订单号
     * @return array|bool
     * @throws Exception
     */
    public function query_fee(string $order_guid)
    {
        $bid                 = self::$bid;
        $express_channel_id  = self::get_express_channel_id_by_order_guid([$order_guid]);
        $express_config_guid = self::get_express_config_guid_by_order_guid([$order_guid]);
        $express             = self::get_express_instance($express_channel_id, $express_config_guid);
        $express_map         = [
            ['bid', '=', $bid],
            ['status', '=', 1],
            ['relation_guid', '=', $order_guid],
        ];
        $db_express_order    = new ExpressOrder();
        $express_no          = $db_express_order->where($express_map)->value('express_no');
        if (empty($express_no)) {
            $this->message = '当前订单不是通过电子面单下单的,不支持查询运费!';
            return false;
        }
        return $express->queryFee(['express_no' => $express_no]);
    }

    /**
     * 下单
     * @access public
     * @param string $order_guid 订单号
     * @param string $express_config_guid 运单模板标识
     * @return bool|array
     * @throws Exception
     */
    public function create_order(string $order_guid, string $express_config_guid)
    {
        $bid              = self::$bid;
        $db_express_order = new ExpressOrder();
        $express_map      = [
            ['bid', '=', $bid],
            ['relation_guid', '=', $order_guid],
        ];
        $order            = $db_express_order->field(['status'])->where($express_map)->findOrEmpty();
        if (!$order->isEmpty() && isset($order['status']) && $order['status'] == 1) {
            $this->message = '当前订单已处理成功,不允许重复下单';
            return false;
        }

        //寻找配置
        $db_express_config      = new ExpressConfig();
        $map_express_config     = [
            ['bid', '=', $bid],
            ['guid', '=', $express_config_guid],
        ];
        $default_express_config = $db_express_config->where($map_express_config)->order(['create_time' => 'ASC'])->findOrEmpty();
        if ($default_express_config->isEmpty()) {
            $msg           = '商家没有可用的电子面单配置~';
            $this->status  = -1;
            $this->message = $msg;
            return false;
        }
        $express_channel_id                         = $default_express_config['channel_id'];
        $is_deduct_express_order_num_express_config = $default_express_config['is_deduct_express_order_num'];
        //如果这个面单通道是需要扣授权数的 则扣除授权数
        $db_express_channel          = new ExpressChannel();
        $map_express_channel         = [
            ['id', '=', $express_channel_id],
        ];
        $is_deduct_express_order_num = $db_express_channel->where($map_express_channel)->value('is_deduct_express_order_num');
        if (($is_deduct_express_order_num_express_config == 1 || $is_deduct_express_order_num == 1) && $order->isEmpty()) {
            $db_business = new Business();
            $map         = [['guid', '=', $bid]];
            $express_num = (int)$db_business->where($map)->value('express_order_num');
            if ($express_num < 1) {
                $msg           = '商家当前可用电子面单授权不足~';
                $this->status  = -1;
                $this->message = $msg;
                return false;
            }
            $db_business->where($map)->setDec('express_order_num');
        }

        $express      = self::get_express_instance($express_channel_id);
        $order_id     = tools()::get_bill_number();
        $express_code = $default_express_config['shipper_code'];
        $sender       = [
            'name'     => $default_express_config['sender_name'],
            'mobile'   => $default_express_config['sender_mobile'],
            'province' => $default_express_config['sender_province_name'],
            'city'     => $default_express_config['sender_city_name'],
            'district' => $default_express_config['sender_exp_area_name'],
            'address'  => $default_express_config['sender_address'],
        ];

        $db_goods_order = new GoodsOrder();

        $goods_order_info = $db_goods_order->get_order_detail($bid, $order_guid, false);

        $receiver = [
            'name'     => $goods_order_info['true_name'],
            'mobile'   => $goods_order_info['mobile'],
            'province' => $goods_order_info['province_name'],
            'city'     => $goods_order_info['city_name'],
            'district' => $goods_order_info['area_name'],
            'address'  => $goods_order_info['address'],
        ];

        $goods_list = $goods_order_info['goods_list'];
        $commodity  = [];
        foreach ($goods_list as $goods) {
            $text = '【' . $goods['name'];
            if (!empty($goods['specs'])) {
                $text .= '[' . $goods['specs'] . ']';
            }
            $text        .= ' * ' . $goods['amount'] . '】';
            $commodity[] = [
                'name' => $text
            ];
        }
        $options     = [
            'commodity'      => $commodity,
            'is_notice'      => $default_express_config['is_notice'], //是否通知快递员上门
            'customer_name'  => $default_express_config['customer_name'],
            'customer_pwd'   => $default_express_config['customer_pwd'],
            'month_code'     => $default_express_config['month_code'],
            'template_size'  => $default_express_config['template_size'],
            'pay_type'       => $default_express_config['pay_type'], //邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付(仅SF支持)
            'exp_type'       => $default_express_config['exp_type'], //快递类型：1-标准快件 ,详细快递类型参考《快递公司快递业务类型.xlsx》
            'need_print_tpl' => 1,
        ];
        $note_guid   = create_guid();
        $insert_data = [
            'bid'                 => $bid,
            'guid'                => $note_guid,
            'express_config_guid' => $default_express_config['guid'],
            'shipper_code'        => $express_code,
            'relation_guid'       => $order_guid,
            'channel_id'          => $express_channel_id,
            'bill_number'         => $order_id,
            'order_code'          => $order_id,
            'receiver'            => $receiver,
            'sender'              => $sender,
            'exp_type'            => $default_express_config['exp_type'],
            'commodity'           => $commodity,
            'status'              => 0
        ];
        $db_express_order->save($insert_data);
        $result           = $express->createOrder($express_code, $order_id, $sender, $receiver, $options);
        $db_express_order = new ExpressOrder();
        $express_map      = [
            ['bid', '=', $bid],
            ['guid', '=', $note_guid],
            ['relation_guid', '=', $order_guid],
        ];
        if ($result['status'] !== '1') {
            // 更新相关记录
            $update_data   = [
                'status'  => -1,
                'message' => $result['message'],
            ];
            $this->status  = (int)$result['status'];
            $this->message = $result['message'];
            $update        = $db_express_order::update($update_data, $express_map);
            return false;
        } else {
            // 更新相关记录
            $order        = $result['order'];
            $express_no   = $order['express_no'];
            $db_express   = new Express();
            $express_code = $db_express->get_code_by_kuaidiniao_code($express_code);
            $update_data  = [
                'status'        => 1,
                'dest_code'     => $order['dest_code'],
                'express_no'    => $express_no,
                'express_code'  => $express_code, //express表中的
                'response_data' => $order['response'],
                'print_tpl'     => $order['print_tpl'] ?? ''
            ];

            $update                      = $db_express_order::update($update_data, $express_map);
            $insert_data['express_no']   = $express_no;
            $insert_data['express_code'] = $express_code;
            return $insert_data;
        }
    }

    /**
     * 获取通道实例
     * @param string $express_channel_id 通道id
     * @param string $express_config_guid 商户通道配置guid
     * @return ExpressInterface
     * @throws Exception
     */
    public static function get_express_instance($express_channel_id, $express_config_guid = null)
    {
        $config         = self::get_express_channel_info($express_channel_id);
        $expressManager = new ExpressManager($config['channel_parameter'], $express_channel_id);
        return $expressManager->express($config['driver'], $express_config_guid);
    }

    /**
     * @throws NotNotifyException
     * @throws Exception
     */
    protected static function get_express_channel_id_by_order_guid($order_guid, $only_success_status = false)
    {
        $bid              = self::$bid;
        $order_guid       = is_array($order_guid) ? $order_guid : [$order_guid];
        $db_express_order = new ExpressOrder();
        $express_map      = [
            ['bid', '=', $bid],
            ['relation_guid', 'IN', $order_guid],
        ];
        if ($only_success_status) {
            $express_map[] = ['status', '=', 1];
        }
        $channel_id_array = $db_express_order->where($express_map)->column('channel_id');
        $channel_id_array = array_unique($channel_id_array);
        if (count($channel_id_array) == 0) {
            throw new NotNotifyException('仅支持打印通过电子面单发货的订单!');
        } elseif (count($channel_id_array) > 1) {
            throw new Exception('请选择通过同一个电子面单模板的订单' . json_encode($channel_id_array));
        }
        self::$express_channel_id = current($channel_id_array);
        return self::$express_channel_id;
    }

    /**
     * @throws NotNotifyException
     * @throws Exception
     */
    protected static function get_express_config_guid_by_order_guid($order_guid, $only_success_status = false)
    {
        $bid              = self::$bid;
        $order_guid       = is_array($order_guid) ? $order_guid : [$order_guid];
        $db_express_order = new ExpressOrder();
        $express_map      = [
            ['bid', '=', $bid],
            ['relation_guid', 'IN', $order_guid],
        ];
        if ($only_success_status) {
            $express_map[] = ['status', '=', 1];
        }
        $express_config_guid_array = $db_express_order->where($express_map)->column('express_config_guid');
        $express_config_guid_array = array_unique($express_config_guid_array);
        if (count($express_config_guid_array) == 0) {
            throw new NotNotifyException('仅支持打印通过电子面单发货的订单!');
        } elseif (count($express_config_guid_array) > 1) {
            throw new Exception('请选择通过同一个电子面单模板的订单' . json_encode($express_config_guid_array));
        }
        return current($express_config_guid_array);
    }

    /**
     * 获取通道配置
     * @return array
     * @throws Exception
     */
    protected static function get_express_channel_info($express_channel_id)
    {
        if (empty(self::$express_channel_info[$express_channel_id])) {
            $db_express_channel                              = new ExpressChannel();
            self::$express_channel_info[$express_channel_id] = $db_express_channel->get_express_channel_info($express_channel_id);
        }
        return self::$express_channel_info[$express_channel_id];
    }

    protected function __clone()
    {
        //disallow clone
    }
    //   public function __wakeup()
    //   {
    //       self::$instance = $this;
    //   }
}