<?php

namespace app\common\service;

use Cyokup\EasyAiChat\EasyAiChat;
use Exception;

/**
 * AI工具服务类
 * 提供AI相关的工具方法和功能
 *
 * Class AiToolsService
 * @package app\common\service
 */
class AiToolsService
{
    /**
     * 商家ID
     * @var string
     */
    public static $bid;

    /**
     * 单例实例
     * @var array
     */
    protected static $instance = null;

    /**
     * 状态码
     * @var int
     */
    public $code;

    /**
     * 消息
     * @var string
     */
    public $msg;

    /**
     * 是否成功
     * @var bool
     */
    public $success = false;

    /**
     * 构造函数 - 禁止直接实例化
     */
    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     * @param string $bid 商家标识
     * @return $this
     * @throws Exception
     */
    public static function get_instance($bid)
    {
        if (empty($bid)) {
            throw new Exception('商家ID不能为空');
        }

        self::$bid = $bid;
        $sn        = get_instance_sn(self::$bid);

        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    /**
     * 反序列化时调用
     */
    public function __wakeup()
    {
        self::$instance = $this;
    }

    /**
     * 禁止克隆
     */
    protected function __clone()
    {
        //disallow clone
    }

    /**
     * 获取商家ID
     * @return string
     */
    public function get_bid()
    {
        return self::$bid;
    }

    public function test($content)
    {
        $aiModel = new EasyAiChat();
//      $messages = [
//          ['role' => 'user', 'content' => $content]
//      ];
        $return = $aiModel::spark()->chat($content);
        halt($return);
    }
}
