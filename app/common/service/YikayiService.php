<?php
/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/7/8
 * Time: 19:55
 */

namespace app\common\service;

use app\model\YkyMemberValueNote;
use Exception;
use OpenApi\Yky;

class YikayiService
{
    protected static $instance = null;
    private static $options = [];
    public $errcode;
    public $errmsg;
    public $openid;
    public $yky;
    public $bid;

    protected function __construct($options)
    {
        self::$options = $options;
        if (!empty($options['openid']) && !empty($options['secret'])) {
            $this->yky = new Yky($options);
        }
        if (!empty($options['bid'])) {
            $this->bid = $options['bid'];
        }
    }

    /**
     * @param array $options
     * @return $this
     */
    public static function get_instance($options)
    {
        $sn = get_instance_sn($options['openid'] . $options['secret']);
        if (!isset(self::$instance) || is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self($options);
        }
        return self::$instance[$sn];
    }

    protected function __clone()
    {
        //disallow clone
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }

    /**
     * 充值
     * @access public
     * @param array $data 数据
     * @return array|bool
     * @throws Exception
     */
    public function add_value($data)
    {
        $key           = $data['uniqueCode'];
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        $db            = new YkyMemberValueNote();
        $yky           = $this->yky;
        //先查询是否存在订单记录 如果存在则直接取出来
        $map   = [
            ['bid', '=', $data['bid']],
            ['guid', '=', $data['uniqueCode']],
        ];
        $order = $db->where($map)->findOrEmpty();
        if ($order->isEmpty()) {
            $order = [
                'guid'                    => $data['uniqueCode'],
                'bid'                     => $data['bid'],
                'way'                     => $data['way'],
                'card_id'                 => $data['cardId'],
                'member_guid'             => $data['member_guid'],
                'relation_guid'           => $data['relation_guid'] ?? '',
                'member_chain_store_guid' => $data['member_chain_store_guid'] ?? '',
                'user_account'            => $data['userAccount'] ?? '10000',
                'value'                   => $data['value'] ?? 0,
                'value_plus'              => $data['valuePlus'] ?? 0,
                'paid_money'              => $data['paidMoney'] ?? 0,
                'memo'                    => $data['meno'] ?? '',
            ];
            //不存在门店的时候则自动获取会员登记门店
            if (empty($order['member_chain_store_guid'])) {
                $member_info = $yky->get_member_info($data['member_guid']);
                if ($member_info === false) {
                    throw new Exception($yky->message);
                }
                $order['member_chain_store_guid'] = $member_info['ChainStoreGuid'];
            }
            $db->save($order);
        } else {
            //状态等于1不重复执行转账,直接返回数据
            if ($order['status'] === 1) {
                $lock_instance->unlock($lock);
                return $order;
            }
        }
        $map    = [
            ['guid', '=', $data['uniqueCode']],
            ['bid', '=', $data['bid']],
        ];
        $result = $yky->Add_Value($data);
        if ($result === false) {
            $update_data = [
                'status'  => -1,
                'message' => $yky->message,
            ];
            $update      = $db::update($update_data, $map);
            throw new Exception($yky->message);
        }
        $bill_number = $result['billNumber'];
        //更新成功后则进行消费
        $update_data = [
            'bill_number' => $bill_number,
            'status'      => 1,
            'message'     => $result['message'],
        ];
        $update      = $db::update($update_data, $map);
        $lock_instance->unlock($lock);
        return $result;
    }

}