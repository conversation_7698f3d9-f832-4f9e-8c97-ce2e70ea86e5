<?php

namespace app\common\service;

use app\model\DingTalkApp;
use app\model\DingTalkUserInfo;
use Exception;

/**
 * @library AlipayService
 * <AUTHOR>
 * @date 2018/05/24 13:23
 *

 */
class DingTalkService
{
    public static $appid;

    /**
     * 获取支付宝user_id
     * @access public
     * @param string $bid 商家标志
     * @return array
     * @throws Exception
     */
    public static function get_open_id($bid)
    {
        $config            = get_config_by_bid($bid);
        $ding_talk_app_key = $config['ding_talk_app_key'];
        if (empty($ding_talk_app_key)) {
            error('商家未授权钉钉应用');
        }
        $db               = new DingTalkApp();
        $app_config       = $db->get_ding_talk_app_config($ding_talk_app_key);
        $app_secret       = $app_config['app_secret'];
        $openid_cache_key = $ding_talk_app_key . '_openid';//先从cookies中获取是否存在openid,存在则直接返回
        $openid           = cookies($openid_cache_key);
        if ($openid) {
            return ['appid' => $ding_talk_app_key, 'openid' => $openid];
        }
        $request      = request();
        $params       = $request->param();
        $redirect_url = $request->url(true);
        if (empty($params['authCode'])) {
            //链接上无auth_code参数则构造授权链接
            $oauth_url = 'https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=' . $ding_talk_app_key . '&scope=openid&state=' . $ding_talk_app_key . '&prompt=consent&redirect_uri=' . urlencode($redirect_url);
            redirect($oauth_url);
        }
        //有参数则进行解析code 获取openid
        $auth_code = $params['authCode'];
        $url       = 'https://api.dingtalk.com/v1.0/oauth2/userAccessToken';
        $data      = [
            'clientId'     => $ding_talk_app_key,
            'clientSecret' => $app_secret,
            'code'         => $auth_code,
            'grantType'    => 'authorization_code',
        ];
        $result    = curl()->set_http_errors(false)->post($url, $data)->get_body();
        //{"requestid":"FCF130E0-B981-74B5-82D0-FBA97197AFBF","code":"invalidParameter.authCode.notFound","message":"不合法的临时授权码"}
        if (empty($result['accessToken'])) {
            $msg = 'token获取失败:';
            $msg .= (is_array($result) && isset($result['message'])) ? $result['message'] : '钉钉接口异常';
            wr_log($msg);
            error($msg);
        }
        $access_token = $result['accessToken'];
        $url          = 'https://api.dingtalk.com/v1.0/contact/users/me';
        $header       = [
            'x-acs-dingtalk-access-token' => $access_token
        ];
        $result       = curl()->set_header($header)->get($url)->get_body();
        if (empty($result['openId'])) {
            $referer    = $request->header('referer');
            $user_agent = $request->header('user_agent');
            wr_log('openid获取失败:user_agent:' . $user_agent . ',referer:' . $referer . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 1);
            logToFile($params);
            error('系统繁忙,请您退出网页重新进入!');
        }
        $db   = new DingTalkUserInfo();
        $data = [
            'bid'       => $bid,
            'client_id' => $ding_talk_app_key,
            'data'      => $result
        ];
        $db->save($data);
        $openid = $result['openId'];
        $exp    = 3600 * 24;
        cookies($openid_cache_key, $openid, $exp);
        redirect(substr($redirect_url, 0, strrpos($redirect_url, '&authCode')));
    }
}