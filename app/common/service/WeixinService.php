<?php

namespace app\common\service;

use app\model\WechatConfig;
use app\common\tools\Visitor;
use Exception;
use think\facade\App;
use We;
use WeChat\Exceptions\InvalidResponseException;
use WeOpen\Service;

/**
 * 加载缓存器
 *
 * Class We
 * @library WeChatDeveloper
 * <AUTHOR>
 * @date 2018/05/24 13:23
 *
 */
class WeixinService
{
    public static $appid;
    public static $component_appid;
    protected static $instance = null;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws Exception
     */
    public static function __callStatic($name, $arguments)
    {
        return We::$name(self::get_all_config());
    }

    /**
     * 获取微信操作对象
     * @return array
     * @throws Exception
     */
    public static function get_all_config()
    {
        $appid_config     = self::get_appid_config();
        $component_config = self::get_component_config();
        if (isset($appid_config['authorized']) && $appid_config['authorized'] == false) {
            //如果未授权状态,则无需注入GetAccessTokenCallback方法,直接用appid+secret即可获取access_token
            unset($component_config['GetAccessTokenCallback']);
        }
        if (empty($appid_config['appid'])) {
            $appid_config['appid']          = $component_config['component_appid'];
            $appid_config['appsecret']      = $component_config['component_appsecret'];
            $appid_config['token']          = $component_config['component_token'];
            $appid_config['encodingaeskey'] = $component_config['component_encodingaeskey'];
        }
        return array_merge($appid_config, $component_config);
    }

    /**
     * 获取微信配置
     * @access public
     * @return array
     * @throws Exception
     */
    public static function get_appid_config()
    {
        if (empty(self::$appid)) {
            return [];
        }
        $component_appid = self::get_component_appid();
        $key             = 'wechat:config:' . self::$appid . ':' . $component_appid;
        $exp             = 3600 * 24;
        $config          = cache($key);
        if (!$config) {
            //缓存不存在则从数据库中查找
            $db_wechat_config = new WechatConfig();
            $field            = [
                'component_appid'           => 'component_appid',
                'authorizer_appid'          => 'appid',
                'authorizer_appsecret'      => 'appsecret',
                'authorizer_encodingaeskey' => 'encodingaeskey',
                'mch_id',
                'token',
                'subscribe_template_id',
                'partnerkey'                => 'mch_key',
                'ssl_cer',
                'ssl_key',
                'ssl_cer_string',
                'ssl_key_string',
                'status',
                'type'
            ];
            $map              = [
                ['component_appid', '=', $component_appid],
                ['authorizer_appid', '=', self::$appid],
            ];
            $config           = $db_wechat_config->failException()->field($field)->where($map)->find()->toArray();
            if (!empty($config['ssl_cer']) && !empty($config['ssl_key'])) {
                //使用绝对路径,确保任何情况都可以使用
                $config['ssl_cer'] = tools()::get_absolute_path($config['ssl_cer']);
                $config['ssl_key'] = tools()::get_absolute_path($config['ssl_key']);
            }
            //合并配置
            $config['authorized'] = ($config['status'] == 1);
            cache($key, $config, $exp);
        }
        if (!is_array($config)) {
            logToFile($config);
            wr_log('config 缓存中获取到不是array:' . $config);
            throw new Exception('config 缓存中获取到不是array');
        }
        $config['runtime_path'] = app()->getRuntimePath();
        return $config;
    }

    /**
     * 获取开放平台appid
     * @access public
     * @param string $appid APPID
     * @return string
     * @throws Exception
     */
    public static function get_component_appid($appid = null)
    {
        if ($appid && empty(self::$appid)) {
            self::$appid = $appid;
        }
        $config = self::get_component_config_by_domain();
        return $config['component_appid'] ?? '';
    }

    /**
     * 通过域名查找微信开放平台配置
     * @access public
     * @return array
     * @throws Exception
     */
    protected static function get_component_config_by_domain()
    {
        $config_array = config('wechat_open_platform');
        if (self::$component_appid) {
            //优先通过开放平台appid获取
            foreach ($config_array as $key => $val) {
                if (self::$component_appid == $val['component_appid']) {
                    return $val;
                }
            }
        }
        if (request()->isCli()) {
            //命令行优先取最近活跃的授权记录
            $map              = [
                ['authorizer_appid', '=', self::$appid],
                ['status', '=', 1]
            ];
            $db_wechat_config = new WechatConfig();
            $component_appid  = $db_wechat_config->where($map)->order(['update_time' => 'DESC'])->value('component_appid');
            if ($component_appid) {
                foreach ($config_array as $key => $val) {
                    if ($component_appid == $val['component_appid']) {
                        return $val;
                    }
                }
            }
            //如果不存在则直接取第一条配置
            return current($config_array);
        }
        //web模式通过域名判断
        $domain = self::get_domain();
        foreach ($config_array as $key => $val) {
            if (in_array($domain, $val['domain'])) {
                return $val;
            }
        }
        throw new Exception('找不到配置');
    }

    /**
     * 获取域名
     * @access protected
     * @return string
     * @throws Exception
     */
    protected static function get_domain()
    {
        return request()->host(false);
    }

    /**
     * 获取开放平台配置
     * @access public
     * @return array
     * @throws Exception
     */
    protected static function get_component_config()
    {
        $config = self::get_component_config_by_domain();
        // 注册授权公众号 AccessToken 处理
        $config['GetAccessTokenCallback'] = function ($authorizer_appid) use ($config) {
            //防止并发则先用分布式锁,获得锁之后先查询缓存 再调用微信接口
            $component_appid = $config['component_appid'];
            $lock_key_name   = 'access_token' . $component_appid . $authorizer_appid;
            $lock_instance   = get_distributed_instance();
            $lock            = $lock_instance->get_lock($lock_key_name);
            //获得分布式锁之后开始获取access_token,先判断是否存在缓存
            //缓存没有继续调用微信API
            $map                      = [
                ['authorizer_appid', '=', $authorizer_appid],
                ['component_appid', '=', $component_appid],
                ['status', '=', 1],
            ];
            $db_wechat_config         = new WechatConfig();
            $authorizer_refresh_token = $db_wechat_config->failException()->where($map)->value('authorizer_refresh_token');
            $open                     = new Service($config);
            $result                   = $open->refreshAccessToken($authorizer_appid, $authorizer_refresh_token);
            if (empty($result['authorizer_access_token'])) {
                throw new InvalidResponseException($result['errmsg'], $result['errcode']);
            }
            $access_token = $result['authorizer_access_token'];
            $data         = ['authorizer_access_token' => $access_token, 'authorizer_refresh_token' => $result['authorizer_refresh_token']];
            $db_wechat_config::update($data, $map);
            $lock_instance->unlock();
            return $access_token;
        };
        return $config;
    }


    public static function get_subscribe_url($bid, $appid, $callback_url = null)
    {
        if (empty($appid)) {
            return '';
        }
        $db_wechat_config = new WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['subscribe_template_id']);
        if (empty($info['subscribe_template_id'])) {
            return '';
        }
        $callback_url = $callback_url ?: request()->header('referer');
        $callback_url = $callback_url ?: request()->url(true);
        return (string)url('gateway/wechat_subscribe_msg/index', ['bid' => $bid, 'appid' => $appid, 'callback_url' => $callback_url], false, true);
    }

    /**
     * 获取微信openid
     * @access public
     * @param string $bid 商家标志
     * @return array
     * @throws Exception
     */
    public static function get_open_id($bid, $appid = null)
    {
        if (Visitor::is_readonly()) {
            error('当前网址不允许登录,请联系管理员!');
        }
        if (empty($appid)) {
            $config = get_config_by_bid($bid);
            $appid  = $config['appid'];
            if (!$appid) {
                error('商户未授权对接公众号');
            }
        }
        $exp = 3600 * 24;
        //先从cookies中获取是否存在openid,存在则直接返回
        $openid_cache_key = $appid . '_openid';
        $openid           = cookies($openid_cache_key);
        //仅当调试模式的情况下而且bid是 admin 这个测试账号 openid 直接写死 xyf
        if (App::isDebug() && (in_array($bid, [
                    config('app.super_admin_business_guid'),
                    'bb8f3de1-c915-6346-18ea-96174cd70dcd',
                    'f37eb109-70ac-9e6e-a089-ecefebd3d396',
                    'f53febf0-2b6b-6248-74ab-c8a792b89cd4',
                    '23579886-1bc1-1c00-4b03-510884c96bac',
                ]) || is_host() && $appid == get_system_config('platform_wechat_appid'))) {
            if ($appid == 'wx291635d4496091ff') {
                $openid = 'oZa5mwwRQi6Gu5EPzkmXG7W8lpMc';
            }
            if ($appid == 'wx0bcc73de74c62512') {
                $openid = 'o5uykjj6L80_QtSH-QiqkeoRxeH0';
            }
            if ($appid == 'wx1047352032f8959a') {
                $openid = 'oitp66ULkTxcirmENw8VIOLHhiGE';
            }
            if ($appid == 'wx07ce368b922c9841') {
                $openid = 'ohg9BuLA04hHe899kwY0JNWweWuQ';
            }
        }
        if ($openid) {
            return ['appid' => $appid, 'openid' => $openid];
        }
        //不存在缓存则进入授权逻辑
        $request      = request();
        $params       = $request->param();
        $params_code  = $params['code'] ?? '';
        $params_state = $params['state'] ?? '';
        $params_appid = $params['appid'] ?? '';
        $redirect_url = $request->url(true);
        //校验链接上得http://www.xxxx.net/admin/passport/bind_user_success?bid=f67ff919-a827-cb90-e6d6-104d99423473&code=011DhB2002HT8R1zme30058OOg2DhB2C&state=wxf17a5bac18ded2d3&appid=wxf17a5bac18ded2d3
        if (strlen($params_code) == 32 && $params_state && $params_appid && $params_appid == $params_state && $appid != $params_appid) {
            wr_log('发现当前url上的参数(wechat_service)和需要授权的appid参数不一致:' . $request->url(true), 1);
            redirect(tools()::remove_after_code_url($redirect_url));
        }
        $instance = self::get_instance($appid)::WeChatOauth();
        $scope    = $params['scope'] ?? 'snsapi_base';
        if (empty($params['code']) || (strlen($params['code']) != 32)) {
            //链接上无code参数则构造授权链接
            $oauth_url = $instance->getOauthRedirect($redirect_url, $appid, $scope);
            redirect($oauth_url);
        }
        if (!empty($params['code']) && empty($params['state']) && empty($params['appid'])) {
            wr_log('链接上存在code参数 但是state和appid参数为空,重新构造授权链接');
            $oauth_url = $instance->getOauthRedirect($redirect_url, $appid, $scope);
            redirect($oauth_url);
        }
        try {
            //有参数则进行解析code 获取openid
            $result = $instance->getOauthAccessToken();
        } catch (\Exception $e) {
            switch ($e->getCode()) {
                case 40029:  //invalid code
                    wr_log('发现invalid code:' . $request->url(true));
                    redirect(tools()::remove_after_code_url($redirect_url));
                    break;
                case 40163:// code been used
                    wr_log('发现code been used:' . $request->url(true));
                    redirect(tools()::remove_after_code_url($redirect_url));
                    break;
                default:
                    throw $e;
            }
        }
        $referer    = $request->header('referer');
        $user_agent = $request->header('user_agent');
        if (empty($result['openid'])) {
            wr_log('openid获取失败:appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 1);
            logToFile($params);
            error('openid获取失败');
        }
        $openid = $result['openid'];
        if (isset($result['is_snapshotuser']) && $result['is_snapshotuser'] == 1) {
            wr_log('发现用户是快照用户:' . $openid, 1);
            error('请点击底部"使用完整服务"开始使用');//必须在获取openid时候拦截,否则会生成垃圾数据
        }
        if ($scope == 'snsapi_userinfo') {
            $user_info = $instance->getUserInfo($result['access_token'], $openid);
            cache('user_info:' . $openid, $user_info);
        }
        cookies($openid_cache_key, $openid, $exp);
        $url = tools()::remove_after_code_url($redirect_url);
        redirect($url);
    }


    /**获取实例
     * @param string $appid 公众号标识
     * @param string $component_appid 开放平台标识
     * @return  We
     * @throws Exception
     */
    public static function get_instance($appid = '', $component_appid = '')
    {
        self::$appid           = $appid;
        self::$component_appid = $component_appid;
        $sn                    = get_instance_sn($appid . $component_appid . self::get_domain());
        if (!isset(self::$instance) || is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }

    protected function __clone()
    {
        //disallow clone
    }
}

if (!function_exists('weixin')) {
    /**
     * 实例化微信SDK
     * @param string $appid 微信appid
     * @param string $component_appid 微信平台appid
     * @return WeixinService|null
     * @throws Exception
     */
    function weixin($appid = '', $component_appid = '')
    {
        return WeixinService::get_instance($appid, $component_appid);
    }
}