<?php

namespace app\common\service;

/**
 * QyWeixinMessageService
 *
 * Class QyWeixinMessageService
 * @library QyWeixinMessageService
 * <AUTHOR>
 * @date 2020/12/12 18:00:00
 * @method QyWeixinMessageService image $data = array()) public方法
 * @method QyWeixinMessageService voice $data = array()) public方法
 * @method QyWeixinMessageService video $data = array()) public方法
 * @method QyWeixinMessageService file $data = array()) public方法
 * @method QyWeixinMessageService news $data = array()) public方法
 * @method QyWeixinMessageService mpnews $data = array()) public方法
 * @method QyWeixinMessageService miniprogram_notice $data = array()) public方法 //todo
 * @method QyWeixinMessageService taskcard $data = array()) public方法 //todo
 * */
class QyWeixinMessageService
{

    //单例对象
    private static $instance = null;
    public $corpid = '';
    public $touser = ''; //指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为”@all”，则向该企业应用的全部成员发送
    public $toparty = ''; //指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
    public $totag = ''; //指定接收消息的标签，标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数


    public $msgtype = 'text';
    public $msgdata = [];
    public $agentid = null; //企业应用的id，整型。企业内部开发，可在应用的设置页面查看；第三方服务商，可通过接口 获取企业授权信息 获取该参数值
    public $enable_id_trans = 0; //表示是否开启id转译，0表示否，1表示是，默认0
    public $enable_duplicate_check = 0; //表示是否开启重复消息检查，0表示否，1表示是，默认0
    public $duplicate_check_interval = 0; //表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时


    private function __construct()
    {

    }

    /**
     * 单例化对象
     */
    public static function get_instance()
    {
        $sn = get_instance_sn();
        //单例方法,用于访问实例的公共的静态方法
        if (!isset(self::$instance) || is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    /**
     * @access public
     * @param int $enable_id_trans
     * @return $this
     */
    public function set_enable_id_trans($enable_id_trans = 0)
    {
        $this->enable_id_trans = $enable_id_trans;
        return $this;
    }

    /**
     * @access public
     * @param int $duplicate_check_interval
     * @return $this
     */
    public function set_duplicate_check_interval($duplicate_check_interval = 0)
    {
        if ($duplicate_check_interval > 0) {
            $this->enable_duplicate_check   = 1;
            $this->duplicate_check_interval = $duplicate_check_interval;
        }
        return $this;
    }

    /**
     * @access public
     * @param int $agentid 应用ID
     * @return $this
     */
    public function set_agent_id(int $agentid)
    {
        $this->agentid = $agentid;
        return $this;
    }

    /**
     * @access public
     * @param string $msgtype 消息类型
     * @param array $argc 消息内容,第一个参数
     * @return $this
     */
    public function __call(string $msgtype, array $argc)
    {
        $this->msgtype = $msgtype;
        $this->msgdata = $argc[0];
        return $this;
    }

    /**
     * @access public
     * @param string $content 消息内容
     * @return $this
     */
    public function text(string $content)
    {
        $this->msgtype = __FUNCTION__;
        $content       = preg_replace('/<br\\s*?\/??>/i', chr(13), $content); //将<br> 替换成 \n
        $content       = $content . "\n" . microsecond();
        $this->msgdata = ['content' => $content];
        return $this;
    }

    /**
     * @access public
     * @param array $content 消息内容
     * @return $this
     */
    public function textcard(array $content)
    {
        $this->msgtype          = __FUNCTION__;
        $description            = $content['description'];
        $description            = preg_replace('/<br\\s*?\/??>/i', chr(13), $description); //将<br> 替换成 \n
        $description            = str_replace('<', '(', $description); //先处理内容不允许包含<> 替换成括号
        $description            = str_replace('>', ')', $description);
        $description            .= "\n当前时间: " . format_timestamp();
        $content['description'] = $description;
        $content['url']         = $content['url'] ?? 'weixin://contacts/profile/';
        $this->msgdata          = $content;
        return $this;
    }

    /**
     * @access public
     * @param string $content 消息内容
     * @return $this
     */
    public function markdown(string $content)
    {
        $this->msgtype = __FUNCTION__;
        $this->msgdata = ['content' => $content];
        return $this;
    }

    /**
     * @access public
     * @param string $touser 接收者,多个用|隔开
     * @return $this
     */
    public function touser($touser = '')
    {
        if ($touser) {
            $this->touser = $touser;
        }
        return $this;
    }

    /**
     * @access public
     * @param string $toparty 接收者,多个用|隔开
     * @return $this
     */
    public function toparty($toparty = '')
    {
        if ($toparty) {
            $this->toparty = $toparty;
        }
        return $this;
    }

    /**
     * @access public
     * @param string $totag 接收者,多个用|隔开
     * @return $this
     */
    public function totag($totag = '')
    {
        if ($totag) {
            $this->totag = $totag;
        }
        return $this;
    }

    /**
     * 用于send后调用 避免CLI模式下数据污染
     * @access public
     * @return mixed
     */
    public function clear()
    {
        $this->corpid                   = '';
        $this->touser                   = ''; //指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为”@all”，则向该企业应用的全部成员发送
        $this->toparty                  = ''; //指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
        $this->totag                    = ''; //指定接收消息的标签，标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
        $this->msgtype                  = 'text';
        $this->msgdata                  = [];
        $this->agentid                  = null; //企业应用的id，整型。企业内部开发，可在应用的设置页面查看；第三方服务商，可通过接口 获取企业授权信息 获取该参数值
        $this->enable_id_trans          = 0; //表示是否开启id转译，0表示否，1表示是，默认0
        $this->enable_duplicate_check   = 0; //表示是否开启重复消息检查，0表示否，1表示是，默认0
        $this->duplicate_check_interval = 0; //表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
    }

    /**
     * @access public
     * @return int
     */
    public function send()
    {
        if (empty($this->touser)) {
            $this->touser = get_system_config('default_touser');
        }
        if (empty($this->corpid)) {
            $this->corpid = get_system_config('default_corpid');
        }
        if (is_null($this->agentid)) {
            $this->agentid = get_system_config('default_agentid');
        }
        $data = [
            'corpid'                   => $this->corpid,
            'agentid'                  => $this->agentid,
            'touser'                   => $this->touser,
            'toparty'                  => $this->toparty,
            'totag'                    => $this->totag,
            'enable_id_trans'          => $this->enable_id_trans,
            'enable_duplicate_check'   => $this->enable_duplicate_check,
            'duplicate_check_interval' => $this->duplicate_check_interval,
            'msgtype'                  => $this->msgtype,
            $this->msgtype             => $this->msgdata
        ];
        //$connections = is_debug() ? 'database' : 'amqp';
        //return job()->set_connections($connections)->set_job_name('Weixin@send_qy_wechat')->push_job($data);
        $this->clear();
        return job()->set_job_name('Weixin@send_qy_wechat')->push_job($data);
    }

    private function __clone()
    {
        //disallow clone
    }
}

if (!function_exists('qy_weixin_msg')) {
    /**
     * @return QyWeixinMessageService|null
     */
    function qy_weixin_msg()
    {
        return QyWeixinMessageService::get_instance();
    }
}