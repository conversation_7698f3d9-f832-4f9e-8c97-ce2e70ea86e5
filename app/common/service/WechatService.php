<?php

namespace app\common\service;

use app\model\Business;
use app\model\User;
use app\model\UserMoneyNote;
use app\model\WechatRedPacketOrder;
use app\model\WechatTransfersOrder;
use Exception;
use Throwable;

/**
 * Created by PhpStorm.
 * User: xieyongfa
 * Date: 2017/7/8
 * Time: 18:33
 */
class WechatService
{
    protected static $instance = null;
    /** status */
    public $status;
    /** message */
    public $message;

    protected function __construct()
    {
        //disallow new instance
    }

    protected function __clone()
    {
        //disallow clone
    }

    /**
     * 单例方法,用于访问实例的公共的静态方法
     */
    public static function get_instance()
    {
        $sn = get_instance_sn();
        if (!isset(self::$instance) || is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    public function __wakeup()
    {
        $sn                  = get_instance_sn();
        self::$instance[$sn] = $this;
    }

    /**
     * 企业转账
     * @access public
     * @param array $data 企业转账
     * @return bool|array
     * @throws Exception
     */
    public function transfer($data)
    {
        $db         = new WechatTransfersOrder();
        $bid        = $data['bid'];
        $order_guid = $data['order_guid'];
        //先查询是否存在订单记录 如果存在则直接取出来
        $map_order = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $order     = $db->where($map_order)->find();
        if ($order['status'] === 1) {
            //状态等于1不重复执行转账,直接返回数据
            return $order;
        }
        $amount_yuan = tools()::nc_price_fen2yuan($order['amount']);
        //扣除账户资金
        $db_user_money_note = new UserMoneyNote();
        $recharge_data      = [
            'bid'           => $bid,
            'way'           => 5,// 途径 1 后台充值 2后台扣除 3 付费激活卡券扣除 4 发货扣除资金 5 微信零钱
            'type'          => -1, //扣除
            'money'         => $amount_yuan,
            'memo'          => '[微信零钱付款]',
            'relation_guid' => $order_guid,
            'unique_code'   => $order_guid
        ];
        try {
            $recharge = $db_user_money_note->recharge_money($recharge_data);
        } catch (Exception|Throwable $e) {
            $this->message = $e->getMessage();
            return false;
        }
        $key           = __FUNCTION__ . $order['appid'] . $order['openid'];
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        $pay           = weixin($order['appid'])::WePayTransfers();
        $key           = 'wechat_balance_status:' . $order['bid'];
        if (cache($key)) {
            $this->message = '当前处于余额不足状态,无法充值';
            $lock_instance->unlock($lock);
            return false;
        }
        $day_max_payment = 500000;
        $map             = [
            ['bid', '=', $order['bid']],
            ['appid', '=', $order['appid']],
            ['openid', '=', $order['openid']]
        ];
        $today_payment   = $db->whereDay('payment_time')->where($map)->sum('amount');
        if (($today_payment + $order['amount']) > $day_max_payment) {
            $this->message = '已达到该用户当日收款金额上限5000元!';
            $this->status  = 'DAY_PAY_LIMIT';
            $lock_instance->unlock($lock);
            return false;
        }
        $options = [
            'partner_trade_no' => $order['partner_trade_no'],
            'openid'           => $order['openid'],
            'check_name'       => 'NO_CHECK',
            'amount'           => $order['amount'],
            'desc'             => $order['desc'],
            'spbill_create_ip' => tools()::get_client_ip(),
        ];
        $config  = get_config_by_bid($bid);
        if (!empty($config['mch_appid'])) {
            $options['mch_appid'] = $config['mch_appid'];
        }
        $result = $pay->create($options);
        if ($result === false) {
            // 更新转账记录表
            $update_data   = [
                'status'  => -1,
                'message' => $pay->errMsg,
            ];
            $this->status  = $pay->errCode;
            $this->message = $pay->errMsg;
            switch ($pay->errCode) {
                case 'NOTENOUGH':
                    cache($key, 1);
                    //判断是否余额不足,通知对应的管理员
                    $this->not_enough_notify($order['bid']);
                    break;
                default:
            }
        } else {
            // 更新转账记录表
            $update_data = [
                'payment_no'   => $result['payment_no'],
                'payment_time' => $result['payment_time'],
                'status'       => 1,
            ];
        }
        $update = $db::update($update_data, $map_order);
        $lock_instance->unlock($lock);
        return $result;
    }

    /**
     * 企业转账
     * @access public
     * @param array $data 企业转账
     * @return bool|array
     * @throws Exception
     */
    public function transfer_beta($data)
    {
        $db         = new WechatTransfersOrder();
        $bid        = $data['bid'];
        $order_guid = $data['order_guid'];
        $user_guid  = $data['user_guid'] ?? '';

        //先查询是否存在订单记录 如果存在则直接取出来
        $map_order = [
            ['bid', '=', $bid],
            ['guid', '=', $order_guid],
        ];
        $order     = $db->where($map_order)->find();
        if ($order['status'] === 1) {
            //状态等于1不重复执行转账,直接返回数据
            return $order;
        }
        if ($order['amount'] < 0.3) {
            $this->message = '付款金额不能低于0.3元';
            return false;
        }
        $amount_yuan = tools()::nc_price_fen2yuan($order['amount']);
        //扣除账户资金
        $db_user_money_note = new UserMoneyNote();
        $recharge_data      = [
            'bid'           => $bid,
            'way'           => 5,// 途径 1 后台充值 2后台扣除 3 付费激活卡券扣除 4 发货扣除资金 5 微信零钱
            'type'          => -1, //扣除
            'money'         => $amount_yuan,
            'memo'          => '[微信零钱付款]',
            'relation_guid' => $order_guid,
            'unique_code'   => $order_guid
        ];
        if ($user_guid) {
            $recharge_data['user_guid'] = $user_guid;
        }
        try {
            $recharge = $db_user_money_note->recharge_money($recharge_data);
        } catch (Exception|Throwable $e) {
            $this->message = $e->getMessage();
            preg_match('/\(([^)]+)\)[^)]*$/', $this->message, $matches);
            $this->status = $matches[1] ?? ''; //返回错误码方便业务端处理判断
            // NO_AUTH：没有该接口权限

            // MONEY_LIMIT：已经达到今日付款总额上限/已达到付款给此用户额度上限
            // SENDNUM_LIMIT：该用户今日付款次数超过限制
            // SEND_MONEY_LIMIT：已达到今日商户付款额度上限
            // RECEIVED_MONEY_LIMIT：已达到今日付款给此用户额度上限
            // NOTENOUGH：您的付款账号余额不足或资金未到账
            // AMOUNT_LIMIT：金额超限

            // PARAM_ERROR：请求参数校验错误
            // OPENID_ERROR：Openid格式错误或者不属于商家公众账号
            // SEND_FAILED：付款错误，请查单确认付款结果
            // SYSTEMERROR：微信内部接口调用发生错误
            // NAME_MISMATCH：收款人身份校验不通过
            // SIGN_ERROR：校验签名错误
            // XML_ERROR：Post请求数据不是合法的xml格式内容
            // FATAL_ERROR：两次请求商户单号一样，但是参数不一致
            // FREQ_LIMIT：接口请求频率超时接口限制

            // CA_ERROR：请求没带商户API证书或者带上了错误的商户API证书
            // V2_ACCOUNT_SIMPLE_BAN：用户微信支付账户未实名，无法付款
            // PARAM_IS_NOT_UTF8：请求参数中包含非utf8编码字符
            // RECV_ACCOUNT_NOT_ALLOWED：收款账户不在收款账户列表
            // PAY_CHANNEL_NOT_ALLOWED：本商户号未配置API发起能力
            // PARAM_ERROR：付款商户号无法使用该品牌信息
            // NO_AUTH：付款商户号无品牌红包产品权限
            // PARAM_ERROR：品牌已被注销或状态无效
            // PARAM_ERROR：品牌和视频号没有绑定或已解绑
            // PARAM_ERROR：品牌和品牌红包消息模板没有对应关系
            // PARAM_ERROR：品牌红包消息模板已被删除
            // EXCEED_PAYEE_ACCOUNT_LIMIT：收款用户身份信息待完善
            // PAYER_ACCOUNT_ABNORMAL：商户号被处罚、冻结
            // PAYEE_ACCOUNT_ABNORMAL：收款用户身份信息待完善
            // PAYMENT_IN_PROGRESS：该笔付款正在处理中
            wr_log($this->message, 1);
            return false;
        }
        $key           = __FUNCTION__ . $order['appid'] . $order['openid'];
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        $key           = 'wechat_balance_status:' . $order['bid'];
        if (cache($key)) {
            $this->message = '当前处于余额不足状态,无法充值';
            $lock_instance->unlock($lock);
            return false;
        }
        $day_max_payment = 500000;
        $map             = [
            ['bid', '=', $order['bid']],
            ['appid', '=', $order['appid']],
            ['openid', '=', $order['openid']]
        ];
        $today_payment   = $db->whereDay('payment_time')->where($map)->sum('amount');
        if (($today_payment + $order['amount']) > $day_max_payment) {
            $this->message = '已达到该用户当日收款金额上限5000元!';
            $this->status  = 'DAY_PAY_LIMIT';
            $lock_instance->unlock($lock);
            return false;
        }
        $options = [
            'partner_trade_no' => $order['partner_trade_no'],
            'openid'           => $order['openid'],
            'check_name'       => 'NO_CHECK',
            'amount'           => $order['amount'],
            'desc'             => $order['desc'],
            'spbill_create_ip' => tools()::get_client_ip(),
        ];
        $config  = get_config_by_bid($bid);
        if (!empty($config['mch_appid'])) {
            $options['mch_appid'] = $config['mch_appid'];
        }
        $pay_instance = pay($bid)->driver('wechat')->scene('transfer');
        try {
            $result = $pay_instance->apply($options);
            // 更新转账记录表
            $update_data = [
                'payment_no'   => $result['payment_no'],
                'payment_time' => $result['payment_time'],
                'status'       => 1,
            ];
        } catch (Exception|Throwable $e) {
            $this->status  = $e->getCode();
            $this->message = $e->getMessage();
            // 更新转账记录表
            $update_data = [
                'status'  => -1,
                'message' => $this->message,
            ];
            $result      = false;
        }
        $update = $db::update($update_data, $map_order);
        $lock_instance->unlock($lock);
        return $result;
    }

    /**
     * 余额不足通知方法
     * @access protected
     * @param string $bid 商家标识
     * @return void
     * @throws Exception
     */
    protected function not_enough_notify($bid)
    {
        $key           = 'not_enough_notify:' . $bid;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key);
        //获得分布式锁之后开始获取判断是否存在缓存
        if (cache($key)) {
            //如果存在缓存则
            $lock_instance->unlock();
            return;
        }
        cache($key, format_timestamp(), 60);
        $db_business   = new Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        $business_name = $business_info['business_name'];
        $data          = [
            'url'         => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb02461a9fc6cbb8c&redirect_uri=https%3a%2f%2fpay.weixin.qq.com%2findex.php%2fxphp%2fcqrcoderecharge%2fqrrechargepage%3fmchcode%3d1418925502&response_type=code&scope=snsapi_base&state=123#wechat_redirect',
            'title'       => '商家' . $business_name . '商户余额不足',
            'detail'      => '微信支付商户号余额不足',
            'user'        => '系统',
            'name'        => '请尽快充值',
            'remark'      => '如有疑问请联系客服',
            'create_time' => format_timestamp(),
        ];
        notify()->set_key_name(NotifyService::Notice)->limit_agent()->set_data($data)->set_bid($bid)->send();
        $lock_instance->unlock();
    }

    /**
     * 获取红包发送状态
     * @access public
     * @param array $data 数据
     * @param bool $return_success_status 是否返回状态
     * @return bool|array
     * @throws Exception
     */
    public function get_red_packet_status($data, $return_success_status = false)
    {
        $db = new WechatRedPacketOrder();
        //先查询是否存在订单记录 如果存在则直接取出来
        $map   = [
            ['bid', '=', $data['bid']],
            ['mch_billno', '=', $data['mch_billno']]
        ];
        $order = $db->where($map)->find();
        if (!$order) {
            $this->message = '订单号不存在';
            return false;
        }
        if ($order['red_packet_status'] == 'RECEIVED') {
            //领取成功则不再查询状态
            return $order;
        }
        $pay    = weixin($order['wxappid'])::WePayRedpack();
        $result = $pay->query($data['mch_billno']);
        if ($result === false) {
            $this->message = $pay->errMsg;
            return false;
        }
        //检查成功则更新
        $update_data = [
            'red_packet_status' => $result['status'],
            'send_time'         => $result['send_time'],
        ];
        if (isset($result['hblist']['hbinfo']['rcv_time'])) {
            $update_data['rcv_time'] = $result['hblist']['hbinfo']['rcv_time'];
        }
        if (isset($result['reason'])) {
            $update_data['reason'] = $result['reason'];
        }
        if (isset($result['refund_amount'])) {
            $update_data['refund_amount'] = $result['refund_amount'];
        }
        if (isset($result['refund_time'])) {
            $update_data['refund_time'] = $result['refund_time'];
        }
        if (isset($result['detail_id'])) {
            $result['send_listid']      = $result['detail_id'];
            $update_data['send_listid'] = $result['detail_id'];
        }
        if ($result['status'] == 'RECEIVED') {
            $update_data['status'] = 1;
        }
        $db::update($update_data, $map);
        //如果是需要返回是否发送成功,而且状态不在领取成功和待领取状态 则直接返回false
        if ($return_success_status && !in_array($update_data['red_packet_status'], ['RECEIVED', 'SENT'])) {
            return false;
        }
        return $update_data;
    }

    /**
     * 发送现金红包
     * @access public
     * @param array $data 数据
     * @return bool|array
     * @throws Exception
     */
    public function send_red_packet($data)
    {
        $db = new WechatRedPacketOrder();
        //先查询是否存在订单记录 如果存在则直接取出来
        $map   = [
            ['bid', '=', $data['bid']],
            ['wxappid', '=', $data['wxappid']],
            ['mch_billno', '=', $data['mch_billno']]
        ];
        $order = $db->where($map)->find();
        if (!$order) {
            try {
                //TODO 要先校验$data的数据合法性 先创建订单记录
                if (!empty($data['openid'])) {
                    $data['re_openid'] = $data['openid'];
                }
                if (!empty($data['sendname'])) {
                    $data['send_name'] = $data['sendname'];
                }
                $order = $data; //先赋值给$order
                $db->save($data);
            } catch (Exception $e) {
                $this->message = $e->getMessage();
                return false;
            }
        } else {
            //状态等于1不重复执行转账,直接返回数据
            //如果是存在风险 则抛出异常 不允许重复发送
            if (stripos($order['reason'], "风险") !== false) {
                $this->message = '发放失败，此请求可能存在风险，已被系统拦截';
                return false;
            }
            if ($order['status'] === 1) {
                return $order;
            }
        }
        $post_data = [
            'mch_billno'   => $order['mch_billno'],
            're_openid'    => $order['re_openid'],
            'total_amount' => $order['total_amount'],
            'remark'       => $order['remark'],
            'act_name'     => $order['act_name'],
            'client_ip'    => $order['client_ip'],
            'send_name'    => $order['send_name'],
            'wishing'      => $order['wishing'],
            'total_num'    => $order['total_num'],
            'scene_id'     => $order['scene_id'],
            'risk_info'    => $order['risk_info'],
            'sub_mch_id'   => $order['sub_mch_id'],
            'msgappid'     => $order['msgappid'],
        ];
        if (!empty($post_data['sub_mch_id'])) {
            $post_data['consume_mch_id'] = $post_data['sub_mch_id'];
        }
        $key = 'wechat_balance_status:' . $data['bid'];
        if (cache($key)) {
            $this->message = '当前处于余额不足状态,无法充值';
            return false;
        }
        $pay    = weixin($order['wxappid'])::WePayRedpack();
        $result = $pay->create($post_data);
        $map    = [
            ['bid', '=', $data['bid']],
            ['guid', '=', $data['guid']]
        ];
        if ($result === false) {
            // 更新转账记录表
            $update_data   = [
                'status' => -1,
                'reason' => $pay->errMsg,
            ];
            $this->status  = $pay->errCode;
            $this->message = $pay->errMsg;
            $update        = $db::update($update_data, $map);
            switch ($pay->errCode) {
                case 'NOTENOUGH':
                    cache($key, 1);
                    //判断是否余额不足,通知对应的管理员
                    $this->not_enough_notify($order['bid']);
                    break;
                default:
            }
            return false;
        } else {
            // 更新转账记录表
            $update_data = [
                'send_listid'          => $result['send_listid'],
                'red_packet_send_time' => format_timestamp(),
                'status'               => 1,
            ];
            $update      = $db::update($update_data, $map);
            return $result;
        }
    }


}