<?php

namespace app\common\service;

use app\model\PayChannel;
use app\model\PayParameter;
use app\model\PayScene;
use Exception;
use Pays\Pays;
use Throwable;

/**
 * @library AlipayService
 * <AUTHOR>
 * @date 2018/05/24 13:23
 *

 */
class AlipayService
{
    public static $appid;

    /**
     * 获取支付宝user_id
     * @access public
     * @param string $bid 商家标志
     * @return array
     * @throws Exception
     */
    public static function get_open_id(string $bid)
    {
        $db_pay_scene      = new PayScene();
        $drive             = 'alipay';
        $scene             = 'oauth';
        $scene_id          = $db_pay_scene->get_scene_id($drive, $scene);
        $db_pay_parameter  = new PayParameter();
        $pay_parameter     = $db_pay_parameter->get_parameter_by_scene_id_or_guid($bid, $scene_id);
        $channel_id        = $pay_parameter['channel_id'];
        $db_pay_channel    = new PayChannel();
        $map               = [['id', '=', $channel_id],];
        $pay_channel_info  = $db_pay_channel->where($map)->findOrFail();
        $channel_parameter = $pay_channel_info['channel_parameter'];
        $appid             = $channel_parameter['app_id'];
        $openid_cache_key  = $appid . '_openid';//先从cookies中获取是否存在openid,存在则直接返回
        $openid            = cookies($openid_cache_key);
        if ($openid) {
            return ['appid' => $appid, 'openid' => $openid];
        }
        $request      = request();
        $params       = $request->param();
        $redirect_url = $request->url(true);
        if (empty($params['auth_code'])) {
            //链接上无auth_code参数则构造授权链接
            $oauth_url = 'https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=' . $appid . '&scope=auth_base&redirect_uri=' . urlencode($redirect_url);
            redirect($oauth_url);
        }
        //有参数则进行解析code 获取openid
        $config    = [$drive => $channel_parameter];
        $pay       = new Pays($config);
        $auth_code = $params['auth_code'];
        $data      = ['grant_type' => 'authorization_code', 'code' => $auth_code];
        $result    = [];
        $msg       = '';
        $notify    = true;
        try {
            $result = $pay->driver($drive)->gateway($scene)->apply($data);
        } catch (Exception|Throwable $e) {
            $msg = $e->getMessage();
            switch ($e->getCode()) {
                case 40002:
                    //授权码code无效
                    $notify = false;
                    break;
                default;
                    break;
            }
        }
        if (empty($result['openid'])) {
            $referer    = $request->header('referer');
            $user_agent = $request->header('user_agent');
            wr_log('openid获取失败:appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE) . ',msg:' . $msg, $notify);
            logToFile($params);
            error('获取用户身份失败:' . $msg . ',请退出网页后重试!');
        }
        $openid = $result['openid'];
        $exp    = 3600 * 24;
        cookies($openid_cache_key, $openid, $exp);
        redirect(substr($redirect_url, 0, strrpos($redirect_url, '&auth_code')));
    }
}