<?php

namespace app\common\service;

use app\model\CropWechatAuth;
use Exception;
use xieyongfa\work_weixin\WorkWeixin;

class WorkWeixinService
{
    public static $bid;
    protected static $instance = null;
    public $code;
    public $msg;
    public $success = false;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     * @param string $bid_or_config 商家标识
     * @return $this|WorkWeixin
     * @throws Exception
     */
    public static function get_instance($bid_or_config = [])
    {
        if ($bid_or_config && is_string($bid_or_config)) {
            self::$bid = $bid_or_config;
        }
        $sn = get_instance_sn($bid_or_config);
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            $business_config     = is_string($bid_or_config) ? self::get_config() : $bid_or_config;
            self::$instance[$sn] = new WorkWeixin($business_config);
        }
        return self::$instance[$sn];
    }


    public function __wakeup()
    {
        self::$instance = $this;
    }


    public static function get_config()
    {
        if (!self::$bid) {
            return [];
        }
        $config = get_config_by_bid(self::$bid);
        if (empty($config['crop_wechat_appid'])) {
            throw new Exception('商户未授权企业微信');
        }
        $crop_wechat_appid   = $config['crop_wechat_appid'];
        $db_crop_wechat_auth = new CropWechatAuth();
        $map                 = [
            ['auth_corpid', '=', $crop_wechat_appid]
        ];
        $auth_info           = $db_crop_wechat_auth->where($map)->findOrFail();
        return [
            'platform_corpid' => $auth_info['platform_corpid'],
            'suite_id'        => $auth_info['suite_id'],
            'auth_corpid'     => $auth_info['auth_corpid'],
            'agent_id'        => $auth_info['agent_id'],
        ];

    }

    protected function __clone()
    {
        //disallow clone
    }

}

if (!function_exists('work_weixin')) {
    /**
     * 实例化微信SDK
     * @param string|array $bid_or_config 商家唯一标识
     * @return WorkWeixinService|null|WorkWeixin
     * @throws Exception
     */
    function work_weixin($bid_or_config)
    {
        return WorkWeixinService::get_instance($bid_or_config);
    }
}