<?php

namespace app\common\service;

use app\common\exceptions\NotNotifyException;
use app\model\Business;
use app\model\SmsChannel;
use app\model\SmsCodeNote;
use app\model\SmsSendNote;
use Exception;
use SendSms\Contracts\SendSmsInterface;
use SendSms\SendSms;

class SmsService
{
    public static $sms_channel_config;
    public static $bid;
    protected static $instance = null;
    public $status;
    public $message;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     * @param null|integer $bid 商家
     * @return $this
     * @throws Exception
     */
    public static function get_instance($bid = null)
    {
        self::$bid      = $bid ?: tools()::get_empty_guid();
        $sms_channel_id = self::get_sms_channel_id(self::$bid);
        if (empty($sms_channel_id)) {
            throw new Exception('短信通道尚未配置');
        }
        $sn = get_instance_sn($sms_channel_id . self::$bid);
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    /**
     * 获取短信通道配置
     * @return string
     * @throws Exception
     */
    protected static function get_sms_channel_id($bid)
    {
        if (tools()::is_empty_guid($bid)) {
            return get_system_config('sms_channel_id');
        }
        $config = get_config_by_bid($bid);
        return $config['sms_channel_id'];
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }

    /**
     * 发送短信验证码
     * @access public
     * @param string $mobile 手机号
     * @return mixed
     * @throws Exception
     */
    public function send_sms_code($mobile)
    {
        if ($mobile == '18603047034') {
            sleep(5);
            success('发送成功,验证码8888');
        }
        if (!tools()::is_mobile($mobile)) {
            error('手机号格式不正确');
        }
        $ip = tools()::get_client_ip();
        if ($ip == '0.0.0.0') {
            error('验证码发送失败');
        }
        $bid                                   = self::$bid;
        $config                                = get_config_by_bid($bid);
        $mobile_or_ip_per_day_sms_code_max_num = $config['mobile_or_ip_per_day_sms_code_max_num'];
        $mobile_cache_key                      = 'sms_code_mobile:' . $mobile;
        $ip_cache_key                          = 'sms_code_ip:' . $ip;
        if ($mobile_or_ip_per_day_sms_code_max_num <= 9999 && (cache($mobile_cache_key) || cache($ip_cache_key))) {
            error('请不要频繁获取验证码');
        }
        //手机号一天之内最多发送3条短信
        $db                = new SmsCodeNote();
        $map               = [['mobile', '=', $mobile]];
        $count             = $db->whereDay('sending_time')->where($map)->count();
        $debug_mobile_list = get_system_config('debug_mobile_list');
        $debug_mobile_list = explode(',', $debug_mobile_list);
        if ($count >= $mobile_or_ip_per_day_sms_code_max_num && !in_array($mobile, $debug_mobile_list)) {
            error('同一手机号一天最多获取' . $mobile_or_ip_per_day_sms_code_max_num . '次验证码');
        }
        //一个IP一天只能发送3条短信
        $map   = [['ip', '=', $ip]];
        $count = $db->whereDay('sending_time')->where($map)->count();
        if ($count >= $mobile_or_ip_per_day_sms_code_max_num && !in_array($mobile, $debug_mobile_list)) {
            error('同一ip一天最多获取' . $mobile_or_ip_per_day_sms_code_max_num . '次验证码');
        }
        //开始发送,默认发送6位验证码
        $sms_code = rand(100000, 999999);
        $data     = [
            'guid'         => create_guid(),
            'bid'          => self::$bid,
            'mobile'       => $mobile,
            'ip'           => $ip,
            'code'         => $sms_code,
            'sending_time' => format_timestamp()
        ];
        //记录到数据库
        $db->save($data);
        //记录成功之后 发送验证码
        $result = send_sms('验证码：' . $sms_code . '，5分钟内有效。如非本人操作，请忽略。', $mobile, self::$bid);
        if ($result === false) {
            error('短信发送失败');
        }
        //发送完毕缓存60秒 防止刷
        cache($mobile_cache_key, time(), 60);
        cache($ip_cache_key, time(), 60);
        success('发送成功');
    }

    /**
     * 校验短信验证码
     * @access public
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @return bool|integer
     * @throws Exception
     */
    public function verify_sms_code($mobile, $code)
    {
        if ($mobile == '18603047034') {
            sleep(5);
            return true;
        }
        $db             = new SmsCodeNote();
        $map            = [
            ['bid', '=', self::$bid],
            ['mobile', '=', $mobile],
            ['status', '=', 0], //未使用的验证码
            ['code', '=', $code], //验证码
        ];
        $cache_key      = __FUNCTION__ . ':' . $mobile;
        $total_fail_num = (int)cache($cache_key);
        if ($total_fail_num > 10) {
            error('当前手机号暂不支持校验验证码,请联系在线客服');
        }
        $note_guid = $db->whereTime('sending_time', '-5 minute')->order(['sending_time' => 'DESC'])->where($map)->value('guid');
        if (empty($note_guid)) {
            cache($cache_key, $total_fail_num + 1, 300); //只缓存5分钟
            //todo 后续要防止猜测验证码, 例如增加mobile缓存  一个手机号尝试错了3次验证码 则当前手机号验证码失效 即便输入正确
            throw new NotNotifyException('验证码输入错误');
        }
        cache($cache_key, null);
        $map = [
            ['bid', '=', self::$bid],
            ['guid', '=', $note_guid],
        ];
        $db::update(['status' => 1], $map); //标识为已使用
        return true;
    }

    /**
     * 发送短信
     * @access public
     * @param array $data 发送内容
     * @return bool|integer
     * @throws Exception
     */
    public function send_sms(array $data)
    {
        $mobile  = $data['mobile'];
        $content = $data['content'];
        if (!tools()::is_mobile($mobile)) {
            $this->message = '短信发送失败,内容:' . $content . ',原因:手机号[' . $mobile . ']不合法:';
            return false;
        }
        $bid            = self::$bid;
        $sms            = self::get_send_sms_instance($bid);
        $send_note_guid = !empty($data['guid']) ? $data['guid'] : create_guid();
        $sign_name      = '';
        $content        = str_replace('【', '[', $content); //短信不能双签名
        $content        = str_replace('】', ']', $content); //短信不能双签名
        $content        = $sms->convert_content($content);
        $fee            = $sms->calculate_fee($content);
        $db             = new SmsSendNote();
        if (!tools()::is_empty_guid($bid)) {
            $config                  = get_config_by_bid($bid);
            $sms_do_not_disturb_time = $config['sms_do_not_disturb_time'];
            $sign_name               = $config['signature'];
            if ($sms_do_not_disturb_time > 0) {
                //如果设置了短信免打扰时间 则查询是否符合条件
                $map   = [
                    ['bid', '=', $bid],
                    ['mobile', '=', $mobile],
                    ['content', '=', $content],
                    ['status', '=', 1],
                    ['create_time', '>=', date('Y-m-d H:i:00', strtotime("-$sms_do_not_disturb_time seconds"))],
                ];
                $order = $db->where($map)->count();
                if ($order > 0) {
                    $this->message = '短信发送至' . $mobile . '失败,内容:' . $content . ',原因:' . $sms_do_not_disturb_time . '秒内有发送成功相同内容';
                    return false;
                }
            }
        }
        //1小时没有发送成功的 则看之前是否有创建相同订单
        $sms_map = [
            ['bid', '=', $bid],
            ['mobile', '=', $mobile],
            ['guid', '=', $send_note_guid],
        ];
        $order   = $db->field(['status'])->where($sms_map)->findOrEmpty();
        if (!$order->isEmpty() && isset($order['status']) && $order['status'] == 1) {
            $this->message = '当前GUID不允许重复发送';
            return false;
        }
        if ($order->isEmpty()) {
            if (!tools()::is_empty_guid($bid)) {
                //非系统短信则扣除商家可用短信
                $db_business   = new Business();
                $map           = [['guid', '=', $bid]];
                $business_info = $db_business->where($map)->field(['sms_num', 'credit_sms'])->findOrFail();
                $total_balance = (int)$business_info['sms_num'];
                $credit_sms    = (int)$business_info['credit_sms'];
                if ($credit_sms > 0) {
                    $total_balance += $credit_sms;
                }
                if ($total_balance < $fee) {
                    $msg           = '商家当前可用短信不足~';
                    $update_data   = [
                        'status'  => -1,
                        'message' => $msg,
                    ];
                    $update        = $db::update($update_data, $map);
                    $this->status  = -1;
                    $this->message = $msg;
//                    wr_log('短信发送至' . $mobile . '失败,内容:' . $content . ',原因:商家可用短信不足');
                    return false;
                }
                $db_business->where($map)->setDec('sms_num', $fee);
                $need_alarm_balance_array = [200, 100, 50, 20];
                foreach ($need_alarm_balance_array as $need_alarm_balance) {
                    if ($total_balance >= $need_alarm_balance && ($total_balance - $fee) < $need_alarm_balance) {
                        //短信余额不足告警
                        $data = [
                            'url'         => 'http://' . config('app.app_host_domain') . '/admin/sms/index?bid=' . $bid . '&business_guid=' . $bid,
                            'title'       => '【短信不足告警】',
                            'name'        => '【短信不足告警】',
                            'create_time' => format_timestamp(),
                            'user'        => '当前剩余:' . $total_balance . '条',
                            'detail'      => '点击在线充值短信',
                            'remark'      => '如有疑问请联系客服',
                        ];
                        notify()->set_key_name(NotifyService::Notice)->limit_business()->set_data($data)->set_bid($bid)->send();
                        $db_business   = new Business();
                        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
                        wr_log($business_info['id'] . '-' . $business_info['account'] . '-' . $business_info['business_name'] . '短信不足:' . $need_alarm_balance . '条通知成功', 1);
                        break; //满足一个节点则退出循环
                    }
                }
            }
            $config      = self::get_sms_channel_config($bid);
            $channel     = $config['driver'];
            $insert_data = [
                'bid'       => $bid,
                'guid'      => $send_note_guid,
                'channel'   => $channel,
                'mobile'    => $mobile,
                'content'   => $content,
                'sign_name' => $sign_name,
                'status'    => 0,//等待发送
                'fee'       => $fee,//消耗条数
            ];
            $db->save($insert_data);
        }
        $result = $sms->send($content, $mobile, $sign_name);
        if ($result['status'] !== 0) {
            // 更新发送记录表
            $update_data   = [
                'status'  => -1,
                'code'    => $result['code'],
                'message' => $result['message'],
            ];
            $this->status  = $result['code'];
            $this->message = $result['message'];
            $update        = $db::update($update_data, $sms_map);
            wr_log('短信发送至' . $mobile . '失败,内容:' . $content . ',原因:' . $result['message'], 1);
            return false;
        } else {
            // 更新短信发送记录
            $update_data = [
                'status'      => 1,
                'request_id'  => $result['request_id'], //阿里云返回的请求GUID
                'out_id'      => $result['out_id'],//阿里云返回的 也可以作为发送唯一标识
                'out_send_id' => $result['out_send_id'],//阿里云中BizId,外部发送唯一标识
                'code'        => $result['code'],
                'message'     => $result['message'],
                'response'    => $result['response']
            ];
            $update      = $db::update($update_data, $sms_map);
            return $result;
        }
    }

    /**
     * 获取短信通道配置
     * @return SendSmsInterface
     * @throws Exception
     */
    protected static function get_send_sms_instance($bid)
    {
        $config = self::get_sms_channel_config($bid);
        return SendSms::driver($config['driver'], $config['channel_parameter']);
    }

    /**
     * 获取短信通道配置
     * @return array
     * @throws Exception
     */
    protected static function get_sms_channel_config($bid)
    {
        if (empty(self::$sms_channel_config[$bid])) {
            $db                             = new SmsChannel();
            self::$sms_channel_config[$bid] = $db->get_sms_channel_config(self::get_sms_channel_id($bid));
            if (!tools()::is_empty_guid($bid)) {
                $config                                                           = get_config_by_bid($bid);
                self::$sms_channel_config[$bid]['channel_parameter']['sign_name'] = $config['signature'];
            }
        }
        return self::$sms_channel_config[$bid];
    }

    protected function __clone()
    {
        //disallow clone
    }
}