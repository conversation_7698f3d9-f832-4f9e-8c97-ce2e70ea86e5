<?php

namespace app\common\service;


use app\model\NotifyTemplateDefault;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Throwable;

class NotifyService
{
    protected static array|null $instance = null;
    protected string $key_name;
    protected string|null $bid = null;
    protected string|null $member_mobile = null;
    protected array|string|null $user_guid = null;
    protected string|null $member_guid = null;
    protected array $data = [];
    protected array $map = [];
    protected array $limit_provider_array = [];

    const SystemProvider = 'system';
    const BusinessProvider = 'business';
    const MemberProvider = 'member';

    const YkyMemberProvider = 'yky_member';
    const AgentProvider = 'agent';

    const Notice = 'Notice';
    const SubmitGoodsOrder = 'SubmitGoodsOrder';
    const SendOutGoodsOrder = 'SendOutGoodsOrder';
    const RegisteredBusinessSuccessfully = 'RegisteredBusinessSuccessfully';
    const CodeSendSuccessfully = 'CodeSendSuccessfully';
    const BusinessUserLoginSuccessfully = 'BusinessUserLoginSuccessfully';
    const BusinessApplyTrialSuccessfully = 'BusinessApplyTrialSuccessfully';
    const PrepareSendOutGoodsOrder = 'PrepareSendOutGoodsOrder';
    const DistributorApply = 'DistributorApply';
    const MemberBrokerageCashApply = 'MemberBrokerageCashApply';
    const MemberDeductMoney = 'MemberDeductMoney';
    const MemberAddMoney = 'MemberAddMoney';
    const MemberExpiredRemind = 'MemberExpiredRemind';
    const CodeExpiredRemind = 'CodeExpiredRemind';
    const AuditSuccess = 'AuditSuccess';
    const AuditFailure = 'AuditFailure';
    const PermissionChanged = 'PermissionChanged';
    const RefreshHomeData = 'RefreshHomeData';
    const PickUpExpress = 'PickUpExpress'; //取件通知,自用

    public function limit_agent(): static
    {
        $this->limit_provider_array[] = self::AgentProvider;
        return $this;
    }

    public function limit_business(): static
    {
        $this->limit_provider_array[] = self::BusinessProvider;
        return $this;
    }

    public function limit_member(): static
    {
        $this->limit_provider_array[] = self::MemberProvider;
        return $this;
    }

    public function limit_yky_member(): static
    {
        $this->limit_provider_array[] = self::YkyMemberProvider;
        return $this;
    }

    public function limit_system(): static
    {
        $this->limit_provider_array[] = self::SystemProvider;
        return $this;
    }

    /**
     * 设置通知key_name
     * @return $this
     */
    public function set_key_name($key_name): static
    {
        //必须先调用此方法,否则会清空其他属性
        $this->clear();
        $this->key_name = $key_name;
        return $this;
    }

    public function set_member_mobile($member_mobile): static
    {
        if (!empty($member_mobile)) {
            $this->member_mobile = $member_mobile;
        }
        return $this;
    }

    public function set_member_guid($member_guid): static
    {
        if (!empty($member_guid)) {
            $this->member_guid = $member_guid;
        }
        return $this;
    }

    public function set_user_guid($user_guid): static
    {
        if (!empty($user_guid)) {
            $this->user_guid = $user_guid;
        }
        return $this;
    }

    public function set_bid($bid): static
    {
        if (!empty($bid)) {
            $this->bid = $bid;
        }
        return $this;
    }

    public function set_data($data = []): static
    {
        if (!empty($data)) {
            $data = tools()::object2array($data);
            foreach ($data as $key => $val) {
                if (is_array($val)) {
                    unset($data[$key]);
                }
            }
            $this->data = $data;
        }
        return $this;
    }

    public function set_map($map = []): static
    {
        if (!empty($map)) {
            $this->map = $map;
        }
        return $this;
    }

    /**
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function send(): bool
    {
        if (empty($this->map) && empty($this->data)) {
            throw new Exception('发送内容和发送条件不能都为空');
        }
        $db_notify_template_default = new NotifyTemplateDefault();
        $map                        = [
            ['key_name', '=', $this->key_name],
            //           ['status', '=', 1]
        ];
        if (!empty($this->limit_provider_array)) {
            //限定了通知方式的仅通知对应角色 用于仅通知代理等情况的通用通知
            $map[] = ['provider', 'IN', $this->limit_provider_array];
        }
        $notify_template_list = $db_notify_template_default->where($map)->select()->toArray();
        $all_job_data         = [];
        foreach ($notify_template_list as $notify_template) {
            $all_job_data[] = [
                'map'           => $this->map,
                'data'          => $this->data,
                'user_guid'     => $this->user_guid,
                'member_mobile' => $this->member_mobile,
                'bid'           => $this->bid,
                'member_guid'   => $this->member_guid,
                'template_id'   => $notify_template['id'],
            ];
        }
        job()->set_amqp_connections()->set_query_status(false)->set_job_name('Notify@send')->push_job($all_job_data);
        $this->clear();
        return true;
    }

    //初始化数据,避免污染
    protected function clear()
    {
        $this->data                 = [];
        $this->map                  = [];
        $this->key_name             = '';
        $this->bid                  = null;
        $this->user_guid            = null;
        $this->member_guid          = null;
        $this->member_mobile        = null;
        $this->limit_provider_array = [];
    }

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     * @return $this
     * @throws Exception
     */
    public static function get_instance(): NotifyService
    {
        $sn = get_instance_sn();
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    //   public function __wakeup()
    //   {
    //       self::$instance = $this;
    //   }

    protected function __clone()
    {
        //disallow clone
    }
}