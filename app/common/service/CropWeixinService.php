<?php

namespace app\common\service;

use app\model\CropWechatConfig;
use Exception;
use Stoneworld\Wechat\AccessToken;
use Stoneworld\Wechat\Agent;
use Stoneworld\Wechat\Auth;
use Stoneworld\Wechat\Authorize;
use Stoneworld\Wechat\Broadcast;
use Stoneworld\Wechat\Cache;
use Stoneworld\Wechat\Card;
use Stoneworld\Wechat\Chat;
use Stoneworld\Wechat\Crypt;
use Stoneworld\Wechat\Group;
use Stoneworld\Wechat\Input;
use Stoneworld\Wechat\Js;
use Stoneworld\Wechat\Media;
use Stoneworld\Wechat\Menu;
use Stoneworld\Wechat\MenuItem;
use Stoneworld\Wechat\Message;
use Stoneworld\Wechat\Server;
use Stoneworld\Wechat\Servicer;
use Stoneworld\Wechat\Tag;
use Stoneworld\Wechat\Url;
use Stoneworld\Wechat\User;
use Throwable;

/**
 * 加载缓存器
 *
 * Class We
 * @library WeChatDeveloper
 * <AUTHOR>
 * @date 2018/05/24 13:23
 *
 * ----- WeChat -----
 * @method AccessToken AccessToken($appid, $secret) AccessToken
 * @method Agent Agent($appid, $secret) Agent
 * @method Auth Auth($appid, $secret) Auth
 * @method Authorize Authorize($appid, $secret) Authorize
 * @method Broadcast Broadcast($appid, $secret) Broadcast
 * @method Cache Cache($appid, $secret) Cache
 * @method Card Card($appid, $secret) Card
 * @method Chat Chat($appid, $secret) Chat
 * @method Servicer Servicer($appid, $secret) Servicer
 * @method Crypt Crypt($appid, $secret) Crypt
 * @method Group Group($appid, $secret) Group
 * @method Input Input($appid, $secret) Input
 * @method Js Js($appid, $secret) Js
 * @method Media Media($appid, $secret) Media
 * @method Menu Menu($appid, $secret) Menu
 * @method MenuItem MenuItem($appid, $secret) MenuItem
 * @method Message Message($appid, $secret) Message
 * @method Server Server($appid, $secret) Server
 * @method Tag Tag($appid, $secret) Tag
 * @method Url Url($appid, $secret) Url
 * @method User User($appid, $secret) User
 *
 */
class CropWeixinService
{
    public static $appid;
    public static $config;
    public static $agent_id;
    protected static $instance = null;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 获取企业微信openid
     * @access public
     * @param string $appid APPID
     * @return array
     * @throws Exception
     */
    public static function getOpenId($appid = null)
    {
        $request      = request();
        $redirect_url = $request->url(true);
        $domain       = $request->domain();
        $param        = $request->param();
        $redirect_url = str_replace($domain . $domain, $domain, $redirect_url);
        $workweixin   = self::get_instance();
        $auth         = $workweixin->Auth();
        $key          = 'openid_' . $workweixin::$appid;
        $openid       = cookies($key);
        if ($openid) {
            return ['appid' => $workweixin::$appid, 'openid' => $openid];
        }
        //不存在缓存则进入授权逻辑
        if (empty($param['code'])) {
            $result = $auth->url($redirect_url);
            redirect($result);
        } else {
            $referer    = $request->header('referer');
            $user_agent = $request->header('user_agent');
            try {
                //            $result     = $auth->auth();
                $result = $auth->user();
                if (isset($result['OpenId'])) {
                    $openid = $result['OpenId'];
                } elseif (isset($result['UserId'])) {
                    $result = $auth->toOpenId($result['UserId']);
                    $openid = $result['openid'];
                } elseif (!empty($result['DeviceId']) && empty($result['OpenId'])) {
                    wr_log('OpenId返回是空,appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer);
                    error('用户身份获取失败,请尝试使用个人微信(非企业微信)APP访问该页面!');
                } else {
                    //{"errcode":40029,"errmsg":"invalid code, hint: [1716543246419203381190680], from ip: *************, more info at https:\/\/open.work.weixin.qq.com\/devtool\/query?e=40029"}
                    $msg = '用户身份获取失败:错误码:' . $result['errcode'] . ',请退出网页重新进入!';
                    wr_log('UserId获取失败,appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer, 1);
                    error($msg);
                }
            } catch (Exception|Throwable $e) {
                $err_msg  = $e->getMessage();
                $err_code = $e->getCode();
                switch ($err_code) {
                    case 40029:
                        wr_log('企业微信授权发现40029错误,code重复使用, 正在重定向:' . $redirect_url);
                        redirect(tools()::remove_after_code_url($redirect_url));
                        break;
                    case 45036:
                        $msg = '系统繁忙,请退出网页重新进入!';
                        wr_log('UserId获取失败45036错误,user_agent:' . $user_agent . ',referer:' . $referer);
                        error($msg);
                        break;
                    case 0:
                        error($err_msg);
                        break;
                    default:
                        wr_log('企业微信授权发现错误,$err_code=' . $err_code . 'appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer, 1);
                        error($err_msg);
                }
            }
        }
        cookies($key, $openid, 3600);
        $url = tools()::remove_after_code_url($redirect_url);
        redirect($url);
    }

    /**获取实例
     * @param string $appid 公众号标识
     * @param string $agent_id 应用ID
     * @return  $this
     * @throws Exception
     */
    public static function get_instance($appid = null, $agent_id = null)
    {
        $default_config = config('app.crop_wechat_config');
        $appid          = is_null($appid) ? $default_config['defalut_appid'] : $appid;
        $agent_id       = is_null($agent_id) ? $default_config['defalut_agentid'] : $agent_id;
        self::$appid    = $appid;
        self::$agent_id = $agent_id;
        $sn             = strtolower(md5($appid . $agent_id));
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            // 写入
            Cache::setter(function ($key, $value, $lifetime) {
                return cache($key, $value, $lifetime);
            });
            // 读取
            Cache::getter(function ($key) {
                return cache($key);
            });
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }

    /**
     * 静态魔术加载方法
     * @param string $name 静态类名
     * @param array $arguments 参数集合
     * @return mixed
     * @throws Exception
     */
    public function __call($name, $arguments)
    {
        $class = '\\Stoneworld\\Wechat\\' . $name;
        if (!class_exists($class)) {
            throw new Exception($class . '不存在');
        }
        $config = self::get_crop_wechat_config();
        if (in_array($name, ['Server'])) {
            return new $class($config);
        } else {
            return new $class($config['appid'], $config['appsecret']);
        }
    }

    /**
     * 获取微信配置
     * @access public
     * @return array
     * @throws Exception
     */
    protected static function get_crop_wechat_config()
    {
        $key    = 'crop_wechat:config:' . self::$appid . ':' . self::$agent_id;
        $exp    = 3600 * 24;
        $config = cache($key);
        if (!$config) {
            $map                   = [
                ['appid', '=', self::$appid],
                ['agentid', '=', self::$agent_id]
            ];
            $db_crop_wechat_config = new CropWechatConfig();
            $config                = $db_crop_wechat_config->where($map)->findOrFail();
            $config                = tools()::object2array($config);
            cache($key, $config, $exp);
        }
        return $config;
    }

    protected function __clone()
    {
        //disallow clone
    }
}