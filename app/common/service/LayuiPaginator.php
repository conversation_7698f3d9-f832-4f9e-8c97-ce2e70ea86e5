<?php

namespace app\common\service;

use think\Paginator;

/**
 * Layui 分页驱动
 */
class LayuiPaginator extends Paginator
{

    /**
     * 渲染分页html
     * @return mixed
     */
    public function render()
    {
        if ($this->hasPages()) {
            if ($this->simple) {
                return sprintf(
                    '<ul class="pager">%s %s</ul>',
                    $this->getPreviousButton(),
                    $this->getNextButton()
                );
            } else {
                return sprintf(
                    '<div class="layui-laypage">%s %s %s %s %s %s %s</div>',
                    $this->getTotal($this->total),
                    $this->getFirstButton(),
                    $this->getPreviousButton(),
                    $this->getLinks(),
                    $this->getNextButton(),
                    $this->getLastButton(),
                    $this->goPage()
                );
            }
        }
    }

    /**
     * 上一页按钮
     * @param string $text
     * @return string
     */
    protected function getPreviousButton(string $text = "&laquo;"): string
    {

        if ($this->currentPage() <= 1) {
            return $this->getDisabledTextWrapper($text);
        }

        $url = $this->url(
            $this->currentPage() - 1
        );

        return $this->getPageLinkWrapper($url, $text);
    }

    /**
     * 生成一个禁用的按钮
     *
     * @param string $text
     * @return string
     */
    protected function getDisabledTextWrapper(string $text): string
    {
        return '<a class="layui-laypage-prev layui-disabled" >' . $text . '</a>';
    }

    /**
     * 获取页码对应的链接
     *
     * @access protected
     * @param int $page
     * @return string
     */
    protected function url(int $page): string
    {
        if ($page <= 0) {
            $page = 1;
        }
        if (strpos($this->options['path'], '[PAGE]') === false) {
            $parameters = [$this->options['var_page'] => $page];
            $path       = $this->options['path'];
        } else {
            $parameters = [];
            $path       = str_replace('[PAGE]', $page, $this->options['path']);
        }
        $request_uri = $_SERVER["REQUEST_URI"];
        $parse_url   = $request_uri . (strpos($request_uri, '?') ? '' : "?");
        $parse       = parse_url($parse_url);
        if (isset($parse['query'])) {
            parse_str($parse['query'], $query);
            $parameters = array_merge($query, $parameters);//此时用新$page覆盖了query中$page变量
        }
        $parameters = array_merge($this->options['query'], $parameters);
        if (!empty($parameters)) {
            $path .= '?' . http_build_query($parameters, '', '&');
        }
        return $path . $this->buildFragment();
    }

    /**
     * 生成普通页码按钮
     *
     * @param string $url
     * @param string $page
     * @return string
     */
    protected function getPageLinkWrapper(string $url, string $page): string
    {
        if ($this->currentPage() == $page) {
            return $this->getActivePageWrapper($page);
        }

        return $this->getAvailablePageWrapper($url, $page);
    }

    /**
     * 生成一个激活的按钮
     *
     * @param string $text
     * @return string
     */
    protected function getActivePageWrapper(string $text): string
    {
        return '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' . $text . '</em></span>';
    }

    /**
     * 生成一个可点击的按钮
     *
     * @param string $url
     * @param string $page
     * @return string
     */
    protected function getAvailablePageWrapper(string $url, string $page): string
    {
        return '<li style="display: inline"><a href="' . htmlentities($url) . '">' . $page . '</a></li>';
    }

    /**
     * 下一页按钮
     * @param string $text
     * @return string
     */
    protected function getNextButton(string $text = '&raquo;'): string
    {
        if (!$this->hasMore) {
            return $this->getDisabledTextWrapper($text);
        }

        $url = $this->url($this->currentPage() + 1);

        return $this->getPageLinkWrapper($url, $text);
    }

    /**
     *  生成总条数
     * @param $num
     * @return string
     */
    protected function getTotal($num)
    {
        return '<span class="layui-laypage-count">共' . $num . '条</span>';
    }

    /**
     * 第一页按钮
     * @param string $text
     * @return string
     */
    protected function getFirstButton(string $text = "首页"): string
    {

        if ($this->currentPage() <= 1) {
            return $this->getDisabledTextWrapper($text);
        }

        $url = $this->url(1);

        return $this->getPageLinkWrapper($url, $text);
    }

    /**
     * 页码按钮
     * @return string
     */
    protected function getLinks(): string
    {
        if ($this->simple) {
            return '';
        }

        $block = [
            'first'  => null,
            'slider' => null,
            'last'   => null,
        ];

        $side   = 3;
        $window = $side * 2;

        if ($this->lastPage < $window + 6) {
            $block['first'] = $this->getUrlRange(1, $this->lastPage);
        } elseif ($this->currentPage <= $window) {
            $block['first'] = $this->getUrlRange(1, $window + 2);
            $block['last']  = $this->getUrlRange($this->lastPage - 1, $this->lastPage);
        } elseif ($this->currentPage > ($this->lastPage - $window)) {
            $block['first'] = $this->getUrlRange(1, 2);
            $block['last']  = $this->getUrlRange($this->lastPage - ($window + 2), $this->lastPage);
        } else {
            $block['first']  = $this->getUrlRange(1, 2);
            $block['slider'] = $this->getUrlRange($this->currentPage - $side, $this->currentPage + $side);
            $block['last']   = $this->getUrlRange($this->lastPage - 1, $this->lastPage);
        }

        $html = '';

        if (is_array($block['first'])) {
            $html .= $this->getUrlLinks($block['first']);
        }

        if (is_array($block['slider'])) {
            $html .= $this->getDots();
            $html .= $this->getUrlLinks($block['slider']);
        }

        if (is_array($block['last'])) {
            $html .= $this->getDots();
            $html .= $this->getUrlLinks($block['last']);
        }

        return $html;
    }

    /**
     * 批量生成页码按钮.
     *
     * @param array $urls
     * @return string
     */
    protected function getUrlLinks(array $urls): string
    {
        $html = '';

        foreach ($urls as $page => $url) {
            $html .= $this->getPageLinkWrapper($url, $page);
        }

        return $html;
    }

    /**
     * 生成省略号按钮
     *
     * @return string
     */
    protected function getDots(): string
    {
        return $this->getDisabledTextWrapper('...');
    }

    /**
     * 最后一页按钮
     * @param string $text
     * @return string
     */
    protected function getLastButton(string $text = "尾页"): string
    {

        if ($this->currentPage() >= $this->lastPage()) {
            return $this->getDisabledTextWrapper($text);
        }

        $url = $this->url($this->lastPage());

        return $this->getPageLinkWrapper($url, $text);
    }

    /**
     * 跳转
     * @return string
     */
    protected function goPage()
    {
        $url = $this->getLocationUrlPrefix();
        return '<span class="layui-laypage-skip">到第<input type="tel" min="1" value="' . $this->currentPage() . '" onkeydown="javascript:if(event.keyCode==13){var page=(this.value>' . $this->lastPage . ')?' . $this->lastPage . ':this.value;location=\'' . $url . $this->options['var_page'] . '=\'+page+\'\'}" class="layui-input" ><button type="button" class="layui-laypage-btn" onclick="javascript:var page =(this.previousSibling.value > ' . $this->lastPage . ') ? ' . $this->lastPage . ': this.previousSibling.value;location=\'' . $url . $this->options['var_page'] . '=\'+page+\'\'">确定</button></span>';
    }

    /**
     * 获取js跳转URL前缀
     *
     * @access private
     * @return string
     */
    private function getLocationUrlPrefix(): string
    {
        $parse = parse_url($_SERVER["REQUEST_URI"]);
        $url   = $parse['path'] . '?';
        if (isset($parse['query'])) {
            parse_str($parse['query'], $query);
            unset($query[$this->options['var_page']]);//排除分页变量
            if (!empty($query)) {
                $url .= http_build_query($query) . '&';
            }
        }
        return $url;
    }
}