<?php

namespace app\common\service;

use app\common\tools\Visitor;
use Exception;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;
use think\Route;

class ResponseService
{
    /**
     * 操作成功跳转的快捷方法
     * @access protected
     * @param mixed $msg 提示信息
     * @param string|null|bool $url 跳转的URL地址
     * @param mixed $data 返回的数据
     * @param integer $wait 跳转等待时间
     * @param array $header 发送的Header信息
     * @return void
     * @throws HttpResponseException
     */
    public static function success($msg = 'success', $url = null, $data = '', $wait = 3, array $header = [])
    {
        if (is_null($url) && Visitor::get_referer()) {
            $url = Visitor::get_referer();
        } elseif ('' != $url && $url !== false) {
            /** @var Route $route */
            $route = app('route');
            $url   = (strpos($url, '://') || 0 === strpos($url, '/')) ? $url : $route->buildUrl($url);
        }

        $result = [
            'code' => 0,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
            'url'  => $url,
            'wait' => $wait,
        ];
        /** @var Request $request */
        $request    = app('request');
        $request_id = $request->header('request_id');
        if ($request_id) {
            $result['request_id'] = $request_id;
        }
        $type = self::get_response_type();
        // 把跳转模板的渲染下沉，这样在 response_send 行为里通过getData()获得的数据是一致性的格式
        if ('html' == strtolower($type)) {
            $type     = 'view';
            $response = Response::create(config('jump.dispatch_success_tmpl'), $type)->header($header)->assign($result);
        } else {
            $response = Response::create($result, $type)->header($header);
        }
        throw new HttpResponseException($response);
    }

    /**
     * 获取当前的response 输出类型
     * @access protected
     * @return string
     */
    protected static function get_response_type(): string
    {
        /** @var Request $request */
        $request = app('request');
        $isJson  = $request->isJson();
        $isPost  = $request->isPost();
        $isAjax  = $request->isAjax();

        return ($isJson || $isAjax || $isPost) ? 'json' : 'html';
    }

    /**
     * 检测当前是否Swoole环境
     * @access protected
     * @return bool
     */
    protected static function is_swoole(): bool
    {
        /** @var Request $request */
        $request = app('request');
        if (!$request->isCli()) {
            return false;
        }
        $argv = $_SERVER['argv'] ?? [];
        if (!empty($argv) && is_array($argv) && count($argv) >= 2) {
            return $argv[0] == 'think' && $argv[1] == 'swoole';
        }
        return false;
    }

    /**
     * 操作错误跳转的快捷方法
     * @access protected
     * @param mixed $msg 提示信息
     * @param integer $code 错误码
     * @param string $url 跳转的URL地址
     * @param integer $wait 跳转等待时间
     * @param array $header 发送的Header信息
     * @param string $sub_code 子错误码
     * @return void
     * @throws HttpResponseException|Exception
     */
    public static function error($msg = 'error', $code = -1, $url = null, $wait = 3, array $header = [], $sub_code = '')
    {
        /** @var Request $request */
        $request = app('request');
        if ($request->isCli() && self::is_swoole() === false) {
            throw new Exception($msg, $code);
        }

        if (is_null($url) && $request->isAjax()) {
            $url = '';
        } elseif (is_null($url) && Visitor::get_referer()) {
            $url = 'javascript:history.back(-1);';
        } elseif ($url) {
            /** @var Route $route */
            $route = app('route');
            $url   = (strpos($url, '://') || 0 === strpos($url, '/')) ? $url : $route->buildUrl($url);
        }

        $result     = [
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'url'  => $url,
            'wait' => $wait,
        ];
        $request_id = $request->header('request_id');
        if ($request_id) {
            $result['request_id'] = $request_id;
        }
        if ($sub_code) {
            $result['sub_code'] = $sub_code;
        }
        $type = self::get_response_type();

        if ('html' == strtolower($type)) {
            $type     = 'view';
            $response = Response::create(config('jump.dispatch_error_tmpl'), $type)->header($header)->assign($result);
        } else {
            $response = Response::create($result, $type)->header($header);
        }

        throw new HttpResponseException($response);
    }

    /**
     * 返回封装后的API数据到客户端
     * @access protected
     * @param mixed $data 要返回的数据
     * @param mixed $msg 提示信息
     * @param integer $code 返回的code
     * @param string $type 返回数据格式
     * @param array $header 发送的Header信息
     * @return void
     * @throws HttpResponseException
     */
    public static function result(array $data, $msg = 'success', $code = 0, $type = 'json', array $header = [])
    {
        $result = [
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ];
        /** @var Request $request */
        $request    = app('request');
        $request_id = $request->header('request_id');
        if ($request_id) {
            $result['request_id'] = $request_id;
        }
        $type     = $type ?: self::get_response_type();
        $response = Response::create($result, $type)->header($header);

        throw new HttpResponseException($response);
    }

    /**
     * URL重定向
     * @access protected
     * @param string $url 跳转的URL表达式
     * @param integer $code http code
     * @param array $with 隐式传参
     * @return void
     * @throws HttpResponseException
     */
    public static function redirect(string $url, $code = 302, $with = [])
    {
        $response = Response::create($url, 'redirect');

        $response->code($code)->with($with);

        throw new HttpResponseException($response);
    }
}

//if (!function_exists('result')) {
//   /**
//    * 返回成功结果
//    * @param array|object|null $data 数据
//    * @param string $msg 提示语
//    * @return void
//    * @throws HttpResponseException
//    */
//   function result($data, $msg = 'success')
//   {
//       $data = empty($data) ? [] : $data;//如果是null 则转换成[]
//       $data = is_object($data) ? tools()::object2array($data) : $data;
//       ResponseService::result($data, $msg);
//   }
//}
//if (!function_exists('error')) {
//   /**
//    * 返回错误信息
//    * @param string $msg 提示语
//    * @param integer $code 提示语
//    * @param string|null $url 跳转URL
//    * @return void
//    * @throws HttpResponseException|\Exception
//    */
//   function error($msg = '', $code = -1, $url = null)
//   {
//       ResponseService::error($msg, $code, $url);
//   }
//}
//if (!function_exists('success')) {
//   /**
//    * 返回成功信息
//    * @param string $msg 提示语
//    * @param string|null $url 跳转URL
//    * @return void
//    * @throws HttpResponseException
//    */
//   function success($msg = 'success', $url = null)
//   {
//       ResponseService::success($msg, $url);
//   }
//}
//if (!function_exists('redirect')) {
//   /**
//    * URL跳转
//    * @param string|null $url 跳转URL
//    * @return void
//    * @throws HttpResponseException
//    */
//   function redirect(string $url)
//   {
//       ResponseService::redirect($url);
//   }
//}