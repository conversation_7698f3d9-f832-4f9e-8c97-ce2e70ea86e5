<?php

namespace app\common\service;

use Exception;
use Redis;
use Throwable;

class DistributedLocksService
{
    private int $retryDelay;//设置每一轮lock失败或者异常，多久之后重新尝试下一轮lock。单位毫秒
    private int $retryCount;//最多几轮lock失败后彻底放弃。
    private float $clockDriftFactor = 0.01;//补偿时间
    private int $quorum;//大于等于1/2的数量
    private array $servers = [];//服务器集群
    private array $instances = [];
    private array $lock = [];

    function __construct(array $servers, $retryDelay = 200, $retryCount = 3)
    {
        $this->servers = $servers;

        $this->retryDelay = $retryDelay;
        $this->retryCount = $retryCount;

        $this->quorum = min(count($servers), (count($servers) / 2 + 1));
    }

    /**
     * 获取分布式锁
     * @access public
     * @param string $lock_key_name 锁名称
     * @param integer $ttl 时长
     * @return array
     * @throws Exception
     */
    public function get_lock($lock_key_name, $ttl = 10000)
    {
        $lock_key_name = 'distributed_lock:' . md5($lock_key_name);
        while (true) {
            $lock = $this->lock($lock_key_name, $ttl);
            if (is_array($lock)) {
                return $this->lock = $lock;
            }
        }
        return false;
    }

    /**
     * 获取分布式锁
     * @access private
     * @param string $resource 锁名称
     * @param integer $ttl 时长
     * @return bool|array
     * @throws Exception
     */
    private function lock($resource, int $ttl = 10000)
    {
        $this->init_instances();

        $token = uniqid();
        $retry = $this->retryCount;
        try {
            do {
                $n = 0;

                $startTime = microtime(true) * 1000;

                foreach ($this->instances as $instance) {
                    if ($this->lock_instance($instance, $resource, $token, $ttl)) {
                        $n++;
                    }
                }

                // Add 2 milliseconds to the drift to account for Redis expires
                // precision, which is 1 millisecond, plus 1 millisecond min drift
                // for small TTLs.
                $drift = ($ttl * $this->clockDriftFactor) + 2;

                $validityTime = $ttl - (microtime(true) * 1000 - $startTime) - $drift;

                if ($n >= $this->quorum && $validityTime > 0) {
                    return [
                        'validity' => $validityTime,
                        'resource' => $resource,
                        'token'    => $token,
                    ];

                } else {
                    foreach ($this->instances as $instance) {
                        $this->unlockInstance($instance, $resource, $token);
                    }
                }

                // Wait a random delay before to retry
                $delay = mt_rand(floor($this->retryDelay / 2), $this->retryDelay);
                usleep($delay * 1000);
                $retry--;

            } while ($retry > 0);

        } catch (Exception|Throwable $e) {
            return false;
        }
        return false;
    }

    /**
     * 初始化连接
     * @access private
     * @return void
     * @throws Exception
     */
    private function init_instances()
    {
        if (empty($this->instances)) {
            foreach ($this->servers as $server) {
                try {
                    list($host, $port, $timeout, $password) = $server;
                    $redis = new Redis();
                    $redis->connect($host, $port, $timeout);
                    if ($password) {
                        $redis->auth($password);
                    }
                    $this->instances[] = $redis;
                } catch (Exception|Throwable $e) {
                    //比如部分机器down机之后 不异常报错
                    continue;
                }
            }
        }
    }

    /**
     * @param Redis $instance
     * @param string $resource
     * @param string $token
     * @param int $ttl
     * @return boolean
     */
    private function lock_instance($instance, $resource, $token, $ttl)
    {
        try {
            return $instance->set($resource, $token, ['NX', 'PX' => $ttl]);
        } catch (Exception|Throwable $e) {
            return false;
        }
    }

    /**
     * @param Redis $instance
     * @param string $resource
     * @param string $token
     * @return boolean
     */
    private function unlockInstance($instance, $resource, $token)
    {
        try {
            $script = '
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        ';
            return $instance->eval($script, [$resource, $token], 1);
        } catch (Exception|Throwable $e) {
            //比如部分机器down机之后 不异常报错
            return false;
        }

    }

    /**
     * 释放分布式锁
     * @access public
     * @param array $lock 锁
     * @return void
     * @throws Exception
     */
    public function unlock($lock = array())
    {
        if (empty($lock) && !empty($this->lock)) {
            $lock = $this->lock;
        }
        $this->init_instances();
        $resource = $lock['resource'];
        $token    = $lock['token'];

        foreach ($this->instances as $instance) {
            $this->unlockInstance($instance, $resource, $token);
        }
    }
}

if (!function_exists('get_distributed_instance')) {
    /**
     * 获取redis分布式锁实例
     * @return DistributedLocksService|null
     */
    function get_distributed_instance()
    {
        $options = config('cache.stores.redis');
        $servers = [[$options['host'], $options['port'], 10, $options['password']]];
        return new DistributedLocksService($servers);
    }
}