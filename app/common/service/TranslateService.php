<?php

namespace app\common\service;

use app\model\Business;
use app\model\TranslateNote;
use Exception;
use xieyongfa\facade\Translate;

class TranslateService
{
    public static $bid;
    protected static $instance = null;
    public $status;
    public $sub_code = 0;
    public $message;

    protected function __construct()
    {
        //disallow new instance
    }

    /**
     * 单例化对象
     * @param null|integer $bid 商家
     * @return $this
     * @throws Exception
     */
    public static function get_instance($bid = null)
    {
        self::$bid = $bid ?: tools()::get_empty_guid();
        //        $translate_channel_name = self::get_translate_channel_name();
        //        if (empty($translate_channel_name)) {
        //            throw new \Exception('翻译通道尚未配置');
        //        }
        $sn = get_instance_sn(self::$bid);
        //单例方法,用于访问实例的公共的静态方法
        if (is_null(self::$instance) || !isset(self::$instance[$sn])) {
            self::$instance[$sn] = new self();
        }
        return self::$instance[$sn];
    }

    /**
     * 获取翻译通道配置
     * @return string
     * @throws Exception
     */
    protected static function get_translate_channel_name()
    {
        if (tools()::is_empty_guid(self::$bid)) {
            return get_system_config('translate_channel_name');
        }
        $config = get_config_by_bid(self::$bid);
        return $config['translate_channel_name'];
    }

    public function __wakeup()
    {
        self::$instance = $this;
    }


    /**
     * 发送翻译
     * @access public
     * @param array $data 发送内容
     * @return bool
     * @throws Exception
     */
    public function get_config(array $data)
    {
        return Translate::channel('deepl')->get_config($data);
    }


    /**
     * 发送翻译
     * @access public
     * @param array $data 发送内容
     * @return bool
     * @throws Exception
     */
    public function translate(array $data)
    {
        $source_lang = $data['source_lang'];
        $target_lang = $data['target_lang'];
        $source_text = $data['source_text'];
        $fee         = iconv_strlen(urldecode($source_text));
        if (!$source_text) {
            $this->message = '翻译失败,内容为空';
            return false;
        }
        $bid = self::$bid;
        //        $translate           = self::get_translate_instance();

        $translate_note_guid = !empty($data['guid']) ? $data['guid'] : create_guid();
        $db                  = new TranslateNote();
        //1小时没有发送成功的 则看之前是否有创建相同订单
        $translate_map = [
            ['bid', '=', $bid],
            ['guid', '=', $translate_note_guid],
        ];
        $order         = $db->field(['status'])->where($translate_map)->findOrEmpty();
        if (!$order->isEmpty() && isset($order['status']) && $order['status'] == 1) {
            $this->message = '当前GUID不允许重复发送';
            return false;
        }
        if ($order->isEmpty()) {
            $db_business   = new Business();
            $map           = [['guid', '=', $bid]];
            $translate_num = (int)$db_business->where($map)->value('translate_num');
            if ($translate_num < $fee) {
                $msg            = '商家当前可用翻译不足~';
                $update_data    = [
                    'status'  => -1,
                    'message' => $msg,
                ];
                $update         = $db::update($update_data, $map);
                $this->status   = -1;
                $this->sub_code = 'TRANSLATE_BALANCE_NOT_ENOUGH';
                $this->message  = $msg;
                //                wr_log('翻译失败,内容:' . $source_text . ',原因:商家可用翻译不足');
                return false;
            }
            $db_business->where($map)->setDec('translate_num', $fee);
            //            $config      = self::get_translate_channel_config();
            $insert_data = [
                'bid'         => $bid,
                'guid'        => $translate_note_guid,
                'sid'         => $data['sid'],
                'user_guid'   => $data['user_guid'],
                'user_id'     => $data['user_id'],
                'source_lang' => $source_lang,
                'target_lang' => $target_lang,
                'source_text' => $source_text,
                'status'      => 0, //等待发送
                'length'      => $fee, //消耗条数
            ];
            $db->save($insert_data);
        }
        $result = Translate::channel('deepl')->translate($data);
        //        $result = $translate->translate($data);
        if ($result['status'] === 0) {
            // 更新翻译发送记录
            $update_data = [
                'status'      => 1,
                'response'    => $result['response'] ?? '',
                'code'        => $result['code'],
                'message'     => $result['message'],
                'target_text' => $result['target_text']
            ];
            $update      = $db::update($update_data, $translate_map);
            return $result;
        } else {
            // 更新发送记录表
            $update_data   = [
                'status'   => -1,
                'response' => $result['response'] ?? '',
                'code'     => $result['code'],
                'message'  => $result['message'],
            ];
            $this->status  = $result['code'];
            $this->message = $result['message'];
            $update        = $db::update($update_data, $translate_map);
            return false;
        }
    }

    protected function __clone()
    {
        //disallow clone
    }
}