<?php

namespace app\common\service;


use Consumer\Consumer\Consumer;
use Exception;
use think\facade\Env;

class QueueConsumer extends Consumer
{
    public function execute($data)
    {
        try {
            $pid      = getmypid();
            $data_arr = json_decode($data[1], true);
            //echo '已完成处理数据: ' . $data[1] . ", pid( {$pid} )" . PHP_EOL;
            $result = $this->resolve_and_fire($data_arr['class'], $data_arr['payload']);
            echo microsecond() . "--已完成处理数据 pid: " . $pid . " ;数据: " . $data[1] . PHP_EOL;
        } catch (Exception $e) {
            wr_log('getMessage:' . $e->getMessage() . 'getFile:' . $e->getFile() . 'getLine:' . $e->getLine());
        }
    }


    protected function resolve_and_fire($name, array $payload)
    {
        [$class, $method] = $this->parse_job($name);
        $instance = $this->resolve($class);
        if ($instance) {
            return $instance->{$method}($payload);
        }
        return false;
    }

    /**
     * Parse the job declaration into class and method.
     * @param string $job
     * @return array
     */
    protected function parse_job($job)
    {
        $segments = explode('@', $job);

        return count($segments) > 1 ? $segments : [$segments[0], 'fire'];
    }

    /**
     * Resolve the given job handler.
     * @param string $name
     * @return mixed
     */
    protected function resolve($name)
    {
        if (strpos($name, '\\') === false) {

            if (strpos($name, '/') === false) {
                $module = '';
            } else {
                [$module, $name] = explode('/', $name, 2);
            }

            $name = Env::get('app.app_namespace') . ($module ? '\\' . strtolower($module) : '') . '\\job\\' . $name;
        }
        if (class_exists($name)) {
            return new $name();
        }
    }

    /**
     * 1. 如果抛出异常发生任务阻塞自动退出
     * 2. 记录异常对象到日志，具体可自定义处理
     * @param $e
     * @return mixed
     */
    public function blocking($e)
    {
        // 1.
        throw $e;
    }
}

