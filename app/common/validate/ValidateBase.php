<?php

namespace app\common\validate;

use think\facade\Db;
use think\Validate;

class ValidateBase extends Validate
{
    public function safePassword($value, $rule, $data)
    {
        if ($value === '') {
            return true;
        }
        return tools():: check_password($value);
    }

    public function guid($value, $rule, $data)
    {
        return tools()::is_guid($value) ? true : $value . '不是有效的guid字符:' . $value;
    }

    public function exist($value, $rule, $data)
    {
        if (is_string($rule)) {
            $rule = explode(',', $rule);
        }
        $db    = Db::name($rule[0]);
        $field = isset($rule[1]) ? $rule[1] : 'id';
        if ($db->where($field, $value)->field($field)->find()) {
            return true;
        }
        return false;
    }

    public function notExist($value, $rule, $data)
    {
        if (is_string($rule)) {
            $rule = explode(',', $rule);
        }
        $db    = Db::name($rule[0]);
        $field = isset($rule[1]) ? $rule[1] : 'id';
        if ($db->where($field, $value)->field($field)->find()) {
            return false;
        }
        return true;
    }


    public function existPid($value, $rule, $data)
    {
        if (intval($value) === 0) {
            return true;
        }

        if (is_string($rule)) {
            $rule = explode(',', $rule);
        }
        $db    = Db::name($rule[0]);
        $field = $db->getPk($rule[0]);
        if (isset($data[$field]) && $data[$field] == $value) {
            return false;
        }
        if ($db->where($field, $value)->field($field)->find()) {
            return true;
        }
        return false;
    }
}