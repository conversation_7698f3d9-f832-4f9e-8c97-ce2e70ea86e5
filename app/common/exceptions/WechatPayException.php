<?php


namespace app\common\exceptions;

use think\exception\HttpResponseException;
use think\Response;

class WechatPayException extends Exception
{
    /**
     * error raw data.
     * @var array
     */
    public $raw = [];

    /**
     * GatewayException constructor.
     * @param string $message
     * @param string $code
     * @param array $raw
     */
    public function __construct($message, $code, $raw = [])
    {
        $this->raw    = $raw;
        $data         = [
            'return_code'  => 'SUCCESS',
            'return_msg'   => 'OK',
            'result_code'  => 'FAIL',
            'err_code'     => $code,
            'err_code_des' => $message,
        ];
        $data['sign'] = tools()::get_sign($data, $raw);
        $response     = Response::create($data, 'xml')->options(['root_node' => 'xml']);
        parent::__construct($message, $code);
        throw new HttpResponseException($response);
    }
}