<?php
declare (strict_types=1);

namespace app;

use app\common\exceptions\NotNotifyException;
use app\model\Business;
use app\common\service\TokenService;
use app\common\tools\Visitor;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\ClassNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\facade\App;
use think\template\exception\TemplateNotFoundException;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ClassNotFoundException::class,
        ValidateException::class,
        TemplateNotFoundException::class,
        NotNotifyException::class
    ];

    /**
     * 获取access_token
     *
     * @access public
     * @return string
     * @throws Exception
     */
    public function get_access_token()
    {
        $key_list = [
            'business_guid' => 'business_user_token',
            'bid'           => 'access_token',
        ];
        $request  = $this->app->request;
        foreach ($key_list as $key => $token_name) {
            if ($access_token = $request->get($token_name)) {
                //优先获取get参数
                return $access_token;
            } elseif ($access_token = $request->post($token_name)) {
                //其次获取post参数
                return $access_token;
            } elseif ($access_token = $request->header($token_name)) {
                //其次获取header参数
                return $access_token;
            } elseif (cookie($key) && $access_token = cookie(cookie($key) . '_' . $token_name)) {
                //都不存在判断是否存在缓存商家标识,有尝试获取cookies中是否有token
                return $access_token;
            }
        }
        return null;
    }

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param Throwable $e
     * @return void
     * @throws Exception
     */
    public function report(Throwable $e): void
    {
        $request = $this->app->request;
        $msg     = $e->getMessage() ?: 'error';
        if (is_host()) {
            $msg .= tools()::exception_to_string($e);
        }
        $root_path          = str_replace("\\", "/", root_path());//替换掉应用目录,确保安全
        $msg                = str_replace($root_path, '', $msg);
        $is_memory_overflow = strpos($msg, 'Allowed memory size of') !== false; //是否内存溢出
        $is_web_request     = Visitor::is_web_request();
        $code               = $e->getCode() ?: -1;
        if (!App::isDebug() && $e instanceof TemplateNotFoundException) {
            error('您访问的路径不正确!', $code);
        }
        if (strpos($msg, 'app/controller/test/Index.php') !== false) {
            error('您访问的路径不正确', $code);
        }
        try {
            if (!$this->isIgnoreReport($e)) {
                $except     = ['access_token', 'business_user_token', '__version__'];
                $json_param = json_encode($request->except($except), JSON_UNESCAPED_UNICODE);
                $ip         = tools()::get_client_ip();
                $url        = $request->url($is_web_request);
                $url        = tools()::replace_url_parameters($url, $except);
                $html       = "通知类型:程序异常;\r\n";
                $html       .= "pid:" . getmypid() . "\r\n";
                $html       .= "code:" . $code . "\r\n";
                $html       .= "msg:" . ($msg ?: 'error') . "\r\n";
                $html       .= "url:" . $url . "\r\n";
                $html       .= "method:" . $request->method() . "\r\n";
                $html       .= "param:" . $json_param . "\r\n";
                $html       .= "file:" . str_replace(root_path(), '', $e->getFile()) . "\r\n";
                $html       .= "line:" . $e->getLine() . "\r\n";
                $html       .= "ip:" . $ip . "\r\n";
                $region     = tools()::get_client_ip_region_info($ip);
                $html       .= "region:" . $region . "\r\n";
                $cache_key  = 'report:' . md5($html);
                if (!cache($cache_key)) {
                    $access_token = $this->get_access_token();
                    if ($access_token) {
                        $jwt_data      = TokenService::decode($access_token);
                        $bid           = $jwt_data['bid'];
                        $db_business   = new Business();
                        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
                        $account       = $business_info['account'];
                        $business_name = $business_info['business_name'];
                        $html          .= "account:" . $account . "\r\n";
                        $html          .= "business_name:" . $business_name . "\r\n";
                    }
                    $request_id = $request->header('request_id');
                    if ($request_id) {
                        $html .= "request_id:" . $request_id . "\r\n";
                    }
                    if (!tools()::match_keyword_in_array($msg, get_system_config('ignore_exception_keyword'))) {
                        if (!is_debug()) {
                            send_qy_wechat($html);
                        } else {
                            wr_log($html);
                        }
                        wr_log(tools()::exception_to_string($e));
                        wr_log($e->getTraceAsString());
                    }
                    cache($cache_key, format_timestamp(), 60);
                }
            }
        } catch (Exception | Throwable $e) {
            logToFile('异常抛出出错:' . tools()::exception_to_string($e));
        }
        //内存溢出不能用error方法,否则无法响应数据到前端
        if ((!App::isDebug() || (is_host() && $request->isPost())) && !($e instanceof HttpResponseException) && $is_web_request && !$is_memory_overflow) {
            error($msg, $code);//命令行或者POST模式下不抛出异常,避免后续的流程被中断
        }
        parent::report($e);
    }
}