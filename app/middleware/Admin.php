<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\Business;
use app\model\User;
use app\model\UserBindNote;
use Closure;
use Exception;
use think\facade\View;
use think\Request;
use think\Response;

class Admin extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->get();
        $path   = get_request_path();
        if (empty($params['bid']) || !empty($params['business_guid'])) {
            //链接上没bid参数或者有business_guid参数(用于PC后台有部分链接用了bid做参数,但是不需要授权的情况兼容)不做强制授权,后续可以通过path动态判断是否需要授权
            return $next($request);
        }
        $bid = strtolower($params['bid']);
        View::assign('bid', $bid);
        $request->__set('_bid', $bid);
        $redirect_url  = $request->url(true);
        $not_auth_list = [
            'passport/bind_user_success',
            'passport/login',
        ];
        //缓存商家标识
        $exp = 3600 * 24;
        cookie('bid', $bid, $exp);
        if (in_array($path, $not_auth_list)) {
            return $next($request);
        }
        $token_name   = 'business_user_token';
        $access_token = $this->get_admin_access_token($request);
        //先判断是否存在缓存,如果存在缓存则直接解析
        if (empty($access_token)) {
            $wechat_user_info = [];
            if (Visitor::is_wechat_browser() || is_debug()) {
                $wechat_user_info = WeixinService::get_open_id($bid);
            } else {
                //redirect($login_url); //后续可以做PC端登录
                error('请用微信访问~');
            }
            $appid  = $wechat_user_info['appid'];
            $openid = $wechat_user_info['openid'];
            $request->__set('_appid', $appid);
            $request->__set('_openid', $openid);
            $db_user_bind_note = new UserBindNote();
            $map               = [
                ['openid', '=', $openid],
                ['appid', '=', $appid],
                ['status', '=', 1],
            ];
            if ($bid) {
                $map[] = ['bid', '=', $bid];
            }
            $user_bind_note = $db_user_bind_note->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
            if ($user_bind_note->isEmpty()) {
                // 不在白名单 而且没有绑定记录 就去绑定
                $token_data       = [
                    'exp'    => 3600,
                    'bid'    => $bid,
                    'appid'  => $appid,
                    'openid' => $openid,
                    'way'    => 1, //1 公众号 2 小程序
                    'from'   => 3, //来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程序
                ];
                $token            = TokenService::encode($token_data);
                $db_business      = new Business();
                $business_info    = $db_business->get_business_info_by_account_or_guid($bid);
                $business_account = $business_info['account'];
                $login_url        = (string)url('admin/passport/login', ['bid' => $bid, 'business_account' => $business_account, 'token' => $token, 'callback' => urlencode($redirect_url)]);
                redirect($login_url);
            }
            $user_info = [
                'appid'  => $appid,
                'openid' => $openid,
                'bid'    => $bid,
                'guid'   => $user_bind_note['user_guid'],
            ];
            // 获取逻辑里面会自动写入cookie,用于后面身份校验
            $db_user = new User();
            $data    = $db_user->get_access_token($user_info, 'client_credential');
        }
        $request->__set($token_name, $access_token);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}