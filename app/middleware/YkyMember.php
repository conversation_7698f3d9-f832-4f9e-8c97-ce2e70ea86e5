<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use Exception;
use OpenApi\Yky;
use think\facade\View;
use think\Request;

class YkyMember extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->param();
        if (empty($params['bid'])) {
            error('商家标识不能为空');
        }
        $bid    = $params['bid'];
        $appid  = $request->__get('_appid');
        $openid = $request->__get('_openid');
        $config = $request->__get('_config');
        $yky    = new Yky($config);
        if (empty($config['yunhuiyuan_username']) || empty($config['yunhuiyuan_bid'])) {
            error('云会员账号信息配置不完整');
        }
        if (empty($openid)) {
            error('openid不能为空');
        }
        $member = $yky->get_member_info_by_openid($openid);
        if ($member === false) {
            $db_yky_member   = new \app\model\YkyMember();
            $member_card_url = $db_yky_member->get_member_card_url($bid, $request->url(true), $params['store_guid'] ?? '');
            //  redirect($member_card_url);
            error("请先绑定会员卡!", -1, $member_card_url);
        }
        View::assign('member', $member);
        $request->__set('_member', $member);
        return $next($request);
    }
}
