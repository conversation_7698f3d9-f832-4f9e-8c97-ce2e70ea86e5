<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

class AllowMethod extends BasicMiddleware
{
    protected array $allow_methods = ['POST'];

    /**
     * 允许跨域请求
     * @access public
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!in_array($request->method(), $this->allow_methods)) {
            $result = [
                'code' => -1,
                'msg'  => '仅允许使用' . join(',', $this->allow_methods) . '方法请求!',
                'time' => time()
            ];
            return json($result);
        }
        return $next($request);
    }
}
