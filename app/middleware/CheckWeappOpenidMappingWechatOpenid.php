<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\tools\Visitor;
use app\model\AppidOpenidMapping;
use app\model\WechatConfig;
use Closure;
use Exception;
use think\Request;
use think\Response;

class CheckWeappOpenidMappingWechatOpenid extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        //如果不是获取access_token 则校验是否有access_token参数
        $path           = get_request_path();
        $need_auth_list = [
            'cart/get',
        ];
        if (!in_array($path, $need_auth_list)) {
            return $next($request);
        }
        if (!Visitor::is_wechat_browser()) {
            // 非公众号访问 则不执行
            return $next($request);
        }
        $access_token = $this->get_member_access_token($request);
        if (empty($access_token)) {
            wr_log('$access_token 为空', 1);
            return $next($request);
        }
        $jwt         = TokenService::decode($access_token);
        $sid         = $jwt['sid'] ?? 0;
        $appid       = $jwt['appid'];
        $bid         = $jwt['bid'];
        $openid      = $jwt['openid'];
        $member_guid = $jwt['member_guid'];
        $member_id   = $jwt['member_id'] ?? 0;
        $appid_type  = $this->get_appid_type($appid);
        if ($appid_type != 2) {
            //不是小程序的不用执行
            //wr_log('不是小程序的不用执行', 1);
            return $next($request);
        }
        $config       = get_config_by_bid($bid);
        $wechat_appid = $config['appid'];
        if (empty($wechat_appid)) {
            //没有授权公众号则不执行
            //wr_log('没有授权公众号则不执行', 1);
            return $next($request);
        }
        if ($this->check_openid_mapping_exists($appid, $openid, $wechat_appid)) {
            //            wr_log('$openid:' . $openid . '已经有关联过 无需再次关联', 1);
            return $next($request);
        }
        //如果没有 则添加header 跳转到 授权url
        $auth_url = (string)url('member/tools/auth', ['bid' => $bid], false, true);
        $response = $next($request);
        /* @var $response Response */
        $response = $response->header(['LocationUrl' => $auth_url]);
        //        wr_log('$openid:' . $openid . '已没有关联关系 返回 LocationUrl 成功', 1);
        return $response;
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}