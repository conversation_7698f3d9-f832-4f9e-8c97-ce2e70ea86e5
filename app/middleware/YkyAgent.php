<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\YkyAgentMap;
use Closure;
use Exception;
use think\Request;

class YkyAgent extends BasicMiddleware

{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params           = $request->param();
        $appid            = $request->__get('_appid');
        $openid           = $request->__get('_openid');
        $db_yky_agent_map = new YkyAgentMap();
        $map              = [
            ['appid', '=', $appid],
            ['openid', '=', $openid],
        ];
        $bind_note        = $db_yky_agent_map->where($map)->count();
        if (empty($bind_note)) {
            $redirect_url = $request->url(true);
            $login_url    = (string)url('member/bind/yky_agent', ['bid' => $params['bid'], 'callback' => urlencode($redirect_url)], true, true);
            redirect($login_url);
        }
        return $next($request);
    }
}
