<?php

namespace app\middleware;

use app\common\service\TokenService;
use Closure;
use Exception;
use think\Request;
use think\Response;

class AutoRefreshAccessToken
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return Response
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $jwt      = $request->__get('_jwt');
        if ($jwt) {
            $exp = $jwt['exp'];
            if (($exp - time()) < 600) {
                $jwt['exp'] += 3600;
                $jwt        = TokenService::encode($jwt);
                /* @var $response Response */
                $response = $response->header(['Business-User-Token' => $jwt]);
            }
        }
        return $response;
    }

}