<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\AppidOpenidMapping;
use Closure;
use Exception;
use think\Request;
use think\Response;

class WeappOpenidMappingWechatOpenid extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Visitor::is_wechat_browser()) {
            // 非公众号访问 则不执行
            return $next($request);
        }
        $params       = $request->param();
        $bid          = strtolower($params['bid']);
        $access_token = $this->get_member_access_token($request);
        if (empty($access_token)) {
            return $next($request);
        }
        $jwt         = TokenService::decode($access_token);
        $sid         = $jwt['sid'] ?? 0;
        $appid       = $jwt['appid'];
        $openid      = $jwt['openid'];
        $member_guid = $jwt['member_guid'];
        $member_id   = $jwt['member_id'] ?? 0;

        $appid_type = $this->get_appid_type($appid);
        if ($appid_type != 2) {
            //不是小程序的不用执行
            //wr_log('不是小程序的不用执行', 1);
            return $next($request);
        }
        $config       = get_config_by_bid($bid);
        $wechat_appid = $config['appid'];
        if (empty($wechat_appid)) {
            //没有授权公众号则不执行
            //wr_log('没有授权公众号则不执行', 1);
            return $next($request);
        }
        if ($this->check_openid_mapping_exists($appid, $openid, $wechat_appid)) {
            return $next($request);
        }
        $user_info               = WeixinService::get_open_id($bid);
        $wechat_appid            = $user_info['appid'];
        $wechat_openid           = $user_info['openid'];
        $insert_data             = [
            'bid'                 => $bid,
            'sid'                 => $sid,
            'member_guid'         => $member_guid,
            'member_id'           => $member_id,
            'appid'               => $appid,
            'openid'              => $openid,
            'appid_type'          => 2, //小程序
            'relation_appid'      => $wechat_appid, //关联公众号APPID
            'relation_openid'     => $wechat_openid, //关联公众号OPENID
            'relation_appid_type' => 1, //关联类型公众号
        ];
        $db_appid_openid_mapping = new AppidOpenidMapping();
        $db_appid_openid_mapping->save($insert_data);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}