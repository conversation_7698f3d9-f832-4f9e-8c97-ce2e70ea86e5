<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\Member as MemberModel;
use Closure;
use Exception;
use think\Request;
use think\Response;

class MemberStatusVerify extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params       = $request->param();
        $redirect_url = $request->url(true);
        $bid          = $request->__get('_bid');
        $member_guid  = $request->__get('_member_guid');
        $db_member    = new MemberModel();
        $map          = ['bid' => $bid, 'guid' => $member_guid];
        $member       = $db_member->verify_member_status($map);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}