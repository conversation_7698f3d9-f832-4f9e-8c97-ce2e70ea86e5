<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\YkyUserMap;
use Closure;
use Exception;
use think\Request;

class YkyUser extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params          = $request->param();
        $bid             = $params['bid'];
        $appid           = $request->__get('_appid');
        $openid          = $request->__get('_openid');
        $db_yky_user_map = new YkyUserMap();
        $map             = [
            ['appid', '=', $appid],
            ['openid', '=', $openid],
            ['bid', '=', $bid],
        ];
        $bind_note       = $db_yky_user_map->where($map)->findOrEmpty();
        if ($bind_note->isEmpty()) {
            $redirect_url    = $request->url(true);
            $url_params_data = ['bid' => $bid, 'callback' => urlencode($redirect_url)];
            $config          = get_config_by_bid($bid);
            if (!empty($config['yikayi_account'])) {
                $url_params_data['user_name'] = $config['yikayi_account'];
            }
            $login_url = (string)url('member/bind/yky_user', $url_params_data, true, true);
            redirect($login_url);
        }
        return $next($request);
    }
}
