<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use Closure;
use Exception;
use think\facade\View;
use think\Request;

class <PERSON><PERSON>i extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->param();
        if (empty($params['bid'])) {
            $params['bid'] = '6ebbc041-81d7-ebc8-ea2b-719172ec2e19';
        }
        $redirect_url = $request->url(true);
        //获取商家标识
        $bid = strtolower($params['bid']);
        View::assign('bid', $bid);
        $config = get_config_by_bid($bid);
        $request->__set('_bid', $bid);
        $request->__set('_config', $config);
        $path          = get_request_path();
        $not_auth_list = [
            'index/index',
            'user/login',
            'user/reg',
        ];
        View::assign('is_login', 0);
        //缓存商家标识
        $exp = 3600 * 24;
        cookie('bid', $bid, $exp);
        $access_token_cache_key = $bid . '_access_token';
        $access_token           = cookie($access_token_cache_key);
        //先判断是否存在缓存,如果存在缓存则直接解析
        if ($access_token) {
            $jwt = TokenService::decode($access_token);
            $request->__set('_member_guid', $jwt['member_guid']);
            View::assign('is_login', 1);
        } else {
            if (!in_array($path, $not_auth_list)) {
                //不在白名单则直接跳转到登陆界面
                $login_url = (string)url('paimai/user/login', ['bid' => $bid, 'callback' => urlencode($redirect_url)]);
                redirect($login_url);
            }
        }
        return $next($request);
    }
}
