<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use Exception;
use GuzzleHttp\Psr7\MimeType;
use think\helper\Str;
use think\Request;
use think\Response;
use think\swoole\response\File;

class AppendContentType extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Visitor::is_swoole()) {
            return $next($request);
        }
        $url = str_replace($request->baseFile(), '', $request->baseUrl());
        if (strpos($url, '.') !== false && Str::startsWith($url, [
                '/static/',
                '/statics/',
                '/file/',
            ])) {
            $file_name = public_path() . ltrim($url, DIRECTORY_SEPARATOR);
            if (DIRECTORY_SEPARATOR == '\\') {
                //兼容Windows
                $file_name = str_replace('/', DIRECTORY_SEPARATOR, $file_name);
            }
            if (file_exists($file_name)) {
                $response    = new File($file_name);
                $contentType = MimeType::fromFilename($file_name);
                if ($contentType) {
                    $response->header([
                        'Content-Type' => $contentType . '; charset=utf-8',
                    ]);
                    return $response;
                }
            }
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}