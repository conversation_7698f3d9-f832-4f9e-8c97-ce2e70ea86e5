<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use Exception;
use think\facade\View;
use think\Request;
use think\Response;

class WechatJsSdk extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (Visitor::is_wechat_browser()) {
            $url = $request->url(true);
            // 创建SDK实例
            $script = weixin($request->__get('_appid'))::WeChatScript();
            // 获取JsApi使用签名，通常这里只需要传 $url参数
            $options = $script->getJsSign($url);
            View::assign('options', $options);
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}