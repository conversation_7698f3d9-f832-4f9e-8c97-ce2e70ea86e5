<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\Business;
use Closure;
use Exception;
use think\Request;

class Openapi extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $get_params = $request->get();
        if (empty($get_params['appid']) || empty($get_params['signature']) || empty($get_params['timestamp'])) {
            error('缺少appid或signature或timestamp参数');
        }
        $appid     = $get_params['appid'];
        $signature = $get_params['signature'];
        $timestamp = $get_params['timestamp'];
        if (time() - $timestamp > 300) {
            error('timestamp已过期');
        }
        $post_params   = $request->post();
        $db            = new Business();
        $business_info = $db->get_business_info_by_appid_secret($appid);
        if (!$business_info) {
            error('应用appid:' . $appid . '不存在');
        }
        $secret = $business_info['secret'];
        if ($this->verify($post_params, $appid, $secret, $timestamp, $signature) === false) {
            error('签名验证不通过');
        }
        return $next($request);
    }

    /**
     * 签名校验
     * @param array $data
     * @param string $appid
     * @param string $secret
     * @param integer $timestamp
     * @param string $signature
     * @return  bool
     */
    public function verify($data, $appid, $secret, $timestamp, $signature)
    {
        ksort($data);
        $sign_content = tools()::get_sign_content($data);
        return strtoupper(md5($sign_content . '&appid=' . $appid . '&secret=' . $secret . '&timestamp=' . $timestamp)) === $signature;
    }
}