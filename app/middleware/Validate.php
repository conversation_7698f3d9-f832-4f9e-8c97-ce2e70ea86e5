<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\exception\ValidateException;
use think\Request;

class Validate extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws ValidateException
     */
    public function handle(Request $request, Closure $next)
    {
        $class = app()->parseClass('validate', $request->controller());
        if (class_exists($class)) {
            /**
             * @var $validate \think\Validate 验证器
             */
            $validate = new $class();
            $scene    = $request->action();
            if ($validate->hasScene($scene)) {
                //存在验证场景才校验,去除前后空格进行校验, 因为取数据的时候已经自动剔除了前后空格
                $validate->failException()->scene($scene)->check($request->param('', null, 'trim'));//错误会自动抛出异常
            }
        }
        return $next($request);
    }
}
