<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\AppidOpenidMapping;
use app\model\WechatConfig;
use Closure;
use Exception;
use think\Request;
use think\Response;

class WechatOpenidMappingPlatformWechatOpenid extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        //如果不是获取access_token 则校验是否有access_token参数
        if (!Visitor::is_wechat_browser()) {
            // 非公众号访问 则不执行
            return $next($request);
        }
        $access_token = $this->get_admin_access_token($request);
        $access_token = TokenService::get_valid_token_data($access_token);
        $param_token  = $request->param('token');
        $access_token = $access_token ?: TokenService::get_valid_token_data($param_token);
        if (empty($access_token)) {
            return $next($request);
        }
        $access_token_appid  = $access_token['appid'] ?? '';
        $access_token_openid = $access_token['openid'] ?? '';
        $access_token_bid    = $access_token['bid'] ?? '';
        $appid               = $request->__get('_appid') ?: $access_token_appid;
        $openid              = $request->__get('_openid') ?: $access_token_openid;
        $bid                 = $request->__get('_bid') ?: $access_token_bid;
        if (!$appid || !$openid) {
            //没有appid和openid不执行
            //            wr_log('$appid ,$openid 为空', 1);
            return $next($request);
        }
        if (!$bid) {
            //没有appid和openid不执行
            //            wr_log('$bid 为空', 1);
            return $next($request);
        }
        $token_name = 'business_user_token';
        $request->__set($token_name, $access_token);
        $platform_wechat_appid = get_system_config('platform_wechat_appid');
        if ($appid == $platform_wechat_appid) {
//            wr_log('appid和platform_openid一致,无需更新', 1);
            return $next($request);
        }
        $appid_type = $this->get_appid_type($appid);
        if ($appid_type != 1) {
            //不是公众号的不执行
//            wr_log('不是公众号的不执行,无需更新', 1);
            return $next($request);
        }
        if ($this->check_openid_mapping_exists($appid, $openid, $platform_wechat_appid, 1, 1)) {
//            debug_log('$openid=' . $openid . '----已经有关联关系,不再执行', 1);
            return $next($request);
        }
        $platform_wechat_appid   = get_system_config('platform_wechat_appid');
        $wechat_user_info        = WeixinService::get_open_id(null, $platform_wechat_appid);
        $platform_appid          = $wechat_user_info['appid'];
        $platform_openid         = $wechat_user_info['openid'];
        $insert_data             = [
            'bid'                 => $bid,
            'sid'                 => $access_token['sid'] ?? 0,
            'member_guid'         => '',
            'member_id'           => 0,
            'appid'               => $appid,
            'openid'              => $openid,
            'appid_type'          => 1, //公众号
            'relation_appid'      => $platform_appid, //关联公众号APPID
            'relation_openid'     => $platform_openid, //关联公众号OPENID
            'relation_appid_type' => 1, //关联类型公众号
        ];
        $db_appid_openid_mapping = new AppidOpenidMapping();
        $db_appid_openid_mapping->save($insert_data);
//        debug_log('$openid=' . $openid . '----平台openid 关联成功,不再执行', 1);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}