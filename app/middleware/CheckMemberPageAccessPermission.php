<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use Closure;
use Exception;
use think\Request;
use think\Response;

class CheckMemberPageAccessPermission extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $access_token = $this->get_member_access_token($request);
        if (empty($access_token)) {
            error('系统繁忙,请退出重新访问');
        }
        $jwt   = TokenService::decode($access_token);
        $sid   = $jwt['sid'] ?? 0;
        $appid = $jwt['appid'];
        if (empty($appid)) {
            error('系统繁忙,请退出重新访问2');
        }
        $bid                        = $jwt['bid'];
        $openid                     = $jwt['openid'];
        $member_guid                = $jwt['member_guid'];
        $member_id                  = $jwt['member_id'];
        $member_id_white_list       = get_system_config('member_id_white_list');
        $member_id_white_list_array = explode(',', $member_id_white_list);
        if (!in_array($member_id, $member_id_white_list_array)) {
            $msg = $member_id . '的编号 正在访问页面';
            wr_log($msg, 1);
            error('您无权限访问该页面,请将用户编号:' . $member_id . ' 发送给管理员申请权限!)');
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}