<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\tools\Visitor;
use app\model\Member as MemberModel;
use app\model\WechatConfig;
use Closure;
use Exception;
use think\Request;
use think\Response;

class CheckMemberMobile extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $path           = get_request_path();
        $need_auth_list = [
            'member/pay_qrcode',
            'code/member_code_list',
        ];
        if (Visitor::is_ding_talk()) {
            // 钉钉访问不执行 ,否则后面appid 去查找平台会报错
            return $next($request);
        }
        if (Visitor::is_work_weixin_browser()) {
            // 企业微信访问不执行 ,否则后面appid 去查找平台会报错
            return $next($request);
        }
        if (is_host()) {
            return $next($request);
        }
        if (!in_array($path, $need_auth_list)) {
            return $next($request);
        }
        //如果不是获取access_token 则校验是否有access_token参数
        $access_token = $this->get_member_access_token($request);
        if (empty($access_token)) {
            wr_log('$access_token 为空', 1);
            return $next($request);
        }
        $jwt   = TokenService::decode($access_token);
        $sid   = $jwt['sid'] ?? 0;
        $appid = $jwt['appid'];
        if (empty($appid)) {
            return $next($request);
        }
        $bid              = $jwt['bid'];
        $openid           = $jwt['openid'];
        $member_guid      = $jwt['member_guid'];
        $member_id        = $jwt['member_id'] ?? 0;
        $db_wechat_config = new WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['type']);
        $appid_type       = $info['type']; //1 公众号 2 小程序
        if ($appid_type != 2) {
            //不是小程序的不用执行
            //wr_log('不是小程序的不用执行', 1);
            return $next($request);
        }
        $map       = ['bid' => $bid, 'guid' => $member_guid];
        $db_member = new MemberModel();
        $member    = $db_member->get_member_info($map);
        if ($member['mobile']) {
            //            wr_log('$openid:' . $openid . '已经绑定了手机号' . $member['mobile'], 1);
            return $next($request);
        }
        $response = $next($request);
        /* @var $response Response */
        $response = $response->header(['LocationPath' => '/pages/get_mobile/get_mobile']);
        //        wr_log('$openid:' . $openid . '没有绑定手机号 返回 LocationPath 成功', 1);
        return $response;
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}