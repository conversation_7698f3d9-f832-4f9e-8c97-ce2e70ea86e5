<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\AuthVerify;
use Closure;
use Exception;
use think\Request;

class Mall extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $db = new AuthVerify();
        $db->verify();
        return $next($request);
    }
}