<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use app\model\Business as BusinessModel;
use Closure;
use Exception;
use think\Request;
use think\Response;

class BusinessStatusVerify extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $bid = $request->__get('_bid');
        if ($bid && Visitor::is_readonly() === false) {
            $db_business = new BusinessModel();
            //会员端主要检测是否锁定 过期也允许访问,否则客户案例很多过期影响不好
            $check = $db_business->login_verify($bid, false);
            if ($check === false) {
                error($db_business->getError());
            }
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}