<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\CropWeixinService;
use app\common\tools\Visitor;
use Closure;
use Exception;
use think\facade\View;
use think\Request;

class CropWeixinAuth extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Visitor::is_wechat_browser()) {
            error('请用微信浏览器访问');
        }
        $user_info = CropWeixinService::getOpenId();
        $openid    = $user_info['openid'];
        $appid     = $user_info['appid'];
        View::assign('openid', $openid);
        $request->__set('_appid', $appid);
        $request->__set('_openid', $openid);
        return $next($request);
    }
}
