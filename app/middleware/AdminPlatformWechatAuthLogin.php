<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\User;
use app\model\UserBindNote;
use Closure;
use Exception;
use think\Request;
use think\Response;

class AdminPlatformWechatAuthLogin extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $path                          = get_request_path();
        $wechat_auth_path              = [
            'mobile/index',
            'mobile/express',
            'code/exchange_m',
            'mobile/user_center',
            'coupon_send_note/active', //获取到的实际是 CouponSendNote
            'coupon_send_note/sale_code_m', //获取到的实际是 CouponSendNote
            'business/login',
            'passport/bind_user_success',
        ];
        $wechat_auth_path_pc_or_mobile = [
            'kefu/index',
        ];
        if (is_debug() || is_host()) {
            return $next($request);
        }
        if (!in_array($path, $wechat_auth_path) && !in_array($path, $wechat_auth_path_pc_or_mobile)) {
            return $next($request);
        }
        $redirect_url = $request->url(true);
        //缓存商家标识
        $access_token = $this->get_admin_access_token($request);
        $access_token = TokenService::get_valid_token_data($access_token);
        $token_name   = 'business_user_token';
        $bid          = $request->__get('_bid');
        //先判断是否存在缓存,如果存在缓存则直接解析
        if (empty($access_token) || empty($access_token['appid']) || empty($access_token['openid'])) {
            $wechat_user_info = [];
            if (Visitor::is_wechat_browser() || is_debug()) {
                $auth_bid = '';
                if ($bid) {
                    $config                      = get_config_by_bid($bid);
                    $user_bind_wechat_appid_type = $config['user_bind_wechat_appid_type'];
                    if ($user_bind_wechat_appid_type) {
                        //使用自己公众号
                        $auth_bid = $bid;
                    }
                }
                if ($auth_bid) {
                    $wechat_user_info = WeixinService::get_open_id($auth_bid);
                } else {
                    $platform_wechat_appid = get_system_config('platform_wechat_appid');
                    $wechat_user_info      = WeixinService::get_open_id(null, $platform_wechat_appid);
                }
            } else {
                if (in_array($path, $wechat_auth_path_pc_or_mobile)) {
                    return $next($request);
                } else {
                    //redirect($login_url); //后续可以做PC端登录
                    error('请用微信访问');
                }
            }
            $appid             = $wechat_user_info['appid'];
            $openid            = $wechat_user_info['openid'];
            $db_user_bind_note = new UserBindNote();
            $map               = [
                ['openid', '=', $openid],
                ['appid', '=', $appid],
                ['status', '=', 1],
            ];
            if ($bid) {
                $map[] = ['bid', '=', $bid];
            }
            $user_bind_note = $db_user_bind_note->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
            if ($user_bind_note->isEmpty()) {
                // 不在白名单 而且没有绑定记录 就去绑定
                $token_data = [
                    'exp'    => 3600,
                    'appid'  => $appid,
                    'openid' => $openid,
                    'way'    => 1, //1 公众号 2 小程序
                    'from'   => 5, //来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程序 5 H5工作台
                ];
                $token      = TokenService::encode($token_data);
                $login_url  = (string)url('admin/passport/login', ['token' => $token, 'callback' => urlencode($redirect_url)]);
                redirect($login_url);
            }
            $bid       = $user_bind_note['bid'];
            $user_guid = $user_bind_note['user_guid'];
            $user_info = [
                'appid'  => $appid,
                'openid' => $openid,
                'bid'    => $bid,
                'guid'   => $user_guid,
            ];
            $request->__set('_appid', $appid);
            $request->__set('_openid', $openid);
            $request->__set('_bid', $bid);
            $request->__set('_user_guid', $user_guid);
            // 获取逻辑里面会自动写入cookie,用于后面身份校验
            $db_user      = new User();
            $data         = $db_user->get_access_token($user_info, 'client_credential');
            $access_token = $data['access_token'];
        }
        $request->__set($token_name, $access_token);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}