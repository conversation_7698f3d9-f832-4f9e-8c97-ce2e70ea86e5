<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use Exception;
use think\Request;
use think\Response;

class DistributedLock extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $distributed_lock_config = config('distributed_lock');
        $distributed_lock_list   = $distributed_lock_config['list'];
        $request_path            = $this->parse_request_class($request);
        if (!isset($distributed_lock_list[$request_path])) {
            return $next($request);
        }
        $params    = $request->param();
        $key_array = $distributed_lock_list[$request_path];
        foreach ($key_array as $key) {
            if (isset($params[$key])) {
                $request_path .= $params[$key];
            } elseif ($request->__get($key)) {
                $request_path .= $request->__get($key);
            }
        }
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($distributed_lock_config['prefix'] . $request_path);
        $response      = $next($request);
        $lock_instance->unlock($lock);
        return $response;
    }

    public function end(Response $response)
    {
        // 回调行为
    }
}
