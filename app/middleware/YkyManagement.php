<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use Closure;
use Exception;
use think\Request;

class YkyManagement extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $key   = 'management_access_token';
        $token = cookie($key);
        if (empty($token)) {
            $redirect_url = $request->url(true);
            $login_url    = (string)url('index/management/login', ['callback' => urlencode($redirect_url)], true, true);
            if ($request->isGet()) {
                redirect($login_url);
            } else {
                error('请先登录!', -2, $login_url);
            }
        } else {
            $jwt     = TokenService::verify($token);
            $account = $jwt['account'];
            $request->__set('_management_account', $account);
        }
        return $next($request);
    }
}
