<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\Crontab;
use Closure;
use Exception;
use think\Request;
use think\Response;

class QueueTrigger extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
        // 回调行为
        $request         = request();
        $config_key      = 'crontab_trigger';
        $crontab_trigger = config($config_key);
        $request_path    = $this->parse_request_class($request);
        if (!isset($crontab_trigger[$request_path])) {
            return;
        }
        $params = $request->param();
        if (empty($params['bid'])) {
            return;
        }
        $bid       = $params['bid'];
        $cache_key = $config_key . ':' . $bid . ':' . $request_path;
        if (cache($cache_key)) {
            return;
        }
        $db       = new Crontab();
        $map      = [['bid', '=', $bid], ['class', 'IN', $crontab_trigger[$request_path]]];
        $job_list = $db->where($map)->select();
        foreach ($job_list as $job) {
            $db->resolve($job);
        }
        cache($cache_key, format_timestamp(), 10); //10秒内不重复触发
    }
}
