<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\UserBindNote;
use Closure;
use Exception;
use think\Request;
use think\Response;

class CheckWechatBindStatus extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $appid     = $request->__get('_appid');
        $openid    = $request->__get('_openid');
        $bid       = $request->__get('_bid');
        $user_guid = $request->__get('_user_guid');
        if ($appid && $openid && $bid && $user_guid) {
            $map               = [
                ['bid', '=', $bid],
                ['user_guid', '=', $user_guid],
                ['appid', '=', $appid],
                ['openid', '=', $openid],
            ];
            $db_user_bind_note = new UserBindNote();
            $bind_note         = $db_user_bind_note->field(['status'])->where($map)->findOrEmpty();
            if ($bind_note->isEmpty()) {
                error('当前用户未绑定,请重新登录');
            }
            if ($bind_note['status'] != 1) {
                tools()::clear_all_cookie_and_session();
                error('当前用户已解绑,请退出页面重新登录');
            }
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}