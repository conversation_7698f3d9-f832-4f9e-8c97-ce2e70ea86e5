<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

class CheckAllowMethod extends BasicMiddleware
{

    /**
     * 检测请求方式是否合法
     * @access public
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->isOptions()) {
            return $next($request);
        }
        $container                        = app()->make($this->parse_request_class($request, false));
        $method                           = $request->method(); // 获取当前请求方式
        $method_allowed_property_name     = 'MethodAllowed'; // 要检查的属性名(允许的方法)
        $method_not_allowed_property_name = 'MethodNotAllowed'; // 要检查的属性名(不允许的方法)
        if (isset($container->$method_allowed_property_name)) {
            //如果定义了允许方法 那只能是这些方法才认为合法请求
            $method_allowed = $container->$method_allowed_property_name;
            if (is_array($method_allowed) && !empty(array_filter($method_allowed)) && !in_array($method, $method_allowed)) {
                error('当前请求方式不被允许');
            }
        }
        if (isset($container->$method_not_allowed_property_name)) {
            //如果定义了不允许方法 那只要是属于这里的方法 则禁止请求
            $method_not_allowed = $container->$method_not_allowed_property_name;
            if (is_array($method_not_allowed) && !empty(array_filter($method_not_allowed)) && in_array($method, $method_not_allowed)) {
                error('当前请求方式不被允许!');
            }
        }
        return $next($request);
    }
}
