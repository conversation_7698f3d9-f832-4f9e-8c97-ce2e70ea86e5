<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use Exception;
use think\Request;
use think\Response;

class AppendHeaderMemberInfo extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $sid              = $request->__get('_sid');
        $member_id        = $request->__get('_member_id');
        $business_account = $request->__get('_business_account');
        $response         = $next($request);
        if ($sid && $member_id) {
            /* @var $response Response */
            $response = $response->header(['Member-Info' => ($business_account ?: $sid) . '@' . $member_id]);
        }
        return $response;
    }
}