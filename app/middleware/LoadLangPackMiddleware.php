<?php

/*
 * @author: 杨红兵
 * @date: 2023-09-01 08:47:54
 * @last modified by:   杨红兵
 * @last modified time: 2023-09-01 08:47:54
 */

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use think\Lang;
use think\middleware\LoadLangPack;
use think\Request;
use think\Response;
use think\swoole\App;
use think\swoole\concerns\ModifyProperty;

/**
 * 重写多语言中间件
 *
 * <AUTHOR>
 * @datetime 2023-09-01 08:48:26
 *
 */
class LoadLangPackMiddleware extends LoadLangPack
{
    use ModifyProperty;

    /**
     * 多应用的语言集
     *
     * @var array
     * <AUTHOR>
     * @datetime 2023-09-01 08:56:25
     *
     */
    protected static $langList = [];

    /**
     * 处理方法
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     * <AUTHOR>
     * @datetime 2023-09-01 08:49:22
     *
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Visitor::is_swoole()) {
            return $next($request);
        }
        // 自动侦测当前语言
        $langset = $this->detect($request);
        if ($this->app instanceof App) {
            // Swoole应用下工作 .
            $appName   = $this->app->http->getName();
            $cacheName = $appName . '-' . $langset;
            if (!isset(static::$langList[$cacheName])) {
                //本应(进程)用的本语言第一个请求，将进行多语言加载，可能会稍微慢一点点，不要在意
                $lang       = $this->app->make(Lang::class, [], true);
                $this->lang = $lang;
                //lang是新的对像，马上进行语言包加载
                $this->lang->switchLangSet($langset);
                //静态变量存在进程空间,一个进程一份
                static::$langList[$cacheName] = $lang;
            } else {
                $this->lang = clone static::$langList[$cacheName];
                $this->modifyProperty($this->lang, $this->app);
            }
            //更新绑定
            $this->app->instance('lang', $this->lang);
        } else {
            //兼容FPM模式
            if ($this->lang->defaultLangSet() != $langset) {
                $this->lang->switchLangSet($langset);
            }
        }

        $this->saveToCookie($this->app->cookie, $langset);

        return $next($request);
    }
}
