<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\model\AppidOpenidMapping;
use think\Request;
use think\Response;


class BasicMiddleware
{
    public function get_admin_access_token(Request $request)
    {
        $cookie_prefix      = 'business_guid';
        $admin_access_token = $this->get_token($request, 'business_user_token', $cookie_prefix);
        $get_value          = $request->get($cookie_prefix);
        if ($admin_access_token && $get_value) {
            //token 获取到了值并且url 上有值 校验一致性 如果和url 上bid不一致 则token视为无效
            $jwt = TokenService::decode($admin_access_token);
            $bid = $jwt['bid'];
            if ($bid != $get_value) {
                wr_log('get_admin_access_token cookie中BID:' . $bid . '.当前参数BID:' . $get_value . ',不一致', 1);
                return null;
            }
        }
        return $admin_access_token;
    }

    public function parse_request_class(Request $request, $with_action = true): string
    {
        $class_name = app()->parseClass('controller', $request->controller());
        $with_action && $class_name .= '@' . $request->action();
        return $class_name;
    }

    public function get_member_access_token(Request $request)
    {
        $cookie_prefix       = 'bid';
        $member_access_token = $this->get_token($request, 'access_token', $cookie_prefix);
        $get_value           = $request->get($cookie_prefix);
        if ($member_access_token && $get_value) {
            //token 获取到了值并且url 上有值 校验一致性 如果和url 上bid不一致 则token视为无效
            $jwt = TokenService::decode($member_access_token);
            $bid = $jwt['bid'];
            if ($bid != $get_value) {
                wr_log('get_member_access_token cookie中BID:' . $bid . '.当前参数BID:' . $get_value . ',不一致', 1);
                return null;
            }
        }
        return $member_access_token;
    }

    public function remove_admin_access_token_cookie()
    {
        $token_name    = 'business_user_token';
        $cookie_prefix = 'business_guid';
        cookie($token_name, null);
        if ($business_guid = cookie($cookie_prefix)) {
            cookie($business_guid . '_' . $token_name, null);
        }
    }

    protected function get_token(Request $request, $token_name, $cookie_prefix)
    {
        // 如果不是获取access_token 则校验是否有access_token参数
        if ($access_token = $request->get($token_name)) {
            //优先获取get参数
        } elseif ($access_token = $request->post($token_name)) {
            //其次获取post参数
        } elseif ($access_token = $request->header($token_name)) {
            //其次获取header参数
        } elseif ($access_token = $request->__get($token_name)) {
            //最后从request参数中获取
        } elseif (cookie($cookie_prefix) && $access_token = cookie(cookie($cookie_prefix) . '_' . $token_name)) {
            //都不存在判断是否存在缓存商家标识,有尝试获取cookies中是否有token
        } else {
            //还是不存在则提示异常
            //            $msg = '登录超时,请您重新登陆~';
            //            error($msg, -2);
        }
        return $access_token;
    }

    public function append_header_access_token(Response $response, $access_token)
    {
        return $response->header(['Authorization' => 'Bearer ' . $access_token]);
    }

    protected function get_link_str($url)
    {
        return stristr($url, '?') ? '&' : '?';
    }

    /**
     * 检查APPID类型
     * @param string $appid
     * @return int 1表示公众号，2表示小程序
     */
    protected function get_appid_type($appid)
    {
        $db_wechat_config = new \app\model\WechatConfig();
        $info             = $db_wechat_config->get_appid_info($appid, ['type']);
        return $info['type']; // 1表示公众号，2表示小程序
    }

    /**
     * 检查OpenID映射关系是否存在
     * @param string $appid APPID
     * @param string $openid OpenID
     * @param string $relation_appid 关联的APPID
     * @param int $appid_type APPID类型，默认2表示小程序
     * @param int $relation_appid_type 关联APPID类型，默认1表示公众号
     * @return bool
     */
    protected function check_openid_mapping_exists($appid, $openid, $relation_appid, $appid_type = 2, $relation_appid_type = 1)
    {
        $db_appid_openid_mapping = new AppidOpenidMapping();
        $map                     = [
            ['appid', '=', $appid],
            ['openid', '=', $openid],
            ['appid_type', '=', $appid_type], //小程序
            ['relation_appid', '=', $relation_appid], //关联公众号APPID
            ['relation_appid_type', '=', $relation_appid_type], //关联类型公众号
        ];
        return $db_appid_openid_mapping->where($map)->count() > 0;
    }
}