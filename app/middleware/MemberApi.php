<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use Closure;
use Exception;
use think\Request;
use think\Response;

class Member<PERSON>pi extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $path          = get_request_path();
        $not_auth_list = [
            'passport/register',
            'passport/login',
            'passport/get_auth_url',
            'sms/send_sms_code',
            'pay/query_result',
        ];
        if (in_array($path, $not_auth_list)) {
            return $next($request);
        }
        //如果不是获取access_token 则校验是否有access_token参数
        $access_token = $this->get_member_access_token($request);
        if (!$access_token) {
            //还是不存在则提示异常
            $msg = '系统繁忙,请退出网页重新进入再试!';
            error($msg, -2);
        }
        $token_name = 'access_token';
        //校验access_token的有效性,是否过期 是否有效等
        $jwt = TokenService::verify($access_token);
        $request->__set('_jwt_data', $jwt);
        $bid              = $jwt['bid'];
        $sid              = $jwt['sid'];
        $business_account = $jwt['business_account'] ?? '';
        $member_guid      = $jwt['member_guid'];
        $member_id        = $jwt['member_id'];
        $request->__set('_bid', $bid);
        $request->__set('_sid', $sid);
        $request->__set('_member_guid', $member_guid);
        $request->__set('_member_id', $member_id);
        $request->__set('_business_account', $business_account);
        $request->__set($token_name, $access_token);
        !empty($jwt['appid']) && $request->__set('_appid', $jwt['appid']);
        !empty($jwt['openid']) && $request->__set('_openid', $jwt['openid']);
        /* @var $response Response */
        $response = $next($request);
        return $this->append_header_access_token($response, $access_token);
    }
}