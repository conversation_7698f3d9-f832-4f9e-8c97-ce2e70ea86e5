<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\AlipayService;
use app\common\service\CropWeixinService;
use app\common\service\DingTalkService;
use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\Business;
use app\model\Member as MemberModel;
use Closure;
use Exception;
use think\facade\View;
use think\Request;
use think\Response;

class Member extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params     = $request->param();
        $controller = get_controller();
        $path       = get_request_path();
        $token_name = 'access_token';
        if ($path == 'pay/result') {
            return $next($request);
        }
        if (in_array($controller, ['passport', 'work_weixin', 'workweixin'])) {
            return $next($request);
        }
        if (empty($params['bid'])) {
            error('商家身份为空');
        }
        $redirect_url = $request->url(true);
        //获取商家标识
        $bid = strtolower($params['bid']);
        View::assign('bid', $bid);
        $config = get_config_by_bid($bid);
        $request->__set('_config', $config);
        $request->__set('_bid', $bid);
        //缓存商家标识
        $exp = 3600 * 24;
        cookie('bid', $bid, $exp);
        cookie('debug', (int)is_debug(), $exp);
        $access_token_cache_key = $bid . '_' . $token_name;
        $access_token           = $this->get_member_access_token($request);
        $user_info              = [];
        //先判断是否存在缓存,如果存在缓存则直接解析
        if ($access_token) {
            $jwt              = TokenService::decode($access_token);
            $access_token_bid = $jwt['bid'];
            $appid            = $jwt['appid'];
            $openid           = $jwt['openid'];
            $member_guid      = $jwt['member_guid'];
            $user_info        = [
                'appid'       => $appid,
                'openid'      => $openid,
                'member_guid' => $member_guid,
            ];
            $wechat_appid     = $config['appid'];
            $weapp_appid      = $config['weappid'];
            if (($wechat_appid || $weapp_appid) && !in_array($appid, [$wechat_appid, $weapp_appid]) && Visitor::is_wechat_browser()) {
                //微信浏览器中检测appid是否发生变更
                cookie($access_token_cache_key, null);
                wr_log('cookie中APPID:' . $appid . '.当前授权APPID:' . $config['appid'] . ',自动清空access_token成功', 1);
                $user_info = [];
            }
            if ($bid != $access_token_bid) {
                cookie($access_token_cache_key, null);
                wr_log('cookie中BID:' . $access_token_bid . '.当前参数BID:' . $bid . ',自动清空access_token成功', 1);
                $user_info = [];
            }
            //          $cache_key = 'user_info:' . $openid;
            //          $scope     = $params['scope'] ?? 'snsapi_base';
            //          if ($scope == 'snsapi_userinfo' && !cache($cache_key)) {
            //              //要求授权用户信息但是缓存中没有用户信息
            //              cookie($access_token_cache_key, null);
            //              $user_info = [];
            //          }
        }
        if (empty($user_info)) {
            if (Visitor::is_work_weixin_browser()) {
                $user_info = CropWeixinService::getOpenId();
            } else
                if (Visitor::is_wechat_browser() || is_debug() || is_host()) {
                    $user_info = WeixinService::get_open_id($bid);
                } elseif (Visitor::is_alipay_browser()) {
                    $user_info = AlipayService::get_open_id($bid);
                } elseif (Visitor::is_ding_talk()) {
                    $user_info = DingTalkService::get_open_id($bid);
                } else {
                    $auto_register_web = $config['auto_register_web'];
                    if ($auto_register_web) {
                        $user_info['member_guid'] = create_guid();
                    } else {
                        error('请使用微信访问');
                        $login_url = (string)url('member/passport/login', ['bid' => $bid, 'callback' => urlencode($redirect_url)]);
                        redirect($login_url);
                    }
                }
        }
        $db_member = new MemberModel();
        if (!empty($user_info['appid']) && !empty($user_info['openid'])) {
            $appid  = $user_info['appid'];
            $openid = $user_info['openid'];
            $map    = ['bid' => $bid, 'openid' => $openid, 'appid' => $appid];
        } elseif (!empty($user_info['member_guid'])) {
            $map = ['bid' => $bid, 'guid' => $user_info['member_guid']];
        } else {
            throw new Exception('会员查找失败');
        }
        $member        = $db_member->get_member_info($map);
        $appid         = $member['appid'] ?? '';
        $openid        = $member['openid'] ?? '';
        $member_guid   = $member['guid'];
        $member_id     = $member['id'];
        $db_business   = new Business();
        $business_info = $db_business->get_business_info_by_account_or_guid($bid);
        if (empty($business_info)) {
            throw new Exception('商户不存在');
        }
        $token_data   = [
            'exp'              => $exp,
            'bid'              => $bid,
            'sid'              => $business_info['id'],
            'business_account' => $business_info['account'],
            'appid'            => $appid,
            'openid'           => $openid,
            'member_guid'      => $member_guid,
            'member_id'        => $member_id
        ];
        $access_token = TokenService::encode($token_data);
        cookie($access_token_cache_key, $access_token, $exp);
        View::assign('openid', $openid);
        $request->__set($token_name, $access_token);
        $request->__set('_appid', $appid);
        $request->__set('_openid', $openid);
        $request->__set('_member_guid', $member_guid);
        $request->__set('_member_id', $member_id);
        $request->__set('_sid', $business_info['id']);
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}