<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\TxtFile;
use Closure;
use Exception;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;

class CheckTxtFile extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $ext = $request->ext();
        if ($ext == 'txt') {
            $file_name    = $request->route('file_name') . '.txt';
            $db           = new TxtFile();
            $map          = [['file_name', '=', $file_name]];
            $file_content = $db->where($map)->value('file_content');
            if ($file_content) {
                $response = Response::create($file_content);
                throw new HttpResponseException($response);
            }
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}