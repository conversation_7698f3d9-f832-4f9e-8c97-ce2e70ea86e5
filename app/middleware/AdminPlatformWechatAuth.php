<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\service\WeixinService;
use app\common\tools\Visitor;
use Closure;
use Exception;
use think\Request;
use think\Response;

class AdminPlatformWechatAuth extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->get();
        $path   = get_request_path();
        //仅允许微信访问,强依赖appid+openid
        $must_wechat_auth_path = [
            'business/register',
        ];
        //可以非微信访问
        $optional_wechat_auth_path = [
            //            'common/login',
        ];
        $is_wechat_browser         = Visitor::is_wechat_browser();
        if (!in_array($path, $must_wechat_auth_path) && !in_array($path, $optional_wechat_auth_path)) {
            return $next($request);
        }
        $token_name = 'token';
        if (!empty($params[$token_name])) {
            return $next($request);
        }
        if (in_array($path, $optional_wechat_auth_path) && !$is_wechat_browser) {
            return $next($request);
        }
        if (!$is_wechat_browser && !is_debug()) {
            error('请用微信访问!');
        }
        $redirect_url = $request->url(true);
        //先判断是否存在缓存,如果存在缓存则直接解析
        $bid      = $request->__get('_bid');
        $auth_bid = '';
        if ($bid) {
            $config                      = get_config_by_bid($bid);
            $user_bind_wechat_appid_type = $config['user_bind_wechat_appid_type'];
            if ($user_bind_wechat_appid_type) {
                //使用自己公众号
                $auth_bid = $bid;
            }
        }
        if ($auth_bid) {
            $wechat_user_info = WeixinService::get_open_id($auth_bid);
        } else {
            $platform_wechat_appid = get_system_config('platform_wechat_appid');
            $wechat_user_info      = WeixinService::get_open_id(null, $platform_wechat_appid);
        }
        $appid      = $wechat_user_info['appid'];
        $openid     = $wechat_user_info['openid'];
        $token_data = [
            'exp'    => 3600,
            'appid'  => $appid,
            'openid' => $openid,
            'way'    => 1, //1 公众号 2 小程序
            'from'   => 6, //来源 0 默认 1 公众号场景码 2 H5链接(登录页面) 3 admin后台H5 4提货小程序 5 H5工作台
        ];
        $token      = TokenService::encode($token_data);
        $url        = $redirect_url . $this->get_link_str($redirect_url) . 'token=' . $token;
        redirect($url);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}