<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use Exception;
use think\Request;
use think\Response;

class AppendHeaderUserInfo extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return Response
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $sid              = $request->__get('_sid');
        $user_id          = $request->__get('_user_id');
        $business_account = $request->__get('_business_account');
        $user_account     = $request->__get('_user_account');
        $response         = $next($request);
        if ($sid && $user_id) {
            /* @var $response Response */
            $response = $response->header(['User-Info' => ($business_account ?: $sid) . '@' . ($user_account ?: $user_id)]);
        }
        return $response;
    }
}