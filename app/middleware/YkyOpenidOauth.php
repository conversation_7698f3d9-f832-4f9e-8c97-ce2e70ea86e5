<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use Exception;
use think\Request;
use xieyongfa\yky\Yky;

class YkyOpenidOauth extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->param();
        if (empty($params['bid'])) {
            error('商家标识不能为空');
        }
        $bid        = $params['bid'];
        $key        = $bid . '_yky_openid_vale';
        $yky_openid = cookies($key);
        if ($yky_openid) {
            return $next($request);
        }
        if (is_host()) {
            return $next($request);
        }
        if (!Visitor::is_wechat_browser()) {
            error('请用微信浏览器访问');
        }
        $config    = get_config_by_bid($bid);
        $yky_oauth = Yky::Oauth($config);
        if (empty($params['identity']) && empty($params['openId'])) {
            //不存在则进行网页授权
            $redirect_url = $request->url(true);
            $result       = $yky_oauth->GetOAuthUrl(urlencode($redirect_url));
            if ($result === false) {
                error('系统繁忙，请稍后再试:' . $yky_oauth->message);
            }
            redirect($result['oAuthUrl']);
            //{"status":0,"oAuthUrl":"https://bkchina.h5.yunhuiyuan.cn/OAuth/Authorize/bd8b3126-9f34-e711-95b5-0010186c9142?bid=e02cbb7b-a9e7-e311-a603-90b11c47e695","message":"获取成功"}
        }
        $signature = $params['signature'];
        $timestamp = $params['timestamp'];
        $yky_oauth->checkSignature($timestamp, $signature);
        //https://www.baidu.com/?openId=D5A508D48F6C4BB5BE3D144301B1DABD&relationId=c8b151f9-d676-11e8-9f73-0010185de866&identity=oZa5mwwRQi6Gu5EPzkmXG7W8lpMc&timestamp=1681778903&signature=9E9B848BE3873A10B70A502F17A206FF
        $yky_openid = $params['identity'] ?? '';
        if (empty($yky_openid)) {
            error('会员系统openid获取失败!', -1);
        }
        cookies($key, $yky_openid, 3600);
        return $next($request);
    }
}
