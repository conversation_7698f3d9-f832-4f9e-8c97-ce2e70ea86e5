<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use Exception;
use think\Request;
use xieyongfa\yky\Yky;

class YkyMemberOauth extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->param();
        if (empty($params['bid'])) {
            error('商家标识不能为空');
        }
        $bid             = $params['bid'];
        $key             = $bid . '_yky_member_guid_vale';
        $yky_member_guid = cookie($key);
        if ($yky_member_guid) {
            return $next($request);
        }
        if (!Visitor::is_wechat_browser()) {
            error('请用微信浏览器访问');
        }
        $config    = get_config_by_bid($bid);
        $yky_oauth = Yky::Oauth($config);
        if (empty($params['relationId']) && empty($params['openId'])) {
            //不存在则进行网页授权
            $redirect_url = $request->url(true);
            $result       = $yky_oauth->GetOAuthUrl(urlencode($redirect_url));
            if ($result === false) {
                error('系统繁忙，请稍后再试:' . $yky_oauth->message);
            }
            redirect($result['oAuthUrl']);
            //{"status":0,"oAuthUrl":"https://bkchina.h5.yunhuiyuan.cn/OAuth/Authorize/bd8b3126-9f34-e711-95b5-0010186c9142?bid=e02cbb7b-a9e7-e311-a603-90b11c47e695","message":"获取成功"}
        }
        //https://www.baidu.com/?openId=D5A508D48F6C4BB5BE3D144301B1DABD&relationId=c8b151f9-d676-11e8-9f73-0010185de866&identity=oZa5mwwRQi6Gu5EPzkmXG7W8lpMc&timestamp=1681778903&signature=9E9B848BE3873A10B70A502F17A206FF
        $yky_member_guid = $yky_member_guid ?: ($params['relationId'] ?? '');
        $openid          = $params['identity'] ?? '';
        $signature       = $params['signature'];
        $timestamp       = $params['timestamp'];
        $yky_oauth->checkSignature($timestamp, $signature);
        if (empty($yky_member_guid) && $openid) {
            $member      = Yky::Member($config);
            $member_info = $member->GetMemberGuidByOpenId(['thirdOpenId' => $openid]);//此接口即便会员过期 也能查询出来
            if ($member_info !== false && is_array($member_info)) {
                $yky_member_guid = $member_info['memberGuid'] ?? '';
                wr_log('openid=' . $openid . '--$yky_member_guid= ' . $yky_member_guid . 'info=' . json_encode($member_info, JSON_UNESCAPED_UNICODE));
            }
        }
        if (empty($yky_member_guid)) {
            $db_yky_member   = new \app\model\YkyMember();
            $member_card_url = $db_yky_member->get_member_card_url($bid, $request->url(true), $params['store_guid'] ?? '');
            error('请先绑定会员卡!', -1, $member_card_url);
        }
        cookie($key, $yky_member_guid, 3600);
        return $next($request);
    }
}
