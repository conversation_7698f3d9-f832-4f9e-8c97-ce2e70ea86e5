<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

class AllowCrossDomain extends BasicMiddleware
{
    protected array $header = [
        'Access-Control-Allow-Credentials' => 'true',
        'Access-Control-Allow-Methods'     => 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers'     => 'Business-User-Token, Business-Account, User-Account, App-Name, App-Version, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With, access-token',
    ];

    /**
     * 允许跨域请求
     * @access public
     * @param Request $request
     * @param Closure $next
     * @param array|null $header
     * @return Response
     */
    public function handle(Request $request, Closure $next, ?array $header = []): Response
    {
        $header = !empty($header) ? array_merge($this->header, $header) : $this->header;
        $origin = $request->header('origin');
        if (!isset($header['Access-Control-Allow-Origin']) && $origin && $origin !== 'null') {
            $top_host           = tools()::get_top_host($origin);
            $cors_host          = config('allow_cross_domain');
            $request_path       = $this->parse_request_class($request);
            $allow_request_path = array_merge($cors_host['*'] ?? [], $cors_host[$top_host] ?? []);
            if (array_intersect(['*', $request_path], $allow_request_path) && !empty($top_host)) {
                $header['Access-Control-Allow-Origin'] = $origin;
            } else {
                $header['Access-Control-Allow-Origin'] = '*';
                if (strpos($origin, '127.0.0.1') === false) {
                    wr_log('检测到跨域请求,来路域名:' . $request->url(true) . '域名:' . $origin . ', allow_request_path: ' . json_encode($allow_request_path));
                }
                //return Response::create()->code(401)->header($header); //后续严格校验,直接返回401状态码
            }
        }
        if ($request->isOptions()) {
            return Response::create()->code(204)->header($header);
        }
        /* @var $response Response */
        $response = $next($request);
        return $response->header($header);
    }
}
