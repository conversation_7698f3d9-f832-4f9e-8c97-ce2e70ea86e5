<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\WeixinService;
use app\common\tools\Visitor;
use app\model\Member as MemberModel;
use app\model\WechatUserInfo;
use Closure;
use Exception;
use think\Request;
use think\Response;

class MemberWechatUserInfo extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Visitor::is_wechat_browser()) {
            return $next($request);
        }
        $bid         = $request->__get('_bid');
        $appid       = $request->__get('_appid');
        $openid      = $request->__get('_openid');
        $member_guid = $request->__get('_member_guid');

        $db_wechat_user_info = new WechatUserInfo();
        if (!$db_wechat_user_info->is_require_auth_user_info($appid, $openid)) {
            return $next($request);
        }
        $params       = $request->param();
        $redirect_url = $params['redirect_url'] ?? $request->url(true);
        $params_code  = $params['code'] ?? '';
        $params_state = $params['state'] ?? '';
        $params_appid = $params['appid'] ?? '';

        //校验链接上得http://www.yikayi.net/admin/passport/bind_user_success?bid=f67ff919-a827-cb90-e6d6-104d99423473&code=011DhB2002HT8R1zme30058OOg2DhB2C&state=wxf17a5bac18ded2d3&appid=wxf17a5bac18ded2d3
        if (strlen($params_code) == 32 && $params_state && $params_appid && $params_appid == $params_state && $appid != $params_appid) {
            wr_log('发现当前url上的参数(member_wechat_user_info)和需要授权的appid参数不一致:' . $request->url(true), 1);
            $length = strrpos($redirect_url, '?code') ?: strrpos($redirect_url, '&code');
            redirect(substr($redirect_url, 0, $length));
        }
        $instance = WeixinService::get_instance($appid)::WeChatOauth();
        $scope    = 'snsapi_userinfo';
        if (empty($params['code']) || (strlen($params['code']) != 32)) {
            //链接上无code参数则构造授权链接
            $oauth_url = $instance->getOauthRedirect($redirect_url, $appid, $scope);
            redirect($oauth_url);
        }
        //有参数则进行解析code 获取openid
        $result           = $instance->getOauthAccessToken();
        $wechat_user_info = $instance->getUserInfo($result['access_token'], $openid);
        $db_wechat_user_info->auto_update_wechat_user_info($appid, $openid, $wechat_user_info);
        if ($bid && $member_guid) {
            $db_member   = new MemberModel();
            $update_data = [
                'bid'      => $bid,
                'guid'     => $member_guid,
                'name'     => $wechat_user_info['nickname'] ?? '',
                'head_img' => $wechat_user_info['headimgurl'] ?? '',
            ];
            $db_member->update_member_info($update_data);
        }
        $length = strrpos($redirect_url, '?code') ?: strrpos($redirect_url, '&code');
        redirect(substr($redirect_url, 0, $length));
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}