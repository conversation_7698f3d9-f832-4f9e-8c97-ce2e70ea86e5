<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use Closure;
use Exception;
use think\facade\View;
use think\Request;

class WorkWeixinAuth extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Visitor::is_wechat_browser()) {
            error('请用微信浏览器访问');
        }
        $user_info = $this->getOpenId($request);
        $openid    = $user_info['openid'];
        $appid     = $user_info['appid'];
        View::assign('openid', $openid);
        $request->__set('_appid', $appid);
        $request->__set('_openid', $openid);
        return $next($request);
    }

    public function getOpenId($request_obj)
    {
        $request = request();
        /* @var $request_obj Request */
        $bid               = input('bid');
        $config            = get_config_by_bid($bid);
        $crop_wechat_appid = $config['crop_wechat_appid'];
        if (!$crop_wechat_appid) {
            error('商户未授权对接企业微信');
        }
        $redirect_url = $request->url(true);
        $domain       = $request->domain();
        $param        = $request->param();
        $redirect_url = str_replace($domain . $domain, $domain, $redirect_url);
        $key          = 'crop_wechat_userid' . $crop_wechat_appid;
        $openid       = cookies($key);
        if ($openid) {
            return ['appid' => $crop_wechat_appid, 'openid' => $openid];
        }
        $auth = work_weixin($bid)::Auth();
        //不存在缓存则进入授权逻辑
        if (empty($param['code'])) {
            $url = $auth->get_user_auth_url($redirect_url);
            redirect($url);
        } else {
            $code    = $param['code'];
            $contact = work_weixin($bid)::Contact();
            $result  = $contact->get_user_info_3rd($code);
            halt($result);
            $referer    = $request->header('referer');
            $user_agent = $request->header('user_agent');
            if (isset($result['openid'])) {
                $openid = $result['openid'];
            } elseif (isset($result['UserId'])) {
                $result = $auth->toOpenId($result['UserId']);
                $openid = $result['openid'];
            } else {
                wr_log('UserId获取失败,appid:' . $appid . ',user_agent:' . $user_agent . ',referer:' . $referer, 1);
                error('系统繁忙,请您退出网页重新进入!');
            }
        }
        cookies($key, $openid, 3600);
        $url = tools()::remove_after_code_url($redirect_url);
        redirect($url);
    }
}
