<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\OperationLog;
use Closure;
use Exception;
use think\Request;
use think\Response;

class MemberApiLog extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    public function end(Response $response)
    {
        $db = new OperationLog();
        $db::record($response);
    }
}