<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\tools\Visitor;
use app\model\Member as MemberModel;
use app\model\WechatCard;
use Closure;
use Exception;
use think\Request;
use think\Response;

class MemberMobileLogin extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (is_debug()) {
            return $next($request);
        }
        $params       = $request->param();
        $redirect_url = $request->url(true);
        //获取商家标识
        //      $bid = strtolower($params['bid']);
        //      View::assign('bid', $bid);
        //缓存商家标识
        //      $access_token_cache_key = $bid . '_access_token';
        //      $access_token           = cookie($access_token_cache_key);
        //      $access_token = $request->__get('_access_token');
        //      $jwt          = TokenService::decode($access_token);
        //      $user_info    = [];
        //      $appid        = $jwt['appid'];
        //      $openid       = $jwt['openid'];
        //      $member_guid  = $jwt['member_guid'];
        $bid         = $request->__get('_bid');
        $member_guid = $request->__get('_member_guid');
        $db_member   = new MemberModel();
        $map         = ['bid' => $bid, 'guid' => $member_guid];
        $member      = $db_member->get_member_info($map, false);
        if (empty($member)) {
            error('会员信息身份为空');
        }
        $login_url = (string)url('member/passport/user_login', ['bid' => $bid, 'callback' => urlencode($redirect_url)]);
        if (empty($member['mobile'])) {
            if (Visitor::is_wechat_browser()) {
                //$user_info = WeixinService::getOpenId($bid);
                //微信中跳转到卡券
                $db_wechat_card  = new WechatCard();
                $wechat_card_url = $db_wechat_card->get_wechat_card_url(urlencode($redirect_url));
                if ($wechat_card_url) {
                    redirect($wechat_card_url);
                }
            }
            redirect($login_url);
        }
        return $next($request);
    }

    /**
     * 结束调度
     * @param Response $response
     * @return void
     * @throws Exception
     */
    public function end(Response $response)
    {
    }
}