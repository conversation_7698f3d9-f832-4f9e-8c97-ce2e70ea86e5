<?php
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

class ForceHttps extends BasicMiddleware
{

    /**
     * 允许跨域请求
     * @access public
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (strtolower($request->scheme()) == 'https') {
            return $next($request);
        }
        $url = $request->url(true);
        redirect(str_replace('http://', 'https://', $url));
    }
}
