<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2021 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\middleware;

use Closure;
use think\Cache;
use think\Config;
use think\Request;
use think\Response;

/**
 * 请求缓存处理
 */
class RequestCache extends BasicMiddleware
{
    /**
     * 缓存对象
     * @var Cache
     */
    protected Cache $cache;

    /**
     * 配置参数
     * @var array
     */
    protected array $config = [
        //缓存前缀
        'request_cache_key_prefix' => 'request_cache:',
        // 请求缓存有效期
        'request_cache_expire'     => 60,
        // 排除掉参数key 用于不同用户范围能共用缓存 如加载商品列表
        'except_param_key'         => ['business_user_token', 'access_token']
    ];

    public function __construct(Cache $cache, Config $config)
    {
        $this->cache  = $cache;
        $this->config = array_merge($this->config, $config->get('request_cache'));
    }

    /**
     * 设置当前地址的请求缓存
     * @access public
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->isPost() === false || empty($request->__get('_bid'))) {
            return $next($request);
        }
        $request_path = $this->parse_request_class($request);
        if (!isset($this->config['list'][$request_path])) {
            return $next($request);
        }
        $config = $this->config['list'][$request_path];
        $expire = $config['exp'] ?? $this->config['request_cache_expire'];
        $key    = $this->parseCacheKey($request, $request_path);
        if (strtotime($request->server('HTTP_IF_MODIFIED_SINCE')) + $expire > $request->server('REQUEST_TIME')) {
            // 读取缓存
            return Response::create()->code(304);
        } elseif (($hit = $this->cache->get($key)) !== null) {
            [$content, $header, $when] = $hit;
            if (null === $expire || $when + $expire > $request->server('REQUEST_TIME')) {
                return Response::create($content)->header($header);
            }
        }
        // 执行具体业务逻辑
        /* @var $response Response */
        $response = $next($request);
        if (200 == $response->getCode()) {
            $response_data = $response->getData();
            $response_data = tools()::is_json($response_data) ? json_decode($response_data, true) : $response_data;
            $code          = (is_array($response_data) && isset($response_data['code'])) ? $response_data['code'] : -1;
            $data          = (is_array($response_data) && isset($response_data['data'])) ? $response_data['data'] : [];
            if ($code == 0 && !empty($data)) {
                $header                  = $response->getHeader();
                $time                    = time();
                $header['Cache-Control'] = 'max-age=' . $expire . ',must-revalidate';
                $header['Last-Modified'] = gmdate('D, d M Y H:i:s') . ' GMT';
                $header['Expires']       = gmdate('D, d M Y H:i:s', $time + $expire) . ' GMT';
                $this->cache->set($key, [$response->getContent(), $header, $time], $expire);
            }
        }
        return $response;
    }

    /**
     * 读取当前地址的请求缓存信息
     * @access protected
     * @param Request $request
     * @param string $request_path
     * @return string
     */
    protected function parseCacheKey(Request $request, $request_path)
    {
        $param = $request->param();
        foreach ($this->config['except_param_key'] as $except_param_key) {
            unset($param[$except_param_key]);
        }
        return $this->config['request_cache_key_prefix'] . $request->__get('_bid') . ':' . $request_path . md5(json_encode($param, JSON_UNESCAPED_UNICODE));
    }
}
