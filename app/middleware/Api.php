<?php
declare (strict_types=1);

namespace app\middleware;

use app\model\Business;
use Closure;
use Exception;
use think\Request;

class Api extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);//暂时不影响
        $data = tools()::xml2arr(file_get_contents("php://input"));
        if (empty($data)) {
            return $this->buildFailResponse('未接受到数据');
        }
        if (empty($data['appid']) && empty($data['mch_id'])) {
            return $this->buildFailResponse('缺少 appid 和 mch_id参数');
        }
        $db = new Business();
        //wx开头则取 mch_id 反之取appid参数
        $is_really_appid = tools()::start_with($data['appid'], 'wx');
        $appid           = $is_really_appid ? $data['mch_id'] : $data['appid'];
        $business_info   = $db->get_business_info_by_appid_secret($appid);
        if (!$business_info) {
            return $this->buildFailResponse($db->getError());
        }
        $secret = $business_info['secret'];
        $bid    = $business_info['guid'];
        if ($this->verify($data, $secret) === false) {
            wr_log('签名不合法', 1);
            return $this->buildFailResponse('签名不合法');
        }
        if (!$is_really_appid) {
            $config        = get_config_by_bid($bid);
            $data['appid'] = $config['weappid']; //重新赋值小程序appid
        }
        return $next($request);
    }

    protected function buildFailResponse($msg)
    {
        $result = [
            'return_code' => 'FAIL',
            'return_msg'  => $msg,
        ];
        return xml($result, 200, [], ['root_node' => 'xml']);
    }

    /**
     * XML内容验证
     * @param array $data
     * @param string|null $key
     * @param string|null $sign
     * @return  bool|array
     * @throws Exception
     */
    public function verify($data, $key, $sign = null)
    {
        $sign = is_null($sign) ? $data['sign'] : $sign;
        return tools()::get_sign($data, $key) === $sign;
    }
}