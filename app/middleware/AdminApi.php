<?php
declare (strict_types=1);

namespace app\middleware;

use app\common\service\TokenService;
use app\common\tools\Visitor;
use app\model\Business;
use app\model\Rule;
use Closure;
use Exception;
use think\Request;
use think\Response;

class AdminApi extends BasicMiddleware
{
    /**
     * 默认返回资源类型
     * @param Request $request
     * @param mixed $next
     * @return Response
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $controller     = get_controller();
        $action         = $request->action(true);
        $path           = $controller . '/' . $action;
        $not_auth_array = [
            'passport/login',
            'passport/bind_user',
            'passport/send_sms_code',
            'passport/reset_password',
            'business/get_token',
            'business/register',
            'common/area',
            'common/get_copyright',
            'system_config/get_base_config'
        ];
        if (in_array($path, $not_auth_array)) {
            return $next($request);
        }
        //如果不是获取access_token 则校验是否有access_token参数
        $access_token = $this->get_admin_access_token($request);
        if (!$access_token) {
            $msg = '登录超时,请您重新登陆~';
            error($msg, -2);
        }
        $token_name = 'business_user_token';

        //校验access_token的有效性,是否过期 是否有效等
        $jwt = TokenService::verify($access_token);
        $bid = $jwt['bid'];

        //检查是否有异地登陆
        $this->check_other_login($jwt);
        //检测商家有效性 比如 过期或者锁定
        $this->check_business_status($bid);

        $sid              = $jwt['sid'];
        $business_account = $jwt['business_account'] ?? '';
        $user_account     = $jwt['user_account'] ?? '';
        $user_guid        = $jwt['user_guid'];
        $user_id          = $jwt['user_id'];
        if ($controller != 'kefu') {
            $this->check_auth($bid, $user_guid, $controller, $action);
        }
        $request->__set('_bid', $bid);
        $request->__set('_sid', $sid);
        $request->__set('_user_guid', $user_guid);
        $request->__set('_user_id', $user_id);
        $request->__set('_business_account', $business_account);
        $request->__set('_user_account', $user_account);
        $request->__set('_jwt', $jwt);
        $request->__set($token_name, $access_token);

        !empty($jwt['appid']) && $request->__set('_appid', $jwt['appid']);
        !empty($jwt['openid']) && $request->__set('_openid', $jwt['openid']);
        /* @var $response Response */
        $response = $next($request);
        return $this->append_header_access_token($response, $access_token);
    }


    public function check_auth($bid, $user_guid, $controller, $action)
    {
        $path             = $controller . '/' . $action;
        $white_controller = ['dictionary', 'file', 'auction'];
        if (!in_array($controller, $white_controller)) {
            //通过账号工号获取角色
            $db_rule = new Rule();
            if ($db_rule->check_rule($bid, $user_guid, $path) === false) {
                $title = $db_rule->where([['name', '=', $path]])->value('title');
                $title = $title ?: $path;
                error('抱歉您没有【' . $title . '】权限!', -1, null, 'NO_AUTH');
            }
        }
    }

    public function check_business_status($bid)
    {
        $db_business = new Business();
        $result      = $db_business->login_verify($bid);
        if ($result === false && Visitor::is_readonly() === false) {
            error($db_business->getError(), -2);
        }
    }

    public function check_other_login($jwt)
    {
        $grant_type = $jwt['grant_type'] ?? null;
        $bid        = $jwt['bid'];
        $user_guid  = $jwt['user_guid'];
        if (!in_array($grant_type, ['client_credential']) && empty($jwt['appid']) && empty($jwt['openid'])) {
            //通过api获取的token 不判断长时间未操作 也不判断是否踢下线
            $jti          = $jwt['jti'];
            $ticket_key   = 'login_ticket:' . $bid . ':' . $user_guid;
            $cache_ticket = cache($ticket_key);
            if (!$cache_ticket) {
                $this->remove_admin_access_token_cookie();
                error('长时间未操作,请您重新登陆!', -2);
            }
            if ($cache_ticket !== $jti) {
                $db_business     = new Business();
                $is_examples_bid = $db_business->is_examples_bid($bid);
                if (!$is_examples_bid) {
                    $this->remove_admin_access_token_cookie();
                    error('您的账号在其他地方登陆,已经强制下线,请您重新登陆!', -2);
                }
            }
            //延长4小时ticket时间
            cache($ticket_key, $cache_ticket, 3600 * 4);
        }
    }
}