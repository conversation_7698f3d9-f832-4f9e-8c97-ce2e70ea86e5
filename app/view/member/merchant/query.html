<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>人脸进件结果查询</title>
  <link rel="stylesheet" href="//file.yikayi.net/static/js/plugins/layuiadmin/layui/css/layui.css?v=2.10.3">
  <style>
      h1 {
          font-size: 2rem;
          text-align: center;
          padding: 1rem;
      }

      footer {
          font-size: 12px;
          color: #999;
          text-align: center;
          position: fixed;
          bottom: 20px;
          width: 100%;
      }

      .main {
          width: 375px;
          margin: 0 auto;
          box-sizing: border-box;
      }
  </style>
</head>
<body>
<div class="main">
  <h1>进件查询</h1>
  <form class="layui-form" action="" style="padding-right:3rem">
    <div class="layui-form-item">
      <label class="layui-form-label">子商户号</label>
      <div class="layui-input-inline">
        <input type="tel" autocomplete="off" name="sub_mch_id" required lay-verify="required"
               placeholder="请输入1开头10位数子商户号"
               maxlength="10"
               autocomplete="off" lay-verType="tips"
               class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button class="layui-btn" type="button" function="query" lay-submit lay-filter="formDemo"><i
            class="layui-icon layui-icon-search"></i>查询
        </button>
        <button class="layui-btn layui-btn-normal" type="reset"><i class="layui-icon layui-icon-delete"></i>重置
        </button>
      </div>
    </div>
  </form>
  <div class="layui-card">
    <div class="layui-card-header">温馨提示</div>
    <div class="layui-card-body">
      <ul>
        <li>1、总部发起授权邀请后需要登陆微信商户平台同意授权,方可通过人脸支付;</li>
        <li>2、如有其他问题请联系业务经理或在线客服.</li>
      </ul>
    </div>
  </div>
</div>
<footer>
  <p>一卡易（股票代码：430671）旗下产品</p>
  <p>©Copyright&nbsp;2006-2022 1Card1.All Rights Reserved</p>
</footer>
<!-- 你的HTML代码 -->
<script src="//file.yikayi.net/static/js/jquery.min.js?v=2.2.4"></script>
<script src="//file.yikayi.net/static/js/plugins/layuiadmin/layui/layui.js?v=2.10.3"></script>
<script src="/static/js/function.js?v=20250521"></script>
<script>
  layui.use('form', function () {
    var form = layui.form;
    //监听提交
    form.on('submit(formDemo)', function (data) {
      var data = data.field;
      post_layui_member_api_v1('/merchant/query', data, function (data) {
        layer.alert(data.msg);
      })
    });
  });
</script>
</body>
</html>
