{layout name="layui" /}
<style>
  /* 新增：设置页面整体红色背景 */
  body {
    background-color: #fff;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .container {
    padding: 10px 10px 80px 10px;
  }

  .top-info {
    display: flex;
    align-items: center;
    /*margin-bottom: 30px;*/
    color: white;
    padding: 10px;
  }

  .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 20px;
  }

  .user-info {
    flex-grow: 1;
  }

  .rule-button {
    position: fixed;
    top: 20px;
    right: 0;
    z-index: 9999;
    background-color: rgba(128, 128, 128, 0.5);
    border-radius: 15px 0 0 15px;
    padding: 4px 12px 4px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .rule-button a {
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: block;
    white-space: nowrap;
  }

  .rule-button a:hover {
    color: #f0f0f0;
  }

  .top-buttons {
    display: flex;
    gap: 10px;
  }

  .top-button {
    background-color: #c62828;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    text-decoration: none;
  }

  .activity-prize,
  .activity-guide {
    background-color: white;
    padding: 10px;
    border-radius: 0 0 8px 8px;
    margin-bottom: 10px;
    margin-top: 0;
  }

  /* 当有外部标题图片时，移除上圆角 */
  .prize-header-outside + .activity-prize {
    border-radius: 0 0 8px 8px;
    /* 确保与上方图片完全贴合 */
    margin-top: -1px;
  }

  /* 外部标题图片样式 */
  .prize-header-outside {
    margin-bottom: 0;
    padding: 0;
    /* 消除容器内的空白间隙 */
    line-height: 0;
    font-size: 0;
  }

  .prize-header-outside img {
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
    width: 100%;
    /* 消除图片下方的间隙 */
    display: block;
    vertical-align: top;
    margin: 0;
    padding: 0;
  }

  .prize-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 0px;
  }

  .prize-header img {
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
  }

  .prize-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
  }

  .prize-img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
  }

  .prize-info {
    flex-grow: 1;
    margin-left: 10px;
  }

  .prize-title {
    font-size: 16px;
    font-weight: bold;
    line-height: initial;
  }

  .prize-desc {
    font-size: 14px;
    color: #666;
  }

  .prize-button {
    background-color: #ff9800;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    white-space: nowrap;
  }

  .margin-prize-button {
    margin-top: -20px;
    float: right;
  }

  .animation {
    animation: pulse 1.2s ease-in-out infinite;
  }

  .progress-container {
    margin: 10px 0;
  }

  .layui-progress-bar {
    background-color: #ff9800;
  }

  .activity-guide {
    padding: 20px;
  }

  .guide-items {
    display: flex;
    justify-content: space-around;
    gap: 20px;
  }

  .guide-item {
    text-align: center;
  }

  .guide-icon {
    font-size: 36px;
    color: #ff9800;
  }

  .guide-desc {
    font-size: 12px;
    color: #666;
  }

  /* 定义放大缩小动画 */
  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.06);
    }

    /* 放大6% */
    100% {
      transform: scale(1);
    }
  }

  /* 修改：活动区块背景改为白色 */
  .activity-prize,
  .activity-guide,
  .activity-info {
    background-color: #ffffff;
    /* 白色背景 */
    padding: 10px;
    /* 减少内边距 */
    border-radius: 8px;
    margin-bottom: 10px;
  }

  .layui-h3 {
    /* 修改：标题文字改为深红色 */
    color: #b71c1c;
  }

  .layui-progress-bar {
    /* 修改：进度条改为红色 */
    background-color: #ff5252;
  }

  .layui-tabs-header .layui-this,
  .layui-tabs-header li:hover {
    color: #ff5252;
  }

  .layui-tabs-header li {
    font-size: 16px;
    font-weight: bold;
  }

  .layui-tabs-header .layui-this:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    border-bottom: 3px solid #ff5252;
    box-sizing: border-box;
    pointer-events: none;
  }

  .layui-icon-fire {
    color: #ff5252;
  }

  .layui-text-center {
    text-align: center;
  }

  .icon {
    font-size: 36px;
    color: #f44336;
  }

  /* 底部提示层样式 */
  .bottom-tip {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    padding: 15px 20px;
    border-radius: 20px;
    text-align: center;
    z-index: 99999999;
    /* 模拟语音气泡效果，添加一个上方向的三角形 */
    position: absolute;
    width: 80%;
  }

  .bottom-tip::after {
    content: "";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
  }

  .bottom-tip p {
    font-size: 16px;
    line-height: 1.5;
  }

  /* 底部导航栏样式 */
  .bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: white;
    padding: 8px 0;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    z-index: 99999;
  }

  /* 图标按钮容器样式 */
  .icon-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  /* 左侧按钮组（我的和客服）*/
  .left-buttons {
    display: flex;
    justify-content: space-evenly;
    flex: 1;
    max-width: 50%;
    margin: 0 -20px;
  }

  /* 右侧按钮组（活动海报和立即购买）*/
  .right-buttons {
    display: flex;
    gap: 18px;
    flex-shrink: 0;
  }

  /* 单个图标按钮样式 */
  .icon-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
  }

  /* 图标样式（这里使用字体图标示例，你可以替换为实际的图标图片） */
  .bottom-icon {
    font-size: 12px;
    width: 24px;
    /*margin-bottom: 5px;*/
  }

  /* 文字样式 */
  .text {
    font-size: 12px;
  }

  /* 活动海报和立即购买按钮样式 */
  button {
    padding: 10px 30px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    cursor: pointer;
  }

  .activity-poster {
    background-color: #ff9800; /* 黄色 */
    color: white;
  }

  .buy-now {
    background-color: #f44336; /* 红色 */
    color: white;
  }

  .guide-item img {
    width: 30px;
    padding-bottom: 6px;
  }

  .description img {
    max-width: 100%;
  }

  /* CSS 中定义样式 */
  .progress-row {
    display: flex; /* 横向排列子元素 */
    align-items: center; /* 子元素垂直居中 */
  }

  /* 可选：给文字容器加间距 */
  .progress-row > div {
    margin-left: 5px;
  }

  .grey {
    background-color: #ccc !important;
  }

  .center_button {
    background-color: #f44336;
    width: 80%;
    text-align: center;
    display: inline-block;
    padding: 10px 0 10px 0;
  }

  /* 网格布局优化 */
  .reward-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  /* 奖励卡片样式优化 */
  .reward-card {
    background: #fff;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
  }

  /* 等级标签 */
  .level-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(244, 67, 54, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    z-index: 1;
  }

  /* 商品图片容器 */
  .reward-image {
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 10px;
  }

  .reward-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .reward-image img:hover {
    transform: scale(1.05);
  }

  /* 商品标题 */
  .reward-title {
    font-size: 15px;
    font-weight: bold;
    margin: 8px 0;
    line-height: 1.4;
    height: 42px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #333;
  }

  /* 统计信息 */
  .reward-stats {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
  }

  .stats-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #666;
  }

  .stats-item i {
    margin-right: 4px;
    font-size: 16px;
    color: #f44336;
  }

  /* 进度条状态文本样式 */
  .progress-wrapper {
    margin: 12px 0;
  }

  .progress-label {
    text-align: right;
    margin-top: 4px;
    font-size: 12px;
  }

  .success-text {
    color: #4caf50;
    font-weight: bold;
  }

  .pending-text {
    color: #ff9800;
  }

  /* 奖品备注 */
  .reward-remark {
    font-size: 12px;
    color: #ff9800;
    margin: 8px 0;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  /* 分享按钮优化 */
  .share-btn {
    width: 100%;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 0;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    margin-top: 12px;
    padding-left: 16px;
    padding-right: 16px;
  }

  .share-btn i {
    margin-right: 4px;
  }

  .share-btn .share-text {
    margin-left: auto;
  }

  .share-btn:active {
    transform: scale(0.98);
    background: #d32f2f;
  }

  .share-btn.grey {
    background: #ccc;
  }

  .share-btn.animation {
    animation: pulse 1.2s ease-in-out infinite;
  }

  @media screen and (max-width: 375px) {
    .reward-grid {
      grid-template-columns: 1fr;
    }
  }

  /* 添加授权弹窗样式 */
  .auth-popup {
    background: #fee7e8;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .auth-popup .icon-container {
    width: 80px;
    height: 80px;
    margin: 0 auto 16px;
    position: relative;
  }

  .auth-popup .bell {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: #ffd700;
    border-radius: 50% 50% 0 0;
    box-shadow: inset -10px -10px 20px rgba(0, 0, 0, 0.1);
  }

  .auth-popup .bow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 20px;
    background: #ff69b4;
    border-radius: 5px;
  }

  .auth-popup .bow:before,
  .auth-popup .bow:after {
    content: "";
    position: absolute;
    top: 50%;
    width: 15px;
    height: 15px;
    background: #ff69b4;
    border-radius: 50%;
  }

  .auth-popup .bow:before {
    left: -10px;
    transform: translateY(-50%) rotate(-45deg);
  }

  .auth-popup .bow:after {
    right: -10px;
    transform: translateY(-50%) rotate(45deg);
  }

  .auth-popup .title {
    font-family: "Arial", sans-serif;
    font-size: 24px;
    font-weight: 700;
    color: #333333;
    margin: 0 0 24px;
  }

  .auth-popup .content {
    background: #fff3f0;
    border: 1px solid #ffe1e1;
    border-radius: 12px;
    padding: 24px;
    margin: 0 0 32px;
    color: #666666;
    font-family: "Helvetica", sans-serif;
    font-size: 14px;
    line-height: 1.5;
  }

  .auth-popup .buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
  }

  .auth-popup .btn {
    min-width: 120px;
    padding: 12px 24px;
    border-radius: 24px;
    font-family: "Arial", sans-serif;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .auth-popup .btn-default {
    background: #ffffff;
    border: 2px solid #ff4d4f;
    color: #ff4d4f;
  }

  .auth-popup .btn-default:hover {
    background: #ff4d4f;
    color: #ffffff;
  }

  .auth-popup .btn-primary {
    background: #ff4d4f;
    border: none;
    color: #ffffff;
  }

  .auth-popup .btn-primary:hover {
    background: #e63946;
    transform: scale(1.05);
  }

  .auth-popup .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* 活动引导卡片样式 */
  .activity-guide-card {
    max-width: 500px;
    width: 90%;
    margin: 0 auto;
    background: #fff3e0;
    border-radius: 24px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
    padding: 32px 24px;
    box-sizing: border-box;
    position: relative;
  }

  .activity-guide__coin {
    width: 80px;
    height: 80px;
    margin: -40px auto -65px;
    position: relative;
    z-index: 10;
  }

  .activity-guide__coin-star {
    width: 80px;
    height: 80px;
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url("/static/img/star.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 10;
  }

  /* 更新数字和文字样式 */
  .activity-guide__steps {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    margin: 0 auto;
    max-width: 400px;
  }

  .activity-guide__step {
    text-align: center;
    position: relative;
    flex: 1;
  }

  .activity-guide__step-number {
    font-family: fantasy;
    font-size: 68px;
    font-weight: 800;
    margin-bottom: 4px;
    line-height: 1;
    background: linear-gradient(180deg, #ff6b6b 0%, rgba(255, 107, 107, 0.3) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    text-shadow: 0px 2px 4px rgba(255, 107, 107, 0.2);
  }

  /* 添加倒影效果 */
  .activity-guide__step-number::after {
    content: attr(data-number);
    position: absolute;
    left: 0;
    bottom: -20px;
    width: 100%;
    transform: scaleY(-0.3) translateY(-100%);
    background: linear-gradient(0deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 107, 107, 0) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    opacity: 0.5;
  }

  .activity-guide__step-text {
    font-family: "Arial", sans-serif;
    font-size: 14px;
    color: #8b4513;
    font-weight: 500;
    line-height: 1.3;
  }

  /* 添加红色边框 */
  .activity-guide__steps-container {
    /* border: 1px solid #ff6b6b; */
    border-radius: 12px;
    padding: 15px 10px;
    margin: 20px 0;
  }

  /* 更新内容区域样式 */
  .activity-guide__content {
    background: #ffffff;
    border-radius: 16px;
    padding: 24px 0 0 0;
    margin: 24px 0;
    min-height: 120px;
  }

  .activity-guide__content h3 {
    color: #ff4d4f;
    font-size: 18px;
    margin-bottom: 16px;
    text-align: center;
  }

  /* 更新按钮样式 */
  .activity-guide__buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
  }

  .activity-guide__btn {
    min-width: 120px;
    padding: 12px 24px;
    border-radius: 32px;
    font-family: "Arial", sans-serif;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .activity-guide__btn--outline {
    background: #ffffff;
    border: 2px solid #ffe0e0;
    color: #ff4d4f;
  }

  .activity-guide__btn--outline:hover {
    background: #ff4d4f;
    color: #ffffff;
  }

  .activity-guide__btn--fill {
    background: #ff4d4f;
    border: none;
    color: #ffffff;
  }

  .activity-guide__btn--fill:hover {
    background: #e63946;
    transform: scale(1.05);
  }

  .activity-guide__btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  @media screen and (max-width: 480px) {
    .activity-guide__steps {
      gap: 16px;
    }

    .activity-guide__step-number {
      width: 60px;
      height: 60px;
      line-height: 60px;
      font-size: 36px;
    }

    .activity-guide__step-text {
      font-size: 14px;
    }
  }

  /* 奖励提示弹窗样式 */
  .reward-tips-popup {
    position: relative;
    width: 100%;
    text-align: center;
    /* background: #fff3f0; */
    border-radius: 12px;
    overflow: hidden;
  }

  .reward-tips-image {
    width: 100%;
    display: block;
    border-radius: 12px 12px 0 0;
  }

  .reward-tips-notice {
    /* background: rgba(255, 255, 255, 0.95); */
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    width: 80%;
    max-width: 320px;
    min-width: 180px;
    height: auto;
    border-radius: 12px;
    display: flex;
    align-items: center;
    text-align: left;
    justify-content: center;
    flex-direction: column;
    padding-top: 10px;
  }

  .reward-tips-notice strong {
    color: #ff4d4f;
  }

  .reward-tips-buttons {
    padding: 0 24px 20px;
    display: flex;
    justify-content: center;
    gap: 16px;
    border-radius: 0 0 12px 12px;
    /* margin-top: 80px; */
    position: relative;
    top: -75px;
  }

  .reward-tips-btn {
    flex: 1;
    max-width: 160px;
    padding: 12px 18px;
    border-radius: 24px;
    font-family: "Arial", sans-serif;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .reward-tips-btn--outline {
    background: #ffffff;
    border: 1px solid #ff4d4f;
    color: #ff4d4f;
  }

  .reward-tips-btn--outline:hover {
    background: #ff4d4f;
    color: #ffffff;
  }

  .reward-tips-btn--fill {
    background: #ff4d4f;
    border: none;
    color: #ffffff;
  }

  .reward-tips-btn--fill:hover {
    background: #e63946;
    transform: scale(1.05);
  }

  /* 新版奖励卡片样式 */
  .reward-card-v2 {
    background: #ffffff;
    border-radius: 12px;
    padding: 4px !important;
    margin-bottom: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  }

  .reward-card-v2 .reward-image-container {
    width: 100%;
    position: relative;
    background: #f8f8f8;
    border-radius: 8px;
    overflow: hidden;
  }

  .reward-card-v2 .reward-image-container img {
    width: 100%;
    display: block;
  }

  .reward-card-v2 .reward-title {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    margin: 12px 0 8px;
    line-height: 1.4;
  }

  .reward-card-v2 .reward-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
  }

  .reward-card-v2 .reward-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 8px 0;
    background-color: #ff57221a;
    border-radius: 8px;
  }

  .reward-card-v2 .reward-price {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .reward-card-v2 .reward-stock {
    color: #999;
    font-size: 12px;
  }

  .reward-card-v2 .reward-progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #666;
    font-size: 13px;
  }

  /* 新版奖励卡片样式 */
  .reward-list-v2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1px;
    padding: 0 !important;
    margin: 0;
  }

  .reward-card-v2 {
    background: #ffffff;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  }

  .reward-card-v2 .reward-image-container {
    width: 100%;
    position: relative;
    background: #f8f8f8;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
  }

  .reward-card-v2 .reward-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .reward-card-v2 .reward-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin: 8px 0 4px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 2.6em;
    max-height: 2.6em;
  }

  .reward-card-v2 .reward-subtitle {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
  }

  .reward-card-v2 .reward-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 6px 0;
    /* gap: 12px; */
  }

  .reward-card-v2 .reward-info {
    flex: 1;
  }

  .reward-card-v2 .reward-info-row {
    display: flex;
    justify-content: flex-start;
    gap: 1px;
    color: #666;
    font-size: 12px;
    line-height: 1.2;
    padding-left: 10px;
  }

  .reward-card-v2 .reward-info-row:first-child {
    margin-bottom: 4px;
  }

  .reward-card-v2 .reward-value {
    color: #ff4d4f;
    font-size: 12px;
  }

  .reward-card-v2 .reward-share-btn {
    width: auto;
    height: 32px;
    line-height: 32px;
    background: #f44336;
    color: #fff;
    border: none;
    border-radius: 16px;
    font-size: 13px;
    text-align: center;
    flex-shrink: 0;
    padding: 0 5%;
  }

  .reward-card-v2 .reward-share-btn.grey {
    background: #ccc;
  }

  .reward-share-btn-v2 {
    height: 100% !important;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
  }

  @media screen and (max-width: 375px) {
    .reward-list-v2 {
      grid-template-columns: 1fr;
    }
  }

  .reward-stats {
    position: relative;
    min-height: 48px;
  }

  .reward-info {
    /* padding-right: 30%; */
    width: 100%;
  }

  /* 弹窗商品展示样式 - 左图右文布局 */
  .popup-reward-display {
    background: #fff;
    border-radius: 12px;
    padding: 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  .popup-reward-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
  }

  .popup-reward-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .popup-reward-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 80px;
    padding-left: 4px;
    text-align: left;
  }

  .popup-reward-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }

  .popup-reward-value {
    color: #ff4d4f;
    font-size: 16px;
    font-weight: bold;
    margin: 0;
  }

  .popup-reward-require_num {
    color: #ff9800;
    font-size: 16px;
    font-weight: bold;
    margin: 0;
  }

  .layui-layer-page .layui-layer-content {
    overflow: visible;
  }

  .reach-reward-popup .popup-reward-display {
    background: transparent;
    border-radius: 0;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .reach-reward-popup .popup-reward-image img {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }

  .reach-reward-popup .popup-reward-info {
    text-align: left;
  }

  .reach-reward-popup .popup-reward-title {
    font-size: 14px;
    margin: 0 0 2px 0;
    color: #333;
  }

  .reach-reward-popup .popup-reward-value {
    font-size: 16px;
    font-weight: bold;
    color: #ff4d4f;
    margin: 0;
  }

  .reach-reward-popup .reward-tips-buttons {
    position: absolute;
    bottom: 12%;
    left: 50%;
    transform: translateX(-50%);
    width: 85%;
    display: flex;
    gap: 8px;
  }

  .reach-reward-popup .reward-tips-btn {
    flex: 1;
    height: 36px;
    border-radius: 18px;
    border: none;
    font-size: 13px;
    cursor: pointer;
  }

  .reach-reward-popup .reward-tips-btn--outline {
    background: transparent;
    border: 1px solid #ff4d4f;
    color: #ff4d4f;
  }

  .reach-reward-popup .reward-tips-btn--fill {
    background: #ff4d4f;
    color: white;
  }

  /* 紧凑按钮样式，减少左右间距，防止与客服按钮挤在一起 */
  .compact-btn {
    padding-left: 16px !important;
    padding-right: 16px !important;
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    white-space: nowrap;
  }

  .body_top_background_image_url {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .notice_rule {
    text-align: left;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    max-height: 140px;
    overflow-y: scroll;
    padding: 2px;
    /* 确保滚动条始终显示 */
    scrollbar-width: thin;
    scrollbar-color: #ccc #f1f1f1;
  }

  /* WebKit浏览器滚动条样式 */
  .notice_rule::-webkit-scrollbar {
    width: 6px;
  }

  .notice_rule::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .notice_rule::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  .notice_rule::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
</style>
<div id="body"></div>
{literal}
<script id="body_tpl" type="text/html">
  {{if activity_detail.rule}}
  <div class="rule-button">
    <a onclick="showActivityRulePopup()" href="#">规则</a>
  </div>
  {{/if}} {{if activity_detail.body_top_background_image_url}}
  <img class="body_top_background_image_url" src="{{activity_detail.body_top_background_image_url}}"/>
  {{/if}}
  <div class="container">
    <!-- 顶部信息栏 -->
    <div class="top-info" style="display: none">
      {{if is_member && member_info.ImagePath }}
      <a href="{{member_card_url}}"><img src="{{member_info.ImagePath}}" alt="用户头像" class="avatar"/></a>
      {{else}}
      <a href="{{member_card_url}}"><img src="{{logo}}" alt="用户头像" class="avatar"/></a>
      {{/if}}

      <div class="user-info">
        {{if is_member}}
        <a href="{{member_card_url}}">
          <p style="color: #FFF">{{member_info.TrueName}} | {{member_info.CardId}}</p>
        </a>
        {{/if}} {{if rank}}
        <p>排名 {{rank}}</p>
        {{/if}}
      </div>
      <div class="top-buttons">
        {{if activity_detail.rule}}
        <a onclick="showActivityRulePopup()" href="#" class="top-button">规则</a>
        {{/if}}

        <a onclick="showComplaintPopup()" id="complain" href="#" class="top-button">投诉</a>
      </div>
    </div>
    {{if activity_detail.join_reward_yky_coupon_guid}}
    <!-- 参与活动奖品 -->
    <!-- 参与活动奖品标题图片 -->
    <div class="prize-header-outside">
      <img src="/static/img/tips_1.png?v=3" alt="参与活动奖品"
           style="display: block; width: 100%; height: auto; border-radius: 8px 8px 0 0;"/>
    </div>
    <div class="activity-prize">
      <div class="prize-content">
        <img src="{{activity_detail.join_award_image_url}}" alt="" class="prize-img"/>
        <div class="prize-info">
          <p class="prize-title">{{activity_detail.join_award_name}}</p>
          <div>
            <p class="prize-desc">剩余数量 {{activity_detail.join_award_stock}}</p>
            {{if join_reward_status}}
            <a onclick="showPromoterPopup()" href="#" class="prize-button margin-prize-button">{{activity_detail.join_after_button_text
              ? activity_detail.join_after_button_text : '已领取'}}</a>
            {{/if}} {{if !join_reward_status}} {{if activity_detail.status ==1 && activity_detail.join_award_stock > 0}}
            <a onclick="showImage()" href="#" class="prize-button animation margin-prize-button">{{activity_detail.join_before_button_text
              ? activity_detail.join_before_button_text : '免费领取'}}</a>
            {{else}}
            <a href="#" class="prize-button margin-prize-button grey">活动结束</a>
            {{/if}} {{/if}}
          </div>

          <p class="prize-desc" style="color: #FF9800">{{activity_detail.join_award_remark}}</p>
        </div>
      </div>
    </div>
    {{/if}}
    <!-- 达标奖励明细区 -->
    {{if reach_reward_item && reach_reward_item.length > 0}}
    <div class="activity-prize" style="display: none;">
      <div class="prize-header">
        <i class="layui-icon layui-icon-fire"></i>
        <h3 class="prize-title">达标奖励明细</h3>
        <i class="layui-icon layui-icon-fire"></i>
      </div>

      <!-- 奖励等级列表 - 使用网格布局 -->
      <div class="reward-grid">
        {{each reach_reward_item item}}
        <div class="reward-card">
          <!-- 等级标签 -->
          <!-- <div class="level-tag">Level {{item.level}}</div> -->

          <!-- 商品图片 -->
          <div class="reward-image">
            <img src="{{item.award_image_url}}" alt="{{item.award_name}}"/>
          </div>

          <!-- 商品信息 -->
          <div class="reward-info">
            <h4 class="reward-title">{{item.award_name}}</h4>
            <!-- 奖品备注 -->
            {{if item.award_remark}}
            <p class="reward-remark">{{item.award_remark}}</p>
            {{/if}}
            <!-- 达标要求和库存信息 -->
            <div class="reward-stats">
              <div class="stats-item">
                <i class="layui-icon layui-icon-cart"></i>
                <span>剩余{{item.award_stock}}</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-wrapper">
              <div class="layui-progress">
                <div class="layui-progress-bar layui-bg-red"
                     lay-percent="{{share_num ? (share_num >= item.require_num ? '100' : (share_num/item.require_num*100)) : '0'}}%">
                  <!-- <span class="layui-progress-text"
                    >{{share_num || 0}}/{{item.require_num}}</span
                  > -->
                </div>
              </div>

              <div class="stats-item" style="display: flex;float: left;">
                <i class="layui-icon layui-icon-user"></i>
                <span>需{{item.require_num}}人</span>
              </div>
              <div class="progress-label">
                {{if share_num >= item.require_num}}
                <span class="success-text">已达标</span>
                {{else}}
                <span class="pending-text">还差{{item.require_num - (share_num || 0)}}人</span>
                {{/if}}
              </div>
            </div>

            <!-- 分享按钮 -->
            {{if activity_detail.status == 1 && item.award_stock > 0}} {{if item.reward_status > 0}}
            <button class="share-btn">已领取</button>
            {{else}}
            <button class="share-btn animation" onclick="showImage()">
              <span>还差{{item.require_num - (share_num || 0)}}人</span>
              <span class="share-text">去分享</span>
            </button>
            {{/if}} {{else}}
            <button class="share-btn grey">{{item.award_stock <= 0 ? '已抢光' : '活动结束'}}</button>
            {{/if}}
          </div>
        </div>
        {{/each}}
      </div>
    </div>
    {{/if}}

    <!-- 在原有的达标奖励明细区后添加新版本 -->
    {{if reach_reward_item && reach_reward_item.length > 0}}
    <!-- 达标奖励明细标题图片 -->
    <div class="prize-header-outside">
      <img src="/static/img/tips_2.png?v=3" alt="达标奖励明细"
           style="display: block; width: 100%; height: auto; border-radius: 8px 8px 0 0;"/>
    </div>
    <div class="activity-prize">
      <!-- 新版奖励列表 -->
      <div class="reward-list-v2">
        {{each reach_reward_item item}}
        <div class="reward-card-v2">
          <div class="reward-image-container">
            <img src="{{item.award_image_url}}" alt="{{item.award_name}}"/>
          </div>
          <h4 class="reward-title">{{item.award_name}}</h4>
          <p class="reward-subtitle">{{item.award_remark}}</p>
          <!-- 进度条 -->
          <div class="progress-wrapper">
            <div class="layui-progress">
              <div class="layui-progress-bar layui-bg-red"
                   lay-percent="{{share_num ? (share_num >= item.require_num ? '100' : (share_num/item.require_num*100)) : '0'}}%">
                <!-- <span class="layui-progress-text"
        >{{share_num || 0}}/{{item.require_num}}</span
      > -->
              </div>
            </div>
          </div>
          <div class="reward-stats">
            <div class="reward-info" onclick="showShareTips('{{item.guid}}')">
              <div class="reward-info-row">
                <span style="color: red;">需{{item.require_num}}人 差{{item.require_num - (share_num || 0)}}人</span>
              </div>

              <div class="reward-info-row">
                <span style="color: blue;" class="reward-value">值{{item.award_cost}}元 剩{{item.award_stock}}份</span>
              </div>
            </div>

            {{if activity_detail.status == 1 && item.award_stock > 0}} {{if item.reward_status > 0}}

            <div class="reward-share-btn reward-share-btn-v2" style="background: #999">已领取</div>
            {{else}}
            <div class="reward-share-btn reward-share-btn-v2" onclick="showImage()">去分享</div>
            {{/if}} {{else}}
            <div class="reward-share-btn grey reward-share-btn-v2">{{item.award_stock <= 0 ? '已抢光' : '活动结束'}}
            </div>
            {{/if}}
          </div>
        </div>
        {{/each}}
      </div>
    </div>
    {{/if}}

    <!-- 活动指南 -->
    <div class="activity-guide">
      <div class="prize-header">
        <i class="layui-icon layui-icon-fire"></i>
        <h3 class="prize-title">活动指南</h3>
        <i class="layui-icon layui-icon-fire"></i>
      </div>
      <div class="guide-items">
        <div class="guide-item">
          <!--                    <i class="layui-icon layui-icon-link icon"></i>-->
          <img src="/static/img/guanzhu.png?v=2"/>
          <p class="guide-desc">
            关注官微
            <br/>
            领卡参与
          </p>
        </div>
        <div class="guide-item">
          <!--                    <i class="layui-icon layui-icon-picture-fine icon"></i>-->
          <img src="/static/img/fenxiang.png?v=2"/>
          <p class="guide-desc">
            生成海报
            <br/>
            邀请好友
          </p>
        </div>
        <div class="guide-item">
          <!--                    <i class="layui-icon layui-icon-ok-circle icon"></i>-->
          <img src="/static/img/faquan.png?v=2"/>
          <p class="guide-desc">
            完成任务
            <br/>
            自动派奖
          </p>
        </div>
      </div>
    </div>

    <!-- 活动信息区 -->
    <div class="activity-info" style="padding: 0">
      <div class="layui-tabs-body" style="padding: 6px 16px 16px 16px;">
        <div class="layui-tabs-item layui-show">
          <div class="layui-tabs" lay-options="{headerMode:'normal'}">
            <ul class="layui-tabs-header" style="text-align: center;">
              <li class="layui-this">活动信息</li>
              <li>我的好友</li>
              <li>人气排行榜</li>
            </ul>
            <div class="layui-tabs-body">
              <div class="layui-tabs-item layui-show description">{{@activity_detail.description}}</div>

              <div class="layui-tabs-item">
                {{if share_note && share_note.length}}
                <div class="layui-text" style="padding:10px;">
                  <div class="layui-row layui-col-space10"
                       style="margin-bottom:8px;border-bottom:1px solid #eee;padding:8px 0;">
                    <div class="layui-col-xs8">
                      <p style="color:#666;font-size:14px;">分享者</p>
                    </div>
                    <div class="layui-col-xs4">
                      <p style="color:#999;font-size:12px;text-align:right">分享时间</p>
                    </div>
                  </div>
                  {{each share_note value index}}
                  <div class="layui-row layui-col-space10"
                       style="margin-bottom:8px;border-bottom:1px solid #eee;padding:8px 0;">
                    <div class="layui-col-xs8">
                      <p style="color:#666;font-size:14px;">{{value.openid}}</p>
                    </div>
                    <div class="layui-col-xs4">
                      <p style="color:#999;font-size:12px;text-align:right">{{value.share_time}}</p>
                    </div>
                  </div>
                  {{/each}}
                </div>
                {{else}}
                <div class="layui-text-center" style="padding:20px;color:#999;">暂无好友分享记录</div>
                {{/if}}
              </div>

              <div class="layui-tabs-item">
                {{if rank_list && rank_list.length}}
                <div class="layui-text" style="padding:10px;">
                  <div class="layui-row layui-col-space10"
                       style="margin-bottom:8px;border-bottom:1px solid #eee;padding:8px 0;">
                    <div class="layui-col-xs2">
                      <p style="color:#666;font-size:14px;">排名</p>
                    </div>
                    <div class="layui-col-xs6">
                      <p style="color:#666;font-size:14px;">分享者</p>
                    </div>
                    <div class="layui-col-xs4">
                      <p style="color:#999;font-size:12px;text-align:right">人数</p>
                    </div>
                  </div>
                  {{each rank_list value index}}
                  <div class="layui-row layui-col-space10"
                       style="margin-bottom:8px;border-bottom:1px solid #eee;padding:8px 0;">
                    <div class="layui-col-xs2">
                      <p style="color:#666;font-size:14px;">{{index+1}}</p>
                    </div>
                    <div class="layui-col-xs6">
                      <p style="color:#666;font-size:14px;">{{value.share_openid}}</p>
                    </div>
                    <div class="layui-col-xs4">
                      <p style="color:#999;font-size:12px;text-align:right">{{value.share_num}}</p>
                    </div>
                  </div>
                  {{/each}}
                </div>
                {{else}}
                <div class="layui-text-center" style="padding:20px;color:#999;">暂无好友分享记录</div>
                {{/if}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bottom-tip" style="display: none">
      <p>
        长按海报并保存到手机，分享到朋友圈/群
        <br/>
        再邀请
        <span style="color:#c62828;">{{activity_detail.reach_reward_require_num - share_num}}</span>
        位好友即可领取奖品
      </p>
    </div>
  </div>
  <!-- 底部导航栏 -->
  <div class="bottom-nav">
    <div class="icon-container">
      <!-- 左侧按钮组：我的和客服 -->
      <div class="left-buttons">
        <!-- 我的图标按钮 -->
        <a href="{{is_member ? member_card_url : register_url}}" class="icon-button">
          <!--                <span class="bottom-icon">&#x1f464;</span> &lt;!&ndash; 这里使用Unicode字符表示图标，你可以替换为实际图标 &ndash;&gt;-->
          <img class="bottom-icon" src="/static/img/member_center.png"/>
          <span class="text">我的</span>
        </a>

        <!-- 客服图标按钮 -->
        <a onclick="showComplaintPopup()" href="#" class="icon-button">
          <!--                <span class="bottom-icon">&#x1f4e4;</span> &lt;!&ndash; 这里使用Unicode字符表示图标，你可以替换为实际图标 &ndash;&gt;-->
          <img class="bottom-icon" src="/static/img/kefu.png"/>
          <span class="text">客服</span>
        </a>
      </div>

      <!-- 右侧按钮组：活动海报和立即购买 -->
      <div class="right-buttons">
        {{if activity_detail.status ==1 && activity_detail.join_award_stock > 0}}
        <!-- 活动海报按钮 -->
        <button onclick="showImage()" class="activity-poster compact-btn">活动海报</button>
        <!-- 立即购买按钮 -->
        {{if join_reward_status}}
        <button onclick="showImage()" class="buy-now animation compact-btn">分享领更多奖品</button>
        {{/if}} {{if !join_reward_status}}
        <button onclick="showImage()" class="buy-now animation compact-btn">{{activity_detail.buy_button_text ?
          activity_detail.buy_button_text : '立即购买'}}
        </button>
        {{/if}} {{else}}
        <!-- 活动海报按钮 -->
        <!--            <button class="activity-poste grey">活动海报</button>-->
        <!-- 立即购买按钮 -->
        <button class="buy-now grey compact-btn">活动结束</button>
        {{/if}}
      </div>
    </div>
  </div>
</script>
{/literal}
<style>
  .kefu_div {
    background: url("/static/img/kefu_bac.png") no-repeat center center;
    width: 100%; /* 确保有宽度 */
    height: 100px; /* 确保有高度 */
    background-size: cover; /* 确保图片覆盖整个背景 */
  }
</style>
<script>
  const currentUrl = window.location.href;
  const guid = getQueryString("guid");
  const bid = getQueryString("bid");
  const share_openid = getQueryString("share_openid");
  const source = getQueryString("source");
  const expire = 3600 * 24;
  var data = {};
  if (!source) {
    wsCache.set(bid + ":" + guid + ":we_com_friend_status", 0, { exp: expire });
  }

  var we_com_live_code_url = "";
  var we_com_friend_status = wsCache.get(bid + ":" + guid + ":we_com_friend_status");
  var openid = wsCache.get(bid + ":openid");
  wsCache.set(bid + ":share_activity_guid", guid, { exp: expire });
  wsCache.set(bid + ":" + guid + ":share_openid", share_openid, { exp: expire });

  post_layui_member_api_v1(
    "/share_activity/share_activity_detail",
    {
      guid: guid,
      share_openid: share_openid,
    },
    function (result) {
      console.log(result);
      data = result.data;
      document.title = result.data.activity_detail.title;
      we_com_live_code_url = result.data.activity_detail.we_com_live_code_url;
      render_template_beta(result.data, "body");
      if (data.activity_detail.body_background_color) {
        $("body").css("background-color", data.activity_detail.body_background_color);
      }
      layui.tabs.render();
      layui.element.render("progress");
      if (source == 3) {
        showImage();
      } else if (source == 4) {
        // 替换原来的alert为自定义弹窗
        showPromoterPopup();
        return false;
      }
    }
  );

  function showPromoterPopup() {
    layui.layer.open({
      type: 1,
      title: false,
      closeBtn: 1,
      area: ["90%", "auto"],
      maxWidth: "500px",
      skin: "layui-layer-nobg",
      shadeClose: true,
      shade: 0.8, // 遮罩透明度
      offset: "20%", // 从顶部偏移20%，实现垂直居中
      success: disableBodyScroll,
      end: enableBodyScroll,
      content:
        `
      <div class="reward-tips-popup">
        <img src="/static/img/join_reward_tips.png?v=2" alt="奖励提示" class="reward-tips-image">
        <div class="reward-tips-notice">
          ` +
        data.activity_detail.join_award_tips +
        `
        </div>
        <div class="reward-tips-buttons">
          <button class="reward-tips-btn reward-tips-btn--outline" onclick="window.location.href='` +
        data.member_card_url +
        `'">去会员中心</button>
          <button class="reward-tips-btn reward-tips-btn--fill" onclick="showImage()">立即分享</button>
        </div>
      </div>
    `,
    });
  }

  function showShareTips(rewardGuid = null) {
    // 根据GUID查找对应的奖品信息
    let rewardItem = null;
    if (rewardGuid && data.reach_reward_item) {
      rewardItem = data.reach_reward_item.find((item) => item.guid === rewardGuid);
    }

    // 奖品信息内容
    let rewardContent = `
        <div class="popup-reward-display">
          <div class="popup-reward-image">
            <img src="${rewardItem.award_image_url}" alt="${rewardItem.award_name}" />
          </div>
          <div class="popup-reward-info">
            <h4 class="popup-reward-title">${rewardItem.award_name}</h4>
            <div class="popup-reward-value">¥${rewardItem.award_cost} </div>
            <div class="popup-reward-require_num">邀请满${rewardItem.require_num}人免费领取 </div>
           </div>
        </div>
      `;

    layui.layer.open({
      type: 1,
      title: false,
      closeBtn: 1,
      area: ["90%", "auto"],
      maxWidth: "400px",
      skin: "layui-layer-nobg",
      shadeClose: true,
      shade: 0.8, // 遮罩透明度
      offset: "20%",
      success: disableBodyScroll,
      end: enableBodyScroll,
      content: `
      <div class="reward-tips-popup">
        <img src="/static/img/reach_reward_tips.png?v=2" alt="奖励提示" class="reward-tips-image">
        <div class="reward-tips-notice" style="align-items: baseline;">
          ${rewardContent}
        </div>
        <div class="reward-tips-buttons">
          <button class="reward-tips-btn reward-tips-btn--outline" onclick="layui.layer.closeAll()">差${rewardItem.require_num - (data.share_num || 0)}人,剩${rewardItem.award_stock}份</button>
          <button class="reward-tips-btn reward-tips-btn--fill" onclick="showImage()">立即分享</button>
        </div>
      </div>
    `,
    });
  }

  // showImage 函数保持不变
  function showImage() {
    if (!is_weixin()) {
      layui.layer.alert("请在微信中访问");
      return false;
    }
    if (!we_com_friend_status && !data.join_reward_status) {
      // alert('请先加企微好友,即将跳转到 ')
      layui.layer.msg("正在跳转中...", {
        icon: 16,
        shade: [0.5, "#000"],
      });
      return redirect(we_com_live_code_url);
    }
    post_layui_member_api_v1(
      "/share_activity/get_share_image",
      {
        share_activity_guid: guid,
        openid: openid,
      },
      function (result) {
        console.log(result);
        let share_image_url = result.data.share_image_url;
        layui.layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ["90%", "80%"],
          offset: "30px",
          skin: "layui-layer-nobg",
          shadeClose: true,
          shade: 0.8, // 遮罩透明度
          content:
            '<div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;"><img src="' +
            share_image_url +
            '" style="max-width: 100%; max-height: 100%; object-fit: contain;">' +
            "</div>",
          success: disableBodyScrollAndShowBottomTip,
          end: enableBodyScrollAndHideBottomTip,
        });
      }
    );
  }

  function disableBodyScrollAndShowBottomTip() {
    disableBodyScroll();
    $(".bottom-tip").show();
  }

  function enableBodyScrollAndHideBottomTip() {
    disableBodyScroll();
    $(".bottom-tip").hide();
  }

  // disableBodyScroll 和 enableBodyScroll 函数保持不变
  function disableBodyScroll() {
    const scrollPosition = {
      x: window.scrollX,
      y: window.scrollY,
    };
    document.body.style.overflow = "hidden";
    document.body.style.position = "fixed";
    document.body.style.width = "100%";
    document.body.style.height = "100%";
    document.body.style.top = `-${scrollPosition.y}px`;
    document.body.style.left = `-${scrollPosition.x}px`;
    window.scrollPosition = scrollPosition;
  }

  function enableBodyScroll() {
    document.body.style.overflow = "";
    document.body.style.position = "";
    document.body.style.width = "";
    document.body.style.height = "";
    document.body.style.top = "";
    document.body.style.left = "";
    if (window.scrollPosition) {
      window.scrollTo(window.scrollPosition.x, window.scrollPosition.y);
      delete window.scrollPosition;
    }
  }

  //
  // loadScript('/static/js/eruda.min.js?v=2.4.1', function () {
  //     eruda.init();
  // });

  function showActivityRulePopup() {
    layui.layer.open({
      type: 1,
      title: false,
      closeBtn: 1,
      area: ["90%", "auto"],
      maxWidth: "500px",
      skin: "layui-layer-nobg",
      shadeClose: true,
      shade: 0.8, // 遮罩透明度
      offset: "20%", // 从顶部偏移20%，实现垂直居中
      success: disableBodyScroll,
      end: enableBodyScroll,
      content: `
        <div class="reward-tips-popup">
          <img src="/static/img/notice_tips.png" alt="活动规则" class="reward-tips-image">
          <div class="reward-tips-notice">
            <div class="notice_rule">
              ${data.activity_detail.rule}
            </div>
          </div>
          <div class="reward-tips-buttons">
            <button class="reward-tips-btn reward-tips-btn--fill" onclick="layui.layer.closeAll()">我知道了</button>
          </div>
        </div>
      `,
    });
  }

  function showNoticePopup() {
    post_layui_member_api_v1(
      "/share_activity/get_config",
      {
        share_activity_guid: guid,
        openid: openid,
      },
      function (result) {
        const popupContent =
          `

                  <div style=" padding: 10px; border-radius: 10px 10px 0 0;">
                  <h3 style="text-align: center">活动须知</h3>
           ` +
          result.data.agreement_html +
          `
                  </div>
         `;
        // 在此处输入 layer 的任意代码
        layer.open({
          type: 1, // page 层类型
          maxmin: false,
          area: ["80%", "60%"],
          title: false,
          shade: 0.8, // 遮罩透明度
          shadeClose: true, // 点击遮罩区域，关闭弹层
          anim: 0, // 0-6 的动画形式，-1 不开启
          content: popupContent,
        });
      }
    );
  }

  function showComplaintPopup() {
    post_layui_member_api_v1(
      "/share_activity/get_config",
      {
        share_activity_guid: guid,
        openid: openid,
      },
      function (result) {
        const popupContent =
          `
                 <div class="kefu_div" style="text-align: center;  background-color: #F4CB62;  border-radius: 10px 10px 0 0;">
                    <h2 style="margin: 0;padding-top: 30px">客服</h2>
                </div>
                <div style="padding: 20px;text-align: center">
                    <p style="text-align: center;">扫描下方二维码，立即联系客服</p>
                    <img src="` +
          result.data.service_qrcode_url +
          `" style="display: block; width: 200px; height: 200px; margin: 20px auto; border-radius: 5px;" />
                    <div style="display: inline-flex; justify-content: space-between; margin-top: 20px;">
                        <div>
                        <img style="width: 20px;margin-top: -4px" src='/static/img/phone-yellow.png'>
                            <i class="layui-icon layui-icon-phone"></i>
                             联系电话: ` +
          result.data.service_phone +
          `
                        </div>
                      <a href="tel:` +
          result.data.service_phone +
          `"   style="margin-left:10px;text-align: center; background-color: #fae065;color: #653D00;border: none;border-radius: 10px;width: 60px;height: 24px;">拨打</a>   </div>
                    <button onclick="closeComplaintPopup()" style="display: block; width: 100%; background-color: #f44336; color: white; padding: 8px 16px; border: none; border-radius: 20px; margin-top: 20px;">关闭</button>
                </div>
         `;

        // 在此处输入 layer 的任意代码
        layer.open({
          type: 1, // page 层类型
          closeBtn: 0,
          maxmin: false,
          area: ["80%", "auto"],
          title: false,
          shade: 0.8, // 遮罩透明度
          shadeClose: true, // 点击遮罩区域，关闭弹层
          anim: 0, // 0-6 的动画形式，-1 不开启
          content: popupContent,
          success: disableBodyScroll,
          end: enableBodyScroll,
        });
      }
    );
  }

  function closeComplaintPopup() {
    $("#complain_div").hide();
    layui.layer.closeAll();
    s;
  }

  // 添加成为推客的处理函数
  function becomePromoter() {
    // TODO: 处理成为推客的逻辑
    layui.layer.closeAll();
    // 这里添加您的推客授权处理代码
  }
</script>
