{layout name="layui"/}
<!-- 引入券卡详情页面专用样式文件 -->
<link href="/static/css/member/code/code_note_detail.css?v=1" rel="stylesheet" type="text/css"/>

<!-- 引入Vue.js框架和Swiper轮播图组件 -->

<link rel="stylesheet" href="/static/css/member/code/swiper-bundle.min.css?v=11.1.14"/>
<script src="/static/js/member/code/swiper-bundle.min.js?v=11.1.14"></script>

<!-- Vue应用主容器 -->
<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-state">
    <div class="loading-content">
      <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      <h4>加载中...</h4>
    </div>
  </div>

  <!-- 页面主容器 -->
  <div v-else class="page-container">
    <!-- 券卡主要信息卡片 -->
    <div class="card-container" v-if="couponInfo">
      <!-- 轮播图区域 -->
      <div class="swiper swiper-zhutu">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img :src="couponInfo.pic" :alt="couponInfo.name"/>
          </div>
        </div>
        <div class="swiper-pagination"></div>
      </div>

      <!-- 券卡信息区域 -->
      <div class="coupon-info">
        <h2 class="coupon-title">{{ couponInfo.name }}</h2>

        <!-- 券号显示 -->
        <div v-if="type === ''" class="coupon-code">
          <span class="label">券号</span>
          <span class="code">{{ code }}</span>
        </div>
      </div>
    </div>

    <!-- 券卡详细信息卡片 -->
    <div class="card-container">
      <div class="coupon-info">
        <div class="info-grid">
          <!-- 有效期信息 -->
          <div class="info-item">
            <div class="info-left">
              <div class="info-icon">
                <i class="layui-icon layui-icon-date"></i>
              </div>
              <div class="info-label">有效期</div>
            </div>
            <div class="info-value">{{ availabilityTime }}至{{ expireTime }}</div>
          </div>

          <!-- 可用次数信息 -->
          <div class="info-item">
            <div class="info-left">
              <div class="info-icon">
                <i class="layui-icon layui-icon-rate"></i>
              </div>
              <div class="info-label">可用次数</div>
            </div>
            <div class="info-value">
              <span v-if="type === ''">{{ availableNum }} 次</span>
              <span v-if="type === 'share'">1 次</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 券卡描述信息卡片 -->
    <div v-if="couponInfo && couponInfo.description" class="card-container">
      <div class="description-card">
        <div class="description-title">
          <i class="layui-icon layui-icon-tips"></i>
          使用说明
        </div>
        <div class="description-content" v-html="couponInfo.description"></div>
      </div>
    </div>
  </div>
  <!-- 结束 page-container -->

  <!-- 底部操作栏 -->
  <div v-if="!loading" class="bottom-actions">
    <!-- 卡券列表按钮 -->
    <button v-if="type !== 'share' || availableNum === 0" class="action-btn btn-success" @click="gotoCodeList">
      <i class="layui-icon layui-icon-list"></i>
      卡券列表
    </button>

    <!-- 立即转赠按钮 -->
    <button v-if="type === '' && availableNum > 0" class="action-btn btn-warning" @click="shareCode">
      <i class="layui-icon layui-icon-share"></i>
      立即转赠
    </button>

    <!-- 立即领取按钮 -->
    <button v-if="type === 'share' && availableNum > 0" class="action-btn btn-warning" @click="acceptCode">
      <i class="layui-icon layui-icon-add-1"></i>
      立即领取
    </button>
  </div>
</div>
<!-- 结束 Vue app -->

{include file="code/footer" /}

<!-- 引入券卡详情页面专用JavaScript文件 -->
<script src="/static/js/member/code/code_note_detail.js?v=1"></script>
