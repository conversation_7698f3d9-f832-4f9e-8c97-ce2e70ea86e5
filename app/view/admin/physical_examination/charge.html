{layout name="layui_plus" /}
<style>
  /*!* 设置表格行的高度为自动 *!*/
  /*table tr td {*/
  /*    height: auto !important;*/
  /*}*/

  /*!* 设置表格行的最小高度，确保即使内容为空，也有一定的行高 *!*/
  /*table tr td {*/
  /*    min-height: 50px; !* 根据需要调整这个值 *!*/
  /*}*/

  /*!* 确保单元格内的段落能够正确显示 *!*/
  /*table tr td p {*/
  /*    margin: 0;*/
  /*    padding: 5px 0; !* 添加一些内边距，让内容看起来不会太紧凑 *!*/
  /*}*/

  .function_btn .layui-btn + .layui-btn {
    margin-left: 0;
  }

  .edit_btn {
    margin-bottom: 10px;
    margin-right: 10px;
  }

  .form-search .layui-form-label {
    width: auto !important;
    padding: 0 16px;
    height: 64px;
    line-height: 64px;
    font-size: 24px;
  }

  .form-search .layui-input-inline {
    width: 300px;
  }

  .form-search .layui-input-inline input,
  .form-search .layui-input-inline select {
    width: 100%;
    height: 64px;
    padding: 4px 16px;
    line-height: 1em;
    font-size: 24px;
  }

  .form-search .layui-btn {
    height: 64px;
    line-height: 56px;
    font-size: 24px;
    padding: 0 20px;
  }
</style>
<div class="layui-fluid min-width-1120">
  <div class="layui-card">
    <div class="layui-card-header" style="position: relative">
      <span>体检收费</span>
      <!--      <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%)">-->
      <!--        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="switchToNewVersion()">-->
      <!--          <i class="layui-icon layui-icon-refresh"></i>-->
      <!--          切换新版-->
      <!--        </button>-->
      <!--      </div>-->
    </div>
    <div class="layui-card-body">
      <form id="query_form" class="layui-form layui-form-pane form-search" autocomplete="off"
            style="text-align: center">
        <div class="layui-form-item layui-inline">
          <label class="layui-form-label">卡号</label>
          <div class="layui-input-inline">
            <input id="code" name="code" value="" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入卡号" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item layui-inline">
          <button id="query" class="layui-btn layui-btn-primary" type="button" style="cursor: pointer">
            <i class="layui-icon layui-icon-search"></i>
            查 询
          </button>
        </div>
      </form>
      <!--            <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid" isLoad="0"-->
      <!--                   url="/code/get_code_info" requestSuccessCallback="show_info" isPage="0"-->
      <!--                   defaultForm="query_form" height="auto">-->
      <!--            </table>-->
      <script>
        $("input[name='code']")[0].focus();
      </script>
    </div>
  </div>
  <div id="info">
    <div class="layui-fluid">
      <div class="layui-row layui-col-space15">
        <div class="layui-col-md3" style="display: none">
          <div class="layui-card">
            <div class="layui-card-header">会员信息</div>
            <div class="layui-card-body layui-text">
              <table class="layui-table">
                <colgroup>
                  <col width="150"/>
                  <col/>
                </colgroup>
                <tbody>
                <tr>
                  <td rowspan="9">
                    <img style="max-width: 200px" alt="image" id="ImagePath"
                         src="https://img2.baidu.com/it/u=2672967448,1375912484&fm=253&fmt=auto&app=138&f=PNG?w=500&h=500"/>
                  </td>
                </tr>

                <tr>
                  <td>会员姓名:</td>
                  <td id="TrueName"></td>
                </tr>
                <tr>
                  <td>会员卡号:</td>
                  <td id="CardId"></td>
                </tr>

                <tr>
                  <td>会员级别:</td>
                  <td id="MemberGroupName"></td>
                </tr>

                <tr>
                  <td>登记门店:</td>
                  <td id="StoreName"></td>
                </tr>
                <tr>
                  <td>登记时间:</td>
                  <td id="RegisterTime"></td>
                </tr>

                <tr>
                  <td>可用积分:</td>
                  <td id="EnablePoint"></td>
                </tr>

                <tr>
                  <td>可用余额:</td>
                  <td id="EnableValue"></td>
                </tr>
                <tr>
                  <td>签到活动:</td>
                  <td id="activity_name"></td>
                </tr>
                </tbody>
              </table>
              <div class="layui-btn-container" style="text-align: center; display: none" id="sign_in_button">
                <button type="button" onclick="sign_in()" style="font-size: 18px; width: 200px; height: 60px"
                        class="layui-btn layui-btn-lg">确认签到
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-col-md12">
          <div style="padding: 16px">
            <div style="font-size: 16px">
              会员可用余额:
              <span id="balance"></span>
              元
            </div>
            <table class="layui-hide" id="test" lay-filter="test"></table>
            <div class="layui-btn-container" id="charge_button_container"
                 style="text-align: center; padding-top: 50px; display: none">
              <button id="charge_button" type="button" style="font-size: 18px; width: 200px; height: 60px"
                      class="layui-btn layui-btn-lg">确认扣费
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
      <button class="layui-btn layui-btn-sm" lay-event="getCheckData">缴费</button>
      <!--            <button class="layui-btn layui-btn-sm" lay-event="getData">获取当前页数据</button>-->
      <!--            <div id="balance"></div>-->
    </div>
  </script>
  <script type="text/html" id="toolDemo">
    <div class="layui-clear-space">
      <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
      <a class="layui-btn layui-btn-xs" lay-event="more">
        更多
        <i class="layui-icon layui-icon-down"></i>
      </a>
    </div>
  </script>

  <script type="text/html" id="feeFlagTpl">
    {{# if(d.feeFlag == '0'){ }}
    <span style="font-size: 24px;color: red">待交费</span>
    <!--        <button class="layui-btn layui-bg-red">待支付</button>-->
    {{# } else if(d.feeFlag == '1'){ }}
    <button class="layui-btn layui-btn-primary layui-border-green">已支付</button>
    {{# } else{ }}
    <button class="layui-btn  layui-btn-danger layui-btn-xs">未知状态</button>
    {{# } }}
  </script>
  <script type="text/html" id="feeDetailInfosTpl">
    <table class="layui-table_">
      <!--            <colgroup>-->
      <!--                <col>-->
      <!--                <col>-->
      <!--            </colgroup>-->
      <!--            <thead>-->
      <!--            &lt;!&ndash;            <tr>&ndash;&gt;-->
      <!--            &lt;!&ndash;                <th>名称</th>&ndash;&gt;-->
      <!--            &lt;!&ndash;                <th>价格(元)</th>&ndash;&gt;-->
      <!--            &lt;!&ndash;            </tr>&ndash;&gt;-->
      <!--            </thead>-->
      <tbody>
      {{# layui.each(d.feeDetailInfos, function(key, value){ }}
      <tr>
        {{# if(value.feeFlag == '0'){ }}
        <td style="color: red">{{ value.itemName }}</td>
        <td style="color: red">{{ value.totalPrice }}</td>
        {{# } else if(value.feeFlag == '1'){ }}
        <td>{{ value.itemName }}</td>
        <td>{{ value.totalPrice }}</td>
        {{# } else if(value.feeFlag == '2'){ }}
        <td style="text-decoration: line-through;">{{ value.itemName }}</td>
        <td style="text-decoration: line-through;">{{ value.totalPrice }}</td>
        {{# } else{ }}
        <td>{{ value.itemName }}</td>
        <td>{{ value.totalPrice }}</td>
        {{# } }}
      </tr>
      {{# }); }}
      </tbody>
    </table>
  </script>

  <script>
    function load_order_list(data) {
      layui.use(["table", "dropdown"], function () {
        table = layui.table;
        // 创建渲染实例
        table.render({
          // method: 'POST',
          elem: "#test",
          data: data,
          // url: '/admin_api/v1/physical_examination/get_order_list', // 此处为静态模拟数据，实际使用时需换成真实接口
          // toolbar: '#toolbarDemo',
          where: {
            card_id: code,
          },
          // defaultToolbar: ['filter', 'exports', 'print', { // 右上角工具图标
          //     title: '提示',
          //     layEvent: 'LAYTABLE_TIPS',
          //     icon: 'layui-icon-tips',
          //     onClick: function (obj) { // 2.9.12+
          //         layer.alert('自定义工具栏图标按钮');
          //     }
          // }],
          // height: 'full-35',
          css: [
            // 重设当前表格样式
            ".layui-table-tool-temp{padding-right: 145px;}",
            ".layui-table-cell{height:auto}",
          ].join(""),
          cellMinWidth: 80,
          page: false,
          cols: [
            [
              { type: "checkbox" },
              {
                field: "feeFlag",
                title: "状态",
                // edit: 'textarea',
                // minWidth: 260,
                // expandedWidth: 260,
                width: 150,
                expandedMode: "default",
                templet: "#feeFlagTpl",
              },
              { field: "name", width: 80, title: "姓名" },
              { field: "clinicCode", width: 120, title: "单号" },
              {
                field: "memberNo",
                fieldTitle: "卡号",
                title: "卡号",
                hide: 0,
                width: 150,
                expandedMode: "tips",
              },
              {
                field: "feeDetailInfos",
                title: "明细",
                // edit: 'textarea',
                // minWidth: 260,
                // expandedWidth: 260,
                expandedMode: "default",
                templet: "#feeDetailInfosTpl",
              },
              {
                field: "totalPrice",
                title: "订单总额",
                width: 120,
                templet: function (d) {
                  return '<span style="color: green;font-size: 24px">' + d.totalPrice + "</span>";
                },
              },
              {
                field: "totalNeedPayPrice",
                title: "待缴费金额",
                width: 120,
                templet: function (d) {
                  if (d.totalNeedPayPrice > 0) {
                    return '<span style="color: red;font-size: 24px">' + d.totalNeedPayPrice + "</span>";
                  } else {
                    return '<span style="color: green;font-size: 24px">' + d.totalNeedPayPrice + "</span>";
                  }
                },
              },
              // {field: 'totalPrice', title: '总金额', width: 120},
              // {title: '操作', fixed: 'right', width: 134, minWidth: 125, templet: '#toolDemo'}
            ],
          ],
          done: function (res, curr, count, origin) {
            var id = this.id;
            $("#balance").html(member_info.EnableValue);
            var data = res.data;
            for (var i = 0; i < data.length; i++) {
              if (data[i].totalNeedPayPrice > 0) {
                // 设置某行选中
                table.setRowChecked("test", {
                  index: i, // 选中行的下标。 0 表示第一行
                });
              }
            }
          },
          error: function (res, msg) {
            console.log(res, msg);
          },
        });
        // 工具栏事件
        table.on("toolbar(test)", function (obj) {
          console.log("toolbar - test");
          var id = obj.config.id;
          var checkStatus = table.checkStatus(id);
          var othis = lay(this);
          switch (obj.event) {
            case "getCheckData":
              var data = checkStatus.data;
              // layer.alert(layui.util.escape(JSON.stringify(data)));
              charge(data);
              break;
            case "getData":
              var getData = table.getData(id);
              console.log(getData);
              layer.alert(layui.util.escape(JSON.stringify(getData)));
              break;
          }
        });
        // 表头自定义元素工具事件 --- 2.8.8+
        table.on("colTool(test)", function (obj) {
          console.log("colTool - test");
          var event = obj.event;
          console.log(obj);
        });
        // 触发单元格工具事件
        table.on("tool(test)", function (obj) {
          // 双击 toolDouble
          console.log("tool - test");
          var data = obj.data; // 获得当前行数据
          console.log(obj);
        });
      });
    }
  </script>
</div>
<script>
  var code = "";
  var card_id = "";
  var activity_guid = "";
  var member_guid = "";
  var member_info;
  var table;
  $("#code").bind("keydown", function check(event) {
    if (event.keyCode == "13") {
      $("#query").trigger("click");
      return false;
    }
  });

  $("#charge_button").click(function () {
    var checkStatus = table.checkStatus("test"); // demo 为表格标识
    var data = checkStatus.data; // 获取选中行的数据
    console.log(data); // 打印数据
    charge(data);
    // 这里可以执行其他操作，比如将数据发送到服务器等
  });

  $("#query").click(function () {
    code = $(":input[name='code']").val();
    post_layui_admin_api_v1("/physical_examination/get_order_list", { card_id: code }, function (result) {
      if (result.code == undefined) {
        return;
      }
      if (result.code !== 0) {
        layer.alert(result.msg, { icon: 2 });
        return false;
      }
      //查询成功逻辑
      member_info = result.data.member_info;
      console.log(member_info);
      $("#ImagePath")[0].src = member_info.ImagePath;
      $("#CardId").html(member_info.CardId);
      $("#TrueName").html(member_info.TrueName);
      $("#StoreName").html(member_info.StoreName);
      $("#MemberGroupName").html(member_info.MemberGroupName);
      $("#EnablePoint").html(member_info.EnablePoint);
      $("#EnableValue").html(member_info.EnableValue);
      $("#RegisterTime").html(member_info.RegisterTime);
      $("#sign_in_button").show();
      member_guid = member_info.MemberGuid;
      card_id = member_info.CardId;
      load_order_list(result.data.order_list);
      $("#charge_button_container").show();
    });
  });

  function clear() {
    $("#ImagePath")[0].src = "";
    $("#CardId").html("");
    $("#TrueName").html("");
    $("#StoreName").html("");
    $("#MemberGroupName").html("");
    $("#EnablePoint").html("");
    $("#EnableValue").html("");
    $("#RegisterTime").html("");
    $("#charge_button_container").hide();
    $("#balance").html("");
    member_guid = "";
    code = "";
    card_id = "";
    // 清空表格数据
    table.reload("test", {
      data: [], // 传递空数组以清空表格数据
    });
  }

  function exchange_success_callback() {
    // $("#query").trigger("click");
    $("input[name='code']").val("");
    clear();
    $("input[name='code']")[0].focus();
  }

  function charge(data) {
    if (isEmpty(card_id)) {
      layer.msg("请输入卡号");
      return;
    }
    //询问框
    layer.confirm(
      "确认扣费吗？",
      {
        btn: ["确定", "取消"], //按钮
      },
      function () {
        post_layui_admin_api_v1(
          "/physical_examination/charge",
          {
            card_id: card_id,
            member_guid: member_guid,
            data: JSON.stringify(data),
          },
          function (result) {
            layer.msg(
              result.msg,
              {
                icon: 1,
                time: 1000, //1秒关闭（如果不配置，默认是3秒）
              },
              function () {
                //do something
                exchange_success_callback();
              }
            );
          }
        );
      },
      function () {
        //  layer.msg('您已经取消操作', {icon: 2});
      }
    );
  }

  function switchToNewVersion() {
    window.location.href = "/admin/physical_examination/charge_beta";
  }
</script>
