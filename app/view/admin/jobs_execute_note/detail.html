{layout name="layui_plus"  /}
<div class="layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <form class="layui-form" id="edit_form" loadUrl="">
        <input type="hidden" name="id"/>
        <div class="layui-form-item">
          <label class="layui-form-label">进程PID</label>
          <div class="layui-input-block">
            <input type="text" name="pid" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入进程PID"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">任务id</label>
          <div class="layui-input-block">
            <input type="text" name="job_id" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入任务id"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">执行开始时间</label>
          <div class="layui-input-block">
            <input type="text" name="execute_begin_time" required="" lay-verType="tips"
                   lay-verify="required"
                   placeholder="请输入执行开始时间"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">执行结束时间</label>
          <div class="layui-input-block">
            <input type="text" name="execute_end_time" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入执行结束时间"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">耗时</label>
          <div class="layui-input-block">
            <input type="text" name="used_time" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入耗时"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">任务主体</label>
          <div class="layui-input-block">
            <input type="text" name="job_payload" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入任务主体"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">队列来源</label>
          <div class="layui-input-block">
            <input type="text" name="way" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入队列来源"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">0 失败 1 成功</label>
          <div class="layui-input-block">
            <input type="text" name="status" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入0 失败 1 成功"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>

        <hr/>
        <div class="layui-form-item" style="text-align: center;">
          <button class="layui-btn fsAdd" lay-submit="" lay-filter="save" url="/jobs_execute_note/add">保存
          </button>
          <button class="layui-btn fsEdit" lay-submit="" lay-filter="edit" url="/jobs_execute_note/edit">保存
          </button>
          <button type="button" class="layui-btn layui-btn-primary" function="close">关闭</button>
        </div>
      </form>
    </div>
  </div>
</div>

