{layout name="layui_plus"  /}
<div class='layui-fluid min-width-1120'>
  <div class='layui-card'>
    <div class='layui-card-body'>
      <div id='layui_form_query'>
        <form class="layui-form" id="query_form">
          <div class="layui-form-item">
            <div class="layui-inline">
              <select name="key" lay-verify="">
                <option value="card_id">会员卡号</option>
                <option value="bill_number">消费单号</option>
              </select>
            </div>
            <div class="layui-inline">
              <input type="text" name="value" placeholder="请输入" autocomplete="off"
                     class="layui-input"/>
            </div>
            <div class="layui-inline">
              <select name="status" lay-verify="">
                <option value="">返赠状态</option>
                <option value="0">待返</option>
                <option value="1">已返</option>
                <option value="-1">已撤销</option>
              </select>
            </div>

            <div class="layui-inline layui-input-wrap">
              <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
              <input type="text" name="create_time" autocomplete="off"
                     class="layui-input fsDate" readonly dateRange="1" placeholder="创建时间"/>
            </div>
            <div class="layui-inline">
              <div class="layui-input-inline">
                <button class="layui-btn" type="button" function="query"><i
                    class="layui-icon layui-icon-search"></i>查询
                </button>
                <button class="layui-btn layui-btn-normal" type="reset"><i
                    class="layui-icon layui-icon-delete"></i>重置
                </button>
              </div>
            </div>
          </div>
        </form>

        <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid" isLoad="1"
               url="/yky_consume_free_of_charge/index" isPage="1" defaultForm="query_form"
               defaultToolbar="filter" height="auto"></table>
        <div class="fsDatagridCols">
          <!--<p type="numbers" title="#"/>-->
          <p checkbox="true"/>
          <p align="center" field="id" title="编号">
          <p align="center" field="card_id" title="会员卡号">
          <p align="center" field="bill_number" title="消费单号">
          <p align="center" field="total_paid" title="实付金额">
          <p align="center" field="status" title="状态" templet="#statusTpl">
          <p align="center" field="consume_times" title="当前周期消费次数">
          <p align="center" field="reward_coupon_num" title="送券张数">
          <p align="center" field="create_time" title="创建时间">
          <p align="center" field="undo_time" title="撤销时间">

            <!--                    <p align="center" field="update_time" title="更新时间">-->
            <!--                    <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="120"/>-->
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/html" id="statusTpl">
  {{# if(d.status == '0'){ }}
  <button class="layui-btn layui-btn-xs layui-btn-primary layui-border-orange">待返</button>
  {{# } else if(d.status == '1'){ }}
  <button class="layui-btn layui-btn-xs layui-btn-primary layui-border-green">已返赠</button>
  {{# } else if(d.status == '-1'){ }}
  <button class="layui-btn layui-btn-xs layui-btn-primary layui-border-black">已撤销</button>
  {{# } else{ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">未知状态</button>
  {{#  } }}
</script>

<script type="text/html" id="toolbarDemo">
  <div class="layui-inline">
    <!--        <button class="layui-btn layui-btn-sm" function="top" topUrl="/admin/yky_consume_free_of_charge/detail"-->
    <!--                topMode="add"-->
    <!--                topWidth="800px"-->
    <!--                topHeight="95%" topTitle="新增">-->
    <!--            <i class="layui-icon layui-icon-addition"></i>新增-->
    <!--        </button>-->

    <button class="layui-btn layui-btn-sm layui-btn-danger" function="submit" url="/yky_consume_free_of_charge/undo"
            isMutiDml="1"
            isConfirm="1" confirmMsg="是否作废选中的单据？" inputs="id:">
      <i class="layui-icon layui-icon-unlink"></i>作废
    </button>
    <!--        <button class="layui-btn layui-btn-sm" function="refresh">-->
    <!--            <i class="layui-icon layui-icon-refresh"></i>刷新-->
    <!--        </button>-->
  </div>
</script>

<script type="text/html" id="barDemo">
  <!--<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="top" topUrl="/admin/yky_consume_free_of_charge/detail"-->
  <!--topMode="readonly"-->
  <!--topWidth="800px" topHeight="600px" topTitle="详情" inputs="guid:">查看</a>-->
  <a class="layui-btn layui-btn-xs" lay-event="top" topUrl="/admin/yky_consume_free_of_charge/detail" topMode="edit"
     topWidth="700px"
     topHeight="600px" topTitle="编辑" inputs="id:">编辑</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="submit" url="/yky_consume_free_of_charge/del"
     isConfirm="1"
     confirmMsg="是否确定删除当前记录？" inputs="id:">删除</a>
</script>
