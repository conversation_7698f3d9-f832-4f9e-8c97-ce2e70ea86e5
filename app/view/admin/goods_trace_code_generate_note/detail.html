{layout name="layui_plus" /}

<div class="layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <form class="layui-form" id="form" loadUrl="">
        <input type="hidden" name="guid"/>
        <input type="hidden" name="batch"/>

        <div class="layui-form-item">
          <label class="layui-form-label">关联产品</label>
          <div class="layui-input-block">
            <select name="goods_guid" lay-search required="" lay-verify="required" lay-verType="tips"
                    class="fsSelect"
                    dict="goods" addNull="1">
            </select>
          </div>
        </div>
        <!--                <div class="layui-form-item">-->
        <!--                    <div class="layui-inline">-->
        <!--                        <label class="layui-form-label">生效时间</label>-->
        <!--                        <div class="layui-input-inline">-->
        <!--                            <input type="text" required="" lay-verify="required" name="availability_time"-->
        <!--                                   placeholder="提货开始时间"-->
        <!--                                   autocomplete="off"-->
        <!--                                   class="layui-input fsDate" readonly-->
        <!--                                   dateType="date"/>-->
        <!--                            <div class="tips" style="cursor:pointer" onclick="set_availability_time()">-->
        <!--                                快速设为今天开始-->
        <!--                            </div>-->

        <!--                        </div>-->
        <!--                    </div>-->

        <!--                    <div class="layui-inline">-->
        <!--                        <label class="layui-form-label">失效时间</label>-->
        <!--                        <div class="layui-input-inline">-->
        <!--                            <input type="text" required="" lay-verify="required" name="expire_time"-->
        <!--                                   placeholder="提货截止时间"-->
        <!--                                   autocomplete="off"-->
        <!--                                   class="layui-input fsDate" readonly-->
        <!--                                   dateType="date"/>-->
        <!--                            <div class="tips" style="cursor:pointer" onclick="set_expire_time()">永久有效</div>-->
        <!--                            <div class="tips" style="cursor:pointer" onclick="set_expire_time_one_year()">明年底</div>-->
        <!--                            <div class="tips" style="cursor:pointer" onclick="set_expire_time_two_year()">后年底</div>-->

        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->

        <div class="layui-form-item" style="display: none">
          <label class="layui-form-label">卡号来源</label>
          <div class="layui-input-block">
            <input type="radio" name="way" value="1" checked title="系统生成" class="fsEditReadonly">
            <input type="radio" name="way" value="2" title="导入Excel">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">卡号类型</label>
          <div class="layui-input-block">
            <input type="radio" name="code_type" value="1" checked title="有序递增" class="fsEditReadonly">
            <input type="radio" name="code_type" value="2" title="16位随机数">
          </div>
        </div>


        <div class="layui-form-item" show_name="code_type" show_value="1">
          <div class="layui-inline">
            <label class="layui-form-label">卡号前缀</label>
            <div class="layui-input-inline">
              <input type="text" name="prefix" maxlength="6" placeholder="请输入卡号前缀,建议留空"
                     autocomplete="off" class="layui-input"/>
            </div>
            <div class="layui-form-mid layui-word-aux">如前缀为888,则生成的卡号为888100001形式</div>

            <!--                            <div class="tips">如前缀为888,则生成的卡号为888100001形式</div>-->
          </div>

        </div>

        <!--                    <div class="layui-form-item fsAdd">-->
        <!--                        <div class="layui-inline">-->
        <!--                            <label class="layui-form-label">跳数字</label>-->
        <!--                            <div class="layui-input-block">-->
        <!--                                <input type="checkbox" name="skip_number[4]" title="逢4跳过">-->
        <!--                                <input type="checkbox" name="skip_number[7]" title="逢7跳过">-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                    </div>-->


        <div class="layui-form-item fsAdd">
          <div class="layui-inline">
            <label class="layui-form-label">生成数量</label>
            <div class="layui-input-inline">
              <input required="" lay-verify="required" lay-verType="tips" type="tel" name="amount"
                     lay-verType="tips"
                     placeholder="单次最多生成5000张"
                     autocomplete="off" class="layui-input"/>
            </div>
            <div class="layui-form-mid layui-word-aux">张</div>

          </div>

          <div class="layui-inline" style="display: none">
            <label class="layui-form-label">跳数字</label>
            <div class="layui-input-inline" style="width: auto;">
              <input type="checkbox" name="skip_number[4]" title="跳过4">
              <input type="checkbox" name="skip_number[7]" title="跳过7">
            </div>
          </div>

          <!--                    <div class="layui-form-mid layui-word-aux">请填写6到12位密码</div>-->
        </div>


        <div show_name="way" show_value="2">
          <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">文件</label>
            <div class="layui-input-inline">
              <input type="text" id="file" lay-verType="tips"
                     name="file"
                     autocomplete="off" disabled="disabled"
                     class="layui-input"/>
            </div>
            <div class="layui-input-inline">
              <button type="button" class="layui-btn layui-btn-normal" function="upload"
                      fileElem="#file"
                      fileAccept="file"
                      fileExts="xls|xlsx" fileSize="3072"
                      inputs="ext:xls,local:1">
                上传文件
              </button>
            </div>
            <div class="layui-form-mid layui-word-aux">请上传excel文件.点击<a
                href="/file/template/coupon_import.xlsx?v=20240130">下载模板</a></div>
          </div>
        </div>


        <!--                <div class="layui-form-item">-->
        <!--                    <div class="layui-inline">-->
        <!--                        <label class="layui-form-label">归属</label>-->
        <!--                        <div class="layui-input-inline">-->
        <!--                            <select name="owner_user_id" required="" lay-verify="required" lay-verType="tips"-->
        <!--                                    class="fsSelect"-->
        <!--                                    dict="user"-->
        <!--                                    addNull="1">-->
        <!--                            </select>-->
        <!--                        </div>-->
        <!--                    </div>-->

        <!--                </div>-->

        <!--                <div class="layui-form-item">-->
        <!--                    <div auth="multiple_exchange">-->
        <!--                        <label class="layui-form-label">总提货次数</label>-->
        <!--                        <div class="layui-input-inline">-->
        <!--                            <input type="tel" name="send_num" required="" lay-verify="required|number"-->
        <!--                                   lay-verType="tips"-->
        <!--                                   lay-verify="number" value="1"-->
        <!--                                   placeholder="每张卡提货X次后卡失效"-->
        <!--                                   autocomplete="off" class="layui-input"/>-->
        <!--                        </div>-->
        <!--                        <div class="layui-form-mid layui-word-aux">次</div>-->
        <!--                    </div>-->
        <!--                    <div auth="user/index">-->
        <!--                        <label class="layui-form-label">归属</label>-->
        <!--                        <div class="layui-input-inline">-->
        <!--                            <select name="owner_user_id" required="" lay-verify="required" lay-verType="tips"-->
        <!--                                    class="fsSelect"-->
        <!--                                    dict="user"-->
        <!--                                    addNull="1">-->
        <!--                            </select>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->


        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label">数量</label>-->
        <!--                    <div class="layui-input-block">-->
        <!--                        <input type="tel" name="amount" required="" lay-verType="tips" lay-verify="number"-->
        <!--                               placeholder="请输入数量,最多1000条"-->
        <!--                               autocomplete="off" class="layui-input"/>-->
        <!--                    </div>-->
        <!--                </div>-->


        <div class="layui-form-item" auth="code_used_limit">
          <div class="layui-inline">
            <label class="layui-form-label">兑换限制</label>
            <div class="layui-input-inline">
              <select lay-filter="used_rate_limit_type" name="used_rate_limit_type">
                <option value="">兑换限制</option>
                <option value="0" selected="">不限制</option>
                <option value="1">每天限兑N次</option>
                <option value="2">每周限兑N次</option>
                <option value="3">每月限兑N次</option>
                <option value="4">每季度限兑N次</option>
                <option value="5">每年限兑N次</option>
              </select>
            </div>
          </div>
          <div class="layui-inline" id="used_rate_limit_num" style="display: none">
            <label class="layui-form-label">限制次数</label>
            <div class="layui-input-inline">
              <input type="tel" placeholder="请输入限制次数" name="used_rate_limit_num" autocomplete="off"
                     class="layui-input"/>
            </div>
          </div>
        </div>

        <div class="layui-form-item">
          <!--                    <div class="layui-inline">-->
          <!--                        <label class="layui-form-label">序号</label>-->
          <!--                        <div class="layui-input-inline">-->
          <!--                            <input type="tel" name="sort" required="" lay-verType="tips" lay-verify="required"-->
          <!--                                   placeholder="请输入序号(越小越靠前)" autocomplete="off" class="layui-input"/>-->
          <!--                        </div>-->
          <!--                    </div>-->
          <div class="layui-inline">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
              <input type="radio" name="status" value="-1" title="待激活">
              <div class="layui-unselect layui-form-radio layui-form-radioed"><i
                  class="layui-anim layui-icon"></i>
              </div>
              <input type="radio" name="status" value="0" title="已激活" checked="">
              <div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-form-item fsEdit">
          <label class="layui-form-label">备注</label>
          <div class="layui-input-block">
            <input type="text" name="send_remark"
                   placeholder="请输入备注"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>


        <div class="layui-form-item" style="text-align: center;">
          <button class="layui-btn fsAdd" lay-submit="" lay-filter="save"
                  requestSuccessCallback2="savecallback" url="/goods_trace_code_generate_note/add">
            立即生成
          </button>
          <button class="layui-btn fsEdit" lay-submit="" lay-filter="edit"
                  url="/goods_trace_code_generate_note/edit">
            确认修改
          </button>
          <!--                    <button class="layui-btn fsEdit" lay-submit="" lay-filter="edit" url="/code/edit">保存</button>-->
          <button type="button" class="layui-btn layui-btn-primary" function="close">关闭</button>
        </div>
        <!--                <hr/>-->
        <!--                <div class="layui-form-item" style="text-align: center;">-->
        <!--                    <button class="layui-btn" type="button" id="generate_button" onclick="generate()">立即生成</button>-->
        <!--                </div>-->
      </form>
    </div>
  </div>
</div>
<script>
  let coupon_send_note_guid = getQueryString('coupon_send_note_guid');
  let type = getQueryString('type');


  //个位数补0
  function getZero(num) {
    // 单数前面加0
    if (parseInt(num) < 10) {
      num = '0' + num;
    }
    return num;
  }

  //获取现在时间
  function getNowDateTime() {

    var myDate = new Date();
    var year = myDate.getFullYear(); //获取当前年
    var mon = myDate.getMonth() + 1; //获取当前月
    var date = myDate.getDate(); //获取当前日
    var hours = myDate.getHours(); //获取当前小时
    var minutes = myDate.getMinutes(); //获取当前分钟
    var seconds = myDate.getSeconds(); //获取当前秒
    // 以自己需要的方式拼接
    var now = year + "-" + this.getZero(mon) + "-" + this.getZero(date);
    return now;
  }

  function set_availability_time() {
    $(":input[name='availability_time']").val(getNowDateTime());
  }

  function set_expire_time() {
    $(":input[name='expire_time']").val('2099-12-31');
  }

  function set_expire_time_one_year() {
    let now = new Date();
    $(":input[name='expire_time']").val((now.getFullYear() + 1) + '-12-31');
  }

  function set_expire_time_two_year() {
    $(":input[name='expire_time']").val((now.getFullYear() + 2) + '-12-31');
  }

  layui.fsCallback.form.form = function (thisForm, fsCommon) {
    //业务处理
    console.log('fsCallback');
    layui.use('form', function () {
      var form = layui.form;
      //各种基于事件的操作，下面会有进一步介绍
      form.on('select(used_rate_limit_type)', function (data) {
        var used_rate_limit_type = data.value; //得到被选中的值
        if (used_rate_limit_type > 0) {
          $('#used_rate_limit_num').show();
        } else {
          $('#used_rate_limit_num').hide();
        }
      });

      // form.on('submit(*)', function (data) {
      //     console.log(data.elem) //被执行事件的元素DOM对象，一般为button对象
      //     console.log(data.form) //被执行提交的form对象，一般在存在form标签时才会返回
      //     console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
      //     return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
      // });
    });
  }

  function generate() {
    var data = get_form_data('form');
    $("#generate_button").addClass("layui-disabled");
    $('#generate_button').attr("disabled", true);
    post_layui_admin_api_v1('/code/generate', data, function (result) {
      layer.alert('生成成功', {
        'yes': function () {
          fsCommon.setRefreshTable("1"); //刷新列表
          parent.layer.close(parent.layer.getFrameIndex(window.name)); //关闭当前窗体
        }
      }), function (result) {
        $("#generate_button").removeClass("layui-disabled");
        $('#generate_button').attr("disabled", false);
        layer.alert(result.msg);
      }
    });
  }

  //result:返回结果集
  //_this:fsForm对象，可以调用里面的方法
  layui.fsRequestSuccessCallback.savecallback = function (result, _this, fsCommon) {
    let host = window.location.host;
    let path = '//' + host + '/admin/goods_trace_code_generate_note/index';
    let title = '溯源管理';
    let current_tab = top.$('#LAY_app_tabsheader>li.layui-this').attr('lay-attr');
    let path_obj = top.$('#LAY_app_tabsheader').find('li[lay-attr="' + path + '"]');
    if (result.code === 0) {
      // load_data();
      parent.layer.close(parent.layer.getFrameIndex(window.name));
      let _top = top;
      if (path.indexOf(current_tab) === -1) {
        fsCommon.setRefreshTable("1"); //刷新列表
      }
      top.layer.msg(result.msg, {
        icon: 1,
        time: 1500 //1.5 秒关闭（如果不配置，默认是3秒）
      }, function () {
        //2秒后自动执行这里面的内容
        if (path.indexOf(current_tab) === -1) {
          // console.log('当前不再发卡管理页面 关闭后重新打开')
          let is_opened = path_obj.length !== 0;
          if (is_opened) {
            // console.log('打开了 先关闭 再重新打开');
            path_obj.find(".layui-tab-close").trigger("click");
          } else {
            // console.log('之前没打开发卡管理页面');
          }
          let options = {
            url: path,
            title: title
          };
          _top.layui.index.openTabsPage(options); //这里要注意的是 parent 的层级关系
        }
      });
    } else {
      layer.alert(result.msg, {
        icon: 2
      }, function (index) {
        //do something
        layer.close(index);
      });
    }
  }
</script>