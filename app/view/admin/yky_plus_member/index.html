{layout name="layui_plus" /}
<style>
    .content {
        text-align: center;
    }

    .qrcode {
        width: 20px;
        height: 20px;
        margin-left: 5px;
    }
</style>
<div class="layui-layout layui-layout-admin">
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body layui-form-query">
        <form class="layui-form" id="query_form">
          <div class="layui-form-item">
            <div class="layui-inline">
              <select name="key" lay-verify="">
                <option value="card_id">卡号</option>
              </select>
            </div>
            <div class="layui-inline">
              <input type="text" name="value" placeholder="请输入" autocomplete="off"
                     class="layui-input"/>
            </div>
            <div class="layui-inline layui-input-wrap">
              <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
              <input type="text" name="create_time" autocomplete="off"
                     class="layui-input fsDate" readonly dateRange="1" placeholder="创建时间"/>
            </div>
            <div class="layui-inline">
              <div class="layui-inline">
                <button class="layui-btn" type="button" function="query"><i
                    class="layui-icon layui-icon-search"></i>查询
                </button>
                <button class="layui-btn layui-btn-normal" type="reset"><i
                    class="layui-icon layui-icon-delete"></i>重置
                </button>
                <button type="button" class="layui-btn" function="refresh">
                  <i class="layui-icon layui-icon-refresh"></i>刷新
                </button>
                <button id="export" class="layui-btn" type="button"
                        onclick="export_note_url('/admin_api/v1/yky_plus_member/export')"
                ><i class="layui-icon">&#xe609;</i>导出
                </button>
              </div>
            </div>
          </div>
        </form>

        <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid" isLoad="1"
               url="/yky_plus_member/index" isPage="1" defaultForm="query_form" defaultToolbar="filter"
               height="auto"></table>
        <div class="fsDatagridCols">
          <!--<p type="numbers" title="#"/>-->
          <!--<p checkbox="true"/>-->
          <!--                    <p field="store_guid" dict="store" hide="true" title="所属门店">-->
          <p align="center" field="card_id" title="卡号">
          <p align="center" field="total_buy_times" title="累计购买或续费次数" sort="true">
          <p align="center" field="total_consume_times" title="累计消费次数" sort="true">
          <p align="center" field="total_consume_money" title="累计消费金额" sort="true">
          <p align="center" field="total_add_value_times" title="累计充值次数" sort="true">
          <p align="center" field="total_add_value_money" title="累计充值金额" sort="true">
          <p align="center" field="first_buy_bill_number" title="首次购卡单号" sort="true">
          <p align="center" field="first_buy_date_time" title="首次购卡时间" sort="true">
          <p align="center" field="last_buy_date_time" title="最后续费时间" sort="true">
          <p align="center" field="last_buy_bill_number" title="最后续费单号">
          <p align="center" field="expired_time" title="当前过期时间" sort="true">
          <p align="center" field="total_coupon_send_num" title="累计发券张数" sort="true">
          <p align="center" field="total_coupon_used_num" title="累计用张数" sort="true">
          <p align="center" field="create_time" title="创建时间" sort="true">
          <p align="center" field="update_time" title="更新时间" sort="true">
          <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="80">
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-inline">
    <!--        <button class="layui-btn layui-btn-sm" function="top" topUrl="/admin/user/detail" topMode="add"-->
    <!--                topWidth="800px"-->
    <!--                topHeight="600px" topTitle="新增">-->
    <!--            <i class="layui-icon layui-icon-addition"></i>新增-->
    <!--        </button>-->
    <!--        <button auth='user/get_organization' class="layui-btn layui-btn-sm layui-btn-normal" function="top"-->
    <!--                topUrl="/admin/user/organization"-->
    <!--                topMode="add"-->
    <!--                topWidth="400px"-->
    <!--                topHeight="600px" topTitle="组织机构">组织机构-->
    <!--        </button>-->
    <button class="layui-btn  layui-btn-sm layui-btn-danger" function="submit" url="#" isMutiDml="1"
            isConfirm="1" confirmMsg="是否确定删除选中的数据？" inputs="id:" style="display: none">
      <i class="layui-icon layui-icon-delete"></i>删除
    </button>
    <!--        <button class="layui-btn layui-btn-sm" function="refresh">-->
    <!--            <i class="layui-icon layui-icon-refresh"></i>刷新-->
    <!--        </button>-->
  </div>
</script>
<script type="text/html" id="barDemo">
  <!--    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="top" topUrl="/admin/user/detail" topMode="readonly"-->
  <!--       topWidth="800px" topHeight="600px" topTitle="查看" inputs="guid:">查看</a>-->

  <a auth="yky_plus_member/del" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="submit"
     url="/yky_plus_member/del" isConfirm="1"
     confirmMsg="是否确定删除当前记录？" inputs="guid:">删除</a>

</script>
<script>

  layui.fsButton.view_qrcode = function (elem, data, datagrid) {
    var domain = document.location.protocol + '//' + window.location.host;
    console.log(data);

    post_layui_admin_api_v1('/user/get_user_bind_url', {
      'bid': data.bid,
      'user_guid': data.guid
    }, function (result) {
      var view_url = result.data.url;
      var qrcode = domain + '/index/plugins/create_qrcode/?size=300&data=' + encodeURIComponent(view_url);
      var content = "<div class='content'>\
            <img style='width: 200px;padding: 20px' src= " + qrcode + ">\
            </div>";
      //自定页
      layer.open({
        type: 1,
        title: '微信扫码绑定接收通知',
        skin: 'layui-layer-rim', //加上边框
        area: ['300px', '300px'], //宽高
//        closeBtn: true, //不显示关闭按钮
//        shift: 2,
//        shadeClose: true, //开启遮罩关闭
        content: content
      });
    })


  };


  layui.fsButton.unlock = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定启用吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      var url = "/user/unlock";
      var data = {};
      data.guid = guid;
      data = jQuery.param(data);
      post_layui_admin_api_v1(url, data, function (result) {
        layer.msg(result.msg, { icon: 1 });
        datagrid.refresh();
      })

    }, function () {
      //layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.lock = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定禁用吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      var url = "/user/lock";
      var data = {};
      data.guid = guid;
      data = jQuery.param(data);
      post_layui_admin_api_v1(url, data, function (result) {
        layer.msg(result.msg, { icon: 1 });
        datagrid.refresh();
      })
    }, function () {
      //layer.msg('您已经取消操作', {icon: 2});
    });
  };

</script>
