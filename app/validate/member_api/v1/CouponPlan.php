<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class CouponPlan extends ValidateBase
{
    protected $rule = [
        'name'             => ['require'],
        'guid'             => ['require'],
        'goods_guid'       => ['require'],
        'coupon_plan_guid' => ['require'],
    ];

    protected $message = [
        'name.require' => '请输入标题',
    ];
    protected $scene = [
        'add'        => ['name'],
        'edit'       => ['guid', 'name'],
        'goods_list' => ['coupon_plan_guid'],
        'detail'     => ['guid'],
        'add_goods'  => ['goods_guid', 'coupon_plan_guid'],
        'del_goods'  => ['goods_guid', 'coupon_plan_guid']
    ];
}
