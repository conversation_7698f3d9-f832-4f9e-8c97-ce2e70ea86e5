<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class Share extends ValidateBase
{
    protected $rule = [
        'cash_money|提现金额'             => ['require', 'float', '>:0'],
        'pay_type|提现方式'               => ['require', 'in:wechat,alipay,bank'],
        'wechat_true_name|微信姓名'       => ['requireIf:pay_type,wechat', 'chs'],
        'wechat_account|微信号'           => ['requireIf:pay_type,wechat'],
        'alipay_true_name|支付宝姓名'     => ['requireIf:pay_type,alipay', 'chs'],
        'alipay_account|支付宝账号'       => ['requireIf:pay_type,alipay'],
        'bank_true_name|银行卡开户人姓名' => ['requireIf:pay_type,bank', 'chs'],
        'bank_name|银行名称'              => ['requireIf:pay_type,bank', 'chs'],
        'bank_account|银行卡号'           => ['requireIf:pay_type,bank'],
        'mobile|手机号'                   => ['require', 'mobile'],
        'name|姓名'                       => ['require'],
        //       'amount'            => ['require', 'number', '<=:3000', '>:0'],
        //       'user_id'           => ['require', 'number'],
        //       'name'              => ['require', 'length:1,32'],
        //       'type'              => ['require', 'in:1,2'],
        //       'description'       => ['max:100000'],
        //       'value'             => ['require', 'float', 'in:50,100,200,500,1000'],
        //       'coupon_value'      => ['require', 'float', 'in:50,100,200,500,1000'],
        //       'expire_time'       => ['date'],
        //       'availability_time' => ['date'],
    ];

    protected $message = [
        'cash_money.require' => '请输入提现金额',
        'cash_money.gt'      => '提现金额需要大于0',
        'pay_type.require'   => '请选择提现方式',
        //
        //       'amount.elt' => '单次最多可生成5000张',
        //       'amount.gt'  => '单次最少生成1张',
    ];

    protected $scene = [
        'apply' => ['cash_money', 'pay_type', 'wechat_true_name', 'wechat_account', 'alipay_true_name', 'alipay_account', 'bank_true_name', 'bank_name', 'bank_account'],
        'join'  => ['name', 'mobile'],
        //       'del'                      => ['guid'],
        //       'generate'                 => ['amount', 'coupon_guid', 'owner_user_id', 'send_num', 'expire_time', 'availability_time'],
        //       'import'                   => ['coupon_guid', 'file'],
        //       'send'                     => ['coupon_guid'],
        //       'batch_send'               => ['coupon_guid', 'file'],
        //       'batch_send_excel'         => ['send_amount', 'mobile'],
        //       'single_send'              => ['coupon_guid', 'send_amount', 'mobile'],
        //       'pass'                     => ['generate_guid'],
        //       'examine'                  => ['apply_guid'],
        //       'apply'                    => ['amount', 'guid'],
        //       'revoke'                   => ['coupon_send_guid'],
        //       'apply_coupon'             => ['apply_type'],
        //       'batch_apply_coupon'       => ['file', 'coupon_type'],
        //       'batch_apply_coupon_excel' => ['amount', 'value', 'mobile'],
        //       'single_apply_coupon'      => ['amount', 'coupon_guid'],
        //       'get_code_info'            => ['code'],
        //       'verify_code'              => ['code', 'password'],
    ];
}
