<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Register extends ValidateBase
{
    protected $rule = [
        'company'             => ['require', 'chsAlphaNum'],
        'true_name'           => ['require', 'chsAlphaNum'],
        'mobile'              => ['require', 'mobile'],
        'password|登录密码'   => ['require', 'confirm:repassword', 'safe_password'],
        'repassword|重复密码' => ['require', 'confirm:password', 'safe_password'],
        'version_guid'        => ['require', 'guid'],
    ];

    protected $message = [
        'mobile.require'     => '请输入手机号',
        'mobile.mobile'      => '请输入正确的手机号',
        'password.confirm'   => '登录密码和重复密码不一致',
        'repassword.confirm' => '登录密码和重复密码不一致!',
    ];
    protected $scene = [
        'submit' => ['company', 'true_name', 'mobile', 'password', 'version_guid'],
    ];
}
