<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class Scores extends ValidateBase
{
    protected $rule = [
        'guid'        => ['require', 'length' => 36],
        'member_guid' => ['require', 'length' => 36],
    ];

    protected $message = [
        'guid.require' => '请传入GUID参数!',
    ];

    protected $scene = [
        'submit'                    => ['member_guid'],
        'detail'                    => ['guid'],
        'member_detail'             => ['guid'],
        'member_group_detail'       => ['guid'],
        'member_group_score_sort'   => ['guid'],
        'member_group_article_list' => ['guid'],
        'member_qrcode'             => ['guid'],
    ];
}
