<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class Bind extends ValidateBase
{
    protected $rule = [
        'bind_note_guid' => ['require'],
        'account'        => ['require'],
        'password'       => ['require'],
        'user_account'   => ['require'],
    ];

    protected $message = [
        'account.require'      => '请输入账号',
        'user_account.require' => '请输入工号',
        'password.require'     => '请输入密码',
    ];

    protected $scene = [
        'business'  => ['account', 'user_account', 'password'],
        'qrcode'    => ['account', 'user_account', 'password', 'guid'],
        'yky_agent' => ['account', 'password'],
        'yky_user'  => ['user_name', 'user_account', 'password'],
    ];
}