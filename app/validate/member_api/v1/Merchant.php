<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class Merchant extends ValidateBase
{
    protected $rule = [
        'sub_mch_id|商户号'      => ['require', 'number', 'length:10', 'regex:^1\d{9}$'],
        'rate|申请费率'          => ['require', 'float', 'between:0.25,0.59'],
        'type|报名类型'          => ['require', 'in:1,2'],
        'activity_type|活动类型' => ['require', 'in:1'],
        'account|账号'           => ['require'],
    ];

    protected $message = [
        'sub_mch_id.require' => '请输入商户号!',
        'sub_mch_id.number'  => '商户号只能是数字!',
        'sub_mch_id.length'  => '请输入10位数商户号!',
        'sub_mch_id.regex'   => '请输入1开头10位数商户号!',
    ];

    protected $scene = [
        'apply'             => ['sub_mch_id'],
        'query'             => ['sub_mch_id'],
        'query_rate'        => ['sub_mch_id'],
        'apply_change_rate' => ['sub_mch_id', 'rate'],
        'activity_submit'   => ['type', 'account', 'activity_type']
    ];
}
