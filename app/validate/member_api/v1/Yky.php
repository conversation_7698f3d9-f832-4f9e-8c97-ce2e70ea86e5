<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Yky extends ValidateBase
{
    protected $rule = [
        'type|兑换途径'           => ['require', 'in:1,2'],
        'exchange_type|兑换类型'  => ['require', 'in:1'],
        'exchange_value|兑换额度' => ['require', 'float'],
    ];
    protected $message = [
        'type.require' => '请输入兑换途径!',
    ];
    protected $scene = [
        'point_or_value_exchange' => ['type', 'exchange_type', 'exchange_value'],
    ];
}