<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class CouponExchange extends ValidateBase
{
    protected $rule = [
        'exchange_type' => ['require', 'in:1,2,3,4'],
        'oil_card_id'   => ['requireIf:exchange_type,1', 'number'],
        'mobile'        => ['require', 'mobile'],
        'code'          => ['require', 'number'],
        'password'      => ['require', 'number'],
        'coupon_value'  => ['require', 'number'],
    ];

    protected $message = [
        'oil_card_id.requireIf' => '请输入油卡号',
        'mobile.require'        => '请输入手机号',
        'mobile.mobile'         => '请输入正确的手机号',
        'code.require'          => '请输入卡号',
        'password.require'      => '请输入密码',
        'password.number'       => '请输入数字密码',
        'coupon_value.require'  => '请选择兑换面值',
    ];
    protected $scene = [
        'exchange' => ['oil_card_id', 'mobile', 'code', 'password', 'coupon_value'],
        'money'    => ['code', 'password'],
        'wechat'   => ['code', 'password'],
    ];
}
