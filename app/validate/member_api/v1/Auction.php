<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Auction extends ValidateBase
{


    protected $rule = [
        'goods_guid'           => ['require'],
        'offer_price|出价价格' => ['require', 'number'],
    ];

    protected $message = [
        'offer_price.number' => '请输入正确的出价价格',
    ];

    protected $scene = [
        'offer' => ['goods_guid', 'offer_price'],
    ];
}
