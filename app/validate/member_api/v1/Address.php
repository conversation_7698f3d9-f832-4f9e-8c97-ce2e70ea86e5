<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Address extends ValidateBase
{
    protected $rule = [
        'guid'                 => ['require'],
        'true_name|姓名'       => ['require'],
        'mobile|手机号'        => ['require'],
        'address|地址'         => ['require'],
        'province_id|省'       => ['require'],
        'city_id|市'           => ['require'],
        'area_id|区'           => ['require'],
        'province_name|省名称' => ['require'],
        'city_name|市名称'     => ['require'],
        'area_name|区名称'     => ['require'],

    ];
    protected $message = [
        'true_name.require' => '请填写姓名',
    ];

    protected $scene = [
        'add'                     => ['true_name', 'mobile', 'address', 'province_id', 'city_id', 'area_id'],
        'edit'                    => ['guid', 'true_name', 'mobile', 'address', 'province_id', 'city_id', 'area_id'],
        'del'                     => ['guid'],
        'detail'                  => ['guid'],
        'set_default'             => ['guid'],
        'get_area_id_from_wechat' => ['province_name', 'city_name', 'area_name'],
    ];
}
