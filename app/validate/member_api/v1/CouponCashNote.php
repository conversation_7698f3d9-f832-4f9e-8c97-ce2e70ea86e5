<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class CouponCashNote extends ValidateBase
{
    protected $rule = [
        'alipay_true_name|支付宝姓名' => ['chs'],
        'alipay_account|支付宝账号'   => ['require'],
        'token'                       => ['require'],
    ];

    protected $message = [

    ];

    protected $scene = [
        'submit' => ['alipay_true_name', 'alipay_account', 'remark_image_list', 'token'],
    ];
}
