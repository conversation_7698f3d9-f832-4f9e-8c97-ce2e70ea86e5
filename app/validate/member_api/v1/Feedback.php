<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Feedback extends ValidateBase
{
    protected $rule = [
        'guid'             => ['require'],
        'name|姓名'        => ['require'],
        'mobile|手机号'    => ['require'],
        'title|标题'       => ['require'],
        'content|反馈内容' => ['require'],
    ];
    protected $message = [
        'true_name.require' => '请填写姓名',
    ];

    protected $scene = [
        'add'    => ['name', 'mobile', 'title', 'content'],
        'detail' => ['guid'],
    ];
}
