<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Barter extends ValidateBase
{
    protected $rule = [
        'guid'                          => ['require'],
        'goods_name|商品名称'           => ['require'],
        'goods_price|商品价格'          => ['require', 'float'],
        'goods_item_type_guid|商品类别' => ['require'],
    ];
    protected $message = [
        'goods_name.require' => '请输入商品名称!',
    ];
    protected $scene = [
        'publish_goods' => ['goods_name', 'goods_price', 'goods_item_type_guid'],
        'edit_goods'    => ['guid', 'goods_name', 'goods_price', 'goods_item_type_guid'],
        'goods_detail'  => ['guid'],
    ];
}