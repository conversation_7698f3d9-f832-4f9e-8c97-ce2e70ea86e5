<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Passport extends ValidateBase
{
    protected $rule = [
        'code'           => ['require'],
        'iv'             => ['require'],
        'encrypted_data' => ['require'],
        'appid'          => ['require'],
        'drive'          => ['require'],
        'bid'            => ['require'],
    ];

    protected $message = [
        'code.require'           => '缺少code',
        'iv.require'             => '缺少iv',
        'encrypted_data.require' => '缺少encrypted_data',
    ];

    protected $scene = [
        'login'            => ['drive', 'bid'],
        'get_phone_number' => ['iv', 'encrypted_data'],
    ];
}