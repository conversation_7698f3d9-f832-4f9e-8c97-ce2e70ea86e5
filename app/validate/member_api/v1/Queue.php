<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;


class Queue extends ValidateBase
{
    protected $rule = [
        'job_name|任务名'   => ['require'],
        'queue|队列名'      => ['require'],
        'action_name|操作'  => ['require'],
        'action_event|操作' => ['require', 'in:restart,pause,del'],
    ];
    protected $scene = [
        'change_status' => ['job_name', 'queue', 'action_name', 'action_event'],
    ];
}
