<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class GoodsOrder extends ValidateBase
{
    protected $rule = [
        'guid'         => ['require'],
        'order_guid'   => ['require'],
        'pick_up_code' => ['require'],
        'pay_type'     => ['require', 'in:1,2'],
    ];

    protected $message = [
        'guid.require' => '参数不正确',
    ];

    protected $scene = [
        'pay'                   => ['order_guid', 'pay_type'],
        'get_pickup_qrcode_url' => ['guid', 'pick_up_code'],
        'detail'                => ['guid'],
        'close'                 => ['order_guid'],
        'confirm'               => ['order_guid'],
    ];
}
