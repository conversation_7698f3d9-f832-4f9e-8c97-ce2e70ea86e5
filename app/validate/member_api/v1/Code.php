<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Code extends ValidateBase
{
    protected $rule = [
        'token'                        => ['require'],
        'guid'                         => ['require'],
        'coupon_send_note_guid'        => ['require'],
        'code'                         => ['requireIf:pick_up_type,1'],
        'password'                     => ['requireIf:pick_up_type,1'],
        'phone'                        => ['requireIf:pick_up_type,2', 'mobile'],
        'verify_code'                  => ['requireIf:pick_up_type,2', 'number', 'length:6'],
        'true_name'                    => ['require'],
        'mobile'                       => ['require', 'mobile'],
        'address'                      => ['require'],
        'request_send_or_pick_up_time' => ['require', 'date'],
        'choose_goods_list'            => ['require'],
        'province_id'                  => ['require', 'number'],
        'city_id'                      => ['require', 'number'],
        'area_id'                      => ['require', 'number'],
        'pick_up_type'                 => ['in:1,2'],
    ];

    protected $message = [

        'mobile.mobile'  => '请输入正确的手机号',
        'mobile.require' => '请输入手机号',

        'code.require'     => '请输入卡号',
        'password.require' => '请输入密码',

        'phone.mobile'    => '请输入正确的手机号',
        'phone.require'   => '请输入手机号',
        'phone.requireIf' => '请输入手机号',

        'verify_code.require' => '请输入六位数验证码',
        'verify_code.number'  => '请输入六位数验证码',
        'verify_code.length'  => '请输入六位数验证码',

        'true_name.require'   => '请输入姓名',
        'address.require'     => '请输入详细地址',
        //        'choose_goods_list.require' => '请选择产品',
        'province_id.require' => '请选择省',
        'city_id.require'     => '请选择市',
        'area_id.require'     => '请选择区',

    ];

    protected $scene = [
//        'get_goods_list_by_token' => [''],
//        'get_goods_list_by_coupon_guid' => ['guid'],
'order_detail'     => ['guid'],
'code_detail'      => ['guid'],
'code_note_detail' => ['coupon_send_note_guid'],
//        'get_submit_order_config' => ['token'],
'verify_code'      => ['code', 'password', 'pick_up_type', 'phone', 'verify_code'],
'submit_order'     => ['mobile']
    ];
}
