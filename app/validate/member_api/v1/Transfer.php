<?php

namespace app\validate\member_api\v1;


use app\common\validate\ValidateBase;

class Transfer extends ValidateBase
{
    protected $rule = [
        'card_id|卡号'          => ['require'],
        'from_card_id|转出卡号' => ['require'],
        'to_card_id|转入卡号'   => ['require'],
        'value|金额'            => ['require', 'float'],
    ];
    protected $message = [
        'from_card_id.require' => '缺少转出卡号',
    ];
    protected $scene = [
        'value_transfer'  => ['from_card_id', 'to_card_id', 'value'],
        'get_member_info' => ['card_id'],
    ];
}
