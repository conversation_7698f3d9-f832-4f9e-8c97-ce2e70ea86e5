<?php

namespace app\validate\member_api\v1;

use app\common\validate\ValidateBase;

class Pay extends ValidateBase
{

    protected $rule = [
        'bill_no|单号'            => ['require'],
        'bid'                     => ['require'],
        'order_guid'              => ['require'],
        'total_fee|支付金额'      => ['require'],
        'out_trade_no|商户订单号' => ['require'],
        'auth_code|付款码'        => ['require'],
    ];

    protected $message = [
    ];

    protected $scene = [
        'apply'    => ['bid', 'type', 'order_guid'],
        'query'    => ['bill_no'],
        //       'create_order' => ['total_fee'],
        'micropay' => ['total_fee', 'out_trade_no', 'auth_code'],
    ];
}
