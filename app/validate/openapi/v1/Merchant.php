<?php

namespace app\validate\openapi\v1;

use app\common\validate\ValidateBase;

class Merchant extends ValidateBase
{
    protected $rule = [
        'mch_id|服务商商户号' => ['require', 'number', 'length:10', 'regex:^1\d{9}$'],
        'sub_mch_id|子商户号' => ['require', 'number', 'length:10', 'regex:^1\d{9}$'],
        'rate'                => ['require', 'float', 'between:0.2,0.6'],
    ];

    protected $message = [
        'mch_id.require'     => '请传入服务商商户号',
        'sub_mch_id.require' => '请传入子商户号',
    ];

    protected $scene = [
        'change_rate' => ['mch_id', 'sub_mch_id', 'rate'],
    ];
}
