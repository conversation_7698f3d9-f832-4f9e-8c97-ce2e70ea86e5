<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponBook extends ValidateBase
{
    protected $rule = [
        'id'               => ['require',],
        'guid'             => ['require',],
        'bid'              => ['require',],
        'name'             => ['require',],
        'coupon_item_guid' => ['require',],
        'create_time'      => ['require',],
        'update_time'      => ['require',],
    ];

    protected $message = [
        'id.require'               => 'id不能为空',
        'guid.require'             => 'guid不能为空',
        'bid.require'              => 'bid不能为空',
        'name.require'             => 'name不能为空',
        'coupon_item_guid.require' => '关联卡券不能为空',
        'create_time.require'      => 'create_time不能为空',
        'update_time.require'      => 'update_time不能为空',
    ];

    protected $scene = [
        'add'  => ['name', 'coupon_item_guid'],
        'edit' => ['name', 'coupon_item_guid'],
        'del'  => ['guid'],
    ];
}