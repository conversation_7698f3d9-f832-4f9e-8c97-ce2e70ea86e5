<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class WechatConfig extends ValidateBase
{

    protected $rule = [
        'appid'          => ['require'],
        'sensitive_type' => ['require', 'in:0,1'],
        'is_permanent'   => ['require', 'in:0,1'],
        'expire_time'    => ['requireIf:is_permanent,0'],
        'certicates_key' => ['requireIf:sensitive_type,1'],
        'certicates_val' => ['requireIf:sensitive_type,1'],
        'first_id'       => ['require'],
        'second_id'      => ['require'],
    ];

    protected $message = [

    ];

    protected $scene = [
        'add_categories'  => ['appid', 'sensitive_type', 'first_id', 'second_id', 'is_permanent', 'certicates_key', 'certicates_val'],
        'categories_list' => ['appid'],
    ];
}
