<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyConsumeFreeOfCharge extends ValidateBase
{
    protected $rule = [
        'id'                => ['require'],
        'guid'              => ['require',],
        'bid'               => ['require',],
        'consume_note_guid' => ['require',],
        'yky_member_guid'   => ['require',],
        'card_id'           => ['require',],
        'bill_number'       => ['require',],
        'total_paid'        => ['require',],
        'status'            => ['require',],
        'consume_times'     => ['require',],
        'coupon_send_guid'  => ['require',],
        'create_time'       => ['require',],
        'update_time'       => ['require',],
    ];

    protected $message = [
        'id.require'                => 'id不能为空',
        'guid.require'              => 'guid不能为空',
        'bid.require'               => 'bid不能为空',
        'consume_note_guid.require' => 'consume_note_guid不能为空',
        'yky_member_guid.require'   => 'yky_member_guid不能为空',
        'card_id.require'           => 'card_id不能为空',
        'bill_number.require'       => 'bill_number不能为空',
        'total_paid.require'        => 'total_paid不能为空',
        'status.require'            => 'status不能为空',
        'consume_times.require'     => 'consume_times不能为空',
        'coupon_send_guid.require'  => 'coupon_send_guid不能为空',
        'create_time.require'       => 'create_time不能为空',
        'update_time.require'       => 'update_time不能为空',
    ];

    protected $scene = [

    ];
}