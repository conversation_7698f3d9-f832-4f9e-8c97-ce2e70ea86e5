<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class ExpressAuth extends ValidateBase
{
    protected $rule = [
        'guid'               => ['require', 'guid'],
        'express_channel_id' => ['require', 'number'],
    ];

    protected $message = [
        'guid.require' => '请传入guid参数',
    ];

    protected $scene = [
        'update' => ['guid', 'express_channel_id'],
    ];
}