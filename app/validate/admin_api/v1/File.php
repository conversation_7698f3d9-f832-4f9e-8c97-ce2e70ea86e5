<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class File extends ValidateBase
{
    protected $rule = [
        'editor|base64_image_content' => ['require'],
        'xls'                         => ['require', 'file', 'max:4194304', 'fileExt:xls,xlsx'],
        'img'                         => ['require', 'max:4194304', 'fileExt:jpg,jpeg,png,gif', 'image'],
    ];
    protected $message = [
        'xls.require' => '请选择文件2',
        'xls.file'    => '请上传文件',
        'xls.max'     => '请上传4M以内的文件',
        'xls.fileExt' => '请上传xls或xlsx格式的文件',
        'img.max'     => '请上传4M以内的图片',
        'img.fileExt' => '请上传jpg,jpeg,png,gif格式的文件',
    ];
    protected $scene = [
        'xls'           => ['xls'],
        'img'           => ['img'],
        'upload_base64' => ['editor']
    ];
}