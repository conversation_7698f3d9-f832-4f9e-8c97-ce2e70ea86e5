<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CropWechatConfig extends ValidateBase
{
    protected $rule = [
        'id'             => ['require',],
        'name'           => ['require',],
        'agentid'        => ['require',],
        'appid'          => ['require',],
        'appsecret'      => ['require',],
        'token'          => ['require',],
        'encodingaeskey' => ['require',],
        'create_time'    => ['require',],
        'update_time'    => ['require',],
    ];

    protected $message = [
        'id.require'             => ['id不能为空',],
        'name.require'           => ['name不能为空',],
        'agentid.require'        => ['agentid不能为空',],
        'appid.require'          => ['appid不能为空',],
        'appsecret.require'      => ['appsecret不能为空',],
        'token.require'          => ['token不能为空',],
        'encodingaeskey.require' => ['encodingaeskey不能为空',],
        'create_time.require'    => ['create_time不能为空',],
        'update_time.require'    => ['update_time不能为空',],
    ];

    protected $scene = [

    ];
}