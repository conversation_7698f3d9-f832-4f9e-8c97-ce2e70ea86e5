<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyCouponSendRuleItem extends ValidateBase
{
    protected $rule = [
        'bid'         => ['require'],
        'guid'        => ['require'],
        'num'         => ['integer'],
        'rule_guid'   => ['require'],
        'coupon_guid' => ['require'],
    ];

    protected $message = [
        'title.require' => '名称必须填写',
    ];

    protected $scene = [
        'add' => ['bid', 'rule_guid', 'coupon_guid'],
        'del' => ['bid', 'rule_guid', 'guid'],
    ];
}