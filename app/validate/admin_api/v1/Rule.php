<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;


class Rule extends ValidateBase
{

    protected $rule = [
        //'name'        => ['require', 'length:1,50'],
        //'name'        => ['require', 'unique:rule,name', 'length:1,50'], //编辑的时候不能有重复的判断
        'title'       => ['length:2,25'],
        'parent_guid' => ['require'],
        'type'        => ['in:0,1,2'],
        'auto_log'    => ['in:0,1'],
        'status'      => ['in:0,1'],
        'sort'        => ['number', 'between:0,1000'],
        'icon'        => ['requireIf:type,1'],
    ];

    protected $message = [
        'guid'                 => '权限&菜单不存在！',
        'name.require'         => '权限&菜单必须填写',
        'name.unique'          => '权限&菜单已存在',
        'name.length'          => '权限&菜单必须大于3个字符小于25个字符',
        'title.require'        => '权限菜单名称必须填写',
        'title.length'         => '权限菜单名称必须大于3个字符小于25个字符',
        'parent_guid.require'  => '上级菜单必须填写',
        'parent_guid.existPid' => '上级菜单选值不正确',
        'type.in'              => '是否菜单选值不正确',
        'sort.number'          => '排序只能是一个数字',
        'sort.between'         => '排序范围值只能在0-255之间',
        'icon.requireIf'       => '菜单必须添加图标文件',
    ];

    protected $scene = [
        'add'  => ['title', 'parent_guid', 'type', 'sort'],
        'edit' => ['guid', 'auto_log'],
    ];

}
