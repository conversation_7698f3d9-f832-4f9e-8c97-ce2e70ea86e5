<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class SystemConfig extends ValidateBase
{
    protected $rule = [
        'guid'     => ['require'],
        'value'    => ['require'],
        'key_name' => ['require', 'unique:system_config,key_name'],
    ];

    protected $message = [
        'title.require' => '名称必须填写',
    ];

    protected $scene = [
        'add'  => ['name', 'key_name', 'value'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}