<?php
declare(strict_types=1);

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponRecommendRule extends ValidateBase
{
    protected $rule = [
        'guid|唯一标识'              => ['require'],                    // 编辑/删除时必需
        'title|规则标题'             => ['require', 'max:100'],        // 必需字段
        'used_coupon_guid|使用券'    => ['require', 'guid'],           // 必需，GUID格式
        'reward_coupon_guid|奖励券'  => ['require', 'guid'],           // 必需，GUID格式  
        'status|状态'               => ['integer', 'in:0,1'],          // 可选，0或1
        'type|规则类型'             => ['require', 'integer', 'in:1'], // 必需，目前只支持1
    ];

    protected $scene = [
        'add'  => ['title', 'used_coupon_guid', 'reward_coupon_guid', 'type'],
        'edit' => ['guid', 'status'],
        'del'  => ['guid'],
    ];
}
