<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class ShortUrlParams extends ValidateBase
{
    protected $rule = [
        'guid'  => ['require'],
        'title' => ['require'],
        'type'  => ['require', 'number', 'in:1'],
    ];

    protected $message = [
        'title.require' => '标题必须填写',
    ];
    protected $scene = [
        'add'    => ['title', 'type'],
        'edit'   => ['guid'],
        'detail' => ['guid'],
        'del'    => ['guid'],
    ];
}