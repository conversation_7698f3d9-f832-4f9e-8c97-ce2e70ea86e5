<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Pay extends ValidateBase
{

    protected $rule = [
        'store_id'     => ['require'],
        'store_name'   => ['require'],
        'device_id'    => ['require'],
        'rawdata'      => ['require'],
        'face_code'    => ['require'],
        'auth_code'    => ['require', 'number'],
        //       'auth_code'    => ['require', 'number', 'regex:^1[012345]\d{16}$'],
        'openid'       => ['require'],
        'ip'           => ['require', 'ip'],
        'total_fee'    => ['require', 'number', 'between:1,5000000'],
        'out_trade_no' => ['require', 'number', 'length:20'],
    ];

    protected $message = [
        'store_id.require'   => 'store_id必须填写',
        'store_name.require' => 'store_name必须填写',
        'device_id.require'  => 'device_id必须填写',
        'rawdata.require'    => 'rawdata必须填写',
        'auth_code.number'   => '付款码必须为数字',
        'total_fee.between'  => '支付金额有误',
        //'auth_code.regex'    => '付款码格式有误',
    ];

    protected $scene = [
        'get_wxpayface_authinfo' => ['store_id', 'store_name', 'device_id', 'rawdata'],
        'facepay'                => ['face_code', 'openid', 'total_fee', 'out_trade_no'],
        'micropay'               => ['auth_code', 'total_fee', 'out_trade_no'],
        'query'                  => ['out_trade_no'],
    ];
}