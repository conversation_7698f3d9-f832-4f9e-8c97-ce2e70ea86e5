<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Weopen extends ValidateBase
{

    protected $rule = [
        'appid'                    => ['require'],
        'template_id'              => ['require'],
        'draft_id'                 => ['require'],
        'source_miniprogram_appid' => ['require'],
    ];

    protected $message = [
        'template_id.require' => '请选择模板',
    ];

    protected $scene = [
        'func_info'       => ['appid'],
        'del_template'    => ['template_id'],
        'add_to_template' => ['draft_id', 'source_miniprogram_appid'],
    ];
}
