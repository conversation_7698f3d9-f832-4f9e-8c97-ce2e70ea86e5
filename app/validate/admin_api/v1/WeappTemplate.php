<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class WeappTemplate extends ValidateBase
{

    protected $rule = [
        'guid'        => ['require'],
        'template_id' => ['require', 'number'],
        'name'        => ['require'],
    ];

    protected $message = [
        'template_id.require' => '模板ID必须填写',
        'name.require'        => '名称必须填写',
    ];

    protected $scene = [
        'add'  => ['name', 'template_id'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}