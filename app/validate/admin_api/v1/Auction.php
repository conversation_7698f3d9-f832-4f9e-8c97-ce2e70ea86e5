<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;

class Auction extends ValidateBase
{

    protected $rule = [
        'file'                => ['require'],
        'goods_category_guid' => ['require'],
        'begin_time'          => ['require'],
        'end_time'            => ['require'],
    ];

    protected $message = [
        'file.require'                => '请上传文件',
        'goods_category_guid.require' => '请选择海关',
        'begin_time.require'          => '开始时间必须填写',
        'end_time.require'            => '结束时间必须填写',
    ];

    protected $scene = [
        'import' => ['file', 'begin_time', 'end_time', 'goods_category_guid'],
    ];
}
