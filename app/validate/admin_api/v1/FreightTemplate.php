<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class FreightTemplate extends ValidateBase
{

    protected $rule = [
        'start_standard|首N件' => ['require', 'number', '>=:1'],
        'add_standard|续N件'   => ['require', 'number', '>=:1'],
        'guid'                 => ['require'],
    ];

    protected $message = [
        'start_standard.require' => '首N件必填',
        'add_standard.require'   => '续N件必填',
    ];

    protected $scene = [
        'add'  => ['start_standard', 'add_standard'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}