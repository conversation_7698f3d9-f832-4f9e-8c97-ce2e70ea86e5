<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyConsumeRewardValueRule extends ValidateBase
{
    protected $rule = [
        'title'       => ['require'],
        'guid'        => ['require'],
        'status'      => ['integer', 'in:0,1'],
        'min_value'   => ['require', 'float', '>:0'],
        'reward_rate' => ['require', 'float', '>:0', '<:1'],
        //       'max_value'   => ['require', 'float'],
    ];

    protected $message = [
        'title.require'  => '名称必须填写',
        'reward_rate.gt' => '返还比例需要大于0',
        'reward_rate.lt' => '返还比例需要小于1',
    ];

    protected $scene = [
        'add'  => ['name', 'status', 'min_value', 'reward_rate'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}