<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyCouponSendRule extends ValidateBase
{
    protected $rule = [
        'title'     => ['require'],
        'guid'      => ['require'],
        'status'    => ['integer'],
        //       'coupon_guid' => ['require'],
        'min_value' => ['require', 'float'],
        'max_value' => ['require', 'float'],
    ];

    protected $message = [
        'title.require' => '名称必须填写',
    ];

    protected $scene = [
        'add'  => ['name'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}