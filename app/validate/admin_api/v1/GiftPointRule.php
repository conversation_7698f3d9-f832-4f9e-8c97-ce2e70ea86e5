<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class GiftPointRule extends ValidateBase
{
    protected $rule = [
        'id'               => [
            'require',
        ],
        'guid'             => [
            'require',
        ],
        'bid'              => [
            'require',
        ],
        'gift_point'       => [
            'require',
        ],
        'reach_point'      => [
            'require',
        ],
        'create_user_guid' => [
            'require',
        ],
        'name'             => [
            'require',
        ],
        'status'           => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'               => 'id不能为空',
        'guid.require'             => 'guid不能为空',
        'bid.require'              => 'bid不能为空',
        'gift_point.require'       => 'gift_point不能为空',
        'reach_point.require'      => 'reach_point不能为空',
        'create_user_guid.require' => 'create_user_guid不能为空',
        'name.require'             => 'name不能为空',
        'status.require'           => 'status不能为空',
    ];

    protected $scene = [

    ];
}