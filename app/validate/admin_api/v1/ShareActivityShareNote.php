<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class ShareActivityShareNote extends ValidateBase
{
    protected $rule = [
        'id'                    => [
            'require',
        ],
        'guid'                  => [
            'require',
        ],
        'bid'                   => [
            'require',
        ],
        'share_activity_guid'   => [
            'require',
        ],
        'share_yky_member_guid' => [
            'require',
        ],
        'share_member_guid'     => [
            'require',
        ],
        'share_openid'          => [
            'require',
        ],
        'share_time'            => [
            'require',
        ],
        'yky_member_guid'       => [
            'require',
        ],
        'member_guid'           => [
            'require',
        ],
        'openid'                => [
            'require',
        ],
        'last_visit_time'       => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                    => 'id不能为空',
        'guid.require'                  => 'guid不能为空',
        'bid.require'                   => 'bid不能为空',
        'share_activity_guid.require'   => 'share_activity_guid不能为空',
        'share_yky_member_guid.require' => 'share_yky_member_guid不能为空',
        'share_member_guid.require'     => 'share_member_guid不能为空',
        'share_openid.require'          => 'share_openid不能为空',
        'share_time.require'            => 'share_time不能为空',
        'yky_member_guid.require'       => 'yky_member_guid不能为空',
        'member_guid.require'           => 'member_guid不能为空',
        'openid.require'                => 'openid不能为空',
        'last_visit_time.require'       => 'last_visit_time不能为空',
    ];

    protected $scene = [

    ];
}