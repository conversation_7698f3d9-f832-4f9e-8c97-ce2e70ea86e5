<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class UserMoneyCashNote extends ValidateBase
{
    protected $rule = [
        'id'                   => ['require',],
        'guid'                 => ['require',],
        'bid'                  => ['require',],
        'bill_number'          => ['require',],
        'examine_time'         => ['require',],
        'relation_bill_number' => ['require',],
        'relation_guid'        => ['require',],
        'user_guid'            => ['require',],
        'status'               => ['require',],
        'operator_user_guid'   => ['require',],
        'memo'                 => ['require',],

        'cash_money|提现金额'             => ['require', 'float', '>:0'],
        'pay_type|提现方式'               => ['require', 'in:wechat,alipay,bank'],
        'wechat_true_name|微信姓名'       => ['requireIf:pay_type,wechat', 'chs'],
        'wechat_account|微信号'           => ['requireIf:pay_type,wechat'],
        'alipay_true_name|支付宝姓名'     => ['requireIf:pay_type,alipay', 'chs'],
        'alipay_account|支付宝账号'       => ['requireIf:pay_type,alipay'],
        'bank_true_name|银行卡开户人姓名' => ['requireIf:pay_type,bank', 'chs'],
        'bank_name|银行名称'              => ['requireIf:pay_type,bank', 'chs'],
        'bank_account|银行卡号'           => ['requireIf:pay_type,bank'],
    ];

    protected $message = [
        'id.require'                   => 'id不能为空',
        'guid.require'                 => 'guid不能为空',
        'bid.require'                  => 'bid不能为空',
        'bill_number.require'          => 'bill_number不能为空',
        'examine_time.require'         => 'examine_time不能为空',
        'relation_bill_number.require' => 'relation_bill_number不能为空',
        'relation_guid.require'        => 'relation_guid不能为空',
        'user_guid.require'            => 'user_guid不能为空',
        'status.require'               => 'status不能为空',
        'operator_user_guid.require'   => 'operator_user_guid不能为空',
        'memo.require'                 => 'memo不能为空',
        'cash_money.require'           => '请输入提现金额',
        'cash_money.gt'                => '提现金额需要大于0',
        'pay_type.require'             => '请选择提现方式',
    ];

    protected $scene = [
        'apply' => ['cash_money', 'pay_type', 'wechat_true_name', 'wechat_account', 'alipay_true_name', 'alipay_account', 'bank_true_name', 'bank_name', 'bank_account'],
    ];
}