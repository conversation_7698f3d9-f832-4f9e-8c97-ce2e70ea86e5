<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class User extends ValidateBase
{

    protected $rule = [
        'name'                      => ['require', 'length:2,25'],
        'email'                     => ['require', 'unique:user,email', 'email'],
        'business_account'          => ['require'],
        'user_account'              => ['require', 'length:5', 'number'],
        'account'                   => ['require', 'length:1,11', 'number'],
        'profile_head'              => ['exist:uploaded_file,guid'],
        'old_login_password|旧密码' => ['requireWith:login_password'],
        'login_password|登录密码'   => ['requireWith:repassword', 'confirm:repassword', 'safePassword'],
        'repassword|重复密码'       => ['requireWith:login_password', 'confirm:login_password', 'safePassword'],
        'sex'                       => ['in:0,1,2'],
        'birthday'                  => ['dateFormat:Y-m-d'],
        'role_guid'                 => ['exist:role,guid'],
        'status'                    => ['in:0,1'],
        'tel'                       => ['mobile'],
        'guid'                      => ['require', 'guid'],
    ];

    protected $message = [
        'tel.mobile'               => '手机号格式不正确',
        'name.require'             => '用户名必须填写',
        'name.unique'              => '用户名已存在',
        'name.length'              => '用户名必须大于3个字符小于25个字符',
        'account.require'          => '工号必须填写',
        'account.unique'           => '工号已存在',
        'account.length'           => '工号必须是5个字符的数字',
        'account.number'           => '工号必须是5个字符的数字哦',
        'login_password.length'    => '密码必须为6-20位字母加数字!',
        'login_password.alphaNum'  => '密码必须为6-20位字母加数字',
        'business_account.require' => '账号必须填写',
        'user_account.require'     => '工号必须填写',
        'user_account.length'      => '工号必须是5个字符的数字',
        'user_account.number'      => '工号必须是5个字符的数字哦',
        'email.require'            => '邮箱地址必须填写',
        'email.unique'             => '邮箱地址已存在',
        'email.email'              => '邮箱地址格式不正确',
        'role_guid.require'        => '所属角色必须填写',
        'role_guid.exist'          => '所属角色不存在',
        'status.in'                => '状态值不可用',
        'profile_head.exist'       => '头像图片不存在',
    ];
    protected $scene = [
        'add'           => ['name', 'account', 'login_password', 'repassword', 'role_guid', 'tel'],
        'profile'       => ['name', 'old_login_password', 'login_password', 'repassword', 'tel'],
        'edit'          => ['name', 'login_password', 'repassword'],
        'login'         => ['business_account', 'user_account', 'login_password'],
        'destroy'       => ['guid'],
        'lock'          => ['guid'],
        'unlock'        => ['guid'],
        'usb_key_login' => ['usb_key_id', 'usb_key_token', 'random', 'encrypt_data'],
    ];
}