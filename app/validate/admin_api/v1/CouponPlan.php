<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponPlan extends ValidateBase
{
    protected $rule = [
        'id'                 => ['require',],
        'guid'               => ['require',],
        'name'               => ['require',],
        'bid'                => ['require',],
        'status'             => ['require',],
        'member_guid'        => ['require',],
        'delete_time'        => ['require',],
        'create_time'        => ['require',],
        'update_time'        => ['require',],
        'simple_description' => ['require',],
        'goods_num'          => ['require',],
    ];

    protected $message = [
        'id.require'                 => 'id不能为空',
        'guid.require'               => 'guid不能为空',
        'name.require'               => 'name不能为空',
        'bid.require'                => 'bid不能为空',
        'status.require'             => 'status不能为空',
        'member_guid.require'        => 'member_guid不能为空',
        'delete_time.require'        => 'delete_time不能为空',
        'create_time.require'        => 'create_time不能为空',
        'update_time.require'        => 'update_time不能为空',
        'simple_description.require' => 'simple_description不能为空',
        'goods_num.require'          => 'goods_num不能为空',
    ];

    protected $scene = [

    ];
}