<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;

class WechatMerchantApplyment extends ValidateBase
{
    protected $rule = [
        'bid'          => ['require', 'length:36'],
        'applyment_id' => ['require', 'length:16', 'number'],
    ];

    protected $message = [
        'applyment_id.length' => '请输入16位申请单编号~',
    ];

    protected $scene = [
        'add' => ['bid', 'applyment_id'],
    ];
}
