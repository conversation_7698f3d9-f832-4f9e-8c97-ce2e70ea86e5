<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;


class AreaRule extends ValidateBase
{

    protected $rule = [
        'name'              => ['require'],
        'guid'              => ['require'],
        'limit_coupon_type' => ['in:0,1'],
        'coupon_item_guid'  => ['requireIf:limit_coupon_type,1'],
    ];

    protected $message = [
        //        'guid'                       => '权限&菜单不存在！',
        //        'name.require'               => '权限&菜单必须填写',
        //        'name.unique'                => '权限&菜单已存在',
        //        'name.length'                => '权限&菜单必须大于3个字符小于25个字符',
        //        'title.require'              => '权限菜单名称必须填写',
        //        'title.length'               => '权限菜单名称必须大于3个字符小于25个字符',
        //        'parent_guid.require'        => '上级菜单必须填写',
        //        'parent_guid.existPid'       => '上级菜单选值不正确',
        //        'type.in'                    => '是否菜单选值不正确',
        //        'sort.number'                => '排序只能是一个数字',
        //        'sort.between'               => '排序范围值只能在0-255之间',
        'coupon_item_guid.requireIf' => '请选择卡券',
    ];

    protected $scene = [
        'add'  => ['name', 'limit_coupon_type', 'coupon_item_guid'],
        'edit' => ['guid'],
        //        'edit' => ['guid', 'name', 'limit_coupon_type', 'coupon_item_guid'],
    ];

}
