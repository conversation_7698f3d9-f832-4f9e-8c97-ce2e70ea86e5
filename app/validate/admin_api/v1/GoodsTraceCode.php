<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class GoodsTraceCode extends ValidateBase
{
    protected $rule = [
        'id'         => ['require'],
        'guid'       => ['require'],
        'bid'        => ['require'],
        'code'       => ['require'],
        'goods_guid' => ['require'],
    ];

    protected $message = [
        'id.require'         => 'id不能为空',
        'guid.require'       => 'guid不能为空',
        'bid.require'        => 'bid不能为空',
        'code.require'       => 'code不能为空',
        'goods_guid.require' => 'goods_guid不能为空',
    ];
    protected $scene = [
        'get_qrcode' => ['code']
    ];
}