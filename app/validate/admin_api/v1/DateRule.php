<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class DateRule extends ValidateBase
{
    protected $rule = [
        //'name'        => ['require', 'length:1,50'],
        'guid'                     => ['require'],
        'status'                   => ['require'],
        'name'                     => ['require'],
        'limit_coupon_type'        => ['in:0,1'],
        'rule_type'                => ['in:1,2,3,4,5'],
        'coupon_item_guid'         => ['requireIf:limit_coupon_type,1'],
        'start_date'               => ['requireIf:rule_type,1'],
        'end_date'                 => ['requireIf:rule_type,1'],
        'date'                     => ['requireIf:rule_type,2'],
        'weekday'                  => ['requireIf:rule_type,3'],
        'start_month_day|开始月日' => ['requireIf:rule_type,4'],
        'end_month_day|截止月日'   => ['requireIf:rule_type,4'],
        'month_days'               => ['requireIf:rule_type,5'],
    ];

    protected $message = [
        'coupon_item_guid.requireIf' => '请选择卡券',
        'start_date.requireIf'       => '请选择开始日期',
        'end_date.requireIf'         => '请选择结束日期',
        'date.requireIf'             => '请选择日期',
        'weekday.requireIf'          => '请选择星期',
        'month_days.requireIf'       => '请选择每月几日',
    ];

    protected $scene = [
        'add'           => ['name', 'limit_coupon_type', 'rule_type', 'coupon_item_guid', 'start_date', 'end_date', 'date', 'weekday', 'start_month_day', 'end_month_day', 'month_days'],
        'edit'          => ['guid', 'name', 'limit_coupon_type', 'rule_type', 'coupon_item_guid', 'start_date', 'end_date', 'date', 'weekday', 'start_month_day', 'end_month_day', 'month_days'],
        'change_status' => ['guid', 'status'],
    ];
}
