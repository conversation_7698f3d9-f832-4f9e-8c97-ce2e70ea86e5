<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponBookShare extends ValidateBase
{
    protected $rule = [
        'id'               => [
            'require',
        ],
        'guid'             => [
            'require',
        ],
        'bid'              => [
            'require',
        ],
        'coupon_book_guid' => [
            'require',
        ],
        'member_guid'      => [
            'require',
        ],
        'true_name'        => [
            'require',
        ],
        'mobile'           => [
            'require',
        ],
        'company'          => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'               => 'id不能为空',
        'guid.require'             => 'guid不能为空',
        'bid.require'              => 'bid不能为空',
        'coupon_book_guid.require' => 'coupon_book_guid不能为空',
        'member_guid.require'      => 'member_guid不能为空',
        'true_name.require'        => 'true_name不能为空',
        'mobile.require'           => 'mobile不能为空',
        'company.require'          => 'company不能为空',
    ];

    protected $scene = [

    ];
}