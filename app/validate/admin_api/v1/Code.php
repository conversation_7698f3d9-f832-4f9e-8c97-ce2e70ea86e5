<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Code extends ValidateBase
{
    protected $rule = [
        'guid'               => ['require'],
        'goods_item_guid'    => ['require'],
        'generate_guid'      => ['require'],
        'exchange_goods_num' => ['require', 'number', '>=:1'],
        'coupon_guid'        => ['require'],
        'coupon_send_guid'   => ['require'],
        'code'               => ['require'],
        'password'           => ['require'],
        'file'               => ['require'],
        'coupon_item_guid'   => ['require'],
        'mobile'             => ['require', 'mobile'],
        'amount|生成数量'    => ['require', 'number', '<=:3000', '>:0'],
        'send_amount'        => ['require', 'number', '<=:3000'],
        'send_num'           => ['require', 'number', '<=:3000'],
        'user_id'            => ['require', 'number'],
        'name'               => ['require', 'length:1,50'],
        'send_type'          => ['require', 'in:0,1'],
        'apply_type'         => ['require', 'in:0,1'],
        'coupon_type'        => ['require', 'in:1,2'],
        'type'               => ['require', 'in:1,2,3,4'],
        'description'        => ['max:100000'],
        'value'              => ['require', 'float', 'in:50,100,200,500,1000'],
        'coupon_value'       => ['require', 'float', 'in:50,100,200,500,1000'],
        'expire_time'        => ['date'],
        'availability_time'  => ['date'],
    ];

    protected $message = [
        'guid.require'             => '请选择卡密',
        'coupon_item_guid.require' => '请选择卡券',
        'goods_item_guid.require'  => '请选择关联产品',
        'name.require'             => '请输入名称',
        'name.length'              => '名称过长',
        'file.require'             => '请上传文件',
        'coupon_type.in'           => '请选择类型',
        'mobile.require'           => '请输入手机号',
        'amount.require'           => '请输入数量',
        'coupon_guid.require'      => '请选择卡券',
        //       'amount.elt'              => '单次最多可生成5000张',
        'amount.gt'                => '单次最少生成1张',
        'send_amount.elt'          => '单次最多发送50张',
    ];

    protected $scene = [
        'add'                      => ['name', 'type'],
        'edit'                     => ['guid'],
        'del'                      => ['guid'],
        'generate'                 => ['amount', 'coupon_guid', 'owner_user_id', 'send_num', 'expire_time', 'availability_time'],
        'import'                   => ['coupon_guid', 'file'],
        'send'                     => ['coupon_guid'],
        'batch_send'               => ['coupon_guid', 'file'],
        'batch_send_excel'         => ['send_amount', 'mobile'],
        'single_send'              => ['coupon_guid', 'send_amount', 'mobile'],
        'pass'                     => ['generate_guid'],
        'examine'                  => ['apply_guid'],
        'apply'                    => ['amount', 'guid'],
        'revoke'                   => ['coupon_send_guid'],
        'apply_coupon'             => ['apply_type'],
        'batch_apply_coupon'       => ['file', 'coupon_type'],
        'batch_apply_coupon_excel' => ['amount', 'value', 'mobile'],
        'single_apply_coupon'      => ['amount', 'coupon_guid'],
        'get_code_info'            => ['code'],
        'report'                   => [''],
        //       'verify_code'              => ['code', 'password'],
    ];
}
