<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;


class Queue extends ValidateBase
{
    protected $rule = [
        'id|任务ID'          => ['require', 'number'],
        'job_id|任务编号'    => ['require', 'number'],
        'job_name|任务名'    => ['require'],
        'queue|队列名'       => ['require'],
        'action|操作'        => ['require', 'in:restart,pause,del'],
        'action_name|操作名' => ['require'],
    ];
    protected $scene = [
        'single' => ['id', 'action', 'action_name'],
        'batch'  => ['job_name', 'queue', 'action', 'action_name'],
        'do_job' => ['job_id']
    ];
}
