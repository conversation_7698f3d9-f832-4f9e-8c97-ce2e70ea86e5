<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Goods extends ValidateBase
{
    protected $rule = [
        'name'  => ['require'],
        'file'  => ['require'],
        'stock' => ['require', 'number'],
        'type'  => ['require', 'in:1,2,3'],
    ];

    protected $message = [
        'name.require' => '名称必须填写',
        'stock.number' => '库存请输入数字',
    ];
    protected $scene = [
        'add'          => ['name'],
        'import'       => ['file'],
        'change_stock' => ['type', 'stock'],
    ];
}