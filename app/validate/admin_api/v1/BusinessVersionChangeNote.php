<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class BusinessVersionChangeNote extends ValidateBase
{
    protected $rule = [
        'id'                  => [
            'require',
        ],
        'guid'                => [
            'require',
        ],
        'bid'                 => [
            'require',
        ],
        'operator_bid'        => [
            'require',
        ],
        'before_version_guid' => [
            'require',
        ],
        'after_version_guid'  => [
            'require',
        ],
        'memo'                => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                  => 'id不能为空',
        'guid.require'                => 'guid不能为空',
        'bid.require'                 => 'bid不能为空',
        'operator_bid.require'        => 'operator_bid不能为空',
        'before_version_guid.require' => 'before_version_guid不能为空',
        'after_version_guid.require'  => 'after_version_guid不能为空',
        'memo.require'                => 'memo不能为空',
    ];

    protected $scene = [

    ];
}