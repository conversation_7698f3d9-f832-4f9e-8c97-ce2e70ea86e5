<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class BusinessParameterVisible extends ValidateBase
{
    protected $rule = [
        'id'       => [
            'require',
        ],
        'guid'     => [
            'require',
        ],
        'bid'      => [
            'require',
        ],
        'key_name' => [
            'require',
        ],
        'status'   => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'       => 'id不能为空',
        'guid.require'     => 'guid不能为空',
        'bid.require'      => 'bid不能为空',
        'key_name.require' => 'key_name不能为空',
        'status.require'   => 'status不能为空',
    ];

    protected $scene = [

    ];
}