<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class WechatFuncInfo extends ValidateBase
{
    protected $rule = [
        'id'          => [
            'require',
        ],
        'type'        => [
            'require',
        ],
        'func_id'     => [
            'require',
        ],
        'name'        => [
            'require',
        ],
        'description' => [
            'require',
        ],
        'mutual'      => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'          => 'id不能为空',
        'type.require'        => 'type不能为空',
        'func_id.require'     => 'func_id不能为空',
        'name.require'        => 'name不能为空',
        'description.require' => 'description不能为空',
        'mutual.require'      => 'mutual不能为空',
    ];

    protected $scene = [

    ];
}