<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;

class Business extends ValidateBase
{
    protected $rule = [
        'account|商家账号'          => ['require', 'length:2,20', 'unique:business', 'alphaNum'],
        'business_account|商家账号' => ['require', 'length:2,20', 'alphaNum'],
        'version_guid'              => ['require', 'guid'],
        'guid'                      => ['require', 'guid'],
        'parent_guid'               => ['require'],
        'email'                     => ['email'],
        'login_password|登录密码'   => ['requireWith:repassword', 'confirm:repassword', 'safe_password'],
        'repassword|重复密码'       => ['requireWith:login_password', 'confirm:login_password', 'safe_password'],
        'mobile'                    => ['require', 'mobile'],
        'status'                    => ['in:0,1'],
        'grant_type'                => ['require', 'in:code,password,client_credential'],
        'recharge_type'             => ['in:1,2,3,4,5'],
        'renew_type'                => ['in:1,2,3,4,5,6'],
        'renew_days'                => ['requireIf:renew_type,5'],
        'after_expired_time'        => ['requireIf:renew_type,6'],
        'amount'                    => ['require'],

        'company'   => ['require', 'chsAlphaNum'],
        'true_name' => ['require', 'chsAlphaNum'],
        'token'     => ['require'], //包含appid+openid的加密数据
    ];

    protected $message = [
        'account.length'          => '请输入2-20位账号~',
        'business_account.length' => '请输入2-20位账号!',
        'name.require'            => '用户名必须填写',
        'version_guid.require'    => '请选择版本',
        'name.unique'             => '用户名已存在',
        'name.length'             => '用户名必须大于3个字符小于25个字符',
        'email.require'           => '邮箱地址必须填写',
        'account.unique'          => '账号已存在',
        'email.email'             => '邮箱地址格式不正确',
        'status.in'               => '状态值不可用',

        'mobile.require'         => '请输入手机号',
        'mobile.mobile'          => '请输入正确的手机号',
        'login_password.confirm' => '登录密码和重复密码不一致',
        'repassword.confirm'     => '登录密码和重复密码不一致!',

        'company.chsAlphaNum'   => '商户名称仅支持中文、字母、数字',
        'true_name.chsAlphaNum' => '姓名仅支持中文、字母、数字',

    ];

    protected $scene = [
        'add'          => ['account', 'status', 'mobile'],
        'edit_profile' => ['login_password', 'repassword', 'status', 'sex', 'birthday', 'profile_head'],
        'edit'         => ['email', 'guid', 'login_password', 'repassword'],
        'login'        => ['business_account', 'user_account', 'login_password'],
        'recharge'     => ['recharge_type', 'amount'],
        'renew'        => ['renew_type', 'days', 'after_expired_time'],
        'get_token'    => ['grant_type'],
        'change'       => ['guid', ''],
        'register'     => ['account', 'token', 'company', 'true_name', 'mobile', 'password', 'version_guid', 'parent_guid', 'parent_user_guid'],
    ];
}
