<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class SinglePage extends ValidateBase
{
    protected $rule = [
        'id'      => [
            'require',
        ],
        'guid'    => [
            'require',
        ],
        'title'   => [
            'require',
        ],
        'content' => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'      => 'id不能为空',
        'guid.require'    => 'guid不能为空',
        'title.require'   => 'title不能为空',
        'content.require' => 'content不能为空',
    ];

    protected $scene = [
        'add'  => ['name', 'title', 'content'],
        'edit' => ['guid'],
        'del'  => ['guid'],
    ];
}