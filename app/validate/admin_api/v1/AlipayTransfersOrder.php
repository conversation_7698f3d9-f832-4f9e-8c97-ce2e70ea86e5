<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class AlipayTransfersOrder extends ValidateBase
{
    protected $rule = [
        'id'                   => [
            'require',
        ],
        'guid'                 => [
            'require',
        ],
        'bid'                  => [
            'require',
        ],
        'way'                  => [
            'require',
        ],
        'relation_bill_number' => [
            'require',
        ],
        'relation_guid'        => [
            'require',
        ],
        'member_guid'          => [
            'require',
        ],
        'user_guid'            => [
            'require',
        ],
        'out_biz_no'           => [
            'require',
        ],
        'payee_type'           => [
            'require',
        ],
        'payee_account'        => [
            'require',
        ],
        'amount'               => [
            'require',
        ],
        'payer_show_name'      => [
            'require',
        ],
        'payee_real_name'      => [
            'require',
        ],
        'remark'               => [
            'require',
        ],
        'status'               => [
            'require',
        ],
        'memo'                 => [
            'require',
        ],
        'message'              => [
            'require',
        ],
        'order_id'             => [
            'require',
        ],
        'pay_date'             => [
            'require',
        ],
        'unique_code'          => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                   => 'id不能为空',
        'guid.require'                 => 'guid不能为空',
        'bid.require'                  => 'bid不能为空',
        'way.require'                  => 'way不能为空',
        'relation_bill_number.require' => 'relation_bill_number不能为空',
        'relation_guid.require'        => 'relation_guid不能为空',
        'member_guid.require'          => 'member_guid不能为空',
        'user_guid.require'            => 'user_guid不能为空',
        'out_biz_no.require'           => 'out_biz_no不能为空',
        'payee_type.require'           => 'payee_type不能为空',
        'payee_account.require'        => 'payee_account不能为空',
        'amount.require'               => 'amount不能为空',
        'payer_show_name.require'      => 'payer_show_name不能为空',
        'payee_real_name.require'      => 'payee_real_name不能为空',
        'remark.require'               => 'remark不能为空',
        'status.require'               => 'status不能为空',
        'memo.require'                 => 'memo不能为空',
        'message.require'              => 'message不能为空',
        'order_id.require'             => 'order_id不能为空',
        'pay_date.require'             => 'pay_date不能为空',
        'unique_code.require'          => 'unique_code不能为空',
    ];

    protected $scene = [

    ];
}