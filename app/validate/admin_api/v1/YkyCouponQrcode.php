<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyCouponQrcode extends ValidateBase
{
    protected $rule = [
        'id'              => [
            'require',
        ],
        'guid'            => [
            'require',
        ],
        'bid'             => [
            'require',
        ],
        'name'            => [
            'require',
        ],
        'yky_coupon_guid' => [
            'require',
        ],
        'status'          => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'              => 'id不能为空',
        'guid.require'            => 'guid不能为空',
        'bid.require'             => 'bid不能为空',
        'name.require'            => 'name不能为空',
        'yky_coupon_guid.require' => 'yky_coupon_guid不能为空',
        'status.require'          => 'status不能为空',
    ];

    protected $scene = [

    ];
}