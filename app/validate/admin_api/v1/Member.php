<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;


class Member extends ValidateBase
{
    protected $rule = [
        'guid|唯一标识'                  => ['require'],
        'type|充值类型'                  => ['require', 'in:-1,1'],
        'member_guid|会员标识'           => ['require'],
        'content|短信内容'               => ['require'],
        'money|金额'                     => ['require', 'number'],
        'mobile|手机号'                  => ['mobile'],
        'brokerage_ratio|分佣比例'       => ['between:0,1'],
        'brokerage_probability|分佣概率' => ['between:0,1'],
    ];

    protected $message = [
        'type.require'  => '请选择类型',
        'type.in'       => '类型不合法',
        'mobile.mobile' => '请输入正确的手机号',
    ];

    protected $scene = [
        'add'      => ['mobile', 'name', 'brokerage_ratio', 'brokerage_probability'],
        'edit'     => ['guid', 'mobile', 'name', 'brokerage_ratio', 'brokerage_probability'],
        'recharge' => ['type', 'member_guid'],
        'send_sms' => ['content'],
    ];
}
