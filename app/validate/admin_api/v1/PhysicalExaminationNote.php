<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class PhysicalExaminationNote extends ValidateBase
{
    protected $rule = [
        'id'              => [
            'require',
        ],
        'guid'            => [
            'require',
        ],
        'bid'             => [
            'require',
        ],
        'user_guid'       => [
            'require',
        ],
        'clinic_code'     => [
            'require',
        ],
        'status'          => [
            'require',
        ],
        'total_price'     => [
            'require',
        ],
        'detail'          => [
            'require',
        ],
        'yky_bill_number' => [
            'require',
        ],
        'pay_time'        => [
            'require',
        ],
        'card_id'         => [
            'require',
        ],
        'message'         => [
            'require',
        ],
        'member_name'     => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'              => 'id不能为空',
        'guid.require'            => 'guid不能为空',
        'bid.require'             => 'bid不能为空',
        'user_guid.require'       => 'user_guid不能为空',
        'clinic_code.require'     => 'clinic_code不能为空',
        'status.require'          => 'status不能为空',
        'total_price.require'     => 'total_price不能为空',
        'detail.require'          => 'detail不能为空',
        'yky_bill_number.require' => 'yky_bill_number不能为空',
        'pay_time.require'        => 'pay_time不能为空',
        'card_id.require'         => 'card_id不能为空',
        'message.require'         => 'message不能为空',
        'member_name.require'     => 'member_name不能为空',
    ];

    protected $scene = [

    ];
}