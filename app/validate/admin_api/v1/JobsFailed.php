<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class JobsFailed extends ValidateBase
{
    protected $rule = [
        'id'         => [
            'require',
        ],
        'connection' => [
            'require',
        ],
        'queue'      => [
            'require',
        ],
        'payload'    => [
            'require',
        ],
        'exception'  => [
            'require',
        ],
        'fail_time'  => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'         => 'id不能为空',
        'connection.require' => 'connection不能为空',
        'queue.require'      => 'queue不能为空',
        'payload.require'    => 'payload不能为空',
        'exception.require'  => 'exception不能为空',
        'fail_time.require'  => 'fail_time不能为空',
    ];

    protected $scene = [

    ];
}