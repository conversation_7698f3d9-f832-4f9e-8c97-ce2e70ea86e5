<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class GoodsOrder extends ValidateBase
{
    protected $rule = [
        'guid'         => ['require'],
        'password'     => ['require'],
        'express'      => ['require'],
        'express_code' => ['require'],
        'province_id'  => ['require'],
        'city_id'      => ['require'],
        'area_id'      => ['require'],
        'address'      => ['require'],
    ];

    protected $message = [
        'guid.require'         => '请选择订单',
        'express.require'      => '请选择快递公司',
        'express_code.require' => '请填写快递单号',
        'province_id.require'  => '请选择省份',
        'city_id.require'      => '请选择城市',
        'area_id.require'      => '请选择区',
        'address.require'      => '请填写详细地址',
    ];

    protected $scene = [
        'send_out_goods'          => [''],
        'detail'                  => ['guid'],
        'print_express_order'     => ['guid'],
        'refund'                  => ['guid', 'password'],
        'refund_third_paid_money' => ['guid'],
        'batch_edit'              => ['province_id', 'city_id', 'area_id', 'address', 'guid'],
    ];
}