<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class GoodsCategory extends ValidateBase
{

    protected $rule = [
        'name'                => ['require', 'length:2,50'],
        //        'name'                => ['require', 'unique:goods_category,name', 'length:2,50'],
        'guid'                => ['require'],
        'member_group_guid'   => ['require'],
        'goods_category_guid' => ['require'],
        'discount'            => ['float'],
    ];
    protected $message = [
        'name.require' => '名称必须填写',
        'name.unique'  => '名称已存在',
    ];

    protected $scene = [
        'add'           => ['name'],
        'edit_discount' => ['discount', 'member_group_guid', 'goods_category_guid'],
        'edit'          => ['guid'],
        'del'           => ['guid'],
    ];
}