<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyCouponToJieShunCoupon extends ValidateBase
{
    protected $rule = [
        'id'                        => [
            'require',
        ],
        'guid'                      => [
            'require',
        ],
        'bid'                       => [
            'require',
        ],
        'yky_card_id'               => [
            'require',
        ],
        'yky_member_guid'           => [
            'require',
        ],
        'yky_coupon_used_note_guid' => [
            'require',
        ],
        'yky_coupon_send_note_guid' => [
            'require',
        ],
        'jie_shun_plan_no'          => [
            'require',
        ],
        'jie_shun_coupons_no'       => [
            'require',
        ],
        'message'                   => [
            'require',
        ],
        'mobile'                    => [
            'require',
        ],
        'status'                    => [
            'require',
        ],
        'send_count_index'          => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                        => 'id不能为空',
        'guid.require'                      => 'guid不能为空',
        'bid.require'                       => 'bid不能为空',
        'yky_card_id.require'               => 'yky_card_id不能为空',
        'yky_member_guid.require'           => 'yky_member_guid不能为空',
        'yky_coupon_used_note_guid.require' => 'yky_coupon_used_note_guid不能为空',
        'yky_coupon_send_note_guid.require' => 'yky_coupon_send_note_guid不能为空',
        'jie_shun_plan_no.require'          => 'jie_shun_plan_no不能为空',
        'jie_shun_coupons_no.require'       => 'jie_shun_coupons_no不能为空',
        'message.require'                   => 'message不能为空',
        'mobile.require'                    => 'mobile不能为空',
        'status.require'                    => 'status不能为空',
        'send_count_index.require'          => 'send_count_index不能为空',
    ];

    protected $scene = [

    ];
}