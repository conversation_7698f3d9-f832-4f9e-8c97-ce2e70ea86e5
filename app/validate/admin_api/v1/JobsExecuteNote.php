<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class JobsExecuteNote extends ValidateBase
{
    protected $rule = [
        'id'                 => [
            'require',
        ],
        'pid'                => [
            'require',
        ],
        'job_id'             => [
            'require',
        ],
        'execute_begin_time' => [
            'require',
        ],
        'execute_end_time'   => [
            'require',
        ],
        'used_time'          => [
            'require',
        ],
        'job_payload'        => [
            'require',
        ],
        'way'                => [
            'require',
        ],
        'status'             => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                 => 'id不能为空',
        'pid.require'                => 'pid不能为空',
        'job_id.require'             => 'job_id不能为空',
        'execute_begin_time.require' => 'execute_begin_time不能为空',
        'execute_end_time.require'   => 'execute_end_time不能为空',
        'used_time.require'          => 'used_time不能为空',
        'job_payload.require'        => 'job_payload不能为空',
        'way.require'                => 'way不能为空',
        'status.require'             => 'status不能为空',
    ];

    protected $scene = [

    ];
}