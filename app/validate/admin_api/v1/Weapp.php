<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Weapp extends ValidateBase
{
    protected $rule = [
        'appid'              => ['require', 'alphaNum', 'length:18'],
        'template_id|模板ID' => ['require'],
        'domain|域名'        => ['require'],
        'wechat_id|微信号'   => ['require'],
        'guid'               => ['require'],
    ];

    protected $message = [
        'appid.require'     => '请填写appid',
        'domain.require'    => '请选择域名',
        'wechat_id.require' => '请输入微信号',
    ];

    protected $scene = [
        'release'             => ['appid'],
        'get_qrcode'          => ['appid'],
        'undocodeaudit'       => ['appid'],
        'submit'              => ['appid'],
        'commit'              => ['appid', 'template_id', 'domain'],
        'bind_tester'         => ['appid', 'wechat_id'],
        'update_audit_status' => ['guid'],
    ];
}

