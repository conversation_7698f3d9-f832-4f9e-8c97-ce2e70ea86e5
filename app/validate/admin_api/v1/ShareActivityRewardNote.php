<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class ShareActivityRewardNote extends ValidateBase
{
    protected $rule = [
        'id'                        => [
            'require',
        ],
        'guid'                      => [
            'require',
        ],
        'bid'                       => [
            'require',
        ],
        'share_activity_guid'       => [
            'require',
        ],
        'yky_coupon_guid'           => [
            'require',
        ],
        'type'                      => [
            'require',
        ],
        'yky_coupon_send_note_guid' => [
            'require',
        ],
        'reward_time'               => [
            'require',
        ],
        'yky_member_guid'           => [
            'require',
        ],
        'member_guid'               => [
            'require',
        ],
        'openid'                    => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                        => 'id不能为空',
        'guid.require'                      => 'guid不能为空',
        'bid.require'                       => 'bid不能为空',
        'share_activity_guid.require'       => 'share_activity_guid不能为空',
        'yky_coupon_guid.require'           => 'yky_coupon_guid不能为空',
        'type.require'                      => 'type不能为空',
        'yky_coupon_send_note_guid.require' => 'yky_coupon_send_note_guid不能为空',
        'reward_time.require'               => 'reward_time不能为空',
        'yky_member_guid.require'           => 'yky_member_guid不能为空',
        'member_guid.require'               => 'member_guid不能为空',
        'openid.require'                    => 'openid不能为空',
    ];

    protected $scene = [

    ];
}