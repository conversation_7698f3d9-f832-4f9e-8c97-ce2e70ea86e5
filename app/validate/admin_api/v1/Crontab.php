<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Crontab extends ValidateBase
{

    protected $rule = [
        'guid'         => ['require'],
        'class'        => ['require'],
        'interval_sec' => ['require', 'number'],
        'status'       => ['in:0,1'],
    ];

    protected $message = [
        'name.require' => '名称必须填写',
    ];

    protected $scene = [
        //'add'  => ['name', 'interval_sec', 'class'],
        //'edit' => ['guid', 'interval_sec', 'class', 'status'],
        'del' => ['guid'],
    ];
}