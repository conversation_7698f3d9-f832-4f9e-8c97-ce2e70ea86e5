<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponSendNote extends ValidateBase
{
    protected $rule = [
        'guid'                      => ['require'],
        'goods_item_guid'           => ['require'],
        'generate_guid'             => ['require'],
        'coupon_send_note_guid'     => ['require'],
        'start_code'                => ['requireIf:edit_from,1'],
        'end_code'                  => ['requireIf:edit_from,1'],
        'coupon_generate_note_guid' => ['requireIf:edit_from,2'],
        'exchange_goods_num'        => ['require', 'number', '>=:1'],
        'coupon_guid'               => ['requireIf:edit_type,4'],
        'availability_time'         => ['requireIf:edit_type,3', 'date'],
        'expire_time'               => ['requireIf:edit_type,3', 'date'],
        'owner_user_id'             => ['requireIf:edit_type,2'],
        'coupon_send_guid'          => ['require'],
        'code'                      => ['require'],
        'password'                  => ['require'],
        'file'                      => ['require'],
        'mobile'                    => ['require', 'mobile'],
        'amount'                    => ['require', 'number', '<=:3000', '>:0'],
        'send_amount'               => ['require', 'number', '<=:3000'],
        'send_num'                  => ['require', 'number', '<=:3000'],
        'user_id'                   => ['require', 'number'],
        'edit_type'                 => ['number'],
        'edit_from'                 => ['require', 'in:1,2,3,4'],
        'name'                      => ['require', 'length:1,32'],
        'send_type'                 => ['require', 'in:0,1'],
        'apply_type'                => ['require', 'in:0,1'],
        'coupon_type'               => ['require', 'in:1,2'],
        'type'                      => ['require', 'in:1,2'],
        'description'               => ['max:100000'],
        'value'                     => ['require', 'float', 'in:50,100,200,500,1000'],
        'coupon_value'              => ['require', 'float', 'in:50,100,200,500,1000'],
    ];

    protected $message = [
        'guid.require'                => '请选择卡密',
        'start_code.requireWith'      => '请输入起始卡号',
        'end_code.requireWith'        => '请输入截止卡号',
        'goods_item_guid.require'     => '请选择关联产品',
        'name.require'                => '请输入名称',
        'file.require'                => '请上传文件',
        'coupon_type.in'              => '请选择类型',
        'mobile.require'              => '请输入手机号',
        'mobile.mobile'               => '手机号格式不正确',
        'amount.require'              => '请输入数量',
        'coupon_guid.require'         => '请选择卡券',
        'coupon_guid.requireIf'       => '请选择卡券',
        'owner_user_id.requireIf'     => '请选择归属者',
        'availability_time.requireIf' => '请选择生效时间',
        'expire_time.requireIf'       => '请选择失效时间',
        'amount.elt'                  => '单次最多可生成5000张',
        'amount.gt'                   => '单次最少生成1张',
        'send_amount.elt'             => '单次最多发送50张',
    ];

    protected $scene = [
        'add'                            => ['name', 'type', 'goods_item_guid'],
        'edit'                           => ['guid'],
        'del'                            => ['guid'],
        'import'                         => ['coupon_guid', 'file'],
        'send'                           => ['coupon_guid'],
        'batch_send'                     => ['coupon_guid', 'file'],
        'batch_send_excel'               => ['send_amount', 'mobile'],
        'single_send'                    => ['coupon_guid', 'send_amount', 'mobile'],
        'pass'                           => ['generate_guid'],
        'examine'                        => ['apply_guid'],
        'apply'                          => ['amount', 'guid'],
        'revoke'                         => ['coupon_send_guid'],
        'apply_coupon'                   => ['apply_type'],
        'batch_apply_coupon'             => ['file', 'coupon_type'],
        'batch_apply_coupon_excel'       => ['amount', 'value', 'mobile'],
        'single_apply_coupon'            => ['amount', 'coupon_guid'],
        'get_code_info'                  => ['code'],
        'batch_edit'                     => ['start_code', 'end_code', 'edit_type', 'edit_from', 'coupon_guid', 'owner_user_id', 'availability_time', 'expire_time'],
        //       'verify_code'              => ['code', 'password'],
        'active'                         => ['code'],
        'batch_send_code_expired_remind' => ['coupon_send_note_guid'],
        'check_code_num'                 => ['start_code', 'end_code', 'coupon_generate_note_guid', 'edit_from'],
    ];
}
