<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyMemberSignInActivity extends ValidateBase
{
    protected $rule = [
        'id'                => [
            'require',
        ],
        'guid'              => [
            'require',
        ],
        'bid'               => [
            'require',
        ],
        'name'              => [
            'require',
        ],
        'member_group_guid' => [
            'require',
        ],
        'value'             => [
            'require',
        ],
        'type'              => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                => 'id不能为空',
        'guid.require'              => 'guid不能为空',
        'bid.require'               => 'bid不能为空',
        'name.require'              => 'name不能为空',
        'member_group_guid.require' => 'member_group_guid不能为空',
        'value.require'             => 'value不能为空',
        'type.require'              => 'type不能为空',
    ];

    protected $scene = [

    ];
}