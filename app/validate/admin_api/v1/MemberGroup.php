<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;


class MemberGroup extends ValidateBase
{
    protected $rule = [
        'guid|唯一标识'                             => ['require'],
        'name|名称'                                 => ['require'],
        'discount_ratio|折扣比例'                   => ['between:0.01,1'],
        'brokerage_ratio|分佣比例'                  => ['between:0.00,1'],
        'brokerage_ratio_second_level|二级分佣比例' => ['between:0.00,1'],
        'point_ratio|积分比例'                      => ['between:0,10'],
    ];

    protected $message = [
        'discount_ratio.between'               => '折扣比例需要在0.01-1之间,0.9代表打9折',
        'brokerage_ratio.between'              => '分佣比例需要在0.00-1之间',
        'brokerage_ratio_second_level.between' => '二级分佣需要在0.00-1之间',
        'point_ratio.between'                  => '积分比例需要在0-10之间,2代表支付1元积2分',
    ];

    protected $scene = [
        'add'  => ['name'],
        'edit' => ['guid', 'discount_ratio', 'point_ratio', 'brokerage_ratio', 'brokerage_ratio_second_level'],
    ];
}