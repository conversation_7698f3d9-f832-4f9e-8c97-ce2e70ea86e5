<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponCashNote extends ValidateBase
{
    protected $rule = [
        'id'                   => [
            'require',
        ],
        'guid'                 => [
            'require',
        ],
        'bid'                  => [
            'require',
        ],
        'bill_number'          => [
            'require',
        ],
        'examine_time'         => [
            'require',
        ],
        'coupon_send_guid'     => [
            'require',
        ],
        'relation_bill_number' => [
            'require',
        ],
        'relation_guid'        => [
            'require',
        ],
        'member_guid'          => [
            'require',
        ],
        'cash_money'           => [
            'require',
        ],
        'status'               => [
            'require',
        ],
        'operator_user_guid'   => [
            'require',
        ],
        'memo'                 => [
            'require',
        ],
        'pay_type'             => [
            'require',
        ],
        'wechat_true_name'     => [
            'require',
        ],
        'wechat_account'       => [
            'require',
        ],
        'alipay_true_name'     => [
            'require',
        ],
        'alipay_account'       => [
            'require',
        ],
        'bank_true_name'       => [
            'require',
        ],
        'bank_name'            => [
            'require',
        ],
        'bank_account'         => [
            'require',
        ],
        'openid'               => [
            'require',
        ],
        'appid'                => [
            'require',
        ],
        'ip'                   => [
            'require',
        ],
        'remark_image_list'    => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'                   => 'id不能为空',
        'guid.require'                 => 'guid不能为空',
        'bid.require'                  => 'bid不能为空',
        'bill_number.require'          => 'bill_number不能为空',
        'examine_time.require'         => 'examine_time不能为空',
        'coupon_send_guid.require'     => 'coupon_send_guid不能为空',
        'relation_bill_number.require' => 'relation_bill_number不能为空',
        'relation_guid.require'        => 'relation_guid不能为空',
        'member_guid.require'          => 'member_guid不能为空',
        'cash_money.require'           => 'cash_money不能为空',
        'status.require'               => 'status不能为空',
        'operator_user_guid.require'   => 'operator_user_guid不能为空',
        'memo.require'                 => 'memo不能为空',
        'pay_type.require'             => 'pay_type不能为空',
        'wechat_true_name.require'     => 'wechat_true_name不能为空',
        'wechat_account.require'       => 'wechat_account不能为空',
        'alipay_true_name.require'     => 'alipay_true_name不能为空',
        'alipay_account.require'       => 'alipay_account不能为空',
        'bank_true_name.require'       => 'bank_true_name不能为空',
        'bank_name.require'            => 'bank_name不能为空',
        'bank_account.require'         => 'bank_account不能为空',
        'openid.require'               => 'openid不能为空',
        'appid.require'                => 'appid不能为空',
        'ip.require'                   => 'ip不能为空',
        'remark_image_list.require'    => 'remark_image_list不能为空',
    ];

    protected $scene = [

    ];
}