<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class GoodsTraceCodeGenerateNote extends ValidateBase
{
    protected $rule = [
        'id'               => ['require'],
        'guid'             => ['require'],
        'bid'              => ['require'],
        'generate_guid'    => ['require'],
        'batch'            => ['require'],
        'goods_guid'       => ['require'],
        'prefix'           => ['require'],
        'serial_number'    => ['require'],
        'code'             => ['require'],
        'status'           => ['require'],
        'owner_user_id'    => ['require'],
        'operator_user_id' => ['require'],
        'amount'           => ['number', '<=:10000', '>:0'],
    ];

    protected $message = [
        'id.require'               => 'id不能为空',
        'guid.require'             => 'guid不能为空',
        'bid.require'              => 'bid不能为空',
        'generate_guid.require'    => 'generate_guid不能为空',
        'batch.require'            => 'batch不能为空',
        'goods_guid.require'       => 'goods_guid不能为空',
        'prefix.require'           => 'prefix不能为空',
        'serial_number.require'    => 'serial_number不能为空',
        'code.require'             => 'code不能为空',
        'status.require'           => 'status不能为空',
        'owner_user_id.require'    => 'owner_user_id不能为空',
        'operator_user_id.require' => 'operator_user_id不能为空',
        'delete_time.require'      => 'delete_time不能为空',
        'create_time.require'      => 'create_time不能为空',
        'update_time.require'      => 'update_time不能为空',
    ];

    protected $scene = [
        'add' => ['amount'],
    ];
}