<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Wechat extends ValidateBase
{

    protected $rule = [
        'mch_id'     => ['require', 'number', 'length:8,16'],
        'partnerkey' => ['require', 'alphaNum', 'length:32'],
    ];

    protected $message = [
        'mch_id.require'      => '请填写商户号',
        'mch_id.number'       => '商户号只能是数字',
        'mch_id.length'       => '商户号长度需为8-16位',
        'partnerkey.require'  => '请填写支付密钥',
        'partnerkey.alphaNum' => '支付密钥必须为字母或者数字',
        'partnerkey.length'   => '支付密钥长度需为32位',
    ];

    protected $scene = [
        'add' => ['mch_id', 'partnerkey'],
    ];

}
