<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Translate extends ValidateBase
{

    protected $rule = [
        'source_text' => ['require'],
        //        'source_lang' => ['require'],
        'target_lang' => ['require'],
    ];

    protected $message = [
        'source_text.require' => '翻译内容必须填写',
        //        'source_lang.require' => '内容源语言必须填写',
        'target_lang.require' => '内容目标语言必须填写',
    ];

    protected $scene = [
        'translate' => ['source_text', 'target_lang'],
    ];
}