<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CropWechatApp extends ValidateBase
{
    protected $rule = [
        'id'              => ['require',],
        'name'            => ['require',],
        'platform_corpid' => ['require',],
        'suite_id'        => ['require',],
        'secret'          => ['require',],
        'token'           => ['require',],
        'encodingaeskey'  => ['require',],
        'status'          => ['require',],
    ];

    protected $message = [
        'id.require'              => 'id不能为空',
        'name.require'            => 'name不能为空',
        'platform_corpid.require' => 'platform_corpid不能为空',
        'suite_id.require'        => 'suite_id不能为空',
        'secret.require'          => 'secret不能为空',
        'token.require'           => 'token不能为空',
        'encodingaeskey.require'  => 'encodingaeskey不能为空',
        'status.require'          => 'status不能为空',
    ];

    protected $scene = [

    ];
}