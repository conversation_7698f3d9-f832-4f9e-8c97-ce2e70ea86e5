<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class UserGroup extends ValidateBase
{
    protected $rule = [
        'id'              => [
            'require',
        ],
        'guid'            => [
            'require',
        ],
        'bid'             => [
            'require',
        ],
        'sort'            => [
            'require',
        ],
        'name'            => [
            'require',
        ],
        'discount_ratio'  => [
            'require',
        ],
        'brokerage_ratio' => [
            'require',
        ],
        'status'          => [
            'require',
        ],
        'memo'            => [
            'require',
        ],
        'description'     => [
            'require',
        ],
    ];

    protected $message = [
        'id.require'              => 'id不能为空',
        'guid.require'            => 'guid不能为空',
        'bid.require'             => 'bid不能为空',
        'sort.require'            => 'sort不能为空',
        'name.require'            => 'name不能为空',
        'discount_ratio.require'  => 'discount_ratio不能为空',
        'brokerage_ratio.require' => 'brokerage_ratio不能为空',
        'status.require'          => 'status不能为空',
        'memo.require'            => 'memo不能为空',
        'description.require'     => 'description不能为空',
    ];

    protected $scene = [

    ];
}