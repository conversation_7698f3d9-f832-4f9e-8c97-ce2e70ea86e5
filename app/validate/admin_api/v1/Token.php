<?php

namespace app\validate\admin_api\v1;


use app\common\validate\ValidateBase;

class Token extends ValidateBase
{
    protected $rule = [
        'username'   => ['require'],
        'account'    => ['require'],
        'password'   => ['require'],
        'grant_type' => ['require'], //authorization_code password client_credentials implicit refresh_token
    ];

    protected $message = [
        'username.require' => '请输入用户名',
    ];

    protected $scene = [
        'get' => ['username', 'account', 'password', 'grant_type'],
    ];
}
