<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class CouponGenerateNote extends ValidateBase
{
    protected $rule = [
        'guid'               => ['require'],
        'goods_item_guid'    => ['require'],
        'generate_guid'      => ['require'],
        'exchange_goods_num' => ['require', 'number', '>=:1'],
        'password_length'    => ['number', '>=:6', '<=:16'],
        'password_type'      => ['number', 'in:1,2,3'],
        'coupon_guid'        => ['requireIf:coupon_way,1'],
        'coupon_name'        => ['requireIf:coupon_way,2'],
        'coupon_send_guid'   => ['require'],
        'code'               => ['require'],
        'password'           => ['require'],
        'mobile'             => ['require', 'mobile'],
        'amount'             => ['requireIf:way,1', 'number', '<=:50000', '>:0'],
        'file'               => ['requireIf:way,2'],
        'send_amount'        => ['require', 'number', '<=:3000'],
        'send_num'           => ['number', '<=:3000'],
        'user_id'            => ['require', 'number'],
        'name'               => ['require', 'length:1,32'],
        'way'                => ['require', 'in:1,2'],
        'coupon_way'         => ['require', 'in:1,2'],
        'send_type'          => ['require', 'in:0,1'],
        'apply_type'         => ['require', 'in:0,1'],
        'coupon_type'        => ['require', 'in:1,2'],
        'type'               => ['require', 'in:1,2'],
        'description'        => ['max:100000'],
        'value'              => ['require', 'float', 'in:50,100,200,500,1000'],
        'coupon_value'       => ['require', 'float', 'in:50,100,200,500,1000'],
        'expire_time'        => ['date'],
        'availability_time'  => ['date'],
    ];

    protected $message = [
        'guid.require'            => '请选择卡密',
        'goods_item_guid.require' => '请选择关联产品',
        'name.require'            => '请输入名称',
        'file.require'            => '请上传文件',
        'coupon_type.in'          => '请选择类型',
        'mobile.require'          => '请输入手机号',
        'amount.require'          => '请输入数量',
        'amount.requireIf'        => '请输入数量',
        'coupon_guid.require'     => '请选择卡券',
        'coupon_guid.requireIf'   => '请选择卡券!',
        'coupon_name.require'     => '请输入卡券名称',
        'coupon_name.requireIf'   => '请输入卡券名称!',
        'amount.elt'              => '单次最多可生成50000张',
        'amount.gt'               => '单次最少生成1张',
        'send_amount.elt'         => '单次最多发送50张',
    ];

    protected $scene = [
        'del' => ['generate_guid'],
        'add' => ['amount', 'way', 'file', 'coupon_guid', 'coupon_way', 'coupon_name', 'owner_user_id', 'send_num', 'expire_time', 'availability_time', 'password_length', 'password_type'],
    ];
}
