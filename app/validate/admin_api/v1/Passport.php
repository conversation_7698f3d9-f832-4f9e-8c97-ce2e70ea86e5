<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Passport extends ValidateBase
{
    protected $rule = [
        'code'           => ['require'],
        'bid'            => ['require'],
        'appid'          => ['require'],
        'type'           => ['require'],
        'mobile'         => ['require', 'mobile'],
        'sms_code'       => ['require', 'number'],
        'login_password' => ['require', 'confirm:repassword'],
        'repassword'     => ['require', 'confirm:login_password'],
    ];

    protected $message = [
        'mobile.require'         => '请输入手机号',
        'mobile.mobile'          => '请输入正确的手机号',
        'login_password.confirm' => '两次输入密码不一致',
        'repassword.confirm'     => '两次输入密码不一致',
    ];

    protected $scene = [
        'login'          => ['code', 'bid', 'appid'],
        'send_sms_code'  => ['mobile', 'type'],
        'reset_password' => ['mobile', 'sms_code', 'login_password', 'repassword'],
    ];
}