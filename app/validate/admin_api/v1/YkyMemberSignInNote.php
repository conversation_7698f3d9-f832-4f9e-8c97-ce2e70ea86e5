<?php

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class YkyMemberSignInNote extends ValidateBase
{
    protected $rule = [
        'code'            => ['require',],
        'guid'            => ['require',],
        'activity_guid'   => ['require',],
        'bid'             => [
            'require',
        ],
        'yky_member_guid' => [
            'require',
        ],
        'user_guid'       => [
            'require',
        ],
        'status'          => [
            'require',
        ],
    ];

    protected $message = [
        'code.require'            => '请输入卡号',
        'guid.require'            => 'guid不能为空',
        'bid.require'             => 'bid不能为空',
        'yky_member_guid.require' => 'yky_member_guid不能为空',
        'activity_guid.require'   => 'activity_guid不能为空',
        'user_guid.require'       => 'user_guid不能为空',
        'status.require'          => 'status不能为空',
    ];

    protected $scene = [
        'get_info' => ['code'],
        'add'      => ['code', 'activity_guid'],
    ];
}