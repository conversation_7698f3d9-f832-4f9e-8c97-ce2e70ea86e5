<?php

namespace app\validate\index;


use app\common\validate\ValidateBase;

class Plugins extends ValidateBase
{
    protected $rule = [
        'path'        => ['require'],
        'store_id'    => ['require'],
        'url'         => ['require'],
        'url_id'      => ['require'],
        'mobile_id'   => ['require'],
        'token'       => ['require'],
        'currentTime' => ['require'],
        'originId'    => ['require'],
        'query'       => ['require'],
        'sign'        => ['require'],
    ];

    protected $message = [
        'path.require' => '请传入路径',
    ];

    protected $scene = [
        'request_kuaidi'        => ['path'],
        'redirect_to_admin'     => ['store_id'],
        'redirect_to_admin_url' => ['url'],
        'redirect_to_url'       => ['url'],
        'output_img'            => ['url'],
        'download_image'        => ['url'],
        'download_file'         => ['token'],
        'redirect_url'          => ['url'],
        'redirect_link'         => ['url_id', 'mobile_id'],
        'get_weapp_url_scheme'  => ['currentTime', 'originId', 'sign'],
    ];
}
