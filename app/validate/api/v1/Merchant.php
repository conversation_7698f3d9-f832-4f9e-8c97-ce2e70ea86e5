<?php

namespace app\validate\api\v1;


use app\common\validate\ValidateBase;

class Merchant extends ValidateBase
{
    protected $rule = [
        'merchant_id|商户号' => ['require', 'number', 'length:10', 'regex:^1\d{9}$'],
        'id'                 => ['require', 'number'],
    ];

    protected $message = [
        'merchant_id.require' => '请输入商户号',
    ];

    protected $scene = [
        'dev_config' => ['merchant_id', 'value'],
        'edit'       => ['merchant_id', 'id'],
        'query'      => ['merchant_id'],
    ];
}
