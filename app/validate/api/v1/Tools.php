<?php

namespace app\validate\api\v1;

use app\common\validate\ValidateBase;

class Tools extends ValidateBase
{
    protected $rule = [
        // 'status' => ['require', 'number', 'length:10', 'regex:^1\d{9}$'],
        'status'           => ['require', 'number'],
        'guid'             => ['require'],
        'corpid|企业标志'  => ['require'],
        'company|商户名称' => ['require'],
        'true_name|姓名'   => ['require'],
        'mobile|手机号'    => ['require', 'mobile'],
    ];

    protected $message = [
        'status.require' => '请输入status',
    ];

    protected $scene = [
        'feng_chao'              => ['status'],
        'work_weixin_apply'      => ['corpid', 'company', 'true_name', 'mobile'],
        'get_single_page_detail' => ['guid'],
    ];
}