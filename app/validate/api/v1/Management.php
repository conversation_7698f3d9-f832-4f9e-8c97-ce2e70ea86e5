<?php

namespace app\validate\api\v1;


use app\common\validate\ValidateBase;

class Management extends ValidateBase
{
    protected $rule = [
        'account'  => ['require'],
        'password' => ['require'],
    ];

    protected $message = [
        'account.require'  => '请输入账号',
        'password.require' => '请输入账号',
    ];

    protected $scene = [
        'login' => ['account', 'password'],
    ];
}
