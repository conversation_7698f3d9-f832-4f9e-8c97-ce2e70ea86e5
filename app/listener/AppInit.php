<?php
declare (strict_types=1);

namespace app\listener;

use Exception;

class AppInit
{

    /**
     * 确定错误类型是否致命
     *
     * @access protected
     * @param int $type
     * @return bool
     */
    protected function isFatal(int $type): bool
    {
        return in_array($type, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE]);
    }

    protected function convertToMegabytesAndRound()
    {
        $sizeStr = strtoupper(ini_get('memory_limit'));
        // 定义单位到兆字节的转换因子
        $conversionFactors = [
            'K' => 1 / 1024, // 千字节转兆字节
            'M' => 1,       // 兆字节保持不变
            'G' => 1024,    // 吉字节转兆字节
        ];
        // 使用正则表达式匹配数字和单位
        preg_match('/^(\d+(\.\d+)?)\s*([KMG])?$/i', $sizeStr, $matches);
        if (!$matches) {
            throw new Exception("Invalid size format: {$sizeStr}");
        }
        // 分别获取数字和单位
        $number = (float)$matches[1];
        $unit   = isset($matches[3]) ? strtoupper($matches[3]) : 'K'; // 默认单位为M
        // 检查单位是否有效
        if (!array_key_exists($unit, $conversionFactors)) {
            throw new Exception("Unsupported unit: {$unit}");
        }
        // 转换为M并取整
        return (int)($number * $conversionFactors[$unit]);
    }

    /**
     * 事件监听处理
     *
     * @return void
     */
    public function handle($event)
    {
        register_shutdown_function(function () {
            if (!is_null($error = error_get_last()) && $this->isFatal($error['type']) && strpos($error['message'], 'Allowed memory size of') !== false) {
                //动态增加内存以防止无法正常将异常返回到前端,否则就是500错误
                ini_set('memory_limit', ($this->convertToMegabytesAndRound() + 60) . 'M');
            }
        });
    }
}
