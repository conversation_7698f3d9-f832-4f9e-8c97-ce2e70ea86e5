<?php
// 全局中间件定义文件
use app\middleware\GlobalMiddleware;
use app\middleware\LoadLangPackMiddleware;
use app\middleware\AppendContentType;

return [
    // 全局请求缓存
    // \think\middleware\CheckRequestCache::class,
    // 多语言加载
    // \think\middleware\LoadLangPack::class,
    // Session初始化
    // \think\middleware\SessionInit::class
    //app\middleware\AllowCrossDomain::class,
    //app\middleware\Validate::class,
    AppendContentType::class,
    LoadLangPackMiddleware::class,
    GlobalMiddleware::class,
];