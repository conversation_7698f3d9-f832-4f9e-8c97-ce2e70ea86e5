<?php

namespace app\queue\controller;

use Exception;
use think\facade\Env;
use think\queue\Job;

class Crontab extends BasicQueue
{
    /**
     *执行任务
     * @access public
     * @param Job $job
     * @param string $data
     * @return boolean
     * @throws Exception
     */
    public function resolve_and_fire_jobs(Job $job, $data)
    {
        $data_arr      = json_decode($data, true);
        $job_guid      = $data_arr['job_guid'];
        $key           = __FUNCTION__ . $job_guid;
        $lock_instance = get_distributed_instance();
        $lock          = $lock_instance->get_lock($key); //同一个任务防止并发处理
        try {
            $result = $this->resolve_and_fire($data_arr['class'], $data_arr['payload']);
            //todo 后续可根据返回值来判断是否执行成功,或者做好执行记录
            $lock_instance->unlock($lock);
            $job->delete();
            return true;
        } catch (Exception $e) {
            wr_log('job_guid:' . $job_guid . '|执行失败:' . $e->getMessage());
            $lock_instance->unlock($lock);
            throw new Exception($e->getMessage());
        }
    }

    protected function resolve_and_fire($name, array $payload)
    {
        [$class, $method] = $this->parse_job($name);
        $instance = $this->resolve($class);
        if ($instance) {
            return $instance->{$method}($payload);
        }
        return false;
    }

    /**
     * Parse the job declaration into class and method.
     * @param string $job
     * @return array
     */
    protected function parse_job($job)
    {
        $segments = explode('@', $job);

        return count($segments) > 1 ? $segments : [$segments[0], 'fire'];
    }

    /**
     * Resolve the given job handler.
     * @param string $name
     * @return mixed
     */
    protected function resolve($name)
    {
        if (strpos($name, '\\') === false) {

            if (strpos($name, '/') === false) {
                $module = '';
            } else {
                [$module, $name] = explode('/', $name, 2);
            }

            $name = Env::get('app.app_namespace') . ($module ? '\\' . strtolower($module) : '') . '\\job\\' . $name;
        }
        if (class_exists($name)) {
            return new $name();
        }
    }
}