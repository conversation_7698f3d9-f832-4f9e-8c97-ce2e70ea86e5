<?php

namespace app\queue\controller;

use app\model\YkyConsumeNote;
use app\model\YkyGoods;
use app\model\YlhAccessToken;
use app\model\YlhPointOrder;
use app\common\service\YlhService;
use Exception;
use think\queue\Job;

class Ylh extends BasicQueue
{
    /**
     *奖励
     * @access public
     * @param Job $job
     * @param string $data
     * @return boolean
     * @throws Exception
     */
    public function ylh_reward(Job $job, $data)
    {
        $data_arr               = json_decode($data, true);
        $consume_note_data      = $data_arr['consume_note_data'];
        $user_id                = $data_arr['ylh_user_id'];
        $rcm_id                 = $data_arr['rcm_id'];
        $yky_rec_member_card_id = $data_arr['yky_rec_member_card_id'];
        $bid                    = $data_arr['bid'];
        $bill_number            = $consume_note_data['bill_number'];
        $note_guid              = $consume_note_data['guid'];
        $total_paid             = $consume_note_data['total_paid'];
        $rate                   = 0.0003;
        $member_reward_rate     = 0.0002;
        $system_card_id         = '888888';
        $consume_note_db        = new YkyConsumeNote();
        $ylh_obj                = YlhService::get_instance(['user_id' => $user_id]);
        $reward_value           = tools()::nc_price_calculate($total_paid, '*', $rate, 4); //奖励金额保留四位小数
        $reward_data            = [
            'guid'                => $data_arr['order_guid'],
            'user_id'             => $user_id,
            'bid'                 => $bid,
            'system_card_id'      => $system_card_id,
            'rate'                => $rate,
            'consume_value'       => $total_paid * $rate / $member_reward_rate, //计算应该消费的金额
            'reward_value'        => $reward_value,
            'reward_card_id'      => $yky_rec_member_card_id,
            'consume_bill_number' => $bill_number,
            'total_paid'          => $total_paid,
        ];
        $result                 = $ylh_obj->reward_value($reward_data);
        $map                    = [['guid', '=', $note_guid]];
        if (!$result) {
            // 更新记录表
            $update_data = [
                'reward_status' => -1,
                'message'       => $ylh_obj->errmsg,
            ];
            $update      = $consume_note_db::update($update_data, $map);
            throw new Exception($ylh_obj->errmsg);
        } else {
            //消费成功则删除任务即可
            $job->delete();
            $update_data = [
                'reward_status' => 1,
                'message'       => '奖励成功,单号:' . $result['billNumber'],
            ];
            $update      = $consume_note_db::update($update_data, $map);
            return true;
        }
    }

    /**
     *单笔消费奖励
     * @access public
     * @param Job $job
     * @param string $data
     * @return boolean
     * @throws Exception
     */
    public function reward(Job $job, $data)
    {
        $data_arr          = json_decode($data, true);
        $user_id           = $data_arr['ylh_user_id'];
        $rcm_id            = $data_arr['rcm_id'];
        $bid               = $data_arr['bid'];
        $consume_note_data = $data_arr['consume_note_data'];
        $member_card_id    = $consume_note_data['card_id'];
        $way               = $consume_note_data['way'];
        $point             = $consume_note_data['point'];
        $total_paid        = $consume_note_data['total_paid'];
        $member_guid       = $consume_note_data['member_guid'];
        $bill_number       = $consume_note_data['bill_number'];
        $note_guid         = $consume_note_data['guid'];
        $consume_note_db   = new YkyConsumeNote();
        $map_note          = [
            ['guid', '=', $note_guid],
            ['bid', '=', $bid],
        ];
        try {
            $config                        = get_config_by_bid($bid);
            $yky                           = new \OpenApi\Yky($config);
            $upgrade_member_group_name_arr = explode(',', $config['upgrade_member_group_name']);
            $mobile                        = $yky->get_member_card_id_or_mobile($member_card_id);
            if ($mobile === false) {
                throw new Exception($yky->message);
            }
            if (isset($data_arr['ylh_mobile'])) {
                $ylh_mobile = $data_arr['ylh_mobile'];
            } else {
                $db_ylh_access_token = new YlhAccessToken();
                $map                 = [['ylh_user_id', '=', $user_id]];
                $ylh_mobile          = $db_ylh_access_token->where($map)->value('mobile');
            }
            if ($mobile == $ylh_mobile) {
                // 更新记录表
                $msg         = '商户手机号与消费手机号一致,无需处理!';
                $update_data = [
                    'status'  => -1,
                    'message' => $msg,
                ];
                $update      = $consume_note_db::update($update_data, $map_note);
                $job->delete();
                return false;
            }
            //TODO 判断是否撤销单据 way  -1快速消费退货 -2 消费收银退货 6商城订单 9拼团订单 7 自助买单
            if (!in_array($way, [-1, -2, 1, 3, 6, 7])) {
                //TODO 其他业务无需处理
                // 更新记录表
                $update_data = [
                    'status'  => 1,
                    'message' => '该业务类型无需处理',
                ];
                $update      = $consume_note_db::update($update_data, $map_note);
                $job->delete();
                return true;
            }
            $ylh_obj = YlhService::get_instance(['user_id' => $user_id]);
            $db      = new YlhPointOrder();
            $result  = false; //默认是失败
            if (in_array($way, [1, 3, 6, 7])) {
                // 3储值扣费 1快速消费 7自助买单 6商城订单 统一赠送给消费者积分
                if ($point > 0) {
                    $return_data = [
                        'guid'            => $data_arr['order_guid'],
                        'user_id'         => $user_id,
                        'bid'             => $bid,
                        'way'             => 1,//消费奖励
                        'level'           => 0, //0级 自己奖励
                        'buyer_mobile'    => $mobile,//买家手机号
                        'yky_bill_number' => $bill_number,//消费单号
                        'status'          => 0,//等待赠送
                        'total_amount'    => $point,//金额（单位：分）
                        'body'            => 'body',
                        'subject'         => 'subject',
                        'out_trade_no'    => $bill_number,//云联惠订单号
                        'order_msg'       => '单据号:' . $bill_number,//订单信息
                    ];
                    $result      = $ylh_obj->return_point($return_data);
                } else {
                    $result = true;
                    wr_log($bill_number . '消费奖励为0,无需奖励', 0, $bid);
                }
                if (in_array($way, [6])) {
                    //现执行会员升级判断
                    //再换取消费明细对应的奖励积分
                    $goods_guid_arr = $yky->get_goods_guid_from_consume_note($consume_note_data['guid']);
                    if ($goods_guid_arr === false) {
                        throw new Exception($yky->message);
                    }
                    $is_need_upgrade_goods_guid_arr = explode(',', $config['is_need_upgrade_goods_guid']);
                    if (!empty(array_intersect($is_need_upgrade_goods_guid_arr, $goods_guid_arr))) {
                        //如果购买了可升级的商品,则进行会员升级,使用交集函数判断
                        $api_data = [
                            'cardId'          => $member_card_id,
                            'memberGroupName' => current($upgrade_member_group_name_arr)
                        ];
                        $upgrade  = $yky->Update_Member($api_data);
                        if ($upgrade) {
                            wr_log('会员' . $member_card_id . '升级成功', 0, $bid);
                        } else {
                            wr_log('会员' . $member_card_id . '升级失败:' . $yky->message, 0, $bid);
                        }
                    }
                    //商城订单 奖励推荐人,先获取推荐人卡号
                    $db                       = new YkyGoods();
                    $map                      = [
                        ['bid', '=', $bid],
                        ['guid', 'in', $goods_guid_arr]
                    ];
                    $reward_recommended_point = $db->where($map)->sum('reward_recommended_point');
                    if ($reward_recommended_point > 0) {
                        $rate_arr                 = [0.7, 0.2, 0.1]; //支持多级奖励
                        $recommend_member_card_id = $yky->get_recommend_member_card_id_or_mobile($mobile);
                        $reward_card_arr          = [$recommend_member_card_id];
                        $level                    = 0;
                        foreach ($rate_arr as $k => $v) {
                            $reward_card_id = end($reward_card_arr);
                            if (!tools()::is_mobile($reward_card_id)) {
                                //不是手机号则跳出循环
                                wr_log($bill_number . '推荐人卡号和手机号都不是手机号', 0, $bid);
                                continue;
                            }
                            //判断要奖励的会员是否需要高级别会员
                            $member            = $yky->get_member_info($reward_card_id);
                            $member_group_name = $member['MemberGroupName'];
                            if (!in_array($member_group_name, $upgrade_member_group_name_arr)) {
                                wr_log($bill_number . '推荐人:' . $reward_card_id . '属于【' . $member_group_name . '】,无需奖励', 0, $bid);
                            } else {
                                //有奖励卡号且是手机号则执行奖励
                                $total_amount = intval(tools()::nc_price_calculate($reward_recommended_point, '*', $rate_arr[$level], 0));
                                $return_data  = [
                                    'guid'            => tools()::md5_guid(md5($data_arr['order_guid'] . $level)), //订单号加上层级 生成唯一码
                                    'user_id'         => $user_id,
                                    'bid'             => $bid,
                                    'way'             => 2,//推荐人奖励
                                    'level'           => $level, //推荐奖励层级
                                    'buyer_mobile'    => $reward_card_id,//买家手机号
                                    'status'          => 0,//等待赠送
                                    'total_amount'    => $total_amount,//金额（单位：分）
                                    'body'            => 'body',
                                    'subject'         => 'subject',
                                    'yky_bill_number' => $bill_number,//消费单号
                                    'out_trade_no'    => $bill_number . '_' . $level,//云联惠订单号
                                    'order_msg'       => '推荐人奖励单据号:' . $bill_number,//订单信息
                                ];
                                $result       = $ylh_obj->return_point($return_data);
                                ++$level;
                            }
                            $next_reward_card  = $yky->get_recommend_member_card_id($reward_card_id);
                            $reward_card_arr[] = $next_reward_card;
                        }
                    }
                }
            }
            //退货逻辑
            if (in_array($way, [-1, -2])) {
                $abs_total_paid = abs($total_paid);
                //同一个会员 同一个金额 需要采用分布式锁 已解决同时退两单金额一样的问题
                $key           = __FUNCTION__ . $member_guid . $abs_total_paid;
                $lock_instance = get_distributed_instance();
                $lock          = $lock_instance->get_lock($key);
                //查找最近一单奖励金额相同的单据
                $map                = [
                    ['a.bid', '=', $bid],
                    ['a.user_id', '=', $user_id],
                    ['a.status', '=', 1],
                    ['ycn.member_guid', '=', $member_guid],
                    ['ycn.total_paid', '=', $abs_total_paid],
                ];
                $before_bill_number = $db->alias('a')->where($map)->join(['yky_consume_note' => 'ycn'], 'ycn.bid=a.bid AND ycn.bill_number=a.yky_bill_number')->order('ycn.create_time', 'desc')->value('bill_number');
                if (!$before_bill_number) {
                    $lock_instance->unlock();
                    throw new Exception('近期没有同金额订单');
                }
                $map  = [
                    ['bid', '=', $bid],
                    ['user_id', '=', $user_id],
                    ['status', '=', 1],
                    ['yky_bill_number', '=', $before_bill_number],
                ];
                $list = $db->where($map)->select();
                if ($list->isEmpty()) {
                    $lock_instance->unlock();
                    throw new Exception('订单查找奖励记录失败');
                }
                //根据一卡易单号直接找到当时奖励的所有关联单号
                foreach ($list as $key => $val) {
                    $refund_data = [
                        'bid'           => $bid,
                        'user_id'       => $user_id,
                        'order_id'      => $val['order_id'],
                        'refund_amount' => $val['total_amount'],
                    ];
                    $result      = $ylh_obj->refund_point($refund_data);
                }
                //处理完毕后释放锁
                $lock_instance->unlock();
            }
            if ($result !== false) {
                // 更新记录表
                $update_data = [
                    'status'  => 1,
                    'message' => '处理成功',
                ];
                $update      = $consume_note_db::update($update_data, $map_note);
                $job->delete();
                return true;
            } else {
                switch ($ylh_obj->errcode) {
                    case 'P990408':
                        $job->release(24 * 3600); //24小时候重新执行
                }
                // 更新记录表
                $update_data = [
                    'status'  => -1,
                    'message' => $ylh_obj->errmsg,
                ];
                $update      = $consume_note_db::update($update_data, $map_note);
                throw new Exception($ylh_obj->errmsg);
            }
        } catch (Exception $e) {
            $msg = '单据号:' . $bill_number . '云联惠返积分任务异常:' . $e->getMessage();
            // 更新记录表
            $update_data = [
                'status'  => -1,
                'message' => $e->getMessage(),
            ];
            $update      = $consume_note_db::update($update_data, $map_note);
            wr_log($msg, 0, $bid);
            throw new Exception($msg);
        }
    }
}