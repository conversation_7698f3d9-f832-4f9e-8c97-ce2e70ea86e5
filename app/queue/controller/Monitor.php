<?php

namespace app\queue\controller;

use app\model\AnjufangRankNote;
use app\model\Business;
use app\model\GoodsPriceMonitor;
use app\model\Jobs;
use app\model\Parameter;
use app\model\PayChannel;
use app\model\PayParameter as PayParameterModel;
use app\model\SystemConfig;
use app\model\WeappSubmitNote;
use app\model\WechatConfig;
use app\model\WechatMerchantApplyment;
use app\common\service\NotifyService;
use app\model\WeixinFuwuTaskList;
use app\model\XiaoQuHouse;
use DateTime;
use Exception;
use JJG\Ping;
use OpenApi\ChangAnCar;
use OpenApi\DingTalk;
use OpenApi\WechatBot;
use OpenApi\ZhuJianJu;
use think\facade\Db;
use think\queue\Job;
use WePayV3\Merchant;
use xieyongfa\facade\Notify;

class Monitor extends BasicQueue
{
    /**
     *
     *业主大会数据通知
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function ye_zhu_da_hui_data_notify_daily(Job $job, $data)
    {
        $job_data               = json_decode($data, true);
        $yesterday              = date("Y-m-d", strtotime("-1 day"));
        $today                  = date("Y-m-d");
        $msg                    = '日期:' . $yesterday . "\n";
        $db                     = new XiaoQuHouse();
        $map                    = [
            ['last_bind_time', '>=', $yesterday],
            ['last_bind_time', '<', $today],
        ];
        $yesterday_bind         = $db->where($map)->count(); //昨日绑定
        $map                    = [
            ['vote_date_time', '>=', $yesterday],
            ['vote_date_time', '<', $today],
        ];
        $yesterday_vote         = $db->where($map)->count(); //昨日投票
        $zjj                    = new ZhuJianJu();
        $result                 = $zjj->get_total_voted_info_v2();
        $voted_house_count      = $result['total_voted_house_num']; //投票户数
        $voted_house_area_count = $result['total_voted_house_area']; //投票户数
        $msg                    .= '昨日投票:' . $yesterday_vote . "户\n";
        $msg                    .= '昨日绑定:' . $yesterday_bind . "户\n";
        $msg                    .= '累计户数:' . $voted_house_count . "户\n";
        $msg                    .= '累计面积:' . $voted_house_area_count . "平米\n";
        $msg                    .= '当前时间:' . format_timestamp();
        $wechat_bot             = new WechatBot();
        $result                 = $wechat_bot->batch_text($msg, ['48621985684@chatroom']);
        // 时段: 3-31 14:00~3-31 16:00
        //新增投票: 5户
        //新增绑定: 1户
        //今日投票: 18户
        //今日绑定: 4户
        //累计投票: 856户(达标率51%)
        //累计面积: 82994平米(达标率46%)
        $job->delete();
        return true;
    }

    /**
     *
     *业主大会数据通知
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function ye_zhu_da_hui_data_notify(Job $job, $data)
    {
        $job_data                   = json_decode($data, true);
        $last_update_time_cache_key = __FUNCTION__ . ':last_update_time';
        $last_update_time           = cache($last_update_time_cache_key);
        $today_timestamp            = strtotime(date('Y-m-d'));
        $last_update_time           = $last_update_time ?: $today_timestamp; // 当天0点的时间戳
        $last_update                = date('n-j H:i', $last_update_time);
        $now                        = date('n-j H:i');
        $msg                        = '时段:' . $last_update . '至' . $now . "\n";
        $db                         = new XiaoQuHouse();
        $map                        = [['last_bind_time', '>', format_timestamp($today_timestamp)]];
        $today_bind                 = $db->where($map)->count(); //今日绑定
        $map                        = [['vote_date_time', '>', format_timestamp($today_timestamp)]];
        $today_vote                 = $db->where($map)->count(); //今日投票
        $map                        = [['last_bind_time', '>', format_timestamp($last_update_time)]];
        $add_bind                   = $db->where($map)->count();
        $map                        = [['vote_date_time', '>', format_timestamp($last_update_time)]];
        $add_vote                   = $db->where($map)->count();
        $zjj                        = new ZhuJianJu();
        $result                     = $zjj->get_total_voted_info_v2();
        $voted_house_count          = $result['total_voted_house_num']; //投票户数
        $voted_house_area_count     = $result['total_voted_house_area']; //投票户数
        $msg                        .= '新增投票:' . $add_vote . "户\n";
        $msg                        .= '新增绑定:' . $add_bind . "户\n";
        $msg                        .= '今日投票:' . $today_vote . "户\n";
        $msg                        .= '今日绑定:' . $today_bind . "户\n";
        $msg                        .= '累计户数:' . $voted_house_count . "户\n";
        $msg                        .= '累计面积:' . $voted_house_area_count . "平米\n";
        $msg                        .= '当前时间:' . format_timestamp();
        $wechat_bot                 = new WechatBot();
        $result                     = $wechat_bot->batch_text($msg, ['56459990960@chatroom']);
        $currentTime                = date('Gi'); // 'Gi' 表示小时(不带0导 0-23)和分钟(0-59)，例如 1730  605
        // 判断是否在下午五点半到六点半之间
        if ($currentTime >= 1730 && $currentTime < 1830) {
            $result = $wechat_bot->batch_text($msg, ['wxid_dpvb1tzyc4mm12']);
        }
        cache($last_update_time_cache_key, time());
        // 时段: 3-31 14:00~3-31 16:00
        //新增投票: 5户
        //新增绑定: 1户
        //今日投票: 18户
        //今日绑定: 4户
        //累计投票: 856户(达标率51%)
        //累计面积: 82994平米(达标率46%)
        $job->delete();
        return true;
    }
    //    /**
    //     *监控微信商户状态
    //     * @acces public
    //     * @param Job $job 任务对象
    //     * @param string $data 数据
    //     * @return mixed
    //     * @throws \Exception
    //     */
    //    public function apply_jd_price_protect(Job $job, $data)
    //    {
    //        $url                = 'https://api.m.jd.com/api?appid=siteppM&functionId=siteppM_skuOnceApply&forcebot=&t=' . time(); //申请价保
    //        $jd_auth_token_list = get_system_config('jd_auth_token_list');
    //        $jd_auth_token_list = explode(',', $jd_auth_token_list);
    //        $url                = 'https://api.m.jd.com/api?appid=siteppM&functionId=siteppM_skuOnceApply&forcebot=&t=' . time(); //申请价保
    //        $body               = [
    //            'sid'      => '',
    //            'type'     => '26',
    //            'forcebot' => '',
    //        ];
    //        $header             = [
    //            'Host'       => 'api.m.jd.com',
    //            'Accept'     => 'application/json',
    //            'Origin'     => 'https://msitepp-fm.jd.com',
    //            'Referer'    => 'https://msitepp-fm.jd.com/',
    //            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.31(0x18001f2d) NetType/WIFI Language/zh_CN/',
    //        ];
    //        $data               = ['body' => json_encode($body, JSON_UNESCAPED_UNICODE)];
    //
    //        foreach ($jd_auth_token_list as $jd_auth_token) {
    //            $cookies = 'wq_auth_token=' . $jd_auth_token . ';';
    //            $result  = curl()->set_header($header)->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
    //
    //        }
    //    }

    /**
     *
     *天文台监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function tianwentai(Job $job, $data)
    {
        $job_data  = json_decode($data, true);
        $wxuser    = $job_data['wxuser'];
        $token     = $job_data['token'];
        $token_id  = $job_data['token_id'];
        $keyword   = $job_data['keyword'] ?? '';
        $post_data = [
            'source'  => 'wx',
            'wxuser'  => $wxuser,
            'token'   => $token,
            'tokenId' => $token_id
        ];
        $url       = 'http://weather.121.com.cn/szqx/api/twt_v2/kfr/list.do';
        $response  = curl()->form_params()->post($url, $post_data)->get_body();
        if (!is_array($response) || !isset($response['code']) || (isset($response['code']) && $response['code'] != 0)) {
            wr_log('天文台监控异常', 1);
            $job->delete();
            return true;
        }
        $list = $response['data']['kfrs'];
        foreach ($list as $key => $val) {
            foreach ($val as $k => $v) {
                if ($v['curNum'] < $v['maxNum']) {
                    // 使用正则表达式删除 <em> 标签及其内部内容
                    $pattern     = '/<em[^>]*>.*?<\/em>/i';
                    $replacement = '';
                    $result      = preg_replace($pattern, $replacement, $key);
                    $need_notify = false;
                    if (empty($keyword)) {
                        $need_notify = true;
                    } else {
                        $keyword_array = explode(',', $keyword);
                        foreach ($keyword_array as $keyword_item) {
                            if (strpos($v['name'], $keyword_item) !== false) {
                                $need_notify = true;
                                break;
                            }
                        }
                    }
                    if ($need_notify) {
                        wr_log($result . $v['name'] . '剩余' . ($v['maxNum'] - $v['curNum']) . '个号,请及时预约, http://weather.121.com.cn/sztwt/#/gr', 1);
                    }
                }
            }
        }
        $job->delete();
        return true;
    }


    /**
     *
     *商户到期提醒
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function expire_notify(Job $job, $data)
    {
        $db_business   = new Business();
        $field         = [
            'id',
            'guid',
            'business_name',
            'account',
            'expired_time',
            Db::raw('TO_DAYS(expired_time)-TO_DAYS(NOW()) as expired_day'),
        ];
        $map           = [['license_status', '=', 2]]; //只关心正式账号到期
        $business_list = $db_business->field($field)->where($map)->whereRaw("TO_DAYS(expired_time)-TO_DAYS(NOW()) IN (1,2,3,7,15,30)")->order(['expired_time' => 'DESC'])->select();
        foreach ($business_list as $business) {
            $data = [
                //                'url'         => 'https://work.weixin.qq.com/kfid/kfccdfdce7a04d1ad35?from=copy_right',
                'url'         => 'http://' . config('app.app_host_domain') . '/u/qnfzrH',
                'title'       => '【重要】账号即将到期提醒',
                'name'        => '【重要】账号' . $business['expired_day'] . '天后即将到期提醒',
                'create_time' => '到期时间:' . date('Y-m-d', strtotime($business['expired_time'])),
                'user'        => $business['business_name'] . '(' . $business['account'] . ')',
                'detail'      => '点击咨询续费',
                'remark'      => '如有疑问请联系客服',
            ];
            notify()->set_key_name(NotifyService::Notice)->limit_business()->limit_agent()->set_data($data)->set_bid($business['guid'])->send();
        }
        $job->delete();
        return true;
    }

    /**
     *监控微信商户状态
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function weapp_icp_entrance_status(Job $job, $data)
    {
        $db_wechat_config = new WechatConfig();
        $min_create_time  = date('Y-m-d H:i:s', strtotime('-3000 day'));
        $map              = [
            ['create_time', '>=', $min_create_time],
            ['icp_entrance_status', 'NOT IN', [6]],
            ['func_info', 'LIKE', '%156%'], // 授权了备案权限
            ['type', '=', 2], // 小程序
        ];
        $list             = $db_wechat_config->where($map)->field(['id', 'icp_entrance_status', 'authorizer_appid', 'principal_name'])->order(['create_time' => 'DESC'])->select();
        foreach ($list as $key => $weapp) {
            $before_icp_entrance_status = $weapp['icp_entrance_status'];
            $appid                      = $weapp['authorizer_appid'];
            $principal_name             = $weapp['principal_name'];
            $id                         = $weapp['id'];
            $weapp                      = weixin($appid)::WeMiniIcp();
            $icp_entrance_info          = $weapp->getIcpEntranceInfo();
            $after_icp_entrance_status  = $icp_entrance_info['info']['status'];
            $errmsg                     = '';
            if (!empty($icp_entrance_info['info']['audit_data'])) {
                foreach ($icp_entrance_info['info']['audit_data'] as $key => $val) {
                    $errmsg .= $val['error'];
                }
            }
            $status_mapping = [
                0    => '初始化',
                2    => '平台审核中',
                3    => '平台审核驳回',
                4    => '管局审核中',
                5    => '管局审核驳回',
                6    => '已备案',
                1024 => '[未备案]',
                1025 => '[未备案]且基本信息未填',
                1026 => '[未备案]且类目未填',
                1027 => '[未备案]且基本信息/类目未填',
            ];

            if ($before_icp_entrance_status != $after_icp_entrance_status) {
                //先更新状态
                $map         = [['id', '=', $id]];
                $update_data = ['icp_entrance_status' => $after_icp_entrance_status];
                $db_wechat_config::update($update_data, $map);

                $db_parameter = new Parameter();
                $bid          = $db_parameter->get_bid_by_appid($appid);
                if ($bid) {
                    //通知代理商
                    $db_business   = new Business();
                    $business_info = $db_business->get_business_info_by_account_or_guid($bid);
                    $business_name = $business_info['business_name'];
                    $data          = [
                        'url'         => '',
                        'title'       => '状态变更通知',
                        'name'        => '小程序备案状态发生变化', //流程名称
                        'create_time' => format_timestamp(), //操作时间
                        'user'        => ($status_mapping[$after_icp_entrance_status] ?? '') . $errmsg,
                        'detail'      => $business_name . '-' . $principal_name,
                        'remark'      => '如有疑问请联系客服',
                    ];
                    notify()->set_key_name(NotifyService::Notice)->limit_agent()->limit_business()->set_data($data)->set_bid($bid)->send();
                } else {
                    wr_log($appid . '备案状态发生变化，但未找到对应商家，请检查');
                }
                if ($after_icp_entrance_status == 6) {
                    //判断是否有待上线的记录 有的话再自动上线
                    $db_weapp_submit_note = new WeappSubmitNote();
                    $last_submit_note     = $db_weapp_submit_note->get_last_submit_note($appid);
                    if (!$last_submit_note->isEmpty() && $last_submit_note['status'] == 0) {
                        $job_data = ['appid' => $appid];
                        job()->set_job_name('Weapp@publish_release')->push_job($job_data);
                    }
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *监控微信商户状态
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function wechat_merchant_applyment(Job $job, $data)
    {
        $channel_id                   = 4;
        $db_wechat_merchant_applyment = new WechatMerchantApplyment();
        $map                          = [
            ['applyment_state', 'NOT IN', ['APPLYMENT_STATE_FINISHED', 'APPLYMENT_STATE_CANCELED']]
        ];
        $list                         = $db_wechat_merchant_applyment->where($map)->order(['create_time' => 'DESC'])->select();
        $db_pay_channel               = new PayChannel();
        $config                       = $db_pay_channel->get_channel_parameter_by_channel_id($channel_id);
        $merchant                     = Merchant::instance($config);
        foreach ($list as $key => $applyment) {
            $applyment_id              = $applyment['applyment_id'];
            $before_applyment_state    = $applyment['applyment_state'];
            $bid                       = $applyment['bid'];
            $auto_add_pay_parameter    = $applyment['auto_add_pay_parameter'];
            $result                    = $merchant->query_by_applyment_id($applyment_id);
            $after_applyment_state     = $result['applyment_state'];
            $after_applyment_state_msg = $result['applyment_state_msg'];
            $sign_url                  = $result['sign_url'];
            $sub_mchid                 = $result['sub_mchid'] ?? '';

            if ($before_applyment_state != $after_applyment_state) {
                $business_name = '【商家名称】';
                if ($bid) {
                    $db_business   = new Business();
                    $business_info = $db_business->get_business_info_by_account_or_guid($bid);
                    $business_name = $business_info['business_name'];
                }
                //状态发生变化
                $map         = [
                    ['applyment_id', '=', $applyment_id]
                ];
                $status_desc = $db_wechat_merchant_applyment->get_status_text($after_applyment_state);
                if ($after_applyment_state == 'APPLYMENT_STATE_REJECTED') {
                    $audit_detail              = array_column($result['audit_detail'], 'reject_reason');
                    $after_applyment_state_msg = join('、', $audit_detail);
                }
                $update_data = [
                    'applyment_state'     => $after_applyment_state,
                    'applyment_state_msg' => $after_applyment_state_msg,
                ];
                if (empty($applyment['sign_url'])) {
                    $update_data['sign_url'] = $sign_url;
                }
                if (empty($applyment['sub_mchid']) && $sub_mchid) {
                    $update_data['sub_mchid'] = $sub_mchid;
                }
                $db_wechat_merchant_applyment::update($update_data, $map);
                $title = '商家【' . $business_name . '】支付进件状态变化(单号' . $applyment_id . ')';
                $data  = [
                    'url'         => $sign_url,
                    'title'       => $title,
                    'remark'      => '点击查看签约二维码',
                    'detail'      => $after_applyment_state_msg,
                    'user'        => $title,
                    'name'        => $status_desc,
                    'create_time' => format_timestamp(),
                ];
                notify()->set_key_name(NotifyService::Notice)->limit_agent()->limit_system()->set_data($data)->set_bid($bid)->send();

                if ($after_applyment_state == 'APPLYMENT_STATE_FINISHED' && $auto_add_pay_parameter && $bid) {
                    //进件成功 自动配置支付参数
                    $db_pay_parameter = new PayParameterModel();
                    $map              = [['bid', '=', $bid], ['channel_id', '=', $channel_id]];
                    $pay_parameter    = $db_pay_parameter->where($map)->order(['create_time' => 'DESC'])->findOrEmpty();
                    if ($pay_parameter->isEmpty()) {
                        $parameter_guid = $db_pay_parameter->add_parameter($channel_id, $bid); //自动添加该通道,会防止重复添加
                        if ($parameter_guid === false) {
                            wr_log('自动添加通道失败:' . $db_pay_parameter->getError(), 1);
                            continue;
                        }
                        $pay_parameter = $db_pay_parameter->where($map)->findOrEmpty();
                    }
                    $pay_parameter                            = tools()::object2array($pay_parameter);
                    $pay_parameter['parameter']['sub_mch_id'] = $sub_mchid;
                    $pay_parameter['parameter']['guid']       = $pay_parameter['guid'];
                    $pay_parameter['parameter']['priority']   = $pay_parameter['priority'];
                    $pay_parameter['parameter']['scene_id']   = $pay_parameter['scene_id'];
                    $pay_parameter['parameter']['status']     = 1;
                    $pay_parameter['parameter']['key']        = 'wechatsub';
                    $update                                   = $db_pay_parameter->update_parameter($bid, $pay_parameter['parameter']);
                    $data                                     = [
                        'url'         => '',
                        'title'       => '商家' . $business_name . '支付自动配置通过',
                        'detail'      => '商户号:' . $sub_mchid,
                        'user'        => '【系统】',
                        'name'        => '支付参数自动配置',
                        'remark'      => '如有疑问请联系客服',
                        'create_time' => format_timestamp(),
                    ];
                    notify()->set_key_name(NotifyService::Notice)->limit_agent()->set_data($data)->set_bid($bid)->send();
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *监控mysql主从同步状态
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function mysql_slave(Job $job, $data)
    {
        //$mysql_slave_list = ['mysql_slave', 'mysql_slave_2', 'mysql_slave_3'];
        $connections = config('database.connections');
        foreach ($connections as $mysql_slave => $val) {
            if (strpos($mysql_slave, 'mysql_slave') !== false) {
                try {
                    $slave_info = Db::connect($mysql_slave)->query("show slave status");
                    if (empty($slave_info)) {
                        wr_log($mysql_slave . '主从监控异常', 1);
                    }
                    $slave_info = $slave_info[0];
                    if ($slave_info['Slave_IO_Running'] != 'Yes' || $slave_info['Slave_SQL_Running'] != 'Yes') {
                        wr_log($mysql_slave . '主从监控Slave_IO_Running异常', 1);
                        wr_log($slave_info, 1);
                    }
                } catch (Exception $e) {
                    wr_log($mysql_slave . '主从连接异常:' . $e->getMessage(), 1);
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *汽车之家余额
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function qichezhijia_money(Job $job, $data)
    {
        $url = 'https://pay.autohome.com.cn/site/my/home.htm';
        //$cookie = 'com.vfsso.cas.token=' . get_system_config('qichezhijia_token');
        $cookie      = get_system_config('qichezhijia_token');
        $result      = curl()->set_cookies($cookie)->ignore_log(true)->get($url)->get_body();
        $cache_key   = 'qichezhijia_money';
        $time        = intval(date('Hi'));
        $is_notify   = ($time > 830 && $time < 2200);
        $cache_money = (string)cache($cache_key);
        if (strpos($result, '安全退出') !== false) {
            $money = (string)tools()::search_str('class="f20">', '</em>', $result);
            if ($cache_money !== $money) {
                wr_log('【余额变动】汽车之家余额由' . $cache_money . '变更为' . $money, 1);
                cache($cache_key, $money, 3600 * 24 * 100);
            }
        } else if (strpos($result, '登录框') !== false) {
            wr_log('【掉线】汽车之家余额监控已经成掉线了', $is_notify);
        } else {
            wr_log('【未知】汽车之家状态', $is_notify);
        }
        $job->delete();
        return true;
    }


    /**
     *12123临牌进度
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function jiaoguan_process_list(Job $job, $data)
    {
        $job_data   = json_decode($data, true);
        $JSESSIONID = $job_data['JSESSIONID'];
        $cookie     = 'JSESSIONID-L=' . $JSESSIONID . ';';
        $url        = 'https://gd.122.gov.cn/user/m/process/getProcessList';
        $data       = [
            'lsh'       => '',
            'wwlsh'     => '',
            'startTime' => date('Y-m-d', strtotime('-9 day')),
            'endTime'   => date('Y-m-d'),
        ];
        $result     = curl()->form_params()->set_cookies($cookie)->ignore_log(true)->post($url, $data)->get_body();
        $cache_key  = '12123:' . __FUNCTION__;
        $cache_md5  = cache($cache_key);
        if (is_array($result)) {
            $process_md5 = md5(json_encode($result, JSON_UNESCAPED_UNICODE));
            if ($cache_md5 != $process_md5) {
                //说明进度有更新
                wr_log('12123办事进度有更新,请注意查看~', 1);
                cache($cache_key, $process_md5, 3600 * 24 * 30);
            }
        } elseif (is_string($result) && strpos($result, '请打开交管12123App扫一扫登录') !== false) {
            wr_log('12123可能已掉线', 1);
        }
        $job->delete();
        return true;
    }

    /**
     *12123临牌进度
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function linpai(Job $job, $data)
    {
        $job_data   = json_decode($data, true);
        $JSESSIONID = $job_data['JSESSIONID'] ?? 'e174d930-ff92-4dcc-8839-353fc7ba27b3';
        $cookie     = 'JSESSIONID-L=' . $JSESSIONID . ';';
        $url        = 'https://gd.122.gov.cn/user/m/process/getProcess';
        $wwlsh      = '144002110095814666';
        $data       = ['ywlb' => '01', 'wwlsh' => $wwlsh];
        $result     = curl()->form_params()->set_cookies($cookie)->ignore_log(true)->post($url, $data)->get_body();
        $cache_key  = '12123:' . $wwlsh;
        $cache_md5  = cache($cache_key);
        if (is_array($result)) {
            $process_md5 = md5(json_encode($result, JSON_UNESCAPED_UNICODE));
            if ($cache_md5 != $process_md5) {
                //说明进度有更新
                wr_log('12123办事进度有更新,请注意查看~', 1);
                cache($cache_key, $process_md5, 3600 * 24 * 30);
            }
        } elseif (is_string($result) && strpos($result, '请打开交管12123App扫一扫登录') !== false) {
            wr_log('12123可能已掉线', 1);
        }
        $job->delete();
        return true;
    }

    /**
     *12123上牌
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function shangpai(Job $job, $data)
    {
        $job_data   = json_decode($data, true);
        $JSESSIONID = $job_data['JSESSIONID'] ?? 'e174d930-ff92-4dcc-8839-353fc7ba27b3';
        $cookie     = 'JSESSIONID-L=' . $JSESSIONID . ';';
        $url        = 'https://gd.122.gov.cn/veh1/netxh';
        $result     = curl()->set_cookies($cookie)->ignore_log(true)->get($url)->get_body();
        if (strpos($result, '你提交的购车发票照片车管所审核中，请耐心等待。审核结果将短信通知') !== false) {
            //todo 还在审核中
        } else if (strpos($result, '请提交购车发票照片，并等待车管所审核') !== false) {
            wr_log('12123已经变成了重新提交', 1);
        } else if (strpos($result, '请打开交管12123App扫一扫登录') !== false) {
            wr_log('12123可能已掉线', 1);
        } else {
            wr_log('12123可能已审核通过', 1);
        }
        $job->delete();
        return true;
    }

    public function ding_talk_check(Job $job, $data)
    {
        $user_list     = ['01535361695274' => '谢永发', '044248343325' => '王文娟'];
        $data          = [
            'userIdList' => array_keys($user_list)
        ];
        $dd            = new DingTalk();
        $result        = $dd->attendance_list($data);
        $record_result = $result['recordresult'];
        foreach ($user_list as $user_id => $user_name) {
            $is_check = false;
            foreach ($record_result as $record) {
                if ($record['userId'] == $user_id && time() - ($record['userCheckTime'] / 1000) < 3600) {
                    $is_check = true;
                    break;
                }
            }
            if (!$is_check) {
                send_ding_talk($user_name . '未打卡,请尽快打卡');
                //后续增加电话通知
            }
        }
        $job->delete();
        return true;
    }

    /**
     *长安车订单信息(新)
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function changan_car_order_beta(Job $job, $data)
    {
        $job_data   = json_decode($data, true);
        $unionid    = $job_data['unionid'] ?? 'o7sY81F3-5Lb3PSCMSDpHBJbjykY';
        $openid     = $job_data['openid'] ?? 'ol7KdjvN1XrUhuCq8TLu0VPDKdUg';
        $config     = ['unionid' => $unionid, 'openid' => $openid];
        $changan    = new ChangAnCar($config);
        $order_list = $changan->getOrderList();
        if (empty($order_list)) {
            throw new Exception('未查询到订单');
        }
        $data        = $order_list[0];
        $expDlryDate = $data['expDlryDate'];
        if (!tools()::is_time($expDlryDate)) {
            //不是时间格式无需处理
            $job->delete();
            return true;
        }
        $promiseDeliyDate = $data['promiseDeliyDate'];
        //$exp_dlry_date_day   = date('Y-m-d H:i:s', strtotime($expDlryDate)); //要车日期
        $exp_dlry_date_day = $expDlryDate;
        if (tools()::is_time($expDlryDate)) {
            $exp_dlry_date_day   = date('Y-m-d', strtotime($expDlryDate)); //要车日期
            $data['expDlryDate'] = $exp_dlry_date_day; //重新赋值 要车日期,因为更新太频繁,精确到天即可
        }
        if (tools()::is_time($promiseDeliyDate)) {
            $promiseDeliyDate = date('Y-m-d', strtotime($promiseDeliyDate)); //预计交车日期
        }
        $statusNode = $data['nowNode']['statusNode'];
        //      $status_info         = '【' . $statusNode . "】\r\n要车日期:" . $expDlryDate . "\r\n预计交期:" . $promiseDeliyDate;
        //      $cache_key           = 'changan_car_order_info:' . $unionid . ':' . $openid;
        //      $cache_status_info   = cache($cache_key);
        //      if ($cache_status_info != $status_info) {
        ////          $textcard = [
        ////              'title'       => '【购车订单更新】',
        ////              'description' => '长安fun购车订单有更新,请及时登录APP查看',
        ////          ];
        //          //$result   = qy_weixin_msg()->textcard($textcard)->send();
        //          wr_log($status_info, 1);
        //          $email_data  = [
        //              'account' => 'xyf',
        //              'send_to' => '<EMAIL>',
        //              'title'   => '【订单状态】更新通知',
        //              'subject' => $status_info,
        //          ];
        //          $send_result = send_email($email_data);
        //          $result      = Notify::channel('wechat')->send($status_info);
        //          cache($cache_key, $status_info, 3600 * 24 * 100);
        //      }
        //看订单整个消息
        $array_cache_key         = 'changan_car_order_info_array:' . $unionid . ':' . $openid;
        $cache_status_info_array = cache($array_cache_key);
        if ($cache_status_info_array != $data) {
            //          $textcard = [
            //              'title'       => '【ARRAY购车订单更新】',
            //              'description' => '长安fun购车订单有更新,请及时登录APP查看',
            //          ];
            //$result   = qy_weixin_msg()->textcard($textcard)->send();
            if (is_array($cache_status_info_array)) {
                $before_data        = tools()::array_diff_assoc2_deep($cache_status_info_array, $data);
                $before_data_string = json_encode($before_data, JSON_UNESCAPED_UNICODE);
                $after_data         = tools()::array_diff_assoc2_deep($data, $cache_status_info_array);
                $after_data_string  = json_encode($after_data, JSON_UNESCAPED_UNICODE);
                $key_word_map       = [
                    'statusNode'       => '订单状态',
                    'expDlryDate'      => '要车日期',
                    'promiseDeliyDate' => '预计交期',
                    'vinNo'            => '车架号',
                    'dcsSeriesName'    => '车型名称',
                ];
                foreach ($key_word_map as $key => $val) {
                    $before_data_string = str_replace($key, $val, $before_data_string);
                    $after_data_string  = str_replace($key, $val, $after_data_string);
                }
                $appid       = 'wx07ce368b922c9841';
                $openid      = 'ohg9BuLA04hHe899kwY0JNWweWuQ';
                $template_id = '9khum0pA9WONQ52Yr_Jy8o6fnr0C9K9n4zNyiT2o_IQ';
                //$url         = $changan->get_user_center_url();
                $url    = (string)url('index/plugins/redirect_changan_user_center_url', [], true, config('app.app_host_domain'));
                $wechat = weixin($appid)::WeChatTemplate();
                //{{first.DATA}}
                //更新时间：{{keyword1.DATA}}
                //订单类型：{{keyword2.DATA}}
                //订单状态：{{keyword3.DATA}}
                //订单来源：{{keyword4.DATA}}
                //订单详情：{{keyword5.DATA}}
                //{{remark.DATA}}
                $template = [
                    'touser'      => $openid,
                    'template_id' => $template_id,
                    'url'         => $url,
                    'data'        => [
                        'first'    => ['color' => '#666666', 'value' => '长安FAN购车订单更新啦!'],
                        'keyword1' => ['color' => '#000000', 'value' => format_timestamp()],
                        'keyword2' => ['color' => '#666666', 'value' => '长安购车订单'],
                        'keyword3' => ['color' => '#FF7F00', 'value' => '【' . $statusNode . "】\r\n要车日期:" . $exp_dlry_date_day . "\r\n预计交期:" . $promiseDeliyDate],
                        'keyword4' => ['color' => '#FF7F00', 'value' => $data['orderChannel']],
                        'keyword5' => ['color' => '#FF7F00', 'value' => '更新前:' . $before_data_string],
                        'remark'   => ['color' => '#FF7F00', 'value' => "更新后:" . $after_data_string],
                    ],
                ];
                $result   = $wechat->send($template);
                //              $email_data  = [
                //                  'account' => 'xyf',
                //                  'send_to' => '<EMAIL>',
                //                  'title'   => '【订单明细】更新通知',
                //                  'subject' => '更新前:' . json_encode($before_data, JSON_UNESCAPED_UNICODE) . '<br/>更新后:' . json_encode($after_data, JSON_UNESCAPED_UNICODE)
                //              ];
                //              $send_result = send_email($email_data);
                //              $status_info = '长安购车订单明细有更新,请注意查看邮件';
                //              $result      = Notify::channel('wechat')->send($status_info);
            }
            cache($array_cache_key, $data, 3600 * 24 * 100);
        }

        $job->delete();
        return true;
    }

    /**
     *监控微信服务市场任务列表
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return true
     * @throws Exception
     */
    public function weixin_fuwu_task(Job $job, $data)
    {
        $url      = 'https://fuwu.weixin.qq.com/ngi/batchGetServiceProviderSideTaskList';
        $db       = new WeixinFuwuTaskList();
        $job_data = json_decode($data, true);
        $limit    = $job_data['limit'] ?? 100;
        $offset   = $job_data['offset'] ?? 0;
        $data     = [
            'enterpriseOpenId' => "sq_ou0civ_pMVOeMMOk6mDwo8K3UH0k",
            'limit'            => $limit,
            'offset'           => $offset,
            'sortBy'           => 1,
            'sortDirection'    => 1,
            'type'             => [1],
        ];
        $cookie   = get_system_config('weixin_fuwu_market_cookie');
        $result   = curl()->ignore_log(true)->set_cookies($cookie)->post($url, $data)->get_body();
        if ($result['success'] !== true) {
            throw new Exception('微信服务市场监控异常:' . ($result['msg'] ?? '未知错误'));
        }
        $total_count = $result['data']['count'];
        $id_array    = [];
        foreach ($result['data']['rows'] as $row) {
            $task_info  = $row['taskInfo'];
            $task_id    = $task_info['taskId'];
            $id_array[] = $task_id;
        }
        $map            = [
            ['id', 'IN', $id_array],
        ];
        $exist_id_array = $db->where($map)->column('id');
        $insert_data    = [];
        foreach ($result['data']['rows'] as $row) {
            $task_info = $row['taskInfo'];
            $task_id   = $task_info['taskId'];
            $base_info = $task_info['baseInfo'];
            if (!in_array($task_id, $exist_id_array)) {
                $insert_data[] = [
                    'id'                              => $task_id,
                    'create_time'                     => format_timestamp($task_info['createTime']),
                    'audit_time'                      => format_timestamp($task_info['auditTime']),
                    'expired_time'                    => format_timestamp($task_info['expiredTime']),
                    'estimated_audit_completion_time' => format_timestamp($task_info['estimatedAuditCompletionTime']),
                    'base_info'                       => $base_info,
                    'description'                     => $base_info['description'],
                    'has_tel'                         => (int)$base_info['hasTel'],
                    'can_rob_count'                   => $row['canRobCount'],
                    'can_rob'                         => (int)$row['canRob'],
                    'cannot_rob_reason'               => $row['cannotRobReason'],
                    'status'                          => $task_info['status'],
                    'budget_lower'                    => $task_info['budgetLower'] ?? 0,
                    'budget_upper'                    => $task_info['budgetUpper'] ?? 99999999,
                    'subject_type'                    => $task_info['subjectType'],
                    'type'                            => $task_info['type'],
                    'insert_time'                     => format_timestamp(),
                    'update_time'                     => microsecond(),
                ];
            }
        }

        if ($insert_data) {
            $result = $db->saveAll($insert_data, false);
            foreach ($insert_data as $task) {
                wr_log('新的需求:' . $task['description'] . ';发布时间:' . $task['create_time']);
            }
        }

        if (count($insert_data) < $limit) {
            debug_log('没有新的需求');
        }
        // 如果当前$db_count < total_count，说明还有更多数据需要获取
        if ($db->count() < $total_count) {
            $next_offset   = $offset + $limit;
            $next_job_data = [
                'offset' => $next_offset,
            ];
            job()->set_sync_connections()->set_job_name('Monitor@weixin_fuwu_task')->push_job($next_job_data);
        }
        $job->delete();
        return true;
    }

    public function douyin_auto_auth(Job $job, $data)
    {
        $job_data           = json_decode($data, true);
        $cookie             = $job_data['cookie'];
        $not_auth_list      = [
            'open.life.to_store_all.tripartite.code'
        ];
        $auth_solution_list = [
            'open.life.to_store_all'
        ];
        $appId              = 'awckn1lzxrhy9vq1';
        $url                = 'https://partner.open-douyin.com/partner_api/developer_thirdparty_life/authority/openMerchantThirdpartyAppService/QueryTpAppAuthPermList?page=1&pageSize=10&appId=' . $appId . '&bizId=1';
        //$cookie        = 'i18next=en; store-region=cn-gd; store-region-src=uid; n_mh=9-mIeuD4wZnlYrrOvfzG3MuT6aQmCUtmr8FxV8Kl8xY; ttwid=1%7CpNKXH7bsuvQD6qxDBAd8-AXtWmx931pvDp6sVMsYdds%7C1700788509%7C0c3399200988c126c5fa8da144c133b60b8d6a61763a8f743ca985a8a0e725e1; _tea_utm_cache_2176=undefined; _tea_utm_cache_183818=undefined; x-jupiter-uuid=17028608057323378; gf_part_1042222=12; d_ticket=3f49f82dd97e2361c81d10dcad71d28ca5df6; odin_tt=decc8ec50812d28597d9c39a58a4894a5965d0c014db95c3da2acf40150fa6463be5f7efcc434c712b1743e72c0f37dc12a5a7157f13ea767f14d859257edc8e; sid_guard=4e75afa3d4fb6ade4db600762f66aa41%7C1702860827%7C5184000%7CFri%2C+16-Feb-2024+00%3A53%3A47+GMT; uid_tt=c7a079183785801bcb25d28c866fc2da; uid_tt_ss=c7a079183785801bcb25d28c866fc2da; sid_tt=4e75afa3d4fb6ade4db600762f66aa41; sessionid=4e75afa3d4fb6ade4db600762f66aa41; sessionid_ss=4e75afa3d4fb6ade4db600762f66aa41; sid_ucp_v1=1.0.0-KDc4Yjc2NTBkZWU0YWMwODgxOGNhNjdlNTNiOGVmM2EwZGE4N2M0ZGUKIAjT3cCBqY2TBxCbsP6rBhixyhQgDDDIiKSdBjgBQOoHGgJscSIgNGU3NWFmYTNkNGZiNmFkZTRkYjYwMDc2MmY2NmFhNDE; ssid_ucp_v1=1.0.0-KDc4Yjc2NTBkZWU0YWMwODgxOGNhNjdlNTNiOGVmM2EwZGE4N2M0ZGUKIAjT3cCBqY2TBxCbsP6rBhixyhQgDDDIiKSdBjgBQOoHGgJscSIgNGU3NWFmYTNkNGZiNmFkZTRkYjYwMDc2MmY2NmFhNDE; s_v_web_id=verify_lqd7vgsa_SAo3cd3t_OTuM_4Bra_9hPU_e0xoIUp5ACPP; csrf_session_id=5a8b4aaf8b94c1d98159260651dbd119';
        $result = curl()->set_cookies($cookie)->get($url)->ignore_log()->get_body();
        if (isset($result['error']) && $result['error'] == 1) {
            $msg = $result['message'] ?? '未知错误';
            wr_log('抖音请求失败:' . $msg, 1);
            $job->delete();
            return true;
        }
        if (empty($result['data']) || empty($result['data']['AuthList'])) {
            //            wr_log('抖音返回null', 1);
            //            {
            //    "data": {
            //   "AuthList": [],
            //		"Total": 0
            //	},
            //	"code": 4,
            //	"message": "啊哦，服务器打瞌睡了"
            //}
            wr_log($result);
            $job->delete();
            return true;
        }
        $AuthList = $result['data']['AuthList'];
        foreach ($AuthList as $key => $value) {
            $AuthStatus = $value['AuthStatus'];
            if ($AuthStatus == 20) {
                $BizID        = $value['BizID'];
                $BizKey       = $value['BizKey'];
                $BizName      = $value['BizName'];
                $SolutionList = $value['SolutionList'];
                foreach ($SolutionList as $Solution) {
                    $SolutionKey = $Solution['SolutionKey'];
                    if (!in_array($SolutionKey, $auth_solution_list)) {
                        continue;
                    }
                    $authPermList = [];
                    foreach ($Solution['PermissionList'] as $Permission) {
                        $PermissionKey        = $Permission['PermissionKey'];
                        $PermissionAuthStatus = $Permission['AuthStatus'];
                        if (!in_array($PermissionKey, $not_auth_list) && $PermissionAuthStatus == 20) {
                            $authPermList[] = [
                                'permissionKey' => $PermissionKey,
                                'authStatus'    => 30
                            ];
                        }
                    }
                    if (!empty($authPermList)) {
                        $data   = [
                            'appId'            => $appId,
                            'bizId'            => $BizID,
                            'bizKey'           => $BizKey,
                            'authSolutionList' => [['solutionKey' => $SolutionKey, 'authPermList' => $authPermList]]
                        ];
                        $url    = 'https://partner.open-douyin.com/platform_api/v1/developer_thirdparty_life/authority/commit';
                        $result = curl()->set_cookies($cookie)->post($url, $data)->get_body();
                        wr_log($result);
                        wr_log($BizName . '自动授权完成! 抖音商户ID:' . $BizKey, 1);
                    }
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *长安车订单信息
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function changan_car_order(Job $job, $data)
    {
        $job_data = json_decode($data, true);
        $max_time = $job_data['max_time'] ?? 2100;
        $min_time = $job_data['min_time'] ?? 800;
        $time     = intval(date('Hi'));
        if ($time > $max_time || $time < $min_time) {
            // 晚上9点到早上8点不执行
            $job->delete();
            return true;
        }
        $login_url = 'https://service.changan.com.cn/changan-b2c-web-h5/#/inquiryService';
        $token     = get_system_config('changan_token');
        $url       = 'https://scrm.changan.com.cn/scrm-app-web/tmjc/generateSignature';
        $data      = ['sys' => 'cafan_app', 'token' => $token];
        $result    = curl()->form_params()->setMaxRetries(3)->setRetryDecider(function ($result) {
            return (is_array($result) && isset($result['result']) && $result['result'] === 0) === false;
        })->post($url, $data)->get_body();
        if ($result['result'] !== 0) {
            $msg    = 'generateSignature 错误,token可能已失效';
            $result = Notify::channel('wechat')->send($msg);
            $job->delete();
            return true;
            //throw new \Exception('generateSignature 错误,token:' . $token . ' ' . $login_url);
        }
        $data     = [
            'sys'            => 'cafan_app',
            'sign'           => $result['data']['sign'],
            'customerMobile' => $result['data']['customerMobile'],
            'echo'           => $result['data']['echo'],
            'time'           => $result['data']['time'],
        ];
        $signInfo = urlencode(json_encode($data));
        $url      = 'http://cvsses.changan.com.cn/tmjc/orderList?customerMobile=' . $result['data']['customerMobile'] . '&isHistory=false&keyWord=&searchType=customerMobile&pageSize=2&signInfo=' . $signInfo;
        $result   = curl()->get($url)->get_body();
        if ($result['code'] !== '0') {
            $msg    = 'orderList 错误,token可能已失效';
            $result = Notify::channel('wechat')->send($msg);
            $job->delete();
            return true;
            // throw new \Exception('orderList 错误,token:' . $token . ' ' . $login_url);
        }
        $data        = $result['data'][0];
        $expDlryDate = $data['expDlryDate'];
        if ($expDlryDate == '暂无') {
            //个别情况暂无无需进行处理
            $job->delete();
            return true;
        }
        $promiseDeliyDate = $data['promiseDeliyDate'];
        $expDlryDate      = date('Y-m-d', strtotime($expDlryDate));
        $promiseDeliyDate = date('Y-m-d', strtotime($promiseDeliyDate));
        $statusNode       = $data['nowNode']['statusNode'];
        $status_info      = '【' . $statusNode . "】\r\n要车日期:" . $expDlryDate . "\r\n预计交期:" . $promiseDeliyDate;
        //$md5              = md5(json_encode($result['data'][0]));
        $cache_key         = 'changan_car_order_info';
        $cache_status_info = cache($cache_key);
        if ($cache_status_info != $status_info) {
            $textcard = [
                'title'       => '【购车订单更新】',
                'description' => '长安fun购车订单有更新,请及时登录APP查看',
            ];
            //$result   = qy_weixin_msg()->textcard($textcard)->send();
            wr_log($status_info, 1);
            $email_data  = [
                'account' => 'xyf',
                'send_to' => '<EMAIL>',
                'title'   => '【长安购车订单更新】',
                'subject' => $status_info,
            ];
            $send_result = send_email($email_data);
            $result      = Notify::channel('wechat')->send($status_info);
            cache($cache_key, $status_info, 3600 * 24 * 100);
        }
        $job->delete();
        return true;
    }


    /**
     *深圳疾控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function shenzhen_jikong(Job $job, $data)
    {
        $data    = json_decode($data, true);
        $token   = $data['token'] ?? '-t-dDaXmSDniiK6J97lK8P9UeHqt__OkIdDySGxpaBRvH27iVnoo8QLIA';
        $depa_id = $data['depa_id'] ?? '3F4BC752-E38A-C4BE-4EA4-0DE0AE52FD0D'; //锦绣御园
        $date    = $data['date'] ?? '2021-07-08';
        $url     = 'https://imm.szcdc.net/mobile/reservationStock/timeNumber';
        $header  = ['token' => $token];
        $data    = ['depaId' => $depa_id, 'date' => $date];
        $result  = curl()->ignore_log(true)->set_header($header)->get($url, $data)->get_body();
        switch ($result['ecode']) {
            case '1000': //成功
                if (is_array($result['data'])) {
                    foreach ($result['data'] as $data) {
                        if ($data['restSurplus'] > 0) {
                            break; //暂时不自动预约
                            // "restId": "645229844510400513",
                            // "depaId": "3F4BC752-E38A-C4BE-4EA4-0DE0AE52FD0D",
                            // "restDate": "2021-09-18 00:00:00",
                            // "ouatId": "3802",
                            // "restSum": 3,
                            // "restSurplus": 1,
                            // "ouatBeginTime": "08:00",
                            // "ouatEndTime": "08:30"

                            //预约
                            $url    = 'https://imm.szcdc.net/mobile/reservation/saveAppointment';
                            $data   = [
                                'reucIds'  => json_encode([$data['restId']]),
                                'depaId'   => $data['depaId'],
                                'ouatId'   => $data['ouatId'],
                                'vaccCode' => '1702',
                                'date'     => date('Y-m-d', strtotime($data['restDate'])),
                            ];
                            $result = curl()->set_header($header)->post($url, $data)->get_body();

                            //   {
                            //   "reucIds": ["610425497524236288"],
                            //  "depaId": "3F4BC752-E38A-C4BE-4EA4-0DE0AE52FD0D",
                            //  "date": "2021-09-18",
                            //  "ouatId": "3802",
                            //  "vaccCode": "1702"
                            //}
                            $msg = $data['ouatBeginTime'] . '-' . $data['ouatEndTime'] . '剩余:' . $data['restSurplus'] . '个号源,预约成功';
                            wr_log($msg, 1);
                            break;
                        }
                    }
                }
                wr_log('深圳疾控有号可预约,请及时查看', 1);
                $job->delete();
                return true;
            case '201001001': // 会话超时
                wr_log('深圳疾控' . $result['msg'], 1);
                $job->delete();
                return true;
            case '201001308': // 该时间段预约号数已约满
                $job->delete();
                return true;
        }
        //如果不是可预约就删除
        $job->delete();
        return true;
    }


    /**
     *监控公告
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function announcement(Job $job, $data)
    {
        $data            = json_decode($data, true);
        $copy_to         = $data['copy_to'] ?? '';
        $email_account   = $data['email_account'] ?? 'xyf';
        $send_to         = $data['send_to'] ?? '<EMAIL>';
        $yikayi_code     = '430671';
        $base_url        = 'http://www.neeq.com.cn';
        $url             = $base_url . '/disclosureInfoController/infoResult_zh.do?callback=jsonp';
        $data            = [
            'page'       => 0,
            'companyCd'  => $yikayi_code,
            'isNewThree' => 1,
            'keyword'    => '',
            'sortfield'  => 'xxssdq',
            'sorttype'   => 'asc',
        ];
        $result          = curl()->form_params()->ignore_log(true)->post($url, $data)->get_body();
        $result          = current(tools()::jsonp_decode($result));
        $listInfo        = $result['listInfo'];
        $total           = $listInfo['totalElements'];
        $total_cache_key = __FUNCTION__ . ':' . $yikayi_code . ':total';
        $cache_total     = (int)cache($total_cache_key);
        $content_list    = [];
        if ($cache_total != $total) {
            //数量不一致说明有新的公告
            foreach ($listInfo['content'] as $content) {
                $content_key = $total_cache_key . ':' . $content['disclosureCode'];
                if (cache($content_key)) {
                    break;
                }
                $company_name = $content['companyName'];
                $company_code = $content['companyCd'];
                $title        = $content['disclosureTitle'];
                //$publish_time   = format_timestamp((int)($content['upDate']['time'] * 0.001));
                $publish_time   = $content['publishDate'];
                $pdf_url        = $base_url . $content['destFilePath'];
                $content_list[] = [
                    'company_name' => $company_name,
                    'company_code' => $company_code,
                    'title'        => $title,
                    'publish_time' => $publish_time,
                    'pdf_url'      => $pdf_url,
                ];
                cache($content_key, format_timestamp());
            }
        }
        cache($total_cache_key, $total);
        $hengbao_code    = '002104';
        $url             = 'http://www.szse.cn/api/disc/announcement/annList?random=' . time();
        $data            = [
            'channelCode' => ['listedNotice_disc'],
            'pageNum'     => 1,
            'pageSize'    => 30,
            'seDate'      => ['', ''],
            'stock'       => [$hengbao_code],
        ];
        $data            = json_encode($data);
        $result          = curl()->ignore_log(true)->post($url, $data)->get_body();
        $total           = $result['announceCount'];
        $total_cache_key = __FUNCTION__ . ':' . $hengbao_code . ':total';
        $cache_total     = (int)cache($total_cache_key);
        if ($cache_total != $total) {
            //数量不一致说明有新的公告
            foreach ($result['data'] as $content) {
                $content_key = $total_cache_key . ':' . $content['id'];
                if (cache($content_key)) {
                    break;
                }
                $company_name   = current($content['secName']);
                $company_code   = current($content['secCode']);
                $title          = $content['title'];
                $publish_time   = $content['publishTime'];
                $disk_url       = 'http://disc.static.szse.cn/download/';
                $pdf_url        = $disk_url . $content['attachPath'];
                $content_list[] = [
                    'company_name' => $company_name,
                    'company_code' => $company_code,
                    'title'        => $title,
                    'publish_time' => $publish_time,
                    'pdf_url'      => $pdf_url,
                ];
                cache($content_key, format_timestamp());
            }
        }
        cache($total_cache_key, $total);
        if (!empty($content_list)) {
            $send_email_title = '股转系统有' . count($content_list) . '份新的公告';
            $html             = "<table cellspacing='0' style='width:100%;border-collapse: collapse;text-align: center;border:none;font-size:11pt'>";
            //          $html     .= "<tr><td colspan='10' style='text-align:center;font-size:24px;font-weight:200'>".$send_email_title."</td></tr>";
            $html     .= "<tr style='background-color:#00B050;color:#FFF;height:40px;'><th>序号</th><th>公司名称</th><th>标题</th><th>日期</th><th>PDF链接</th></tr>";
            $td_style = 'border: solid #000 1px;';
            foreach ($content_list as $key => $val) {
                $background_color = ($key % 2 == 0) ? '#FFFFFF' : '#dbf4e6';
                $now_key          = ++$key;
                $html             .= "<tr style='background-color:$background_color'>";
                $html             .= "<td style='$td_style'>" . $now_key . "</td>";
                $html             .= "<td style='$td_style'>" . $val['company_name'] . "</td>";
                $html             .= "<td style='$td_style'>" . $val['title'] . "</td>";
                $html             .= "<td style='$td_style'>" . $val['publish_time'] . "</td>";
                $pdf_url_html     = '<a target="_blank" href=' . $val['pdf_url'] . '>查看PDF</a>';
                $html             .= "<td style='$td_style'>" . $pdf_url_html . "</td>";
                $html             .= "</tr>";
            }
            //$html .= "<tr><td colspan='6' style='text-align:center;font-size: 36px;font-weight:200'><a href='$url' target='_blank'> 处理完毕</a></td></tr>";
            $html        .= "</table >";
            $email_data  = [
                'account' => $email_account,
                'send_to' => $send_to,
                'title'   => $send_email_title,
                'subject' => $html,
                'copy_to' => $copy_to
            ];
            $send_result = send_email($email_data);
            wr_log($send_email_title, 1);
        }
        $job->delete();
        return true;
    }

    /**
     *社康号源监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function newhealth3(Job $job, $data)
    {
        $data       = json_decode($data, true);
        $ignore_log = true;
        $token      = $data['token'];
        $outpName   = $data['name'] ?? '';
        $url        = 'https://xgsz.szcdc.net/crmobile/outpatient/nearby';
        $data       = [
            'pageNum'          => 1,
            'numPerPage'       => 10,
            'areaCode'         => '440309',
            'bactCode'         => '5601',
            'outpName'         => $outpName,
            'outpMapLongitude' => '',
            'outpMapLatitude'  => '',
            'corpCode'         => '',
        ];
        $header     = [
            'appId'            => 'app569d18f5',
            'selfAppId'        => 'wx262c00273afab5c7',
            'token'            => $token,
            'reservationToken' => '9c62f720e5d04b219177f61f9e705450',
        ];
        $result     = curl()->ignore_log($ignore_log)->form_params()->set_header($header)->post($url, $data)->get_body();
        if ($result['ecode'] !== '1000') {
            throw new Exception($result['msg']);
        }
        $msg = '';
        if (!empty($result['data']) && !empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $data) {
                if ($data['status'] == 1) {
                    $msg .= $data['outpName'] . "\r\n";
                    //                  send_qy_wechat(json_encode($data));
                }
            }
        }
        if ($msg) {
            $msg .= ':有号源,请及时关注!!';
            wr_log($msg, 1);
        }
        $job->delete();
        return true;
    }

    /**
     *社康号源监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function yimiao(Job $job, $data)
    {
        $url  = 'https://imm.szcdc.net/mobileSz/outpatient/nearby';
        $ua   = 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.31(0x18001f28) NetType/WIFI Language/zh_CN';
        $data = [
            'pageNum'          => 1,
            'numPerPage'       => 10,
            'areaId'           => '440309',
            'outpName'         => '',
            'reucId'           => '797461548838047744',
            'vaccCode'         => '5502',
            'ageType'          => 2,
            'outpMapLatitude'  => '',
            'outpMapLongitude' => '',
        ];

        $headers = [
            'token' => get_system_config('shekang_token'),
            //            'token' => '-t--',
            //            'otn'            => 'l8bBWvfli5zut9CeP60e4vdtdcCrNZj6fGuUDzwcPxa6Jl2+G/ocz8tu0a/nd1K0EH0x4RHYwq2qUcJ5AQ6N79FjXx+xDUHcR6oLmY8MSk/FN58xS30jBvvN8jlB0PzO',
            //            'ybm'            => 'BZd2N8Cn4/RLi5h335cHZjOhDqVR31NBHWo+YpiwIyU=',
            //            'effectivetoken' => 'ybzl',
            //            'Appid'          => 'app569d18f5',
            //            'User-Agent'     => $ua,
            //            'Content-Type'   => 'application/json',
            //            //            'Accept'          => 'application/json, text/plain, */*"',
            //            //            'Accept-Language' => 'zh-CN,zh-Hans;q=0.9',
            //            'Host'           => 'imm.szcdc.net',
            //            'Referer'        => 'https://imm.szcdc.net//selfMerge/?appId=wx5402a9708b90332e&param=5'
        ];
        $result  = curl()->set_header($headers)->post($url, $data)->get_body();
        if ($result['ecode'] == '1000') {
            foreach ($result['data']['list'] as $key => $val) {
                $nums = $val['nums'];
                $name = $val['outpName'];
                if ($nums > 0) {
                    wr_log($name . '可预约 https://imm.szcdc.net//selfMerge/?appId=wx5402a9708b90332e&param=5#/appointment/outpAdressList?chargeType=2', 1);
                }
            }
            wr_log('监控成功,目前没有号源');
        } else {
            wr_log($result['msg'] ?? '模拟登录失败', 1);
        }
        $job->delete();
        return true;
    }

    /**
     *社康号源监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function newhealth2(Job $job, $data)
    {
        $data       = json_decode($data, true);
        $ignore_log = true;
        $token      = $data['token'];
        $url        = 'https://szhc.newhealth.com.cn/dsmh/service?function=vaccineorder&method=vaccorglist&visitcode=Pi0ynNDpmTJMf87i&token=' . $token;
        $data       = [
            'vaccCrowd'  => '3',
            'pid'        => 605572,
            'areaId'     => '440309',
            'numPerPage' => 100,
            'pageNum'    => 1,
            'vaccCode'   => '5601',
            'corpCode'   => 'all',
            'outpName'   => '锦绣御园',
        ];
        $result     = curl()->ignore_log($ignore_log)->json()->post($url, $data)->get_body();
        if ($result['ret'] !== 1) {
            throw new Exception($result['message']);
        }
        if (!empty($result['data']) && !empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $data) {
                if ($data['status'] == 1) {
                    wr_log($data['outpName'] . ':有号源,请及时关注', 1);
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *社康号源监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function newhealth(Job $job, $data)
    {
        $data       = json_decode($data, true);
        $ignore_log = true;
        $token      = $data['token'];
        $url        = 'https://szhc.newhealth.com.cn/dsmh/service?function=regorderpool&method=nucleinHQ001&visitcode=Pi0ynNDpmTJMf87i&token=' . $token;
        $data       = ['orgUuid' => '************', 'deptAttribute' => '4'];
        $result     = curl()->ignore_log($ignore_log)->json()->post($url, $data)->get_body();
        if ($result['ret'] !== 1) {
            throw new Exception($result['message']);
        }
        if (!empty($result['data'])) {
            foreach ($result['data']['deptOderArray'] as $data_array) {
                foreach ($data_array as $key => $val) {
                    $array = ['morning', 'afternoon', 'night'];
                    if (in_array($key, $array)) {
                        foreach ($val as $list_key => $list_val) {
                            if (!empty($list_val)) {
                                $left = (int)$list_val['amountTotal'] - (int)$list_val['amountUsed'];
                                if ($left > 0) {
                                    wr_log($list_val['sdate'] . '-' . $key . '-剩余:' . $left . '个号源', 1);
                                }
                            }
                        }
                    }
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *layui版本监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function layui_version(Job $job, $data)
    {
        $url          = 'https://layui.dev/docs/2.8/versions.html';
        $result       = curl()->ignore_log()->set_timeout(5)->get($url)->get_body();
        $last_version = tools()::search_str('/layui/v/', '/css', $result);
        $cache_key    = 'layui_last_version';
        $cache_value  = cache($cache_key);
        if ($last_version != $cache_value && version_compare($last_version, $cache_value) > 0) {
            wr_log('layui 已发布新版本:' . $last_version, 1);
            cache($cache_key, $last_version, 3600 * 24 * 31);
        }
        $job->delete();
        return true;
    }

    /**
     *建行余额监控(监控余额+最后一笔转账记录)
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function ccb_account_balance(Job $job, $data)
    {
        $data                             = json_decode($data, true);
        $ccb_account_monitor_url_key_name = 'ccb_account_monitor_url';
        $ccb_account_monitor_url          = get_system_config($ccb_account_monitor_url_key_name);
        if (empty($ccb_account_monitor_url)) {
            $job->delete();
            return true;
        }
        $result = curl()->get($ccb_account_monitor_url)->get_body();
        if (!empty($result['DOCUMENT']['BODY']['ERRORMSG'])) {
            //            {"DOCUMENT":{"BODY":{"SUCCESS":"false","ERRORCODE":"0130Z0100001","ERRORMSG":"交易结果不确定，请核查账户确认是否成功。如有疑问，请咨询在线客服或致电95533。交易状态不确定"}}}
            //{"DOCUMENT":{"BODY":{"SUCCESS":"false","ERRORCODE":"0130Z1108007","ERRORMSG":"暂时未能处理您的请求，请重新登录。如有疑问，请咨询在线客服或致电95533。"}}}
            $err_code = $result['DOCUMENT']['BODY']['ERRORCODE'];
            $msg      = '建行网银登录失败:' . $result['DOCUMENT']['BODY']['ERRORMSG'];
            wr_log($msg, 1);
            if ($err_code == '0130Z0100001') {
                $job->delete();
                return true;
            }
            $db = new SystemConfig();
            $db->update_value($ccb_account_monitor_url_key_name, 0);
            //$this->stop_crontab($data);
            $job->delete();
            return true;
        } elseif (!empty($result['DOCUMENT']['QUERYORDER'][0])) {
            $first_bank_info = $result['DOCUMENT']['QUERYORDER'][0]; //最新一条记录
            $account_number  = $result['DOCUMENT']['BODY']['ACC_NO']; //账户
            $balance         = $first_bank_info['BALANCE']; //余额
            $to_account      = $first_bank_info['TO_ACC_NM']; //对方用户名
            $mark            = $first_bank_info['MARK']; //转账备注

            $cache_key   = 'bank_balance:' . $account_number;
            $cache_value = cache($cache_key);
            if ($cache_value != $balance) {
                //余额有变化
                $description = '';
                $cache_value = floatval($cache_value);
                $diff_money  = tools()::nc_price_calculate($balance, '-', $cache_value, 2);
                $diff        = sprintf("%+d", $diff_money);
                $description .= "变动: " . $diff . "元\n";
                $description .= '当前余额: ' . $balance . "\n";
                $description .= $to_account . '(' . $mark . ")\n";
                $textcard    = [
                    'title'       => '【建行余额变动】通知(' . $diff . ')',
                    'description' => $description,
                ];
                $result      = qy_weixin_msg()->textcard($textcard)->send();
                cache($cache_key, $balance, 3600 * 24 * 365);
                $ios_push_data = [
                    'title' => '建行余额变动',
                    'body'  => $diff . '元,' . $to_account . '(' . $mark . ')'
                ];
                ios_push($ios_push_data);
            }
        } else {
            wr_log('建行网银未知错误');
        }
        $job->delete();
        return true;
    }

    /**
     *建行余额监控(仅监控余额)
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function ccb_account_balance_only(Job $job, $data)
    {
        $data                             = json_decode($data, true);
        $ccb_account_monitor_url_key_name = 'ccb_account_monitor_url';
        $ccb_account_monitor_url          = get_system_config($ccb_account_monitor_url_key_name);
        if (empty($ccb_account_monitor_url)) {
            $job->delete();
            return true;
        }
        $result = curl()->get($ccb_account_monitor_url)->get_body();
        if (!empty($result['DOCUMENT']['BODY']['ERRORMSG'])) {
            $msg = '建行网银登录失败:' . $result['DOCUMENT']['BODY']['ERRORMSG'];
            $db  = new SystemConfig();
            $db->update_value($ccb_account_monitor_url_key_name, 0);
            wr_log($msg, 1);
            //$this->stop_crontab($data);
            $job->delete();
            return true;
        } elseif (!empty($result['DOCUMENT']['QUERYORDER'][0])) {
            $first_bank_info = $result['DOCUMENT']['QUERYORDER'][0];
            $balance         = $first_bank_info['AVL_BAL'];
            $account_number  = $first_bank_info['ACC_NO'];
            $flag            = $first_bank_info['FLAG'];
            if ($flag == 2 && empty($balance)) {
                //间歇性异常 做兼容处理
                $job->delete();
                return true;
            }
            $cache_key   = 'bank_balance:' . $account_number;
            $cache_value = cache($cache_key);
            if ($cache_value != $balance) {
                //余额有变化
                $description = '';
                $cache_value = floatval($cache_value);
                $diff_money  = tools()::nc_price_calculate($balance, '-', $cache_value, 2);
                $diff        = sprintf("%+d", $diff_money);
                //                $description .= '上次金额: ' . $cache_value . "\n";
                $description .= "变动: " . $diff . "元\n";
                $description .= '当前余额: ' . $balance . "\n";
                $textcard    = [
                    'title'       => '【建行余额变动】通知(' . $diff . ')',
                    'description' => $description,
                ];
                $result      = qy_weixin_msg()->textcard($textcard)->send();
                cache($cache_key, $balance, 3600 * 24 * 365);
                $ios_push_data = [
                    'title' => '建行网银余额变动',
                    'icon'  => 'https://' . config('app.app_host_domain') . '/static/img/app_icon/ccb.jpg',
                    'url'   => 'ccbcompanybank://',
                    'body'  => "变动:" . $diff . "元\n余额:" . $balance . '元'
                ];
                ios_push($ios_push_data);
            }
        } else {
            wr_log('建行网银未知错误');
        }
        $job->delete();
        return true;
    }

    /**
     *净值监控
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function net_worth(Job $job, $data)
    {
        $data           = json_decode($data, true);
        $ignore_log     = false;
        $base_url       = 'https://wap.bank.ecitic.com/NMBFOServer';
        $business       = $data['business'];
        $key            = $data['key'];
        $url_key        = $data['url_key'];
        $number         = $data['number'];
        $cache_key      = 'net_worth:' . md5($url_key);
        $url            = $base_url . '/MobileBankWeb/?index=Share.FinancialShare.share&key=' . $url_key;
        $cookies        = curl()->ignore_log(true)->get($url)->get_cookies();
        $url            = $base_url . '/api.do?act=PEMBPKQY&isWeb=1';
        $cookies2       = curl()->ignore_log($ignore_log)->json()->set_cookies($cookies)->post($url)->get_cookies();
        $data           = [
            'dataPackage' => json_encode([
                //'business' => json_decode($body['dataPackage']['business'], true)['MODULUS'],
                'business' => $business,
                'key'      => $key
            ])
        ];
        $url            = $base_url . '/api.do?act=PENFHNWQ&isWeb=1';
        $result         = curl()->ignore_log($ignore_log)->json()->set_cookies($cookies . $cookies2)->post($url, $data)->get_body();
        $business       = json_decode($result['dataPackage']['business'], true);
        $last_value     = current($business['resultList']);
        $cache_value    = cache($cache_key);
        $exp            = 7 * 24 * 3600;
        $last_open_date = $last_value['OPENDATE'];
        $last_net_worth = $last_value['NETWORTH'];
        if (is_array($cache_value)) {
            //如果存在缓存 则比较是否开放日期有更新
            $last_cache_open_date = $cache_value['OPENDATE'];
            if ($last_open_date != $last_cache_open_date) {
                // 最后开放日发生变更,比较差异
                $description     = '';
                $cache_net_worth = $cache_value['NETWORTH'];
                $cache_money     = tools()::nc_price_calculate($cache_net_worth, '*', $number, 2);
                $last_money      = tools()::nc_price_calculate($last_net_worth, '*', $number, 2);
                $diff_money      = tools()::nc_price_calculate($last_money, '-', $cache_money, 2);
                $description     .= '上次价值: ' . $cache_money . " (" . $last_cache_open_date . ")\n";
                $description     .= "变动: " . sprintf("%+d", $diff_money) . "元\n";
                $description     .= '最新价值: ' . $last_money . " (" . $last_open_date . ")\n";
                $textcard        = [
                    'title'       => '【净值变动】理财净值变动通知',
                    'description' => $description,
                ];
                $result          = qy_weixin_msg()->textcard($textcard)->send();
                cache($cache_key, $last_value, $exp);
            }
        } else {
            $description = '';
            $last_money  = tools()::nc_price_calculate($last_net_worth, '*', $number, 2);
            $description .= '最新价值: ' . $last_money . " (" . $last_open_date . ")\n";
            $textcard    = [
                'title'       => '【净值变动】理财净值变动通知',
                'description' => $description,
            ];
            $result      = qy_weixin_msg()->textcard($textcard)->send();
            cache($cache_key, $last_value, $exp);
        }
        $job->delete();
        return true;
    }

    /**
     *监控域名http证书到期时间
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return true
     * @throws Exception
     */
    public function ssl_expire(Job $job, $data)
    {
        $data    = json_decode($data, true); //后期可以传入域名 提前天数等字段
        $domains = [
            'www.yikayi.net',
            'file.yikayi.net',
            'chat.yikayi.net',
            // 添加更多的域名到这里
        ];
        foreach ($domains as $domain) {
            $certInfo = tools()::get_certificate_info($domain);
            if ($certInfo) {
                // 计算剩余天数
                $validToDateTime = new DateTime($certInfo['validTo']);
                $currentDateTime = new DateTime();
                $remainingDays   = $currentDateTime->diff($validToDateTime)->days;
                if ($remainingDays <= 7) {
                    wr_log('域名【' . $domain . '】 https证书还有' . $remainingDays . '天过期,请及时处理', 1);
                }
            } else {
                wr_log('域名【' . $domain . '】 https证书信息获取失败!', 1);
            }
        }
        $job->delete();
        return true;
    }

    /**
     *访问URL
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function curl(Job $job, $data)
    {
        $data = json_decode($data, true);
        try {
            $method    = isset($data['method']) ? $data['method'] : 'get';
            $parameter = isset($data['parameter']) ? $data['parameter'] : [];
            curl()->$method($data['url'], $parameter)->get_body();
            $job->delete();
            return true;
        } catch (Exception $e) {
            wr_log('定时访问url异常:' . $e->getMessage() . 'data:' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     *京东自动保价
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function baojia(Job $job, $data)
    {
        $data      = json_decode($data, true);
        $cookie    = $data['cookie'];
        $post_data = $data['data'] ?? [];
        $params    = $data['params'] ?? '';
        $url       = 'https://api.m.jd.com?' . $params;

        //        $url      = 'https://api.m.jd.com/api?appid=siteppM&functionId=siteppM_skuOnceApply&forcebot=&t=1701832577365&x-api-eid-token=jdd037FZCNWJQURNZTQYH7LLDERIGEZ4EQFHGDNXP6RQ24YOMDGPC63QSEF5WODIEVOHK4FTBQDN2VD2Q6Y2EF76VRTQD3YAAAAMMHUQCYIYAAAAADNIQSHQHCDIWBIX';
        //        $cookie   = '__jdu=16919755887571451529675; shshshfpa=9ea20612-a027-de45-2567-0dc17d2ea172-1692578680; shshshfpx=9ea20612-a027-de45-2567-0dc17d2ea172-1692578680; mba_muid=16919755887571451529675; 3AB9D23F7A4B3C9B=7FZCNWJQURNZTQYH7LLDERIGEZ4EQFHGDNXP6RQ24YOMDGPC63QSEF5WODIEVOHK4FTBQDN2VD2Q6Y2EF76VRTQD3Y; autoOpenApp_downCloseDate_auto=1701067571966_1800000; __jdv=122270672%7Cdirect%7C-%7Cnone%7C-%7C1701067572502; wxa_level=1; cid=9; jxsid=17018318612910846331; appCode=ms0ca95114; webp=1; __jda=122270672.16919755887571451529675.1691975589.1701067572.1701831861.8; __jdc=122270672; visitkey=8183986405457049082; autoOpenApp_downCloseDate_jd_homePage=1701831862226_1; retina=1; cd_eid=jdd037FZCNWJQURNZTQYH7LLDERIGEZ4EQFHGDNXP6RQ24YOMDGPC63QSEF5WODIEVOHK4FTBQDN2VD2Q6Y2EF76VRTQD3YAAAAMLNGAXSAIAAAAACLHCR3A5TS77NUX; equipmentId=7FZCNWJQURNZTQYH7LLDERIGEZ4EQFHGDNXP6RQ24YOMDGPC63QSEF5WODIEVOHK4FTBQDN2VD2Q6Y2EF76VRTQD3Y; fingerprint=f029dac19a92d41dc3c5c888a31ec99a; deviceVersion=*********; deviceOS=android; deviceOSVersion=6.0; deviceName=Chrome; sc_width=400; PPRD_P=UUID.16919755887571451529675; jxsid_s_u=https%3A//home.m.jd.com/myJd/newhome.action; jcap_dvzw_fp=X0R3qsUZVhw_K4O3TOllIEAMAUue7vJYD6NH7GMTUvc63lNfdFjUKDHW2Vg8ANrqehIp1u7M1SRuNm_k_6NH4Q==; whwswswws=; lg-p=7V9F0mAfvuG2LJ%2B5l8f7kQ%3D%3D; lg-s=ng; shshshsID=e6c02ed8d710c0f18c1834a178f06f77_8_1701832135578; TrackerID=upBXUBgX6Q5JxhA30ot-xw02xqJXTicIBxmWjc_G8NZMUssF10jitP0IKL9wzHD9PzDdxCmJva0MSBDlOybTELVfY_JJRXIgD3B1l1sH-SMwbZ8RwCsAzKBm-WkSB8Y3cZVDggctc17CZ8xGR4gq5A; pt_key=AAJlb-XhADDVoYs2AX2jhJ423iJ1Bhkgl5Ym5NzXe8SRy0l3LEWl8qOTMkM5myh_dOy6SLSncxk; pt_pin=xieyongfa123; pt_token=ptg9busy; pwdt_id=xieyongfa123; sfstoken=tk01mbada1b36a8sMyszWlNwMit0GthQcT9xiLUCqSc/8fY+1LBMBYzef+zUpT6/dDS6LSwS5+GV0E74e3MGOdDUCGXf; wqmnx1=MDEyNjM3M3BtLkpvaTg1MGwgO2kgNS8pVzUoIGVoMTBlaTYxMHI0YTRWVUhGSA%3D%3D; __wga=1701832162601.1701831881225.1701831881225.1701831881225.2.1; jxsid_s_t=1701832162673; shshshfpb=AArUAGj2MEqIGEqAn3kUlZw3BfS6hchaSV4aATAAAAAx4aWV5b25nZmExMjM; priceProPin=A56EX3SXYX2TGWEAEIVBJGGORA; __jdb=122270672.19.16919755887571451529675|8.1701831861; mba_sid=17018318614056491680459141655.27; 3AB9D23F7A4B3CSS=jdd037FZCNWJQURNZTQYH7LLDERIGEZ4EQFHGDNXP6RQ24YOMDGPC63QSEF5WODIEVOHK4FTBQDN2VD2Q6Y2EF76VRTQD3YAAAAMMHUQCYIYAAAAADNIQSHQHCDIWBIX; _gia_d=1; __jd_ref_cls=NEW_M_APPLY_1634006558659%7C1';
        //        $data     = "body=%7B%22sid%22%3A%22%22%2C%22type%22%3A%2225%22%2C%22forcebot%22%3A%22%22%7D&h5st=20231206111617382%3Bgt3tm3mm6zin5960%3Bd2f64%3Btk02wbbce1c6041lMSsxKzN4MngzvjIsKXfBZufNFcJLRHd9Ga1P1aQFAZrZPMo7n8SuyOYQ3RQCHYv-IlBDlGfcldpX%3Bde26f11b04d421347bd7f2ee4183685d%3B4.1%3B1701832577382%3B400b31b4ba0ceea2f8bf1b992ef178acef4edee7688d6be62e610c77be77154748334e814bb0e09de90514eb3b0fe42610314814166a072f966d64f40df3d7be66ed13b15098c0020ecb69350fc90434535da2431746ebdc408c448d69126773682193e97cec9ef87108c35c2c914e869ffe8cde026cd81a7d49c6a62d23135bc63d25d9a6d012d6a215d8fe5d5c0845977e1dfbb71b44ad66f2efb191482f13b06528eb14d393a89900762a045dea3289490fd45c02e45e379483c1672c600c0c1466fe50801cbc10f73bf381ae8f27c85f1368e5c8bb6f808e72a36d2f5ef0476a496dbdccc67fadce2a22f8d8a3dde56a54716f7c6671e538555022dc4374b2d8ebd98546c889067abfa8638d44f198f9e32947f77d2d8216c08966feca3eb032edf024e9bb0b9cc2140bc7799f5ec8f7f4a7b521badef9519a99799cfcbc78cc16e497157ca1cdb9c0421545fc79";
        $headers  = [
            "content-type" => "application/x-www-form-urlencoded",
            "user-agent"   => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 Edg/132.0.0.0",
        ];
        $response = curl()->set_header($headers)->set_cookies($cookie)->get($url, $post_data)->get_body();
        $response = $response['data'] ?? [];
        if (!isset($response['flag'])) {
            wr_log('保价异常', 1);
            wr_log($response, 1);
            $job->delete();
            return false;
        }
        $flag = $response['flag'];
        if ($flag === true) {
            $succNum    = $response['succNum'];
            $succAmount = $response['succAmount'] ?? 0;
            if ($succNum > 0) {
                $msg = '保价成功' . $succNum . '件';
                $succAmount && $msg .= ',' . $succAmount . '元';
                ios_push($msg);
                wr_log($msg, 1);
            } else {
                //                wr_log('京东自动保价成功,但是没有差价');
            }
        } else {
            wr_log($response['responseMessage'] ?? '保价请求异常', 1);
        }
        $job->delete();
        return false;
    }

    public function er_tong_yu_yue(Job $job, $data_string)
    {
        $data = json_decode($data_string, true);
        $url  = 'https://wxis.91160.com/wxis/sch_new/schedulelist.do';
        //        $cookies = 'JSESSIONID=E2A01ED12C915B5A23EE8FE6202F6B18';
        $cookies    = get_system_config('er_tong_yi_yuan_cookie');
        $unit_id    = $data['unit_id'] ?? '111';
        $dep_id     = $data['dep_id'] ?? '200190972';
        $doctor_id  = $data['doctor_id'] ?? '19246';
        $cur_dep_id = $data['cur_dep_id'] ?? '200307769';
        $unit_name  = $data['unit_name'] ?? '深圳市儿童医院';
        $dep_name   = $data['dep_name'] ?? '特诊泌尿外科';
        $post_data  = [
            'unit_id'    => $unit_id,
            'dep_id'     => $dep_id,
            'sub_dep_id' => '', // 如果没有子科室ID，可以留空或者根据实际情况处理
            'doctor_id'  => $doctor_id,
            'cur_dep_id' => $cur_dep_id,
            'unit_name'  => $unit_name,
            'dep_name'   => $dep_name
        ];
        $result     = curl()->set_cookies($cookies)->form_params()->post($url, $post_data)->get_body();
        //   {"status":"3","message":"当前未登录，不能获取排班"}
        if (isset($result['status']) && $result['status'] != 1) {
            wr_log($dep_name . ' 号源监控异常:' . ($result['message'] ?? '160请求出现异常'), 1);
            $job->delete();
            return false;
        }
        $cache_key    = __FUNCTION__ . ':' . md5(json_encode($post_data, JSON_UNESCAPED_UNICODE));
        $cache_value  = cache($cache_key);
        $milliseconds = round(microtime(true) * 1000);
        $notify_url   = 'https://wxis.91160.com/wxis//sch/main.do?r=' . $milliseconds . '&unit_id=' . $unit_id . '&dep_id=' . $dep_id . '&doc_id=' . $doctor_id . '&dep_name=' . urlencode(urlencode($dep_name));
        $desc         = '';
        foreach ($result['data']['sch'] as $list) {
            //            if ($list['y_state'] == 1 && date('N', strtotime($list['to_date'])) >= 6 && ($list['to_date'] == '2024-08-03' || $list['to_date'] == '2024-08-04')) {
            if ($list['y_state'] == 1) {
                $to_date = $list['to_date'];
                $desc    .= $to_date . '(' . tools()::date_to_week($to_date) . ')' . $list['time_type_desc'] . '剩余' . $list['left_num'] . '个号';
            }
        }
        if ($cache_value && $cache_value != json_encode($result, JSON_UNESCAPED_UNICODE) && $desc) {
            $notify_url = tools()::long_to_short($notify_url);
            $msg        = $unit_name . ($result['data']['sch'][0]['doctor_name'] ?? '') . $desc . ',点击查看: ' . $notify_url;
            wr_log($msg, 1);
            send_qy_wechat($msg, 'wangwenjuan');
            ios_push($msg);
            send_ding_talk($msg);
            ios_push($msg, 'wwj');
        }
        cache($cache_key, json_encode($result, JSON_UNESCAPED_UNICODE), 3600 * 24 * 7);
        $job->delete();
        return false;
    }

    /**
     *160预约
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function booking(Job $job, $data)
    {
        $data = json_decode($data, true);
        try {
            $header = [
                'Host'   => 'www.91160.com',
                //'User-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.37',
                //'CLIENT-IP: ' . mt_rand(0, 255) . '.' . mt_rand(0, 255) . '.' . mt_rand(0, 255) . '.' . mt_rand(0, 255),
                //'X-Forwarded-For: ' . mt_rand(0, 255) . '.' . mt_rand(0, 255) . '.' . mt_rand(0, 255) . '.' . mt_rand(0, 255),
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
            ];
            $url    = 'https://www.91160.com/dep/getschmast/uid-' . $data['uid'] . '/depid-' . $data['depid'] . '/date-' . date("Y-m-d") . '/p-0.html';
            $result = curl()->set_header($header)->get($url)->get_body();
            if (!is_array($result)) {
                wr_log('160请求出现异常', 1);
                $job->delete();
                return false;
            }
            $list = $result['sch'];
            $msg  = '';
            foreach ($list as $key1 => $val1) {
                foreach ($val1 as $key2 => $val2) {
                    foreach ($val2 as $key3 => $val3) {
                        if ($val3['y_state'] == 1) {
                            $msg .= $val3['doctor_name'] . '--' . $val3['to_date'] . '--' . $val3['time_type_desc'] . '有' . (int)$val3['left_num'] . "个号，请尽快预约！\r\n";
                        }
                    }
                }
            }
            if ($msg) {
                wr_log($msg, 1);
            }
            $job->delete();
        } catch (Exception $e) {
            wr_log('booking监控出现异常' . $e->getMessage(), 1);
        }
    }

    /**
     *查询安居房排名变化
     * @acces public
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function anjufang(Job $job, $data)
    {
        $data   = json_decode($data, true);
        $url    = 'http://zjj.sz.gov.cn/bzflh/lhmcAction.do?';
        $data   = [
            'method'     => 'queryLhmcInfo1', //GET参数
            'pageNumber' => 1,
            'pageSize'   => 1,
            'waitTpye'   => 1,
            'bahzh'      => 'SQR00075430',
            'xingm'      => '王文娟', //如果不通过 http_build_query 则需要urlencode
            'sfz'        => '412722198905201167',
        ];
        $url    .= http_build_query($data); //todo 暂时post不通
        $result = curl()->get($url)->get_body();
        if (!is_array($result)) {
            wr_log('安居房查询异常');
            $job->delete();
            return true;
        }
        $data       = reset($result['rows']);
        $cache_key  = 'anjufang';
        $cache_data = cache($cache_key);
        if ($cache_data !== $data) {
            cache($cache_key, $data, 3600 * 24 * 31);
            $content = "\n【申请类型】:" . $data['WAIT_TPYE'] . "\n";
            $content .= "【申请人】:" . $data['XINGM'] . "\n";
            $content .= "【备案号】:" . $data['SHOULHZH'] . "\n";
            if (is_array($cache_data) && isset($cache_data['PAIX'])) {
                $content .= "【市排名变化】:" . sprintf("%+d", ($data['PAIX'] - $cache_data['PAIX'])) . "\n";
                $content .= "【上次市排名】:" . $cache_data['PAIX'] . "\n";
            }
            $content .= "【最新市排名】:" . $data['PAIX'] . "\n";
            if (is_array($cache_data) && isset($cache_data['AREA_PAIX'])) {
                $content .= "【区排名变化】:" . sprintf("%+d", ($data['AREA_PAIX'] - $cache_data['AREA_PAIX'])) . "\n";
                $content .= "【上次区排名】:" . $cache_data['AREA_PAIX'] . "\n";
            }
            $content .= "【最新区排名】:" . $data['AREA_PAIX'] . "\n";
            $db      = new AnjufangRankNote();
            $data    = [
                'city_rank'     => $data['PAIX'],
                'district_rank' => $data['AREA_PAIX'],
            ];
            $db->save($data);
            send_qy_wechat($content, 'xieyongfa|wangwenjuan');
            wr_log($content);
        }
        $job->delete();
        return true;
    }

    /**
     *监控商品价格
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function goods_price(Job $job, $data)
    {
        $data = json_decode($data, true);
        $db   = new GoodsPriceMonitor();
        $map  = [
            ['end_time', '>', format_timestamp()],
            ['status', '=', 1]
        ];
        $list = $db->where($map)->order(['end_time' => 'DESC'])->select();
        foreach ($list as $goods) {
            $db          = new GoodsPriceMonitor();
            $goods_price = $db->get_jd_price($goods['goods_id']);
            if ($goods_price != $goods['last_price']) {
                //$msg = "商品链接:<a href='" . $goods['url'] . "'>点击进入</a>\r\n";
                $msg         = "<a href='" . $goods['url'] . "'>" . $goods['name'] . "</a>\r\n";
                $msg         .= '原价:' . $goods['begin_price'] . "\r\n";
                $msg         .= '上次价格:' . $goods['last_price'] . "\r\n";
                $msg         .= '当前价:' . $goods_price . "\r\n";
                $msg         .= "变动:" . sprintf("%+d", ($goods_price - $goods['begin_price'])) . "\r\n";
                $msg         .= "价格保护:<a href='https://msitepp-fm.jd.com/rest/priceprophone/priceProPhoneMenu'>点击进入</a>\r\n";
                $update_data = ['last_price' => $goods_price];
                send_qy_wechat($msg);
            } else {
                $update_data = ['update_time' => format_timestamp()];
            }
            $map = [['id', '=', $goods['id']]];
            $db::update($update_data, $map);
        }
        $job->delete();
        return true;
    }

    /**
     *共享汽车数量
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function you_lun(Job $job, $data)
    {

        $data = json_decode($data, true);

        $url         = 'https://h5.cmskchp.com/ors/ors/queryVenueList';
        $productCode = '9635959C13CC142B813E52DBC464EE07';
        $cookies     = '_c_WBKFRo=7fYE0tNgC0YWnnBp330wUkooDXm3CMMzf5DhFlrP;_nb_ioWEgULi;acw_tc=0bdd26ca17273158760845046e12ae546930246f65e4bbaf6f532a8b656c86;cdn_sec_tc=dde5cb2717273158759743913edd9deca1dca3f5cff21544419bdace04;chanId=0;cruiseChanId=4;openid=oZs2yuLSjvEw68pE1wuniasIDuwY;openid-sign=1727310715;SESSION=372ac310103969ba5d36ae8acc14f56063151908;user_from=MicroMessenger';
        $data        = [
            'productCode' => $productCode,
            'venueDate'   => "2024-10-02",
        ];

        $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
        if ($result && is_array($result) && !empty($result['message']['venues'])) {
            wr_log('10月2日有数据: ' . json_encode($result['message']['venues'], JSON_UNESCAPED_UNICODE), 1);
        }
        $data   = [
            'productCode' => $productCode,
            'venueDate'   => "2024-10-01",
        ];
        $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
        foreach ($result['message']['venues'] as $venues) {
            if ($venues['statusCode'] != 3) {
                wr_log('正在尝试预约https://h5.cmskchp.com/ors?trans=toSubmitOrder&productCode=9635959C13CC142B813E52DBC464EE07&venueCode=' . $venues['code'], 1);
                $passengers_list = ["5894326", "5895453", "5894815", "5895701", "5897933"];
                $url             = 'https://h5.cmskchp.com/ors/ors/submitOrder';
                foreach ($passengers_list as $passengerId) {
                    $data   = [
                        'productCode' => $productCode,
                        'phone'       => "18603047034",
                        'venueCode'   => $venues['code'],
                        //                'passengers'  => json_encode(["5897933", "5895701", "5895453", "5894326", "5894815"], JSON_UNESCAPED_UNICODE),
                        'passengers'  => json_encode([$passengerId], JSON_UNESCAPED_UNICODE),
                        //5894326 谢永发
                        //5894815 王文娟
                        //5895453 谢佳明
                        //5895701 谢佳悦
                        //5897933 谢福生
                    ];
                    $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
                    if ($result['status'] == false) {
                        $msg = $result['message'] ?? '';
                        $msg != '系统异常' && wr_log($msg, 1);
                        break;
                    } elseif ($result['status'] && $result['message']['reserveStatus'] == 3) {
                        break;
                    } elseif ($result['status'] && $result['message']['reserveStatus'] != 3) {
                        wr_log('游轮可能预约上了:' . $result['message']['message'], 1);
                    }
                }
            }
        }
        $job->delete();
        return true;


        $url  = 'https://h5.cmskchp.com/ors/ors/queryVenue';
        $data = [
            'productCode' => "9635959C13CC142B813E52DBC464EE07",
            'venueDate'   => "2024-10-01",
        ];

        $cookies = '_c_WBKFRo=7fYE0tNgC0YWnnBp330wUkooDXm3CMMzf5DhFlrP;_nb_ioWEgULi;acw_tc=0bdd26ca17273158760845046e12ae546930246f65e4bbaf6f532a8b656c86;cdn_sec_tc=dde5cb2717273158759743913edd9deca1dca3f5cff21544419bdace04;chanId=0;cruiseChanId=4;openid=oZs2yuLSjvEw68pE1wuniasIDuwY;openid-sign=1727310715;SESSION=372ac310103969ba5d36ae8acc14f56063151908;user_from=MicroMessenger';
        $result  = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();

        $msg = 'https://h5.cmskchp.com/ors?trans=toVenue&productCode=9635959C13CC142B813E52DBC464EE07   ;';

        $count = substr_count($result, '约满');
        if ($count > 1 && $count < 7) {
            $msg .= ' 可能有剩余号码';
            wr_log($msg, 1);
        } elseif ($count == 0) {
            wr_log('可能掉线了', 1);
        }


        $list = [
            '21B4A88DFD1B585240BA8097B77E887A',
            '6DE735447CBA4A71547E91307D873EF7',
            '543A13F3783D5AC5F611B57B88E1AED3',
            'AF6BF7675F5B6F62000B4980D6CCDC35',
            '37D43DC6E7C04C5ED82874D7E3CDB335',
            '6699FC84825F9A1499FF2CCD934E28AF',
            '1C7583A2B223D7ED7175F3E11FDFC032',
        ];
        $url  = 'https://h5.cmskchp.com/ors/ors/submitOrder';
        foreach ($list as $venueCode) {
            $passengers_list = [5894326, 5895453, 5894815, 5895701, 5897933];
            foreach ($passengers_list as $passengerId) {
                $data   = [
                    'productCode' => "9635959C13CC142B813E52DBC464EE07",
                    'phone'       => "18603047034",
                    'venueCode'   => $venueCode,
                    //                'passengers'  => json_encode(["5897933", "5895701", "5895453", "5894326", "5894815"], JSON_UNESCAPED_UNICODE),
                    'passengers'  => json_encode([$passengerId], JSON_UNESCAPED_UNICODE),
                    //5894326 谢永发
                    //5894815 王文娟
                    //5895453 谢佳明
                    //5895701 谢佳悦
                    //5897933 谢福生
                ];
                $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
                if ($result['status'] == false) {
                    $msg = $result['message'] ?? '';
                    $msg != '系统异常' && wr_log($msg, 1);
                    break;
                } elseif ($result['status'] && $result['message']['reserveStatus'] == 3) {
                    break;
                } elseif ($result['status'] && $result['message']['reserveStatus'] != 3) {
                    wr_log('游轮可能预约上了:' . $result['message']['message'], 1);
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *共享汽车数量
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function car(Job $job, $data)
    {
        $data   = json_decode($data, true);
        $url    = 'https://api.zerocarcn.com/api/car/network_car/270/?limit=10';
        $result = curl()->get($url)->get_body();
        if (isset($result['count'])) {
            $count  = intval($result['count']);
            $key    = 'hui_hai_left_car_count';
            $before = intval(cache($key));
            if ($before != $count) {
                wr_log('共享汽车汇海停车场车辆数由' . $before . '量变为' . $count . '量', 1);
                cache($key, $count);
            }
        }
        $job->delete();
        return true;
    }

    /**
     *共享汽车数量
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function gongyuantingchewei(Job $job, $data)
    {
        $url     = 'https://smartum.sz.gov.cn/tcyy/parking/lot-mobile/service-parking-mobile/webapi/parkInfo/parkDetail';
        $headers = [
            'Host'            => ' smartum.sz.gov.cn',
            'Cookie'          => ' UPIP_MP_AUTHORIZATION=oDJ04uLFUzqI-8ok3YB6kVVDf_zk',
            'User-Agent'      => ' Mozilla/5.0 (iPhone; CPU iPhone OS 16_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x18003028) NetType/WIFI Language/zh_CN',
            'Referer'         => ' https://smartum.sz.gov.cn/tcyy/parking/lot-mobile/park-list/QH0001',
            'Origin'          => ' https://smartum.sz.gov.cn',
            'Sec-Fetch-Dest'  => ' empty',
            'Sec-Fetch-Site'  => ' same-origin',
            'nonce'           => ' vgcnembz',
            'sign'            => ' 770f8f5a943197e14cd35937b331b738',
            'X-ItemCode'      => ' tcyy',
            'auth'            => ' ',
            'timestamp'       => ' 1711094972633',
            'Authorization'   => ' oDJ04uLFUzqI-8ok3YB6kVVDf_zk',
            'Accept-Language' => ' zh-CN,zh-Hans;q=0.9',
            'X-AppCode'       => ' parking',
            'Accept'          => ' application/json, text/plain, */*',
            'Content-Type'    => ' application/json;charset=utf-8',
            'X-token'         => ' 20240322-42e6021d-57f0-4d73-acf5-fc2fcd3e4c48',
            'originId'        => ' gh_95355fb87f0o',
            'Sec-Fetch-Mode'  => ' cors',
        ];

        $data   = [
            'parkCode'  => 'QH0001',
            'longitude' => 116.397455,
            'latitude'  => 39.909187,
        ];
        $result = curl()->set_header($headers)->post($url, $data)->get_body();
        if (isset($result['code']) && $result['code'] == 0) {
            $name = $result['data']['lotList'][0]['name'];
            $num  = $result['data']['lotList'][0]['fuelOilResidueNum'];
            if ($num > 0) {
                $msg = $name . '有' . $num . '个停车位https://smartum.sz.gov.cn/tcyy/parking/lot-mobile/park-list/QH0001';
                wr_log($msg, 1);
                ios_push($msg);
            }
        } else {
            wr_log('公园停车位预约请求失败');
        }
        $job->delete();
        return true;
    }


    /**
     *子商户cookies检测
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function sub_merchant_cookies_update_time(Job $job, $data)
    {
        $data = json_decode($data, true);
        if (is_off_work()) {
            return true;
        }
        $sub_merchant_cookies_update_time = (int)strtotime(cache('sub_merchant_cookies_update_time:1531081171'));
        $result                           = ((time() - $sub_merchant_cookies_update_time) > 60 * 20);
        if ($result) {
            send_qy_wechat('子商户模拟登陆超时,请及时处理');
        }
        $job->delete();
        return true;
    }

    /**
     *git更新检测
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function git(Job $job, $data)
    {
        $data = json_decode($data, true);
        //监控github中相关项目版本是否有新的提交记录
        //      https://api.github.com/repos/top-think/framework/branches/6.0
        $last_update_time_cache_key = 'git_version:last_update_time';
        if (cache($last_update_time_cache_key)) {
            $job->delete();
            return true;
        }
        $base_url = 'https://api.github.com/repos/';
        $git_arr  = [
            ['name' => 'top-think/framework', 'branch' => ['5.1', '6.0']],
            ['name' => 'zoujingli/WeChatDeveloper', 'branch' => ['master']],
            ['name' => 'zoujingli/WeOpenDeveloper', 'branch' => ['master']],
        ];
        foreach ($git_arr as $key => $val) {
            $name        = $val['name'];
            $branch_list = $val['branch'];
            foreach ($branch_list as $branch) {
                $url    = $base_url . $name . '/branches/' . $branch;
                $result = curl()->get($url)->get_body();
                if (!is_array($result)) {
                    continue;
                }
                $cache_key = 'git_version:' . $name . '_' . $branch;
                $version   = $result['commit']['sha'];
                $cache     = cache($cache_key);
                if ($cache != $version && strlen($version) == 40) {
                    $content = $result['commit']['commit']['message'];
                    cache($cache_key, $version, 3600 * 24 * 30);
                    $textcard = [
                        'title'       => '版本更新通知',
                        'description' => "项目: " . $name . '@' . $branch . "\n更新内容: " . $content,
                        'url'         => $url
                    ];
                    qy_weixin_msg()->textcard($textcard)->send();
                }
            }
        }
        cache($last_update_time_cache_key, format_timestamp(), 1800);
        $job->delete();
        return true;
    }

    /**
     *获取代理商概况
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function get_agent_survey_list(Job $job, $data)
    {
        $data       = json_decode($data, true);
        $agent_list = [
            //          [
            //              'agent_account' => 'lianqi',
            //              'min_value'     => 1000,
            //              'email'         => '<EMAIL>;<EMAIL>',//朱笑英
            //          ],
            [
                'agent_account' => 'WYKJ',
                'min_value'     => 200,
                'email'         => '<EMAIL>;<EMAIL>;<EMAIL>', //曾丽和
            ],
            [
                'agent_account' => 'huiqi',
                'min_value'     => 100,
                'email'         => '<EMAIL>', //曾丽和
            ],
        ];
        $yikayi     = new \OpenApi\Yikayi();
        $list       = $yikayi->get_agent_survey_list();
        foreach ($list as $info) {
            foreach ($agent_list as $agent) {
                $now_value     = $info[5];
                $agent_account = $agent['agent_account'];
                $min_value     = $agent['min_value'];
                if (in_array($agent_account, $info) && $now_value < $min_value) {
                    $msg = '代理商账号:' . $agent_account . '当前奖惩金余额仅剩' . $now_value . ',已经不足' . $min_value . '元,请尽快充值';
                    //wr_log($msg, 1);
                    $email_data = [
                        'account' => 'xyf',
                        'send_to' => $agent['email'],
                        'title'   => $msg,
                        'subject' => $msg,
                        //'copy_to' => '<EMAIL>'
                    ];
                    send_email($email_data);
                }
            }
        }
        $job->delete();
        return true;
    }

    /**
     *当前先享机绑定数量
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function get_payscore_bind_note_count(Job $job, $data)
    {
        $data   = json_decode($data, true);
        $yikayi = new \OpenApi\Yikayi();
        $list   = $yikayi->get_payscore_bind_note_html();
        if (strpos($list, '总记录条数') === false) {
            wr_log('先享机数量查询异常', 1);
            $job->delete();
            return false;
        }
        $count     = (int)tools()::search_str('(总记录条数:', ')', $list);
        $cache_key = 'payscore_bind_note_count';
        $cache     = (int)cache($cache_key);
        if ($cache !== $count) {
            cache($cache_key, $count);
            $text_card = [
                'title'       => '【先享机】新签约提醒',
                'description' => '当前签约: ' . $count . '台',
            ];
            qy_weixin_msg()->textcard($text_card)->send();
        }
        $job->delete();
        return true;
    }

    /**
     *检查微信队列
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function check_wechat_queues(Job $job, $data)
    {
        $data              = json_decode($data, true);
        $db                = new Jobs();
        $wechat_queues     = config('app.wechat_queues');
        $wechat_queues_arr = explode(',', $wechat_queues);
        foreach ($wechat_queues_arr as $key => $val) {
            $wechat_queues_arr[$key] = '%' . $val . '%';
        }
        $map[] = ['payload->job', 'like', $wechat_queues_arr, 'OR'];
        $map[] = ['queue', '=', 'default'];
        $field = [
            "JSON_UNQUOTE(json_extract(payload->>'$.data', '$.bid'))" => 'bid',
            'count(1)'                                                => 'count'
        ];
        $list  = $db->field($field)->where($map)->whereDay('available_at')->group('bid')->select();
        foreach ($list as $key => $val) {
            if ($val['count'] < 3) {
                //小于三笔说明正常
                continue;
            }
            $cache_key = 'wechat_balance_status:' . $val['bid'];
            if (cache($cache_key)) {
                //当前处于余额不足状态不告警
                continue;
            }
            $db_business   = new Business();
            $business_info = $db_business->get_business_info_by_account_or_guid($val['bid']);
            $textcard      = [
                'title'       => '任务堵塞通知(' . $business_info['account'] . ')',
                'description' => "队列任务: 提现任务\n堵塞条数: " . $val['count'] . '条',
            ];
            qy_weixin_msg()->textcard($textcard)->send();
        }
        $job->delete();
        return true;
    }

    /**
     *检查openvpn登陆状态,掉线则自动重启
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed
     * @throws Exception
     */
    public function check_openvpn_status(Job $job, $data)
    {
        $data      = json_decode($data, true);
        $host      = $data['host'];
        $ping      = new Ping($host, 255, 3);
        $ping_type = $data['ping_type'] ?? 'fsockopen';
        $latency   = $ping->ping($ping_type);
        if ($latency !== false) {
            //wr_log('[' . $host . '] Latency is ' . (int)$latency . ' ms');
            $job->delete();
            return true;
        }
        $cmd = 'sudo -u root -S supervisorctl restart openvpn';
        exec($cmd, $out, $code);
        if ($code !== 0) {
            $msg = '命令' . $cmd . '执行失败,状态码:' . $code;
            wr_log($msg, 1);
        }
        wr_log('openvpn检测已经掉线,已经自动登录成功');
        $job->delete();
        return true;
    }


    /**
     *实例
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed|bool
     * @throws Exception
     */
    public function jianhangcc(Job $job, $data)
    {
        $data_array    = json_decode($data, true);
        $Authorization = $data_array['Authorization'];
        $url           = 'https://cy.cloud.ccb.com/gateway/goods-server/goods/ccBeanCategory/queryProduct';
        $headers       = [
            "Host"            => "cy.cloud.ccb.com",
            //            "Cookie"          => "zx", // 请替换为实际的Cookie值
            "Content-Type"    => "application/json;charset=utf-8",
            "Channelid"       => "TCH2022022500000207",
            "Authorization"   => $Authorization, // 请替换为实际的Authorization值
            "Accept"          => "application/json, text/plain, */*",
            "Channel"         => "JH-0007",
            "Sec-Fetch-Site"  => "same-origin",
            "Accept-Language" => "zh-CN,zh-Hans;q=0.9",
            "Sec-Fetch-Mode"  => "cors",
            "Origin"          => "https://cy.cloud.ccb.com",
            "Referer"         => "https://cy.cloud.ccb.com/qymall/ccbean/exchange?isBack=",
            "Traceid"         => "e0486a06171107933089666d94e6b86", // 请替换为实际的Traceid值
            "User-Agent"      => "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x18003028) NetType/WIFI Language/zh_CN",
            "Sec-Fetch-Dest"  => "empty"
        ];
        $data          = [
            "channel"           => "JH-0007",
            "channelCategoryId" => "TCC2024012400005471",
            "city"              => "440300",
            "province"          => "440000",
            "channelId"         => "TCH2022022500000207",
            "type"              => ""
        ];
        $result        = curl()->set_header($headers)->set_http_errors(false)->post($url, $data)->get_body();
        if ($result['code'] != 0) {
            wr_log($result['msg'] . ' ,CC积分监控已经停止', 1);
            $job->delete();
            $this->stop_crontab($data_array);
            return true;
        }
        foreach ($result['data'] as $data) {
            if ($data['remainingInventory'] > 0) {
                $msg = $data['activityName'] . '剩余库存' . $data['remainingInventory'] . '张';
                wr_log($msg, 1);
                send_qy_wechat($msg);
                ios_push($msg);
            }
        }
        $cache_key = 'ek';
        $md5       = md5(json_encode($result, JSON_UNESCAPED_UNICODE));
        $cache_md5 = cache($cache_key);
        cache($cache_key, $md5, 3600 * 24);
        if ($cache_md5 != $md5) {
            send_qy_wechat('可能有京东E卡 https://cy.cloud.ccb.com/qymall/ccbean/exchange?isBack=');
            ios_push('可能有京东E卡');
        }
        $job->delete();
        return true;
    }

    /**
     *实例
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed|bool
     * @throws Exception
     */
    public function bowuguan(Job $job, $data)
    {
        $data = json_decode($data, true);
        //    $host = $data['host'];
        $url    = 'https://wxmini.chnmuseum.cn/prod-api/basesetting/HallSetting/gainAllSystemConfigLogin?channel=wxMini&requestTaskKey=gainAllSystemConfigLogin&ticketUseType=1&p=wxmini';
        $header = [
            'Host'            => 'wxmini.chnmuseum.cn',
            'Content-Type'    => 'application/json',
            'Accept'          => 'application/json',
            'xweb_xhr'        => '1',
            'User-Agent'      => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/9079',
            'Authorization'   => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpbl91c2VyX25hbWUiOiLmuLjlrqIgMTg2MDMwNDcwMzQiLCJsb2dpbl9leHBpcmVkX3RpbWUiOjE3MTE4NzY1Mzg0MzYsImxvZ2luX3VzZXJfaWQiOjE0NTgyNTE3LCJsb2dpbl91c2VyX2tleSI6IjE0NTgyNTE3OjY1NzczNmJkLTMyODAtNDQyNy04MWI2LWMzOTJkODg4MDQwYSIsImxvZ2luX3VzZXJfYWNjb3VudCI6IjE4NjAzMDQ3MDM0In0.v0GFumVXdYPT-QtpQdT8a5WQmmYc0brbLrM4L-sS_A4',
            'Sec-Fetch-Site'  => 'cross-site',
            'Sec-Fetch-Mode'  => 'cors',
            'Sec-Fetch-Dest'  => 'empty',
            'Referer'         => 'https://servicewechat.com/wx9e2927dd595b0473/72/page-frame.html',
            'Accept-Language' => 'zh-CN,zh;q=0.9'
        ];
        $result = curl()->set_header($header)->get($url)->get_body();
        if ($result['code'] != 200) {
            wr_log($result['msg'] ?? '博物馆登录失败', 1);
            $this->stop_crontab($data);
            $job->delete();
            return true;
        }
        foreach ($result['data']['calendarTicketPoolsByDate'] as $list) {
            if ($list['currentDate'] == '2024-04-06') {
                $msg = '6号博物馆有' . $list['ticketPool'] . '张票';
                if ($list['ticketPool'] > 0 && isset($list['scheduleTicketPoolVOS'])) {
                    foreach ($list['scheduleTicketPoolVOS'] as $item) {
                        if ($item['ticketPool'] > 0 && $item['scheduleName'] == '09:00-11:00') {
                            // "scheduleName": "09:00-11:00",
                            // "scheduleName": "11:00-13:30",
                            // "scheduleName": "13:30-16:00",
                            $notify_msg = $item['currentDate'] . $item['scheduleName'] . '有' . $item['ticketPool'] . '张票,请及时查看';
                            wr_log($notify_msg, 1);
                            send_ding_talk($notify_msg);
                            ios_push($notify_msg);
                        }
                    }
                }
            }
        }

        $job->delete();
        return true;
    }

    /**
     *实例
     * @param Job $job 任务对象
     * @param string $data 数据
     * @return mixed|bool
     * @throws Exception
     */
    public function demo(Job $job, $data)
    {
        $data = json_decode($data, true);
        $host = $data['host'];
        $job->delete();
        return true;
    }
}
