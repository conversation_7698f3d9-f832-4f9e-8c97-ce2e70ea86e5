<?php

use app\common\service\BunnyService;
use app\common\service\Curl;
use app\common\service\DistributedLocksService;
use app\common\service\HttpService;
use app\common\service\NotifyService;
use app\common\service\PayService;
use app\common\service\QueueService;
use app\common\service\QyWeixinMessageService;
use app\common\service\ResponseService;
use app\common\service\ToolsService;
use app\common\service\WeixinService;
use app\common\service\WorkWeixinService;
use app\common\tools\AES_256_CBC;
use app\common\tools\Redis;
use app\common\tools\Visitor;
use app\model\Log as LogModel;
use app\model\Parameter;
use app\model\SystemConfig;
use Swoole\Coroutine;
use think\exception\HttpResponseException;
use think\facade\App;
use think\facade\Cookie;
use think\facade\Log;
use xieyongfa\facade\Notify;
use xieyongfa\work_weixin\WorkWeixin;


function get_action(): string
{
    return request()->action(true);
}

/**
 * 获取当前的控制器名
 * @access public
 * @return string
 */
function get_controller(): string
{
    return parse_name(lcfirst(request()->controller(false, true)));
}

/**
 * 获取当前的模块名
 * @access public
 * @param bool $convert 转换为小写
 * @return string
 */
function get_layer(bool $convert = true): string
{
    $layer_name = request()->layer($convert);
    if (empty($layer_name)) {
        return '';
    }
    //admin_api/v1  这种返回 admin_api
    $layer_name = explode('/', $layer_name);
    return reset($layer_name);
}

/**
 * 获取当前的请求路径
 * @access public
 * @return string
 */
function get_request_path(): string
{
    $controller_name = get_controller();
    $action_name     = request()->action(true);
    return $controller_name . '/' . $action_name;
}

/**
 * 发送钉钉消息
 * @param string $content 发送内容
 * @return boolean
 */
function send_ding_talk(string $content)
{
    return Notify::channel('ding_talk')->send($content);
}

/**
 * 处理告警事件
 * @param string $content 告警文本内容
 * @return bool
 */
function alarm_msg(string $content, $max_time = 180, $max_count = 3)
{
    if (empty($content)) {
        return false;
    }
    wr_log(__FUNCTION__ . ':' . $content);
    $redis           = get_redis_instance();
    $keys            = md5($content);
    $alarm_event_key = 'alarm_msg:' . $keys;
    $start_time      = time();
    $result          = $redis->zAdd($alarm_event_key, $start_time, format_timestamp($start_time));
    $count           = $redis->zCount($alarm_event_key, $start_time - $max_time, $start_time);
    //删除过期的key
    $redis->zRemRangeByScore($alarm_event_key, 0, $start_time - $max_time);
    if ($count > $max_count) {
        send_qy_wechat('【告警】' . $content);
        //一旦触发删除key,重新统计
        $result = $redis->del($alarm_event_key);
        return true;
    }
    // 设置过期时间,避免事件残留
    $redis->expire($alarm_event_key, $max_time);
    return false;
}

/**
 * 通知
 * @return NotifyService
 */
function notify()
{
    return NotifyService::get_instance();
}

/**
 * 企业微信通知发送方法
 * @param string $content 发送内容
 * @param string $to_user 接受者
 * @return boolean
 */
function send_qy_wechat(string $content, string $to_user = '')
{
    return Notify::channel('work_weixin')->send($content, $to_user);
}

/**
 * 电话告警通知方法
 * @param string $content 电话告警内容
 * @return boolean
 */
function call(string $content)
{
    return Notify::channel('call')->send($content);
}

/**
 * 发送邮件方法
 * @param array $data
 * @param int $later 延迟多少秒发送
 * @return integer
 */
function send_email(array $data, $later = 0)
{
    try {
        return job()->set_job_name('Email@send')->push_job($data, $later);
    } catch (Exception | Throwable $e) {
        return false;
    }
}

/**
 * 发送短信方法
 * @param string $content 发送内容
 * @param string $mobile 发送手机号
 * @param string $bid 商家标识
 * @return mixed
 * @throws Exception
 */
function send_sms(string $content, string $mobile, $bid = null)
{
    try {
        $data = [
            'guid'    => create_guid(),
            'bid'     => $bid,
            'mobile'  => $mobile,
            'content' => $content
        ];
        return job()->set_job_name('Sms@send_sms')->push_job($data);
    } catch (Exception | Throwable $e) {
        return false;
    }
}

/**
 * 获取时间戳
 * @param int $timestamp 时间戳
 * @return false|string
 */
function format_timestamp($timestamp = null)
{
    $timestamp = $timestamp ?: time();
    return date('Y-m-d H:i:s', $timestamp);
}

//返回当前的毫秒时间戳
function get_microsecond()
{
    list($s1, $s2) = explode(' ', microtime());
    return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
}

/**
 * 获取毫秒时间戳
 * @return string
 * @throws Exception
 */
function microsecond()
{
    $dateTime = new DateTime();
    return $dateTime->format('Y-m-d H:i:s.u');
}

function get_diff_time($begin_time, $end_time = null)
{
    $end_time       = $end_time ?: microtime(true);
    $execution_time = $end_time - $begin_time;
    return number_format($execution_time, 3, '.', '');
}

/**
 * Curl请求
 * @param array $config 配置
 * @return Curl
 */
function curl(array $config = [])
{
    return Curl::get_instance($config);
}

/**
 * 获取GUID
 * @return string
 */
function create_guid()
{
    return tools()::create_guid();
}

function is_test_bid($bid)
{
    return $bid == 'f37eb109-70ac-9e6e-a089-ecefebd3d396';
}

function ios_push($data, $to = '')
{
    try {
        $to = $to ?: 'xyf';
        switch ($to) {
            case 'xyf':
                $device_key = 'UJMxejD3QuRXNus2RCxyrG';
                break;
            case 'wwj':
                $device_key = 'LWSghD7A9Hh4vELDXyHfgh';
                break;
            default:
                throw new Exception('不合法的通知对象');
        }
        if (is_string($data)) {
            $data = ['body' => $data];
        }
        $url          = 'https://api.day.app/push';
        $default_data = [
            'device_key' => $device_key,
            //            'level'      => 'timeSensitive',
            //        'title'      => '【警告】',
            //        'body'       => '余额不足通知',
            'url'        => 'weixin://',
            //            'icon'       => 'https://picnew13.photophoto.cn/20190305/lvseweixinliaotianruanjianlogotubiao-32287526_1.jpg'
        ];
        $data         = array_merge($default_data, $data);
        $result       = curl()->set_timeout(10)->post($url, $data)->get_body();
        if (is_array($result) && isset($result['code']) && $result['code'] == 200) {
            return true;
        }
        return false;
    } catch (Throwable $e) {
        return false;
    }
}

function get_instance_sn(array | string $config = ''): string
{
    $config = is_array($config) ? json_encode($config, JSON_UNESCAPED_UNICODE) : $config;
    PHP_SAPI == 'cli' && $config .= getmypid();
    class_exists('Swoole\Coroutine') && $config .= Coroutine::getCid();
    return md5($config);
}

/**
 * Cookie管理
 * @param string $name cookie名称
 * @param mixed $value cookie值
 * @param mixed $option 参数
 * @return mixed
 */
function cookie(string $name = '', $value = '', $option = null)
{
    if ($value !== '') {
        $current_cookie        = Cookie::get();
        $current_cookie[$name] = $value;
        request()->withCookie($current_cookie); //确保本次请求后续如果获取 cookie 的值可以准确获取
    }

    if (is_null($value)) {
        // 删除
        Cookie::delete($name, $option ?: []);
    } elseif ('' === $value) {
        // 获取
        return 0 === strpos($name, '?') ? Cookie::has(substr($name, 1)) : Cookie::get($name);
    } else {
        // 设置
        return Cookie::set($name, $value, $option);
    }
}

/**
 * Cookie管理(带加密)
 * @param string $name cookie名称
 * @param mixed $value cookie值
 * @param mixed $option 参数
 * @return mixed
 */
function cookies(string $name = '', $value = '', $option = null)
{
    if ($value !== '') {
        $current_cookie        = Cookie::get();
        $current_cookie[$name] = $value;
        request()->withCookie($current_cookie); //确保本次请求后续如果获取 cookie 的值可以准确获取
    }

    if (is_null($value)) {
        // 删除
        Cookie::delete($name, $option ?: []);
    } elseif ('' === $value) {
        // 获取
        return 0 === strpos($name, '?') ? Cookie::has(substr($name, 1)) : ($name === '' ? Cookie::get($name) : AES_256_CBC::decrypt(Cookie::get($name))); //取到的值进行解密
    } else {
        // 设置
        return Cookie::set($name, AES_256_CBC::encrypt($value), $option); //加密后再设置cookie
    }
}

/**
 * 参数查询方法
 * @param string $bid 账号
 * @param string $object 对象
 * @param string $object_guid 对象id
 * @return array
 */
function get_config_by_bid(string $bid, string $object = '', string $object_guid = '')
{
    $parameter_db = new Parameter();
    return $parameter_db->get_config_by_bid($bid, $object, $object_guid);
}

/**
 * 写调试日志,增加前缀便于删除
 * @param string|array $content 日志内容
 * @param int|bool $notify 是否需要用微信企业号通知
 * @return bool
 */
function debug_log($content, $notify = false)
{
    try {
        if (is_array($content)) {
            $content = json_encode($content, JSON_UNESCAPED_UNICODE);
        }
        return wr_log('debug_log:' . $content, $notify);
    } catch (Exception | Throwable $e) {
    }
}

/**
 * 写日志
 * @param string|array $content 日志内容
 * @param int|bool $notify 是否需要用微信企业号通知
 * @param null|string $bid 商家唯一标识
 * @return bool
 */
function wr_log($content, $notify = false, $bid = null)
{
    try {
        if (is_array($content)) {
            $content = json_encode($content, JSON_UNESCAPED_UNICODE);
        }
        $content = trim($content);
        if (empty($content)) {
            return false;
        }
        LogModel::record($content, $bid);
        if ($notify) {
            send_qy_wechat($content);
        }
        trace($content);
        return true;
    } catch (Exception | Throwable $e) {
        logToFile('日志写入失败:' . $e->getMessage() . '.内容:' . $content);
        return false;
    }
}

/**
 * 打印输出数据到文件
 * @param mixed $data
 * @param string|null $path_name
 */
function logToFile($data, $path_name = null)
{
    try {
        is_null($path_name) && $path_name = App::getRuntimePath();
        $request = request();
        if (!Visitor::is_web_request()) {
            $path_name .= 'cli' . DIRECTORY_SEPARATOR . $request->url();
        } else {
            $path_name .= get_layer() . DIRECTORY_SEPARATOR . get_controller();
        }
        if (!is_dir($path_name)) {
            mkdir($path_name, 0755, true); // 若目录不存在则创建之,支持递归
        }
        $path_name .= DIRECTORY_SEPARATOR . date('Ymd') . '.log';
        if (is_array($data)) {
            $str = json_encode($data, JSON_UNESCAPED_UNICODE);
        } elseif (is_string($data)) {
            $str = $data;
        } elseif (is_object($data)) {
            $str = print_r($data, true);
        } else {
            $str = var_export($data, true);
        }
        $log_time = DateTime::createFromFormat('0.u00 U', microtime())->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('Y-m-d H:i:s.u');
        $content  = [
            'time' => $log_time,
            'type' => 'info',
            'msg'  => $str,
        ];
        $data     = json_encode($content, JSON_UNESCAPED_UNICODE);
        file_put_contents($path_name, $data . "\n", FILE_APPEND);
        Log::record($str);
    } catch (Exception | Throwable $e) {
    }
}

/**
 * websocket推送
 * @param array $data 推送内容
 * @param string $to_uid 推送终端标识 为空表示向所有在线用户推送
 * @param string $type 类型 默认 message
 * @return string|bool
 */
function publish_websocket(array $data, $to_uid = '', $type = 'push')
{
    $ip           = is_debug() ? config('app.app_host_domain') : config('app.master_lan_ip'); // 推送的url地址，上线时改成自己的服务器地址
    $port         = 2121;
    $push_api_url = "http://" . $ip . ":" . $port . "/";
    $data['code'] = $data['code'] ?? 0; //默认0 成功
    $data['msg']  = $data['msg'] ?? 'success';
    $data['time'] = $data['time'] ?? date('Y-m-d H:i:s');
    $post_data    = [
        'type' => $type, //默认message类型
        'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
        'to'   => $to_uid,
    ];
    $result       = curl()->set_expect(false)->post($push_api_url, $post_data)->get_body();
    if (isset($result['code']) && $result['code'] === 0) {
        return true;
    } else {
        return $result['msg'] ?? 'fail';
    }
}

/**
 * tcp_socket推送
 * @param array $data 推送内容
 * @param string $to_client_id 推送终端标识 为空表示向所有在线用户推送
 * @return bool
 */
function publish_tcp_socket(array $data, $to_client_id = '')
{
    // 建立socket连接到内部推送端口
    $client = stream_socket_client('tcp://127.0.0.1:2011', $errno, $errmsg, 1); // 默认端口1011 但是需要大多数系统需要root权限以低于1024的端口上创建服务器套接字。 导致不能统一nginx
    if (!$client) {
        //链接失败
        return false;
    }
    // 推送的数据，包含uid字段，表示是给这个uid推送
    if (empty($data['client_id']) && $to_client_id) {
        $data['client_id'] = $to_client_id;
    }
    //$data = array('uid' => 'uuid008', 'key' => 'send', 'data' => ['a' => 'b', 'c' => 'd']);
    // 发送数据，注意5678端口是Text协议的端口，Text协议需要在数据末尾加上换行符
    fwrite($client, json_encode($data) . "\n");
    // 读取推送结果
    $result = fread($client, 8192);
    fclose($client);
    $result = str_replace(PHP_EOL, '', $result);
    return $result === 'SUCCESS';
}

/**
 * 判断当前是否工作时间
 * @return bool
 */
function is_work_time()
{
    //早上9点~晚上7点 为工作时间
    $time = intval(date('Hi'));
    return ($time > 900 && $time < 1900);
}

/**
 * 判断当前是否休息
 * @return bool
 */
function is_off_work()
{
    //先判断星期
    $day = date('w');
    if ($day > 0 && $day < 6) {
        //周一到周五继续判断
        $time = intval(date('Hi'));
        if (($time > 900 && $time < 1200) || ($time > 1330 && $time < 1900)) {
            //早上9点~12点 或者下午13:30到晚上7点 为工作时间
            return false;
        }
    }
    return true;
}

/**
 * 是否调试模式
 * @return bool
 */
function is_debug()
{
    return is_host();
    return App::isDebug();
}

function is_host()
{
    return env('IS_HOST');
}

/**
 * 刷新URL
 * @param string|null $url 刷新URL
 * @return string
 */
function refresh_url($url = null)
{
    $url = $url ?: (string)url('', [], false);
    ob_clean();
    return '<script>window.location.href="' . $url . '";</script>'; //这种形式的referer才是真正的当前页面
}

/**
 * 获取系统参数
 * @param string|null $key_name 需要获取的参数
 * @return array|string
 */
function get_system_config($key_name = null)
{
    $db = new SystemConfig();
    return $db->get_cache($key_name);
}

/**
 * 获取redis实例
 * @return \Redis|Redis
 * @throws Exception
 */
function get_redis_instance()
{
    return new Redis(config('cache.stores.redis'));
}

/**
 * 获取BunnyService实例
 * @return BunnyService
 * @throws Exception
 */
function get_bunny_instance()
{
    return BunnyService::get_instance();
}

/**
 * 获取redis分布式锁实例
 * @return DistributedLocksService|null
 */
function get_distributed_instance()
{
    $options = config('cache.stores.redis');
    $servers = [[$options['host'], $options['port'], 10, $options['password']]];
    return new DistributedLocksService($servers);
}

/**
 * 助手函数实例
 * @return ToolsService|null
 */
function tools()
{
    return ToolsService::get_instance();
}

/**
 * HTTP请求实例
 * @return HttpService|null
 */
function curl_beta()
{
    return HttpService::get_instance();
}

/**
 * 发送企业微信
 * @return QyWeixinMessageService
 */
function qy_weixin_msg()
{
    return QyWeixinMessageService::get_instance();
}

/**
 * 获取支付实例
 * @param string $bid 商家标识
 * @return PayService
 */
function pay($bid)
{
    return PayService::get_instance($bid);
}

/**
 * 获取队列实例
 * @return QueueService
 */
function job()
{
    return QueueService::get_instance();
}

/**
 * 实例化微信SDK
 * @param string $appid 微信appid
 * @param string $component_appid 微信平台appid
 * @return WeixinService|We|null
 * @throws Exception
 */
function weixin(string $appid = '', string $component_appid = '')
{
    return WeixinService::get_instance($appid, $component_appid);
}

/**
 * 实例化微信SDK
 * @param string $bid_or_config 商家唯一标识 或者配置
 * @return WorkWeixinService|null|WorkWeixin
 * @throws Exception
 */
function work_weixin($bid_or_config = [])
{
    return WorkWeixinService::get_instance($bid_or_config);
}

/**
 * 设置缓存方法
 * @param string $name 缓存key
 * @param string $value 缓存value
 * @param int $expire 缓存value
 * @return bool
 * @throws Exception
 */
function cache2(string $name, $value = '', $expire = 0)
{
    $redis_instance = get_redis_instance();
    if ('' === $value) {
        // 获取缓存
        $data = $redis_instance->get($name);
        return is_numeric($data) ? $data : unserialize($data);
    } elseif (is_null($value)) {
        // 删除缓存
        return $redis_instance->del($name);
    } else {
        // 设置缓存
        $value = is_numeric($value) ? $value : serialize($value);
        if ($expire == 0) {
            return $redis_instance->set($name, $value);
        } else {
            return $redis_instance->setex($name, $expire, $value);
        }
    }
}

/**
 * 返回成功结果
 * @param array|object|null $data 数据
 * @param string $msg 提示语
 * @return void
 * @throws HttpResponseException
 */
function result($data, $msg = 'success')
{
    $data = empty($data) ? [] : $data; //如果是null 则转换成[]
    $data = is_object($data) ? tools()::object2array($data) : $data;
    ResponseService::result($data, $msg);
}

/**
 * 返回错误信息
 * @param string $msg 提示语
 * @param integer $code 提示语
 * @param string|null $url 跳转URL
 * @param string $sub_code 子状态码
 * @param array $header header
 * @return void
 * @throws HttpResponseException|Exception
 */
function error($msg = '', $code = -1, $url = null, $sub_code = '', $header = [])
{
    ResponseService::error($msg, $code, $url, 3, $header, $sub_code);
}

/**
 * 返回成功信息
 * @param string $msg 提示语
 * @param string|null|bool $url 跳转URL
 * @return void
 * @throws HttpResponseException
 */
function success($msg = 'success', $url = null)
{
    ResponseService::success($msg, $url);
}

/**
 * URL跳转
 * @param string $url 跳转URL
 * @return void
 * @throws HttpResponseException
 */
function redirect(string $url)
{
    ResponseService::redirect($url);
}

function set_user_and_group($user = 'nginx', $user_group = 'nginx')
{
    if (!extension_loaded('posix')) {
        return false;
    }
    // Get uid.
    $user_info = posix_getpwnam($user);
    if (!$user_info) {
        //echo "Warning: User '{$user}' not exsits.\n";
        return false;
    }

    $uid = $user_info['uid'];

    if ($user_group) {
        $group_info = posix_getgrnam($user_group);
        if (!$group_info) {
            //echo "Warning: Group '{$user_group}' not exsits\n";
            return false;
        }
        $gid = $group_info['gid'];
    } else {
        $gid = $user_info['gid'];
    }

    // Set uid and gid.
    if ($uid !== posix_getuid() || $gid !== posix_getgid()) {
        if (!posix_setgid($gid) || !posix_initgroups($user_info['name'], $gid) || !posix_setuid($uid)) {
            //echo "Warning: change gid or uid fail.\n";
            return false;
        }
    }
    return true;
}
