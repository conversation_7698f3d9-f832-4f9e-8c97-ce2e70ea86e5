<?php
// 事件定义文件
return [
    'bind'      => [
        //'UserLogin' => 'app\event\UserLogin',
        // 更多事件绑定
    ],
    'listen'    => [
        'AppInit'  => ['app\listener\AppInit'],
        'HttpRun'  => [],
        'HttpEnd'  => [
            //          'HttpEnd' => 'app\event\HttpEnd',
        ],
        'LogLevel' => [],
        'LogWrite' => [],
        //'UserLogin' => ['app\listener\UserLogin'],
        // 更多事件监听
    ],
    'subscribe' => [
        //'app\subscribe\User',
        // 更多事件订阅
    ],
];
