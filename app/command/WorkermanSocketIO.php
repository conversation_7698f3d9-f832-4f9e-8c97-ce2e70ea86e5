<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command;

use app\model\WebsocketUser;
use Exception;
use PHPSocketIO\Socket;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use Workerman\Worker;
use Workerman\Lib\Timer;
use PHPSocketIO\SocketIO;
use Workerman\Protocols\Http\Request;
use Workerman\Connection\TcpConnection;

/**
 * Worker Server 命令行类
 */
class WorkermanSocketIO extends Command
{
    protected array $config = [];
    // 全局数组保存uid在线数据
    protected array $uidConnectionMap = [];
    // 记录最后一次广播的在线用户数
    protected int $last_online_count = 0;
    // 记录最后一次广播的在线页面数
    protected int $last_online_page_count = 0;
    protected SocketIO $sender_io;

    public function configure()
    {
        $this->setName('worker:socketio')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->setDescription('Workerman SocketIO Server for ThinkPHP');
    }

    protected function stop()
    {
        sleep(1);
        exit();
    }

    public function execute(Input $input, Output $output)
    {
//        cli_set_process_title('xyf workerman socketio- ' . microsecond());
        set_user_and_group();

        // PHPSocketIO服务
        $port            = 2120;
        $this->sender_io = new SocketIO($port);
        // 客户端发起连接事件时，设置连接socket的各种事件回调
        $this->sender_io->on('connection', function ($socket) {
            /**
             * @var $socket Socket
             */
            // 当客户端发来登录事件时触发
            $socket->on('login', function ($uid) use ($socket) {
                // 已经登录过了
                if (isset($socket->uid)) {
                    echo 'uid:' . $socket->uid . "已经存在当前连接中\n";
                    return;
                }
                // 更新对应uid的在线数据
                $uid = (string)$uid;
                if (!isset($this->uidConnectionMap[$uid])) {
                    $this->uidConnectionMap[$uid] = 0;
                }
                // 这个uid有++$this->uidConnectionMap[$uid]个socket连接
                ++$this->uidConnectionMap[$uid];
                // 将这个连接加入到uid分组，方便针对uid推送数据
                $socket->join($uid);
                $socket->uid = $uid;
                // 更新这个socket对应页面的在线数据
                $date_time = date('Y-m-d H:i:s');
                $headers   = $socket->request->headers ?? [];
                $data      = [
                    'code' => 0,
                    'msg'  => $uid . "欢迎[" . $uid . "]登录:当前<b>{$this->last_online_count}</b>人在线，共打开<b>{$this->last_online_page_count}</b>个页面!您的IP:" . $headers['x-real-ip'] ?? '0.0.0.0',
                    'data' => [
                        'last_online_count'      => $this->last_online_count,
                        'last_online_page_count' => $this->last_online_page_count,
                    ],
                    'time' => $date_time
                ];
                $socket->emit('login_callback', $data);
                if (strpos($uid, 'web') !== 0) {
                    return;
                }
                try {
                    $channel           = 'websocket';
                    $db_websocket_user = new WebsocketUser();
                    $map               = [
                        ['uid', '=', $uid],
                        ['channel', '=', $channel],
                    ];
                    $user_id           = $db_websocket_user->where($map)->value('id');
                    if ($user_id) {
                        //存在做更新
                        $data  = [
                            'status'          => 1,
                            'last_login_time' => $date_time,
                            'update_time'     => $date_time,
                        ];
                        $map[] = ['id', '=', $user_id];
                        $db_websocket_user->where($map)->data($data)->update();
                        echo $uid . "于" . $date_time . "上线了,更新成功\n";
                    } else {
                        $array = explode('_', $uid);
                        //不存在做新增
                        $data = [
                            'uid'              => $uid,
                            'channel'          => $channel,
                            'platform'         => $array[0] ?? '',
                            'type'             => $array[1] ?? '',
                            'bid'              => $array[2] ?? '',
                            'status'           => 1,
                            'first_login_time' => $date_time,
                            'last_login_time'  => $date_time,
                            'create_time'      => $date_time,
                            'update_time'      => $date_time,
                        ];
                        switch ($data['type']) {
                            case 'user':
                                $data['user_guid'] = $array[3] ?? '';
                                break;
                            case 'member':
                                $data['member_guid'] = $array[3] ?? '';
                                break;
                            default:
                        }
                        $db_websocket_user->insert($data);
                        echo $uid . "于" . $date_time . "上线了,插入成功\n";
                    }
                } catch (Exception $e) {
                    echo $e->getMessage() . "\n";
                    $this->stop();
                }
            });

            // 当客户端发来pong时触发
            $socket->on('pong', function ($uid) use ($socket) {
                $date_time = date('Y-m-d H:i:s');
                $data      = [
                    'code' => 0,
                    'msg'  => '服务端收到了您的响应' . $uid,
                    'time' => $date_time
                ];
                //$sender_io->to($uid)->emit('pong_callback', $data);
                $socket->emit('pong_callback', $data);
            });
            //当客户端发来登录事件时触发
            $socket->on('send', function ($data) use ($socket) {
                $array_data = json_decode($data, true);
                $uid        = $array_data['uid'] ?? 0;
                $type       = $array_data['type'];
                $date_time  = date('Y-m-d H:i:s');
                switch ($type) {
                    case 'send_type';
                        $data = [
                            'code' => 0,
                            'msg'  => 'send_type1',
                            'data' => $array_data,
                            'time' => $date_time
                        ];
                        break;
                    default:
                        $data = [
                            'code' => 0,
                            'msg'  => '收到了' . $type . '类型的消息',
                            'time' => $date_time,
                        ];
                }
                if (empty($data)) {
                    return;
                }
                if ($uid && isset($this->uidConnectionMap[$uid])) {
                    //$sender_io->to($uid)->emit('push', $data);
                    $socket->emit('push', $data);
                } else {
                    //$sender_io->emit('push', $data); //包括当前连接的所有人收到消息
                    $socket->broadcast->emit('push', $data); //除了当前连接以外所有连接会收到消息
                }
            });
            // 当客户端断开连接是触发（一般是关闭网页或者跳转刷新导致）
            $socket->on('disconnect', function () use ($socket) {
                if (!isset($socket->uid)) {
                    return;
                }
                // 将uid的在线socket数减一
                if (--$this->uidConnectionMap[$socket->uid] <= 0) {
                    unset($this->uidConnectionMap[$socket->uid]);
                }
                $uid = (string)$socket->uid;
                if (strpos($uid, 'web') !== 0) {
                    return;
                }
                try {
                    $channel           = 'websocket';
                    $db_websocket_user = new WebsocketUser();
                    $map               = [
                        ['uid', '=', $uid],
                        ['channel', '=', $channel],
                    ];
                    $user_id           = $db_websocket_user->where($map)->value('id');
                    if ($user_id) {
                        //存在做更新
                        $date_time = date('Y-m-d H:i:s');
                        $data      = [
                            'status'              => 0,
                            'last_login_out_time' => $date_time,
                            'update_time'         => $date_time,
                        ];
                        $map[]     = ['id', '=', $user_id];
                        $db_websocket_user->where($map)->data($data)->update();
                        echo $uid . "于" . $date_time . "离线了,更新成功\n";
                    }
                } catch (Exception $e) {
                    echo $e->getMessage() . "\n";
                    $this->stop();
                }
            });
        });

        // 当$sender_io启动后监听一个http端口，通过这个端口可以给任意uid或者所有uid推送数据
        $this->sender_io->on('workerStart', function () {
            // 监听一个http端口  推送数据的url格式 type=message&to=uid&data=xxxx
            $inner_http_worker = new Worker('http://0.0.0.0:2121');
            // 当http客户端发来数据时触发
            $inner_http_worker->onMessage = function (TcpConnection $http_connection, Request $request) {
                /**
                 * @var $sender_io Socket
                 */
                $sender_io = $this->sender_io;
                $post      = $request->post() ?: $request->get();
                if (empty($post['type']) || empty($post['data']) || !isset($post['to'])) {
                    $data = ['code' => -1, 'msg' => 'type or  data or to can not empty'];
                    return $http_connection->send(json_encode($data, JSON_UNESCAPED_UNICODE));
                }
                $to        = $post['to'];
                $type      = $post['type'];
                $emit_data = json_decode($post['data'], true);
                if ($to) {
                    //单发
                    if (!isset($this->uidConnectionMap[$to])) {
                        $data   = ['code' => -1, 'msg' => 'offline'];
                        $result = $http_connection->send(json_encode($data, JSON_UNESCAPED_UNICODE));
                        try {
                            //如果推送发现离线,则更新status状态为0
                            $channel           = 'websocket';
                            $db_websocket_user = new WebsocketUser();
                            $map               = [
                                ['uid', '=', $to],
                                ['channel', '=', $channel],
                            ];
                            $user_id           = $db_websocket_user->where($map)->value('id');
                            if ($user_id) {
                                //存在做更新
                                $data  = [
                                    'status'      => 0,
                                    'update_time' => date('Y-m-d H:i:s'),
                                ];
                                $map[] = ['id', '=', $user_id];
                                $db_websocket_user->where($map)->data($data)->update();
                            }
                        } catch (Exception $e) {
                            echo $e->getMessage() . "\n";
                        }
                        return $result;
                    }
                    $sender_io->to($to)->emit($type, $emit_data);
                } else {
                    //广播
                    $sender_io->emit($type, $emit_data);
                }
                $data = ['code' => 0, 'msg' => 'ok'];
                return $http_connection->send(json_encode($data, JSON_UNESCAPED_UNICODE));
            };
            // 执行监听
            $inner_http_worker->listen();

            // 一个定时器，定时向所有uid推送当前uid在线数及在线页面数
            Timer::add(1, function () {
                $sender_io             = $this->sender_io;
                $online_count_now      = count($this->uidConnectionMap);
                $online_page_count_now = array_sum($this->uidConnectionMap);
                // 只有在客户端在线数变化了才广播，减少不必要的客户端通讯
                if ($this->last_online_count != $online_count_now || $this->last_online_page_count != $online_page_count_now) {
                    $data = [
                        'code' => 0,
                        'msg'  => "心跳返回:当前 <b>" . $online_count_now . "</b > 人在线，共打开<b >" . $online_page_count_now . "</b > 个页面",
                        'data' => [
                            'last_online_count'      => $online_count_now,
                            'last_online_page_count' => $online_page_count_now,
                        ],
                        'time' => date('Y-m-d H:i:s'),
                    ];
                    $sender_io->emit('update_online_count', $data);
                    $this->last_online_count      = $online_count_now;
                    $this->last_online_page_count = $online_page_count_now;
                }
            });
            // 一个定时器，定时向所有uid推送当前uid在线数及在线页面数
            Timer::add(10, function () {
                $sender_io = $this->sender_io;
                $data      = [
                    'code' => 0,
                    'msg'  => '这是一条ping消息',
                    'time' => date('Y-m-d H:i:s'),
                ];
                $sender_io->emit('ping', $data);
            });
        });
        // Run worker
        Worker::$pidFile      = app()->getRuntimePath() . $port . '_socket.pid';
        Worker::$logFile      = app()->getRuntimePath() . $port . '_log_file.log';
        Worker::$stdoutFile   = app()->getRuntimePath() . $port . '_stdout_file.log';
        Worker::$processTitle = 'xyf WorkermanSocketIO';
        Worker::runAll();
    }
}