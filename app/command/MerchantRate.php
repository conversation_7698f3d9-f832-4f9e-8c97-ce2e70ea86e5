<?php
declare (strict_types=1);

namespace app\command;

use app\model\SubMerchantRateApplyNote;
use Exception;
use OpenApi\SubMerchant;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class MerchantRate extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('merchant_rate:update')
            ->addArgument('page', Argument::REQUIRED, 'The Page Of Required Update')
            ->setDescription('update merchant_rate list');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $lock_instance = get_distributed_instance();
        $lock_key      = __FUNCTION__;
        $lock          = $lock_instance->get_lock($lock_key);
        $sub           = new SubMerchant();
        $max_page      = (int)$this->input->getArgument('page');
        $output->writeln('merchant list updated' . $max_page);
        for ($page = 1; $page <= $max_page; $page++) {
            $output->writeln('正在开始第:' . $page . '页');
            $data         = ['page' => $page];
            $insert_num   = 0;
            $default_data = ['offset' => $page];
            try {
                $result = $sub->crate_benchmarking_getapplylist(array_merge($default_data, $data));
            } catch (Exception $e) {
                $lock_instance->unlock($lock);
                $msg = '第' . $page . '页_获取费率申请列表列表异常:' . $e->getMessage();
                wr_log($msg, 1);
            }
            if (empty($result['applylist'])) {
                //applylist为空说明没有和获取到列表,直接跳出循环
                break;
            }
            $total = $result['total_num'];
            foreach ($result['applylist'] as $merchant) {
                $apply_id      = $merchant['apply_id'];
                $merchant_id   = $merchant['merchant_code'];
                $merchant_name = $merchant['merchant_name'];
                $examine_time  = $merchant['apply_time'];
                $cache_key     = 'get_sub_merchant_rate_apply_list:' . $merchant_id . $apply_id;
                if (cache($cache_key)) {
                    $output->writeln('商户号:' . $merchant_id . '已处理过,无需重复处理');
                    continue;
                }
                $db    = new SubMerchantRateApplyNote();
                $map   = [
                    ['merchant_id', '=', $merchant_id],
                    ['apply_id', '=', $apply_id],
                ];
                $count = $db->where($map)->count();
                if ($count == 0) {
                    $insert_data = [
                        'merchant_id'   => $merchant_id,
                        'merchant_name' => $merchant_name,
                        'apply_id'      => $apply_id,
                        'examine_time'  => $examine_time,
                        'status'        => 1,
                    ];
                    $db->save($insert_data);
                    $insert_num++;
                }
                cache($cache_key, format_timestamp(), 3600 * 24 * 7);
            }
            if ($insert_num) {
                $msg = '第' . $page . '页_发现有' . $insert_num . '个费率审核通过,当前审核通过' . $total . '个商户';
                wr_log($msg);
            }
            $output->writeln('已经结束:' . $page . '页');
        }
        // 指令输出
        $output->writeln('merchant_rate list updated');
        $lock_instance->unlock($lock);
    }
}
