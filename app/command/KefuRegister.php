<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use GatewayWorker\Register;

class KefuRegister extends Command
{
    protected function configure()
    {
        $this->setName('kefu_register')->setDescription('启动客服Register进程');
    }

    protected function execute(Input $input, Output $output)
    {
        $config        = config('kefu_socket');
        $register_port = $config['register_port'];
        $register      = new Register('text://0.0.0.0:' . $register_port);
        Register::runAll();
    }
}
