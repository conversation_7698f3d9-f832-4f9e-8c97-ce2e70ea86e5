<?php
declare (strict_types=1);

namespace app\command;

use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;
use Swoole\Coroutine\WaitGroup;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\queue\Worker;
use function Swoole\Coroutine\run;


class Test extends Command
{

    /**
     * The queue worker instance.
     * @var Worker
     */
    protected $worker;

    /**
     * The queue worker pid.
     * @var string
     */
    protected $pid;

    public function __construct(Worker $worker)
    {
        parent::__construct();
        $this->worker = $worker;
        $this->pid    = getmypid();
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('test')
            ->addArgument('action', Argument::OPTIONAL, 'The action') //调用方法
            ->setDescription('the test command');
    }

    public function batch_job()
    {
        $job_name = 'Tools@sleep';
        $job_data = [
            'seconds' => 120,
        ];
        for ($i = 1; $i <= 30; $i++) {
            $all_job_data[] = [
                'seconds' => rand(5, 60),
            ];;
        }
        job()->set_job_name($job_name)->push_job($all_job_data);
    }

    public function a7()
    {
        dump(url('index/tools/action') . '');
//        dump(url('') . '');
    }

    public function a6()
    {
        Coroutine::set(['hook_flags' => SWOOLE_HOOK_ALL | SWOOLE_HOOK_CURL]); //真正的协程化所有类型，包括CURL
        run(function () {
            $url    = 'https://www.yikayi.net/index/tools/sleep?sleep=5';
            $wg     = new WaitGroup();
            $result = [];

            $wg->add();
            //启动第一个协程
            Coroutine::create(function () use ($wg, &$result, $url) {
                //启动一个协程客户端client，请求淘宝首页
//                $cli = new Client('www.taobao.com', 443, true);
//                $cli->setHeaders([
//                    'Host'            => 'www.taobao.com',
//                    'User-Agent'      => 'Chrome/49.0.2587.3',
//                    'Accept'          => 'text/html,application/xhtml+xml,application/xml',
//                    'Accept-Encoding' => 'gzip',
//                ]);
//                $cli->set(['timeout' => 1]);
//                $cli->get('/index.php');
//
//                $result['taobao'] = $cli->body;
//                $cli->close();
                $result['taobao'] = curl()->get($url)->get_body();
                $wg->done();
            });

            $wg->add();
            //启动第二个协程
            Coroutine::create(function () use ($wg, &$result, $url) {
                //启动一个协程客户端client，请求百度首页
//                $cli = new Client('www.baidu.com', 443, true);
//                $cli->setHeaders([
//                    'Host'            => 'www.baidu.com',
//                    'User-Agent'      => 'Chrome/49.0.2587.3',
//                    'Accept'          => 'text/html,application/xhtml+xml,application/xml',
//                    'Accept-Encoding' => 'gzip',
//                ]);
//                $cli->set(['timeout' => 1]);
//                $cli->get('/index.php');
//
//                $result['baidu'] = $cli->body;
//                $cli->close();
                $result['baidu'] = curl()->get($url)->get_body();
                $wg->done();
            });

            //挂起当前协程，等待所有任务完成后恢复
            $wg->wait();
            //这里 $result 包含了 2 个任务执行结果
            var_dump($result);
        });
    }

    public function a5()
    {

        run(function () {
            $wg     = new WaitGroup();
            $result = [];

            $wg->add();
            //启动第一个协程
            Coroutine::create(function () use ($wg, &$result) {
                //启动一个协程客户端client，请求淘宝首页
                $cli = new Client('www.taobao.com', 443, true);
                $cli->setHeaders([
                    'Host'            => 'www.taobao.com',
                    'User-Agent'      => 'Chrome/49.0.2587.3',
                    'Accept'          => 'text/html,application/xhtml+xml,application/xml',
                    'Accept-Encoding' => 'gzip',
                ]);
                $cli->set(['timeout' => 1]);
                $cli->get('/index.php');

                $result['taobao'] = $cli->body;
                $cli->close();

                $wg->done();
            });

            $wg->add();
            //启动第二个协程
            Coroutine::create(function () use ($wg, &$result) {
                //启动一个协程客户端client，请求百度首页
                $cli = new Client('www.baidu.com', 443, true);
                $cli->setHeaders([
                    'Host'            => 'www.baidu.com',
                    'User-Agent'      => 'Chrome/49.0.2587.3',
                    'Accept'          => 'text/html,application/xhtml+xml,application/xml',
                    'Accept-Encoding' => 'gzip',
                ]);
                $cli->set(['timeout' => 1]);
                $cli->get('/index.php');

                $result['baidu'] = $cli->body;
                $cli->close();

                $wg->done();
            });

            //挂起当前协程，等待所有任务完成后恢复
            $wg->wait();
            //这里 $result 包含了 2 个任务执行结果
            var_dump($result);
        });

    }

    public function a4()
    {
        $method      = 'test';
        $class       = 'app\model\AlipayConfig';
        $num         = 3;
        $param_array = [];
        for ($i = 1; $i < $num; $i++) {
            $param_array[] = [$i];
        }
        dump(format_timestamp());
        $result = job()->batch_call($class, $method, $param_array);
        dump(format_timestamp());
        dump($result);
    }

    public function a2()
    {
        $job_name = 'Tools@sleep';
        $seconds  = 30;
        for ($i = 1; $i <= 10; $i++) {
            $all_job_data[] = [
                'seconds' => $seconds,
            ];
        }
        job()->set_redis_connections()->set_worker_num(30)->set_job_name($job_name)->push_job($all_job_data);
    }

    protected function add_job()
    {
        for ($i = 1; $i <= 200; $i++) {
            job()->set_job_name('Tools@sleep')->push_job(['seconds' => 10]); //发送休眠队列,用于阻塞
        }
    }

    protected function no()
    {
        $id     = '36093138';
        $result = job()->retry_sync($id);
        var_dump($result);
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $action     = $input->getArgument('action');
        $begin_time = microtime(true);
        $output->writeln($action . '开始,当前时间:' . format_timestamp());
        if (method_exists($this, $action)) {
            call_user_func_array([$this, $action], []);
        } else {
            $output->writeln($action . '方法不存在,当前时间:' . format_timestamp());
        }
        $diff = get_diff_time($begin_time);
        $output->writeln($action . '结束,当前时间:' . format_timestamp() . ';耗时:' . $diff . '秒');
    }
}
