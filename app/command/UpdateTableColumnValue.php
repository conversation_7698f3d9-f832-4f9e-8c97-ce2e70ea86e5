<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class UpdateTableColumnValue extends Command
{

    protected function configure()
    {
        // 指令示例 php think update_table_column_value  --schema=platform   --column_name=bid  --before_value=before_value  --after_value=after_value
        $this->setName('update_table_column_value')
            ->addOption('schema', null, Option::VALUE_OPTIONAL, 'The schema', 'platform')
            ->addOption('column_name', null, Option::VALUE_OPTIONAL, 'The column_name', 'bid')
            ->addOption('before_value', null, Option::VALUE_OPTIONAL, 'The before_value')
            ->addOption('after_value', null, Option::VALUE_OPTIONAL, 'The after_value')
            // ->addOption('cmd', null, Option::VALUE_OPTIONAL, 'The cmd', null)
            // ->addOption('max_num', null, Option::VALUE_OPTIONAL, 'The cmd', 100)
            ->setDescription('the UpdateTableColumnValue command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $output->writeln('执行任务开始');
        $schema       = $input->getOption('schema');
        $column_name  = $input->getOption('column_name');
        $before_value = $input->getOption('before_value');
        $after_value  = $input->getOption('after_value');
        $sql          = "SELECT table_name FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$schema' AND TABLE_TYPE='BASE TABLE'";
        $table_list   = Db::query($sql);
        $table_list   = array_column($table_list, 'table_name');
        foreach ($table_list as $key => $table) {
            $sql              = "select COLUMN_NAME from information_schema.COLUMNS where table_name = '$table' and table_schema = '$schema'";
            $column_name_list = Db::query($sql);
            $column_name_list = array_column($column_name_list, 'COLUMN_NAME');
            if (in_array($column_name, $column_name_list) && !in_array($table, ['api_log'])) {
                $msg = 'table:' . $table . '存在:' . $column_name . '字段,正在更新中';
                $output->writeln($msg);
                $result = Db::name($table)->where($column_name, $before_value)->data([$column_name => $after_value])->update();
                $msg    = 'table:' . $table . '更新成功,影响行数' . (int)$result;
                $output->writeln($msg);
            }
        }
        //      $result = Db::name('business')->where('guid', $before_value)->data(['guid' => $after_value])->update();
        //      $msg    = 'table:business' . '更新成功,影响行数' . (int)$result;
        //      $output->writeln($msg);
        while (true) {
            sleep(100);
        }
        $output->writeln('执行完毕');
    }
}
