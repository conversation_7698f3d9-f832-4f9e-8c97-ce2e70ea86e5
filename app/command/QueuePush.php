<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

class QueuePush extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('queue_push')
            ->addOption('num', null, Option::VALUE_OPTIONAL, 'The queue num', 100)
            ->addOption('connection', null, Option::VALUE_OPTIONAL, 'The connection', 'database')
            ->addOption('queue_name', null, Option::VALUE_OPTIONAL, 'The queue_name', 'default')
            ->addOption('job_name', null, Option::VALUE_OPTIONAL, 'The job_name', 'Tools@sleep')
            ->setDescription('the queue_push command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        cli_set_process_title('xyf queue push- ' . microsecond());
        $num        = $input->getOption('num');
        $connection = $input->getOption('connection');
        $queue_name = $input->getOption('queue_name');
        $job_name   = $input->getOption('job_name');
        $output->writeln('队列任务添加开始----' . microsecond() . PHP_EOL);
        for ($i = 0; $i < $num; $i++) {
            job()->set_connections($connection)->set_queue_name($queue_name)->set_job_name($job_name)->push_job();
        }
        $output->writeln('队列任务添加结束----' . microsecond() . PHP_EOL);
    }
}
