<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\queue\Worker;

class YuYue extends Command
{

    /**
     * The queue worker instance.
     * @var Worker
     */
    protected $worker;

    /**
     * The queue worker pid.
     * @var string
     */
    protected $pid;

    public function __construct(Worker $worker)
    {
        parent::__construct();
        $this->worker = $worker;
        $this->pid    = getmypid();
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('yu_yue')
            ->addArgument('connection', Argument::OPTIONAL, 'The name of the queue connection to work')
            ->setDescription('the yu_yue command');
    }

    public function log($msg)
    {
        $this->output->writeln(format_timestamp() . '--' . $msg);
    }


    protected function execute(Input $input, Output $output)
    {

        set_user_and_group();
        while (true) {
            $this->log('正在监控中...');
//            $url         = 'https://h5.cmskchp.com/ors/ors/queryVenueList';
            $productCode = '9635959C13CC142B813E52DBC464EE07';
//            $data        = [
//                'productCode' => $productCode,
//                'venueDate'   => "2024-10-02",
//            ];
            $cookies   = '_c_WBKFRo=7fYE0tNgC0YWnnBp330wUkooDXm3CMMzf5DhFlrP;_nb_ioWEgULi;acw_tc=0bdd26ca17273158760845046e12ae546930246f65e4bbaf6f532a8b656c86;cdn_sec_tc=dde5cb2717273158759743913edd9deca1dca3f5cff21544419bdace04;chanId=0;cruiseChanId=4;openid=oZs2yuLSjvEw68pE1wuniasIDuwY;openid-sign=1727310715;SESSION=372ac310103969ba5d36ae8acc14f56063151908;user_from=MicroMessenger';
            $date_list = [
                'C2EF53A8DACF3A8774393E2DBADF29D0',
                '4FF72691967FC3B0EB9AD5EFFABE2783',
                '0E77E616F9A22DAB1CA4FCB4162AD010',
                '75B146C48802AD026F1BF8B898371EFB',
                '3005DC15117B4B8E09479F79DB52CB51',
                '4206BC43D5207F2F590B9837C7A49F0C',
                '2A7BDCEDFF451FC537BE729B687CA037',
            ];


            $date_list = [
                '4206BC43D5207F2F590B9837C7A49F0C',
                '2A7BDCEDFF451FC537BE729B687CA037',
                '3005DC15117B4B8E09479F79DB52CB51',
                '75B146C48802AD026F1BF8B898371EFB',
                '0E77E616F9A22DAB1CA4FCB4162AD010',
                '4FF72691967FC3B0EB9AD5EFFABE2783',
                'C2EF53A8DACF3A8774393E2DBADF29D0',


                '21B4A88DFD1B585240BA8097B77E887A',
                '6DE735447CBA4A71547E91307D873EF7',
                '543A13F3783D5AC5F611B57B88E1AED3',
                'AF6BF7675F5B6F62000B4980D6CCDC35',
                '37D43DC6E7C04C5ED82874D7E3CDB335',
                '6699FC84825F9A1499FF2CCD934E28AF',
                '1C7583A2B223D7ED7175F3E11FDFC032',

            ];


            foreach ($date_list as $date_code) {
                $sub_order_url = cache('url:' . $date_code);
                if (!$sub_order_url) {
                    $url    = 'https://h5.cmskchp.com/ors/ors/loadSubmitOrderData';
                    $data   = [
                        'productCode' => $productCode,
                        'venueCode'   => $date_code,
                    ];
                    $result = curl()->form_params()->set_timeout(5)->set_cookies($cookies)->post($url, $data)->get_body();
                    if (is_array($result) && isset($result['message']) && !empty($result['message']['url'])) {
                        $sub_order_url = $result['message']['url'];
                        cache('url:' . $date_code, $sub_order_url);
                        $this->log('获取提交订单地址成功:' . $sub_order_url);
                    } else {
                        $this->log($date_code . '当前还未返回提交订单地址,继续监控...');
                    }
                }
                if ($sub_order_url) {
                    $url  = 'https://h5.cmskchp.com/ors' . $sub_order_url;
                    $list = ["6286618", "6286562"];
                    foreach ($list as $user) {
                        $data   = [
                            'productCode' => $productCode,
                            'phone'       => "18603047034",
                            'venueCode'   => $date_code,
                            'passengers'  => json_encode([$user], JSON_UNESCAPED_UNICODE),
                            //                        'passengers'  => json_encode([$passengerId], JSON_UNESCAPED_UNICODE),
                        ];
                        $result = curl()->form_params()->set_timeout(5)->ignore_log()->set_cookies($cookies)->post($url, $data)->get_body();
                        $this->log($date_code . '返回结果:' . json_encode($result, JSON_UNESCAPED_UNICODE));
                        if ($result['status'] == false) {
                            $msg = $result['message'] ?? '';
                            $msg != '系统异常' && wr_log($msg, 1);
                            wr_log($result, 1);
                        } elseif ($result['status'] && $result['message']['reserveStatus'] == 3) {
                            break;
                        } elseif ($result['status'] && $result['message']['reserveStatus'] != 3) {
                            wr_log($result, 1);
                            wr_log('游轮可能预约上了:' . $result['message']['message'], 1);
                        }

                    }
                }
                sleep(1);
            }
//            $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
//            foreach ($result['message']['venues'] as $venues) {
//                if ($venues['statusCode'] != 3 || 1 == 1) {
//                    $venues['statusCode'] != 3 && wr_log('正在尝试预约https://h5.cmskchp.com/ors?trans=toSubmitOrder&productCode=9635959C13CC142B813E52DBC464EE07&venueCode=' . $venues['code'], 1);
//                    $url    = 'https://h5.cmskchp.com/ors/ors/submitOrder';
//                    $data   = [
//                        'productCode' => $productCode,
//                        'phone'       => "18603047034",
//                        'venueCode'   => $venues['code'],
//                        'passengers'  => json_encode(["6286618", "6286562"], JSON_UNESCAPED_UNICODE),
//                        //                        'passengers'  => json_encode([$passengerId], JSON_UNESCAPED_UNICODE),
//                        //5894326 谢永发
//                        //5894815 王文娟
//                        //5895453 谢佳明
//                        //5895701 谢佳悦
//                        //5897933 谢福生
//                    ];
//                    $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
//                    if ($result['status'] == false) {
//                        $msg = $result['message'] ?? '';
//                        $msg != '系统异常' && wr_log($msg, 1);
//                        break;
//                    } elseif ($result['status'] && $result['message']['reserveStatus'] == 3) {
//                        break;
//                    } elseif ($result['status'] && $result['message']['reserveStatus'] != 3) {
//                        wr_log('游轮可能预约上了:' . $result['message']['message'], 1);
//                    }
////                    $passengers_list = ["5895453", "5894326", "5894815", "5895701", "5897933"];
////                    foreach ($passengers_list as $passengerId) {
////                        $data   = [
////                            'productCode' => $productCode,
////                            'phone'       => "18603047034",
////                            'venueCode'   => $venues['code'],
////                            //  'passengers'  => json_encode(["5897933", "5895701", "5895453", "5894326", "5894815"], JSON_UNESCAPED_UNICODE),
////                            'passengers'  => json_encode([$passengerId], JSON_UNESCAPED_UNICODE),
////                            //5894326 谢永发
////                            //5894815 王文娟
////                            //5895453 谢佳明
////                            //5895701 谢佳悦
////                            //5897933 谢福生
////                        ];
////                        $result = curl()->form_params()->set_cookies($cookies)->post($url, $data)->get_body();
////                        if ($result['status'] == false) {
////                            $msg = $result['message'] ?? '';
////                            $msg != '系统异常' && wr_log($msg, 1);
////                            break;
////                        } elseif ($result['status'] && $result['message']['reserveStatus'] == 3) {
////                            break;
////                        } elseif ($result['status'] && $result['message']['reserveStatus'] != 3) {
////                            wr_log('游轮可能预约上了:' . $result['message']['message'], 1);
////                        }
////                    }
//                }
//            }
//            sleep(5);
        }
    }
}
