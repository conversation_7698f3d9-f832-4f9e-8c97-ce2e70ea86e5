<?php

namespace app\command;

use app\model\Area;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class GenerateAreaData extends Command
{
    protected function configure()
    {
        $this->setName('generate:area')
            ->setDescription('生成行政区划JSON数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output_dir = 'vendor/alapi/address_parse/data/';
        $suffix     = '_from_database.json';
        // 生成省份数据
        $db        = new Area();
        $provinces = $db
            ->where('level', 'province')
            ->field('id as code, name')
            ->select()
            ->toArray();
        // 转换code为字符串
        foreach ($provinces as &$province) {
            $province['code'] = (string)$province['code'];
        }
        file_put_contents($output_dir . 'provinces' . $suffix, json_encode($provinces, JSON_UNESCAPED_UNICODE));

        // 生成城市数据
        $cities = $db
            ->where('level', 'city')
            ->field('id as code, name, pid as provinceCode')
            ->select()
            ->toArray();
        // 转换code和provinceCode为字符串
        foreach ($cities as &$city) {
            $city['code']         = (string)$city['code'];
            $city['provinceCode'] = (string)$city['provinceCode'];
        }
        file_put_contents($output_dir . 'cities' . $suffix, json_encode($cities, JSON_UNESCAPED_UNICODE));

        // 生成区县数据
        $areas = $db
            ->where('level', 'district')
            ->field('id as code, name, pid as cityCode')
            ->select()
            ->toArray();


        // 预先查询所有城市数据
        $cityIds = array_column($areas, 'cityCode');
        $cities  = $db
            ->whereIn('id', array_unique($cityIds))
            ->field('id, pid')
            ->select()
            ->toArray();

        // 构建城市ID到省份ID的映射
        $cityProvinceMap = array_column($cities, 'pid', 'id');

        // 补充省份代码
        foreach ($areas as &$area) {
            $area['provinceCode'] = (string)$cityProvinceMap[$area['cityCode']] ?? null;
            $area['code']         = (string)$area['code'];         // 转换当前区县代码为字符串
            $area['cityCode']     = (string)$area['cityCode']; // 转换城市代码为字符串
            $area['provinceCode'] = (string)$area['provinceCode']; // 转换城市代码为字符串
        }

        file_put_contents($output_dir . 'areas' . $suffix, json_encode($areas, JSON_UNESCAPED_UNICODE));

        $output->writeln('行政区划JSON数据生成完成');
    }
}
