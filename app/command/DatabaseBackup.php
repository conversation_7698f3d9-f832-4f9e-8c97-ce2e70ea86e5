<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class DatabaseBackup extends Command
{


    /**
     * The queue worker pid.
     * @var string
     */
    protected $pid;

    public function __construct()
    {
        parent::__construct();
        $this->pid = getmypid();
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('database_backup')
            ->addOption('schema', null, Option::VALUE_OPTIONAL, 'The schema', 'platform')
            ->setDescription('the database_backup command');
    }


    protected function execute(Input $input, Output $output)
    {
        $output->writeln('执行任务开始');
        set_user_and_group();
        $schema     = $input->getOption('schema');
        $sql        = "SELECT table_name FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$schema' AND TABLE_TYPE='BASE TABLE'";
        $table_list = Db::query($sql);
        $table_list = array_column($table_list, 'table_name');
        foreach ($table_list as $key => $table) {
            $sql              = " SHOW CREATE TABLE {$table} ";
            $schema_info      = Db::query($sql);
            $create_table_sql = $schema_info[0]['Create Table'];
            $path             = tools()::get_absolute_path_disk() . 'sql/';
            tools()::mkdir($path);
            file_put_contents($path . $table . '.sql', $create_table_sql);
        }
        $output->writeln('执行完毕');
    }
}
