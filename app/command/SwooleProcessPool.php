<?php
declare (strict_types=1);

namespace app\command;

use Swoole\Coroutine;
use Swoole\Event;
use Swoole\Process;
use Swoole\Process\Pool;
use swoole_process;
use think\App;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;

class SwooleProcessPool extends BasicQueue
{
    public int $way = 8;


    protected function configure()
    {
        // 指令配置
        $this->setName('swoole_process_pool')
            ->addArgument('connection', Argument::OPTIONAL, 'The name of the queue connection to work', null)
            ->addOption('queue', null, Option::VALUE_OPTIONAL, 'The queue to listen on', 'debug')
            ->addOption('worker_num', null, Option::VALUE_OPTIONAL, 'worker_num', 2)
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'memory', 32)
            ->setDescription('the SwooleProcessPool command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $this->listenForEvents();
        $worker_num = (int)$input->getOption('worker_num');
        $connection = $input->getArgument('connection') ?: $this->app->config->get('queue.default');
        $queue      = $input->getOption('queue') ?: $this->app->config->get("queue.connections.{$connection}.queue", 'default');
        swoole_set_process_name('xyf SwooleProcessPool Master');
        $process = new Process(function (swoole_process $process) use ($worker_num, $connection, $queue) {
            swoole_set_process_name('xyf Process 2');
            echo 'Child #' . getmypid() . " start and sleep {0}s" . PHP_EOL;
            $pool = new Pool($worker_num);
            $pool->set(['enable_coroutine' => true]);
            $pool->on('WorkerStart', function (Pool $pool, $workerId) use ($connection, $queue) {
                /** 当前是 Worker 进程 */
                static $running = true;
                Process::signal(SIGTERM, function () use (&$running) {
                    $running = false;
                    echo "TERM\n";
                });
                Process::signal(SIGKILL, function () use (&$running) {
                    $running = false;
                    echo "KILL\n";
                });
                Process::signal(SIGCHLD, function () use (&$running) {
                    // $running = false;
                    echo "CHLD\n";
                });
                Process::signal(SIGINT, function () use (&$running) {
                    $running = false;
                    echo "INT\n";
                });
                Process::signal(SIGUSR1, function () use (&$running) {
                    // $running = false;
                    echo "SIGUSR1\n";
                });

                swoole_set_process_name('xyf SwooleProcessPool Worker # ' . $workerId);
                echo("[Worker #{$workerId}] WorkerStart, pid: " . posix_getpid() . "\n");
                while ($running) {
                    Coroutine::sleep(1);
                    //                  do {
                    var_dump('开始执行任务');
                    //                      /** @var Worker $worker */
                    //                      //$worker = $this->app->make(Worker::class);
                    //                      $result = $this->worker->runNextJob($connection, $queue, 3, 0, 3);
                    //                      // $result = $worker->runNextJob($connection, $queue, 3, 0, 3);
                    //                      var_dump($result);
                    //                  } while ($result !== []);
                    //                  var_dump('没有任务啦,休眠1秒继续把');
                    $running      = false;
                    $childProcess = $pool->getProcess();
                    $result       = $childProcess->exec('/usr/local/php/bin/php', ['/mnt/website/yikayi.net_master/think', 'version']); // exec 系统调用
                    var_dump($result);
                    echo "sleep 1 - " . format_timestamp() . PHP_EOL;
                }
            });
            $pool->on('WorkerStop', function (Pool $pool, $workerId) {
                echo("[Worker #{$workerId}] WorkerStop\n");
                $pool->shutdown();
            });
            $pool->start();
        });
        $process->name('xyf Process');
        $pid = $process->start();
        Event::wait();
    }
}