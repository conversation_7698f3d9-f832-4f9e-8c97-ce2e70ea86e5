<?php
declare (strict_types=1);

namespace app\command;

use app\model\WechatUserInfo;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

class SyncWechatUserList extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('sync_wechat_user_list')
            ->addOption('appid', null, Option::VALUE_OPTIONAL, 'The Wechat Appid', 'wx0bcc73de74c62512')
            ->addOption('async', null, Option::VALUE_NONE, 'Async process')
            ->setDescription('the sync_wechat_user_list command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $appid       = $input->getOption('appid');
        $async       = $input->getOption('async');
        $wechat      = weixin($appid)::WeChatUser();
        $next_openid = '';
        do {
            $output->writeln(format_timestamp() . '开始获取用户列表');
            $user_list = $wechat->getUserList($next_openid);
            $total     = $user_list['total'];
            $count     = $user_list['count'];
            $output->writeln(format_timestamp() . '用户列表结束,共计:' . $total . '个,当前页:' . $count . '个');
            if ($count == 0) {
                break;
            }
            $next_openid = $user_list['next_openid'];
            $openid_list = $user_list['data']['openid'];
            // 5. 批量获取用户资料
            $page_size = 100;
            foreach (array_chunk($openid_list, $page_size) as $key => $item) {
                $current_number = ($key + 1) * $page_size;
                $output->writeln(format_timestamp() . '第' . $current_number . '个用户数据开始获取中...');
                if ($async) {
                    $job_data = ['appid' => $appid, 'openid_json' => json_encode($item, JSON_UNESCAPED_UNICODE)];
                    job()->set_redis_connections()->set_job_name('Weixin@batch_update_wechat_user')->push_job($job_data); //先发布队列,并发执行
                    continue;
                }
                $user_list = $wechat->getBatchUserInfo($item);
                $output->writeln(format_timestamp() . '第' . $current_number . '个用户数据获取成功');
                foreach ($user_list['user_info_list'] as $user_info) {
                    $subscribe = $user_info['subscribe'];
                    $openid    = $user_info['openid'];
                    if ($subscribe == 0) {
                        continue;
                    }
                    $db         = new WechatUserInfo();
                    $map        = [['appid', '=', $appid], ['openid', '=', $openid]];
                    $user_count = $db->where($map)->count();
                    //如果存在表情,则替换非法字符
                    if (isset($user_info['nickname'])) {
                        $user_info['nickname'] = tools()::emoji_encode($user_info['nickname']);
                    }
                    if ($user_count > 0) {
                        $db::update($user_info, $map);
                    } else {
                        $user_info['guid']  = create_guid();
                        $user_info['appid'] = $appid;
                        $db->save($user_info);
                    }
                }
                $output->writeln(format_timestamp() . '第' . $current_number . '个用户数据更新到数据库成功');
            }
            if ($total == $count) {
                $output->writeln(format_timestamp() . 'total_count相等');
                break;
            }
        } while ($next_openid);
        // 指令输出
        $output->writeln(format_timestamp() . 'appid:' . $appid . '用户列表同步更新成功,共计' . $total . '个');
    }

}
