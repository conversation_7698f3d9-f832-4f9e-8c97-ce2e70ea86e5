<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use GatewayWorker\Gateway;

class KefuGateway extends Command
{
    protected function configure()
    {
        $this->setName('kefu_gateway')->setDescription('启动客服Gateway进程');
    }

    protected function execute(Input $input, Output $output)
    {
        $config = config('kefu_socket');
        if ($config['is_open_ssl']) {
            $gateway            = new Gateway("Websocket://0.0.0.0:" . $config['ws_port'], $config['context']);
            $gateway->transport = 'ssl';
        } else {
            $gateway = new Gateway("Websocket://0.0.0.0:" . $config['ws_port']);
        }
        $gateway->name  = 'wsAppGateway';
        $gateway->count = $config['gateway_worker'];

        $gateway->lanIp                = '127.0.0.1';
        $gateway->startPort            = 2900;
        $gateway->registerAddress      = '127.0.0.1:' . $config['register_port'];
        $gateway->pingInterval         = 30;
        $gateway->pingNotResponseLimit = 2;
        $gateway->pingData             = '';
        Gateway::runAll();
    }
}
