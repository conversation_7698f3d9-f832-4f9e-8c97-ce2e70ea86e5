<?php
declare (strict_types=1);

namespace app\command;

use Swoole\Server;
use Swoole\Table;
use swoole_process;
use think\App;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;
use think\queue\Worker;

class QueueSwoolePools extends BasicQueue
{
    private $is_add = false;
    private $num = 0;
    private $pool;

    /**
     * @var Server //记录所有worker的process对象
     */

    public $server;

    /**
     * @var swoole_process[] 记录所有worker的process对象
     */
    private $workers = [];

    /**
     * @var array 记录worker工作状态
     */
    private $used_workers = [];

    /**
     * @var int 最小进程数
     */
    private $min_woker_num = 3;

    //  /**
    //   * @var int 初始进程数
    //   */
    //  private $start_worker_num = 10;

    /**
     * @var int 最大进程数
     */
    private $max_woker_num = 50;

    /**
     * 进程闲置销毁秒数
     * @var int
     */
    private $idle_seconds = 5;

    /**
     * @var int 当前进程数
     */
    private $curr_num;
    /**
     * @var int 当前进程数
     */
    private $add;
    /**
     * The queue worker instance.
     * @var Worker
     */
    protected $table;
    /**
     * 闲置进程时间戳
     * @var array
     */
    private $active_time = [];

    public int $way = 6;


    public function dojob()
    {
        /** @var Worker $worker */
        var_dump('开始执行任务');
        $worker = $this->app->make(Worker::class);
        $result = $worker->runNextJob('database', 'async', 3, 0, 3);
        var_dump($result);
        //      do {
        //          $result = $worker->runNextJob('database', 'async', 3, 0, 3);
        //          var_dump($result);
        //      } while ($result != []);
        var_dump('结束执行任务');
    }


    public function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $this->listenForEvents();
        $output->writeln('执行任务开始');
        // $worker_num = 5;
        $worker_num = $this->min_woker_num;
        swoole_set_process_name('xyf manage');

        $server           = new Server('127.0.0.1', 9502);
        $queue_start_time = time();
        $table            = new Table(1024);
        $table->column('int_value', Table::TYPE_INT, 8);
        $table->column('status', Table::TYPE_STRING, 64);
        $table->create();
        $server->set([
            'worker_num'        => $worker_num,
            'max_wait_time'     => 60,
            'reload_async'      => true,
            'task_ipc_mode'     => 3,
            'message_queue_key' => 'swoole_queue'
            //'admin_server'    => '0.0.0.0:9502',
        ]);
        $server->table = $table;
        $this->table   = $table;
        $server->on('Start', [$this, 'onStart']);
        $server->on('ManagerStart', [$this, 'onManagerStart']);
        $server->on('WorkerStart', [$this, 'onWorkerStart']);
        $server->on('WorkerStop', [$this, 'onWorkerStop']);
        $server->on('ManagerStop', [$this, 'onManagerStop']);
        $server->on('WorkerExit', [$this, 'onWorkerExit']);
        $server->on('Connect', [$this, 'onConnect']);
        $server->on('Close', [$this, 'onClose']);
        $server->on('Packet', [$this, 'onPacket']);
        $server->on('PipeMessage', [$this, 'onPipeMessage']);
        $server->on('Receive', [$this, 'onReceive']);
        $server->on('WorkerError', [$this, 'onWorkerError']);
        $server->on('BeforeReload', [$this, 'onBeforeReload']);
        $server->on('AfterReload', [$this, 'onAfterReload']);
        $server->on('BeforeShutdown', [$this, 'onBeforeShutdown']);
        $server->on('Shutdown', [$this, 'onShutdown']);
        //$server->on('Task', [$this, 'onTask']);
        $server->on('Shutdown', [$this, 'onShutdown']);
        $server->on('Finish', [$this, 'onFinish']);
        $server->start();
        $this->server = $server;
        $output->writeln('执行任务结束');
    }

    public function onReceive(Server $server, $fd, $reactor_id, $data)
    {
        if (!tools()::is_json($data)) {
            return;
        }
        $data_array = json_decode($data, true);
        $type       = $data_array['type'];
        switch ($type) {
            case 'queue':
                $server->task(json_encode($data_array['data']), JSON_UNESCAPED_UNICODE);
                break;
            case 'reload':
                $server->reload();
                break;
            case 'shutdown':
                $server->shutdown();
                break;
            default:
                wr_log('收到其他type:' . $type);
        }
    }


    public function onManagerStart(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
        var_dump('getManagerPid' . $server->getManagerPid());
        var_dump('getMasterPid' . $server->getMasterPid());
        var_dump('getWorkerPid' . $server->getWorkerPid());
        $this->install_signal_handler();
        //      Process::signal(SIGCHLD, function () {
        //          while ($ret = Process::wait(false)) {
        //              var_dump($ret);
        //              $pid = $ret['pid'] ?? -1;
        //              $this->num--;
        //              $reload = 1 === (int)($ret['code'] ?? 0);
        //          }
        //      });
        var_dump('pid=' . getmypid() . ',time=' . microsecond() . ',event=ManagerStart');
        swoole_set_process_name('xyf Manager');
        //          Timer::tick(1000 * 10, function () use ($server) {
        //              var_dump('time=.' . format_timestamp());
        //              return;
        //              $server->sendMessage("hello task process", 3);
        //
        ////              $process = new Process(function () {
        ////                  swoole_set_process_name('xyf Child');
        ////                  echo 'Child #' . getmypid() . " start and sleep {3}s" . PHP_EOL;
        ////                  sleep(3);
        ////                  echo 'Child #' . getmypid() . ' exit' . PHP_EOL;
        ////              });
        ////              $process->start();
        //          });
        var_dump('**************');
        while (true) {
            sleep(1);
            // 模拟有任务 发现database-default有队列了 快通知空闲的worker去执行
            $app_name       = tools()::get_lan_ip() . ':' . config('app.app_name');
            $redis          = get_redis_instance();
            $redis_key_name = $app_name . ':connection:queue_debug';
            $jobs           = $redis->brPop($redis_key_name, 3);
            if (empty($jobs)) {
                var_dump('没有任务.休眠3秒后继续');
                sleep(3);
                continue;
            }
            var_dump('模拟发现了任务 想办法投递');
            /**  @var $table Table */
            $table   = $server->table;
            $is_send = false;
            do {
                for ($worker_id = 1; $worker_id < $server->setting['worker_num']; $worker_id++) {
                    $worker_id_status = $table->get((string)$worker_id, 'status');
                    if ($worker_id_status == 0) {
                        $table->set((string)$worker_id, ['status' => 1]);
                        $server->sendMessage('dojob', $worker_id);
                        var_dump('投递成功给' . $worker_id . '进程');
                        $is_send = true;
                        break;
                    }
                }
                if ($is_send == false) {
                    //尝试去创建子进程做任务
                    var_dump('投递失败 查看目前已经创建的子进程');
                    sleep(1);
                    // $num = (int)$table->get('current_child_num', 'int_value');
                    var_dump('当前开的 子进程数' . $this->num . '个');
                    if ($this->num < 10) {
                        $table->incr('current_child_num', 'int_value');
                        var_dump('小于10个,当前开了' . $this->num . '个子进程,开起来');
                        $worker = new swoole_process([$this, 'callback_function']);
                        $this->num++;
                        $pid    = $worker->start();
                        $result = $worker->write("complete" . "pid=" . $pid);
                        var_dump($result);
                        $is_send = true;
                    } else {
                        var_dump('子进程数量太多了 不能再加啦 休眠一秒继续检测吧');
                        usleep(100000);
                    }
                }
            } while ($is_send = false);
        }


        //      Timer::tick(1000 * 60, function () use ($server) {
        //          //启动后自动查询是否需要检测队列是否有剩余任务
        //          $queue_connections = config('queue.connections');
        //          foreach ($queue_connections as $connection) {
        //              if (isset($connection['auto']) && $connection['auto'] == true) {
        //                  $type  = $connection['type'];
        //                  $queue = $connection['queue'];
        //                  $data  = ['type' => 'queue', 'data' => ['connection' => $type, 'queue' => $queue, 'auto' => true]];
        //                  $server->sendMessage(json_encode($data, JSON_UNESCAPED_UNICODE), 0);
        //              }
        //          }
        //      });
    }


    public function onStart(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
        swoole_set_process_name('xyf Master');
        echo 'pid=' . getmypid() . ',time=' . microsecond() . ',event=Start' . PHP_EOL;
        sleep(1);
    }

    public function onWorkerStart(Server $server, $worker_id)
    {
        // wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event=WorkerStart,worker_id:' . $worker_id);
        var_dump('pid=' . getmypid() . ',time=' . microsecond() . ',event=WorkerStart,worker_id:' . $worker_id);
        swoole_set_process_name('xyf Worker - ' . $worker_id);
        /**  @var $table Table */
        $table = $server->table;
        $table->set((string)$worker_id, ['int_value' => $worker_id, 'status' => 0]);
        // var_dump($this->used_workers);
        if ($worker_id < $server->setting['worker_num']) {

        }
    }

    public function onWorkerStop(Server $server, $worker_id)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onManagerStop(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onWorkerExit(Server $server, $worker_id)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onConnect(Server $server, $fd, $reactorId)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onClose(Server $server, $fd, $reactorId)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onPacket(Server $server, string $data, array $clientInfo)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onPipeMessage(Server $server, int $src_worker_id, mixed $message)
    {
        //$server->task($message);
    }

    public function onWorkerError(Server $server, int $worker_id, int $worker_pid, int $exit_code, int $signal)
    {
        //当 Worker/Task 进程发生异常后会在 Manager 进程内回调此函数。
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event=WorkerError, exit_code=' . $exit_code . ',signal=' . $signal . ',worker_id=' . $worker_id . ',worker_pid=' . $worker_pid);
    }

    public function onBeforeReload(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onAfterReload(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onBeforeShutdown(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onShutdown(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onFinish(Server $server, $task_id, $result)
    {
        //wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }


    public function callback_function(swoole_process $process)
    {
        var_dump('360360');
        swoole_event_add($process->pipe, function ($pipe) use ($process) {
            $data = trim($process->read());
            /** @var Worker $worker */
            var_dump('开始执行任务22:' . $data);
            /**  @var $table Table */
            $table = $this->table;
            $num   = (int)$table->get('current_child_num', 'int_value');
            swoole_set_process_name('xyf Child-' . $num);
            // $worker = $this->app->make(Worker::class);
            // $result = $worker->runNextJob('database', 'async', 3, 0, 3);
            // $this->dojob();
            sleep(rand(5, 15));
            var_dump('休眠10秒');
            // var_dump($result);
            var_dump('结束执行任务22');
            $table->decr('current_child_num', 'int_value');
            // $process->exit(0);
        });
        var_dump('378378');

    }

    /**
     * 设置当前时钟信号
     */
    private function registerTimeSig()
    {
        // p cntl_alarm(30);

        pcntl_signal(SIGALRM, function () {
            var_dump('SIGALRM');
            //超时后要退出该进程
        });
    }

    /**
     * 取消时钟信号
     */
    private function delTimeSig()
    {
        pcntl_signal(SIGALRM, SIG_IGN);
    }

    private function install_signal_handler()
    {
        // register signal
        //      Process::signal(SIGCHLD, function () {
        //          while ($ret = Process::wait(false)) {
        //              $pid = $ret['pid'] ?? -1;
        //              dump($ret);
        //              $reload = 1 === (int)($ret['code'] ?? 0);
        //          }
        //      });
        //
        //
        //      return;
        // install signal handler for dead kids
        pcntl_signal(SIGCHLD, array($this, 'sig_handler'));
        // pcntl_signal(SIGINT, array($this, 'sig_handler')); //ctrl+c
        // pcntl_signal(SIGTERM, array($this, 'sig_handler')); //暴力退出
        // 注册信号处理函数，处理子进程结束时的回收工作
    }

    private function sig_handler($signo)
    {
        echo "Recive: $signo \r";
        var_dump($signo);
        switch ($signo) {
            case SIGCHLD:
                while ($ret = swoole_process::wait(false)) {
                    var_dump($ret);
                    echo "PID={$ret['pid']}";
                    $this->num--;
                    var_dump('num 现在= ' . $this->num);
                }
                break;
            case SIGINT:
                echo "我被ctrl+c了\n";
                // $this->server->shutdown();
                exit();
                break;
            case SIGTERM:
                echo "暴力退出了\n";
                exit();
                break;
            case SIGUSR1:
                echo "退出主进程SIGUSR1\n";
                exit();
                break;
            default:
                break;
        }
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('pools')
            ->addOption('cmd', null, Option::VALUE_OPTIONAL, 'The cmd', null)
            ->setDescription('the test command');
    }
}
