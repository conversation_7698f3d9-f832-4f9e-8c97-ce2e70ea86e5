<?php
declare (strict_types=1);

namespace app\command;

use app\common\service\QueueConsumer;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class QueuePcntl extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('pcntl')
            ->addArgument('name', Argument::OPTIONAL, 'your name')
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 32)
            ->addOption('max_worker', null, Option::VALUE_OPTIONAL, 'The max_worker', 30)
            ->addOption('fork_number', null, Option::VALUE_OPTIONAL, 'The fork_number', 5)
            ->addOption('sleep', null, Option::VALUE_OPTIONAL, 'Number of seconds to sleep when no job is available', 60)
            ->setDescription('Do Pcntl Queue Job');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $memory      = $input->getOption('memory');
        $max_worker  = $input->getOption('max_worker');
        $fork_number = $input->getOption('fork_number');
        $name        = $input->getArgument('name');
        $output->writeln('Pcntl Queue Job is Starting:' . $name);
        $app_last_update_time = cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time');
        $config               = [
            'name'                 => 'delay:order',
            'tasks'                => 50,   // < 1 永久不退出,否则按照指定任务数量退出
            'memory_limit'         => $memory,      // 默认是配置文件4/1内存限制,单位:MB
            'max_consumers'        => 20,     // 临时+常驻消费者最多：8个
            'task_timeout'         => 35,     // 任务从队列消费超30秒，消费者退出并记录数据
            'idle_time'            => 30,     // 临时消费者空闲30秒没任务，自动退出节约资源
            'user'                 => 'nginx', // 用户
            'user_group'           => 'nginx', // 用户组
            'daemonize'            => false,  // 守护进程
            'logger'               => __DIR__ . DIRECTORY_SEPARATOR . 'runtime' . DIRECTORY_SEPARATOR . 'logs', // 日志目录
            'intervals'            => 3,  // 全部workers(临时+常驻),默认间隔10秒钟汇报一次当前状态给Master.
            'app_last_update_time' => $app_last_update_time
        ];
        $consumer             = new QueueConsumer($fork_number, $config);
        $consumer->setMaxWorker($max_worker);
        $consumer->start();
        $output->writeln('Pcntl Queue Job is Started:' . $name);
    }
}
