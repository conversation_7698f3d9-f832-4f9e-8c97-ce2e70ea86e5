<?php
declare (strict_types=1);

namespace app\command;

use Exception;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\exception\Handle;
use Throwable;

/**
 * Class WatchAppUpdate
 * @package app\command
 */
class WatchAppUpdate extends Command
{
    /** @var Handle */
    protected Handle $handle;

    /**
     * The queue worker pid.
     * @var int
     */
    protected int $pid;

    /**
     * Indicates if the worker should exit.
     *
     * @var bool
     */
    public bool $shouldQuit = false;

    /**
     * Crontab constructor.
     * @param Handle $handle
     */
    public function __construct(Handle $handle)
    {
        $this->handle = $handle;
        $this->pid    = getmypid();
        parent::__construct();
    }


    protected function configure()
    {
        $this->setName('watch_app_update')
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 32)
            ->setDescription('Do Crontab Job');
    }

    /**
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws Exception
     */
    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
//        $queue_start_time = time();
//        $memory           = (int)$input->getOption('memory');
        cli_set_process_title('xyf watch_app_update - ' . microsecond());
        $msg = 'WatchAppUpdate pid: ' . $this->pid . ' started successfully , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        if ($this->supportsAsyncSignals()) {
            $this->listenForSignals();
        }
        try {
            ini_set('default_socket_timeout', '-1');
            $redis    = get_redis_instance();
            $channels = ['app_update'];    // 订阅多个频道则直接添加该数组子元素
            $redis->subscribe($channels, function ($instance, $channel, $message) use ($channels) {
                // 这里除了 SUBSCRIBE、PSUBSCRIBE、UNSUBSCRIBE、PUNSUBSCRIBE 这4条命令之外其它命令都不能使用
                try {
                    tools()::update_app();
                    //更新当前服务器字段缓存
                } catch (\Exception|Throwable $e) {
                    wr_log('update app error' . $e->getMessage(), 1);
                } finally {
                    /* @var $instance \Redis */
                    // 处理完消息后，取消订阅,以退出阻塞
                    $instance->unsubscribe($channels);
                    $this->stop();
                }
            });
            wr_log('WatchAppUpdate 退出成功!');
            $this->stop();
        } catch (\Exception|Throwable $e) {
            sleep(3);
            wr_log('WatchAppUpdate 出错' . $e->getMessage());
            $this->stop();
        }
    }

    /**
     * Enable async signals for the process.
     *
     * @return void
     */
    protected function listenForSignals()
    {
        pcntl_async_signals(true);
        pcntl_signal(SIGTERM, function () {
            $this->shouldQuit = true;
        });
    }

    /**
     * Determine if "async" signals are supported.
     *
     * @return bool
     */
    protected function supportsAsyncSignals()
    {
        return extension_loaded('pcntl');
    }

    /**
     * 检查内存是否超出
     * @param int $memoryLimit
     * @return bool
     */
    protected function memoryExceeded($memoryLimit = 32)
    {
        return round(memory_get_usage(true) / 1024 / 1024, 4) >= $memoryLimit;
    }

    /**
     * 检测是否存在应用更新或者已经运行超过24小时
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function queueShouldRestart(int $queueStartTime): bool
    {
        $appLastUpdateTime = min((int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time'), time());
        return (time() - $queueStartTime > 3600 * 24) || ($queueStartTime < $appLastUpdateTime);
    }

    /**
     * 检查是否要重启守护进程
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function stopIfNecessary($queueStartTime, $memory)
    {
        if ($this->shouldQuit || $this->memoryExceeded($memory) || $this->queueShouldRestart($queueStartTime)) {
            $this->stop();
        }
    }

    /**
     * 退出进程
     *
     * @param int $status
     * @return void
     */
    public function stop($status = 0)
    {
        $msg = 'WatchAppUpdate pid: ' . $this->pid . ' exited , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        //wr_log($msg);
        exit($status);
    }

    /**
     * @param $response
     */
    protected function output($response)
    {
        $this->output->writeln($response);
    }
}