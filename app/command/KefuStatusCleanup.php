<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\model\User;
use Workerman\Lib\Timer;

/**
 * 客服状态清理定时任务
 * 用于清理异常断线但状态仍为在线的客服
 */
class KefuStatusCleanup extends Command
{
    protected function configure()
    {
        $this->setName('kefu:status-cleanup')
            ->setDescription('清理异常断线的客服在线状态');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行客服状态清理任务...');

        try {
            // 获取所有状态为在线的客服
            $online_kefus = User::where('service_status', 1)->select();

            $cleaned_count = 0;
            $checked_count = 0;

            foreach ($online_kefus as $kefu) {
                $checked_count++;

                // 检查客服是否真的在线（通过WebSocket连接检查）
                $is_really_online = $this->checkKefuReallyOnline($kefu['guid']);

                if (!$is_really_online) {
                    // 客服实际已离线，更新状态
                    User::where('guid', $kefu['guid'])->update([
                        'service_status' => 0,
                        'update_time' => date('Y-m-d H:i:s')
                    ]);

                    $cleaned_count++;
                    $output->writeln("清理离线客服: {$kefu['name']} ({$kefu['guid']})");

                    // 记录日志
                    debug_log([
                        '[KEFU_CLEANUP] 清理异常离线客服',
                        'kefu_guid' => $kefu['guid'],
                        'kefu_name' => $kefu['name'],
                        'bid' => $kefu['bid']
                    ]);
                }
            }

            $output->writeln("客服状态清理完成: 检查了 {$checked_count} 个客服，清理了 {$cleaned_count} 个异常状态");
        } catch (\Exception $e) {
            $output->writeln("客服状态清理失败: " . $e->getMessage());
            debug_log('[KEFU_CLEANUP] 清理任务异常: ' . $e->getMessage());
        }
    }

    /**
     * 检查客服是否真的在线
     * @param string $kefu_guid
     * @return bool
     */
    private function checkKefuReallyOnline($kefu_guid)
    {
        try {
            // 这里需要根据实际的WebSocket实现来检查
            // 如果使用GatewayWorker，可以使用Gateway::isUidOnline()
            if (class_exists('\GatewayWorker\Lib\Gateway')) {
                return \GatewayWorker\Lib\Gateway::isUidOnline($kefu_guid);
            }

            // 如果使用其他WebSocket实现，需要相应的检查方法
            // 这里可以添加其他检查逻辑，比如：
            // 1. 检查Redis中的连接状态
            // 2. 检查最后活跃时间
            // 3. 发送ping消息检查响应

            // 检查最后登录时间，如果超过10分钟没有心跳则认为离线
            $user = User::where('guid', $kefu_guid)->find();
            if ($user && $user['last_login_time']) {
                $last_login = strtotime($user['last_login_time']);
                $now = time();
                // 如果超过10分钟没有心跳活动，认为已离线（心跳间隔是5分钟）
                return ($now - $last_login) < 600;
            }

            return false;
        } catch (\Exception $e) {
            debug_log('[KEFU_CLEANUP] 检查客服在线状态异常: ' . $e->getMessage());
            return false;
        }
    }
}
