<?php
declare (strict_types=1);

namespace app\command;

use Exception;
use Swoole\Process;
use Swoole\Server;
use Swoole\Timer;
use think\App;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;
use Throwable;

class QueueSwooleTask extends BasicQueue
{
    public int $way = 3;


    protected function configure()
    {
        // 指令配置
        $this->setName('queue_swoole_task')
            ->addOption('task_worker_num', null, Option::VALUE_OPTIONAL, 'task_worker_num', 50)
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'memory', 32)
            ->setDescription('the queue_swoole_task command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $this->listenForEvents();
        $server           = new Server('127.0.0.1', 9501);
        $task_worker_num  = $input->getOption('task_worker_num');
        $memory           = $input->getOption('memory');
        $queue_start_time = time();
        $server->set([
            'task_worker_num' => $task_worker_num,
            //'worker_num'        => 2,
            'max_wait_time'   => 60,
            'reload_async'    => true,
            'task_ipc_mode'   => 1,
            //'message_queue_key' => 'swoole_queue' // task_ipc_mode=3 有效
            //'admin_server'    => '0.0.0.0:9502',
        ]);
        /**
         * 添加用户进程以监控队列任务是否有新增
         */
        $process = new Process(function (Process $process) use ($server, $memory, $queue_start_time) {
            swoole_set_process_name("php queue swoole User worker");
            while (true) {
                try {
                    $app_name       = tools()::get_lan_ip() . ':' . config('app.app_name');
                    $redis          = get_redis_instance();
                    $redis_key_name = $app_name . ':connection:queue';
                    $jobs           = $redis->brPop($redis_key_name, 60);
                    if (empty($jobs)) {
                        if ($this->worker->queueShouldRestart($queue_start_time)) {
                            wr_log('queueShouldRestart 重启server');
                            return $this->shutdownServer($server);
                        }
                        if ($this->worker->memoryExceeded($memory)) {
                            wr_log('memoryExceeded 重启server');
                            return $this->shutdownServer($server);
                        }
                        continue;
                    }
                    $data_array = json_decode($jobs[1], true);
                    $type       = $data_array['type'] ?? '';
                    switch ($type) {
                        case 'queue':
                            $server->sendMessage($jobs[1], 0);
                            break;
                        case 'reload':
                            $server->reload();
                            break;
                        case 'shutdown':
                            $this->shutdownServer($server);
                            break;
                        default:
                            wr_log('收到其他type:' . $type);
                    }
                } catch (Exception|Throwable $e) {
                    wr_log('Exception Msg : MESSAGE:' . $e->getMessage() . ';FILE:' . $e->getFile() . ';LINE:' . $e->getLine());
                    sleep(10);
                }
            }
        }, false, 2, true);
        $server->addProcess($process);
        $server->on('Start', [$this, 'onStart']);
        $server->on('ManagerStart', [$this, 'onManagerStart']);
        $server->on('WorkerStart', [$this, 'onWorkerStart']);
        $server->on('WorkerStop', [$this, 'onWorkerStop']);
        $server->on('ManagerStop', [$this, 'onManagerStop']);
        $server->on('WorkerExit', [$this, 'onWorkerExit']);
        $server->on('Connect', [$this, 'onConnect']);
        $server->on('Close', [$this, 'onClose']);
        $server->on('Packet', [$this, 'onPacket']);
        $server->on('PipeMessage', [$this, 'onPipeMessage']);
        $server->on('Receive', [$this, 'onReceive']);
        $server->on('WorkerError', [$this, 'onWorkerError']);
        $server->on('BeforeReload', [$this, 'onBeforeReload']);
        $server->on('AfterReload', [$this, 'onAfterReload']);
        $server->on('BeforeShutdown', [$this, 'onBeforeShutdown']);
        $server->on('Shutdown', [$this, 'onShutdown']);
        $server->on('Task', [$this, 'onTask']);
        $server->on('Shutdown', [$this, 'onShutdown']);
        $server->on('Finish', [$this, 'onFinish']);
        $server->start();
    }

    public function onReceive(Server $server, $fd, $reactor_id, $data)
    {
        if (!tools()::is_json($data)) {
            return;
        }
        $data_array = json_decode($data, true);
        $type       = $data_array['type'];
        switch ($type) {
            case 'queue':
                $server->task(json_encode($data_array['data']), JSON_UNESCAPED_UNICODE);
                break;
            case 'reload':
                $server->reload();
                break;
            case 'shutdown':
                $server->shutdown();
                break;
            default:
                wr_log('收到其他type:' . $type);
        }
    }


    public function onManagerStart(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
        swoole_set_process_name("php queue swoole Manager worker");
        Timer::tick(1000 * 60, function () use ($server) {
            //启动后自动查询是否需要检测队列是否有剩余任务
            $queue_connections = config('queue.connections');
            foreach ($queue_connections as $connection) {
                if (isset($connection['auto']) && $connection['auto'] == true) {
                    $type  = $connection['type'];
                    $queue = $connection['queue'];
                    $data  = ['type' => 'queue', 'data' => ['connection' => $type, 'queue' => $queue, 'auto' => true]];
                    $server->sendMessage(json_encode($data, JSON_UNESCAPED_UNICODE), 0);
                }
            }
        });
    }

    public function onTask(Server $server, $task_id, $src_worker_id, $data)
    {
        // wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
        if (!tools()::is_json($data)) {
            echo '[' . $server->getWorkerPid() . '][' . microsecond() . '] -【NOT JSON Task】-[' . $server->getWorkerId() . ']-[' . $task_id . ']' . $data . PHP_EOL;
            $msg = '收到的data不是json' . PHP_EOL;
            echo $msg;
            wr_log($msg);
            return $msg;
        }
        $data_array = json_decode($data, true);
        $type       = $data_array['type'] ?? '';
        switch ($type) {
            case 'queue':
                $queue_data = $data_array['data'];
                $connection = $queue_data['connection'];
                $queue      = $queue_data['queue'];
                if (in_array($queue, ['debug'])) {
                    break;
                }
                $auto      = $queue_data['auto'] ?? false;
                $connector = $this->worker->getConnector($connection);
                do {
                    //如果有任务,则循环执行,没必要等待调度,减少开销
                    $job    = $this->worker->getNextJob($connector, $queue);
                    $job_id = 0;
                    if ($job) {
                        if ($auto) {
                            try {
                                //如果是每分钟定时执行检测出来的说明任务有堆积,需要在执行任务之前通知其他进程来取任务,这样可实现并行处理
                                $server->sendMessage($data, 0);
                            } catch (Exception|Throwable $e) {
                            }
                        }
                        $job->setAttach(['worker_id' => $src_worker_id, 'task_id' => $task_id]);
                        $this->worker->runJob($job, $connection);
                        $this->worker->updateLastQueueTime();
                        $job_id = $job->getJobId();
                    }
                } while ($job);
                $return = ['job_id' => $job_id, 'auto' => $auto];
                unset($data_array, $queue_data, $job, $data);
                $server->finish(json_encode($return, JSON_UNESCAPED_UNICODE));
                break;
            default:
                wr_log('暂时task中只支持处理type=queue类型数据,不支持' . $type . '类型,data=' . $data);
                break;
        }
    }

    public function onStart(Server $server)
    {
        swoole_set_process_name("php queue swoole Master worker");
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onWorkerStart(Server $server, $worker_id)
    {
        // wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event=WorkerStart,worker_id:' . $worker_id);
        $worker_num = $server->setting['worker_num'];
        if ($worker_id >= $worker_num) {
            swoole_set_process_name("php queue swoole Task worker # " . ($worker_id - $worker_num + 1));
        } else {
            swoole_set_process_name("php queue swoole Event worker # " . ($worker_id + 1));
        }
    }

    public function onWorkerStop(Server $server, $worker_id)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onManagerStop(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onWorkerExit(Server $server, $worker_id)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onConnect(Server $server, $fd, $reactorId)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onClose(Server $server, $fd, $reactorId)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onPacket(Server $server, string $data, array $clientInfo)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onPipeMessage(Server $server, int $src_worker_id, mixed $message)
    {
        $server->task($message);
    }

    public function onWorkerError(Server $server, int $worker_id, int $worker_pid, int $exit_code, int $signal)
    {
        //当 Worker/Task 进程发生异常后会在 Manager 进程内回调此函数。
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event=WorkerError, exit_code=' . $exit_code . ',signal=' . $signal . ',worker_id=' . $worker_id . ',worker_pid=' . $worker_pid);
    }

    public function onBeforeReload(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onAfterReload(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onBeforeShutdown(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onShutdown(Server $server)
    {
        wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    public function onFinish(Server $server, $task_id, $result)
    {
        //wr_log('pid=' . getmypid() . ',time=' . microsecond() . ',event = ' . __FUNCTION__);
    }

    /**
     * 安全重启swoole server
     * @param Server $server
     */
    public function shutdownServer(Server $server)
    {
        $i = 1;
        while (true) {
            $i++;
            sleep(1);
            $info = $server->stats();
            $msg  = '';
            foreach ($info as $key => $val) {
                $msg .= $key . ':' . $val . ';';
            }
            if ($info['tasking_num'] == 0 && $info['task_idle_worker_num'] == $info['task_worker_num'] && $info['worker_num'] == $info['idle_worker_num']) {
                $msg = '目前可能全部队列处理完毕,开始执行shutdown';
                echo $msg . PHP_EOL;
                wr_log($msg);
                $server->shutdown();
                return;
                // Process::kill(getmypid(), SIGKILL);
            } else {
                if ($i > 30) {
                    //$server->shutdown();
                    //超过30次尝试强制关闭
                    Process::kill(getmypid(), SIGKILL);
                    break;
                }
                $msg = 'i=' . $i . '; pid=' . getmypid() . ';tasking_num= ' . $info['tasking_num'] . ';task_idle_worker_num= ' . $info['task_idle_worker_num'] . ';idle_worker_num= ' . $info['idle_worker_num'];
                echo $msg . PHP_EOL;
                wr_log($msg);
                $task_worker_num = $info['task_worker_num'];
                for ($id = 0; $id < $task_worker_num; $id++) {
                    $worker_status = $server->getWorkerStatus($id);
                    $worker_pid    = $server->getWorkerPid($id);
                    $msg           = 'worker_status,id=' . $id . '; pid= ' . $worker_pid . ' worker_status=' . (int)$worker_status;
                    echo $msg . PHP_EOL;
                    wr_log($msg);
                }
                if ($info['tasking_num'] == 0) {
                    //wr_log('只有一个task进程不空闲了,第' . $i . '次');
                    wr_log('只有一个task进程不空闲了,第' . $i . '次');
                    $i++;
                    if ($i > 10) {
                        for ($id = 0; $id < $task_worker_num; $id++) {
                            $worker_status = $server->getWorkerStatus($id);
                            $worker_pid    = $server->getWorkerPid($id);
                            $msg           = 'worker_status,id=' . $id . '; pid= ' . $worker_pid . ' worker_status=' . (int)$worker_status;
                            echo $msg . PHP_EOL;
                            wr_log($msg);
                            if ($worker_status != 2) {
                                Process::kill($worker_pid);
                            }
                        }
                        $cmd = "sudo -u root -S ps -ef | grep async | grep -v grep | awk '{print $2}' | xargs kill -9";
                        exec($cmd, $out, $code);
                        $msg = 'cmd 状态码:' . $code . ';输出:' . json_encode($out, JSON_UNESCAPED_UNICODE);
                        echo $msg . PHP_EOL;
                        wr_log($msg);
                        $server->shutdown();
                    }
                }
                sleep(3);
            }
            //wr_log(json_encode($info, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }
}
