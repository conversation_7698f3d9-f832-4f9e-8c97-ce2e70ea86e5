<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use GatewayWorker\BusinessWorker;

class KefuWorker extends Command
{
    protected function configure()
    {
        $this->setName('kefu_worker')->setDescription('启动客服BusinessWorker进程');
    }

    protected function execute(Input $input, Output $output)
    {
        $config                  = config('kefu_socket');
        $worker                  = new BusinessWorker();
        $worker->eventHandler    = 'app\\service\\Events';
        $worker->name            = 'wsBusinessWorker';
        $worker->count           = $config['business_worker'];
        $worker->registerAddress = '127.0.0.1:' . $config['register_port'];
        BusinessWorker::runAll();
    }
}
