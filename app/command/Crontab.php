<?php
declare (strict_types=1);

namespace app\command;

use app\model\Crontab as CrontabModel;
use app\model\CrontabExecuteNote;
use Exception;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\exception\Handle;
use Throwable;

/**
 * Class Crontab
 * @package app\command
 */
class Crontab extends Command
{
    /** @var Handle */
    protected Handle $handle;

    /**
     * The queue worker pid.
     * @var int
     */
    protected int $pid;

    /**
     * Indicates if the worker should exit.
     *
     * @var bool
     */
    public bool $shouldQuit = false;

    /**
     * Crontab constructor.
     * @param Handle $handle
     */
    public function __construct(Handle $handle)
    {
        $this->handle = $handle;
        $this->pid    = getmypid();
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('crontab')
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 32)
            ->setDescription('Do Crontab Job');
    }

    /**
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws Exception
     */
    protected function execute(Input $input, Output $output)
    {
        $queue_start_time = time();
        cli_set_process_title('xyf crontab - ' . microsecond());
        $memory = (int)$input->getOption('memory');
        $msg    = $this->getName() . ' pid: ' . $this->pid . ' started successfully , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        //wr_log($msg);
        if ($this->supportsAsyncSignals()) {
            $this->listenForSignals();
        }
        $db                          = new CrontabModel();
        $next_execute_time_arr       = [];//所有任务中下次执行的时间差数组
        $last_crontab_time_cache_key = tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_crontab_time';
        while (true) {
            try {
                $this->stopIfNecessary($queue_start_time, $memory);
                $time = time();
                cache($last_crontab_time_cache_key, $time); //写入定时任务最后执行时间便于监控是否有异常
                $map  = [['status', '=', 1], ['next_execute_time', '<=', format_timestamp($time)]];
                $list = $db->where($map)->select();
                foreach ($list as $key => $val) {
                    $crontab_type             = $val['crontab_type'];
                    $before_next_execute_time = $val['next_execute_time'];
                    $interval_unit            = $val['interval_unit'];
                    $interval_sec             = $val['interval_sec'];//间隔周期
                    $crontab_string           = $val['crontab_string'];
                    $crontab_guid             = $val['guid'];
                    $class                    = $val['class'];
                    $bid                      = $val['bid'];
                    $connections              = $val['connections'];
                    $next_execute_time        = '';
                    switch ($crontab_type) {
                        case 'select':
                            //没有crontab表达式则使用 interval_unit 和 interval_sec 计算下次执行时间
                            $next_execute_time = date('Y-m-d H:i:s', strtotime("+ $interval_sec $interval_unit", strtotime($before_next_execute_time)));
                            break;
                        case 'input':
                            //有crontab表达式则使用 parse_crontab 计算下次执行时间
                            $next_execute_time = date('Y-m-d H:i:s', tools()::parse_crontab($crontab_string, strtotime($before_next_execute_time) + 60));
                            break;
                        default:
                            break;
                    }
                    if (empty($next_execute_time)) {
                        continue;
                    }
                    $next_execute_time_arr[] = strtotime($next_execute_time);
                    $class                   = str_replace('app\\queue\\controller\\', '', $class);
                    $output                  = 'pid:' . $this->pid . ';' . microsecond() . ';connection:' . $connections . ';job:' . $class . ';bid:' . $bid;
                    $output                  .= '; MemoryUsed:' . round(memory_get_usage() / 1024 / 1024, 4) . ' Mb';
                    $output                  .= '; MemoryRealUsed:' . round(memory_get_usage(true) / 1024 / 1024, 4) . ' Mb';
                    $result                  = $db->resolve($val);
                    if ($result) {
                        $this->output($output);
                        $last_execute_time = format_timestamp($time);
                        $update_data       = ['last_execute_time' => $last_execute_time, 'next_execute_time' => $next_execute_time];
                        $map               = [['guid', '=', $crontab_guid]];
                        $db::update($update_data, $map);
                        $insert_data = ['guid' => create_guid(), 'crontab_guid' => $crontab_guid, 'execute_time' => microsecond(),];
                        CrontabExecuteNote::create($insert_data);
                    } else {
                        $this->output('执行失败:' . $output);
                        sleep(1);
                        $this->stop();
                    }
                }
                unset($list);
                $time = time();
                if (empty($next_execute_time_arr)) {
                    $next_execute_time_arr[] = strtotime(date('Y-m-d H:i:00', $time)) + 60;
                }
                $next_execute_time_arr = array_filter(array_unique($next_execute_time_arr), function ($value) use ($time) {
                    //删除比此刻更早的数据
                    return $value > $time;
                });
                // 再取剩余数组中最小值, 算距离现在多少秒 $next_execute_time_arr 为空 min会报错
                $sleep = !empty($next_execute_time_arr) ? ((int)min($next_execute_time_arr) - $time) : 1;
                $sleep = min(60, max($sleep, 1));
                sleep($sleep);
            } catch (Exception|Throwable $e) {
                $this->analyse_exception($e);
                sleep(3);
            }
        }
    }

    /**
     * Enable async signals for the process.
     *
     * @return void
     */
    protected function listenForSignals()
    {
        pcntl_async_signals(true);
        pcntl_signal(SIGTERM, function () {
            $this->shouldQuit = true;
        });
    }

    /**
     * Determine if "async" signals are supported.
     *
     * @return bool
     */
    protected function supportsAsyncSignals()
    {
        return extension_loaded('pcntl');
    }

    /**
     * 检查内存是否超出
     * @param int $memoryLimit
     * @return bool
     */
    protected function memoryExceeded($memoryLimit = 32)
    {
        return round(memory_get_usage(true) / 1024 / 1024, 4) >= $memoryLimit;
    }

    /**
     * 检测是否存在应用更新或者已经运行超过24小时
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function queueShouldRestart(int $queueStartTime): bool
    {
        $appLastUpdateTime = min((int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time'), time());
        return (time() - $queueStartTime > 3600 * 24) || ($queueStartTime < $appLastUpdateTime);
    }

    /**
     * 检查是否要重启守护进程
     * @param int $queueStartTime
     * @return void
     * @throws Exception
     */
    protected function stopIfNecessary($queueStartTime, $memory)
    {
        if ($this->shouldQuit || $this->memoryExceeded($memory) || $this->queueShouldRestart($queueStartTime)) {
            $this->stop();
        }
    }

    /**
     * 退出进程
     *
     * @param int $status
     * @return void
     */
    public function stop($status = 0)
    {
        $msg = 'Crontab pid: ' . $this->pid . ' exited , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        exit($status);
    }

    /**
     * @param $response
     */
    protected function output($response)
    {
        $this->output->writeln($response);
    }

    /**
     * 处理异常
     * @param Throwable $e
     * @return void
     */
    protected function analyse_exception(Throwable $e)
    {
        $msg = $e->getMessage();
        $this->output($msg);
        $this->handle->report($e);
        if ($this->isMysqlOrRabbitBreak($msg)) {
            $this->stop();
        }
    }

    /**
     * 是否mysql or rabbit 断线
     * @param string $error
     * @return bool
     */
    protected function isMysqlOrRabbitBreak($error)
    {
        $break_match_str_list = config('app.break_match_str_list');
        foreach ($break_match_str_list as $msg) {
            if (false !== stripos($error, $msg)) {
                wr_log('crontab mysql or rabbit 断线退出成功');
                return true;
            }
        }
        return false;
    }
}