<?php
declare (strict_types=1);

namespace app\command;

use app\model\WechatDevice;
use Exception;
use OpenApi\SubMerchant;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

/**
 * Class Device
 * @package app\command
 */
class Device extends Command
{
    /**
     *
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('device:list')
            ->addArgument('page', Argument::REQUIRED, 'The Page Of Required Update')
            ->setDescription('update device list');
    }

    /**
     * @param Input $input
     * @param Output $output
     * @return int|void|null
     * @throws Exception
     */
    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $lock_instance = get_distributed_instance();
        $lock_key      = 'device_list';
        $lock          = $lock_instance->get_lock($lock_key);
        $sub           = new SubMerchant();
        $max_page      = (int)$this->input->getArgument('page');
        $output->writeln('device list list updated' . $max_page);
        for ($page = 1; $page <= $max_page; $page++) {
            $output->writeln('正在开始第:' . $page . '页');
            $data       = ['page_index' => $page];
            $result     = $sub->get_device_list($data);
            $total      = $result['total'];
            $insert_num = 0;
            foreach ($result['list'] as $device) {
                $device_sn = $device['device_sn'];
                $db        = new WechatDevice();
                unset($device['id']);
                $map   = [['device_sn', '=', $device_sn]];
                $count = $db->where($map)->count();
                if ($count) {
                    $db::update($device, $map);
                    $output->writeln('设备号:' . $device_sn . '_更新成功');
                    continue;
                }
                $result = $db->save($device);
                if ($result) {
                    $insert_num++;
                } else {
                    dump('插入失败');
                }
            }
            if ($insert_num) {
                $msg = '第' . $page . '页_发现有' . $insert_num . '个新设备,当前合计' . $total . '台设备';
                //wr_log($msg);
            }
            $output->writeln('已经结束:' . $page . '页');
        }
        // 指令输出
        $output->writeln('device list updated');
        $lock_instance->unlock($lock);
    }
}
