<?php
declare (strict_types=1);

namespace app\command;

use app\common\service\DoJobService;
use Redis;
use swoole_process;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

class QueuePool extends Command
{
    private $maxProcesses = 100;
    private $child;
    private $redis_task_wing = 'task:data'; //待处理队列

    public function callback_function(swoole_process $process)
    {
        // 第一个处理
        swoole_event_add($process->pipe, function ($pipe) use ($process) {
            /*var $worker \swoole_process*/
            /**
             * @var $process swoole_process
             */
            //  $result = $process->pop();
            // echo "子输出主内容: {$result}" . PHP_EOL;
            $data_pop = $process->read();
            echo PHP_EOL . $data_pop . '===' . $process->pid . '===' . $data_pop;
            $data_arr = json_decode($data_pop, true);
            $payload  = $data_arr[1];
            $job      = new DoJobService($this->app, $payload);
            $result   = $job->fire();
            swoole_event_del($process->pipe);
            $process->exit(0);
        });
        exit();
    }

    public function push_job()
    {
        for ($i = 0; $i < 1000; $i++) {
            $data = [
                'i'         => $i,
                'timestamp' => microsecond()
            ];
            $this->redis_client()->lpush($this->redis_task_wing, json_encode($data));
        }
        exit;
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('test')
            ->addOption('cmd', null, Option::VALUE_OPTIONAL, 'The cmd', null)
            ->addOption('max_num', null, Option::VALUE_OPTIONAL, 'The cmd', 100)
            ->setDescription('the test command');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $output->writeln('执行任务开始');
        $max_num            = $input->getOption('max_num');
        $this->maxProcesses = $max_num;
        $cmd                = $input->getOption('cmd');
        if ($cmd) {
            $output->writeln('开始执行action【' . $cmd . '】');
            return $this->$cmd();
        }
        $this->install_signal_handler($input, $output);
        $this->run_job();
        $output->writeln('执行任务结束');
    }

    private function install_signal_handler(Input $input, Output $output)
    {
        pcntl_async_signals(true);
        // install signal handler for dead kids
        pcntl_signal(SIGCHLD, array($this, 'sig_handler'));
        pcntl_signal(SIGINT, array($this, 'sig_handler')); //ctrl+c
        pcntl_signal(SIGTERM, array($this, 'sig_handler')); //暴力退出
        pcntl_signal(SIGINT, array($this, 'sig_handler')); //查看状态
        // 注册信号处理函数，处理子进程结束时的回收工作
    }

    private function run_job()
    {
        $rds      = $this->redis_client();
        $app_name = tools()::get_lan_ip() . ':' . config('app.app_name');
        $redis    = get_redis_instance();
        while (true) {
            $redis_key_name = $app_name . ':connection:queue';
            $jobs           = $redis->brPop($redis_key_name, 60);
            if (empty($jobs)) {
                continue;
            }
            $data_array = json_decode($jobs[1], true);
            $type       = $data_array['type'] ?? '';
            switch ($type) {
                case 'queue':
                    break;
                default:
                    wr_log('收到其他type:' . $type);
            }
            if ($this->child < $this->maxProcesses) {
                $this->child++;
                $this->output->writeln("now we de have 【" . $this->child . "】child processes");
                $data_pop = $rds->brPop($this->redis_task_wing, 3);//无任务时,阻塞等待
                if (!$data_pop) {
                    $this->output->writeln("没有任务,继续执行");
                    continue;
                }
                $this->output->writeln("\t Starting new child | now we de have $this->child child processes");
                $process = new swoole_process([$this, 'callback_function'], false);
                $json    = json_encode($data_pop);
                // 启用消息队列 int $msgkey = 0, int $mode = 2
                // $process->useQueue(0, 2);
                // $process->push($json);
                $process->write($json); // 管道写入内容
                $pid = $process->start();
                $this->output->writeln('开始执行PID【' . $pid . '】');
            }
        }
    }

    private function redis_client()
    {
        $rds = new Redis();
        $rds->connect('127.0.0.1', 6379);
        $rds->auth('XieYongFa123');
        return $rds;
    }

    private function sig_handler($signo)
    {
        echo "Receive: $signo \r";
        switch ($signo) {
            case SIGCHLD:
                while ($ret = swoole_process::wait(false)) {
                    echo "PID={$ret['pid']}";
                    $this->child--;
                }
                break;
            case SIGINT:
                echo "我被ctrl+c了\n";
                exit(0);
            case SIGTERM:
                echo "暴力退出了\n";
                exit(0);
            case SIGUSR1:
                echo "退出主进程SIGUSR1\n";
                exit(0);
            default:
                break;
        }
    }
}