<?php
declare (strict_types=1);

namespace app\command;

use app\model\Crontab as CrontabModel;
use app\model\CrontabExecuteNote;
use Exception;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\exception\Handle;
use Workerman\Crontab\Crontab;
use Workerman\Worker;

/**
 * Class Crontab
 * @package app\command
 */
class CrontabWorkerman extends Command
{
    /** @var Handle */
    protected Handle $handle;

    /**
     * The queue worker pid.
     * @var int
     */
    protected int $pid;

    /**
     * Indicates if the worker should exit.
     *
     * @var bool
     */
    public bool $shouldQuit = false;

    /**
     * Crontab constructor.
     * @param Handle $handle
     */
    public function __construct(Handle $handle)
    {
        $this->handle = $handle;
        $this->pid    = getmypid();
        parent::__construct();
    }


    protected function configure()
    {
        $this->setName('crontab_workerman')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, 'Run the workerman server in daemon mode.')
            ->setDescription('Do Crontab Job');
    }

    /**
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws Exception
     */
    protected function execute(Input $input, Output $output)
    {
        date_default_timezone_set('PRC');
        set_user_and_group();
        $queue_start_time = time();
        // 指令输出
        $output->writeln('crontab_workerman start');

        $action = $input->getArgument('action');
        $mode   = $input->getOption('mode');

        // 重新构造命令行参数,以便兼容workerman的命令
        global $argv;
        $argv = [];

        array_unshift($argv, 'think', $action);
        if ($mode == 'd') {
            $argv[] = '-d';
        } else if ($mode == 'g') {
            $argv[] = '-g';
        }

        $worker = new Worker();
        // 设置时区，避免运行结果与预期不一致
        $msg = $this->getName() . ' pid: ' . $this->pid . ' started successfully , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        wr_log($msg);
        if ($this->supportsAsyncSignals()) {
            $this->listenForSignals();
        }
        $worker->onWorkerStart = function () {
            $db           = new CrontabModel();
            $map          = [['status', '=', 1], ['crontab_string', '<>', '']];
            $list         = $db->where($map)->select();
            $crontab_list = [];
            foreach ($list as $key => $val) {
                $val['crontab_string'] = str_replace('?', '*', $val['crontab_string']);
                $crontab_list[]        = new Crontab($val['crontab_string'], function () use ($val) {
                    $before_next_execute_time = $val['next_execute_time'];
                    $interval_unit            = $val['interval_unit'];
                    $interval_sec             = $val['interval_sec'];
                    $crontab_string           = $val['crontab_string'];
                    $crontab_guid             = $val['guid'];
                    $class                    = $val['class'];
                    $bid                      = $val['bid'];
                    $connections              = $val['connections'];
                    $payload                  = $val['payload'];
                    $queue_name               = $val['queue_name'];
                    $this->output('正在执行:' . $class . format_timestamp());
                    job()->set_connections($connections)->set_job_name($class)->set_queue_name($queue_name)->push_job($payload);
                    //更新执行记录
                    $last_execute_time = format_timestamp();
                    $update_data       = ['last_execute_time' => $last_execute_time, 'next_execute_time' => $next_execute_time];
                    $map               = [['guid', '=', $crontab_guid]];
                    CrontabModel::update($update_data, $map);
                    $insert_data = ['guid' => create_guid(), 'crontab_guid' => $crontab_guid, 'execute_time' => microsecond(),];
                    CrontabExecuteNote::create($insert_data);

                });
                $this->output($val['class'] . '定时任务启动成功');
            }
        };
        Worker::runAll();
    }

    /**
     * Enable async signals for the process.
     *
     * @return void
     */
    protected function listenForSignals()
    {
        pcntl_async_signals(true);
        pcntl_signal(SIGTERM, function () {
            $this->shouldQuit = true;
        });
    }

    /**
     * Determine if "async" signals are supported.
     *
     * @return bool
     */
    protected function supportsAsyncSignals()
    {
        return extension_loaded('pcntl');
    }

    /**
     * 检查内存是否超出
     * @param int $memoryLimit
     * @return bool
     */
    protected function memoryExceeded($memoryLimit = 32)
    {
        return round(memory_get_usage(true) / 1024 / 1024, 4) >= $memoryLimit;
    }

    /**
     * 检测是否存在应用更新或者已经运行超过24小时
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function queueShouldRestart(int $queueStartTime): bool
    {
        $appLastUpdateTime = min((int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time'), time());
        return (time() - $queueStartTime > 3600 * 24) || ($queueStartTime < $appLastUpdateTime);
    }

    /**
     * 检查是否要重启守护进程
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function stopIfNecessary($queueStartTime, $memory)
    {
        if ($this->shouldQuit || $this->memoryExceeded($memory) || $this->queueShouldRestart($queueStartTime)) {
            $this->stop();
        }
    }

    /**
     * 退出进程
     *
     * @param int $status
     * @return void
     */
    public function stop($status = 0)
    {
        $msg = 'Crontab pid: ' . $this->pid . ' exited , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        wr_log($msg);
        exit($status);
    }

    /**
     * @param $response
     */
    protected function output($response)
    {
        $this->output->writeln($response);
    }

    /**
     * 处理异常
     * @param Exception $e
     * @return void
     */
    protected function analyse_exception(Exception $e)
    {
        $msg = $e->getMessage();
        $this->output($msg);
        $this->handle->report($e);
        if ($this->isMysqlOrRabbitBreak($msg)) {
            $this->stop();
        }
    }

    /**
     * 是否mysql or rabbit 断线
     * @param string $error
     * @return bool
     */
    protected function isMysqlOrRabbitBreak($error)
    {
        $break_match_str_list = config('app.break_match_str_list');
        foreach ($break_match_str_list as $msg) {
            if (false !== stripos($error, $msg)) {
                wr_log('crontab mysql or rabbit 断线退出成功');
                return true;
            }
        }
        return false;
    }
}