<?php
declare (strict_types=1);

namespace app\command;

use <PERSON>\MultiProcesses\Pool as MultiProcessesPool;
use Bobby\MultiProcesses\Worker as MultiProcessesWorker;
use think\App;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;

class QueueMultiProcesses extends BasicQueue
{
    public int $way = 4;

    protected function configure()
    {
        // 指令配置
        $this->setName('pools')
            ->addOption('connection', null, Option::VALUE_OPTIONAL, 'The connection', 'database')
            ->addOption('queue', null, Option::VALUE_OPTIONAL, 'The queue', 'default')
            ->setDescription('the test command');
    }

    public function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $this->listenForEvents();
        $times             = 1000;
        $connection        = $input->getOption('connection');
        $queue             = $input->getOption('queue');
        $minIdleWorkersNum = 2;
        $maxWorkerNum      = 20;
        $worker            = new MultiProcessesWorker(function (MultiProcessesWorker $worker) use ($times, $minIdleWorkersNum, $connection, $queue) {
            $workerId  = $worker->getWorkerId();
            $workTimes = 0;

            $queue_worker = $this->worker;
            cli_set_process_title('xyf Worker#' . $workerId);
            $connector = $queue_worker->getConnector($connection);
            do {
                $job = $queue_worker->getNextJob($connector, $queue);
                $queue_worker->updateLastQueueTime();
                if ($job) {
                    $worker->lock();
                    $result = $queue_worker->runJob($job, $connection);
                    var_dump($result);
                } else {
                    $worker->free();
                    if ($workerId > $minIdleWorkersNum) {
                        var_dump('我是临时进程#' . $workerId . '-没有任务我要退出了');
                    } else {
                        var_dump('我是主进程不能退出,休眠三秒继续吧');
                        sleep(3);
                    }
                }
            } while ($job);

            //          while ($masterData = $worker->read()) {
            //              // 将当前进程设置为任务进行中状态
            //              $worker->lock();
            //
            //              $workTimes++;
            //
            //              echo "I am worker:$workerId,My master send data:$masterData to me." . PHP_EOL;
            //
            //              sleep(1);
            //
            //              if ($workerId < $minIdleWorkersNum && $workTimes >= (2 + $times)) break;
            //              if ($workerId >= $minIdleWorkersNum && $workTimes >= $times) break;
            //
            //              // 将当前进程设置为闲置可用状态
            //              $worker->free();
            //          }

            //$worker->write("Worker:$workerId exited, finish work times: $workTimes." . PHP_EOL);
        }, false);

        $worker->setName('xyf Pool worker');

        $pool = new MultiProcessesPool($maxWorkerNum, $worker);

        // 设置启动时最少可用worker进程数量。不设置的话则默认和进程池最大数量相同
        $pool->setMinIdleWorkersNum($minIdleWorkersNum);

        // PHP7意思可以异步监听子进程信号不需要声明TICKS
        $pool->onCollect();

        $pool->run();

        $workersNum = $pool->getWorkersNum();
        for ($i = 0; $i < $workersNum; $i++) {
            $worker = $pool->getWorker();
            //$worker->write("Master sending to worker:" . $worker->getWorkerId());
        }

        //$pool->broadcast("broadcasting.");

        // sleep函数会被进程信号中断
        // 此函数使调用进程被挂起，直到满足以下条件之一：
        // 1)已经过了seconds所指定的墙上时钟时间
        // 2)调用进程捕捉到一个信号并从信号处理程序返回
        //var_dump(sleep(10));

        $n = 0;
        // 当发现进程池中没有可用闲置进程时 将动态fork出新的子进程知道到达进程池最大进程数量为止
        while (true) {
            if (!$worker = $pool->getIdleWorker()) {
                var_dump('没有可用进程啦');
                continue;
            }

            echo "Using worker:" . $worker->getWorkerId() . PHP_EOL;

            $worker->write("\ ^ . ^ /");

            usleep(50000);

            $n++;
            //          $runningWorkersNum = $pool->getWorkersNum();
            //          if ($n >= $times * $runningWorkersNum) {
            //              echo "Master send total messages:$n.\n";
            //              break;
            //          }
        }

        while ($maxWorkerNum--) {
            echo ($worker = $pool->getWorker())->read();
            $worker->clearIpc();
        }
        var_dump('结束啦');
    }
}
