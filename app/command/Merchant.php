<?php
declare (strict_types=1);

namespace app\command;

use app\model\SubMerchants;
use OpenApi\SubMerchant;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class Merchant extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('merchant:update')
            ->addArgument('page', Argument::REQUIRED, 'The Page Of Required Update')
            ->setDescription('update merchant list');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $lock_instance               = get_distributed_instance();
        $lock_key                    = 'merchant_update';
        $lock                        = $lock_instance->get_lock($lock_key);
        $sub                         = new SubMerchant();
        $max_page                    = (int)$this->input->getArgument('page');
        $platform_sub_merchant_appid = get_system_config('platform_sub_merchant_appid');
        $output->writeln('merchant list updated' . $max_page);
        for ($page = 1; $page <= $max_page; $page++) {
            $output->writeln('正在开始第:' . $page . '页');
            $data       = ['page' => $page];
            $result     = $sub->merchant_list($data);
            $total      = $result['total'];
            $insert_num = 0;
            foreach ($result['items'] as $merchant) {
                $id                    = $merchant['merchantId'];
                $merchant_id           = $merchant['tenpayMchId'];
                $company_name          = $merchant['companyName'];
                $merchant_name         = $merchant['merchantName'];
                $applyment_modify_time = $merchant['applymentModifyTime'];
                $db                    = new SubMerchants();
                $map                   = [['id', '=', $id]];
                $count                 = $db->where($map)->count();
                if ($count > 0) {
                    $output->writeln('商户号:' . $merchant_id . '_已经存在');
                    //发现有重复,说明后续都是重复
                    continue;
                }
                $result      = $sub->get_merchant_info_api($merchant_id);
                $insert_data = [
                    'id'                    => $id,
                    'category'              => $result['category'],
                    'merchant_id'           => $merchant_id,
                    'company_name'          => $company_name,
                    'merchant_name'         => $merchant_name,
                    'applyment_modify_time' => $applyment_modify_time,
                ];
                $db->save($insert_data);
                $insert_num++;
                $job_data = [
                    'appid'    => $platform_sub_merchant_appid,
                    'apply_id' => $id
                ];
                job()->set_job_name('Merchant@add_sub_appid')->push_job($job_data); //添加支付APPID
            }
            if ($insert_num) {
                $msg = '第' . $page . '页_发现有' . $insert_num . '个新商户提交,当前合计入驻' . $total . '个商户';
                wr_log($msg);
            }
            $output->writeln('已经结束:' . $page . '页');
        }
        // 指令输出
        $output->writeln('merchant list updated');
        $lock_instance->unlock($lock);
    }
}
