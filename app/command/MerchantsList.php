<?php
declare (strict_types=1);

namespace app\command;

use app\model\SubMerchantsList;
use Exception;
use OpenApi\SubMerchant;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class MerchantsList extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('merchants_list:update')
            ->setDescription('update merchants list');
    }

    /**
     * @throws Exception
     */
    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $lock_instance  = get_distributed_instance();
        $lock_key       = 'merchants_update';
        $lock           = $lock_instance->get_lock($lock_key);
        $sub            = new SubMerchant();
        $cookies        = $sub->get_cookies();
        $result         = $sub->get_apply_micro_list();
        $total          = $result['total'];
        $request_num    = 50;
        $page_size      = 20;
        $total_max_page = ceil($total / $page_size);
        $max_index      = ceil($total / $request_num / $page_size);
        $start_time     = time();
        $output->writeln('merchant list merchants_list at: ' . format_timestamp());
        for ($index = 1; $index <= $max_index; $index++) {
            $array    = [];
            $min_page = (($index - 1) * $request_num) + 1;
            $max_page = $index * $request_num;
            $output->writeln('正在获取第:' . $min_page . '--' . $max_page . '页数据');
            for ($page = $min_page; $page <= $max_page; $page++) {
                if ($page <= $total_max_page) {
                    $data    = ['pageNum' => $page];
                    $array[] = $sub->debug()->get_apply_micro_list($data);
                }
            }
            $request_start_time = time();
            $result_array       = curl()->form_params()->ignore_log(true)->set_cookies($cookies)->multi_post($array);
            $output->writeln('数据请求结束,耗时 ' . (time() - $request_start_time) . ' s,当前时间:' . format_timestamp());
            $insert_data_array = [];
            foreach ($result_array as $result) {
                if ($result['success'] !== true) {
                    throw new Exception('请求异常' . $result['response']);
                }
                $result         = $result['response'];
                $merchant_array = $result['applyMicroList'] ?? [];
                if (!empty($merchant_array)) {
                    foreach ($merchant_array as $merchant) {
                        $id                  = $merchant['secondaryMerchantId'];
                        $merchant_id         = $merchant['merchantCode'];
                        $company_name        = '';
                        $merchant_name       = $merchant['merchantName'];
                        $insert_data_array[] = [
                            'id'            => $id,
                            'category'      => '',
                            'merchant_id'   => $merchant_id,
                            'company_name'  => $company_name,
                            'merchant_name' => $merchant_name,
                        ];
                    }
                }
            }
            $db = new SubMerchantsList();
            $output->writeln('正在往数据库写入第:' . $min_page . '--' . $max_page . '页数据');
            $insert_start_time = time();
            $result            = Db::name('sub_merchants_list')->data($insert_data_array)->limit(200)->insertAll();
            $output->writeln('数据插入数据库结束,耗时 ' . (time() - $insert_start_time) . ' s,当前时间:' . format_timestamp());
            //$db->insertAll($insert_data_array);
            //$db->saveAll($insert_data_array, false);
            //          $chunk_data = array_chunk($insert_data_array, 100); //30秒即可
            //          foreach ($chunk_data as $insert_data) {
            //              $db = new SubMerchantsList();
            //              $db->saveAll($insert_data, false);
            //          }
        }
        $output->writeln('商户列表获取结束,共计消耗: ' . (time() - $start_time) . ' s,当前时间:' . format_timestamp());
        $lock_instance->unlock($lock);
    }
}
