<?php
declare (strict_types=1);

namespace app\command;

use app\model\ExportTask as ExportTaskModel;
use app\model\GoodsOrder;
use app\common\service\UrlService;
use Exception;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\exception\Handle;
use think\Model;

class ExportTask extends Command
{
    /** @var Handle */
    protected Handle $handle;

    /**
     * The queue worker pid.
     * @var int
     */
    protected int $pid;

    /**
     * Indicates if the worker should exit.
     *
     * @var bool
     */
    public bool $shouldQuit = false;

    /**
     * Crontab constructor.
     * @param Handle $handle
     */
    public function __construct(Handle $handle)
    {
        $this->handle = $handle;
        $this->pid    = getmypid();
        parent::__construct();
    }


    protected function configure()
    {
        // 指令示例 php think export_goods_order  --account=tihuo
        $this->setName('export_goods_order')
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 32)
            ->setDescription('the ExportGoodsOrder command');
    }

    /**
     *查询数据
     * @access protected
     * @param Model|\think\Db $model 模型
     * @return array
     * @throws Exception
     */
    protected function _chunk($model, $limit = 10000)
    {
        if (empty($model)) {
            error('模型未定义');
        }
        $order    = $model->getOption('order') ?: ['create_time' => 'DESC'];
        $all_data = [];
        $model->order($order)->chunk($limit, function ($data_array) use (&$all_data) {
            foreach ($data_array as $data) {
                // 处理模型对象
                $all_data[] = $data->toArray();
                unset($data);
            }
            unset($data_array);
        }, $order, 'DESC');
        return $all_data;
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        ini_set('memory_limit', '1024M'); // 设置为1024MB
        $start_time = time();
        $msg        = $this->getName() . ' pid: ' . $this->pid . ' started successfully , now time is ' . format_timestamp() . '!';
        $memory     = (int)$input->getOption('memory');
        $this->output($msg);
        $redis_key_name = 'export_task';
        $redis          = get_redis_instance();
        while (true) {
            $this->stopIfNecessary($start_time, $memory);
//            \think\facade\Db::startTrans();
            $db_export_task = new ExportTaskModel();
            $map            = [['status', '=', 0]];
            $task_info      = $db_export_task->where($map)->order(['create_time' => 'ASC'])->findOrEmpty();
            if ($task_info->isEmpty()) {
                $this->output('数据库没数据,继续监听redis' . microsecond());
                $jobs = $redis->brPop($redis_key_name, 60);
                if (empty($jobs)) {
                    $this->output('没有数据,重新循环监听' . microsecond());
                } else {
                    $this->output('发现有数据,重新循环监听,下次监听可以查询到数据库' . microsecond());
                }
                continue;
            }

            //查询数据库,以便于每次重启任务都能重新执行
            //$data_array     = json_decode($jobs[1], true);
            //$task_guid      = $data_array['guid'];
            //$type           = $data_array['type'] ?? ''; //1 导出商品订单

            $task_guid   = $task_info['guid'];
            $type        = $task_info['type'];
            $bid         = $task_info['bid'];
            $user_guid   = $task_info['user_guid'];
            $map         = [['guid', '=', $task_guid]];
            $update_data = ['status' => 1, 'begin_time' => format_timestamp()];
            $db_export_task::update($update_data, $map); //标记为正在处理
            switch ($type) {
                case 1:
                    $db_goods_order = new GoodsOrder();
                    $info           = $db_goods_order->build_query($bid, $user_guid);
                    $model          = $info['model'];
                    $data           = $this->_chunk($model);
                    $this->output('当前行数' . __LINE__ . ';当前内存:' . (int)tools()::get_memory_get_usage());
                    $this->output('查询数据结束' . microsecond());
                    if (empty($data)) {
                        $msg = '没有要导出的数据!';
                        $this->output($msg);
                        $update_data = ['status' => -1, 'finish_time' => format_timestamp()];
                        $db_export_task::update($update_data, $map); //标记为导出失败
                        break;
                    }
                    $file_name = $db_goods_order->export_data($data, $bid, $user_guid);
                    $file_name = str_replace('localhost', config('app.app_host_domain'), $file_name);
                    $output->writeln('当前行数' . __LINE__ . ';当前内存:' . (int)tools()::get_memory_get_usage());
                    $short_url_code = UrlService::long_to_short($file_name);
                    $file_name      = UrlService::get_full_url($short_url_code);
                    $update_data    = ['status' => 2, 'file_url' => $file_name, 'finish_time' => format_timestamp()];
                    $db_export_task::update($update_data, $map); //标记为导出成功
                    $msg = $bid . '--所有订单导出成功,下载链接:' . $file_name;
                    wr_log($msg, 1);
                    $this->output($msg);
                    break;
                default:
                    wr_log('收到其他type:' . $type);
            }
        }
    }

    public function stop($status = 0)
    {
        $msg = 'ExportTask pid: ' . $this->pid . ' exited , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        exit($status);
    }

    /**
     * 检查是否要重启守护进程
     * @param int $start_time
     * @param int $memory
     * @return void
     * @throws Exception
     */
    protected function stopIfNecessary($start_time, $memory)
    {
        if ($this->shouldQuit || $this->memoryExceeded($memory) || $this->queueShouldRestart($start_time)) {
            $this->stop();
        }
    }

    /**
     * 检测是否存在应用更新或者已经运行超过24小时
     * @param int $queueStartTime
     * @return bool
     * @throws Exception
     */
    protected function queueShouldRestart(int $queueStartTime): bool
    {
        $appLastUpdateTime = min((int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time'), time());
        return (time() - $queueStartTime > 3600 * 24) || ($queueStartTime < $appLastUpdateTime);
    }

    /**
     * 检查内存是否超出
     * @param int $memoryLimit
     * @return bool
     */
    protected function memoryExceeded($memoryLimit = 32)
    {
        return round(memory_get_usage(true) / 1024 / 1024, 4) >= $memoryLimit;
    }

    /**
     * @param $response
     */
    protected function output($response)
    {
        $this->output->writeln($response);
    }
}
