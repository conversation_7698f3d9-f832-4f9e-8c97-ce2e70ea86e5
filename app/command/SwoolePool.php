<?php
declare (strict_types=1);

namespace app\command;

use Swoole\Process;
use swoole_process;
use think\App;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;

class SwoolePool extends BasicQueue
{
    public int $way = 7;

    /**
     * @var Process 记录所有worker的process对象
     */

    private Process $pool;

    /**
     * @var Process[] 记录所有worker的process对象
     */
    private array $workers = [];

    /**
     * @var array 记录worker工作状态
     */
    private array $used_workers = [];

    /**
     * @var int 最小进程数
     */
    private int $min_worker_num = 5;

    /**
     * @var int 初始进程数
     */
    private int $start_worker_num = 10;

    /**
     * @var int 最大进程数
     */
    private int $max_worker_num = 20;

    /**
     * 进程闲置销毁秒数
     * @var int
     */
    private int $idle_seconds = 10;

    /**
     * @var int 当前进程数
     */
    private int $curr_num;

    /**
     * 闲置进程时间戳
     * @var array
     */
    private array $active_time = [];


    protected function configure()
    {
        // 指令配置
        $this->setName('swoole_pool')
            ->addArgument('connection', Argument::OPTIONAL, 'The name of the queue connection to work')
            ->addOption('queue', null, Option::VALUE_OPTIONAL, 'The queue to listen on', 'debug')
            ->addOption('delay', null, Option::VALUE_OPTIONAL, 'Amount of time to delay failed jobs', 3)
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 24)
            ->addOption('tries', null, Option::VALUE_OPTIONAL, 'Number of times to attempt a job before logging it failed', 2)
            ->addOption('sleep', null, Option::VALUE_OPTIONAL, 'Number of seconds to sleep when no job is available', 0)
            ->addOption('timeout', null, Option::VALUE_OPTIONAL, 'Number of timeout', 60)
            ->addOption('worker_num', null, Option::VALUE_OPTIONAL, 'Number of worker_num', 10)
            ->setDescription('swoole_pool');
    }

    public function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $this->listenForEvents();
        $queue_start_time = time();
        $connection       = $input->getArgument('connection') ?: $this->app->config->get('queue.default');
        $queue            = $input->getOption('queue') ?: $this->app->config->get("queue.connections.{$connection}.queue", 'default');
        $delay            = $input->getOption('delay');
        $sleep            = (int)$input->getOption('sleep');
        $tries            = (int)$input->getOption('tries');
        $max_memory       = (int)$input->getOption('memory');
        $timeout          = (int)$input->getOption('timeout');
        $worker_num       = (int)$input->getOption('worker_num');
        $this->pool       = new swoole_process(function () {
            // 循环建立worker进程
            for ($i = 0; $i < $this->start_worker_num; $i++) {
                $this->createWorker();
            }
            echo '初始化进程数：' . $this->curr_num . PHP_EOL;
            echo '开始监听redis 是否有新的队列任务' . PHP_EOL;
            while (true) {
                $app_name       = tools()::get_lan_ip() . ':' . config('app.app_name');
                $redis          = get_redis_instance();
                $redis_key_name = $app_name . ':connection:queue';
                $jobs           = $redis->brPop($redis_key_name, 10);
                if (empty($jobs)) {
                    var_dump('没有任务,10秒后继续尝试');
                    continue;
                }
                //有任务则获取一个可用进程,投递消息过去,如果一直没有可用进程 此处会一直阻塞
                $idle_worker_pid = $this->get_idle_worker_pid();
                //标记获取到的进程为繁忙状态
                $this->used_workers[$idle_worker_pid] = 1;
                $this->active_time[$idle_worker_pid]  = time();
                $this->workers[$idle_worker_pid]->write('dojob');
                var_dump('给空闲进程:' . $idle_worker_pid . ' 任务投递成功,继续监听');
            }
        });
        $master_pid       = $this->pool->start();
        echo "Master $master_pid start" . PHP_EOL;
        cli_set_process_title('xyf master- ' . microsecond());
        while ($ret = swoole_process::wait()) {
            $pid = $ret ['pid'];
            echo "process {$pid} existed" . PHP_EOL;
        }
    }

    /**
     * 创建一个新进程
     * @return int  新进程PID
     */
    public function createWorker()
    {
        var_dump('正在创建createWorker');
        $worker_process = new swoole_process(function (swoole_process $worker) {
            // 给子进程管道绑定事件
            swoole_event_add($worker->pipe, function ($pipe) use ($worker) {
                $data = trim($worker->read());
                if ($data == 'exit') {
                    $worker->exit(0);
                    exit();
                }
                //开始执行任务
                echo "{$worker->pid} 正在处理 {$data}" . PHP_EOL;
                $queue_worker = $this->worker;
                cli_set_process_title('xyf Worker 3 # ' . $this->curr_num . '#' . $worker->pid);
                $connection = 'database';
                $queue      = 'async';
                $connector  = $queue_worker->getConnector($connection);
                $job        = $queue_worker->getNextJob($connector, $queue);
                $queue_worker->updateLastQueueTime();
                if ($job) {
                    var_dump('发现有任务,执行job开始-' . microsecond());
                    $result = $queue_worker->runJob($job, $connection);
                    var_dump('执行job结束-' . microsecond());
                } else {
                    var_dump('我是子进程, 没有取到任务哦');
                }
                echo "{$worker->pid} 处理完啦 {$data}" . PHP_EOL;
                // 返回结果，表示空闲
                $worker->write("complete");
            });
        });

        $worker_pid = $worker_process->start();
        cli_set_process_title('xyf worker2 #' . $worker_pid);
        var_dump('worker_pid: ' . $worker_pid);
        // 给父进程管道绑定事件
        swoole_event_add($worker_process->pipe, function ($pipe) use ($worker_process) {
            $data = trim($worker_process->read());
            if ($data == 'complete') {
                // 标记为空闲
                echo "{$worker_process->pid} 空闲了" . PHP_EOL;
                $this->used_workers[$worker_process->pid] = 0;
            }
        });
        // 保存process对象
        $this->workers[$worker_pid] = $worker_process;
        // 标记为空闲
        $this->used_workers[$worker_pid] = 0;
        $this->active_time[$worker_pid]  = time();
        $this->curr_num                  = count($this->workers);
        return $worker_pid;
    }

    /**
     * 获取一个可用进程的PID
     * @return int  新进程的pid
     */
    public function get_idle_worker_pid()
    {
        foreach ($this->used_workers as $pid => $used) {
            if ($used == 0) {
                // 标记使用中
                // $this->used_workers[$pid] = 1;
                // $this->active_time[$pid]  = time();
                return $pid;
            }
        }
        //执行到这里说明当前可用的进程全是繁忙状态,需要重建一个worker

        //先判断当前进程数是否小于最大进程 如果已经超过了,则持续等待
        while (true) {
            if ($this->curr_num < $this->max_worker_num) {
                return $this->createWorker();
            }
            var_dump('当前进程数量:' . $this->curr_num . ',已经大于或者等于最大进程数:' . $this->max_worker_num . ',休眠1秒后继续尝试创建进程');
            sleep(1);
        }
    }

    // 每秒检查空闲的进程,0
    public function add_swoole_timer_tick_for_destroy_idle_worker()
    {
        swoole_timer_tick(1000, function ($timer_id) {
            // 闲置超过一段时间则销毁进程
            foreach ($this->active_time as $pid => $timestamp) {
                if ((time() - $timestamp) > $this->idle_seconds && $this->curr_num > $this->min_worker_num) {
                    // 如果已经超过空闲时间 且当前的进程数已经超过最小进程数 则销毁该进程
                    if (isset($this->workers[$pid]) && $this->workers[$pid] instanceof swoole_process) {
                        $this->workers[$pid]->write('exit');
                        unset($this->workers[$pid]);
                        $this->curr_num = count($this->workers);
                        unset($this->used_workers[$pid]);
                        unset($this->active_time[$pid]);
                        echo "{$pid} 空闲很久了 而且现在有足够多的进程 销毁吧 destroyed" . PHP_EOL;
                        break;
                    }
                }
            }
            //          echo "任务{$count}/{$this->curr_num}" . PHP_EOL;
            //
            //          if ($count == 20) {
            //              foreach ($this->workers as $pid => $worker) {
            //                  $worker->write('exit');
            //              }
            //              // 关闭定时器
            //              swoole_timer_clear($timer_id);
            //              // 退出进程池
            //              $this->pool->exit(0);
            //              exit();
            //          }
        });
    }
}