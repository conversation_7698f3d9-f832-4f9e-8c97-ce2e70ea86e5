<?php
declare (strict_types=1);

namespace app\command;

use app\model\XiaoQuBuildingAll;
use app\model\XiaoQuHouseAll;
use app\model\XiaoQuAll;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use Throwable;

class ZhuJuanJu extends Command
{
    protected int $worker_num = 1;
    protected int $mid = 0;
    protected string $order_by = 'ASC';


    protected function configure()
    {
        // 指令配置
        $this->setName('zhu_jian_ju')
            ->addArgument('action', Argument::OPTIONAL, 'The action') //调用方法
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 24) //限制内存
            ->addOption('mid', null, Option::VALUE_OPTIONAL, 'mid', 0) //是否从中间开始排序
            ->addOption('worker_num', null, Option::VALUE_OPTIONAL, 'Number of worker_num', 1) //并发请求数
            ->addOption('order_by', null, Option::VALUE_OPTIONAL, 'order_by', 'ASC') //排序规则
            ->setDescription('ZhuJuanJu');
    }

    public function log($msg)
    {
        $this->output->writeln(microsecond() . '--' . $msg);
    }

    public function execute(Input $input, Output $output)
    {
        $queue_start_time = time();
        set_user_and_group();
        $action     = $input->getArgument('action');
        $worker_num = (int)$input->getOption('worker_num');
        $mid        = (int)$input->getOption('mid');
        $order_by   = $input->getOption('order_by');
        $order_by   = strtoupper($order_by);
        if (!in_array($order_by, ['ASC', 'DESC'])) {
            $output->writeln('order_by参数错误,当前时间:' . format_timestamp());
            return;
        }
        $this->worker_num = $worker_num;
        $this->order_by   = $order_by;
        $this->mid        = $mid;
        $output->writeln($action . '开始,当前时间:' . format_timestamp());
        if (method_exists($this, $action)) {
            call_user_func_array([$this, $action], []);
        } else {
            $output->writeln($action . '方法不存在,当前时间:' . format_timestamp());
        }
        $output->writeln($action . '结束,当前时间:' . format_timestamp());
    }

    public function save_xiao_qu_house_all()
    {
        while (true) {
            $order_by       = $this->order_by;
            $db_xiao_qu_all = new XiaoQuAll();
            $map            = [
                ['building_update_status', '=', 0],
            ];
            $list           = $db_xiao_qu_all->where($map)->order(['id' => $order_by])->limit(10)->select()->toArray();
            if (count($list) == 0) {
                $this->log(__FUNCTION__ . '执行完毕');
                break;
            }
            $url = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingNO';
            foreach ($list as $xiao_qu) {
                $xiao_qu_code = $xiao_qu['code'];
                $xiao_qu_id   = $xiao_qu['id'];
                try {
                    $data   = [
                        "openId"     => '',
                        //  "openId"     => "oLtYzuO3pf061iTnf1Z8nxRHXdxY",
                        "eaAreaCode" => $xiao_qu_code,
                    ];
                    $result = curl()->set_timeout(45)->setMaxRetries(1)->ignore_log()->form_params()->post($url, $data)->get_body();
                    if (count($result) > 0) {
                        $this->log('xiao_qu_code=' . $xiao_qu_code . ';合计有' . count($result) . '栋');
                        $insert_data = [];
                        $time        = microsecond();
                        foreach ($result as $item) {
                            $insert_data[] = [
                                'xiao_qu_code'                     => $xiao_qu_code,
                                'ea_area_house_total'              => $item['EaAreaHouseTotal'],
                                'bind_ea_area_house_success_total' => $item['BindEaAreaHouseSuccessTotal'],
                                'building_name'                    => $item['BuildingName'],
                                'building_no'                      => $item['BuildingNo'],
                                'create_time'                      => $time,
                                'update_time'                      => $time,
                            ];
                        }
                        Db::name('xiao_qu_building_all')->insertAll($insert_data, 100);
                    }
                    $update_data = [
                        'building_update_status' => 1,
                        'building_num'           => count($result),
                    ];
                } catch (\Exception $e) {
                    $update_data = [
                        'building_update_status'  => -1,
                        'building_update_message' => $e->getMessage(),
                    ];
                }
                $db_xiao_qu_all::update($update_data, [['id', '=', $xiao_qu_id]]);
            }
        }
    }

    public function update_xiao_qu_all()
    {
        while (true) {
            $db_xiao_qu_all = new XiaoQuAll();
            $map            = [
                ['bind_info_update_status', '=', 0]
            ];
            $list           = $db_xiao_qu_all->where($map)->order(['bind_info_last_update_time' => 'ASC'])->limit(10)->select()->toArray();
            if (count($list) == 0) {
                $msg = '全部' . __FUNCTION__ . '更新完毕,继续等待 60 S';
                $this->log($msg);
                sleep(60);
            }
            $url = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBind';
            foreach ($list as $xiao_qu) {
                //遍历小区 xiao_qu_all
                $code                                    = $xiao_qu['code'];
                $id                                      = $xiao_qu['id'];
                $before_bind_ea_area_owner_success_total = $xiao_qu['bind_ea_area_owner_success_total'];
                $map_xiao_qu_all                         = [['id', '=', $id]];
                $data                                    = [
                    "openId"     => '',
                    "eaAreaCode" => $code,
                ];
                $result                                  = curl()->ignore_log()->form_params()->post($url, $data)->get_body();
                if ($result == 'null') {
                    $update_data_xiao_qu_all = [
                        'bind_info_update_message'   => '返回结果是NULL字符串',
                        'bind_info_update_status'    => -1,
                        'bind_info_last_update_time' => format_timestamp(),
                    ];
                } elseif (is_array($result) && isset($result['EaAreaHouseTotal'])) {
                    $update_data_xiao_qu_all = [
                        'bind_info_update_status'          => 1,
                        'ea_area_house_total'              => $result['EaAreaHouseTotal'],
                        'bind_ea_area_house_success_total' => $result['BindEaAreaHouseSuccessTotal'],
                        'bind_ea_area_owner_success_total' => $result['BindEaAreaOwnerSuccessTotal'],
                        'ea_area_house_total_rate'         => $result['EaAreaHouseTotalRate'],
                        'bind_info_last_update_time'       => format_timestamp(),
                    ];
                } else {
                    $update_data_xiao_qu_all = [
                        'bind_info_update_message'   => '返回结果是不是数组或者不合法:' . json_encode($result, JSON_UNESCAPED_UNICODE),
                        'bind_info_update_status'    => -1,
                        'bind_info_last_update_time' => format_timestamp(),
                    ];
                }
                $after_bind_ea_area_owner_success_total = $result['BindEaAreaOwnerSuccessTotal'] ?? 0;

                if ($before_bind_ea_area_owner_success_total < $after_bind_ea_area_owner_success_total) {
                    //如果有新数据, 则需要更新每一栋的绑定信息
                    $this->log($code . '--发现绑定数据有更新');
                    $db_xiao_qu_building_all = new XiaoQuBuildingAll();
                    $map                     = [
                        ['xiao_qu_code', '=', $code]
                    ];
                    $list                    = $db_xiao_qu_building_all->where($map)->select();
                    $url                     = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingNO';
//                    $data                    = [
//                        "openId"     => '',
//                        "eaAreaCode" => $code,
//                    ];
                    $result = curl()->ignore_log()->form_params()->post($url, $data)->get_body();
                    if (empty($result)) {
                        $this->log($code . '--$result为空');
                        continue;
                    }

                    foreach ($list as $building) {
                        //更新每一栋绑定数据
                        $building_id                             = $building['id'];
                        $building_name                           = $building['building_name'];
                        $before_bind_ea_area_house_success_total = $building['bind_ea_area_house_success_total'];
                        $building_result                         = [];
                        foreach ($result as $item) {
                            if (($item['BuildingName'] ?? '') === $building_name) {
                                $building_result = $item;
                                break;
                            }
                        }
                        if (empty($building_result)) {
                            $this->log($code . '--未找到对应栋:' . $building_name . 'result=' . json_encode($result, JSON_UNESCAPED_UNICODE));
                            continue;
                        }
                        if (!is_array($building_result)) {
                            $this->log($code . '--结果不是array');
                            continue;
                        }
                        $after_bind_ea_area_house_success_total = $building_result['BindEaAreaHouseSuccessTotal'] ?? 0;
                        $building_name                          = $building_result['BuildingName'];
                        $map_xiao_qu_building_all               = [
                            ['id', '=', $building_id]
                        ];
                        $update_data_xiao_qu_building_all       = [
                            'bind_update_status'               => 1,
                            'bind_last_update_time'            => format_timestamp(),
                            'ea_area_house_total'              => $building_result['BindEaAreaHouseSuccessTotal'],
                            'bind_ea_area_house_success_total' => $building_result['BindEaAreaHouseSuccessTotal'],
                        ];
                        //  "EaAreaHouseTotal": 150,
                        //	"BindEaAreaHouseSuccessTotal": 98, //绑定成功户数
                        //	"BindEaAreaOwnerSuccessTotal": null,
                        //	"EaAreaHouseTotalRate": null,
                        //	"BuildingNo": "77142566",
                        //	"BuildingName": "锦绣御园（一期）1栋",
                        if ($before_bind_ea_area_house_success_total < $after_bind_ea_area_house_success_total) {
                            //如果本栋有新数据, 则获取本栋绑定明细  然后尝试更新/新增   xiao_qu_house_all
                            $this->log($building_name . '-$building_id=-' . $building_id . '发现绑定数据有更新$before_bind_ea_area_house_success_total=' . $before_bind_ea_area_house_success_total . ';$after_bind_ea_area_house_success_total=' . $after_bind_ea_area_house_success_total);
                            $data       = [
                                'openId'       => '',
                                'eaAreaCode'   => $code,
                                'buildingName' => $building_name,
                            ];
                            $url        = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingHouseNO';
                            $begin_time = microtime(true);
                            $result2    = curl()->ignore_log()->set_timeout(60)->setMaxRetries(1)->form_params()->post($url, $data)->get_body();
                            $this->log('请求结束,耗时' . get_diff_time($begin_time) . '秒');
                            if (!empty($result2['List'])) {
                                $db_xiao_qu_house_all = new XiaoQuHouseAll();
                                $map                  = [
                                    ['xiao_qu_code', '=', $code],
                                    ['building_name', '=', $building_name],
                                    ['openid', '<>', ''],
                                ];
                                $openid_array         = $db_xiao_qu_house_all->where($map)->column('openid');
                                foreach ($result2['List'] as $user) {
                                    //      "EaAreaHouseTotal": null,
                                    //		"BindEaAreaHouseSuccessTotal": null,
                                    //		"BindEaAreaOwnerSuccessTotal": null,
                                    //		"EaAreaHouseTotalRate": null,
                                    //		"BuildingNo": "77142566",
                                    //		"BuildingName": "锦绣御园（一期）1栋",
                                    //		"HouseNo": "11C",
                                    //		"BindOfOwnerName": "1",
                                    //		"CertificateCode": "oLtYzuJvUCCm6cdzWQNEEdhbHdHE",
                                    //		"BuildOrder": "11",
                                    //		"FloorNum": "11",
                                    //		"Sort": 1757
                                    $bind_status = $user['BindOfOwnerName'] ?? -1;
                                    $house_no    = $user['HouseNo'] ?? -1;
                                    $openid      = $user['CertificateCode'] ?? '';
                                    if ($bind_status == 1 && $openid && !in_array($openid, $openid_array)) {
                                        //做更新
                                        $map                  = [
                                            ['xiao_qu_code', '=', $code],
                                            ['house_no', '=', $house_no],
                                            ['building_name', '=', $building_name],
                                        ];
                                        $db_xiao_qu_house_all = new XiaoQuHouseAll();
                                        $update_data          = [
                                            'bind_status' => $bind_status,
                                            'openid'      => $openid,
                                        ];
                                        $db_xiao_qu_house_all::update($update_data, $map);
                                        $this->log($building_name . '--' . $house_no . 'openid=' . $openid . '更新成功');
                                    } else {
//                                        $this->log($building_name . '--' . $house_no . 'openid=' . $openid . '可能已经存在或者openid 为空');
                                    }
                                }
                            } else {
                                $this->log($building_name . '--List  空的');
                            }
                        } else {
                            $this->log($building_name . '--发现绑定数据无需更新');
                        }
                        $db_xiao_qu_building_all::update($update_data_xiao_qu_building_all, $map_xiao_qu_building_all);
                    }
//                    $msg = $code . '更新完毕,已绑定' . $after_bind_ea_area_owner_success_total . '人';
                } else {
                    $this->log($code . '--数据没更新');
                }
                $db_xiao_qu_all::update($update_data_xiao_qu_all, $map_xiao_qu_all);
            }
        }
    }

    public function save_xiao_qu_all()
    {
        while (true) {
            $db_xiao_qu_all = new XiaoQuAll();
            $start_index    = $db_xiao_qu_all->count();
            $url            = 'https://zjj.sz.gov.cn/szwywx/page/enterpriseRanking/EvaluateStatisticControllerSvr.assx/getEvaluateRankStatistic';
            $data           = [
                "openId"          => "",
                "cantonCode"      => "",
                "streetCode"      => "",
                "stationCode"     => "",
                "evaluateObjType" => "3",
                "year"            => "2024",
                "month"           => "08",
                "startIndex"      => $start_index,
                "maxSize"         => "20",
                "sortOrder"       => true,
                "likeName"        => ""
            ];
            $result         = curl()->ignore_log()->form_params()->post($url, $data)->get_body();
            $insert_data    = [];
            $time           = microsecond();
            foreach ($result['orgList'] as $item) {
//            ["average"] => string(5) "59.37"
//            ["code"] => string(16) "1044030401001234"
//            ["name"] => string(24) "雕塑家园、雕塑院"
//            ["rank"] => string(4) "3231"
                $insert_data[] = [
                    'average'     => $item['average'],
                    'code'        => $item['code'],
                    'name'        => $item['name'],
                    'rank'        => $item['rank'],
                    'start_index' => $start_index,
                    'create_time' => $time,
                    'update_time' => $time,
                ];
            }
            if (empty($insert_data)) {
                $this->log(__FUNCTION__ . '更新完毕');
                break;
            }
            Db::name('xiao_qu_all')->insertAll($insert_data, 100);
        }
    }

    public function update_user_info()
    {
        $worker_num = $this->worker_num;
        $mid        = $this->mid;
        $order_by   = $this->order_by;
        while (true) {
            $db  = new XiaoQuHouseAll();
            $map = [
                ['person_name_full', 'NULL', NULL],
                ['bind_status', '=', 1],
                ['user_info_update_status', '<>', -1],
                ['openid', '<>', ''],
            ];
            if ($mid) {
                //从中间开始排序 则看看最大和最小的id
                $id_info = $db->where($map)->field(['max(id) as max_id', 'min(id) as min_id'])->find();
                $max_id  = $id_info['max_id'];
                $min_id  = $id_info['min_id'];
                $mid_id  = (int)($min_id + ($max_id - $min_id) / 2);
                $this->log('$max_id=' . $max_id . ';$min_id=' . $min_id . ';$mid_id=' . $mid_id);
                if ($order_by == 'ASC') {
                    //如果是从小到大  那就要大于中间值 避免和从0开始的id重复了
                    $map[] = ['id', '>', $mid_id];
                } else {
                    //如果是从大到小  那就要小于中间值
                    $map[] = ['id', '<', $mid_id];
                }
            }
            $list = $db->where($map)->limit(1000)->field(['openid', 'id'])->order(['id' => $this->order_by])->select()->toArray();
            $this->log('count($list)=' . count($list));
            if (count($list) == 0) {
                $msg = '全部update_user_info更新完毕,继续等待 60 S';
                $this->log($msg);
                sleep(60);
            } else {
                $url = 'https://zjj.sz.gov.cn/wxjwx/wx/weixinBasService/getOwnerInfo.json';
                if ($worker_num == 1) {
                    foreach ($list as $val) {
                        $id     = $val['id'];
                        $openid = $val['openid'];
                        $data   = ['openid' => $openid, 'owner_face_rs' => ''];
                        try {
                            $result = curl()->ignore_log()->form_params()->post($url, $data)->get_body();
                            $db     = new XiaoQuHouseAll();
                            $map    = [['id', '=', $id]];
                            if (is_array($result)) {
                                $update_data = [
                                    'user_info_update_status' => 1,//用户状态刷新成功
                                    'user_info'               => $result,
                                    'bind_time'               => $result['bindtime'],
                                    'phone_num_full'          => $result['phone_num'],
                                    'person_name_full'        => $result['person_name'],
                                    'certificate_code_full'   => $result['certificate_code'],
                                ];
                            } else {
                                $update_data = [
                                    'user_info_update_status'  => -1,
                                    'user_info_update_message' => '返回结果不是array',
                                ];
                            }
                        } catch (\Exception $e) {
                            $update_data = [
                                'user_info_update_status'  => -1,
                                'user_info_update_message' => $e->getMessage(),
                            ];
                        }
                        $result = $db::update($update_data, $map);
                        $msg    = '单个更新成功,ID' . $id . '----' . format_timestamp();
                        $this->log($msg);
                    }
                } else {
                    $chunks = array_chunk($list, $worker_num);
                    foreach ($chunks as $chunk) {
                        $url_data_array = [];
                        foreach ($chunk as $key => $val) {
                            $data             = [
                                'openid'        => $val['openid'],
                                'owner_face_rs' => ''
                                //    ["offline"] => string(1) "0"
                                //    ["certificate_code"] => string(18) "440306198510233115"
                                //    ["cert_no"] => NULL
                                //    ["bindtime"] => string(19) "2024-08-18 10:44:00"
                                //    ["openid"] => string(28) "oLtYzuJUc6iNUF-k6WNHW42r3824"
                                //    ["person_name"] => string(9) "黄宇生"
                                //    ["phone_num"] => string(11) "13823333017"
                                //    ["certificate_type"] => string(8) "02015001"
                                //    ["qualifi"] => string(1) "1"
                            ];
                            $url_data_array[] = [
                                'url'  => $url,
                                'data' => $data,
                                'ext'  => $val
                            ];
                        }
                        $result_array = curl()->ignore_log()->form_params()->multi_post($url_data_array);
                        foreach ($result_array as $result) {
                            $response     = $result['response'];
                            $request_data = $result['data'];
                            $request_ext  = $result['ext'];
                            $id           = $request_ext['id'];
                            $db           = new XiaoQuHouseAll();
                            $map          = [['id', '=', $id]];
                            if ($result['success'] !== true) {
                                $update_data = [
                                    'user_info_update_status'  => -2,
                                    'user_info_update_message' => $response,
                                ];
                            } else {
                                if (is_array($response)) {
                                    $update_data = [
                                        'user_info_update_status' => 1,
                                        'user_info'               => $response,
                                        'phone_num_full'          => $response['phone_num'],
                                        'bind_time'               => $response['bindtime'],
                                        'person_name_full'        => $response['person_name'],
                                        'certificate_code_full'   => $response['certificate_code'],
                                    ];
                                } else {
                                    $update_data = [
                                        'user_info_update_status'  => -1,
                                        'user_info_update_message' => '返回结果不是array',
                                    ];
                                }
                            }
                            $result = $db::update($update_data, $map);
                            $msg    = '并发更新成功,ID' . $id . '----' . format_timestamp();
                            $this->log($msg);
                        }
                    }
                }
            }
        }
    }

    public function update_xiao_qu_building_all()
    {
        $db    = new XiaoQuHouseAll();
        $count = $db->count();
        while (true) {
            $db_xiao_qu_building_all  = new XiaoQuBuildingAll();
            $map_xiao_qu_building_all = [['bind_update_status', '=', 0]];
            $info                     = $db_xiao_qu_building_all->limit(10)->field(['id', 'xiao_qu_code', 'building_name'])->where($map_xiao_qu_building_all)->order(['update_time' => 'ASC'])->select();
            if (count($info) == 0) {
                $msg = '全部update_xiao_qu_building_all更新完毕' . format_timestamp();
                $this->log($msg);
                break;
            }
            foreach ($info as $item) {
                $xiao_qu_code = $item['xiao_qu_code'];
                $data         = [
                    'openId'       => '',
                    'eaAreaCode'   => $xiao_qu_code,
                    'buildingName' => $item['building_name'],
                ];
                $update_map   = [['id', '=', $item['id']]];
                $insert_data  = [];
                try {
                    $url    = 'https://zjj.sz.gov.cn/szwywx/page/communityBasicInfo/HmfsOwnerHouseControllerSvr.assx/GetListEaAreaBuildingHouseNO';
                    $result = curl()->ignore_log()->form_params()->post($url, $data)->get_body();
                    if (empty($result['List'])) {
                        $update_data = [
                            'bind_update_status'  => -2,
                            'bind_update_message' => 'List 空数据'
                        ];
                    } else {
                        $update_data = [
                            'bind_update_status'   => 1,
                            'house_bind_total'     => $result['HouseBindTotal'] ?? -1,
                            'bind_owner_total'     => $result['BindOwnerTotal'] ?? -1,
                            'building_house_total' => $result['BuildingHouseTotal'] ?? -1,
                        ];
                        $time        = microsecond();
                        foreach ($result['List'] as $user) {
                            $insert_data[] = [
                                'xiao_qu_code'  => $xiao_qu_code,
                                'house_no'      => $user['HouseNo'] ?? -1,
                                'building_name' => $user['BuildingName'] ?? -1,
                                'building_no'   => $user['BuildingNo'] ?? -1,
                                'bind_status'   => $user['BindOfOwnerName'] ?? -1,
                                'openid'        => $user['CertificateCode'] ?? '',
                                'floor_num'     => $user['FloorNum'] ?? 0,
                                'create_time'   => $time,
                                'update_time'   => $time,
                            ];
                        }
                        if (!empty($insert_data)) {
                            Db::connect('mysql_host')->name('xiao_qu_house_all')->insertAll($insert_data, 100);
                        } else {
                            $update_data = [
                                'bind_update_status'  => -3,
                                'bind_update_message' => '$insert_data 空数据'
                            ];
                        }
                    }
                } catch (\Throwable|\Exception $e) {
                    $update_data = [
                        'bind_update_status'  => -1,
                        'bind_update_message' => $e->getMessage()
                    ];
                }
                $db_xiao_qu_building_all::update($update_data, $update_map);
                $msg = '更新成功' . count($insert_data) . '条,当前合计: ' . ($count += count($insert_data)) . ' 条' . format_timestamp();
                $this->log($msg);
                debug_log($msg);
            }
        }
    }

    public function monitor_all_house_num()
    {
        while (true) {
            $db   = new XiaoQuHouseAll();
            $map  = [['certificate_code_full', 'NOT NULL', NULL]];
            $list = $db->where($map)->count();
            $msg  = '当前合计: ' . $list . '条信息';
            $this->log($msg);
            debug_log($msg, 1);
            sleep(600);
        }
    }
}