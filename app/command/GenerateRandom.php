<?php

namespace app\command;

use app\common\service\TokenService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use app\common\tools\Excel;
use app\common\service\ToolsService;

class GenerateRandom extends Command
{
    protected function configure()
    {
        // 命令名称和参数定义（保持不变）
        $this->setName('generate:random')
            ->setDescription('生成指定长度的不重复随机数并导出Excel')
            ->addArgument('length', Argument::REQUIRED, '随机数长度（正整数）')
            ->addArgument('total', Argument::REQUIRED, '总生成数量（正整数）')
            ->addArgument('split', Argument::REQUIRED, '分割文件数（正整数）', 1);
    }

    protected function execute(Input $input, Output $output)
    {
        $length = (int)$input->getArgument('length');
        $total  = (int)$input->getArgument('total');
        $split  = (int)$input->getArgument('split');

        if ($length < 1 || $total < 1 || $split < 1) {
            $output->error('所有参数必须为正整数');
            return;
        }
        if ($split > $total) {
            $output->error('分割文件数不能超过总数量');
            return;
        }

        $perFile = (int)ceil($total / $split);
        $output->info("正在生成 {$total} 个 {$length} 位不重复随机数...");
        $randArr  = ToolsService::unique_rand($length, $total);
        $randList = array_chunk($randArr, $perFile);

        $path_guid       = create_guid();
        $file_name_array = [];

        $output->info("开始导出分割文件（共 {$split} 个）...");
        $excel     = new Excel();
        $header    = ['code' => '卡号'];
        $total_num = 0;

        foreach ($randList as $index => $list) {
            $fileNum = $index + 1;
            $data    = [];
            foreach ($list as $v) {
                $data[] = ['code' => $v];
            }
            $file_name = "random_{$length}位_{$fileNum}-{$split}";
            if (in_array($file_name, $file_name_array)) {
                $file_name = $file_name . '_' . $fileNum;
            }
            $file_name_array[] = $file_name;
            $local_file_name   = $excel->arrayToExcel($header, $data, $file_name, $path_guid, true);
            $total_num         += count($data);
            $output->info("文件 {$fileNum}/{$split} 生成完成：{$file_name}.xlsx");
        }

        $zip_file_name = $path_guid . '.zip';
        $result        = tools()::zip(tools()::get_absolute_path_disk() . $path_guid, tools()::get_absolute_path_disk() . $zip_file_name);
        if ($result) {
            // 这里没有 $bid 和 Business 相关逻辑，模拟下载文件名
            $download_file_name = "random_{$length}位_{$total}个_{$split}份-合计{$total_num}个卡号";
            $file_name          = tools()::auto_download($zip_file_name, $download_file_name);
            $output->info("文件生成成功,下载链接：{$file_name}");
        } else {
            $output->error('文件压缩失败,请反馈给客服!');
        }
    }
}