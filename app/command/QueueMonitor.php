<?php
declare (strict_types=1);

namespace app\command;

use <PERSON><PERSON>\BugsBunny\Connection;
use <PERSON><PERSON>\BugsBunny\Dispatcher;
use <PERSON><PERSON>\BugsBunny\QueueMessage;
use <PERSON><PERSON>\BugsBunny\Worker;
use <PERSON><PERSON>\BugsBunny\WorkerFactory;
use <PERSON><PERSON>\Whisper\Message;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\queue\command\BasicQueue;
use Throwable;

class QueueMonitor extends BasicQueue
{
    public int $way = 5;

    protected function configure()
    {
        // 指令配置
        $this->setName('queue_monitor')
            ->addArgument('connection', Argument::OPTIONAL, 'The name of the queue connection to work')
            ->addOption('queue', null, Option::VALUE_OPTIONAL, 'The queue to listen on')
            ->addOption('delay', null, Option::VALUE_OPTIONAL, 'Amount of time to delay failed jobs', 10)
            ->addOption('memory', null, Option::VALUE_OPTIONAL, 'The memory limit in megabytes', 32)
            ->addOption('tries', null, Option::VALUE_OPTIONAL, 'Number of times to attempt a job before logging it failed', 3)
            ->addOption('timeout', null, Option::VALUE_OPTIONAL, 'Number of timeout', 60)
            ->addOption('idle_shutdown_time', null, Option::VALUE_OPTIONAL, 'idle_shutdown_time of seconds', 120)
            ->addOption('worker_num', null, Option::VALUE_OPTIONAL, 'Number of worker_num', 20)
            ->setDescription('queue_monitor');
    }

    public function execute(Input $input, Output $output)
    {
        $output->writeln('队列监控开始,当前时间:' . format_timestamp());
        set_user_and_group();
        cli_set_process_title('xyf Queue Monitor Master');
        $this->listenForEvents();
        $queue_start_time   = time();
        $connection         = $input->getArgument('connection') ?: $this->app->config->get('queue.default');
        $queue              = $input->getOption('queue') ?: $this->app->config->get("queue.connections.{$connection}.queue", 'default');
        $delay              = $input->getOption('delay');
        $idle_shutdown_time = (int)$input->getOption('idle_shutdown_time');
        $tries              = (int)$input->getOption('tries');
        $max_memory         = (int)$input->getOption('memory');
        $timeout            = (int)$input->getOption('timeout');
        $worker_num         = (int)$input->getOption('worker_num');
        $params             = config('bugsbunny');
        $cache_key          = 'queue_monitor';
        $ttl                = 600;// 多少秒内有队列则会激活调度
        cache($cache_key, 1, $ttl); // 开启queue_monitor为 1,这样有队列才会通知rabbitmq,进行调度
        $factory = (new WorkerFactory())
            // 当worker空闲一定时间(秒)后会不再接收消息,并通知dispatcher结束它的生命周期
            ->setIdleShutdown($idle_shutdown_time)
            ->setMessageHandler(function (QueueMessage $message, Worker $worker) use ($connection, $queue, $delay, $tries, $cache_key, $ttl, $timeout) {
                $worker_id = $worker->getWorkerID();
                cli_set_process_title('xyf Worker #' . $worker_id);
                $body = json_decode($message->getContent(), true);
                $type = $body['type'] ?? '';
                switch ($type) {
                    case 'queue':
                        $queue_data = $body['data'];
                        $connection = $queue_data['connection'];
                        $queue      = $queue_data['queue'];
                        cache($cache_key, 1, $ttl); // 开启 queue_monitor为1,这样有队列才会通知rabbitmq,进行调度,每次执行完毕队列则延长有效期
                        do {
                            $result = $this->worker->runNextJob($connection, $queue, $delay, 0, $tries, $timeout);
                        } while ($result !== []);
                        break;
                    default:
                        wr_log('收到其他type:' . $type, 1);
                }
            })
            ->registerEvent('start', function (Worker $worker) use ($output) {
                $output->writeln('factory-start-event 队列启动成功,当前时间:' . format_timestamp());
            })
            ->registerEvent('patrolling', function (Worker $worker) {
                echo "factory-patrolling-event" . microsecond() . PHP_EOL;
            })
            ->registerEvent('message', function (Message $msg, Worker $worker) {
                echo "factory-message-event" . microsecond() . PHP_EOL;
            })
            ->registerEvent('disconnected', function (Worker $worker) {
                echo "factory-disconnected-event" . microsecond() . PHP_EOL;
            })
            ->registerEvent('shutdown', function (Worker $worker) {
                echo "factory-shutdown-event" . microsecond() . PHP_EOL;
            })
            ->registerEvent('error', function (string $reason, Throwable $e, Worker $worker) {
                echo "factory-error-event Worker Error. Reason: {$reason}, Message: {$e->getMessage()}\n";
            });
        $conn    = new Connection($params['connectionOptions'], $params['queues']);
        (new Dispatcher($conn, $factory))
            // 设置worker上限,将数量控制在一个安全的范围内
            ->setMaxWorkers($worker_num)
            // 设置缓存数量
            ->setCacheLimit(1000)
            ->on('start', function (Dispatcher $dispatcher) {
                echo "dispatcher-start-event" . microsecond() . PHP_EOL;
            })
            ->on('patrolling', function (Dispatcher $dispatcher) {
                echo "dispatcher-patrolling-event" . microsecond() . PHP_EOL;
            })
            ->on('consumed', function (Dispatcher $dispatcher) {
                echo "dispatcher-consumed-event" . microsecond() . PHP_EOL;
            })
            ->on('limitReached', function (Dispatcher $dispatcher) {
                echo "dispatcher-limitReached-event" . microsecond() . PHP_EOL;
            })
            ->on('message', function (Dispatcher $dispatcher) {
                echo "dispatcher-message-event" . microsecond() . PHP_EOL;
            })
            ->on('customMessageProcessed', function (string $workerID, Dispatcher $dispatcher) {
                echo "dispatcher-customMessageProcessed-event  workerID=" . $workerID . ' ;time=' . microsecond() . PHP_EOL;
            })
            ->on('processed', function (string $workerID, Dispatcher $dispatcher) use ($queue_start_time, $max_memory) {
                echo "dispatcher-processed-event" . microsecond() . PHP_EOL;
                $stat               = $dispatcher->getStat();
                $processed          = $stat['processed'];//处理了的消息总数(它代表处理的队列消息的数量,不包含自定义消息)
                $consumed           = $stat['consumed'];//消费了的消息总数(它代表消费的队列消息的数量,不包含自定义消息)
                $max_message_length = $stat['maxMessageLength'];//消费了的消息总数(它代表消费的队列消息的数量,不包含自定义消息)
                $peak_num_workers   = $stat['peakNumWorkers'];//worker数量峰值
                $peak_num_cached    = $stat['peakNumCached'];//缓存消息数量峰值
                $msg                = '';
                foreach ($stat as $k => $v) {
                    $msg .= $k . ' = ' . $v . ';';
                }
                //$memory    = number_format(memory_get_usage());
                $memory           = round(memory_get_usage() / 1024 / 1024, 4);
                $memory_real_used = round(memory_get_usage(true) / 1024 / 1024, 4);
                echo '[' . microsecond() . "] {$processed}/{$consumed} Worker {$workerID} Has Processed A Message, Cached:{$dispatcher->numCached()}, Workers:{$dispatcher->countWorkers()}, Memory Usage:{$memory} Mb, Memory Real Usage:{$memory_real_used} Mb ;stat_info:" . $msg . PHP_EOL;
                if ($this->worker->memoryExceeded($max_memory)) {
                    echo "内存已经超出啦,尝试重启" . PHP_EOL;
                    $dispatcher->shutdown();
                } elseif ($this->worker->queueShouldRestart($queue_start_time)) {
                    echo "可能应用有更新,尝试重启" . PHP_EOL;
                    $dispatcher->shutdown();
                }
            })
            ->on('workerExit', function (string $workerID, int $pid, Dispatcher $dispatcher) {
                echo "dispatcher-workerExit-event" . microsecond() . PHP_EOL;
                $count = $dispatcher->countWorkers();
                echo "Worker {$workerID} Quit, PID: {$pid}, {$count} Remains" . PHP_EOL;
            })
            ->on('error', function (string $reason, Throwable $e, Dispatcher $dispatcher) {
                echo "Dispatcher Error, Reason: {$reason}, Message: {$e->getMessage()}\n";
                $dispatcher->shutdown();
            })
            ->on('shutdown', function (Dispatcher $dispatcher) use ($cache_key) {
                echo "dispatcher-shutdown-event" . microsecond() . PHP_EOL;
                cache($cache_key, 0); // 系统关闭之前,关闭调度开关
                $stat = $dispatcher->getStat();
                echo "\n";
                echo "Consumed Message: {$stat['consumed']}" . PHP_EOL;
                echo "Processed Message: {$stat['processed']}" . PHP_EOL;
                echo "Max Queue Message Length: {$stat['maxMessageLength']} Bytes" . PHP_EOL;
                echo "Peak Number Of Workers: {$stat['peakNumWorkers']}" . PHP_EOL;
                echo "Peak Number Of Cached Messages: {$stat['peakNumCached']}" . PHP_EOL;
                //echo "Peak Memory Usage: " . number_format(memory_get_peak_usage()) . ' Bytes.' . PHP_EOL;
                echo "Peak Memory Usage: " . round(memory_get_usage() / 1024 / 1024, 4) . ' Mb' . PHP_EOL;
            })
            ->addSignalHandler(SIGINT, function (int $signal, Dispatcher $dispatcher) {
                $dispatcher->shutdown();
            })
            ->run();
    }
}