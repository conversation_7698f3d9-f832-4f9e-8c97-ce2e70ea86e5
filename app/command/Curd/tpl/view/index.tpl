{layout name="layui_plus"  /}
<div class='layui-fluid min-width-1120'>
    <div class='layui-card'>
        <div class='layui-card-body'>
            <div id='layui_form_query'>
                <form class="layui-form" id="query_form">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <select name="key" lay-verify="">
                                <option value=""></option>
                                <option_list>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 200px;">
                                <input type="text" name="value" placeholder="请输入" autocomplete="off"
                                       class="layui-input"/>
                            </div>
                        </div>
                        <div class="layui-inline layui-input-wrap">
                            <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                            <input type="text" name="create_time" autocomplete="off"
                                   class="layui-input fsDate" readonly dateRange="1" placeholder="创建时间"/>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <button class="layui-btn" type="button" function="query"><i
                                            class="layui-icon layui-icon-search"></i>查询
                                </button>
                                <button class="layui-btn layui-btn-normal" type="reset"><i
                                            class="layui-icon layui-icon-delete"></i>重置
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid" isLoad="1"
                       url="/<controller>/index" isPage="1" defaultForm="query_form"
                       defaultToolbar="filter" height="auto"></table>
                <div class="fsDatagridCols">
                    <!--<p type="numbers" title="#"/>-->
                    <!--<p checkbox="true"/>-->
<table_field_list>
                        <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="120"/>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="toolbarDemo">
    <div class="layui-inline">
        <button class="layui-btn layui-btn-sm" function="top" topUrl="/admin/<controller>/detail" topMode="add"
                topWidth="800px"
                topHeight="95%" topTitle="新增">
            <i class="layui-icon layui-icon-addition"></i>新增
        </button>

        <!--        <button class="layui-btn layui-btn-sm layui-btn-danger" function="submit" url="#" isMutiDml="1"-->
        <!--                isConfirm="1" confirmMsg="是否确定删除选中的数据？" inputs="id:">-->
        <!--            <i class="layui-icon layui-icon-delete"></i>删除-->
        <!--        </button>-->
        <!--        <button class="layui-btn layui-btn-sm" function="refresh">-->
        <!--            <i class="layui-icon layui-icon-refresh"></i>刷新-->
        <!--        </button>-->
    </div>
</script>

<script type="text/html" id="barDemo">
    <!--<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="top" topUrl="/admin/<controller>/detail"-->
    <!--topMode="readonly"-->
    <!--topWidth="800px" topHeight="600px" topTitle="详情" inputs="guid:">查看</a>-->
    <a class="layui-btn layui-btn-xs" lay-event="top" topUrl="/admin/<controller>/detail" topMode="edit"
       topWidth="700px"
       topHeight="600px" topTitle="编辑" inputs="id:">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="submit" url="/<controller>/del" isConfirm="1"
       confirmMsg="是否确定删除当前记录？" inputs="id:">删除</a>
</script>
