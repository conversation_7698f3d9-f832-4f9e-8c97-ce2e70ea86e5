<?php

namespace app\command\Curd;

use app\command\Curd\template\ControllerAutoMake;
use app\command\Curd\template\ModelAutoMake;
use app\command\Curd\template\ValidateAutoMake;
use app\command\Curd\template\ViewAutoMake;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

class Curd extends Command
{
    protected function configure()
    {
        $this->setName('auto curd')
            ->addOption('table', 't', Option::VALUE_OPTIONAL, 'the table name', null)
            ->addOption('layer', 'l', Option::VALUE_OPTIONAL, 'the layer name', 'admin_api\v1')
            ->addOption('controller', 'c', Option::VALUE_OPTIONAL, 'the controller name', null)
            ->setDescription('auto make curd file');
    }

    protected function execute(Input $input, Output $output)
    {
        !defined('DS') && define('DS', DIRECTORY_SEPARATOR);

        $table = $input->getOption('table');
        if (!$table) {
            $output->error("请输入 -t 表名");
            return null;
        }

        $layer = $input->getOption('layer');
        if (!$layer) {
            $output->error("请输入 -l 模块");
            return null;
        }

        $controller = $input->getOption('controller') ?: $table;

        // 执行生成controller策略
        $controller_auto_make = new ControllerAutoMake();
        $controller_auto_make->make($controller, $layer, $table);

        // 执行生成model策略
        $model_auto_make = new ModelAutoMake();
        $model_auto_make->make($controller, $layer, $table);

        // 执行生成validate策略
        $validate_auto_make = new ValidateAutoMake();
        $validate_auto_make->make($controller, $layer, $table);

        // 执行生成view策略
        $view_auto_make = new ViewAutoMake();
        $view_auto_make->make($controller, $layer, $table);

        $output->writeln("auto make curd success");
        return null;

    }
}