<?php

namespace app\command\Curd\template;

use Symfony\Component\VarExporter\VarExporter;
use think\console\Output;

class ValidateAutoMake extends Base
{

    public function make($controller, $layer, $table)
    {
        $output_file_name = app()->parseClass('validate', $layer . DS . $controller) . '.php';
        if (file_exists($output_file_name)) {
            $output = new Output();
            $output->error("$output_file_name 文件已经存在,不再自动生成");
            return;
        }
        $tplContent = file_get_contents(dirname(__DIR__) . DS . 'tpl' . DS . 'validate.tpl');


        $table_info = $this->get_table_info($table);
        $rule       = [];
        $message    = [];
        foreach ($table_info as $vo) {
            $field = $vo['Field'];
            if (in_array($field, ['delete_time', 'create_time', 'update_time'])) {
                continue;
            }
            $comment                      = $vo['Comment'];
            $rule[$field]                 = ['require'];
            $message[$field . '.require'] = $field . '不能为空';
        }

        $ruleArr    = VarExporter::export($rule);
        $messageArr = VarExporter::export($message);

        $tplContent = str_replace('<rule>', $ruleArr, $tplContent);
        $tplContent = str_replace('<message>', $messageArr, $tplContent);
        $tplContent = $this->replace_tpl_content($tplContent, $controller, $layer, $table);

        tools()::mkdir(dirname($output_file_name));
        file_put_contents($output_file_name, $tplContent);
    }
}