<?php

namespace app\command\Curd\template;

use think\facade\Db;

class Base
{
    public $table_info = [];

    /**
     * 下划线转驼峰
     * @param $unCamelizedWords
     * @param string $separator
     * @return string
     */
    public static function camelize($unCamelizedWords, $separator = '_')
    {
        $unCamelizedWords = $separator . str_replace($separator, " ", strtolower($unCamelizedWords));
        return ltrim(str_replace(" ", "", ucwords($unCamelizedWords)), $separator);
    }

    /**
     * @param $table
     * @return array|mixed
     */
    public function get_table_info($table)
    {
        if (!empty($this->table_info)) {
            return $this->table_info;
        }
        $prefix = config('database.connections.mysql.prefix');
        return $this->table_info = Db::query('SHOW FULL COLUMNS FROM `' . $prefix . $table . '`');
    }

    public function replace_tpl_content($tplContent, $controller, $layer, $table)
    {
        $model      = ucfirst(self::camelize($table));
        $namespace  = empty($layer) ? '\\' : '\\' . $layer . '\\';
        $pk         = $this->get_pk_field($table);
        $tplContent = str_replace('<namespace>', $namespace, $tplContent);
        $tplContent = str_replace('<controller>', $controller, $tplContent);
        $tplContent = str_replace('<model>', $model, $tplContent);
        $tplContent = str_replace('<layer>', $layer, $tplContent);
        return str_replace('<pk>', $pk, $tplContent);
    }

    /**
     * @param $table
     * @return mixed|string
     */
    public function get_pk_field($table)
    {
        $table_info = $this->get_table_info($table);
        $pk         = '';
        foreach ($table_info as $vo) {
            if ($vo['Key'] == 'PRI') {
                $pk = $vo['Field'];
                break;
            }
        }
        foreach ($table_info as $vo) {
            if ($vo['Field'] == 'guid') {
                $pk = 'guid';
                break;
            }
        }
        return $pk;
    }
}