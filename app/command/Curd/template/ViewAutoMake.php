<?php

namespace app\command\Curd\template;

use think\console\Output;

class ViewAutoMake extends Base
{

    public function make($controller, $layer, $table)
    {
        $output_file_path = app()->parseClass('view', 'admin');
        $output_file_path = strtolower($output_file_path . DS . $controller);
        $index_file_name  = 'index.html';
        $detail_file_name = 'detail.html';
        $output           = new Output();
        if (file_exists($output_file_path . DS . $index_file_name)) {
            $output->error($output_file_path . DS . $index_file_name . "文件已经存在,不再自动生成");
            return;
        }
        if (file_exists($output_file_path . DS . $detail_file_name)) {
            $output->error($output_file_path . DS . $detail_file_name . "文件已经存在,不再自动生成");
            return;
        }

        $indexTpl         = dirname(__DIR__) . '/tpl/view/index.tpl';
        $detailTpl        = dirname(__DIR__) . '/tpl/view/detail.tpl';
        $indexTplContent  = file_get_contents($indexTpl);
        $detailTplContent = file_get_contents($detailTpl);
        $indexTplContent  = $this->replace_tpl_content($indexTplContent, $controller, $layer, $table);
        $detailTplContent = $this->replace_tpl_content($detailTplContent, $controller, $layer, $table);


        $pk               = $this->get_pk_field($table);
        $table_info       = $this->get_table_info($table);
        $table_field_list = '';
        $edit_field_list  = '';
        $option_list      = '';
        foreach ($table_info as $vo) {
            $field            = $vo['Field'];
            $comment          = $vo['Comment'];
            $title            = $comment ?: $field;
            $table_field_list .= "<p align=\"center\" field=\"{$field}\" title=\"{$title}\">";
            $table_field_list .= "\r\n";
            if (!in_array($field, ['create_time', 'update_time', $pk])) {
                $edit_field_list .= "<div class=\"layui-form-item\">
                        <label class=\"layui-form-label\">$title</label>
                        <div class=\"layui-input-block\">
                            <input type=\"text\" name=\"$field\" required=\"\" lay-verType=\"tips\" lay-verify=\"required\"
                                   placeholder=\"请输入$title\"
                                   autocomplete=\"off\" class=\"layui-input\"/>
                        </div>
                    </div>";
                $edit_field_list .= "\r\n";

                $option_list .= "<option value=\"$field\">$title</option>";
                $option_list .= "\r\n";
            }
        }
        // 列表页模板生成

        $indexTplContent = str_replace('<table_field_list>', $table_field_list, $indexTplContent);
        $indexTplContent = str_replace('<option_list>', $option_list, $indexTplContent);

        //详情页模板生成

        $detailTplContent = str_replace('<edit_field_list>', $edit_field_list, $detailTplContent);

        tools()::mkdir($output_file_path);
        file_put_contents($output_file_path . DS . $index_file_name, $indexTplContent);
        file_put_contents($output_file_path . DS . $detail_file_name, $detailTplContent);
    }
}