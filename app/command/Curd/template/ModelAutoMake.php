<?php

namespace app\command\Curd\template;

use think\console\Output;

class ModelAutoMake extends Base
{

    public function make($controller, $layer, $table)
    {
        $output_file_name = app()->parseClass('model', $controller) . '.php';
        if (file_exists($output_file_name)) {
            $output = new Output();
            $output->error("$output_file_name 文件已经存在,不再自动生成");
            return;
        }
        $tplContent = file_get_contents(dirname(__DIR__) . DS . 'tpl' . DS . 'model.tpl');
        $tplContent = $this->replace_tpl_content($tplContent, $controller, $layer, $table);
        tools()::mkdir(dirname($output_file_name));
        file_put_contents($output_file_name, $tplContent);
    }
}