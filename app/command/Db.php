<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

class Db extends Command
{


    /**
     * The queue worker pid.
     * @var string
     */
    protected $pid;

    public function __construct()
    {
        parent::__construct();
        $this->pid = getmypid();
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('remove_field_cache')
            ->setDescription('the test command');
    }


    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        \app\common\tools\Db::remove_field_cache();
        $output->writeln('remove_field_cache success');
    }
}
