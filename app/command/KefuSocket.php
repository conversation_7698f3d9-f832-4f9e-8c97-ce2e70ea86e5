<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

use Workerman\Worker;
use GatewayWorker\Gateway;
use GatewayWorker\Register;
use GatewayWorker\BusinessWorker;

class KefuSocket extends Command
{
    protected function configure()
    {
        $this->setName('kefu_socket')
            ->addArgument('action', null, 'start|stop|restart|status', 'start')
            ->setDescription('启动客服WebSocket服务 (支持 start|stop|restart|status)');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action') ?: 'start';
        $output->writeln('WebSocket 服务启动中...');

        // 兼容 get_lan_ip
        if (!function_exists('get_lan_ip')) {
            function get_lan_ip()
            {
                return gethostbyname(gethostname());
            }
        }

        // 加载配置
        $config        = config('kefu_socket');
        $master_lan_ip = config('app.master_lan_ip');
        $register_port = $config['register_port'];
        $lan_ip        = get_lan_ip();

        // Register
        if ($lan_ip == $master_lan_ip) {
            $register = new Register('text://0.0.0.0:' . $register_port);
        }

        // Gateway
        if ($lan_ip == $master_lan_ip) {
            if ($config['is_open_ssl']) {
                $gateway            = new Gateway("Websocket://0.0.0.0:" . $config['ws_port'], $config['context']);
                $gateway->transport = 'ssl';
            } else {
                $gateway = new Gateway("Websocket://0.0.0.0:" . $config['ws_port']);
            }
            $gateway->name                 = 'wsAppGateway';
            $gateway->count                = $config['gateway_worker'];
            $gateway->lanIp                = $lan_ip;
            $gateway->startPort            = $config['start_port'];
            $gateway->registerAddress      = $lan_ip . ':' . $register_port;
            $gateway->pingInterval         = 30;
            $gateway->pingNotResponseLimit = 2;
            $gateway->pingData             = '';
        }

        // BusinessWorker
        $worker                  = new BusinessWorker();
        $worker->eventHandler    = 'app\\service\\Events';
        $worker->name            = 'wsBusinessWorker';
        $worker->count           = $config['business_worker'];
        $worker->registerAddress = $master_lan_ip . ':' . $register_port;

        // 兼容 Workerman 命令参数
        global $argv;
        $argv = [
            'think',
            'kefu_socket',
            $action
        ];

        Worker::runAll();
    }
}
