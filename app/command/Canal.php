<?php
declare (strict_types=1);

namespace app\command;

use app\model\Cdc;
use Com\Alibaba\Otter\Canal\Protocol\Column;
use Com\Alibaba\Otter\Canal\Protocol\Entry;
use Com\Alibaba\Otter\Canal\Protocol\EntryType;
use Com\Alibaba\Otter\Canal\Protocol\EventType;
use Com\Alibaba\Otter\Canal\Protocol\RowChange;
use Com\Alibaba\Otter\Canal\Protocol\RowData;
use Exception;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use xingwenge\canal_php\adapter\CanalConnectorBase;
use xingwenge\canal_php\CanalClient;
use xingwenge\canal_php\CanalConnectorFactory;

class Canal extends Command
{


    /**
     * The queue worker pid.
     * @var string
     */
    protected $pid;
    /**
     * Indicates if the worker should exit.
     *
     * @var bool
     */
    public bool $shouldQuit = false;

    public function __construct()
    {
        parent::__construct();
        $this->pid = getmypid();
    }

    protected function configure()
    {
        // 指令配置
        $this->setName('canal')
            ->setDescription('the canal command');
    }

    /**
     * Enable async signals for the process.
     *
     * @return void
     */
    protected function listenForSignals()
    {
        pcntl_async_signals(true);
        pcntl_signal(SIGTERM, function () {
            $this->output('收到SIGTERM信号,标记要退出');
            $this->shouldQuit = true;
        });
    }

    /**
     * Determine if "async" signals are supported.
     *
     * @return bool
     */
    protected function supportsAsyncSignals()
    {
        return extension_loaded('pcntl');
    }

    protected function execute(Input $input, Output $output)
    {
        set_user_and_group();
        $start_time = time();
        if ($this->supportsAsyncSignals()) {
            $this->listenForSignals();
        }
        while (true) {
            try {
                $client = CanalConnectorFactory::createClient(CanalClient::TYPE_SOCKET_CLUE);
                # $client = CanalConnectorFactory::createClient(CanalClient::TYPE_SWOOLE);
                $client->connect("127.0.0.1", 11111, 'canal', 'canal');
                $client->subscribe(1001, "example", ".*\\..*");
                # $client->subscribe("1001", "example", "db_name.tb_name"); // 设置过滤 支持多次 subscribe 调用
                while (true) {
                    if ($this->shouldRestart($start_time)) {
                        //超过一定时间了需要退出
                        $this->stop();
                    }
                    $message         = $client->getWithoutAck(1000);
                    $id              = $message->getId();
                    $insert_data_all = [];
                    $create_time     = microsecond();
                    if ($entries = $message->getEntries()) {
                        foreach ($entries as $entry) {
                            $temp_data = self::analysis($entry, $create_time);
                            if (!empty($temp_data)) {
                                $insert_data_all = array_merge($insert_data_all, $temp_data);
                            }
//                            try {
//                                self::binlog_sql($entry);
//                            } catch (\Exception $e) {
//                                dump($e->getMessage());
//                            }
                        }
                    }
                    if (!empty($insert_data_all)) {
                        $result = Db::name('cdc')->data($insert_data_all)->limit(100)->insertAll();
                    }
                    $client->ack($id);
//                    sleep(1);
                    usleep(100000); // 休眠100毫秒
                }
                /** @var CanalConnectorBase $client */
                $client->disConnect();
            } catch (Exception $e) {
                $msg = 'canal发生故障,' . $e->getMessage() . $e->getLine() . 'FILE=' . $e->getFile() . PHP_EOL;
                echo $msg;
//                if (strpos($e->getMessage(), 'MySQL server has gone away') !== false) {
//                    exit();
//                }
                sleep(3);
            }
        }
        $output->writeln('execute success');
    }

    protected static function is_require_record($schema_name, $table_name, $changed_fields)
    {
        $key = array_search('update_time', $changed_fields);
        if ($key !== false) {
            unset($changed_fields[$key]);//单独这个字段都不记录
        }
        if (empty($changed_fields)) {
            return false;
        }
        $rule_array = [
            'platform'           => [
                'jobs'                  => false,
                'crontab'               => false,
                'cdc'                   => false,
                'his'                   => false,
                'sms_send_note'         => false,
                'yky_consume_note'      => false,
                'websocket_user'        => false,
                'xiao_qu_all'           => false,
                'xiao_qu_building_all'  => false,
                'xiao_qu_house_all'     => false,
                'business'              => [
                    'ignore_fields' => ['last_login_time', 'sms_num'],
                ],
                'yky_plus_member'       => [
                    'ignore_fields'       => ['create_time'],
                    'ignore_fields_group' => [
                        ['update_time', 'last_update_date'],
                    ]
                ],
                'goods'                 => [
                    'ignore_fields' => ['view_times', 'sales'],
                ],
                'goods_order'           => [
                    'ignore_fields' => ['delete_time'],
                ],
                'yky_plus_member_order' => [
                    'ignore_fields'       => ['create_time'],
                    'ignore_fields_group' => [
                        ['update_time', 'last_update_date'],
                    ]
                ],
            ],
            'mysql'              => false,
            'trace'              => false,
            'information_schema' => false
        ];
        if (!array_key_exists($schema_name, $rule_array)) {
            //不在这个名单的直接返回需要记录
            return true;
        }

        if (is_bool($rule_array[$schema_name]) && $rule_array[$schema_name] === false) {
            //在名单且设置为false,则整个数据库都不记录
            return false;
        }

        $schema_rule_array = $rule_array[$schema_name];
        if (!array_key_exists($table_name, $schema_rule_array)) {
            //不在这个名单的直接返回需要记录
            return true;
        }
        if (is_bool($schema_rule_array[$table_name]) && $schema_rule_array[$table_name] === false) {
            //在名单且设置为false,则整个表都不记录
            return false;
        }
        $table_rule_array = $schema_rule_array[$table_name];
        if (array_key_exists('ignore_fields', $table_rule_array)) {
            if ($changed_fields == array_intersect($changed_fields, $table_rule_array['ignore_fields'])) {
                //如果变更的数组 全是忽略字段 则不记录
                return false;
            }
        }
        if (array_key_exists('ignore_fields_group', $table_rule_array)) {
            foreach ($table_rule_array['ignore_fields_group'] as $ignore_fields_group) {
                if (!array_diff($ignore_fields_group, $changed_fields) && !array_diff($changed_fields, $ignore_fields_group)) {
                    // 即相互都不存在差集，那么这两个数组就是相同的了，则不记录
                    return false;
                }
            }
        }
        return true;
    }

    public static function compare_items($k, $v)
    {
        #caution: if v is NULL, may need to process
        if (is_numeric($v)) {
            //todo
        } else if (is_null($v)) {
            return "`{$k}` IS NULL";
        } else {
            $v = "'" . str_replace(array("\r", "\n"), array('\\r', '\\n'), addslashes($v)) . "'";
        }
        return "`$k`=$v";
    }

    function generate_sql_pattern($binlogevent, $row = null, $flashback = false, $nopk = false)
    {
        $template = '';
        $values   = [];
        if ($flashback) {
            if ($binlogevent instanceof WriteRowsDTO) {

                $arr    = [];
                $values = array_values($row['values']);
                foreach ($row['values'] as $k => $v) {
                    $arr[] = $this->compare_items($k, $v);
                }
                $template = printf(
                    'DELETE FROM `%s`.`%s` WHERE %s LIMIT 1;',
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(' AND ', $arr)
                );
            } elseif ($binlogevent instanceof DeleteRowsDTO) {

                $arr    = [];
                $keyArr = [];
                $values = array_values($row['values']);
                foreach ($row['values'] as $k => $v) {
                    $keyArr[] = '`' . $k . '`';
                    $arr[]    = "'{$v}'";
                }
                $whereString = implode(', ', $arr);
                $template    = printf(
                    "INSERT INTO `%s`.`%s`(%s) VALUES (%s);",
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(', ', $keyArr),
                    implode(', ', $arr)
                );
            } elseif ($binlogevent instanceof UpdateRowsDTO) {
                $arr    = [];
                $keyArr = [];
                $values = $row['values'];
                foreach ($row['before_values'] as $k => $v) {
                    $keyArr[] = '`' . $k . '`=' . $v;
                }
                foreach ($row['after_values'] as $k => $v) {
                    $arr[] = $this->compare_items($k, $v);
                }
                $whereString = implode(', ', $arr);
                $template    = printf(
                    "UPDATE `%s`.`%s` SET %s WHERE %s LIMIT 1;",
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(', ', $keyArr),
                    implode(' AND ', $arr)
                );
                $values      = array_values($row['before_values'] + $row['after_values']);
            }
        } else {

            if ($binlogevent instanceof WriteRowsDTO) {

//            if ($nopk) {
//                if ($binlogevent->primary_key) {
//                    array_pop($row['values']);
//                }
//            }
                $arr    = [];
                $keyArr = [];
                $values = array_values($row['values']);
                foreach ($row['values'] as $k => $v) {
                    $keyArr[] = '`' . $k . '`';
                    $arr[]    = "'{$v}'";
                }
                $whereString = implode(', ', $arr);
                $template    = printf(
                    "INSERT INTO `%s`.`%s`(%s) VALUES (%s);",
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(', ', $keyArr),
                    implode(', ', $arr)
                );
            } elseif ($binlogevent instanceof DeleteRowsDTO) {

                $arr    = [];
                $values = array_values($row['values']);
                foreach ($row['values'] as $k => $v) {
                    $arr[] = $this->compare_items($k, $v);
                }
                $template = printf(
                    'DELETE FROM `%s`.`%s` WHERE %s LIMIT 1;',
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(' AND ', $arr)
                );
            } elseif ($binlogevent instanceof UpdateRowsDTO) {
                $arr    = [];
                $keyArr = [];
                foreach ($row['before_values'] as $k => $v) {
                    $keyArr[] = '`' . $k . '`=' . $v;
                }
                foreach ($row['after_values'] as $k => $v) {
                    $arr[] = $this->compare_items($k, $v);
                }
                $template = printf(
                    "UPDATE `%s`.`%s` SET %s WHERE %s LIMIT 1;",
                    $binlogevent->schema,
                    $binlogevent->table,
                    implode(', ', $keyArr),
                    implode(' AND ', $arr)
                );
                $values   = array_values($row['after_values'] + $row['before_values']);
            }
        }

        return [
            'template' => $template,
            'values'   => $values,
        ];
    }

    /**
     * @param Entry $entry
     * @throws Exception
     */
    protected static function binlog_sql(Entry $entry)
    {
        switch ($entry->getEntryType()) {
            case EntryType::TRANSACTIONBEGIN:
            case EntryType::TRANSACTIONEND:
                return;
        }

        $rowChange = new RowChange();
        $rowChange->mergeFromString($entry->getStoreValue());
        $header       = $entry->getHeader();
        $execute_time = $header->getExecuteTime();
        $table_name   = $header->getTableName();
        $schema_name  = $header->getSchemaName();
        $execute_time = format_timestamp($execute_time / 1000);
        $file_name    = $header->getLogfileName();
        $off_set      = $header->getLogfileOffset();
        $event_type   = $header->getEventType();
        $sql          = $rowChange->getSql();
        foreach ($rowChange->getRowDatas() as $rowData) {
            switch ($event_type) {
                case EventType::DELETE:
                    //                    self::get_column_name_value_array($rowData->getBeforeColumns());
                    break;
                case EventType::INSERT:
                    break;
                case EventType::UPDATE:
                    $before_value_array = self::get_column_name_value_array($rowData->getBeforeColumns());
                    $after_value_array  = self::get_column_name_value_array($rowData->getAfterColumns());

                    $arr    = [];
                    $keyArr = [];
                    foreach ($before_value_array as $k => $v) {
//                        $keyArr[] = '`' . $k . '`=' . $v;
                        $keyArr[] = self::compare_items($k, $v);
                    }
                    foreach ($after_value_array as $k => $v) {
                        $arr[] = self::compare_items($k, $v);
                    }
                    $template = printf(
                        "UPDATE `%s`.`%s` SET %s WHERE %s LIMIT 1;",
                        $schema_name,
                        $table_name,
                        implode(', ', $keyArr),
                        implode(' AND ', $arr)
                    );

                    echo 'template:' . $template . PHP_EOL;

                    $before_value   = array_diff_assoc($before_value_array, $after_value_array);
                    $after_value    = array_diff_assoc($after_value_array, $before_value_array);
                    $changed_fields = array_keys($after_value);

                    break;
                default:
                    echo '-------> 暂不支持的type:' . $event_type . PHP_EOL;
                    break;
            }
        }
    }

    /**
     * @param Entry $entry
     * @throws Exception
     */
    public static function analysis(Entry $entry, $create_time)
    {
        switch ($entry->getEntryType()) {
            case EntryType::TRANSACTIONBEGIN:
            case EntryType::TRANSACTIONEND:
                return [];
        }
        $rowChange = new RowChange();
        $rowChange->mergeFromString($entry->getStoreValue());
        $header       = $entry->getHeader();
        $execute_time = $header->getExecuteTime();
        $table_name   = $header->getTableName();
        $schema_name  = $header->getSchemaName();
        $execute_time = format_timestamp($execute_time / 1000);
        $file_name    = $header->getLogfileName();
        $off_set      = $header->getLogfileOffset();
        $event_type   = $header->getEventType();
        $sql          = $rowChange->getSql();
        echo $execute_time . '发生数据变动:' . $schema_name . '.' . $table_name . PHP_EOL . 'sql:' . $sql . PHP_EOL . 'off_set=' . $off_set . PHP_EOL;
        /** @var RowData $rowData */
        $insert_data = [];
        foreach ($rowChange->getRowDatas() as $rowData) {
            switch ($event_type) {
                case EventType::DELETE:
                    //                    self::get_column_name_value_array($rowData->getBeforeColumns());
                    break;
                case EventType::INSERT:
                    break;
                case EventType::UPDATE:
                    $before_value_array = self::get_column_name_value_array($rowData->getBeforeColumns());
                    $after_value_array  = self::get_column_name_value_array($rowData->getAfterColumns());
                    $before_value       = array_diff_assoc($before_value_array, $after_value_array);
                    $after_value        = array_diff_assoc($after_value_array, $before_value_array);
                    $changed_fields     = array_keys($after_value);
                    if (!self::is_require_record($schema_name, $table_name, $changed_fields)) {
                        break;
                    }
//                    $db   = new Cdc();
                    $update_time   = microsecond();
                    $insert_data[] = [
                        'off_set'      => $off_set,
                        'table_name'   => $table_name,
                        'schema_name'  => $schema_name,
                        'execute_time' => $execute_time,
                        'bid_value'    => $after_value_array['bid'] ?? '',
                        'id_value'     => $after_value_array['id'] ?? '',
                        'guid_value'   => $after_value_array['guid'] ?? '',
                        'after_value'  => json_encode($after_value, JSON_UNESCAPED_UNICODE),
                        'before_value' => json_encode($before_value, JSON_UNESCAPED_UNICODE),
                        'create_time'  => $create_time,
                        'update_time'  => $update_time,
                    ];
//                    $db->save($data);
                    break;
                default:
                    echo '-------> 暂不支持的type:' . $event_type . PHP_EOL;
                    break;
            }
        }
        return $insert_data;
    }

    protected static function get_update_value($columns)
    {
        $arr = [];
        /** @var Column $column */
        foreach ($columns as $column) {
            $is_update = $column->getUpdated();
            if ($is_update) {
                $arr[$column->getName()] = $column->getValue();
            }
        }
        return $arr;
    }

    /**
     * @param $response
     */
    protected function output($response)
    {
        $this->output->writeln($response);
    }

    /**
     * 退出进程
     *
     * @param int $status
     * @return void
     */
    public function stop($status = 0)
    {
        $msg = 'Canal pid: ' . $this->pid . ' exited , now time is ' . format_timestamp() . '!';
        $this->output($msg);
        exit($status);
    }

    /**
     * 检测是否存在应用更新或者已经运行超过24小时
     * @param int $StartTime
     * @return bool
     * @throws Exception
     */
    protected function shouldRestart(int $StartTime): bool
    {
        return $this->shouldQuit || time() - $StartTime > 3600 * 24;
//        $appLastUpdateTime = min((int)cache(tools()::get_lan_ip() . ':' . config('app.app_name') . ':last_update_time'), time());
//        return (time() - $queueStartTime > 3600 * 24) || ($queueStartTime < $appLastUpdateTime);
    }

    protected static function get_column_name_value_array($columns): array
    {
        $array = [];
        /** @var Column $column */
        foreach ($columns as $column) {
            $array[$column->getName()] = $column->getValue();
            //  echo sprintf("%s : %s  update= %s", $column->getName(), $column->getValue(), var_export($column->getUpdated(), true)), PHP_EOL;
        }
        return $array;
    }
}