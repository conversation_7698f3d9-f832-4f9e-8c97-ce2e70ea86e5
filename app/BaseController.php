<?php
declare (strict_types=1);

namespace app;

use Exception;
use think\App;
use think\db\Raw;
use think\facade\Db;
use think\exception\ValidateException;
use think\facade\View;
use think\Model;
use think\Request;
use think\Validate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * 参数
     * @var array
     */
    protected array $params;

    /**
     * Request实例
     * @var Request
     */
    protected Request $request;

    /**
     * 应用实例
     * @var App
     */
    protected App $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected bool $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected array $middleware = [];
    /**
     * @var $model Model|\think\Db|null
     */
    protected $model = NULL;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;
        $this->params  = $this->request->param('', null, 'trim');
        $input_str     = file_get_contents("php://input");
        if (tools()::is_json($input_str)) {
            $this->params = array_merge($this->params, json_decode($input_str, true));
        }
        // 控制器初始化
        $this->initialize();
    }

    /**
     *魔术方法
     * @access public
     * @param string $name 方法名
     * @param array $arguments 参数
     * @return void
     * @throws Exception
     */
    public function __call_model_action($name, $arguments)
    {
        $action = strtolower($this->request->action());
        $model  = $this->model;
        $result = call_user_func_array([$model, $action], [(array)$this->params]);
        if (is_array($result)) {
            result($result);
        } elseif ($result !== false) {
            success('操作成功');
        } else {
            error($model->getError());
        }
    }

    // 初始化,如果当前类自己有initialize会自动执行当前类的initialize方法,而不是下面的方法
    protected function initialize()
    {
    }

    /**
     *初始化模型
     * @access protected
     * @return void
     * @throws Exception
     */
    protected function init_model_class()
    {
        $model_class = app()->parseClass('model', get_controller());
        if (!$this->model && class_exists($model_class)) {
            $this->model = new $model_class;
        }
    }

    /**
     * 验证数据
     * @access protected
     * @param array $data 数据
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array $message 提示信息
     * @param bool $batch 是否批量验证
     * @param bool $failException 是否抛出异常
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false, bool $failException = true)
    {
        /**
         * @var $v Validate
         */
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.') === 0) {
                // .开头
                $scene    = ltrim($validate, '.');
                $validate = $this->request->controller();
            } elseif (strpos($validate, '.') !== false) {
                // .在中间
                [$validate, $scene] = explode('.', $validate);
            }
            if (!empty($scene)) {
                $class = (false !== strpos($validate, '\\')) ? $validate : $this->app->parseClass('validate', $validate);
                $v     = new $class();
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException($failException)->check($data);
    }

    /**
     *组装查询条件
     * @access protected
     * @return array
     * @throws Exception
     */
    protected function _list()
    {
        return $this->_paginate($this->model);
    }

    /**
     *分页返回
     * @access protected
     * @param Model|Db $model 模型
     * @return array
     * @throws Exception
     */
    protected function _paginate($model)
    {
        if (empty($model)) {
            error('模型未定义');
        }
        $model_options_order = $model->getOption('order');
        $order               = $model_options_order ?: ['create_time' => 'DESC'];
        $params              = $this->params;
        if (isset($params['field']) && isset($params['order'])) {
            $field      = $model->getOption('field');
            $alias      = $model->getOption('alias');
            $alias_name = $alias[$model->getTable()] ?? '';
            if (!empty($field)) {
                $params['field'] = $this->get_field($params['field'], $field, $alias_name);
            }
            if ($model_options_order) {
                $all_order_filed = array_merge([$params['field'] => $params['order']], $model_options_order);
                $model           = $model->setOption('order', $all_order_filed);
            } else {
                $order = [$params['field'] => $params['order']];
            }
        }
        $map = $this->build_condition_map($model);
//        $sql = $model->where($map)->order($order)->buildSql();
//        halt($sql);
        return $model->where($map)->order($order)->paginate($this->get_paginate_config())->toArray();
    }

    protected function get_ignored_fields_array()
    {
        $array = ['field', 'order', 'page', ':group', 'limit', 'key', 'value', 'access_token', 'business_user_token', 'conditions'];
        return array_unique(array_merge($array, array_keys($this->request->route())));
    }

    /**
     * 解析 dynamicCondition 多条件查询参数
     * @param array $jsonStrArr
     * @return array
     */
    protected function parse_dynamic_conditions($jsonStrArr)
    {
        $map = [];
        if (!is_array($jsonStrArr)) {
            return $map;
        }
        foreach ($jsonStrArr as $item) {
            $field = $item['conditionFieldVal'] ?? '';
            $op    = $item['conditionOptionVal'] ?? '';
            $val   = $item['conditionValueVal']['value'] ?? '';
            $left  = $item['conditionValueLeftVal']['value'] ?? '';
            $right = $item['conditionValueRightVal']['value'] ?? '';
            // 操作符映射
            switch ($op) {
                case 'equal':
                    $map[] = [$field, '=', $val];
                    break;
                case 'unequal':
                    $map[] = [$field, '<>', $val];
                    break;
                case 'like':
                    $map[] = [$field, 'LIKE', "%$val%"];
                    break;
                case 'notlike':
                    $map[] = [$field, 'NOT LIKE', "%$val%"];
                    break;
                case 'between':
                    $map[] = [$field, 'BETWEEN', [$left, $right]];
                    break;
                case 'notbetween':
                    $map[] = [$field, 'NOT BETWEEN', [$left, $right]];
                    break;
                case 'in':
                    $arr   = is_array($val) ? $val : explode(',', $val);
                    $map[] = [$field, 'IN', $arr];
                    break;
                case 'notin':
                    $arr   = is_array($val) ? $val : explode(',', $val);
                    $map[] = [$field, 'NOT IN', $arr];
                    break;
                case 'start':
                    $map[] = [$field, 'LIKE', "$val%"];
                    break;
                case 'end':
                    $map[] = [$field, 'LIKE', "%$val"];
                    break;
                case 'gt':
                    $map[] = [$field, '>', $val];
                    break;
                case 'egt':
                    $map[] = [$field, '>=', $val];
                    break;
                case 'lt':
                    $map[] = [$field, '<', $val];
                    break;
                case 'elt':
                    $map[] = [$field, '<=', $val];
                    break;
                case 'empty':
                    $map[] = new Raw("($field IS NULL OR $field = '')");
                    break;
                case 'notempty':
                    $map[] = new Raw("($field IS NOT NULL AND $field <> '')");
                    break;
                default:
                    if ($field && $val !== '') {
                        $map[] = [$field, '=', $val];
                    }
            }
        }
        return $map;
    }

    /**
     *组装查询条件
     * @access private
     * @param Model|Db $model 模型
     * @return array
     */
    public function build_condition_map($model)
    {
        $params      = $this->params;
        $field       = $model->getOption('field');
        $alias       = $model->getOption('alias');
        $alias_name  = $alias[$model->getTable()] ?? '';
        $field_match = '/^[a-z0-9_|.]+$/';//包含小写字母 数字下划线 . 认为是字段
        $map         = [];

        // 新增：支持 dynamicCondition 多条件查询
        if (isset($params['conditions']['jsonStr'])) {
            $jsonStr = $params['conditions']['jsonStr'];
            if (is_string($jsonStr)) {
                $jsonArr = json_decode($jsonStr, true);
            } else {
                $jsonArr = $jsonStr;
            }
            if (is_array($jsonArr)) {
                $map = array_merge($map, $this->parse_dynamic_conditions($jsonArr));
            }
        }

        if (!empty($params['key']) && isset($params['value']) && $params['value'] !== '') {
            //key 和 value 成对出现则认为是搜索
            $key   = $params['key'];
            $value = $params['value'];
            if (strpos($key, '#') === false) {
                $key .= '#LIKE';
            }
            $params[$key] = tools()::remove_empty_string($value, false);
        }
        foreach ($params as $key => $val) {
            if (is_array($val) || $val === '' || in_array($key, $this->get_ignored_fields_array())) {
                continue;
            }
            $val = trim((string)$val);
            if (strpos($key, '#') !== false) {
                // name#= name#<= 类似这样
                $array = explode('#', $key);
                $count = count($array);
                if ($count !== 2) {
                    continue;
                }
                $operator = strtoupper($array[1]);
                if (in_array($operator, ['>=', '<=', '<', '>'])) {
                    $val = floatval($val);
                } elseif ($operator == 'LIKE') {
                    $val = '%' . $val . '%';
                }
                $map[] = [$array[0], $operator, $val];
                continue;
            }
            // https://www.jb51.net/article/100008.htm  PHP正则匹配日期和时间(时间戳转换)的实例代码
            $month_match = '/^([0-9]{4}-[0-9]{2})?$/'; //2019-12 格式
            if (preg_match($month_match, $val)) {
                $val = date('Y-m-d', strtotime($val)); //2019-12 格式自动补全为 2019-12-01
            }
            if (strpos($val, ' - ') !== false) {
                $date_time = explode(' - ', $val); //layui日期组件 -  分割
                if (count($date_time) == 2) {
                    //$datetime_match = '/^([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})?$/'; //2019-12-04 00:00:00 格式
                    $date_match = '/^([0-9]{4}-[0-9]{2}-[0-9]{2})?$/'; //2019-12-04 格式
                    if (preg_match($date_match, $date_time[0]) && preg_match($date_match, $date_time[1])) {
                        $date_time[0] = date('Y-m-d 00:00:00', strtotime($date_time[0]));   //2019-12-04开始时间默认从00:00:00
                        $date_time[1] = date('Y-m-d 23:59:59', strtotime($date_time[1]));   //2019-12-04截止时间默认到23:59:59
                    }
                    $map[] = [$key, 'BETWEEN', $date_time];
                }
            } elseif (preg_match($field_match, $key) && !tools()::start_with($key, '_') && strpos($key, '._') === false) {
                $map[] = [$key, '=', $val];
            }
        }
        foreach ($map as $key => $val) {
            if (is_array($val)) {
                $map[$key][0] = $this->get_field($val[0], $field, $alias_name);
            }
        }
        return $map;
    }

    /**
     *从字段名中查询真实字段
     * @access private
     * @param string $key 参数
     * @param array $field 字段名
     * @param string $alias_name 别名
     * @return string|Raw
     */

    private function get_field($key, $field, $alias_name)
    {
        if (empty($field)) {
            return $key;
        }
        $key = str_replace('|', '.', $key);
        if (strpos($key, '.') !== false && strpos($key, '&') === false) {
            return $key;
        }
        $key = str_replace('&', '|', $key);
        foreach ($field as $k => $v) {
            if (is_string($k) && $v == $key) {
                //  'b.business_name'                             => 'business_name',
                //  "CONCAT(b.business_name,'(',b.account,')')"   => 'business_info',
                return strpos($k, 'CONCAT') !== false ? Db::raw($k) : $k;
            }
            $value = is_integer($k) ? $v : $k;
            if (!is_object($value) && strpos($value, '.') !== false) {
                $array = explode('.', $value);
                if (count($array) == 2 && $array[1] == $key) {
                    // 情况二 ['ho.status']
                    return $value;
                }
            }
        }
        return $alias_name ? $alias_name . '.' . $key : $key;
    }

    /**
     *获取分页配置
     * @access protected
     * @param integer $limit 默认分页数量
     * @return array
     * @throws Exception
     */
    protected function get_paginate_config($limit = 0)
    {
        $limit         = $limit ?: config('paginate.list_rows');//未指定分页数量则获取配置
        $params        = $this->params;
        $max_list_rows = config('paginate.max_list_rows');//获取最大分页数量
        $list_rows     = (int)(!empty($params['limit']) ? $params['limit'] : $limit);
        $list_rows     = ($list_rows > $max_list_rows) ? $max_list_rows : $list_rows;
        $list_rows     = max(1, $list_rows);//分页不能小于1
        return [
            'list_rows' => $list_rows, //每页数量
        ];
    }

    /**
     *查询数据
     * @access protected
     * @param Model|Db $model 模型
     * @return array
     * @throws Exception
     */
    protected function _select($model)
    {
        if (empty($model)) {
            error('模型未定义');
        }
        $order = $model->getOption('order') ?: ['create_time' => 'DESC'];
        $map   = $this->build_condition_map($model);
        return $model->where($map)->order($order)->select()->toArray();
    }

    /**
     *查询数据
     * @access protected
     * @param Model|Db $model 模型
     * @return array
     * @throws Exception
     */
    protected function _chunk($model, $limit = 10000)
    {
        if (empty($model)) {
            error('模型未定义');
        }
        $order    = $model->getOption('order') ?: ['create_time' => 'DESC'];
        $map      = $this->build_condition_map($model);
        $all_data = [];
        $model->where($map)->order($order)->chunk($limit, function ($data_array) use (&$all_data) {
            foreach ($data_array as $data) {
                // 处理模型对象
                $all_data[] = $data->toArray();
                unset($data);
            }
            unset($data_array);
        }, key($order), 'DESC');
        return $all_data;
    }

    /**
     *返回单条数据
     * @access protected
     * @param Model|Db $model 模型
     * @return array|Model|static|mixed
     * @throws Exception
     */
    protected function _find($model)
    {
        if (empty($model)) {
            error('模型未定义');
        }
        $map = $this->build_condition_map($model);
        return $model->where($map)->findOrEmpty();
    }

    /**
     * 解析和获取模板内容 用于输出
     * @access protected
     * @param string $template 模板文件名
     * @param array $vars 模板输出变量
     * @return string
     * @throws Exception
     */
    protected function fetch($template = '', $vars = [])
    {
        return View::fetch($template, $vars);
    }

    /**
     * 模板变量赋值
     * @access public
     * @param string|array $name 模板变量
     * @param mixed $value 变量值
     * @return \think\View
     */
    protected function assign($name, $value = null)
    {
        return View::assign($name, $value);
    }

    public function get_bid()
    {
        return $this->request->__get('_bid');
    }

    public function get_sid()
    {
        return $this->request->__get('_sid');
    }

    public function get_appid()
    {
        return $this->request->__get('_appid');
    }

    public function get_openid()
    {
        return $this->request->__get('_openid');
    }

    public function get_member_guid()
    {
        return $this->request->__get('_member_guid');
    }

    public function get_member_id()
    {
        return $this->request->__get('_member_id');
    }

    public function get_user_guid()
    {
        return $this->request->__get('_user_guid');
    }

    public function get_user_id()
    {
        return $this->request->__get('_user_id');
    }

    public function get_jwt_data()
    {
        return $this->request->__get('_jwt_data');
    }

    public function get_config()
    {
        return $this->request->__get('_config');
    }
}
